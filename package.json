{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "", "scripts": {"dev": "vite", "build:preview": "vite build --mode qa", "build:release": "vite build --mode release", "lint": "eslint --ext .js,.vue src", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "analysis": "npm run build -- --mode analysis"}, "dependencies": {"@fullcalendar/core": "4.4.2", "@fullcalendar/daygrid": "4.4.2", "@fullcalendar/interaction": "4.4.2", "@fullcalendar/list": "4.4.2", "@fullcalendar/timegrid": "4.4.2", "@fullcalendar/vue": "4.4.2", "@stomp/stompjs": "^7.0.0", "axios": "0.21.1", "core-js": "^3.27.2", "dompurify": "^3.1.6", "element-resize-detector": "^1.2.4", "element-ui": "2.15.14", "file-saver": "^2.0.2", "intl-tel-input": "^18.1.5", "js-cookie": "2.2.1", "lodash": "^4.17.15", "moment": "^2.30.1", "moment-timezone": "^0.5.28", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-to-regexp": "6.2.0", "pdfmake": "^0.2.7", "sockjs-client": "^1.6.1", "sortablejs": "^1.13.0", "tippy.js": "^6.2.3", "vue": "2.7.16", "vue-authenticate": "^1.4.1", "vue-router": "3.5.1", "vuex": "3.6.2"}, "devDependencies": {"autoprefixer": "^9.8.8", "chalk": "4.1.0", "connect": "3.7.0", "eslint": "^8.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-vue": "^9.0.1", "mockjs": "1.1.0", "path-browserify": "^1.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "rollup-plugin-visualizer": "^5.12.0", "runjs": "4.4.2", "sass": "1.32.8", "serve-static": "1.14.1", "svgo": "2.2.0", "vite": "^4.5.1", "vite-plugin-chunk-split": "0.4.7", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.1", "vite-plugin-style-import": "^1.4.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue2": "^2.0.3", "vue-template-compiler": "2.7.16"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "license": "MIT"}