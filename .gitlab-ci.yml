workflow:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^release.*$/ || $CI_COMMIT_BRANCH =~ /^preview.*$/
      when: always
    - when: never
stages:
  - build

deploy_dev:
  extends: .build_scripts
  except:
    - /^release.*$/
  environment:
    name: aws/${CI_COMMIT_BRANCH}
deploy_master:
  extends: .build_scripts
  only:
    - /^release.*$/
  environment:
    name: aws/${CI_COMMIT_BRANCH}

.build_scripts:
  tags:
    - k8s
  stage: build
  image:
    name: 133841643524.dkr.ecr.us-east-2.amazonaws.com/kaniko_kubectl:v1.23.2-debug
  before_script:
    - export NAMESPACE=sg
      export REGION=sg
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ ^release.*$ ]]
      then
          export ENV=default-${REGION}
          export HOST=${PROD_FRONT_HOST}.${REGION}
          export REPLICAS=2
      else
          export ENV=dev-${REGION}
          export NAMESPACE=dev
          export HOST=${DEV_FRONT_HOST}.${REGION}
          export REPLICAS=1
      fi
  script:
    - /kaniko/kubectl get secrets/default-aws-ecr-us-east-2  -n gitlab -o jsonpath='{.data.\.dockerconfigjson}' | base64 -d > /kaniko/.docker/config.json
    - |
      /kaniko/executor \
        --context "${CI_PROJECT_DIR}" \
        --build-arg BUILD_ENV=${CI_COMMIT_BRANCH} \
        --dockerfile "${CI_PROJECT_DIR}/Dockerfile" \
        --destination "133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}" \
        --cache=true \
        --cache-repo "133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}-cache"

    - cd manifests/
    - /kaniko/envsubst < deployment.tpl.yaml > deployment.yaml
    - /kaniko/envsubst < ingress.tpl.yaml > ingress.yaml
    - /kaniko/envsubst < service.tpl.yaml > service.yaml
    - |
      /kaniko/kubectl apply -f deployment.yaml -f ingress.yaml -f service.yaml
