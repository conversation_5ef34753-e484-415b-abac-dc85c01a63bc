apiVersion: apps/v1
kind: Deployment
metadata:
  name: {JO<PERSON>_NAME}-{BRANCH}
  namespace: {NAMESPACE}
spec:
  replicas: 1
  minReadySeconds: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: {JOB_NAME}-{BRANCH}
  template:
    metadata:
      labels:
        app: {JOB_NAME}-{BRANCH}
    spec:
      containers:
        - name: {JOB_NAME}-{BRANCH}
          image: 133841643524.dkr.ecr.us-east-2.amazonaws.com/{JOB_NAME}:{<PERSON><PERSON><PERSON>}-{BUILD_NUMBER}
          env:
            - name: BRANCH
              value: {BRANCH}
            - name: NODE_ENV
              value: {NODE_ENV}
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: default-aws-ecr-us-east-2

---
apiVersion: v1
kind: Service
metadata:
  name: {JO<PERSON>_NAME}-{BRANCH}
  namespace: {NAMESPACE}
spec:
  ports:
    - name: http
      targetPort: 80
      port: 80
  selector:
    app: {JOB_NAME}-{BRANCH}

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {JOB_NAME}-{BRANCH}
  namespace: {NAMESPACE}
  annotations:
    traefik.frontend.rule.type: PathPrefixStrip
spec:
  rules:
    - host: {HOST}
      http:
        paths:
          - path: /{JOB_NAME}/{BRANCH}
            backend:
              serviceName: {JOB_NAME}-{BRANCH}
              servicePort: http
