<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="loadData" />
      <el-select
        v-model="searchQuery.compliance_status"
        filterable
        class="filter-item"
        placeholder="Client Approval"
        @change="handleSearch">
        <el-option
          v-for="item in options.status"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <el-select
        v-model="searchQuery['account_managers.user.id']"
        filterable
        clearable
        class="filter-item"
        placeholder="AM"
        @change="handleSearch">
        <el-option
          v-for="item in options.am_list"
          :key="item.id"
          :label="item.name"
          :value="item.id" />
      </el-select>
      <el-input
        v-model="searchQuery.name_like"
        class="w-200px filter-item"
        placeholder="Client Name"
        @input="handleSearch" />
    </div>
    <div>
      <el-table
        v-loading="loading"
        border
        stripe
        :data="data.list"
      >
        <el-table-column label="Id" prop="id" width="55" />
        <el-table-column label="Client">
          <template slot-scope="scope">
            <router-link-client v-if="scope.row" :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="AM">
          <template slot-scope="scope">
            {{ scope.row.am }}
          </template>
        </el-table-column>
        <el-table-column label="Pre-Call CA">
          <template slot-scope="scope">
            <router-link
              v-if="scope.row.pre_call_ca.id"
              class="link"
              :to="{ name: 'ClientAgreementDetail', query: { client_id: scope.row.pre_call_ca.client_id, inquiry_id: scope.row.pre_call_ca.id } }">
              {{ scope.row.pre_call_ca.name }}
            </router-link>
            <span v-else>N/A</span>
          </template>
        </el-table-column>
        <el-table-column label="Post-Call CA">
          <template slot-scope="scope">
            <router-link
              v-if="scope.row.post_call_ca.id"
              class="link"
              :to="{ name: 'ClientAgreementDetail', query: { client_id: scope.row.post_call_ca.client_id, inquiry_id: scope.row.post_call_ca.id } }">
              {{ scope.row.post_call_ca.name }}
            </router-link>
            <span v-else>N/A</span>
          </template>
        </el-table-column>
        <el-table-column label="Capvision Approval" width="140">
          <template slot-scope="scope">
            {{ scope.row.capvision_approval }}
          </template>
        </el-table-column>
        <el-table-column label="Client Approval" width="120">
          <template slot-scope="scope">
            {{ scope.row.client_approval }}
          </template>
        </el-table-column>
        <el-table-column v-if="type === 'APPLYING'" label="Action">
          <template slot-scope="scope">
            <el-popconfirm
              icon="el-icon-info"
              icon-color="#13ce66"
              title="Confirmation that the client is approved?"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="success"
              @confirm="actionApprove(scope.row)"
            >
              <el-button
                slot="reference"
                type="primary"
                icon="el-icon-check"
                class="el-button--xs"
                :disabled="!permission">
                Approve
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="searchQuery.page"
        :limit.sync="searchQuery.size"
        @pagination="loadData"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { AppHttpService } from '@/utils/request'
import ClientAPI from '@/api/client'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import { debounce, RouteTools } from '@/utils/tool'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'ClientApprovalIndex',
  components: { RefreshButton, Pagination, RouterLinkClient },
  data() {
    return {
      loading: false,
      options: {
        am_list: [],
        status: [
          {
            value: 'APPLYING',
            label: 'Compliance Rules Pending Approval',
            // label: 'TBA'
          }, {
            value: 'APPROVED',
            label: 'Compliance Approved',
          }],
      },
      searchQuery: {
        page: 1,
        size: 10,
        compliance_status: 'APPLYING',
        'account_managers.user.id': '',
        name_like: '',
        extra: 'ca_list,compliance_preference,account_managers,account_managers.user',
      },
      type: 'APPLYING',
      total: 0,
      data: {
        list: [],
      },
      permission: false,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'roles',
      'name',
    ]),
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)

    this.getAmList()
    this.loadData()
    this.getPermission()
  },
  methods: {
    loadData() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      const params = {}
      Object.assign(params, this.searchQuery)
      params['account_managers.user.id'] = params['account_managers.user.id'] || undefined
      if (params['name_like'] === '') params['name_like'] = undefined

      this.loading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.total = data.count
          this.data = this.handleData(data)
          this.type = this.searchQuery.compliance_status
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.loadData()
      }, 500)
    },
    handleData(data) {
      const approve_policy = [
        {
          value: 'NO_NEED',
          capvision_approval: 'No',
          client_approval: 'No',
        }, {
          value: 'CAPVISION_APPROVAL',
          capvision_approval: 'Yes',
          client_approval: 'No',
        }, {
          value: 'CAPVISION_THEN_CLIENT_APPROVAL',
          capvision_approval: 'Yes',
          client_approval: 'Yes',
        }, {
          value: 'CAPVISION_AND_CLIENT_APPROVAL_INTERNAL',
          capvision_approval: 'Yes',
          client_approval: 'Internal',
        }, {
          value: 'CAPVISION_AND_CLIENT_APPROVAL_SOME',
          capvision_approval: 'Yes',
          client_approval: 'Some',
        }, {
          value: 'CAPVISION_AND_CLIENT_APPROVAL_ALL',
          capvision_approval: 'Yes',
          // client_approval: 'All',
          client_approval: 'Yes',
        }]
      data.list = data.list.map(item => {
        const client_rule = item.compliance_preference ? item.compliance_preference.approve_policy : 'NO_NEED'
        const rule = approve_policy.find(policy => policy.value === client_rule)
        item.am = item.account_managers.map(item => item.user && item.user.name)
          .join(', ')
        item.pre_call_ca = this.getCA(item.ca_list, 'PRE_CALL_CA')
        item.post_call_ca = this.getCA(item.ca_list, 'POST_CALL_CA')
        item.capvision_approval = rule.capvision_approval
        item.client_approval = rule.client_approval
        return item
      })
      return data
    },
    async getAmList() {
      this.options.am_list = await ClientAPI.getAmList()
    },
    actionApprove(row) {
      this.changeApproveStatus('APPROVED', row)
    },
    actionReject(row) {
      this.$prompt('', 'Reject Reason', {
        confirmButtonText: 'Submit',
      })
        .then(({ value }) => {
          this.changeApproveStatus('REJECTED', row.id, value)
        })
        .catch(() => {
        })
    },
    changeApproveStatus(target_status, client, reason = undefined) {
      const params = {
        compliance_status: target_status,
        reason,
      }
      return AppHttpService
        .patch(`/clients/${client.id}/approval`, params)
        .then(() => {
          this.loadData()
          this.spendEmailToContactAssociatedPerson(client)
        })
    },
    getCA(ca_list, type) {
      const result = ca_list.find(item => item.type === type)
      if (result) {
        return result
      } else {
        return { name: 'N/A', id: undefined, client_id: undefined }
      }
    },
    getPermission() {
      this.permission = this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
    spendEmailToContactAssociatedPerson(client) {
      // 客户 法务规则勾选 Arrange/Emails and Invites from capvision-pro.com时，用户通过复制模板内容，自己从Outlook发送邮件，不在系统中发送给客户
      const has_rule = client.compliance_preference?.rule
      const pro = has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro
      if (pro) {
        this.$message({
          showClose: true,
          message: `The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com), it will not send relevant emails through the system.`,
          type: 'warning',
          offset: 100,
          dangerouslyUseHTMLString: true,
          duration: 5000,
        })
        return Promise.resolve()
      }

      const client_aM = client.account_managers.map(item => item.user.name)
        .join(', ')

      const params = {
        subject: 'Compliance Client Status',
        content: `
        <div>
          <div>Hello <span>${client_aM}</span>,<br>
            The client compliance status for
            <span>${client.name}</span>
            has been approved and updated to ‘Compliance Approved’ by Capvision Compliance.
          </div>
          <p>Thank you, <br><span>${this.name}</span></p>
        </div>`,
      }
      console.log(params.content)
      return ClientAPI.spendEmailToContactAssociatedPerson(client.id, params)
    },
  },
}
</script>

<style scoped>
</style>
