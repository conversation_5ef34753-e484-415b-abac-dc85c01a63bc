<template>
  <div class="app-container">
    <el-form ref="form" :model="form" label-width="200px" class="mt-8 w-90">
      <el-card shadow="never">
        <div slot="header">Allowed to Send Before TC Signed</div>
        <el-form-item label="Client Types">
          <div class="setting-item">
            <span v-if="display.client_types">
              {{ form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES | getLabels(client_options.type) }}
              <el-button class="el-icon-edit ml-8 el-button--xs" @click="display.client_types=false"> Edit</el-button>
            </span>
            <div v-if="!display.client_types">
              <el-select
                v-model="form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES"
                clearable
                multiple
                class="w-90"
                placeholder="Type"
              >
                <el-option
                  v-for="item in client_options.type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button type="primary"
                         class="el-icon-check ml-8 el-button--xs"
                         @click="updateClientTypes(form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES)"> Save
              </el-button>
            </div>

          </div>
        </el-form-item>
        <el-form-item label="Expert Geography Restriction">
          <div class="setting-item">
            <span v-if="display.expert_geography_restriction">
              {{
                form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES | getLabels(client_options.type)
              }}
              {{ ' - ' + getNames(form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS, location_list) }}
              <el-button class="el-icon-edit ml-8 el-button--xs" @click="display.expert_geography_restriction=false"> Edit</el-button>
            </span>
            <div v-if="!display.expert_geography_restriction">
              <el-select
                v-model="form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES"
                clearable
                multiple
                placeholder="Type"
                class="w-90"
              >
                <el-option
                  v-for="item in client_options.type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <location
                v-model="form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS"
                placeholder="Expert Location"
                class="w-90 inline-block mt-8"
                @change="actionLocationChange"
              />
              <el-button type="primary"
                         class="el-icon-check ml-8 el-button--xs"
                         @click="updateExpertGeographyRestriction()"> Save
              </el-button>
              <!--              <el-link-->
              <!--                type="primary"-->
              <!--                :underline="false"-->
              <!--                icon="el-icon-check"-->
              <!--                class="ml-8"-->
              <!--                @click="updateExpertGeographyRestriction()"-->
              <!--              />-->
            </div>

          </div>
        </el-form-item>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getLocationById } from '@/utils/tool'
import Location from '@/components/Location'
import ComplianceAPI from '@/api/compliance'

export default {
  name: 'SettingIndex',
  components: { Location },
  data() {
    return {
      permission: false,
      setting_data: null,
      location_list: [],
      form: {
        LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES: [],
        LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS: [],
        LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES: [],
      },
      display: {
        client_types: true,
        expert_geography_restriction: true,
      },
    }
  },
  computed: {
    ...mapGetters([
      'client_options',
    ]),
  },
  mounted() {
    this.getAllSetting()
  },
  methods: {
    getAllSetting() {
      const params = {
        type: 'GLOBAL_LEGAL_SETTING',
      }
      return ComplianceAPI.getGlobalSetting(params).then(data => {
        this.formatData(data)
        this.actionLocationChange()
      })
    },
    updateClientTypes(data) {
      const params = {
        value: data.join(),
      }
      this.updateAllowedToSendBeforeTCSigned(params,
        'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES').then(() => {
        this.display.client_types = true
      })
    },

    actionLocationChange() {
      getLocationById(this.form.LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS).then(data => {
        this.location_list = data
      })
    },

    updateExpertGeographyRestriction() {
      const location_type = 'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS'
      const client_type = 'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES'
      const params_location = {
        value: this.form[location_type].join(),
      }
      const params_client = {
        value: this.form[client_type].join(),
      }
      Promise.all([
        this.updateAllowedToSendBeforeTCSigned(params_location, location_type),
        this.updateAllowedToSendBeforeTCSigned(params_client, client_type)])
        .then(() => {
          this.display.expert_geography_restriction = true
        })
    },

    updateAllowedToSendBeforeTCSigned(params, type) {
      return ComplianceAPI.updateAllowedToSendBeforeTCSigned(params, type)
    },

    getNames(list, option) {
      if (!list || !list.length) return '-'
      return list.map(item => {
        const result = option.filter(opt => opt.id === item)
        return result.length ? result[0].name : undefined
      }).join(', ')
    },

    formatData(data) {
      data.forEach(item => {
        switch (item.key) {
          case 'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS':
            this.form[item.key] = item.value.split(
              ',').map(Number)
            break
          case 'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES':
            this.form[item.key] = item.value.split(
              ',')
            break
          default:
            this.form[item.key] = item.value.split(
              ',')
        }
      })
    },
  },
}
</script>

<style scoped>
.setting-item {
  width: 90%;
}

.w-90 {
  width: 90%;
}

.w-80 {
  width: 80%;
}
</style>
