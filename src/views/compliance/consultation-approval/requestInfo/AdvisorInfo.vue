<template>
  <div class="advisor-box scrollbar-narrow">
    <div>
      <div><b>Basic Information</b></div>
      <div class="pl-5">
        <el-form
          label-position="left"
          label-width="90px">
          <el-form-item label="Profile Page">
            <router-link
              class="link"
              :to="{ name: 'ConsultantDetail', params: { advisor_id: advisor.id} }">
              {{ advisor.name_prefix || ''}} {{ advisor.firstname }} {{ advisor.lastname }}
            </router-link>
          </el-form-item>
          <el-form-item label="First Name">{{ advisor.firstname }}</el-form-item>
          <el-form-item label="Last Name">{{ advisor.lastname }}</el-form-item>
          <el-form-item label="Type">{{ advisor.type | getLabel(advisor_options.type) }}</el-form-item>
          <el-form-item label="Location">{{ advisor.location && advisor.location.name }}</el-form-item>
        </el-form>
      </div>
      <br>
    </div>
    <br>
    <div class="mt-8">
      <div><b>Employment History</b></div>
      <div class="mt-8 pl-5">
        <div v-for="job in advisor.jobs" :key="job.id">
          <router-link
            class="link"
            :to="{ name: 'CompanyDetail', params: { company_id: job.company.id } }">
            {{ job.company.name }}
          </router-link>
          {{
            ` | ${job.position} | ${jobDateFormat(job.start_date)} ~
                  ${job.is_current ? 'current' : jobDateFormat(job.end_date)}  `
          }}
          {{ getStockCode(job.company) }}
        </div>
      </div>
    </div>
    <br>
    <div class="mt-8">
      <div><b>Background</b></div>
      <div class="mt-8 pl-5">{{ advisor.background }}</div>
    </div>
    <br>
    <div class="mt-8">
      <div><b>Screening Questions & Response</b></div>
      <div v-if="latestedSq" class="mt-8 ml-3">
        <show-answered-questions where="consultation_approval" :list="latestedSq.questions" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment-timezone'
import ShowAnsweredQuestions from '@/components/Question/ShowAnsweredQuestions'

export default {
  name: 'AdvisorInfo',
  components: { ShowAnsweredQuestions },
  props: {
    advisor: {
      type: Object,
    },
    latestedSq: {
      type: Object,
    },
  },

  computed: {
    ...mapGetters(['advisor_options', 'lang_type_options', 'company_options']),
  },

  methods: {
    jobDateFormat(date) {
      const list = date.split('-')
      switch (list.length) {
        case 2:
          return moment(date)
            .format('MM/YYYY')
        case 3:
          return moment(date)
            .format('MM/DD/YYYY')
        default:
          return date
      }
    },

    getStockCode(company) {
      if (company.exchange) {
        return ' | ' + company.exchange + ' - ' + company['stock_code']
      }
      if (company.type === 'PRIVATE') {
        return ' | ' + 'Private'
      }
    },

  },

}
</script>

<style scoped>
::v-deep .el-form-item {
  margin-bottom: 0;
}

.advisor-box {
  height: inherit;
  overflow: auto;
}

.sq-answer {
  color: #3f9eff;
  margin: 2px 0;
}
</style>
