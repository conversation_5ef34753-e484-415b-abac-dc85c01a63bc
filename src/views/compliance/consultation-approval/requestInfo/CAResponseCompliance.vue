<template>
  <div class="right-border">
    <div class="pl-5 mt-8">
      <div><b>Compliance Preferences</b></div>
      <div class="client-compliance scrollbar-narrow">
        <div v-if="client.compliance_preference.rule.compliance_rules">
          <communication :type-rule="client_compliance_rules" />
          <language-rule :type-rule="client_compliance_rules" />
          <general-rule :type-rule="client_compliance_rules" />
          <email-copy :type-rule="client_compliance_rules" />
          <internal-copy :type-rule="client_compliance_rules" :client-am="client_am" />
          <project-creation :type-rule="client_compliance_rules" />
          <pre-call-ca :type-rule="client_compliance_rules" />
          <post-call :type-rule="client_compliance_rules" />
          <call-scheduling-email :type-rule="client_compliance_rules" />
          <calendar-invite :type-rule="client_compliance_rules" />
          <transcription-note :type-rule="client_compliance_rules" />
          <expert-restriction :type-rule="client_compliance_rules" :show-empty-tip="false" />
          <recommend :type-rule="client_compliance_rules" />
          <approval :type-rule="client_compliance_rules" :client-am="client_am" />
          <arrange :type-rule="client_compliance_rules" />
          <arrange :type-rule="client_compliance_rules" />
          <survey-rules :type-rule="client_compliance_rules" />
        </div>
        <div v-else class="warning">This client has not set a compliance preference.</div>

      </div>
    </div>
  </div>
</template>

<script>
import EmailCopy from '@/components/ComplianceWarning/components/EmailCopyRule'
import Approval from '@/components/ComplianceWarning/components/Approval'
import Arrange from '@/components/ComplianceWarning/components/Arrange'
import Recommend from '@/components/ComplianceWarning/components/RecommendEmail'
import ProjectCreation from '@/components/ComplianceWarning/components/ProjectCreation'
import CallSchedulingEmail from '@/components/ComplianceWarning/components/CallSchedulingEmail'
import PreCallCa from '@/components/ComplianceWarning/components/PreCallCA'
import CalendarInvite from '@/components/ComplianceWarning/components/CalendarInvite'
import ExpertRestriction from '@/components/ComplianceWarning/components/ExpertRestriction'
import Communication from '@/components/ComplianceWarning/components/Communication'
import InternalCopy from '@/components/ComplianceWarning/components/InternalCopy'
import LanguageRule from '@/components/ComplianceWarning/components/Language'
import GeneralRule from '@/components/ComplianceWarning/components/GeneralRule'
import PostCall from '@/components/ComplianceWarning/components/PostCall'
import TranscriptionNote from '@/components/ComplianceWarning/components/TranscriptNotetaking'
import SurveyRules from '@/components/ComplianceWarning/components/Survey'

export default {
  name: 'CACompliance',
  components: {
    EmailCopy,
    Approval,
    Arrange,
    Recommend,
    ProjectCreation,
    CallSchedulingEmail,
    PreCallCa,
    CalendarInvite,
    ExpertRestriction,
    Communication,
    InternalCopy,
    LanguageRule,
    GeneralRule,
    PostCall,
    TranscriptionNote,
    SurveyRules,
  },
  props: {
    client: {
      type: Object,
    },
  },

  data() {
    return {
      client_am: undefined,
      client_compliance_rules: {},
      option_type: [
        { name: 'Pass', value: 'ALLOW' },
        { name: 'Hold', value: 'HOLD' },
        { name: 'Fail', value: 'DENY' },
      ],
      ca_result_status: [
        { name: 'Auto Pass', id: 'AUTO_PASSED', type: 'success' },
        { name: 'Auto Hold', id: 'AUTO_HOLD', type: 'warning' },
        { name: 'Auto Fail', id: 'AUTO_REJECTED', type: 'danger' },
      ],
    }
  },

  mounted() {
    this.client_am = this.client.account_managers.map(item => item.user.name)
      .join()

    if (this.client.compliance_preference.rule.compliance_rules) {
      this.client_compliance_rules = this.client.compliance_preference.rule.compliance_rules
    }
  },
}
</script>

<style scoped>

.client-compliance {
  height: 36vh;
  overflow: auto;
}

.client-agreement {
  height: 20vh;
  overflow: auto;
  border-bottom: 1px solid #e6e2e2;
}

</style>
