<template>
  <div class="right-border">
    <div class="project-info scrollbar-narrow">
      <div><b>Project Information</b></div>
      <el-form
        ref="form"
        class="pl-5"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="Project">
          <router-link-project :data="project" />
        </el-form-item>
        <el-form-item label="Industry">
          {{ project.industry | getLabel(project_options.industry) }}
        </el-form-item>
        <el-form-item label="Investment Target">
          <div v-for="item in project.investment_target_companies" :key="item.id">
            <router-link-company :data="item" />
            <span v-if="item.exchange"> - {{ item.exchange }}<span v-if="item.stock_code"> | {{item.stock_code}}</span></span>
          </div>
        </el-form-item>
        <el-form-item label="Tickers">
          <slot v-if="project.tickers.length">
            <span v-for="item in project.tickers" :key="item.id">{{ item.company.name }}</span>
          </slot>
        </el-form-item>
        <el-form-item label="Tags">
          <slot v-if="project.tags.length">
            <span v-for="item in project.tags" :key="item.id">{{ item.name }}</span>
          </slot>
        </el-form-item>
        <el-form-item label="Project Start">
          {{ project.start_date }}
        </el-form-item>
        <br>
        <el-form-item
          label="Client Name"
        >
          <router-link-client :data="client" />
        </el-form-item>
        <el-form-item
          label="Type"
        >
          {{ client.type | getLabel(client_options.type) }}
        </el-form-item>
        <el-form-item label="Client Contact">
          <slot v-if="project.project_client_contacts.length">
            <div v-for="item in project.project_client_contacts" :key="item.id">
              <div v-if="item.client_contact && item.client_contact.contact_infos.length">
                {{ item.client_contact.name }}
                <div class="inline-block overflow-break">
                  <span>{{ ` ${item.client_contact.contact_infos[0].type.slice(0, 1)}:  ` }}</span>
                  <span>{{ `${item.client_contact.contact_infos[0].value}` }}</span>
                </div>
              </div>
            </div>
          </slot>
        </el-form-item>
        <el-form-item label="AM/Sales">
          <template v-if="client.account_managers">
            <span v-for="(item,index) in client.account_managers" :key="item.id"><span
              v-if="index>0">, </span>{{ item.user.name }}</span>
          </template>
        </el-form-item>
      </el-form>
    </div>

    <div class="mt-8 pl-5">
      <div><b>Request Information</b></div>
      <div class="request-info scrollbar-narrow" v-html="$sanitizeHtml(project.request_content)" />
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import RouterLinkProject from '@/components/RouterLink/Project'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkCompany from '@/components/RouterLink/Company'

export default {
  name: 'ProjectInfo',
  components: { RouterLinkProject, RouterLinkClient, RouterLinkCompany },
  props: {
    project: {
      type: Object,
    },
    client: {
      type: Object,
    },
    request: {
      type: Object,
    },
  },
  computed: {
    ...mapGetters(['project_options', 'client_options']),
  },
}
</script>

<style scoped>
::v-deep .el-form-item {
  margin-bottom: 0;
}

.project-info {
  height: 20vh;
  overflow: auto;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e2e2;
}

.contact-content {
  margin: 0 2px;
  color: #20a0ff;
}

.request-info {
  height: 17vh;
  overflow: auto;
  padding-top: 5px;
  padding-left: 5px;
  white-space: pre-wrap;
}
</style>
