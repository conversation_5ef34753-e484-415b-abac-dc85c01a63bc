<template>
  <div v-bind="$attrs" class="custom-table-expend">
    <ProjectInfo :project="data.project" :client="data.client" class="flex-1 item" />
    <CAResponseCompliance :client="data.client" class="flex-1 item" />
    <CAResponse :data="data" :ca-response="data['latest_submitted_pre_ca']" class="flex-1 item" />
    <AdvisorInfo v-if="!data.advisor.gdpr_delete_by_id"
                 :advisor="data.advisor"
                 :latested-sq="data['latest_submitted_sq']"
                 class="flex-1 item" />
    <div v-else class="flex-1 item">
      <span class="warning">
        Expert Deleted by {{data.advisor.gdpr_delete_by.name}} at {{ data.advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A') }}.
      </span>
    </div>
  </div>
</template>

<script>
import ProjectInfo from './ProjectInfo'
import CAResponseCompliance from '@/views/compliance/consultation-approval/requestInfo/CAResponseCompliance'
import CAResponse from '@/views/compliance/consultation-approval/requestInfo/ClientAgreementResponse'
import AdvisorInfo from '@/views/compliance/consultation-approval/requestInfo/AdvisorInfo'

export default {
  name: 'RequestInfo',
  components: { ProjectInfo, CAResponseCompliance, CAResponse, AdvisorInfo },
  props: {
    data: {
      type: Object,
    },
  },

}
</script>

<style scoped lang="scss">
.custom-table-expend {
  height: 40vh;
  overflow: hidden;
}

::v-deep .el-form-item label {
  font-weight: 400;
}

.right-border {
  border-right: 1px solid #e6e2e2;
}

.item {
  padding: 5px;
  overflow: auto;
}

</style>
