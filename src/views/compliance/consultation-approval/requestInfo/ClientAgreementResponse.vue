<template>
  <div class="custom-box right-border">
    <div><b>CA Response</b>
      <el-link
        v-if="caResponse && caResponse.inquiry_instance"
        :underline="false"
        :type="getTagType()"
        effect="plain"
        class="ml-8"
        size="mini">
        {{ caResponse.inquiry_instance.status | getLabel(ca_result_status) }}
      </el-link>
      <span v-if="caResponse && caResponse.method === 'EMAIL'">By Email</span>
    </div>
    <div class="ca-response scrollbar-narrow pl-5">
      <div v-if="caResponse && caResponse.inquiry_instance">
        <div v-for="item in caResponse.inquiry_instance.qa_list_snapshot" :key="item.id" class="flex">
          <div>{{ item.sort_order + 1 }}.</div>
          <div>
            <span class="white-pre-wrap" v-html="item.title" />
            <div v-if="getAnswer(item)" class="answer-item">
              <div v-if="getAnswerFullName(item)" class="black">Full Name: {{getAnswerFullName(item)}}</div>
              {{ getAnswer(item) }}
              <el-tag
                v-if="item.type==='RADIO'"
                size="mini"
                :type="getAnswerResult(item,'type')">
                {{ getAnswerResult(item, 'name') }}
              </el-tag>
              <div v-if="getAnswerDesc(item)" class="answer-comment">Note: {{ getAnswerDesc(item) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>N/A</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComplianceAgreementResponse',
  props: {
    caResponse: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      option_type: [
        { name: 'Pass', value: 'ALLOW', type: 'success' },
        { name: 'Hold', value: 'HOLD', type: 'warning' },
        { name: 'Fail', value: 'DENY', type: 'danger' },
      ],
      ca_result_status: [
        { label: 'Auto Pass', value: 'AUTO_PASSED', type: 'success' },
        { label: 'Auto Hold', value: 'AUTO_HOLD', type: 'warning' },
        { label: 'Auto Fail', value: 'AUTO_REJECTED', type: 'danger' },
      ],
    }
  },
  methods: {
    getTagType() {
      if (!this.caResponse || !this.caResponse.inquiry_instance) return ''
      const has_status = this.ca_result_status.find(item => item.value === this.caResponse.inquiry_instance.status)
      return has_status && has_status.type ? has_status.type : ''
    },
    getAnswer(item) {
      if (!['RADIO', 'TEXT'].includes(item.type) || !item.answer) return ''
      return item.type === 'RADIO' ? item.options[item.answer.content.result].text : item.answer.content.result
    },
    getAnswerFullName(item) {
      if (!['RADIO'].includes(item.type) || !item.answer || !item.tags.includes('REQUIRE_ADVISOR_INPUT_FULLNAME')) return ''
      return 'test'
    },
    getAnswerDesc(item) {
      const question_has_desc = item.options.filter(i => i.description).length > 0
      if (item.type !== 'RADIO' || !item.answer || !question_has_desc) return ''
      return item.answer.content && item.answer.content.desc ? item.answer.content.desc : 'NA'
    },
    getAnswerResult(item, type) {
      if (item.type !== 'RADIO' || !item.answer) return ''
      const answer_ype = item.options[item.answer.content.result].type
      return this.option_type.find(item => item.value === answer_ype)[type] || ''
    },
  },
}
</script>

<style scoped lang="scss">

.black{
  color:#000000;
}

.custom-box {
  height: inherit;
}

.answer-item {
  margin: 4px 0;
  color: #5996d4;
}

.answer-comment {
  font-size: 9pt;
  color: #E6A23C;
  margin-top: 3px;
  word-break: break-all;
}

.ca-response {
  height: 80%;
  overflow: auto;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.white-pre-wrap {
  white-space: pre-wrap;
}

::v-deep {
  .el-tag--mini {
    height: 14px;
    line-height: 13px;
    font-size: 12px;
    padding: 0 2px;
    margin-left: 2px;
  }
}

::v-deep a {
  text-decoration: revert;
}
</style>
