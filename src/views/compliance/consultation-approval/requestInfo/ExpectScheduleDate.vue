<template>
  <div v-if="task.legal_request && task.legal_request.expect_schedule_date">
    <el-popover
      v-model="visible"
      placement="top"
      width="300"
    >
      <div class="text-center mt-8">
        <el-date-picker
          v-model="expect_schedule.date"
          type="date"
          :clearable="false"
          format="MM/dd/yyyy"
          value-format="yyyy-MM-dd"
          placeholder="Selected"
          :picker-options="pickerOptions"
          class="mb-8" />
        <br>
        <el-checkbox v-if="showUrgentCheckBox"
                     v-model="expect_schedule.urgent">
          Urgent Review Required
        </el-checkbox>
      </div>
      <div class="text-center">
        <el-button size="mini" type="text" @click="cancelEdit">Cancel</el-button>
        <el-button type="primary" size="mini" @click="updateExpectScheduleDate">Save</el-button>
      </div>
      <el-button slot="reference" type="text">
        <span>{{ task.legal_request.expect_schedule_date | momentFormat('MM/DD') }}</span></el-button>
    </el-popover>
    <div :class="iconColor" class="circle inline-block" />
  </div>
</template>
<script>
import moment from 'moment'
import ProjectAPI from '@/api/project'

export default {
  name: 'ExpectScheduleDate',
  props: {
    task: Object,
  },
  data() {
    return {
      visible: false,
      expect_schedule: this.init(),
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < moment().subtract(1, 'day')
        },
      },
    }
  },

  computed: {
    showUrgentCheckBox() {
      return this.expect_schedule.date === moment().format('YYYY-MM-DD')
    },
    // https://www.notion.so/capvision/ICOM-Update-ICOM-form-with-anticipated-call-date-8faf0bb450a749099547e7cc97bdcbdc
    iconColor() {
      if (!this.task.legal_request?.expect_schedule_date) return ''
      const today = moment().format('YYYY-MM-DD')
      const expect_date = moment(this.task.legal_request.expect_schedule_date)
      const days = expect_date.diff(today, 'days')
      let result
      if (days < 0) {
        result = 'purple'
      } else if (days === 0) {
        result = this.task.legal_request.urgent ? 'crimson' : 'light-red'
      } else if (days === 1) {
        result = 'organ'
      } else if (days < 7) {
        result = 'yellow'
      } else {
        result = 'green'
      }
      return result
    },
  },

  methods: {
    init() {
      return {
        date: this.task.legal_request?.expect_schedule_date,
        urgent: this.task.legal_request?.urgent,
      }
    },

    cancelEdit() {
      this.visible = false
      this.expect_schedule = this.init()
    },

    updateExpectScheduleDate() {
      const params = {
        expect_schedule_date: this.expect_schedule.date,
        urgent: this.showUrgentCheckBox ? this.expect_schedule.urgent : false,
      }
      return ProjectAPI.changeTaskLegalRequest(this.task.legal_request.id, params)
        .then(data => {
          this.task.legal_request.expect_schedule_date = data.expect_schedule_date
          this.task.legal_request.urgent = data.urgent
          this.$message({
            message: 'Save successful',
            type: 'success',
          })
        })
        .finally(() => {
          this.visible = false
          this.expect_schedule = this.init()
        })
    },
  },
}
</script>

<style scoped lang="scss">
.circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 6px;

  &.crimson {
    background: #aa0510;
  }

  &.light-red {
    background: #f45662a8;
  }

  &.organ {
    background: #ffa500;
  }

  &.yellow {
    background: #ffeb3b;
  }

  &.green {
    background: #67C23A;
  }

  &.purple {
    background: #6445c7;
  }
}
</style>
