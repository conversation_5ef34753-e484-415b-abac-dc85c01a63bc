<template>
  <div class="app-container">
    <div class="position-relative tool-switch">
      <transition name="fade">
        <div
          v-if="!multipleSelection.length"
          :key="1"
          class="filter-container tool-switch-page1 flex justify-content-center align-items-center flex-wrap"
          style="width:100%;">
          <div>
            <refresh-button class="filter-item" @action="loadData" />
            <el-select
              v-model="searchQuery.client_compliance_status_in"
              multiple
              filterable
              clearable
              class="filter-item"
              placeholder="Client Approval"
              @change="handleSearch">
              <el-option
                v-for="item in options.client"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-select
              v-model="searchQuery.capvision_compliance_status_in"
              multiple
              filterable
              clearable
              class="filter-item"
              placeholder="Capvision Approval"
              @change="handleSearch">
              <el-option
                v-for="item in options.capvision"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-input
              v-model="searchQuery['client.name_like']"
              class="w-200px filter-item"
              placeholder="Client Name"
              @input="handleSearch" />
            <advisor-search v-model="searchQuery.advisor_ids" class="filter-item" @change="handleSearch" />
            <el-date-picker
              v-model="date_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Submit start"
              end-placeholder="Submit end"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <user-search
              v-model="searchQuery.request_by"
              class="inline-block filter-item w-150px"
              placeholder="Requested By"
              clearable
              @change="handleSearch" />
            <user-search
              v-model="searchQuery.project_manager"
              class="inline-block filter-item w-150px"
              placeholder="Project Manager"
              clearable
              @change="handleSearch" />
            <el-date-picker
              v-model="expect_schedule_range"
              type="daterange"
              class="filter-item"
              value-format="yyyy-MM-dd"
              start-placeholder="Estimated start"
              end-placeholder="Estimated end"
              @change="handleSearch" />
          </div>
        </div>
        <div v-else :key="2" class="filter-container tool-switch-page2">
          <el-tooltip placement="top" :disabled="!client_approval_disabled">
            <div slot="content">
              task :
              <div v-if="client_not_need_approval.length" class="warning">[{{ client_not_need_approval.join() }},the client do not need to approval.]</div>
              <div v-if="cap_not_yet_approved.length" class="warning">[{{ cap_not_yet_approved.join() }}, the Capvision legal need to approval first.]</div>
              <div v-if="client_approved.length" class="warning">[{{ client_approved.join() }}, the client has been approved.]</div>
              <div v-if="chaperones_required.length" class="warning">[{{ chaperones_required.join() }}, you need select the client chaperones first.]</div>
            </div>
            <div class="filter-item inline-block">
              <el-popconfirm
                confirm-button-text="Confirm"
                cancel-button-text="Review"
                title="Please confirm this call is Client Approve ?"
                @confirm="actionClientApprove"
              >
                <el-button
                  slot="reference"
                  type="primary"
                  icon="el-icon-check"
                  :disabled="client_approval_disabled">
                  Client Approve
                </el-button>
              </el-popconfirm>
            </div>
          </el-tooltip>

          <el-tooltip placement="top" :disabled="!cap_approval_disabled">
            <div slot="content">
              task :
              <div v-if="client_not_yet_approved.length" class="warning">[{{ client_not_yet_approved.join() }},the client need to approval first.]</div>
              <div v-if="cap_approved.length" class="warning">[{{ cap_approved.join() }}, the Capvision legal has been approved.]</div>
            </div>
            <div class="filter-item inline-block">
              <el-popconfirm
                confirm-button-text="Confirm"
                cancel-button-text="Review"
                title="Please confirm this call is Capvision Approve ?"
                @confirm="actionCapvisionApprove"
              >
                <el-button
                  slot="reference"
                  type="primary"
                  icon="el-icon-check"
                  :disabled="cap_approval_disabled">
                  Capvision Approve
                </el-button>
              </el-popconfirm>
            </div>
          </el-tooltip>

          <el-tooltip placement="top" :disabled="!client_approval_disabled && !cap_approval_disabled">
            <div slot="content">
              task:
              <div v-if="client_not_need_approval.length" class="warning">[{{ client_not_need_approval.join() }}, the client do not need to approval.]</div>
              <div v-if="client_approved.length" class="warning">[{{ client_approved.join() }}, the client has been approved.]</div>
              <div v-if="cap_approved.length" class="warning">[{{ cap_approved.join() }}, the Capvision legal has been approved.]</div>
              <div v-if="client_not_yet_approved.length" class="warning">[{{ client_not_yet_approved.join() }},the client need to approval first.]</div>
              <div v-if="cap_not_yet_approved.length" class="warning">[{{ cap_not_yet_approved.join() }}, the Capvision legal need to approval first.]</div>
            </div>
            <div class="filter-item inline-block">
              <el-popconfirm
                confirm-button-text="Confirm"
                cancel-button-text="Review"
                title="Please confirm this call is Approve Both ?"
                @confirm="actionDirectApprove"
              >
                <el-button
                  slot="reference"
                  type="primary"
                  icon="el-icon-check"
                  :disabled="client_approval_disabled || cap_approval_disabled">
                  Approve Both
                </el-button>
              </el-popconfirm>
            </div>
          </el-tooltip>
          <el-tooltip placement="top" :disabled="allowRejectApprovedCall && !rejected_disabled">
            <div slot="content">
              task:
              <div v-if="!allowRejectApprovedCall" class="warning">[{{ cap_approved.join() }},already approved,cannot be rejected.]</div>
              <div v-if="rejected_tasks.length" class="warning">[{{ rejected_tasks.join() }},already rejected.]</div>
            </div>
            <div class="filter-item inline-block">
              <el-popconfirm
                class="filter-item"
                confirm-button-text="Confirm"
                cancel-button-text="Review"
                icon="el-icon-warning"
                icon-color="#d63140"
                :title="rejectPrompt"
                @confirm="actionReject"
              >
                <el-button
                  slot="reference"
                  type="danger"
                  :disabled="!allowRejectApprovedCall || rejected_disabled"
                  icon="el-icon-close">
                  Reject
                </el-button>
              </el-popconfirm>
            </div>
          </el-tooltip>
        </div>
      </transition>
    </div>
    <div>
      <el-table
        ref="consultationApproval"
        v-loading="loading"
        border
        stripe
        :data="data.list"
        :row-class-name="$options.filters.rowSetIndex"
        @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, data.list, $refs['consultationApproval'])"
        @sort-change="handleSortData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" :selectable="checkSelectable" />
        <el-table-column label="Task" prop="id" width="60" />
        <el-table-column
          label="Estimated Scheduling"
          width="110"
          prop="legal_request"
          sortable="custom">
          <template slot-scope="scope">
            <expect-schedule-date :task.sync="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="Time Submitted" width="105" prop="task.request_cap_legal_approve_time" sortable="custom">
          <template slot-scope="scope">
            {{ scope.row.request_cap_legal_approve_time | momentFormat('MM/DD/YYYY HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column label="Scheduled Time" prop="task.schedule.start_time" width="105" sortable="custom">
          <template slot-scope="scope">
            <span v-if="scope.row.schedule">
              {{ scope.row.schedule.start_time | momentFormat('MM/DD/YYYY HH:mm') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="View Request" type="expand" width="70">
          <template slot-scope="scope">
            <div class="table-expand">
              <request-info
                class="flex flex-wrap justify-content-between"
                :data="scope.row"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Client" min-width="140">
          <template slot-scope="scope">
            <router-link-client v-if="scope.row.client" :data="scope.row.client" />
          </template>
        </el-table-column>
        <el-table-column label="Client Chaperons" width="125">
          <template slot-scope="scope">
            <el-popover
              v-if="scope.row.client && scope.row.client.compliance_preference && scope.row.client.compliance_preference.require_chaperone"
              v-model="scope.row.chaperone_popover_visible"
              trigger="click"
              @show="initTaskClientChaperonesIds(scope.row)"
            >
              <el-checkbox-group v-model="scope.row.task_client_chaperones_ids">
                <el-checkbox
                  v-for="item in scope.row.client.compliance_preference.chaperones"
                  :key="item.id"
                  :label="item.id"
                >
                  <span>{{ item.name }}</span><span class="info"> - {{item.email}}</span>
                </el-checkbox>
              </el-checkbox-group>
              <div class="flex mt-8">
                <el-button type="text" class="mr-auto" @click="scope.row.task_client_chaperones_ids = []">Reset</el-button>
                <el-button type="text" @click="scope.row.chaperone_popover_visible = false">Cancel</el-button>
                <el-button type="primary" size="xs" @click="updateTaskClientChaperones(scope.row)">Confirm</el-button>
              </div>
              <span slot="reference" class="dropdown-link link">
                <span v-if="scope.row.task_client_chaperones.length">
                  {{ scope.row.task_client_chaperones.map(i => i.chaperone?.name).join(', ') }}
                </span>
                <span v-else class="info">-- Select --</span>
                <i class="el-icon-arrow-down el-icon--right" />
              </span>
            </el-popover>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="Advisor" min-width="120">
          <template slot-scope="scope">
            <router-link-advisor v-if="scope.row.advisor" :data="scope.row.advisor" />
          </template>
        </el-table-column>
        <el-table-column label="Company" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.advisor_profile && scope.row.advisor_profile.company"
            >{{ scope.row.advisor_profile.company.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Position" min-width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.advisor_profile">{{ scope.row.advisor_profile.position }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Requested By" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.latest_request_cap_approval_user">
              {{ scope.row.latest_request_cap_approval_user.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="Project Manager" width="120">
          <template slot-scope="scope">
            {{ scope.row.project_manager && scope.row.project_manager.user.name }}
          </template>
        </el-table-column>
        <el-table-column label="Client Approval" width="120">
          <template slot-scope="scope">
            {{ scope.row.client_compliance_status | getLabel(options.status) }}
          </template>
        </el-table-column>
        <el-table-column label="Capvision Approval" width="140">
          <template slot-scope="scope">
            {{ scope.row.capvision_compliance_status | getLabel(options.status) }}
          </template>
        </el-table-column>
        <el-table-column label="T&C" min-width="150">
          <template v-if="scope.row.advisor" slot-scope="scope">
            <slot v-if="scope.row.advisor.latest_signed_tc">
              {{ scope.row.advisor.latest_signed_tc.tc.name }}
            </slot>
          </template>
        </el-table-column>
        <el-table-column label="Client Agreement" min-width="150">
          <template slot-scope="scope">
            <slot v-if="scope.row.latest_submitted_pre_ca">
              {{ scope.row.latest_submitted_pre_ca.inquiry ? scope.row.latest_submitted_pre_ca.inquiry.name : '' }}
              <slot v-if="['EMAIL','EXTERNAL','OVERRIDDEN'].includes(scope.row.latest_submitted_pre_ca.method)">
                <span v-if="scope.row.latest_submitted_pre_ca.method==='OVERRIDDEN'" class="success">Overridden</span>
                <slot v-else>
                  <el-tag type="success" effect="plain" size="mini">UPLOADED</el-tag>
                  <download-file :file-list="scope.row.latest_submitted_pre_ca.files" />
                </slot>
              </slot>
              <slot v-else>
                <el-tag
                  v-if="scope.row.latest_submitted_pre_ca.inquiry_instance"
                  effect="plain"
                  size="mini"
                  :type="scope.row.latest_submitted_pre_ca.inquiry_instance.status | statusClass">
                  {{ scope.row.latest_submitted_pre_ca.inquiry_instance.status | caStatusText }}
                </el-tag>
              </slot>
            </slot>
          </template>
        </el-table-column>
        <el-table-column label="Internal Status" width="200">
          <template slot-scope="scope">
            <el-dropdown
              v-if="!scope.row.edit_custom_status"
              placement="bottom-start"
              trigger="click"
              :disabled="scope.row.internal_status === 'APPROVED'"
              @command="(val) => actionUpdateInternalStatus(scope.row, val)"
            >
                  <span class="dropdown-link link">
                      {{ getStatusLabel(scope.row) }}
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in internal_status_options"
                  :key="item.value"
                  :command="item.value"
                >
                  <span>{{ item.label }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <div v-else>
              <el-input v-model="scope.row.internal_status_custom_status_new" />
              <div class="mt-3 text-center">
                <el-button
                  type="primary"
                  class="el-button--xs"
                  :loading="scope.row.loading"
                  :disabled="!scope.row.internal_status_custom_status_new"
                  @click="actionUpdateInternalStatus(scope.row,'OTHER_CUSTOM_STATUS',scope.row.internal_status_custom_status_new)">Save</el-button>
                <el-button
                  type="info"
                  class="el-button--xs"
                  @click="scope.row.edit_custom_status=false">Cancel</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="History" width="65">
          <template v-if="scope.row.task_legal_internal_status_logs" slot-scope="scope">
            <el-popover
              placement="top-start"
              width="400"
              trigger="click">
              <el-table
                class="stripe-table scrollbar-narrow"
                max-height="400"
                :data="scope.row.task_legal_internal_status_logs"
                stripe
              >
                <el-table-column label="Time">
                  <template scope="scope">
                    {{ scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm a') }}
                  </template>
                </el-table-column>
                <el-table-column label="Status" width="200">
                  <template scope="scope">
                    {{ getStatusLabel(scope.row) }}
                  </template>
                </el-table-column>
              </el-table>
              <div slot="reference">
                <el-link
                  type="primary"
                  size="mini"
                  icon="el-icon-document"
                  :underline="false"
                />
              </div>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="searchQuery.page"
        :limit.sync="searchQuery.size"
        @pagination="loadData"
      />
    </div>
  </div>
</template>

<script>
import { AppHttpService } from '@/utils/request'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import { debounce } from '@/utils/tool'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'
import RequestInfo from './requestInfo/index'
import ProjectAPI from '@/api/project'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import DownloadFile from '@/components/DownloadList/index'
import ExpectScheduleDate from './requestInfo/ExpectScheduleDate.vue'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import { mapGetters } from 'vuex'

export default {
  name: 'ConsultationApprovalIndex',
  components: {
    AdvisorSearch,
    RouterLinkAdvisor,
    RefreshButton,
    Pagination,
    RouterLinkClient,
    RequestInfo,
    UserSearch,
    DownloadFile,
    ExpectScheduleDate,
  },
  filters: {
    statusClass(status) {
      switch (status) {
        case 'AUTO_PASSED':
          return 'success'
        case 'AUTO_REJECTED':
          return 'danger'
        default:
          return 'warning'
      }
    },
    caStatusText(status) {
      switch (status) {
        case 'AUTO_PASSED':
          return 'PASS'
        case 'AUTO_REJECTED':
          return 'FAIL'
        default:
          return 'HOLD'
      }
    },
  },
  data() {
    return {
      loading: false,
      options: {
        client: [
          {
            value: 'SENT',
            // label: 'TBA'
            label: 'Pending Approval',
          }, {
            value: 'APPROVED',
            label: 'Approved',
          }, {
            value: 'REJECTED',
            label: 'Rejected',
          }],
        capvision: [
          {
            value: 'SENT',
            // label: 'TBA'
            label: 'Pending Approval',
          }, {
            value: 'APPROVED,DIRECT_APPROVED',
            label: 'Approved',
          }, {
            value: 'REJECTED',
            label: 'Rejected',
          }],
        status: [
          {
            value: 'INITIAL',
            label: 'Initial',
          }, {
            value: 'SENT',
            // label: 'TBA'
            label: 'Pending Approval',
          }, {
            value: 'APPROVED',
            label: 'Approved',
          }, {
            value: 'DIRECT_APPROVED',
            label: 'Direct Approved',
          }, {
            value: 'REJECTED',
            label: 'Rejected',
          }, {
            value: 'N/A',
            label: 'N/A',
          }, {
            value: 'NOT_APPLICABLE',
            label: '--',
          }],
      },
      date_range: [],
      expect_schedule_range: [],
      searchQuery: {
        page: 1,
        size: 10,
        'client.name_like': '',
        client_compliance_status_in: [],
        capvision_compliance_status_in: ['SENT'],
        request_by: undefined,
        project_manager: undefined,
        advisor_ids: [],
        sort: 'expect_schedule_date_is_null asc,overdue asc,urgent desc,expect_schedule_date asc',
      },
      sort_default: 'expect_schedule_date_is_null asc,overdue asc,urgent desc,expect_schedule_date asc',
      total: 0,
      data: {
        list: [],
      },
      select_client_id: 0,
      multipleSelection: [],
      client_not_need_approval: [],
      client_not_yet_approved: [],
      cap_not_yet_approved: [],
      rejected_tasks: [],
      cap_approved: [],
      client_approved: [],
      chaperones_required: [],
      client_approval_disabled: false,
      cap_approval_disabled: false,
      rejected_disabled: false,
      internal_status_options: [
        { label: 'Received', value: 'RECEIVED' },
        { label: 'Approved', value: 'APPROVED' },
        { label: 'Awaiting Client\'s confirmation by email that the Expert\'s current employer (PLC) will not be discussed on the call.', value: 'AWAITING_CLIENT_CONFIRMATION' },
        { label: 'Awaiting ECOM', value: 'AWAITING_ECOM' },
        { label: 'Awaiting research to update Expert\'s employment history', value: 'AWAITING_RESEARCH_UPDATE_EMPLOYMENT_HISTORY' },
        { label: 'Awaiting research to update company label - Incorrect classification as private/public/part of public', value: 'AWAITING_RESEARCH_UPDATE_COMPANY_LABEL' },
        { label: 'Awaiting research to add missing role', value: 'AWAITING_RESEARCH_ADD_MISSING_ROLE' },
        { label: 'Awaiting research to inform Client via email of Expert’s missing role', value: 'AWAITING_RESEARCH_INFORM_CLIENT_MISSING_ROLE' },
        { label: 'Awaiting research to inform Client via email of the correct company label', value: 'AWAITING_RESEARCH_INFORM_CLIENT_CORRECT_LABEL' },
        { label: 'Waiting for the client to confirm the investment target via email', value: 'AWAITING_CLIENT_CONFIRM_INVESTMENT_TARGET' },
        { label: 'Awaiting updated CA', value: 'AWAITING_UPDATED_CA' },
        { label: 'Awaiting updated T&C', value: 'AWAITING_UPDATED_TNC' },
        { label: 'Awaiting research to inform client via email of changes to the Expert’s profile', value: 'AWAITING_RESEARCH_INFORM_CLIENT_PROFILE_CHANGES' },
        { label: 'Awaiting research to inform client of changes to the Expert’s CA', value: 'AWAITING_RESEARCH_INFORM_CLIENT_CA_CHANGES' },
        { label: 'Awaiting research to update call schedule', value: 'AWAITING_RESEARCH_UPDATE_CALL_SCHEDULE' },
        { label: 'CA Failed - Flagged', value: 'CA_FAILED_FLAGGED' },
        { label: 'Work info mismatch (DB, LinkedIn, Client communication) - Awaiting Expert\'s confirmation by email', value: 'WORK_INFO_MISMATCH' },
        { label: 'Third-party call participants, unauthorized individuals joining the call – Flagged and awaiting next steps', value: 'THIRD_PARTY_UNAUTHORIZED_CALL_PARTICIPANTS' },
        { label: 'Other', value: 'OTHER_CUSTOM_STATUS' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'info',
    ]),

    allowRejectApprovedCall() {
      return this.cap_approved.length <= 0 || !!this.info.roles.find(item => item.role.name === 'AllowRejectApprovedCall')
    },
    rejectPrompt() {
      return this.cap_approved.length ? 'These tasks have been approved by Capvision Legal and may have been completed, are you sure you want to reject it?' : 'Please confirm this call is Reject?'
    },
  },
  mounted() {
    this.resolveRouteQuery()
    this.loadData()
  },
  methods: {
    loadData() {
      RouteTools.saveQueryToRouter(this.searchQuery)
      const {
        page,
        size,
        request_by,
        project_manager,
        capvision_compliance_status_in,
        client_compliance_status_in,
        sort,
        advisor_ids,
      } = this.searchQuery
      const query = {
        page,
        size,
        sort,
        extra: [
          'task.advisor.jobs.company',
          'task.advisor.company',
          'task.advisor.location',
          'task.advisor_profile.company',
          'task.advisor.latest_signed_tc.tc',
          'task.advisor.gdpr_delete_by',
          'task.project.members.user',
          'task.project.tags',
          'task.project.project_client_contacts.client_contact.contact_infos',
          'task.project.tickers.company',
          'task.project.investment_target_companies',
          'task.client.compliance_preference.chaperones',
          'task.client.account_managers.user',
          'task.latest_submitted_pre_ca.inquiry',
          'task.latest_submitted_pre_ca.files',
          'task.latest_submitted_pre_ca.inquiry_instance.questions',
          'task.latest_submitted_pre_ca.inquiry_instance.answers',
          'task.latest_submitted_sq.questions',
          'task.latest_submitted_sq.answers',
          'task.schedule',
          'task.latest_request_cap_approval_user',
          'task.legal_request',
          'task_legal_internal_status_logs',
          'task.task_client_chaperones.chaperone',
        ].join(),
      }

      const params = {
        task: {
          capvision_compliance_status_in: capvision_compliance_status_in.length
            ? capvision_compliance_status_in.join(',').split(',')
            : undefined,
          client_compliance_status_in: client_compliance_status_in.length ? client_compliance_status_in : undefined,
        },
      }

      if (request_by) {
        params.task.latest_cap_compliance_send = { create_by_id: request_by }
      }

      if (project_manager) {
        params.task.project = { manager: { user_id: project_manager } }
      }

      if (this.searchQuery['client.name_like']) {
        params.task.client = { name_like: this.searchQuery['client.name_like'] }
      }

      if (advisor_ids.length) {
        params.task.advisor = { ids: advisor_ids }
      }

      if (this.date_range && this.date_range.length) {
        params.task.latest_cap_compliance_send = {
          create_at_gte: this.date_range[0],
          create_at_lte: this.date_range[1],
        }
      }

      if (this.expect_schedule_range && this.expect_schedule_range.length) {
        params.task['legal_request.expect_schedule_date_gte'] = this.expect_schedule_range[0]
        params.task['legal_request.expect_schedule_date_lte'] = this.expect_schedule_range[1]
      }

      this.loading = true
      return ProjectAPI.getTaskLegalRequests(query, params).then((data) => {
        this.total = data.count
        this.data = this.handleData(data)
      }).finally(() => {
        this.loading = false
      })
    },

    resolveRouteQuery() {
      RouteTools.resolveRouteQuery(this.$route, this.searchQuery, ['client_compliance_status_in', 'capvision_compliance_status_in'])
      const capvision_compliance_status_in = new Set(this.searchQuery.capvision_compliance_status_in)
      if (capvision_compliance_status_in.has('DIRECT_APPROVED')) {
        capvision_compliance_status_in.delete('DIRECT_APPROVED')
        capvision_compliance_status_in.delete('APPROVED')
        capvision_compliance_status_in.add('APPROVED,DIRECT_APPROVED')
      }
      this.searchQuery.capvision_compliance_status_in = Array.from(capvision_compliance_status_in)
    },

    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.loadData()
      }, 500)
    },

    handleSortData({ order, prop }) {
      const dict = {
        'ascending': 'asc',
        'descending': 'desc',
      }
      if (!order) {
        this.searchQuery.sort = this.sort_default
      } else {
        if (prop === 'legal_request') {
          this.searchQuery.sort = `expect_schedule_date_is_null asc,overdue asc,urgent desc,expect_schedule_date ${dict[order]}`
        } else {
          this.searchQuery.sort = `${prop} ${dict[order]}`
        }
      }
      this.loadData()
    },

    handleData(data) {
      data.list = data.list.map(item => {
        item.task.project_manager = item.task.project?.members.find(member => member.role === 'PROJECT_MANAGER')
        item.task.internal_status = item.internal_status
        item.task.legal_request_id = item.id
        item.task.internal_status_custom_status = item.internal_status_custom_status
        item.task.task_legal_internal_status_logs = item.task_legal_internal_status_logs
        item.task.edit_custom_status = false
        item.task.loading = false
        item.task.internal_status_custom_status_new = ''
        item.task.chaperone_popover_visible = false
        this.initTaskClientChaperonesIds(item.task)
        return item.task
      })
      return data
    },

    initTaskClientChaperonesIds(task) {
      task.task_client_chaperones_ids = task.task_client_chaperones?.map(i => i.chaperone_id)
    },

    updateTaskClientChaperones(task) {
      const params = {
        project_id: task.project_id,
        id: task.id,
        task_client_chaperones: task.task_client_chaperones_ids.map(chaperone_id => ({ chaperone_id })),
      }
      return ProjectAPI.updateTask(params)
        .then(() => {
          this.loadData()
          task.chaperone_popover_visible = false
        }).finally(() => {
          task.chaperone_popover_visible = false
        })
    },

    handleSelectionChange(val) {
      if (val.length) {
        this.select_client_id = val[0].client_id
        val.forEach(item => {
          if (item.client_id !== this.select_client_id) {
            this.$refs.consultationApproval.toggleRowSelection(item, false)
          }
        })
        this.multipleSelection = val.filter(item => item.client_id === this.select_client_id)
      } else {
        this.select_client_id = 0
        this.multipleSelection = []
      }
      this.client_approval_disabled = false
      this.cap_approval_disabled = false
      this.rejected_disabled = false
      this.client_not_need_approval = []
      this.client_not_yet_approved = []
      this.cap_not_yet_approved = []
      this.cap_approved = []
      this.client_approved = []
      this.chaperones_required = []
      this.rejected_tasks = []

      if (this.multipleSelection.length) {
        const current_client = this.multipleSelection[0].client
        const client_preference = current_client.compliance_preference
        const clientApprovalAfterCAP = client_preference?.approve_policy === 'CAPVISION_THEN_CLIENT_APPROVAL'
        const clientApprovalSome = client_preference?.approve_policy === 'CAPVISION_AND_CLIENT_APPROVAL_SOME'

        this.multipleSelection.forEach(item => {
          const just_need_cap_approval = ['CAPVISION_APPROVAL'].includes(client_preference?.approve_policy) || ['N/A', 'NOT_APPLICABLE'].includes(item.client_compliance_status)
          const clientApproved = ['APPROVED'].includes(item.client_compliance_status)

          if (item.capvision_compliance_status === 'REJECTED') {
            this.rejected_disabled = true
            this.rejected_tasks.push(item.id)
          }

          if (just_need_cap_approval || clientApproved) {
            this.client_approval_disabled = true
            just_need_cap_approval ? this.client_not_need_approval.push(item.id) : this.client_approved.push(item.id)
          }

          if (clientApprovalAfterCAP && item.capvision_compliance_status !== 'APPROVED') {
            this.client_approval_disabled = true
            this.cap_not_yet_approved.push(item.id)
          }

          if (['APPROVED', 'DIRECT_APPROVED'].includes(item.capvision_compliance_status)) {
            this.cap_approval_disabled = true
            this.cap_approved.push(item.id)
          }

          if (item.client?.compliance_preference?.require_chaperone && !item.task_client_chaperones.length) {
            this.client_approval_disabled = true
            this.chaperones_required.push(item.id)
          }

          if (clientApprovalSome && notNeedClientApproveForSOME(item, item.client?.compliance_preference)) {
            this.client_approval_disabled = true
            this.client_not_need_approval.push(item.id)
          }
        })
      }

      // 客户法务规则设置依据CA状态为hold时才需要审核。当CA:AUTO_PASSED 无需客户法务审核
      function notNeedClientApproveForSOME(task, compliance_preference) {
        const client_conditional_approve_type = compliance_preference?.rule?.compliance_rules.approval.client_conditional_approve_type
        const task_ca_auto_passed = task.latest_submitted_pre_ca?.inquiry_instance?.status === 'AUTO_PASSED'
        return task_ca_auto_passed && client_conditional_approve_type === 'Based on CA'
      }
    },
    actionCapvisionApprove() {
      this.changeApproveStatus('APPROVED')
    },
    actionReject() {
      this.$prompt('', 'Reject Reason', {
        confirmButtonText: 'Submit',
      })
        .then(({ value }) => {
          this.changeApproveStatus('REJECTED', value)
        })
        .catch(() => {
        })
    },
    actionDirectApprove() {
      this.changeApproveStatus('CAPVISION_DIRECT_APPROVED')
    },

    changeApproveStatus(target_status, comment = undefined, isClientApprove = false) {
      const params = {
        batch: this.multipleSelection.map(item => {
          return {
            task_id: item.id,
            result: target_status,
            comment,
          }
        }),
      }

      const url = isClientApprove ? 'client_legal_reviews' : 'cap_legal_reviews'
      return AppHttpService
        .post(`/tasks/${url}/batch`, params)
        .then(this.loadData)
    },

    checkSelectable(row) {
      // 目前限制只能批量操作同一个客户的task
      return !this.select_client_id || row.client_id === this.select_client_id
    },

    actionClientApprove() {
      this.changeApproveStatus('APPROVED', undefined, true)
    },

    actionUpdateInternalStatus(row, status, custom_status = undefined) {
      if (status === 'OTHER_CUSTOM_STATUS' && !custom_status) {
        row.edit_custom_status = true
        return
      }

      row.loading = true
      const params = {
        internal_status: status,
        internal_status_custom_status: custom_status,
      }
      return ProjectAPI.changeTaskLegalRequest(row.legal_request_id, params)
        .then(data => {
          row.loading = false
          row.edit_custom_status = false
          row.internal_status = data.internal_status
          row.internal_status_custom_status = data.internal_status_custom_status
          row.task_legal_internal_status_logs.push(data)
        })
    },

    getStatusLabel(row) {
      let result = '-'
      if (row.internal_status === 'OTHER_CUSTOM_STATUS') return `Other(${row.internal_status_custom_status})`
      this.internal_status_options.forEach(item => {
        if (item.value === row.internal_status) {
          result = item.label
        }
      })
      return result
    },
  },
}
</script>

<style scoped lang="scss">
.tool-switch {
  min-height: 40px;
}

::v-deep .el-table__expanded-cell {
  padding: 0;
}

.dropdown-link {
  font-size: 12px;
  cursor: pointer;
}

.mr-auto {
  margin-right: auto;
}
</style>
