<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="loadData" />
      <el-input
        v-model="searchQuery.id"
        placeholder="ID"
        class="w-172px filter-item"
        @input="debounceResetSearch" />
      <el-input
        v-model="searchQuery.project_tsid"
        placeholder="Project Ts ID"
        class="w-172px filter-item"
        @input="debounceResetSearch" />
      <project-search
        v-model="searchQuery.project_ids"
        class="filter-item"
        @change="resetSearch" />
      <el-select
        v-model="searchQuery.type"
        class="filter-item"
        @change="resetSearch"
      >
        <el-option value="ALL" label="All Type" />
        <el-option value="IMPORT" label="Import" />
        <el-option value="EXPORT" label="Export" />
      </el-select>
      <!--      <el-select-->
      <!--        v-model="searchQuery.outsource_status"-->
      <!--        class="filter-item"-->
      <!--        @change="resetSearch"-->
      <!--      >-->
      <!--        <el-option value="ALL" label="All Status" />-->
      <!--        <el-option value="APPROVED" label="Approved" />-->
      <!--        <el-option value="REJECTED" label="Rejected" />-->
      <!--        <el-option value="INITIAL" label="Initial" />-->
      <!--      </el-select>-->
    </div>

    <outsource-approval-table
      v-loading="loading"
      :data="list"
      @reply="loadData"
    />

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="loadData"
    />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { debounce } from '@/utils/tool'
import OutsourceAPI from '@/api/outsource'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch/index'
import OutsourceApprovalTable from './components/OutsourceApprovalTable'

export default {
  name: 'OutsourceApprovalIndex',
  components: { ProjectSearch, RefreshButton, Pagination, OutsourceApprovalTable },
  data() {
    return {
      loading: false,
      searchQuery: {
        page: 1,
        size: 10,
        extra: 'project,events',
        id: undefined,
        project_ids: [],
        project_tsid: undefined,
        type: 'ALL',
        // outsource_status: 'ALL',
      },
      total: 0,
      list: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      const params = {
        ...this.searchQuery,
        id: this.searchQuery.id || undefined,
        project_ids: this.searchQuery.project_ids.join() || undefined,
        project_tsid: this.searchQuery.project_tsid || undefined,
        outsource_advisor_to: this.searchQuery.type === 'IMPORT' ? (this.isSGDB ? 'SEA' : 'US') : undefined,
        outsource_advisor_from: this.searchQuery.type === 'EXPORT' ? (this.isSGDB ? 'SEA' : 'US') : undefined,
      }
      delete params.type
      return OutsourceAPI.getOutsourceApprovalList(params)
        .then(data => {
          this.total = data.count
          this.list = data.list.map(item => {
            item.expandActiveTab = 'events'
            item.events.reverse()
            return item
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    debounceResetSearch() {
      debounce(() => {
        this.resetSearch()
      }, 500)
    },
    resetSearch() {
      this.searchQuery.page = 1
      this.loadData()
    },
  },
}
</script>

<style scoped>
</style>
