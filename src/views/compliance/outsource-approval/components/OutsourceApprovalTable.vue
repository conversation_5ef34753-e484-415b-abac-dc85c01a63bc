<template>
  <div>
    <el-table
      border
      stripe
      :row-key="row => row.id"
      :expand-row-keys="expandRowKeys"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-table-column type="expand">
        <template #default="{row}">
          <div class="expand-content">
            <el-tabs v-model="row.expandActiveTab">
              <el-tab-pane label="Events" name="events">
                <el-table
                  :data="row.events"
                  border
                >
                  <el-table-column label="Event ID" prop="id" width="70" />
                  <el-table-column label="Operator" width="180">
                    <template v-slot="scope">
                      {{ scope.row.operator_name || scope.row.operator_email }}
                    </template>
                  </el-table-column>
                  <el-table-column label="Event Type" width="100">
                    <template v-slot="scope">
                      {{ scope.row.event_type }}
                    </template>
                  </el-table-column>
                  <el-table-column label="Create Time" width="130">
                    <template v-slot="scope">
                      {{ scope.row.create_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
                    </template>
                  </el-table-column>
                  <el-table-column label="Message Source" width="80">
                    <template v-slot="scope">
                      {{ scope.row.message_source }}
                    </template>
                  </el-table-column>
                  <el-table-column label="Message">
                    <template v-slot="scope">
                      <span v-if="row.type === 'IMPORT' ? scope.row.event_type !== 'APPROVE' : true">
                        {{ scope.row.message }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane
                v-for="item in options.tabs"
                :key="item.value"
                :label="item.label"
                :name="item.value"
                :disabled="!row[item.value]"
              >
                <div class="pre-line request-content scrollbar-narrow" v-html="$sanitizeHtml(row[item.value])" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </template>
      </el-table-column>

      <template v-if="!inProject">
        <el-table-column key="id" label="ID" prop="id" width="55" />
        <el-table-column key="project_tsid" label="Project Ts ID" prop="project_tsid" width="120" />
      </template>
      <el-table-column key="project" label="Project">
        <template #default="{row}">
          <router-link-project v-if="row.project" :data="row.project" />
          <span v-else>{{ row.project_name }}</span>
        </template>
      </el-table-column>
      <el-table-column key="project_type" label="Project Type" prop="project_type" width="85" />
      <template v-if="!inProject">
        <el-table-column key="outsource_advisor_from" label="To" prop="outsource_advisor_from" width="48" />
        <el-table-column key="outsource_advisor_to" label="From" prop="outsource_advisor_to" width="48" />
        <el-table-column label="Type" prop="type" width="77">
          <template #default="{row}">
            <span v-if="row.type === 'EXPORT'" class="success">
              <svg-icon icon-class="export" /> Export
            </span>
            <span v-if="row.type === 'IMPORT'" class="primary">
              <svg-icon icon-class="import" /> Import
            </span>
          </template>
        </el-table-column>
      </template>
      <template v-else>
        <el-table-column key="outsource_region" label="Region" prop="outsource_region" width="60" />
      </template>
      <el-table-column label="Create Time" width="125">
        <template #default="{row}">
          {{ row.create_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column label="Update Time" width="125">
        <template #default="{row}">
          {{ row.update_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column label="Status" align="center">
        <el-table-column label="Local" width="85">
          <template #default="{row}">
            <span :class="getApprovalStatus(row.local_approval)">
              {{ row.local_approval | getLabel(options.approval) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="Outsource" width="85">
          <template #default="{row}">
            <span :class="getApprovalStatus(row.outsource_approval)">
              {{ row.outsource_approval | getLabel(options.approval) }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="Events" width="60">
        <template #default="{row}">
          <el-link type="primary" :underline="false" @click="manualExpandEvents(row, 'events')">{{ row.events.length }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="Action" width="140">
        <template #default="{row}">
          <el-tooltip
            v-if="hasPermission && ['INITIAL'].includes(row.local_approval) && row.is_latest_approval"
            content="Approve"
            placement="top"
          >
            <el-button
              type="success"
              plain
              icon="el-icon-check"
              class="action-button"
              @click="openActionApprovalDialog(row, 'Approve')"
            />
          </el-tooltip>
          <el-tooltip
            v-if="hasPermission && ['INITIAL', 'APPROVED'].includes(row.local_approval) && row.is_latest_approval"
            content="Reject"
            placement="top"
          >
            <el-button
              type="danger"
              plain
              icon="el-icon-close"
              class="action-button"
              @click="openActionApprovalDialog(row, 'Reject')"
            />
          </el-tooltip>
          <el-tooltip
            v-if="(row.type !== 'IMPORT' || row.events.some(e => e.event_type === 'REPLY')) && row.is_latest_approval"
            content="Reply"
            placement="top"
          >
            <el-button
              type="primary"
              plain
              icon="el-icon-chat-dot-round"
              class="action-button"
              @click="openActionApprovalDialog(row, 'Reply')"
            />
          </el-tooltip>
          <el-tooltip
            v-if="hasPermission && ['APPROVED'].includes(row.local_approval) && row.is_latest_approval"
            content="Comment"
            placement="top"
          >
            <el-button
              type="info"
              plain
              icon="el-icon-chat-line-round"
              class="action-button"
              @click="openActionApprovalDialog(row, 'Comment')"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <action-approval-dialog
      ref="actionApprovalDialog"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project.vue'
import ActionApprovalDialog from '@/views/compliance/outsource-approval/components/ActionApprovalDialog.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'OutsourceApprovalTable',
  components: { RouterLinkProject, ActionApprovalDialog },
  props: {
    inProject: Boolean,
  },
  data() {
    return {
      expandRowKeys: [],
      options: {
        approval: [
          { value: 'INITIAL', label: 'Initial' },
          { value: 'REJECTED', label: 'Rejected' },
          { value: 'APPROVED', label: 'Approved' },
          { value: 'CANCELED', label: 'Canceled' },
        ],
        tabs: [
          { value: 'request_content', label: 'Request Content' },
          { value: 'original_request_content', label: 'Original Request Content' },
          { value: 'client_event_rules', label: 'Compliance Rules' },
          { value: 'outsource_client_event_rules', label: 'Outsource Compliance Rules' },
          { value: 'text_outsource_screen_questions', label: 'Outsource Screening Questions' },
        ],
      },
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    hasPermission() {
      return this.roles.some(item => ['Admin', 'Compliance'].includes(item.role.name))
    },
  },
  methods: {
    manualExpandEvents(approval, tabName) {
      const index = this.expandRowKeys.findIndex(item => item === approval.id)
      if (index === -1) {
        this.expandRowKeys.push(approval.id)
      } else {
        if (approval.expandActiveTab === tabName) {
          this.expandRowKeys.splice(index, 1)
        }
      }
      approval.expandActiveTab = tabName

      if (this.inProject) {
        this.$emit('update:data', [...this.$attrs.data])
      }
    },
    openActionApprovalDialog(approval, type) {
      this.$refs['actionApprovalDialog'].show(approval, type)
    },
    getApprovalStatus(status) {
      switch (status) {
        case 'APPROVED':
          return 'success'
        case 'REJECTED':
          return 'danger'
        case 'CANCELED':
          return 'info'
        default:
          return ''
      }
    },
  },
}
</script>

<style scoped lang="scss">
.expand-content {
  padding: 0 1rem 1rem;

  ::v-deep .el-tabs__header {
    margin-bottom: 12px;
  }

  ::v-deep .el-tabs__item {
    padding: 0 18px;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
  }
}

.request-content {
  max-height: calc(100vh - 100px);
  overflow: auto;
}

.action-button {
  padding: 4px 8px;
}
</style>
