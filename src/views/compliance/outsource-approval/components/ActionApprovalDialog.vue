<template>
  <el-dialog
    :title="`${actionType} Approval`"
    :visible.sync="dialogVisible"
    append-to-body
  >
    <el-form>
      <el-form-item>
        <el-input
          v-model="form.message"
          type="textarea"
          :placeholder="`${actionType} Message`"
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        type="text"
        @click="dialogVisible = false"
      >
        Close
      </el-button>
      <el-button
        :type="actionType | getLabel(options.action_types)"
        :disabled="actionType === 'Reply' ? !form.message : false"
        :loading="submitting"
        @click="handleActionApproval"
      >
        {{ actionType }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import OutsourceAPI from '@/api/outsource'

export default {
  name: 'ActionApprovalDialog',
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      actionType: '',
      data: {},
      form: {
        message: '',
      },
      options: {
        action_types: [
          {
            label: 'success',
            value: 'Approve',
            type: 'APPROVE',
          }, {
            label: 'danger',
            value: 'Reject',
            type: 'REJECT',
          }, {
            label: 'primary',
            value: 'Reply',
            type: 'REPLY',
          }, {
            label: 'primary',
            value: 'Comment',
            type: 'COMMENT',
          },
        ],
      },
    }
  },
  methods: {
    show(data, type) {
      this.form.message = ''
      this.actionType = type
      this.data = data
      this.dialogVisible = true
    },
    handleActionApproval() {
      const params = {
        approval_id: this.data.id,
        message: this.form.message.trim(),
        event_type: this.options.action_types.find(item => item.value === this.actionType).type,
      }
      this.submitting = true
      return OutsourceAPI.actionOutsourceApproval(params)
        .then(() => {
          this.$message.success('success')
          this.$emit('reply')
          this.dialogVisible = false
        })
        .finally(() => {
          this.submitting = false
        })
    },
  },
}
</script>
