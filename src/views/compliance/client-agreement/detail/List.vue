<template>
  <div class="app-container">
    <div class="flex">
      <div class="question-content">
        <div v-if="current_instance.id" class="filter-container">
          <el-button
            class="filter-item"
            type="success"
            icon="el-icon-plus"
            @click="createInquiry()">
            New
          </el-button>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-edit"
            @click="editInquiry()">
            Edit
          </el-button>
          <el-link
            type="primary"
            class="filter-item"
            @click="actionGoToHistory">Update History
          </el-link>
        </div>
        <div v-if="detail_show">
          <div v-if="!current_left_list.length" class="questionnaire-no">
            <el-button
              type="primary"
              icon="el-icon-plus"
              class="mt-8 ml-8"
              @click="createInquiry(activeName)">
              Create
            </el-button>
            <span>No Data</span>
          </div>

          <questionnaire v-else :question-content="current_instance" />
        </div>
      </div>

      <div v-loading="loading" class="inquiries-list" :class="{ 'list-mt' :detail_show }">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane v-for="item in lang_type_options" :key="item.value" :label="item.label" :name="item.value" />
        </el-tabs>
        <el-card
          v-for="item in current_left_list"
          :key="item.id"
          class="box-card"
          :class="{'col-3':!detail_show,'active':item.id === current_instance.id}"
          shadow="hover"
          @click.native="getInquiryContent(item.id)"
        >
          <div slot="header" class="card-head">
            <span class="title">{{ item.title }}</span>
            <span class="card-id">{{ item.inquiry_type | getLabel(type_options) }} / ID - {{ item.id }}</span>
          </div>

          <div class="card-actions" @click.stop>
            <div class="set-default-box">
              <el-button v-if="item.is_default_using" class="item success" type="text" icon="el-icon-star-on" />
              <el-popconfirm
                v-else
                icon="el-icon-info"
                icon-color="#13ce66"
                title="Are you sure you want to set as the default questionnaire?"
                cancel-button-text="Cancel"
                confirm-button-text="Confirm"
                confirm-button-type="success"
                @confirm="defaultInquiry(item)"
              >
                <el-button slot="reference" class="item" type="text" icon="el-icon-star-off" />
              </el-popconfirm>
            </div>
            <!--  disabled     兼容旧数据 后期恢复-->
            <el-tooltip effect="dark" content="Copy(Temporarily disabled)" placement="top">
              <div class="inline-block">
                <el-button
                  disabled
                  class="item"
                  type="text"
                  icon="el-icon-document-copy"
                  @click="copyInquiry(item.id)" />
              </div>
            </el-tooltip>
            <el-popconfirm
              icon="el-icon-info"
              icon-color="red"
              title="Are you sure you want to delete this questionnaire?"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="danger"
              @confirm="removeInquiry(item)"
            >
              <el-button
                slot="reference"
                type="text"
                :disabled="current_left_list.length === 1"
                class="item danger"
                icon="el-icon-delete"
              />
            </el-popconfirm>

          </div>
        </el-card>
      </div>
    </div>

  </div>
</template>

<script>
import Questionnaire from './Questionnaire_v2'
import InquiryAPI from '@/api/inquiry'
import { mapGetters } from 'vuex'
import { RouteTools } from '@/utils/tool'

export default {
  name: 'List',
  components: { Questionnaire },
  props: {
    id: {
      type: Number,
      default: null,
    },
  },

  data() {
    return {
      activeName: 'en-US',
      all_list: [],
      current_instance: {},
      client_name: '',
      client_id: this.$route.query.client_id,
      inquiry_id: this.$route.query.inquiry_id,
      query_branch_id: +this.$route.query.branch_id || undefined,
      inquiry_type: undefined,
      detail_show: false,
      loading: true,
      type_options: [
        { value: 'PRE_CALL_CA', label: 'Pre Call' },
        { value: 'POST_CALL_CA', label: 'Post Call' },
        // { value: 'LISTED_ONLY_CA', label: 'Listed Only' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'lang_type_options',
    ]),
    current_left_list() {
      return this.all_list.filter(item => item.locale === this.activeName)
    },
  },

  created() {
    this.activeName = this.$route.query.lang_type || 'en-US'
    this.getInquiries()
  },

  methods: {
    getInquiries() {
      this.loading = true
      RouteTools.resolveRouteQuery(this.$route)
      return InquiryAPI.getInquiryOne(this.inquiry_id)
        .then(data => {
          this.all_list = data.branches
          this.inquiry_type = data.type
          this.client_id = data.client_id
          this.client_name = data.client?.name
          RouteTools.setRouterMetaTitle(`${data['client']['name']} CA`, this.$route)
        })
        .finally(() => {
          this.loading = false
          this.getInquiryContent(this.query_branch_id)
        })
    },

    getInquiryContent(branch_id) {
      if (branch_id) {
        this.current_instance = this.all_list.filter(item => item.id === branch_id)[0]
      } else {
        this.current_instance = this.current_left_list.length ? this.current_left_list.filter(
          item => item.is_default_using)[0] : {}
      }

      this.detail_show = true
    },

    createInquiry(lang_type) {
      const query = {
        inquiry_id: this.inquiry_id,
        client_id: this.client_id,
        type: this.inquiry_type,
      }
      query.lang_type = lang_type || undefined

      this.$router.push({
        name: 'ClientAgreementCreate',
        query,
      })
    },

    editInquiry() {
      this.$router.push({
        name: 'ClientAgreementCreate',
        query: { inquiry_id: this.inquiry_id, branch_id: this.current_instance.id, is_update: true },
      })
    },

    copyInquiry(branch_id) {
      this.$router.push({
        name: 'ClientAgreementCreate',
        query: { inquiry_id: this.inquiry_id, branch_id: branch_id },
      })
    },

    defaultInquiry(item) {
      return InquiryAPI.setDefaultCA(item.inquiry_id, item.id)
        .then(() => {
          this.getInquiries()
        })
    },

    removeInquiry(item) {
      return InquiryAPI.deleteInquiry(item.inquiry_id, item.id)
        .then(() => {
          if (item.id === this.query_branch_id) this.query_branch_id = null
          this.getInquiries()
        })
    },

    handleClick(tab, event) {
      this.current_instance = {}
      if (this.current_left_list.length) {
        this.current_instance = this.current_left_list.filter(item => item.is_default_using)[0] ||
          this.current_left_list[0]
      }
    },

    actionGoToHistory() {
      this.$router.push({
        name: 'ClientComplianceRulesHistory',
        params: { client_id: this.client_id },
        query: {
          client_name: this.client_name,
          entity_id: this.current_instance.id,
          type: 'CA' },
      })
    },

  },
}
</script>

<style lang="scss" scoped>
.inquiries-list {
  flex: 2;
}

.list-mt {
  /*margin-top: 42px;*/
  margin-left: 20px;
}

.question-content {
  flex: 4;

  ::v-deep .el-tabs__item.is-active {
    color: #42b983;
  }

  ::v-deep .el-tabs__item:hover {
    color: #42b983;
  }

  ::v-deep .el-tabs__active-bar {
    background-color: #42b983;
  }
}

.create-button {
  margin-bottom: 6px;
}

.box-card {
  margin: 0 10px 10px 0;
}

.active {
  border: 1px solid #42B983;
}

.col-3 {
  display: inline-block;
  width: 32%;
}

.card-head {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding-bottom: 10px;

  .title {
    font-size: 1.1rem;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:hover {
      color: #3f9eff;
      cursor: pointer;
    }
  }

  .card-id {
    white-space: nowrap;
    margin: 0 20px;
  }
}

.card-actions {
  float: right;

  .item {
    font-size: 1rem;
    margin-right: 5px;
  }

  .set-default-box {
    display: inline-block;
  }
}

.questionnaire-no {
  border: 1px solid #dbdde0;
  border-radius: 10px;
  background-color: #f0f1f338;
  height: 500px;

  span {
    display: block;
    height: 300px;
    line-height: 300px;
    width: 100%;
    font-weight: 600;
    color: #abadb1;
    text-align: center;
  }

}
</style>
