<template>
  <div class="app-container questionnaire-container w-1400">
    <div>
      <div v-if="title && !is_created" class="question-subject">
        <div class="title">{{ title }}</div>
      </div>
      <div v-else>
        <div class="flex">
          <el-radio-group
            v-model="language_type"
            :disabled="!!is_update"
            size="small"
            class="language-btn">
            <el-radio-button label="en-US">EN_US</el-radio-button>
            <el-radio-button label="zh-CN">ZH_CN</el-radio-button>
            <el-radio-button label="en-zh">EN & ZH</el-radio-button>
          </el-radio-group>
        </div>
        <el-divider />

        <div class="question-subject-edit">
          <div class="text-center">
            <el-input v-model="title" class="title" />
          </div>
        </div>
      </div>
    </div>
    <div
      v-for="(item, index) in question_form"
      :id="`question-`+index"
      :key="item.temp_index"
      :class="[is_created?'question-box':'question-box-item']"
    >
      <div>
        <div v-if="is_created">
          <new-edit
            :question.sync="question_form[index]"
            :question-index="index"
            :language-type="language_type"
            :show-question="true" />
          <div class="question-actions-row">
            <el-button type="danger" circle icon="el-icon-delete" @click="removeQuestion(index)" />
            <el-button v-if="index > 0" circle icon="el-icon-top" @click="moveUp(index)" />
            <el-button
              v-if="index < question_form.length-1"
              circle
              icon="el-icon-bottom"
              @click="moveDown(index)" />
          </div>
        </div>
        <show-question v-if="!is_created" :form="item" />
      </div>
    </div>
    <div v-if="is_created">
      <div>
        <el-button
          icon="el-icon-plus"
          type="success"
          @click="actionAddQuestion()">
          Add Question
        </el-button>
        <el-popover
          v-if="question_form.length"
          v-model="popoverVisible"
          width="300"
          placement="top"
        >
          <div v-if="noOptionsQuestion.length>0">
            There are some problematic options that are not set, please check number:
            <span class="danger">{{ noOptionsQuestion.join() }}</span>
          </div>
          <div v-else>
            <p><i class="el-icon-info" /> Confirm to generate？</p>
            <div style="text-align: right;">
              <el-button type="text" @click="popoverVisible = false">Cancel</el-button>
              <el-button v-if="!inquiry_id" type="primary" @click="createInquiry">Create</el-button>
              <el-button v-else type="primary" @click="updateInquiry">Confirm</el-button>
            </div>
          </div>
          <el-button
            slot="reference"
            :disabled="!question_form.length"
            type="success"
            style="float:right"
            icon="el-icon-s-claim"
          >
            Generate
          </el-button>
        </el-popover>
      </div>
    </div>

    <client-select :center-dialog-visible.sync="has_client" @save="saveClient" />

    <div id="ca-copy" class="display-none">
      <div>
        Hello <span>{{ client_am }}</span>,
        The client agreement for <span>{{ client_name }}</span> has been approved by Capvision Compliance.
      </div>
      <p>
        Thank you <br>
        <span>{{ name }}</span>
      </p>
      <ca-copy :data="copy_data" />
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import _ from 'lodash'
import ClientAPI from '@/api/client'
import InquiryAPI from '@/api/inquiry'
import ClientSelect from '../components/ClientSelect'
import CA_mixin from '../components/mixin/index'
import NewEdit from '../components/EditQuestion'
import ShowQuestion from '@/views/compliance/client-agreement/components/showQuestion'
import CaCopy from '@/components/Question/CACopy'

export default {
  name: 'QuestionV2',
  components: { ShowQuestion, ClientSelect, NewEdit, CaCopy },
  mixins: [CA_mixin],
  props: {
    questionContent: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },

  data() {
    return {
      CA_info: {
        creator_type: 'KM',
        type: '',
        name: '',
      },
      language_type: 'en_US',
      question_form: [],
      has_client: false,
      title: '',
      description: '',
      client_name: undefined,
      client_am: undefined,
      client_id: null,
      inquiry_id: null,
      branch_id: null,
      is_created: true,
      popoverVisible: false,
      default_inquiry: true,
      showForCopy: false,
      is_update: false,
      copy_data: {
        title: '',
        description: '',
        questions: [],
      },
      temp_index_count: 0,
    }
  },

  computed: {
    ...mapGetters([
      'name',
    ]),
    noOptionsQuestion() {
      const no_options_question = []
      this.question_form.forEach((item, index) => {
        if (item.type === 'RADIO' && !item.options.length) {
          no_options_question.push(index + 1)
        }
      })
      return no_options_question
    },
  },

  watch: {
    'questionContent': {
      handler(newVal) {
        this.getQuestion(newVal)
      },
    },
  },

  mounted() {
    // 查看
    if (this.questionContent.inquiry_id) {
      this.is_created = false
      this.inquiry_id = this.questionContent.inquiry_id
      this.getQuestion(this.questionContent)
    } else {
      this.is_created = true
      const query = this.$route.query
      this.inquiry_id = query.inquiry_id
      this.branch_id = query.branch_id
      this.has_client = !query.inquiry_id

      if (query.branch_id) {
        // copy & update
        // 防止页面刷新 question_form 不存在
        this.showForCopy = true
        this.is_update = query.is_update
        this.getCaContent(query.branch_id)
      } else {
        // add  create
        this.client_id = query.client_id
        this.CA_info.type = query.type
        this.title = this.autoTitle(query.type)
        this.language_type = query.lang_type || 'en-US'
        this.client_id ? this.getClientProfile() : ''
      }
    }
  },

  methods: {
    getClientProfile() {
      return ClientAPI.getClient(this.client_id, 'account_managers.user')
        .then(data => {
          this.client_name = data.name
          this.client_am = data.account_managers.map(item => item.user.name)
            .join(', ')
        })
    },
    autoTitle(type) {
      const type_name = type === 'PRE_CALL_CA' ? 'Pre-Consultation' : 'Post-Consultation'
      return type_name + ' Client Agreement'
    },
    saveClient(val) {
      this.client_id = val.client_id
      this.title = this.autoTitle(val.type)
      this.CA_info = Object.assign(this.CA_info, val)
    },

    getCaContent(branch_id) {
      return InquiryAPI.getInquiryOne(this.inquiry_id)
        .then(data => {
          this.client_id = data.client_id
          const result = data.branches.filter(item => item.id === +branch_id)[0]
          this.getQuestion(result)

          this.title = result.title || ''
          this.language_type = result.locale

          this.getClientProfile()
        })
    },

    getQuestion(item) {
      this.title = item.title
      this.question_form = item.questions.map((question, index) => {
        question.show_type = undefined
        question.temp_index = index
        return question
      })
      this.temp_index_count = item.questions.length
    },

    actionAddQuestion() {
      this.temp_index_count++
      this.question_form.push({
        type: 'RADIO',
        show_type: 'acknowledgement',
        tags: [],
        title: '',
        is_required: true,
        options: [
          { type: 'ALLOW', value: 'AGREE', text: 'I Agree', description: false },
          { type: 'DENY', value: 'AGREE_NOT', text: 'I Disagree', description: false }],
        temp_index: this.temp_index_count,
        sort_order: this.question_form.length,
      })
    },

    removeQuestion(index) {
      this.question_form.splice(index, 1)
    },

    moveDown(index) {
      const arr = this.question_form
      const temp = arr[index]
      arr.splice(index, 1)
      setTimeout(() => {
        arr.splice(index + 1, 0, temp)
      }, 0)
    },

    moveUp(index) {
      const arr = this.question_form
      const temp = arr[index]
      arr.splice(index, 1)
      setTimeout(() => {
        arr.splice(index - 1, 0, temp)
      }, 0)
    },

    createInquiry() {
      this.CA_info.client_id = this.client_id
      const data = this.CA_info

      data.branch = {
        locale: this.language_type,
        title: this.title,
        description: '',
        questions: this.formatQuestions(),
      }

      this.updateCopyCA(data.branch)

      if (this.CA_info.is_required) {
        this.changeClientRequiredStatus(this.CA_info.client_id, this.CA_info.type, true)
      }
      return InquiryAPI.addInquiry(data)
        .then(() => {
          this.spendEmailToContactAssociatedPerson()
          this.$router.push({ name: 'ClientAgreementList', params: { client_id: this.client_id } })
        }, err => {
          console.log(err)
        })
        .finally(() => {
          this.popoverVisible = false
        })
    },

    updateInquiry() {
      const params = {
        locale: this.language_type,
        title: this.title,
        description: '',
        questions: this.formatQuestions(),
      }

      this.updateCopyCA(params)
      const request = this.is_update
        ? InquiryAPI.updateQuestion(this.inquiry_id, this.branch_id, params, 'SAVE,UPDATE,DELETE')
        : InquiryAPI.updateInquiry(this.inquiry_id, params)

      return request.then((data) => {
        this.spendEmailToContactAssociatedPerson()
        this.$router.push({
          name: 'ClientAgreementDetail',
          query: { inquiry_id: this.inquiry_id, branch_id: data['id'], lang_type: this.language_type },
        })
      }, err => {
        console.log(err)
      })
        .finally(() => {
          this.popoverVisible = false
        })
    },

    formatQuestions() {
      return _.cloneDeep(this.question_form)
        .filter(item => item.show_type && item.title)
        .map((item, index) => {
          const { title, type, options, is_required, tags } = item
          return { title, type, sort_order: index, options, is_required, tags }
        })
    },

    updateCopyCA(content) {
      this.copy_data.title = content.title
      this.copy_data.description = content.description
      this.copy_data.questions = content.questions
        .map(item => {
          var res = _.cloneDeep(item)
          if (res.sub_questions) {
            res.sub_questions.forEach(sub => {
              sub.answer = ''
            })
          } else {
            res.answer = ''
          }
          return res
        })
    },

    spendEmailToContactAssociatedPerson() {
      const ca_copy_element = document.querySelector('#ca-copy')
      return ClientAPI.getClient(this.client_id, 'compliance_preference')
        .then(data => {
          // 客户 法务规则勾选 Arrange/Emails and Invites from capvision-pro.com时，用户通过复制模板内容，自己从Outlook发送邮件，不在系统中发送给客户
          const has_rule = data.compliance_preference?.rule
          const pro = has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
            has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro

          if (pro) {
            this.$message({
              showClose: true,
              message: `The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com), it will not send relevant emails through the system.`,
              type: 'warning',
              offset: 100,
              dangerouslyUseHTMLString: true,
              duration: 5000,
            })
            return Promise.resolve()
          } else {
            const params = {
              subject: 'Compliance Client Agreement',
              content: ca_copy_element.innerHTML,
            }
            return ClientAPI.spendEmailToContactAssociatedPerson(this.client_id, params)
          }
        })
    },

  },
}
</script>

<style scoped lang="scss">
.question-box-item {
  margin-bottom: 10px;
  padding: 20px;
  //user-select: none;

  .order {
    min-width: 1rem;
  }

  .content-html {
    width: 90%;
    padding-left: 3px;
    overflow-wrap: break-word;
    word-break: break-all;
  }
}

.question-actions-row {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f9f8f8;
}

::v-deep .el-radio-button__inner {
  border-left: 1px solid #DCDFE6;
  border-radius: 4px;
}

::v-deep .el-radio-button {
  margin-right: 20px;
}

::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 4px;
}

::v-deep .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 4px;
}

::v-deep .el-checkbox-button__inner {
  border-left: 1px solid #DCDFE6;
  border-radius: 4px;
}

::v-deep .el-checkbox-button {
  margin-right: 20px;
}

::v-deep .el-checkbox-button:first-child .el-checkbox-button__inner {
  border-radius: 4px;
}

::v-deep .el-checkbox-button:last-child .el-checkbox-button__inner {
  border-radius: 4px;
}

</style>
