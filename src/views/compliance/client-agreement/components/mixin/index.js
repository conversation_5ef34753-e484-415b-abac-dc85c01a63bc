import ClientAPI from '@/api/client'
import InquiryAP<PERSON> from '@/api/inquiry'

export default {
  data() {
    return {
      total: 0,
      selectLoading: false,
      loading: true,
      CAList: [],
      clientList: [],
      CA_name_list: [],
      CA_type_options: [
        { value: 'PRE_CALL_CA', label: 'Pre-Consultation', disabled: false },
        { value: 'POST_CALL_CA', label: 'Post-Consultation', disabled: false },
        // { value: 'LISTED_ONLY_CA', label: 'Listed Only', disabled: false },
      ],
    }
  },

  methods: {
    getList(value) {
      this.loading = true
      this.CAList = []
      const types = this.CA_type_options.map(item => {
        return item.value
      })

      const params = Object.assign({
        types: types.join(','),
        sort: 'client.name asc',
        'client.page': 0,
      }, value)
      InquiryAPI.getInquiryList(params)
        .then(data => {
          this.total = data.count
          this.CAList = data.list
          this.CA_name_list = data.list.map(item => item.name)
          this.loading = false
        })
    },
    getClient(val) {
      const params = {
        name_like: val || undefined,
        page: 1,
        size: 15,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList = data.list
        })
    },

    changeClientRequiredStatus(client_id, type, status) {
      const data = {
        id: client_id,
      }
      switch (type) {
        case 'LIST_ONLY_CA':
          data.require_list_only_ca = status
          break
        case 'POST_CALL_CA':
          data.require_post_call_ca = status
          break
        case 'PRE_CALL_CA':
          data.require_pre_call_ca = status
          break
      }

      return ClientAPI.updateClient(data)
    },
  },
}
