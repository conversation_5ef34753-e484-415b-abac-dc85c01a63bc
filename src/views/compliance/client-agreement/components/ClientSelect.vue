<template>
  <el-dialog
    :visible.sync="centerDialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="Creating Client Agreement"
    width="30%"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="70px">
      <el-form-item label="Client" prop="client_id">
        <el-select
          v-model="form.client_id"
          class="remote-select w-200px"
          filterable
          remote
          reserve-keyword
          placeholder="Client"
          :disabled="!!$route.query.client_id"
          :loading="selectLoading"
          :remote-method="getClient"
          no-data-text="No matching data"
        >
          <el-option
            v-for="item in clientList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="type" label="Type">
        <el-select
          v-model="form.type"
          placeholder="Type"
          class="w-200px"
          @change="autoGeneratedName"
        >
          <el-option
            v-for="item in CA_type_options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="item.disabled"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Name" prop="name">
        <el-input v-model="form.name" @input="checkName()" />
      </el-form-item>
      <!--      <el-form-item label="Required">-->
      <!--        <el-switch v-model="form.is_required" active-color="#13ce66" />-->
      <!--      </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="cancelCreate()">Cancel</el-button>
      <el-button type="primary" @click="submitForm('form')">Next
        <i class="el-icon-arrow-right el-icon--right" />
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { debounce } from '@/utils/tool'
import InquiryAPI from '@/api/inquiry'
import CA_mixin from './mixin/index'
import _ from 'lodash'

export default {
  name: 'ClientSelect',
  mixins: [CA_mixin],
  props: {
    centerDialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('Name is required!'))
      } else if (!this.name_is_validate) {
        callback(new Error('This name already exists!'))
      } else {
        callback()
      }
    }
    return {
      name_is_validate: true,
      had_CA_type: [],
      form: {
        client_id: null,
        name: '',
        type: '',
        is_required: true,
      },

      rules: {
        name: [
          { required: true, validator: validateName, trigger: ['blur'] },
        ],
        client_id: [
          { required: true, message: 'Client must choose', trigger: ['blur'] },
        ],
        type: [
          { required: true, message: 'Type must choose', trigger: ['blur', 'change'] },
        ],
      },
    }
  },
  watch: {
    'form.client_id': {
      handler(newVal) {
        if (newVal) {
          this.checkClientCAType()
        }
      }, immediate: false,
    },
    'centerDialogVisible': {
      handler(newVal) {
        if (newVal) {
          if (this.$route.query.client_id) {
            this.form.client_id = +this.$route.query.client_id
            this.getClient(this.$route.query.client)
          } else {
            this.getClient()
          }
        }
      }, immediate: false,
    },
  },
  methods: {
    checkName() {
      debounce(() => {
        this.name_is_validate = !this.CA_name_list.includes(this.form.name)
      }, 500)
    },

    checkClientCAType() {
      this.typeReset()
      const params = {
        client_id: this.form.client_id,
        types: this.CA_type_options.map(item => item.value)
          .join(),
      }

      return InquiryAPI.getInquiryList(params)
        .then(data => {
          if (data.list.length) {
            const list = data.list.map(item => item.type)
            this.had_CA_type = _.uniq(list)
            this.checkCATypeValidate()
          }
        })
    },

    checkCATypeValidate() {
      const option_list = _.cloneDeep(this.CA_type_options)

      this.CA_type_options = option_list.map(option => {
        if (this.had_CA_type.includes(option.value)) {
          option.disabled = true
        }
        return option
      })
    },

    submitForm(formName) {
      const data = Object.assign({}, this.form)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('save', data)
          this.$emit('update:centerDialogVisible', false)
        } else {
          this.$message({
            message: 'error submit!',
            type: 'error',
          })
          return false
        }
      })
    },

    cancelCreate() {
      this.$emit('update:centerDialogVisible', false)
      if (this.$route.query.client_id) {
        this.$router.push({
          name: 'ClientDetail',
          params: {
            client_id: this.$route.query.client_id },
          query: { tab: 'compliance' } })
      } else {
        this.$router.push({ name: 'ClientAgreementList' })
      }
    },

    typeReset() {
      this.CA_type_options = [
        { value: 'PRE_CALL_CA', label: 'Pre-Consultation', disabled: false },
        { value: 'POST_CALL_CA', label: 'Post-Consultation', disabled: false },
        // { value: 'LISTED_ONLY_CA', label: 'Listed Only', disabled: false },
      ]
      this.had_CA_type = []
      this.form.type = ''
    },

    autoGeneratedName() {
      const result = this.CA_type_options.filter(item => item.value === this.form.type)[0]['label'] + ' ' +
        this.clientList.filter(item => item.id === this.form.client_id)[0]['name']
      this.form.name = result
    },
  },
}
</script>

<style scoped>

</style>
