<template>
  <div>
    <el-form label-width="150px">
      <el-form-item label="Number">
        {{ questionIndex + 1 }}
      </el-form-item>
      <el-form-item label="Type">
        <el-radio-group v-model="question_data.show_type" fill="#409eff" @change="checkOption">
          <el-radio-button
            v-for="item in options.type"
            :key="item.value"
            :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="Question">
        <tinymce v-model="question_data.title" :height="100" :toolbar="toolbar" />
      </el-form-item>
      <el-form-item v-if="question_data.show_type === 'text'" label="Response">
        <el-input disabled type="textarea" />
      </el-form-item>
      <div v-if="!no_option">
        <el-form-item label="Response">
          <el-radio-group disabled fill="#409eff">
            <el-radio-button
              v-for="item in question_data.options"
              :key="item.value"
              :label="item.value">
              {{ item.text }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="question_data.show_type === 'yes_comment'" label="Requires Comment">
          <el-checkbox-group v-model="require_comment" fill="#409eff" @change="matchOptionComment()">
            <el-checkbox-button
              v-for="item in question_data.options"
              :key="item.value"
              :label="item.value">
              {{ item.text }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Pass">
          <el-checkbox-group v-model="auto_judgment.ALLOW" fill="#409eff" @change="matchOption('ALLOW')">
            <el-checkbox-button
              v-for="item in answer_options"
              :key="item.value"
              :label="item.value">
              {{ item.text }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Hold">
          <el-checkbox-group v-model="auto_judgment.HOLD" fill="#409eff" @change="matchOption('HOLD')">
            <el-checkbox-button
              v-for="item in answer_options"
              :key="item.value"
              :label="item.value">
              {{ item.text }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Fail">
          <el-checkbox-group v-model="auto_judgment.DENY" fill="#409eff" @change="matchOption('DENY')">
            <el-checkbox-button
              v-for="item in answer_options"
              :key="item.value"
              :label="item.value">
              {{ item.text }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="question_data.show_type === 'acknowledgement'" label="">
          <el-checkbox-group v-model="question_data.tags">
            <el-checkbox
              label="REQUIRE_ADVISOR_INPUT_FULLNAME">
              Require Expert to input full name
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'

export default {
  name: 'EditQuestion',
  components: { Tinymce },
  props: {
    question: {
      type: Object,
      default: () => {
        return {}
      },
    },
    languageType: String,
    questionIndex: Number,
    showQuestion: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      no_option: false,
      question_data: this.question,
      answer_options: undefined,
      answer_options_show: undefined,
      toolbar: ['undo redo | bold italic | link'],
      require_comment: [],
      auto_judgment: {
        ALLOW: [],
        HOLD: [],
        DENY: [],
      },
      options: {
        type: [
          { value: 'acknowledgement', label: 'Statement Acknowledgement' },
          { value: 'yes', label: 'Yes,No,N/A' },
          { value: 'yes_comment', label: 'Yes,No,N/A,with Comment' },
          { value: 'text', label: 'Text' },
        ],
        'en-US': {
          agree: [
            { value: 'AGREE', text: 'I Agree', type: 'ALLOW', description: false },
            { value: 'AGREE_NOT', text: 'I Disagree', type: 'DENY', description: false },
          ],
          yes_no: [
            { value: 'YES', text: 'Yes', type: 'ALLOW', description: false },
            { value: 'NO', text: 'No', type: 'DENY', description: false },
            { value: 'N/A', text: 'N/A', type: 'HOLD', description: false },
          ],
        },
        'zh-CN': {
          agree: [
            { value: 'AGREE', text: '同意', type: 'ALLOW', description: false },
            { value: 'AGREE_NOT', text: '不同意', type: 'DENY', description: false },
          ],
          yes_no: [
            { value: 'YES', text: '是', type: 'ALLOW', description: false },
            { value: 'NO', text: '否', type: 'DENY', description: false },
            { value: 'N/A', text: '不适用', type: 'HOLD', description: false },
          ],
        },
        'en-zh': {
          agree: [
            { value: 'AGREE', text: 'I Agree (同意)', type: 'ALLOW', description: false },
            { value: 'AGREE_NOT', text: 'I Disagree (不同意)', type: 'DENY', description: false },
          ],
          yes_no: [
            { value: 'YES', text: 'Yes (是)', type: 'ALLOW', description: false },
            { value: 'NO', text: 'No (否)', type: 'DENY', description: false },
            { value: 'N/A', text: 'N/A (不适用)', type: 'HOLD', description: false },
          ],
        },
      },
    }
  },

  watch: {
    'showQuestion': {
      handler(newVal) {
        if (newVal) {
          this.formatQuestion()
        }
      }, immediate: true,
    },
    'languageType': {
      handler(newVal) {
        if (newVal) {
          this.checkLanguageOption()
        }
      }, immediate: true,
    },
  },

  methods: {
    checkLanguageOption() {
      if (this.question_data.show_type === 'text') {
        this.answer_options = []
        this.question_data.type = 'TEXT'
        this.no_option = true
      } else {
        switch (this.question_data.show_type) {
          case 'acknowledgement':
            this.answer_options = this.options[this.languageType]['agree']
            break
          case 'yes_comment':
            this.answer_options = this.options[this.languageType]['yes_no']
            this.matchOptionComment()
            break
          case 'yes':
            this.require_comment = []
            this.answer_options = this.options[this.languageType]['yes_no']
            this.matchOptionComment()
            break
        }
        this.question_data.type = 'RADIO'
        this.no_option = false
        this.question_data.options = this.answer_options.filter(item => item.type)
        this.autoSetAnswerOptions()
      }
    },

    checkOption() {
      this.resetAdjustment()
      this.checkLanguageOption()

      // 仅在 'acknowledgement' 类型时 才启用 REQUIRE_ADVISOR_INPUT_FULLNAME
      if (this.question_data.show_type !== 'acknowledgement') {
        this.question_data.tags = this.question_data.tags.filter(item => item !== 'REQUIRE_ADVISOR_INPUT_FULLNAME')
      }
    },

    matchOption(type) {
      /* 选项互斥 */
      for (const key in this.auto_judgment) {
        if (key !== type) {
          this.auto_judgment[key] = this.auto_judgment[key].filter(item => !this.auto_judgment[type].includes(item))
        }
      }
      this.autoSetAnswerOptions()
    },

    matchOptionComment() {
      this.answer_options.forEach(item => {
        item.description = this.require_comment.includes(item.value)
      })
    },

    formatQuestion() {
      // 新旧数据兼容 旧数据只展示title
      if (!this.question_data.options) {
        this.no_option = true
        return
      }
      if (this.question_data.type === 'TEXT') {
        this.answer_options = []
        this.question_data.show_type = 'text'
        this.no_option = true
        return
      }

      this.answer_options = this.question_data.options
      this.question_data.options.forEach(item => {
        this.question_data.show_type = ['AGREE', 'AGREE_NOT'].includes(item.value) ? 'acknowledgement' : 'yes'
        if (item.type) {
          this.auto_judgment[item.type].push(item.value)
        }

        if (item.description) {
          this.require_comment.push(item.value)
        }
      })

      if (this.question_data.options.filter(item => item.description).length > 0) {
        this.question_data.show_type = 'yes_comment'
      }
    },

    resetAdjustment() {
      for (const key in this.auto_judgment) {
        const match_item = this.answer_options.filter(item => item.type === key)
        if (match_item.length > 0) {
          this.auto_judgment[key] = match_item.map(item => item.value)
        } else {
          this.auto_judgment[key] = []
        }
      }
    },

    /**
     * 选项值设置
     */
    autoSetAnswerOptions() {
      const judgment = this.auto_judgment
      const answer_options = this.answer_options

      answer_options.forEach(item => {
        item.type = getJudgementStatus(item.value, judgment)
      })

      this.question_data.options = answer_options.filter(item => item.type)

      function getJudgementStatus(value, judgment) {
        let output

        Object.keys(judgment).forEach(key => {
          if (judgment[key].includes(value)) {
            output = key
          }
        })

        return output
      }
    },

  },
}
</script>

<style scoped lang="scss">
.title-edit {
  padding-top: 4px;
  padding-left: 4px;
  padding-bottom: 4px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;

  &:focus-within {
    border-color: #409EFF;
  }

  ::v-deep .mce-edit-focus {
    outline: none;
  }
}

//::v-deep .mce-flow-layout {
//  display: none;
//}

::v-deep .mce-menubar {
  border: 0;
}

::v-deep .mce-statusbar .mce-container-body {
  display: none;
}
</style>
