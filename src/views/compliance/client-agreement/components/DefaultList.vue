<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getClientCAList" />
      <client-search
        v-model="searchQuery.client_id"
        class="w-200px filter-item"
        @change="handleSearch" />
      <el-select
        v-model="searchQuery.types"
        clearable
        class="filter-item"
        placeholder="Type"
        @change="handleSearch"
      >
        <el-option
          v-for="item in CA_type_options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="createCA()"
      >New
      </el-button>
    </div>
    <el-table
      v-loading="ListLoading"
      :data="clientCAList"
      border
      style="width: 100%"
    >
      <el-table-column
        label="Client"
      >
        <template v-if="scope.row.client" slot-scope="scope">
          <router-link-client :data="{name:scope.row.client.name,id:scope.row.client_id}" />
        </template>
      </el-table-column>
      <el-table-column label="Name">
        <template slot-scope="scope">
          <router-link
            class="link"
            :to="{ name: 'ClientAgreementDetail', query: { inquiry_id: scope.row.id, client_id: scope.row.client_id } }">
            {{ scope.row.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
        width="150">
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(CA_type_options) }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getClientCAList"
    />

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import InquiryAPI from '@/api/inquiry'
import RouterLinkClient from '@/components/RouterLink/Client'
import CA_mixin from './mixin/index'
import { debounce, RouteTools } from '../../../../utils/tool'
import RefreshButton from '@/components/RefreshButton/index'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'

export default {
  name: 'DefaultList',
  components: { RefreshButton, Pagination, RouterLinkClient, ClientSearch },
  mixins: [CA_mixin],
  data() {
    return {
      ListLoading: false,
      clientCAList: [],
      total: 0,
      searchQuery: {
        client_id: undefined,
        types: undefined,
        page: 1,
        size: 10,
        'client.page': 0,
        sort: 'client.name asc',
        extra: 'client',
      },
    }
  },

  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)

    if (this.$route.params.client_id) {
      this.searchQuery.client_id = this.$route.params.client_id
    }

    this.getClient()
    this.handleSearch()
  },

  methods: {
    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getClientCAList()
      }, 500)
    },

    getClientCAList() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      this.ListLoading = true
      const params = Object.assign({}, this.searchQuery)
      params.client_id = this.searchQuery.client_id || undefined
      params.types = this.searchQuery.types || this.CA_type_options.map(item => item.value)
        .join()
      return InquiryAPI.getInquiryList(params)
        .then(data => {
          this.clientCAList = data.list
          this.total = data.count
        })
        .finally(() => {
          this.ListLoading = false
        })
    },

    createCA() {
      this.$router.push({
        name: 'ClientAgreementCreate',
        query: { client_id: null },
      })
    },

    changeRequired(item) {
      return this.changeClientRequiredStatus(item.client_id, item.type,
        !item.client['require_' + item.type.toLowerCase()])
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Success',
          })
          this.getClientCAList()
        })
    },
  },

}
</script>

<style scoped>

</style>
