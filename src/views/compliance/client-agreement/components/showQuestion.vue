<template>
  <div>
    <div class="question-title">
      <div>
        <span class="order">{{ form.sort_order + 1 + '.  ' }}</span>
      </div>
      <div class="content ml-8 outermost-p-td-0" v-html="form.title" />
    </div>

    <div v-if="form.type === 'RADIO'">
      <div v-if="form.tags.includes('REQUIRE_ADVISOR_INPUT_FULLNAME')" class="option-item">
        <div>Please type your full name to acknowledge the above statement.</div>
        <el-input v-model="advisor_full_name" required disabled type="input" placeholder="full name" />
      </div>

      <el-radio-group v-model="answer" disabled>
        <div v-for="(option, indexKey) in form.options" :key="indexKey" class="option-item">
          <el-radio :label="indexKey">{{ option.text }}
            <span class="info">{{ getOptionType(option.type) }}</span>
            <span v-if="option.description && !showAnswer" class="allow-desc-icon el-icon-chat-line-square" />
          </el-radio>
          <div v-if="showAnswer && getAnswerDesc(form.answer,indexKey)" class="description">
            {{ getAnswerDesc(form.answer, indexKey) }}
          </div>
        </div>
      </el-radio-group>
      <div v-if="answer_comment" class="answer-comment">
        {{ answer_comment }}
      </div>
    </div>

    <div v-if="form.type === 'TEXT'" class="option-item">
      <el-input v-model="answer" disabled type="textarea" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowQuestion',
  props: {
    form: {
      type: Object,
      default: () => {
        return {}
      },
    },
    showAnswer: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      answer: '',
      advisor_full_name: '',
      answer_comment: '',
      option_type: [
        { name: 'Pass', value: 'ALLOW' },
        { name: 'Hold', value: 'HOLD' },
        { name: 'Fail', value: 'DENY' },
      ],
    }
  },

  watch: {
    'form': {
      handler(newVal) {
        if (this.showAnswer) {
          this.answer = newVal.answer.result
          this.advisor_full_name = newVal.answer.advisor_full_name
          this.answer_comment = newVal.answer.comment
        }
      }, immediate: true,
    },
  },

  methods: {
    getOptionType(type) {
      const result = this.option_type.filter(item => item.value === type)[0].name
      return '(' + result + ')'
    },

    getAnswerDesc(answer, option_index) {
      let result = ''
      if (!answer.desc) return result
      if (typeof answer.desc !== 'string') {
        Object.keys(answer.desc)
          .forEach(key => {
            +key === option_index ? result = answer.desc[key] : ''
          })
      } else {
        answer.result === option_index ? result = answer.desc : ''
      }
      return result
    },
  },
}
</script>

<style scoped lang="scss">
.question-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 15px;
  font-size: 11pt;
  font-family: Calibri;

  .order {
    width: 1rem;
  }

  .content {
    padding-left: 3px;
    width: 90%;
    overflow-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.option-item {
  display: block;
  max-width: 90%;
  margin: 0 20px 12px;
  word-break: break-all;
  font-family: Calibri;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep .el-radio__input.is-disabled + span.el-radio__label {
    font-size: 11pt;
    color: #61646b;
    white-space: normal;
    word-wrap: break-word;
  }

  .allow-desc-icon {
    font-size: 11pt;
    color: #65b8de;
    padding-left: 2px;
  }

  .description {
    color: #607cc3;
    margin-top: 4px;
    margin-left: 24px;
    font-size: 11pt;
  }
}

.answer-comment {
  font-size: 11pt;
  color: #61646b;
  max-width: 90%;
  margin: 12px 20px;
  word-break: break-all;
}

::v-deep .el-radio-group {
  width: 100%;
}

::v-deep a {
  text-decoration: revert;
}

</style>
