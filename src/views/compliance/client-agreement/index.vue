<template>
  <div class="app-container">
    <slot v-if="isCurrentRoute">
      <default-list />
    </slot>
    <router-view />
  </div>
</template>

<script>
import DefaultList from './components/DefaultList'

export default {
  name: 'ClientAgreement',
  components: { DefaultList },
  data() {
    return {}
  },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'ClientAgreementList'
    },
  },

}

</script>

<style scoped>

</style>
