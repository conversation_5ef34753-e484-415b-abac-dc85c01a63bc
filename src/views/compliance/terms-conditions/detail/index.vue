<template>
  <div ref="TCD">
    <edit-page :id="id" is-edit @cancel="cancel" />
  </div>
</template>

<script>
import EditPage from '../update'

export default {
  name: 'TermsConditionsDetail',
  components: { EditPage },
  data() {
    return {
      id: Number(this.$route.params.id),
    }
  },
  mounted() {
  },
  methods: {
    cancel() {
      this.$router.push({ path: '/compliance/tnc' })
    },
  },
}
</script>

<style scoped>
</style>
