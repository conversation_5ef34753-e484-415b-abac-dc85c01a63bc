<template>
  <div v-if="isLoaded" class="app-container">
    <el-form ref="form"
             v-loading="tc_loading"
             :model="form"
             :rules="rules"
             :label-width="labelWidth">

      <!-- TC Basic Info -->
      <el-form-item v-if="form.update_at" label="Last Update Time">
        {{ form.update_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
      </el-form-item>
      <el-form-item label="Name" prop="name">
        <el-input v-model="form.name" placeholder="TC Name" />
      </el-form-item>
      <el-form-item label="Current" prop="is_current">
        <el-switch
          v-model="form.is_current"
          :disabled="isCreate || (form.is_current && has_current)" />
      </el-form-item>
      <el-form-item label="Type" prop="Type">
        <el-radio v-model="form.type" :disabled="!isCreate" label="DEFAULT">Default</el-radio>
        <el-radio v-model="form.type" :disabled="!isCreate" label="SURVEY">Survey</el-radio>
      </el-form-item>
      <el-form-item label="Terms" prop="terms">
        <el-select
          v-if="isCreate"
          v-model="form.terms"
          multiple
          placeholder="Term">
          <el-option v-for="item in options.terms" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <span v-if="!isCreate">{{ form.terms | getLabels(options.terms) }}</span>
      </el-form-item>
      <el-form-item label="Language Version">
        <el-checkbox-group v-model="form.language_list"
                           @change="actionChangeLanguageVersion">
          <el-checkbox v-for="(item, index) in sub_version_list"
                       :key="index"
                       :label="item.iso_label"
                       :disabled="!item.enabled">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- Multiple languages -->
      <el-tabs v-model="activeTab"
               type="border-card">
        <el-tab-pane v-for="(item, index) in form.templates"
                     :key="item.locale"
                     :label="item.display__language_name"
                     :name="item.locale">

          <!-- Update Time -->
          <div v-if="item.update_at">
            <el-form-item label="Last Update Time" label-width="125px">
              {{ item.update_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
            </el-form-item>
            <el-divider class="divider-template" />
          </div>

          <!-- PDF File -->
          <el-form-item label="PDF File" label-width="125px">
            <div v-if="item.file_url"
                 style="line-height: initial; margin: 5px 0;">
              <el-link type="primary"
                       :href="item.file_url">{{ item.file_name }}
              </el-link>
              <el-link type="danger"
                       style="margin-left: 1em;"
                       @click="actionRemoveFile(item)">
                <el-icon name="delete" />
              </el-link>
            </div>

            <el-upload class="inline-block"
                       style="min-width: 200px;"
                       accept=".pdf"
                       :show-file-list="false"
                       :action="upload_url"
                       :before-upload="beforeUpload"
                       :http-request="actionUpload"
                       :headers="{uid: uid}">
              <el-button size="mini"
                         type="primary"
                         :loading="uploadSubmitting">Upload...
              </el-button>
              <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
            </el-upload>
          </el-form-item>

          <!-- TC Content -->
          <el-form-item label="Title and Content" label-width="125px" required />
          <div>
            <el-input v-model="item.title" class="email-subject-input" placeholder="Title" />
            <el-form-item :prop="'templates.' + index + '.title'"
                          :rules="{required: activeTab === item.locale, message:'title is required', trigger: 'change'}" />
          </div>
          <div>
            <el-input v-model="item.content" class="position-absolute" type="hidden" />
            <tinymce v-model="item.content"
                     placeholder="Content"
                     :is-email="true"
                     inline />
            <el-form-item :prop="'templates.' + index + '.content'" />
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- Save / Cancel -->
      <div style="margin-top: 18px;">
        <div v-if="!isEdit">
          <el-button type="primary"
                     icon="el-icon-check"
                     :loading="submitting"
                     @click="onSave('TermsConditionsList')">Save
          </el-button>
        </div>
        <div v-else>
          <el-button type="primary"
                     icon="el-icon-check"
                     :loading="submitting"
                     @click="onSave('TermsConditionsList')">Save
          </el-button>
          <el-button type="text" @click="$emit('cancel')">Cancel</el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import FormMixin from '@/components/FormPage/mixin'
import TC_API from '@/api/tc'
import { mapGetters } from 'vuex'
import { AppHttpService } from '@/utils/request'
import lodash from 'lodash'

export default {
  name: 'UpdateTermsConditions',
  components: { Tinymce },
  mixins: [FormMixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    lang: {
      type: String,
    },
    id: {
      type: Number,
    },
  },
  data() {
    return {
      isLoaded: false,
      activeTab: '',
      form: {},
      rules: {
        name: [{ required: true, trigger: ['blur', 'change', 'input'] }],
        title: [{ required: true, trigger: 'change' }],
        version: [{ required: true, trigger: 'change' }],
      },
      tc_loading: false,
      tc_id: 0,
      has_current: false,
      isCreate: true,
      upload_url: import.meta.env.VITE_APP_US_DB_API_V2 + '/tc/templates/files',
      uploadSubmitting: false,
      sub_version_list: [
        {
          label: 'English',
          iso_label: 'en-US',
          enabled: true,
        },
        {
          label: 'Chinese 中文',
          iso_label: 'zh-CN',
          enabled: true,
        },
        {
          label: 'Spanish Español',
          iso_label: 'es-ES',
          enabled: true,
        },
        {
          label: 'German Deutsch',
          iso_label: 'de-DE',
          enabled: true,
        },
        {
          label: 'French Français',
          iso_label: 'fr-FR',
          enabled: true,
        },
        {
          label: 'Italian Italiano',
          iso_label: 'it-IT',
          enabled: true,
        },
        {
          label: 'Portuguese Português',
          iso_label: 'pt-PT',
          enabled: true,
        },
        {
          label: 'Russian Pусский',
          iso_label: 'ru-RU',
          enabled: true,
        },
        {
          label: 'Japanese 日本',
          iso_label: 'ja-JP',
          enabled: true,
        },
        {
          label: 'Korean 한국어',
          iso_label: 'ko-KR',
          enabled: true,
        },
      ],
      original_data: {},
      options: {
        terms: [
          { label: 'Consultation', value: 'CONSULTATION' },
          { label: 'Survey', value: 'SURVEY' },
        ],
      },
    }
  },
  computed: {
    ...mapGetters({
      uid: 'uid',
    }),
    labelWidth() {
      return this.isCreate ? '120px' : '140px'
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.form = this.initForm()
      this.activeTab = this.form.language_list[0]

      if (this.isEdit && this.id) {
        this.tc_id = this.id
        this.isCreate = false
        this.loadTC(this.id)
      } else {
        this.form.templates = this.sub_version_list.map(item => {
          return {
            locale: item.iso_label,
            display__language_name: item.label,
            title: '',
            content: '',
            update_at: '',
            file_name: undefined,
            file_url: undefined,
          }
        })
        this.isLoaded = true
      }
    },

    initForm() {
      return {
        name: '',
        is_current: false,
        language_list: this.sub_version_list.map(item => item.iso_label),
        update_at: '',
        templates: [],
        type: 'DEFAULT',
        terms: [],
      }
    },

    /**
     * Save TC
     * @param form
     * @returns {Promise<void>}
     */
    handleSave(form) {
      return this.tcSaveFlow()
    },

    /**
     * TC 保存流程
     * 先保存主TC, 再保存其子语言版本
     * @returns {Promise<void>}
     */
    tcSaveFlow() {
      // @Northern: 暂不需要表单验证
      // if (this.validate()) {
      //   return Promise.reject('Validate failed.')
      // }

      const params = this.getSaveParams(this.form)

      return this
        .saveTC(params)
        .then(() => {
          this.$notify({
            title: 'Success',
            message: '',
            type: 'success',
          })
        }, error => {
          this.$notify({
            title: 'Error',
            message: error,
            type: 'error',
            duration: 0,
          })
          return Promise.reject()
        })
    },

    getSaveParams(form) {
      return this.isCreate
        ? this.getCreateParams(form)
        : this.getUpdateParams(form)
    },

    getCreateParams(form) {
      return {
        name: form.name,
        is_current: form.is_current,
        type: form.type,
        terms: form.terms.length < 0 ? undefined : form.terms,
        templates: form.templates.filter(item => item.content !== ''),
      }
    },

    getUpdateParams(form) {
      const data = {
        id: form.id,
        name: form.name,
        is_current: form.is_current,
        templates: [],
      }

      /* 保存仅被更改的 subTC */
      form.templates.forEach(item => {
        if (this.isSubTcEdited(item)) {
          data.templates.push({
            tc_id: form.id,
            locale: item.locale,
            title: item.title,
            content: item.content,
            file_name: item.file_name,
            file_url: item.file_url,
          })
        }
      })

      return data
    },

    isSubTcEdited(data) {
      let flag = false
      const subTC = this.original_data.templates?.find(item => item.id === data.id)
      if (subTC) {
        const dirty_check_keys = ['title', 'content', 'file_name', 'file_url']
        dirty_check_keys.forEach(key => {
          if (data[key] !== subTC[key]) {
            flag = true
          }
        })
      } else {
        /* 新增的语言版本视为被修改 */
        flag = true
      }

      return flag
    },

    saveTC(params) {
      return this.isCreate
        ? TC_API.createTC(params)
        : TC_API.updateTC(params)
    },

    validate() {
      let hasError = false

      /* 必须含有子语言版本 */
      if (this.form.templates.length === 0) {
        hasError = true
        this.$notify({
          title: 'Success',
          message: '',
          type: 'success',
        })
      }

      return hasError
    },

    loadTC(id = this.tc_id) {
      this.startLoading()
      return TC_API.getTCDetail(id)
        .then(data => {
          let activeTab = 'en-US'
          const templates = data['templates']

          if (templates && templates.length) {
            /* subTC 整理后汇入 form 表单 */
            const recommend_template_order = this.sub_version_list.map(item => item.iso_label)
            this.form.templates = templates
              .sort((a, b) => recommend_template_order.indexOf(a.locale) - recommend_template_order.indexOf(b.locale))
              .map(item => {
                return Object.assign(item, {
                  display__language_name: this.$options.filters
                    .getName(item.locale, this.sub_version_list, 'iso_label', 'label'),
                })
              })

            /* subTC 不可移除 (禁用其对应勾选框) */
            const templates_locales = templates.map(item => item.locale)
            this.sub_version_list.forEach(option => {
              if (templates_locales.includes(option.iso_label)) {
                option.enabled = false
              }
            })

            /* "Language Version" 勾选项选中 subTC */
            this.form.language_list = templates.map(item => item.locale)

            /* 激活最新 subTC 标签页 */
            const id_list = templates.map(item => item.id)
              .sort((a, b) => a - b)
              .reverse()
            activeTab = templates.find(item => item.id === id_list[0]).locale
          } else {
            this.form.language_list = []
          }

          /* 语言栏位定位至最近编辑的版本/en-US */
          this.activeTab = activeTab

          /* masterTC 数据加载 */
          for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key) && key !== 'templates') {
              this.form[key] = data[key]
            }
          }
          this.has_current = data['is_current']

          /* 备份原始数据, 用于保存时对比 */
          this.backupOriginData(data)
        })
        .finally(() => {
          this.endLoading()
          this.isLoaded = true
        })
    },

    backupOriginData(data) {
      this.original_data = lodash.cloneDeep(data)
    },

    startLoading() {
      this.tc_loading = true
    },

    endLoading() {
      setTimeout(() => {
        this.tc_loading = false
      }, 500)
    },

    actionUpload(ref) {
      const template = this.form.templates.find(item => item.locale === this.activeTab)

      const bodyFormData = new FormData()
      bodyFormData.append('file', ref.file)

      this.uploadSubmitting = true

      const config = {
        headers: ref.headers,
      }

      return AppHttpService
        .post('/tc/templates/files', bodyFormData, config)
        .then(data => {
          template.file_name = ref.file.name
          template.file_url = data['url']
        }, error => {
          return Promise.reject(error)
        })
        .finally(() => {
          this.uploadSubmitting = false
        })
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },

    actionRemoveFile(template) {
      template.file_name = undefined
      template.file_url = undefined
    },

    /**
     * 添加一个语言版本
     * @param target_locale
     */
    addSubTC(target_locale) {
      /* Filter in use locales */
      const all_locale = this.sub_version_list
        .map(item => item.iso_label)

      const will_use_locale = all_locale
        .filter(locale => {
          const isUsing = !!this.form.templates.find(item => item.locale === locale)
          const tobeCreate = locale === target_locale
          return isUsing || tobeCreate
        })

      /* Add to page templates */
      const target_index = will_use_locale.indexOf(target_locale)
      const data = {
        locale: target_locale,
        display__language_name: this.$options.filters
          .getName(target_locale, this.sub_version_list, 'iso_label', 'label'),
        title: '',
        content: '',
        update_at: '',
        file_name: undefined,
        file_url: undefined,
      }

      this.form.templates.splice(target_index, 0, data)
    },

    /**
     * 语言勾选项变更时，调节多语言编辑标签页
     * @param aimLocales
     */
    actionChangeLanguageVersion(aimLocales) {
      const templates = this.form.templates
      const templates_locales = templates.map(item => item.locale)

      this.form.templates = this.form.templates.filter(item => {
        return aimLocales.includes(item.locale)
      })

      aimLocales.forEach(item => {
        const isExist = templates_locales.includes(item)
        if (!isExist) {
          this.addSubTC(item)
        }
      })

      this.activeTab = this.form.templates.length > 0 ? this.form.templates[0].locale : undefined
    },

  },
}
</script>

<style lang="scss" scoped>
.divider-template {
  margin: 15px 0;
}

.tinymce-container ::v-deep {
  .tinymce-heading {

  }

  .tinymce-body {
    box-shadow: inset 0 5px 50px -5px hsl(0deg 0% 65% / 40%);
    border-radius: .5rem;
    padding: 2rem .5rem 2rem 1.5rem;
  }
}

/* 邮件编辑 主题 */
.email-subject-input {
  margin-bottom: 5px;
  margin-top: 5px;

  ::v-deep .el-input__inner {
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    color: #000000;
    font-size: 1.34rem;
    text-align: center;
  }
}
</style>
