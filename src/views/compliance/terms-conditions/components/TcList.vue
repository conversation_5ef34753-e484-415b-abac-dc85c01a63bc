<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button type="success" icon="el-icon-plus" class="filter-item" @click="actionGoToCreate">New</el-button>
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData">
      <el-table-column
        prop="id"
        label="ID"
        width="55" />
      <el-table-column
        prop="name"
        label="Name"
      >
        <template slot-scope="scope">
          <router-link
            class="link"
            :to="{ name: 'TermsConditionsDetail', params: { id: scope.row.id } }">
            {{ scope.row.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="type"
        label="Type"
        width="150">
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(type_options) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="terms_str"
        label="Terms"
        width="150">
        <template slot-scope="scope">
          {{ scope.row.terms | getLabels(term_options) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="is_current"
        label="Current"
        width="100">
        <template slot-scope="scope">
          {{ scope.row.is_current ? 'Yes' : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="Language"
        width="320">
        <template slot-scope="scope">
          {{ scope.row.language_list }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import API from '@/api/tc'
import Pagination from '@/components/Pagination'

export default {
  name: 'TcList',
  components: { Pagination },
  data() {
    return {
      loading: false,
      langtype: navigator.language,
      searchQuery: {
        page: 1,
        size: 20,
      },
      total: 0,
      tableData: [],
      type_options: [
        { value: 'DEFAULT', label: 'Default' },
        { value: 'SURVEY', label: 'Survey' },
      ],
      term_options: [
        { label: 'Consultation', value: 'CONSULTATION' },
        { label: 'Survey', value: 'SURVEY' },
      ],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        page: this.searchQuery.page,
        size: this.searchQuery.size,
        pagination: undefined,
        extra: undefined,
      }
      return API.getTCList(params)
        .then(data => {
          this.total = data.count
          this.tableData = data.list.map(item => {
            item.language_list = Object.keys(item.locale_template_id_map).join()
            return item
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    actionGoToCreate() {
      this.$router.push({ name: 'CreateTermsConditions' })
    }
    ,
  },
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";

.el-tag + .el-tag {
  margin-left: $gap-sm/2;
}
</style>
