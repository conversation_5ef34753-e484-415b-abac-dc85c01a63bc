<template>
  <div>
    <el-row class="flex button-box">
      <el-button type="primary" size="mini" @click="actionRequest('COMPLETE')">Complete</el-button>
      <el-button type="danger" size="mini" @click="actionRequest('REJECT')">Reject</el-button>
    </el-row>
    <el-dialog
      :title="title"
      :visible.sync="requestDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="50%"
    >
      <el-form ref="form" :model="form">
        <el-form-item v-if="status === 'COMPLETE'">
          <el-upload
            accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
            :action="upload_url"
            :on-success="handleSuccess"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :data="{'uid':uid}"
            :limit="1"
            :file-list="fileList"
          >
            <div>
              <el-button size="small" type="primary">Upload Contract</el-button>
              <div class="inline-block" @click.stop>
                <span class="el-icon-question tooltip-icon">
                  <span class="upload-tip">The maximum upload size is {{ file_maxSize }} MB. </span>
                </span>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item :label="label">
          <el-input v-model="params.generate_note" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="status === 'COMPLETE'"
                   type="primary"
                   :loading="isSubmitting"
                   @click="approveRequest()">Complete</el-button>
        <el-button v-else
                   type="danger"
                   :loading="isSubmitting"
                   @click="rejectRequest()">Reject</el-button>
        <el-button type="text" @click="requestDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/contract'
import { mapGetters } from 'vuex'

export default {
  name: 'CheckRequest',
  props: {
    id: [Number, String],
  },
  data() {
    return {
      isSubmitting: false,
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      loading: false,
      requestDialogVisible: false,
      status: '',
      title: '',
      label: '',
      file_maxSize: 20,
      fileList: [],
      params: {
        generate_note: '',
        materials: [],
      },
      form: {
        file_name: '',
        file_url: '',
        type: 'OTHER',
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },

  methods: {
    actionRequest(status) {
      this.status = status
      if (status === 'COMPLETE') {
        this.title = 'Request Complete'
        this.label = 'Feedback'
      } else {
        this.label = ''
        this.title = 'Reject Reason'
      }
      this.requestDialogVisible = true
    },

    approveRequest() {
      if (!this.form.file_url) {
        return this.$message({
          message: 'Must upload  contract',
          type: 'error',
        })
      }
      this.isSubmitting = true
      this.params.materials.push(this.form)
      return API.generationApprove(this.id, this.params)
        .then(data => {
          this.requestDialogVisible = false
          this.$emit('update')
        }).finally(() => {
          this.isSubmitting = false
        })
    },

    rejectRequest() {
      this.isSubmitting = true
      return API.generationReject(this.id, this.params)
        .then(data => {
          this.requestDialogVisible = false
          this.$emit('update')
        }).finally(() => {
          this.isSubmitting = false
        })
    },

    handleSuccess(response) {
      if (response.msg === 'ok') {
        this.form.file_name = response.data.file_name
        this.form.file_url = response.data.url
      } else {
        this.$message(response.message)
      }
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`)
      }
    },

    handleRemove(file, fileList) {
      this.form.file_name = ''
      this.form.file_url = ''
    },
  },

}
</script>

<style lang="scss" scoped>
.button-box {
  ::v-deep .el-button--mini {
    padding: 4px 4px;
  }
}

.tooltip-icon {
  margin-left: 10px;
  font-size: 1.2rem;
  color: #606266;

  .upload-tip {
    display: none;
  }

  &:hover {
    .upload-tip {
      display: inline-block;
      padding-left: 10px;
      font-size: 0.8rem !important;
    }
  }

}
</style>
