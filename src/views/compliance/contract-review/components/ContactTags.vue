<template>
  <div v-if="data">
    <el-popover
      v-for="(value, key, index) in contact_options"
      :key="index"
      placement="top"
      popper-class="contact-tags-popover"
      :append-to-body="true"
      trigger="hover">
      <div v-show="value" slot="reference" class="contact-tags-item">
        <svg-icon :icon-class="key" />
      </div>
      <div class="contact-tags-detail">
        <a v-if="key==='email'" class="link" :href="'mailto:'+value">{{value}}</a>
        <span v-else>{{value}}</span>
      </div>
    </el-popover>

  </div>
</template>

<script>
export default {
  name: 'ContactTags',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      contact_options: {
        email: '',
        phone: '',
      },
    }
  },

  mounted() {
    this.contact_options.email = this.data.email
    this.contact_options.phone = this.data.phone
  },
}
</script>

<style scoped lang="scss">

  .contact-tags-item {
    cursor: pointer;
    display: inline-block;
    margin-right: 15px;
  }

  .contact-tags-popover {

    .contact-tags-detail {
      font-size: 12px;
    }
  }

</style>
