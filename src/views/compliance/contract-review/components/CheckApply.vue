<template>
  <div>
    <el-row class="flex button-box">
      <el-button type="primary" size="mini" @click="actionApply('COMPLETE')">Approve</el-button>
      <el-button type="danger" size="mini" @click="actionApply('REJECT')">Reject</el-button>
    </el-row>

    <el-dialog
      :title="title"
      :visible.sync="applyDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="50%"
    >
      <contract-detail v-if="id" :apply-contract-id="id" />
      <el-input v-model="params.approve_note" type="textarea" :placeholder="placeholder" :rows="4" />
      <span slot="footer" class="dialog-footer">
        <el-button v-if="status === 'COMPLETE'" type="primary" @click="approveApply()">Approve</el-button>
        <el-button v-else type="danger" @click="rejectApply()">Reject</el-button>
        <el-button type="text" @click="applyDialogVisible=false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/contract'
import ContractDetail from '@/views/finance/contract/detail/index'

export default {
  name: 'CheckApply',
  components: { ContractDetail },
  props: {
    id: [Number, String],
  },
  data() {
    return {
      loading: false,
      applyDialogVisible: false,
      status: '',
      title: '',
      placeholder: '',
      params: {
        approve_note: '',
      },
    }
  },

  methods: {
    actionApply(status) {
      this.status = status
      if (status === 'COMPLETE') {
        this.title = 'Approve Feedback'
        this.placeholder = 'You can give the applicant some advice'
      } else {
        this.title = 'Reject Reason'
        this.placeholder = 'Please enter a reason for rejection to let the applicant know'
      }
      this.applyDialogVisible = true
    },

    approveApply() {
      return API.approvalApprove(this.id, this.params)
        .then(data => {
          this.applyDialogVisible = false
          this.$emit('update')
        })
    },

    rejectApply() {
      return API.approvalReject(this.id, this.params)
        .then(data => {
          this.applyDialogVisible = false
          this.$emit('update')
        })
    },

  },
}
</script>

<style scoped lang="scss">
.button-box {
  ::v-deep .el-button--mini {
    padding: 4px 6px;
  }
}
</style>
