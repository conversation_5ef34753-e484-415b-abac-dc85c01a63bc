<template>
  <div>
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div>
        <refresh-button class="filter-item" @action="getList(searchQuery)" />
        <client-search
          v-model="searchQuery.client_id"
          class="w-200px filter-item"
          @change="handleSearch" />
      </div>

      <div class="tabs-stackOverflow filter-item">
        <template v-for="(item, index) in status_options">
          <el-tag
            :key="index"
            :class="{active: searchQuery.approval_status === item.value }"
            @click.native="filterStatusSearch(item)">
            {{ item.label }}
            <span class="status-count">{{ getAggregationsCount(item.value) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      v-if="statusInRequest"
      v-loading="loading"
      :data="current_data"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="55" />
      <el-table-column label="Client">
        <template slot-scope="scope">
          <router-link-client :data="{name:scope.row.client_name,id:scope.row.client_id}" />
        </template>
      </el-table-column>
      <el-table-column label="AM Team">
        <template slot-scope="scope">
          <span v-if="scope.row.client.am_team">{{ scope.row.client.am_team.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name: 'ContractDetail', params: { id: scope.row.id, from_comp: true } }">
            {{ scope.row.name }}
          </router-link>
          <el-tag v-if="scope.row.applicable_project_types === 'SURVEY'" size="mini" type="success">Survey</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Unit Price US / China">
        <template slot-scope="scope">
          <span v-if="scope.row.payway!=='PROJECT_BASE'">
            {{ scope.row.unit_price_us }} / {{ scope.row.unit_price_china }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Approval Status">
        <template slot-scope="scope">
          {{ scope.row.approval_status | getLabel(contract_options.approval_status) }}
        </template>
      </el-table-column>
      <el-table-column label="Requester">
        <template slot-scope="scope">
          <div v-if="scope.row.requester" class="flex justify-content-center">
            <span>{{ scope.row.requester && scope.row.requester.name }}</span>
            <contact-tag :data="scope.row.requester" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Note">
        <template slot-scope="scope">
          <a class="text-truncate" :title="scope.row.generate_note">{{ scope.row.generate_note }}</a>
        </template>
      </el-table-column>
      <el-table-column label="Time">
        <template slot-scope="scope">
          {{ scope.row.request_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <div v-if="searchQuery.approval_status !== 'DRAFT_REQUESTED'">
        <el-table-column label="Operator" prop="generator.name">
          <template v-if="scope.row.generator" slot-scope="scope">
            {{ scope.row.generator && scope.row.generator.name }}
          </template>
        </el-table-column>
        <el-table-column label="Action Time">
          <template slot-scope="scope">
            {{ scope.row.generate_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
          </template>
        </el-table-column>
      </div>
      <el-table-column v-if="searchQuery.approval_status === 'DRAFT_REQUESTED'" label="Actions" width="140">
        <template slot-scope="scope">
          <check-request
            :id="scope.row.id"
            @update="handleSearch" />
        </template>
      </el-table-column>
    </el-table>

    <el-table
      v-if="!statusInRequest"
      v-loading="loading"
      :data="current_data"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="55" />
      <el-table-column label="Client">
        <template slot-scope="scope">
          <router-link-client :data="{name:scope.row.client_name,id:scope.row.client_id}" />
        </template>
      </el-table-column>
      <el-table-column label="AM Team">
        <template slot-scope="scope">
          <span v-if="scope.row.client.am_team">{{ scope.row.client.am_team.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name: 'ContractDetail', params: { id: scope.row.id, from_comp: true } }">
            {{ scope.row.name }}
          </router-link>
          <el-tag v-if="scope.row.applicable_project_types === 'SURVEY'" size="mini" type="success">Survey</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Unit Price US / China">
        <template slot-scope="scope">
          <span v-if="scope.row.payway!=='PROJECT_BASE'">
            {{ scope.row.unit_price_us }} / {{ scope.row.unit_price_china }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Approval Status">
        <template slot-scope="scope">
          {{ scope.row.approval_status | getLabel(contract_options.approval_status) }}
        </template>
      </el-table-column>
      <el-table-column label="Applier">
        <template slot-scope="scope">
          <div v-if="scope.row.applier" class="flex justify-content-center">
            <span>{{ scope.row.applier.name }}</span>
            <contact-tag :data="scope.row.applier" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Note">
        <template slot-scope="scope">
          <a class="text-truncate" :title="scope.row.apply_note">{{ scope.row.apply_note }}</a>
        </template>
      </el-table-column>
      <el-table-column label="Time">
        <template slot-scope="scope">
          {{ scope.row.apply_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column label="File">
        <template slot-scope="scope">
          <template v-for="(item, index) in scope.row.materials">
            <a
              v-if="item.type === 'SIGNED_CONTRACT_FILE'"
              :key="index"
              :href="item.file_url"
              target="_blank"
              class="text-truncate link"
              :title="item.file_name">
              {{ item.file_name }}
              <br>
            </a>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        v-if="searchQuery.approval_status !== 'PENDING_APPROVAL'"
        label="Approver">
        <template slot-scope="scope">
          {{ scope.row.approver && scope.row.approver.name }}
        </template>
      </el-table-column>
      <el-table-column v-if="searchQuery.approval_status !== 'PENDING_APPROVAL'" label="Action Time">
        <template slot-scope="scope">
          {{ scope.row.approve_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column v-if="searchQuery.approval_status === 'PENDING_APPROVAL'" label="Actions" width="140">
        <template slot-scope="scope">
          <check-apply :id="scope.row.id" @update="handleSearch" />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="pageList"
    />

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import API from '@/api/contract'
import CheckRequest from './CheckRequest'
import ContactTag from './ContactTags'
import CheckApply from './CheckApply'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RefreshButton from '@/components/RefreshButton/index'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'

export default {
  name: 'ContractList',
  components: { RefreshButton, Pagination, CheckRequest, CheckApply, ContactTag, RouterLinkClient, ClientSearch },
  data() {
    return {
      loading: false,
      initAggregation: true,
      aggregations_list: [],
      total: 0,
      select_status: 'DRAFT_REQUESTED',
      searchQuery: {
        id: null,
        client_id: null,
        // request_status: undefined,
        approval_status: 'DRAFT_REQUESTED',
        page: 1,
        size: 10,
        extra: 'client_name,requester,applier,approver,generator,materials,client.am_team',
        aggregation: 'approval_status,effective_status',
        sort: 'request_time desc',
      },
      current_data: [],

      status_options: [
        { value: 'DRAFT_REQUESTED', label: 'Draft Requested' },
        { value: 'DRAFT_CREATED', label: 'Draft Created' },
        { value: 'REQUEST_REJECTED', label: 'Requested Rejected' },
        { value: 'PENDING_APPROVAL', label: 'Pending Approval' },
        { value: 'REJECTED', label: 'Rejected' },
        { value: 'APPROVED', label: 'Approved' },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'contract_options',
    ]),
    statusInRequest() {
      return ['DRAFT_REQUESTED', 'DRAFT_CREATED', 'REQUEST_REJECTED'].includes(this.searchQuery.approval_status)
      // return ['IN_REQUEST', 'REQUEST_COMPLETED', 'REQUEST_REJECTED'].includes(this.select_status)
    },
  },

  mounted() {
    this.searchQuery.sort = this.statusInRequest ? 'request_time desc' : 'apply_time desc'
    this.searchQuery.id = this.$route.query.id || undefined
    const params = Object.assign({}, this.searchQuery)
    this.getList(params)
  },

  methods: {
    getList(params) {
      this.current_data = []
      this.loading = true

      return API.getContractList(params)
        .then(data => {
          this.total = data.count
          this.current_data = data.list
          this.aggregations_list = data.aggregations.approval_status
        })
        .finally(() => {
          this.loading = false
        })
    },

    filterStatusSearch(item) {
      this.searchQuery.page = 1
      this.searchQuery.approval_status = item.value
      this.getList(this.searchQuery)
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.searchQuery.id = null
      this.searchQuery.client_id = this.searchQuery.client_id || null
      const params = Object.assign({}, this.searchQuery)
      this.getList(params)
    },

    pageList() {
      this.getList(this.searchQuery)
    },

    getAggregationsCount(value) {
      let result = 0
      if (value === null) return ''
      this.aggregations_list.forEach(item => {
        if (item.value === value) {
          result = item.count
        }
      })
      return result
    },
  },

}
</script>

<style scoped>
</style>
