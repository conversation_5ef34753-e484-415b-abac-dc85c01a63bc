<template>
  <div class="app-container">
    <slot v-if="isCurrentRoute">
      <contract-list />
    </slot>
    <router-view />
  </div>
</template>

<script>
import ContractList from './components/ContractList'

export default {
  name: 'ContractIndex',
  components: { ContractList },
  data() {
    return {}
  },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'ContractApproval'
    },
  },
}
</script>

<style scoped>

</style>
