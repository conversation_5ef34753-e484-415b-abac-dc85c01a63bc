<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getContactList" />
      <el-input
        v-model="searchQuery.keyword"
        class="w-200px filter-item"
        placeholder="Keyword"
        @input="handleSearch"
      />
      <el-input
        v-model="searchQuery.name_like"
        class="w-200px filter-item"
        placeholder="Client User Name"
        @input="handleSearch"
      />
      <el-select
        v-model="searchQuery.client_id"
        class="remote-select filter-item w-200px"
        filterable
        remote
        clearable
        reserve-keyword
        placeholder="Client"
        :loading="selectLoading"
        :disabled="select_disabled"
        :remote-method="getClient"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option v-for="item in clientList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select
        v-model="searchQuery.types_contains_any"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Type"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.contact_person_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.status"
        clearable
        class="w-200px filter-item"
        placeholder="Status"
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.contact_status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="searchQuery['office.name_contains']"
        class="w-200px filter-item"
        placeholder="Office Name"
        @input="handleSearch" />
      <el-button
        v-if="isAddContact"
        :disabled="!checkedContact.length"
        type="primary"
        class="filter-item"
        @click="addContactToProject">
        Add to Project
      </el-button>
      <outreach
        v-if="outreach_select.length"
        class="filter-item"
        :select-user="outreach_select"
        @update="handleSearch" />
    </div>

    <el-table
      ref="multipleTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      :row-key="getRowKeys"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['multipleTable'])"
      @sort-change="sortData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" />
      <el-table-column label="Name" prop="name" sortable="custom">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :to="{ name:'ClientContactDetail', params: { contact_id: scope.row.id }}">
            {{ scope.row.name || '' }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Client" prop="client_id" sortable="custom">
        <template slot-scope="scope">
          <router-link-client v-if="scope.row.client" :data="{id:scope.row.client_id,name:scope.row.client.name}" />
        </template>
      </el-table-column>

      <el-table-column label="Client User Type" prop="type">
        <template slot-scope="scope">
          <span v-for="(item,index) in scope.row.types" :key="index">
            <span v-if="index>0">, </span>{{ item | getLabel(client_options.contact_person_type) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Contact">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope['row'].contacts" :key="index" class="text-truncate">
            <span>{{ item.short_type }}:</span>
            <span class="contact-content">{{ item.value }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Last Contact Time">
        <template slot-scope="scope">
          {{ scope.row.last_communication_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column label="Office" prop="office.name" sortable="custom">
        <template v-if="scope.row.office" slot-scope="scope">
          {{ scope.row.office.name }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        width="100"
        prop="status"
        sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.status | getLabel(client_options.contact_status) }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="60" align="center">
        <template slot-scope="scope">
          <add-task-dashboard
            :data-user="scope.row"
            :data-client="scope.row.client" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getContactList"
    />

    <!--    <router-view />-->
  </div>
</template>

<script>
import { debounce } from '@/utils/tool'
import ClientAPI from '@/api/client'
import ProjectAPI from '@/api/project'
import { RouteTools } from '@/utils/tool'
import { mapGetters } from 'vuex'
import RouterLinkClient from '@/components/RouterLink/Client'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import AddTaskDashboard from '@/views/client/detail/components/AddTaskDashboard'
import Outreach from './outreach/index'

export default {
  name: 'ClientContact',
  components: {
    RefreshButton,
    Pagination,
    RouterLinkClient,
    AddTaskDashboard,
    Outreach,
  },
  data() {
    return {
      project_id: undefined,
      checkedContact: [],
      outreach_select: [],
      select_ids: [],
      loading: false,
      selectLoading: false,
      select_disabled: false,
      clientList: [],
      tableData: [],
      total: 0,
      searchQuery: {
        keyword: undefined,
        name_like: undefined,
        client_id: undefined,
        'office.name_contains': undefined,
        status: undefined,
        types_contains_any: [],
        page: 1,
        size: 20,
        sort: 'id desc',
        extra: 'contact_infos,client,location,office,last_communication_time',
      },
      contact_type: [
        {
          value: 'EMAIL',
          label: 'Email',
        },
        {
          value: 'PHONE',
          label: 'Phone',
        },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
    ]),
    isAddContact() {
      return this.$route.name === 'AddContact'
    },
  },

  mounted() {
    if (this.$route.query.client_id) {
      this.select_disabled = true
      this.searchQuery.client_id = +this.$route.query.client_id
      this.getClient(null, this.$route.query.client_id)
    } else {
      this.getClient()
    }
    this.getContactList()
  },

  methods: {
    getRowKeys(row) {
      return row.id
    },
    getContactList(val) {
      this.loading = true
      const params = Object.assign({}, this.searchQuery, val)
      params.name_like = params.name_like || undefined
      params.keyword = params.keyword || undefined
      params['office.name_contains'] = params['office.name_contains'] || undefined
      params.status = params.status || undefined
      params.types_contains_any = this.formatParams(params.types_contains_any)

      return ClientAPI.getFullContactList(params)
        .then(data => {
          data.list.forEach(item => {
            item.contacts = item.contact_infos.map(info => {
              return {
                short_type: info.type === 'PHONE' ? 'P' : 'E',
                value: info.value,
                is_main: info.is_main,
              }
            })
          })

          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.initSelect()
        this.getContactList()
      }, 500)
    },

    actionGoToClientDetail(client_id) {
      this.$router.push(
        {
          name: 'ClientDetail',
          params: { client_id: client_id },
        })
    },

    handleSelectionChange(val) {
      const mew_select = val.filter(item => !this.select_ids.includes(item.id))
      this.project_id = this.$route.params.project_id
      const add_project_val = mew_select.map(item => {
        return {
          task: {
            client_contact_id: item.id,
            type: 'CLIENT_TASK',
          },
        }
      })
      this.checkedContact = this.checkedContact.concat(add_project_val)

      const outreach_val = mew_select.map(item => {
        return {
          client_contact_id: item.id,
          last_communication_time: item.last_communication_time,
          client_id: item.client_id,
          contact_infos: item.contact_infos,
          name: item.name,
        }
      })
      this.outreach_select = this.outreach_select.concat(outreach_val)
      this.select_ids = this.select_ids.concat(mew_select.map(item => item.id))
      this.removeUnchecked(val.map(item => item.id))
    },

    removeUnchecked(list) {
      const uncheckList = this.tableData.filter(item => !list.includes(item.id)).map(item => item.id)
      uncheckList.forEach(item => {
        const exist_index = this.select_ids.indexOf(item)
        const outreach_index = this.outreach_select.findIndex(select => select.client_contact_id === item)
        const add_contact_index = this.checkedContact.findIndex(select => select.task.client_contact_id === item)
        if (exist_index > -1) this.select_ids.splice(exist_index, 1)
        if (outreach_index > -1) this.outreach_select.splice(outreach_index, 1)
        if (add_contact_index > -1) this.checkedContact.splice(add_contact_index, 1)
      })
    },

    getClient(val, client_id) {
      const params = {
        name_like: val || undefined,
        id: client_id || undefined,
        extra: 'client_contacts',
        page: 1,
        size: 15,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList = data.list
        })
    },

    addContactToProject() {
      const params = this.checkedContact
      return ProjectAPI.addAdvisor(this.project_id, params)
        .then(() => {
          return RouteTools.removeCurrentRoute(this.$route)
        })
        .then(() => {
          this.$router.push({
            name: 'ContactTasks',
            params: {
              project_id: this.project_id,
            },
          })
        })
    },
    initSelect() {
      this.checkedContact = []
      this.outreach_select = []
      this.select_ids = []
      this.$refs.multipleTable.clearSelection()
    },

    sortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.prop + order
        this.initSelect()
        this.getContactList()
      }
    },

    formatParams(list) {
      return (Array.isArray(list) && list || []).map(item => {
        return item
      })
        .join() || undefined
    },
  },

}
</script>
