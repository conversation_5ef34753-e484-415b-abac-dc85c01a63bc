<template>
  <div class="inline-block">
    <el-button :loading="emailLoading" type="primary" @click="getOutreach()">
      <svg-icon icon-class="email" />
      Outreach
    </el-button>

    <el-dialog
      title="Outreach"
      :visible.sync="dialogVisible"
      append-to-body
      width="60%"
      @closed="emailData = []">
      <div>
        <el-collapse v-if="emailData.length" accordion>
          <el-collapse-item v-for="(item, index) in emailData" :key="index" :name="index">
            <template slot="title">
              <el-row class="w-100">
                <el-col :span="8">
                  <el-popconfirm
                    title="Confirm removal of this user?"
                    confirm-button-text="Confirm"
                    cancel-button-text="Cancel"
                    @confirm="actionRemoveUser(index)"
                  >
                    <el-button slot="reference"
                               type="danger"
                               icon="el-icon-close"
                               circle
                               size="mini"
                               class="remove-btn"
                               @click.stop />
                  </el-popconfirm>
                  <router-link
                    class="text-truncate link"
                    :to="{ name:'ClientContactDetail', params: { contact_id: item.client_contact_id }}">
                    {{ item.name || '' }}
                  </router-link>
                  <span v-if="!item.has_email" class="danger">(No Email)</span>
                  <span v-if="item.last_contact_within_seven_days"
                        class="warning pl-5"> (Last contact within 7 days)</span>
                </el-col>
                <el-col :span="16">
                  <el-select
                    v-model="item.template_id"
                    class="w-85px"
                    @change="generateEmails(item)"
                  >
                    <el-option
                      v-for="(tem, _index) in item.template_options"
                      :key="_index"
                      :label="tem.name"
                      :value="tem.id" />
                  </el-select>
                </el-col>
              </el-row>
            </template>
            <div v-loading="item.emailLoading">
              <to-cc-editor :data="item.email" />
              <tinymce
                :ref="`subject_${item.client_contact_id}`"
                v-model="item.email.subject"
                inline
                inline-header />
              <tinymce
                :ref="`content_${item.client_contact_id}`"
                v-model="item.email.content"
                :height="300"
                inline
                is-email />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary"
                   :disabled="!emailData.length"
                   :loading="sendLoading"
                   @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email.js'
import ClientAPI from '@/api/client'
import Tinymce from '@/components/Tinymce'
import Filters from '@/utils/filters'
import _ from 'lodash'
import moment from 'moment'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'

export default {
  name: 'Index',
  components: { ToCcEditor, Tinymce },
  props: {
    selectUser: Array,
  },
  data() {
    return {
      dialogVisible: false,
      sendLoading: false,
      emailLoading: false,
      emailData: [],
      mail_user_list: [],
      templateList: null,
    }
  },
  methods: {
    getOutreach() {
      this.mail_user_list = _.cloneDeep(this.selectUser)
      this.mail_user_list.forEach(item => {
        item.last_contact_within_seven_days = this.checkContactUser(item)
        item.has_email = !!item.contact_infos.find(item => item.type === 'EMAIL')
        item.template_id = null
        item.email = {
          from: null,
          from_name: null,
          subject: null,
          content: null,
          to_list: [],
          cc_list: [],
          bcc_list: [],
        }
      })
      this.getEmailTemplates()
    },

    getEmailTemplates() {
      this.emailLoading = true
      const client_ids = this.mail_user_list.map(item => item.client_id).join()
      const params = {
        reference_ids: client_ids,
        reference_type: 'CLIENT',
        content_type: 'CLIENT_OUTREACH',
        page: 1,
        size: 999,
        has_permission: true,
        include_default_reference_type: true,
        uid: this.$store.state.user.uid,
      }
      return EmailAPI.getEmailList(params).then(data => {
        this.formatTemplateOptions(data.list)
      }).then(() => {
        const list = []
        this.mail_user_list.forEach(item => {
          if (item.template_id) list.push(this.generateEmails(item))
        })
        return Promise.all(list)
      })
        .finally(() => {
          this.emailData = this.mail_user_list
          this.emailLoading = false
          this.dialogVisible = true
        })
    },

    formatTemplateOptions(list) {
      this.mail_user_list.forEach(item => {
        item.template_options = list.filter(i => +i.reference_id === +item.client_id) || []
        item.template_id = item.template_options[0] ? item.template_options[0].id : null
      })
    },

    generateEmails(item) {
      const params = [
        {
          client_id: item.client_id,
          contact_id: item.client_contact_id,
          user_id: null,
        }]
      item.emailLoading = true
      return EmailAPI.getEmailsByTemplate(item.template_id, params).then(data => {
        item.email = data[0]
        this.$nextTick(() => {
          if (this.$refs[`subject_${item.client_contact_id}`] &&
            this.$refs[`content_${item.client_contact_id}`] && item.email.subject) {
            this.$refs[`subject_${item.client_contact_id}`][0].setContent(item.email.subject)
            this.$refs[`content_${item.client_contact_id}`][0].setContent(item.email.content)
          }
        })
      }).finally(() => {
        item.emailLoading = false
        this.$forceUpdate()
      })
    },

    actionRemoveUser(index) {
      this.emailData.splice(index, 1)
    },

    sendEmails() {
      const existFrequentContact = this.emailData.filter(item => item.last_contact_within_seven_days).length > 0
      const alert_message = existFrequentContact
        ? 'Existing members have contacted within seven days, are you sure to continue sending?'
        : 'Confirm to send emails?'

      const existNoTemplate = this.emailData.filter(item => !item.template_id).length > 0
      if (existNoTemplate) {
        this.$message({
          type: 'error',
          message: 'There are email templates not selected.',
        })
        return
      }

      if (this.checkMailAddress()) return

      this.$confirm(alert_message, 'Notice', {
        type: 'warning',
      }).then(() => {
        const params = {
          batch: this.emailData.map(item => {
            return {
              client_contact_id: item.client_contact_id,
              email: {
                from: item.email.from,
                from_name: item.email.from_name,
                subject: item.email.subject,
                content: item.email.content,
                to_list: item.email.to_list,
                cc_list: item.email.cc_list,
                bcc_list: item.email.bcc_list,
                reply_to_list: item.email.reply_to_list,
              },
            }
          }),
        }
        this.sendLoading = true
        return ClientAPI.mailOutreach(params).then((data) => {
          if (data.length > 0) {
            this.$message({
              type: 'success',
              message: 'Sending successfully!',
            })
            this.$emit('update')
            this.dialogVisible = false
          } else {
            this.$message({
              type: 'error',
              message: 'Sending failed!',
            })
          }
        }).finally(() => {
          this.sendLoading = false
        })
      }).catch(() => {
      })
    },

    checkMailAddress() {
      const error_address = this.emailData.filter(
        item => (!Filters.checkValidMail(item.email.to_list) || !Filters.checkValidMail(item.email.cc_list)))

      if (error_address.length > 0) {
        const error_users = error_address.map(item => item.user.full_name).join()
        const message = `Please check: <span class="danger">${error_users}</span>`
        this.$alert(message,
          'Incorrect email address', {
            confirmButtonText: 'OK',
            type: 'error',
            dangerouslyUseHTMLString: true,
          })
      }
      return error_address.length > 0
    },

    checkContactUser(item) {
      if (!item.last_communication_time) return false
      const last_time = moment(item.last_communication_time)
      return moment().diff(last_time, 'days') <= 7
    },
  },
}
</script>

<style scoped lang="scss">
.remove-btn {
  margin-right: 5px;
}

::v-deep .el-button--mini.is-circle {
  padding: 2px;
}
</style>
