<template>
  <div>
    <div class="mb-8 flex justify-content-between">
      <b>Notes</b>
      <div>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="editNote()" />
        <note-tag-search
          v-if="tag_list.length>0"
          v-model="search_tags"
          :tag-list="tag_list"
          :allow-create="false"
          class="inline-block"
          @change="getNoteList" />
      </div>
    </div>
    <div v-loading="loading" class="mt-8 note-box scrollbar-narrow">
      <div v-for="item in note_list" :key="item.id">
        <div class="note-item">
          <div class="note">
            <div>
              <el-tag type="primary" size="mini">{{ item.tags | getNames(tag_list) }}</el-tag>
              <el-tag v-if="item.create_by" type="info" size="mini" style="color:#3c3a3a;">
                {{item.create_by.name}}
              </el-tag>
              <el-tag type="info" size="mini">{{ item.time_text }}</el-tag>
            </div>
            <div class="actions">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-edit"
                class="item"
                @click="editNote(item)" />
              <el-popconfirm
                icon="el-icon-info"
                icon-color="red"
                title="Are you sure you want to delete this note?"
                cancel-button-text="Cancel"
                confirm-button-text="Confirm"
                confirm-button-type="danger"
                @confirm="deleteNote(item.id)"
              >
                <el-link
                  slot="reference"
                  type="danger"
                  class="item"
                  :underline="false"
                  icon="el-icon-delete" />
              </el-popconfirm>
            </div>
          </div>
          <div v-html="$sanitizeHtml(item.content)" />
        </div>
      </div>
    </div>

    <el-dialog
      :visible.sync="isDialogVisible"
      title="Note"
      width="50%">
      <el-form
        ref="contactNoteValidateForm"
        :model="form"
        label-width="100px">
        <el-form-item
          v-for="(item, index) in form.emails"
          v-show="!form.id"
          :key="index"
          :label="!index ? 'Notifications' : ''"
          :prop="'emails.' + index + '.value'"
          :rules="[{ validator: checkEmail, trigger: ['change','blur'] }]">
          <el-input
            v-model="item.value"
            class="w-200px"
          />
          <el-link
            v-if="index === form.emails.length-1"
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addEmailItem" />
          <el-link
            v-if="index"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeEmailItem(index)" />
        </el-form-item>
        <el-form-item label="Tags" prop="tags">
          <note-tag-search v-model="form.tags" :tag-list="tag_list" class="multiple-select" />
        </el-form-item>
        <el-form-item label="Content" prop="content" required>
          <tinymce ref="note_content" v-model="form.content" :height="400" :toolbar="toolbar" />
        </el-form-item>
        <el-form-item v-show="!form.id" label="send Mail">
          <el-checkbox v-model="send_mail" :disabled="sendToClientThroughCapvisionPro" />
          <div v-if="sendToClientThroughCapvisionPro" class="warning line-height-normal">
            The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com), it will not send relevant emails through the system.
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="!form.content.trim() || !form.tags.length"
          @click="actionSubmit">Save</el-button>
        <el-button type="text" @click="isDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import ClientAPI from '@/api/client'
import Tinymce from '@/components/Tinymce'
import NoteTagSearch from '@/components/ClientNotes/NoteTagSearch'

export default {
  name: 'Notes',
  components: { NoteTagSearch, Tinymce },
  props: {
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
    clientContactId: [Number, String, Object],
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      })
    }
    return {
      checkEmail: validateEmail,
      toolbar: ['undo redo | bold italic link | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      isDialogVisible: false,
      client_id: this.dataUser.client_id,
      contact_id: this.clientContactId,
      search_tags: [],
      note_list: [],
      tag_list: [],
      send_mail: true,
      defaultNotificationEmails: [],
      form: this.initForm(),
    }
  },

  computed: {
    sendToClientThroughCapvisionPro() {
      const has_rule = this.dataUser.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        this.dataUser.client.preference?.tpa_client
    },
  },

  watch: {
    'sendToClientThroughCapvisionPro': {
      handler(newVal) {
        if (newVal) {
          this.send_mail = false
        }
      }, immediate: true,
    },
  },

  mounted() {
    this.getNoteList()
  },

  methods: {
    getNoteList() {
      this.loading = true
      const params = {
        related_client_contact_id: this.clientContactId,
        'tag_refs.tag.ids': this.search_tags.join() || undefined,
        page: 1,
        size: 9999,
        extra: 'create_by,tag_refs',
      }
      return ClientAPI.getClientNoteList(params).then(data => {
        this.note_list = data.list.map(item => {
          item.time_text = moment(item.create_at).fromNow()
          item.is_edit = false
          item.tags = item.tag_refs.map(i => i.tag_id)
          item.emails = item.notification_emails.map(i => {
            return { value: i }
          })
          return item
        })
        if (this.search_tags.length < 1) this.getTagList()
      }).finally(() => {
        this.loading = false
      })
    },

    getTagList() {
      return ClientAPI.getClientNoteTags().then(data => {
        this.tag_list = data.list
      })
    },

    deleteNote(note_id) {
      const index = this.note_list.findIndex(item => item.id === +note_id)
      this.note_list.splice(index, 1)
      return ClientAPI.deleteClientNote(note_id)
    },

    initForm() {
      return {
        tags: [1],
        content: '',
        emails: [{ value: '' }],
      }
    },
    editNote(val) {
      if (val) {
        this.form = Object.assign({}, val)
        if (this.form.emails.length < 1) {
          this.form.emails = [{ value: '' }]
        }
        this.send_mail = false
      } else {
        this.form = this.initForm()
        const default_emails = this.dataUser.client?.account_note_notification_emails
        if (default_emails) {
          this.form.emails = default_emails.map(item => {
            return { value: item }
          })
        }
      }
      if (this.$refs['note_content']) this.$refs['note_content'].setContent(this.form.content)
      this.isDialogVisible = true
    },

    addEmailItem() {
      this.form.emails.push({ value: '' })
    },

    removeEmailItem(index) {
      this.form.emails.splice(index, 1)
    },

    actionSubmit() {
      this.$refs.contactNoteValidateForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.form.id || undefined,
            client_id: this.dataUser.client_id,
            client_contact_id: this.contact_id,
            target_type: 'CLIENT_CONTACT',
            tag_refs: this.form.tags.map(item => {
              return { 'tag_id': item }
            }),
            notification_emails: this.send_mail ? this.form.emails.map(item => item.value) : [],
            content: this.form.content,
          }

          const request = params.id ? ClientAPI.updateClientNote(params) : ClientAPI.addClientNote(params)
          request.then((data) => {
            this.$message({
              message: 'Save successful',
              type: 'success',
            })
            this.getNoteList()
            this.getTagList()
          }).finally(() => {
            this.isDialogVisible = false
          })
        } else {
          return false
        }
      })
    },

  },
}
</script>

<style scoped lang="scss">
.note-box {
  max-height: 600px;
  overflow: auto;
}

.note-item {
  font-size: 12px;
  line-height: 1.5;
  padding-bottom: 0.5rem;

  .note {
    display: flex;
    justify-content: space-between;
  }

  .actions {
    text-align: right;
    flex-shrink: 0;

    .item {
      display: none;
    }

  }

  &:hover {
    .actions {
      .item {
        display: inline-block;
      }
    }
  }
}

</style>
