<template>
  <div>
    <div class="filter-container">
      <el-select
        v-model="searchQuery.status"
        clearable
        class="w-200px filter-item"
        placeholder="Status"
        @change="handleSearch"
      >
        <el-option
          v-for="item in project_options.status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.type"
        clearable
        class="w-200px filter-item"
        placeholder="Type"
        @change="handleSearch"
      >
        <el-option
          v-for="item in project_options.type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-if="isConsultingFirm"
        v-model="searchQuery.case_type"
        placeholder="Case Type"
        class="w-200px filter-item"
        clearable
        @change="handleSearch">
        <el-option
          v-for="item in project_options.case_type"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <project-search v-model="searchQuery.ids" class="w-200px filter-item" @change="handleSearch" />
    </div>
    <el-table
      stripe
      border
      class="headerFixed"
      :data="project_list">

      <el-table-column
        label="Status"
      >
        <template slot-scope="scope">
          <span :class="scope.row.status | getProjectStausClass(filterStatusOptions)">
            {{ scope.row.status | getLabel(filterStatusOptions) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="Start Date"
      >
        <template slot-scope="scope">
          {{ scope.row.start_date | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column
        label="End Date"
      >
        <template slot-scope="scope">
          {{ scope.row.end_date | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column
        label="Project"
      >
        <template slot-scope="scope">
          <router-link-project :data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
      >
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(project_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="isConsultingFirm"
        label="Case Type"
      >
        <template slot-scope="scope">
          {{ scope.row.case_type | getLabel(project_options.case_type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
      >
        <template slot-scope="scope">
          {{ scope.row.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Investment Target"
      >
        <template slot-scope="scope">
          <div v-for="item in scope.row.investment_target_companies" :key="item.id">
            <router-link-company :data="item" />
            <span v-if="item.exchange"> - {{ item.exchange }}<span v-if="item.stock_code"> | {{ item.stock_code }}</span></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.manager">
            {{ scope.row.manager.user.name }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import Pagination from '@/components/Pagination'
import RouterLinkProject from '@/components/RouterLink/Project'
import RouterLinkCompany from '@/components/RouterLink/Company'

export default {
  name: 'Projects',
  components: { RouterLinkProject, Pagination, ProjectSearch, RouterLinkCompany },
  props: {
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      project_list: [],
      searchQuery: {
        status: undefined,
        ids: undefined,
        page: 1,
        size: 20,
        client_contact_id: this.$route.params.contact_id,
        extra: 'manager.user,investment_target_companies',
      },
      total: 0,
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'uid',
      'name',
    ]),

    isConsultingFirm() {
      return this.dataUser.client?.type === 'CONSULTING_FIRM'
    },

    filterStatusOptions() {
      const result = _.cloneDeep(this.project_options.status)
      result.push({ value: 'TOTAL', color: 'primary', label: 'Total' })
      return result
    },
  },

  mounted() {
    this.getList()
  },

  methods: {
    getList() {
      const params = Object.assign({}, this.searchQuery)
      params.status = params.status || undefined
      params.type = params.type || undefined
      params.case_type = params.case_type || undefined
      params.ids = params.ids.length ? params.ids.join(',') : undefined
      return ProjectAPI.getProjectList(params).then(data => {
        this.project_list = data.list
        this.total = data.count
      })
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },
  },
}
</script>

<style scoped>

</style>
