<template>
  <div>
    <div>
      <div class="mb-8">
        <b>Basic Information</b>
        <el-button v-if="!isEdit" class="el-icon-edit" size="mini" @click="handleIsEdit"> Edit</el-button>
        <div v-if="isEdit" class="inline-block">
          <el-button class="el-icon-check" type="primary" size="mini" :loading="submitting" @click="onSave()"> Save
          </el-button>
          <el-button size="mini" @click="cancelEdit"> Cancel</el-button>
        </div>
      </div>
      <el-form
        v-if="!isEdit"
        class="show-form flex flex-wrap"
        label-position="left"
        label-width="110px">
        <div class="flex-1">
          <el-form-item label="First Name">{{ dataUser.firstname }}</el-form-item>
          <el-form-item label="Last Name">{{ dataUser.lastname }}</el-form-item>
          <el-form-item label="Type">
            {{ dataUser.types | getLabels(client_options.contact_person_type) }}
          </el-form-item>
          <el-form-item label="Status">
            {{ dataUser.status | getLabel(client_options.contact_status) }}
          </el-form-item>
          <el-form-item label="Product">
            {{ dataUser.product_of_interests | getLabels(client_options.product) }}
          </el-form-item>
          <el-form-item label="Coverage">
            {{ dataUser.coverages | getLabels(client_options.coverage) }}
          </el-form-item>
          <el-form-item label="Office">
            <span v-if="dataUser.office_id">{{ dataUser.office.name }}</span>
          </el-form-item>
          <el-form-item label="Funds">
            <slot v-if="dataUser.whale_wisdom_filer_maps.length">
              <div v-for="item in dataUser.whale_wisdom_filer_maps" :key="item.id">
                <span v-if="item.whale_wisdom_filer">{{ item.whale_wisdom_filer.filer_name }}</span>
              </div>
            </slot>
          </el-form-item>
        </div>
        <div class="flex-1">
          <el-form-item label-width="180px" label="Location">
            {{ dataUser.location ? dataUser.location.name : '' }}
          </el-form-item>
          <el-form-item label-width="180px" label="Time zone">
            {{ dataUser.zone_id_string || '-' }}
          </el-form-item>
          <el-form-item label-width="180px" label="Project Active">
            {{ dataUser.project_active_count }}
          </el-form-item>
          <el-form-item label-width="180px" label="Project Closed">
            {{ dataUser.project_closed_count }}
          </el-form-item>
          <el-form-item label-width="180px" label="Current Month Billable Hours">
              <span v-if="dataUser.current_period_usage">
                {{ dataUser.current_period_usage.current_month.billable_hour }}
              </span>
          </el-form-item>
          <el-form-item label-width="180px" label="Quarterly Billable Hours">
              <span v-if="dataUser.current_period_usage">
                {{ dataUser.current_period_usage.current_quarter.billable_hour }}
              </span>
          </el-form-item>
          <el-form-item label-width="180px" label="Annual Billable Hours">
              <span v-if="dataUser.current_period_usage">
                {{ dataUser.current_period_usage.current_year.billable_hour }}
              </span>
          </el-form-item>
        </div>
      </el-form>
      <el-form
        v-if="isEdit"
        ref="form"
        :model="form"
        :rules="rules"
        label-position="left"
        class="update-form"
        label-width="110px"
      >
        <el-form-item
          label="First Name"
          prop="firstname">
          <el-input v-model="form.firstname" placeholder="First Name" class="w-200px" />
        </el-form-item>
        <el-form-item
          label="Last Name"
          prop="lastname">
          <el-input v-model="form.lastname" placeholder="Last Name" class="w-200px" />
        </el-form-item>
        <el-form-item
          label="Type"
          prop="types">
          <el-select v-model="form.types" multiple>
            <el-option
              v-for="lang in client_options.contact_person_type"
              :key="lang.value"
              :label="lang.label"
              :value="lang.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Status" required prop="status">
          <el-select
            v-model="form.status"
            class="w-200px filter-item"
            placeholder="Status"
          >
            <el-option
              v-for="item in client_options.contact_status"
              :key="item.value"
              :disabled="item.value !== 'LEFT_COMPANY'"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Product" required prop="product_of_interests">
          <el-select
            v-model="form.product_of_interests"
            multiple
            class="w-200px filter-item"
          >
            <el-option
              v-for="item in client_options.product"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Coverage" required prop="coverages">
          <el-select
            v-model="form.coverages"
            class="w-200px filter-item"
            multiple
          >
            <el-option
              v-for="item in client_options.coverage"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="Office">
          <el-select
            v-model="form.office_id"
            class="w-200px filter-item"
          >
            <el-option
              v-for="item in office_option"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="Location"
          prop="location_id">
          <location v-model="form.location_id" />
        </el-form-item>
        <el-form-item label="Time zone">
          <el-select
            v-model="form.zone_id_string"
            clearable
            filterable>
            <el-option v-for="lang in timezone_list" :key="lang.id" :label="lang.name" :value="lang.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Funds">
          <whale-wisdom-filer-search
            v-model="form.whale_wisdom_filer_maps"
            :filer-list="dataUser.whale_wisdom_filer_maps"
            class="w-100"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import FormMixin from '@/components/FormPage/mixin'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import ClientAPI from '@/api/client'
import Location from '@/components/Location'
import WhaleWisdomFilerSearch from '@/components/SelectRemoteSearch/WhaleWisdomFilerSearch'

export default {
  name: 'Profile',
  components: { Location, WhaleWisdomFilerSearch },
  mixins: [FormMixin],
  props: {
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  data() {
    return {
      isEdit: false,
      form: _.cloneDeep(this.dataUser),
      total: 0,
      office_option: [],
      rules: {
        firstname: [{ required: true, message: 'First name is required', trigger: 'change' }],
        lastname: [{ required: true, message: 'Last name is required', trigger: 'change' }],
        types: [{ required: true, trigger: 'change' }],
        product: [{ required: true, trigger: 'change' }],
        coverages: [{ required: true, message: 'coverages is required', trigger: 'change' }],
        office: [{ required: true, message: 'office is required', trigger: 'change' }],
      },
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
      'timezone_list',
    ]),
  },

  mounted() {
  },

  methods: {
    handleSave(form) {
      const params = {
        firstname: form.firstname,
        lastname: form.lastname,
        types: form.types,
        coverages: form.coverages,
        product_of_interests: form.product_of_interests,
        office_id: form.office_id,
        status: form.status,
        location_id: form.location_id,
        zone_id_string: form.zone_id_string,
        whale_wisdom_filer_maps: form.whale_wisdom_filer_maps,
      }

      return ClientAPI.updateClientContact(this.dataUser.client_id, this.dataUser.id, params)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Update successfully!',
          })
          this.isEdit = false
          this.$emit('update')
        })
    },

    getOffices() {
      return ClientAPI.getClientOffices(this.dataUser.client_id).then(data => {
        this.office_option = data['list']
      })
    },

    handleIsEdit() {
      this.form = _.cloneDeep(this.dataUser)
      this.getOffices()
      this.isEdit = true
    },

    cancelEdit() {
      this.isEdit = false
    },
  },
}
</script>

<style scoped>
.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}

</style>
