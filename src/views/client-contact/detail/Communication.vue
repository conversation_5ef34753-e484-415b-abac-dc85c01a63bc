<template>
  <div>
    <el-table
      stripe
      class="headerFixed"
      :data="list">
      <el-table-column
        label="Project"
      >
        <template slot-scope="scope">
          <router-link-project v-if="scope.row.project_id" :data="scope.row.project" />
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
        prop="email_content_type"
      >
        <template slot-scope="scope">
          {{ getType(scope.row.email_content_type) }}
        </template>
      </el-table-column>
      <el-table-column label="Subject">
        <template slot-scope="scope">
          {{ scope.row.email_record.subject }}
        </template>
      </el-table-column>
      <el-table-column label="Content">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="light" placement="left" :open-delay="200">
            <div slot="content" class="message-content">
              <span v-html="scope.row.email_record.content" />
            </div>
            <el-button>message</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="Date"
      >
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="page"
      :limit.sync="size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import ClientAPI from '@/api/client'

export default {
  name: 'Communication',
  components: { RouterLinkProject, Pagination },
  data() {
    return {
      list: [],
      contact_id: this.$route.params.contact_id,
      page: 1,
      size: 20,
      total: 0,
      type_options: [
        { label: 'Client Outreach', value: 'CLIENT_OUTREACH' },
        { label: 'Linkedin Message', value: 'LINKEDIN_MESSAGE_CLIENT' },
        { label: 'Arrange & Availability', value: 'CALENDAR_TASK_ARRANGE_CONTACT' },
        { label: 'Arrange', value: 'TASK_CLIENT_SCHEDULED_TO_CONTACT' },
        { label: 'Client Compliance Approve', value: 'CLIENT_LEGAL_CONSULTATION_NEED_APPROVE' },
        { label: 'Post Call Client Agreement', value: 'PORTAL_CONTACT_POST_CALL' },
        { label: 'Pre Call Client Agreement', value: 'PORTAL_CONTACT_PRE_CALL' },
      ],
    }
  },
  mounted() {
    this.getList()
  },

  methods: {
    getList() {
      const params = {
        page: this.page,
        size: this.size,
        client_contact_id: this.contact_id,
        extra: 'email_record,email_record.from_name,contact_portal,project',
      }
      return ClientAPI.ContactCommunications(params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
    },
    getType(val) {
      let result = val
      this.type_options.forEach(item => {
        if (item.value === val) {
          result = item.label
        }
      })
      return result
    },
  },
}
</script>

<style scoped>

</style>
