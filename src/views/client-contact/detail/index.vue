<template>
  <div v-loading="loading" class="app-container">
    <div>
      <h3 v-if="contact_data">{{ contact_data.name }} --
        <router-link-client :data="contact_data.client" />
      </h3>
    </div>
    <loading-status :status="!isFinished" :data="contact_data" />
    <el-tabs
      v-if="permissionView && isFinished && isClientContactLoaded"
      v-model="activeName"
      type="card"
      @tab-click="tabClick"
    >
      <el-tab-pane
        v-for="item in tabs"
        :key="item.router_name"
        :name="item.router_name"
      >
        <span slot="label"> {{ item.label }}</span>
      </el-tab-pane>
    </el-tabs>

    <router-view
      v-if="isClientContactLoaded"
      :data-user="contact_data"
      :client-contact-id="contact_id"
      :client="contact_data"
      @update="getData" />

    <el-alert
      v-if="!permissionView && isFinished"
      title="No Permission"
      type="error"
      show-icon
      :closable="false" />
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import RouterLinkClient from '@/components/RouterLink/Client'
import LoadingStatus from '@/components/LoadingStatus'
import { mapGetters } from 'vuex'
import { RouteTools } from '@/utils/tool'

export default {
  name: 'ClientContactDetail',
  components: {
    RouterLinkClient,
    LoadingStatus,
  },
  data() {
    return {
      isFinished: false,
      loading: false,
      contact_id: this.$route.params.contact_id,
      contact_data: null,
      activeName: this.$route.query.tab || 'Profile',
      permissionView: false,
      tabs: [
        { label: 'Profile', router_name: 'ClientContactProfile' },
        { label: 'Projects', router_name: 'ClientContactProjects' },
        { label: 'Tasks', router_name: 'ClientContactTasks' },
        { label: 'Communications', router_name: 'ClientContactCommunications' },
        { label: 'Task Dashboard', router_name: 'ClientContactTaskDashboard' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'uid',
    ]),
    isClientContactLoaded() {
      return this.contact_data && Object.prototype.hasOwnProperty.call(this.contact_data, 'id')
    },
  },

  watch: {
    '$route': {
      handler(to) {
        this.setActiveTab(to.name)
      },
      immediate: true,
    },
  },

  async mounted() {
    await this.getData()
    this.getViewPermission()
  },

  methods: {
    getData() {
      this.loading = true
      const params = {
        extra: 'projects_agg,' +
          'current_period_usage,' +
          'contact_infos,office,' +
          'coverage,' +
          'client.account_managers,' +
          'client.compliance_preference,' +
          'office,jobs,location,' +
          'whale_wisdom_filer_maps.whale_wisdom_filer,',
      }
      return ClientAPI.getContactOne(this.contact_id, params)
        .then(data => {
          data.project_closed_count = data.projects_agg['CLOSED']
          data.project_active_count = Object.values(data.projects_agg).reduce((a, b) => {
            return a + b
          }) - data.project_closed_count

          this.contact_data = data

          this.initTabs()

          return RouteTools.setRouterMetaTitle(data.name, this.$route)
        })
        .finally(() => {
          this.loading = false
        })
    },
    getViewPermission() {
      const isOnlyAM = this.roles.length === 1 && this.roles[0].role_id === 5
      if (isOnlyAM) {
        this.permissionView = this.contact_data.client['account_managers']?.some(item => item.uid === this.uid)
      } else {
        this.permissionView = true
      }
      this.isFinished = true
    },
    initTabs() {
      if (this.contact_data.whale_wisdom_filer_maps.length && !this.tabs.some(tab => tab.label === 'AM Dashboard')) {
        this.tabs.push({ label: 'AM Dashboard', router_name: 'ClientContactAmDashboard' })
      }
    },
    setActiveTab(route_name) {
      this.activeName = route_name
    },
    tabClick(tab) {
      this.$router.push({
        name: tab.name,
      })
    },
  },
}
</script>

<style scoped>
</style>
