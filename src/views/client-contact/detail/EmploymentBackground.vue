<template>
  <div>
    <div class="mb-8">
      <b>Employment History & Background</b>
      <el-button v-if="!isEdit" class="el-icon-edit" @click="handleIsEdit"> Edit</el-button>
      <div v-if="isEdit" class="inline-block">
        <el-button class="el-icon-check" type="primary" :loading="submitting" @click="onSave()"> Save</el-button>
        <el-button @click="cancelEdit"> Cancel</el-button>
      </div>
      <!--show-->
      <el-form
        v-if="dataUser.jobs && !isEdit"
        class="show-form"
        label-position="left"
        label-width="110px">
        <el-form-item label="Experience">
          <div class="line-height-normal">
            <div v-for="job in dataUser.jobs" :key="job.id">
              <router-link
                v-if="job.company"
                class="link"
                :to="{ name: 'CompanyDetail', params: { company_id: job.id } }">
                {{ job.company }}
              </router-link>
              {{
                ` | ${job.position} | ${jobDateFormat(job.start)} ~
                  ${job.is_current ? 'current' : jobDateFormat(job.end)} `
              }}
              {{ getStockCode(job.company) }}
            </div>
          </div>
        </el-form-item>
        <el-form-item label="Background">
          <div class="pre-line line-height-normal">{{ dataUser.background }}</div>
        </el-form-item>
      </el-form>

      <!--edit-->
      <el-form
        v-if="isEdit"
        ref="form"
        :model="form"
        class="update-form mt-8"
        label-width="110px"
        label-position="left"
        :disabled="submitting">
        <el-form-item
          v-for="(exp, index) in form.jobs"
          :key="`experience-${index}`"
          :label="!index ? 'Experience' : ''"
          class="show-form"
          required>
          <el-form-item>
            <el-row class="inline-block">
              <el-form-item
                class="form-item inline-block"
                :prop="`jobs[${index}].company_object`"
                :rules="{required: true, message: 'Company is required', trigger: 'change'}"
              >
                <el-select
                  v-model="exp.company_object"
                  value-key="id"
                  class="remote-select"
                  :class="[{'is-reverse': is_visible}]"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="Company"
                  loading-text="loading..."
                  :remote-method="getCompanyOptions"
                  no-data-text="No matching data"
                  @change="(val)=> {exp.company_id = val.id}"
                >
                  <el-option v-for="item in companyList.data" :key="item.id" :label="item.name" :value="item">
                    <span>{{ item.name }}</span>
                    <span class="ml-3 mr-normal pull-right" style="color: #8492a6;">{{ item.stock_code }}</span>
                  </el-option>
                  <template slot="empty">
                    <p class="el-select-dropdown__empty">
                      <el-link
                        type="primary"
                        :underline="false"
                        @click="goToCreateCompany(companySelectInput, index)">
                        Create <strong>{{ companySelectInput }}</strong> ...
                      </el-link>
                    </p>
                  </template>
                </el-select>
              </el-form-item>
            </el-row>
            <el-form-item
              class="form-item inline-block"
              :prop="'jobs.' + index + '.position'"
              :rules="{required: true, message: 'Position is required', trigger: 'change'}"
            >
              <el-input
                v-model="exp.position"
                class="w-200px"
                placeholder="Position" />
            </el-form-item>
          </el-form-item>
          <el-form-item class="show-form">
            <el-form-item
              class="form-item inline-block"
              :prop="'jobs.' + index + '.start'"
              :rules="{required: true, message: 'Start date is required', trigger: 'change'}"
            >
              <el-date-picker
                v-model="exp.start"
                type="month"
                placeholder="Start Date"
                format="MM/yyyy"
                value-format="yyyy-MM"
              />
            </el-form-item>
            -
            <div class="inline-block">
              <el-form-item
                class="form-item inline-block"
                :prop="'jobs.' + index"
                :rules="{required: true, validator: endTimeValidator , trigger: 'change'}"
                style="margin-top: 15px;"
              >
                <el-date-picker
                  v-model="exp.end"
                  :disabled="exp.is_current"
                  type="month"
                  placeholder="End Date"
                  format="MM/yyyy"
                  value-format="yyyy-MM"
                />
              </el-form-item>
              <el-form-item
                class="form-item ml-8 inline-block"
                :prop="'jobs.' + index + '.is_current'"
              >
                <el-checkbox v-model="exp.is_current">Current
                </el-checkbox>
              </el-form-item>
            </div>
            <el-link
              v-if="form.jobs.length > 1"
              type="danger"
              icon="el-icon-delete"
              class="icon-font"
              :underline="false"
              @click="removeFormItem(form.jobs, index)" />
            <el-link
              type="primary"
              icon="el-icon-plus"
              class="icon-font"
              :underline="false"
              @click="addExperience" />
          </el-form-item>
        </el-form-item>
        <el-form-item label="Background">
          <el-input v-model="form.background" type="textarea" :rows="6" placeholder="Background" />
        </el-form-item>
      </el-form>

      <el-dialog
        title="Create Company"
        width="600px"
        append-to-body
        :visible.sync="dialogVisible"
        @open="openCompanyDialog">
        <create-company
          ref="create-company"
          is-dialog
          :data="{name: companySelectInput}"
          @create="afterCreateCompany" />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import createCompany from '@/views/company/update'
import _ from 'lodash'
import FormMixin from '@/components/FormPage/mixin'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import CompanyAPI from '@/api/company'
import ClientAPI from '@/api/client'
import moment from 'moment-timezone'

export default {
  name: 'EmploymentBackground',
  components: { createCompany },
  mixins: [FormMixin, SelectMixin],
  props: {
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      client_contact: _.cloneDeep(this.dataUser),
      isEdit: false,
      dialogVisible: false,
      companySelectInput: '',
      jobEditingIndex: 0,
      selectLoading: false,
      form: this.initForm(),
      companyList: {
        data: [],
        count: 0,
      },
    }
  },

  methods: {
    initForm() {
      return {
        jobs: [
          {
            company_object: null,
            company_id: '',
            position: '',
            is_current: true,
            start: '',
            end: '',
          }],
        background: '',
      }
    },

    async handleIsEdit() {
      this.isEdit = true
      if (this.dataUser.id) {
        this.client_contact = _.cloneDeep(this.dataUser)
        this.form.jobs = this.client_contact.jobs.map(item => {
          item.company_object = {
            name: item.company,
            id: item.company_id,
          }
          return item
        })
        this.form.background = this.client_contact.background
        this.initSelect()
      }
    },

    addExperience() {
      this.form.jobs.push({
        company_object: null,
        company_id: '',
        position: '',
        is_current: true,
        start: '',
        end: '',
      })
    },

    initSelect() {
      if (this.dataUser.jobs && this.dataUser.jobs.length) {
        const editJobIds = this.dataUser.jobs.map(item => {
          return item.company_id
        })
          .join()
        this.loadGetCompany({
          ids: editJobIds,
          page: 1,
          size: 100,
        })
      } else {
        this.form.jobs = [
          {
            company_object: null,
            company_id: '',
            position: '',
            is_current: true,
            start: '',
            end: '',
          }]
      }
    },

    handleSave(val) {
      const params = {
        jobs: val.jobs.map(item => {
          item.company = item.company_object.name
          return item
        }),
        background: val.background,
      }

      return ClientAPI.updateClientContact(this.dataUser.client_id, this.dataUser.id, params)
        .then(() => {
          this.$emit('update')
          this.cancelEdit()
        })
    },

    getCompanyOptions(val) {
      if (!val) return
      this.companySelectInput = val
      this.loadGetCompanyByEs(val)
    },

    loadGetCompany(params) {
      this.selectLoading = true
      return CompanyAPI.getCompanyList(params)
        .then(data => {
          this.companyList.data = data.list
          this.companyList.count = data.count
        })
        .finally(() => {
          this.selectLoading = false
        })
    },

    loadGetCompanyByEs(val) {
      this.selectLoading = true
      const params = {
        keyword: val,
        page: 1,
        size: 20,
      }
      return CompanyAPI.getCompanyListByEs(params)
        .then(data => {
          this.companyList.data = data.list.map(item => item.source)
          this.companyList.count = data.total
        })
        .finally(() => {
          this.selectLoading = false
        })
    },

    goToCreateCompany(name, index) {
      this.companySelectInput = name
      this.jobEditingIndex = index
      this.dialogVisible = true
    },

    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },

    afterCreateCompany(val) {
      this.dialogVisible = false
      const job = this.form.jobs[this.jobEditingIndex]
      job.company_object = val
      job.company_id = val.id
      this.companyList.data.push(val)
    },
    removeFormItem(form, index) {
      form.splice(index, 1)
    },

    endTimeValidator(rule, value, callback) {
      if (!value.end && !value.is_current) {
        callback(new Error('end date is required'))
      } else {
        callback()
      }
    },

    jobDateFormat(date) {
      if (!date) return '--'
      const list = date.split('-')
      switch (list.length) {
        case 2:
          if (!list[1]) {
            return moment(date)
              .format('YYYY')
          } else {
            return moment(date)
              .format('MM/YYYY')
          }
        case 3:
          return moment(date)
            .format('MM/DD/YYYY')
        default:
          return date
      }
    },

    getStockCode(company) {
      if (company && company.exchange) {
        return ' | ' + company.exchange + ' - ' + company['stock_code']
      }
      if (company && company.type === 'PRIVATE') {
        return ' | ' + 'Private'
      }
    },

    cancelEdit() {
      Object.assign(this.client_contact, this.dataUser)
      this.isEdit = false
    },
  },
}
</script>

<style scoped>
.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}
</style>
