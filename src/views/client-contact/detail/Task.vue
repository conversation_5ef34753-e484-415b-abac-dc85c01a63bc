<template>
  <div>
    <div class="filter-container">
      <project-search v-model="searchQuery.project_ids" class="w-200px filter-item" @change="handleSearch" />
      <advisor-search v-model="searchQuery.advisor_ids" class="w-200px filter-item" @change="handleSearch" />
      <el-select
        v-model="searchQuery.general_status"
        clearable
        class="w-200px filter-item"
        placeholder="Status"
        @change="handleSearch"
      >
        <el-option
          v-for="item in status_option"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-table
      stripe
      border
      class="headerFixed"
      :data="task_list">

      <el-table-column
        label="Status"
      >
        <template slot-scope="scope">
          <span class="capitalize">
            {{ scope.row.general_status.toLowerCase() }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Project"
      >
        <template slot-scope="scope">
          <router-link-project :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column
        label="Project Type"
      >
        <template slot-scope="scope">
          {{ scope.row.project_type | getLabel(project_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Task Date"
      >
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column
        label="Client hours"
      >
        <template slot-scope="scope">
          {{ scope.row.client_hours }}
        </template>
      </el-table-column>
      <el-table-column
        label="Consultant"
      >
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column
        label="Support"
      >
        <template v-if="scope.row.support" slot-scope="scope">
          {{ scope.row.support.name }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

  </div>
</template>
<script>
import ProjectAPI from '@/api/project'
import RouterLinkProject from '@/components/RouterLink/Project'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'

export default {
  name: 'Task',
  components: { Pagination, RouterLinkProject, RouterLinkAdvisor, ProjectSearch, AdvisorSearch },
  data() {
    return {
      task_list: [],
      searchQuery: {
        general_status: undefined,
        project_ids: undefined,
        advisor_ids: undefined,
        page: 1,
        size: 20,
        client_contact_id: this.$route.params.contact_id,
        extra: 'project,advisor,support',
      },
      total: 0,
      status_option: [
        { value: 'INITIAL', label: 'Initial' },
        { value: 'RECOMMENDED', label: 'Recommended' },
        { value: 'SELECTED', label: 'Selected' },
        { value: 'SCHEDULED', label: 'Scheduled' },
        { value: 'ARRANGED', label: 'Arranged' },
        { value: 'COMPLETED', label: 'Completed' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
    ]),
  },

  // 只有arrange 后的task，才会在这里展示
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const params = Object.assign({}, this.searchQuery)
      params.general_status = params.general_status || undefined
      params.advisor_ids = params.advisor_ids.length ? params.advisor_ids.join(',') : undefined
      params.project_ids = params.project_ids.length ? params.project_ids.join(',') : undefined
      return ProjectAPI.getConsultationList(params).then(data => {
        this.task_list = data.list
        this.total = data.count
      })
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },
  },
}
</script>

<style scoped>

</style>
