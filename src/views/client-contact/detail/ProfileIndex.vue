<template>
  <el-row>
    <el-col :span="14">
      <profile class="item" v-bind="$attrs" v-on="$listeners" />
      <employment-background class="item" v-bind="$attrs" v-on="$listeners" />
    </el-col>
    <el-col :span="10">
      <contact-info user-type="CONTACT" class="item" v-bind="$attrs" v-on="$listeners" />
      <Notes class="item" v-bind="$attrs" v-on="$listeners" />
    </el-col>
  </el-row>
</template>

<script>
import EmploymentBackground from '@/views/client-contact/detail/EmploymentBackground'
import ContactInfo from '@/components/ContactInfo'
import Profile from '@/views/client-contact/detail/profile'
import Notes from '@/views/client-contact/detail/Notes'

export default {
  name: 'ClientContactProfileIndex',
  components: {
    EmploymentBackground,
    ContactInfo,
    Profile,
    Notes,
  },
  data() {
    return {}
  },
}
</script>

<style scoped>
.item {
  border: 1px solid #c9c5c5;
  padding: 15px;
  margin: 10px 5px;
}

::v-deep .item .el-button--mini {
  padding: 3px 6px;
}
</style>
