<template>
  <div class="app-container">
    <div class="flex justify-content-between flex-wrap mb-3">
      <div class="filter-container">
        <el-input
          v-model="searchQuery.keyword"
          placeholder="Keywords"
          class="filter-item w-408px"
        />
        <br>
        <user-search
          v-model="searchQuery.am_ids"
          class="filter-item w-200px"
          placeholder="AM / Sales"
          clearable
          multiple
        />
        <el-select
          v-model="searchQuery.is_proactive"
          class="filter-item w-200px"
          :class="{'is-reverse':is_visible}"
          reserve-keyword
          placeholder="Proactive"
          clearable
          no-data-text="No matching data"
        >
          <el-option label="Yes" :value="true" />
          <el-option label="No" :value="false" />
        </el-select>
        <el-select
          v-model="searchQuery.sub_type"
          placeholder="Project Type"
          class="filter-item  w-200px"
          clearable
          @change="handleSearch">
          <el-option
            v-for="lang in project_options.sub_type"
            :key="lang.value"
            :label="lang.label"
            :value="lang.value" />
        </el-select>
        <br>
        <el-select
          v-model="searchQuery.status"
          class="filter-item w-200px"
          placeholder="Status"
          clearable
          multiple
        >
          <el-option v-for="item in project_options.status" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select
          v-model="searchQuery.industry"
          class="filter-item w-200px"
          placeholder="Industry"
          clearable
          multiple
        >
          <el-option
                     v-for="item in project_options.industry"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value" />
        </el-select>
        <el-select
          v-model="searchQuery.team_id"
          class="filter-item w-200px"
          placeholder="Team"
          clearable
          multiple
          remote
          filterable
          :remote-method="getTeamList"
        >
          <el-option
                     v-for="item in team_list"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id" />
        </el-select>
        <br>
        <el-input
          v-model="searchQuery.name"
          placeholder="Project Name"
          class="filter-item w-200px"
        />
        <el-input
          v-model="searchQuery.investment_target"
          placeholder="Investment Target"
          class="filter-item w-200px"
        />

        <user-search
          v-model="searchQuery.user_ids"
          class="filter-item w-200px"
          placeholder="Project Manager"
          clearable
          multiple
        />
        <br>
        <el-date-picker
          v-model="searchQuery.start_date"
          type="date"
          placeholder="Start"
          value-format="yyyy-MM-dd"
          class="filter-item date-picker"
        />
        <el-date-picker
          v-model="searchQuery.end_date"
          type="date"
          placeholder="End"
          value-format="yyyy-MM-dd"
          class="filter-item date-picker"
        />
        <client-search
          v-model="searchQuery.client_id_not_in"
          multiple
          placeholder="Exclude Client"
          class="w-200px filter-item" />
        <el-input
          v-if="isSGDB"
          v-model="searchQuery.outsource_client_name_contains"
          placeholder="CN Client"
          class="filter-item w-200px"
        />
        <br>
        <el-select
          v-model="searchQuery.client_type"
          class="filter-item w-200px"
          placeholder="Client Type"
          clearable
        >
          <el-option v-for="item in client_options.type" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <client-search
                       v-model="searchQuery.client_id"
                       multiple
                       placeholder="Client"
                       class="w-200px filter-item"
        />
        <el-select
          v-model="searchQuery.client_contact_ids"
          class="filter-item w-200px remote-select"
          :class="{'is-reverse':is_visible}"
          filterable
          remote
          reserve-keyword
          placeholder="Client Contact"
          multiple
          clearable
          :loading="selectLoading"
          :remote-method="getClientContacts"
          no-data-text="No matching data"
        >
          <el-option v-for="item in contactsList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>

        <el-button type="primary" class="filter-item" @click="handleSearch()">Search</el-button>
        <el-button type="primary" class="filter-item" :disabled="total<1" plain @click="downloadData()">Download</el-button>
      </div>
      <div>
      <el-table
          v-if="total_data"
          v-loading="loading"
          border
          :show-header="false"
          stripe
          :data="total_data"
        >
          <el-table-column width="220" prop="label" />
          <el-table-column width="60" prop="value" />
        </el-table>
      </div>
    </div>
    <el-table
      ref="projectData"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['projectData'])"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column
        label="Client"
        min-width="120">
        <template v-if="scope.row.client_id" slot-scope="scope">
          <router-link-client :data="{id:scope.row.client_id,name:scope.row.client}" />
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.project_manager }}
        </template>
      </el-table-column>
      <el-table-column
        label="Project Name"
        min-width="120">
        <template slot-scope="scope">
          <router-link-project :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
      >
        <template slot-scope="scope">
          {{ scope.row.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        prop="status"
      />
      <el-table-column
        label="Proactive"
      >
        <template slot-scope="scope">
          {{ scope.row.project.is_proactive | yesOrNo }}
        </template>
      </el-table-column>
      <el-table-column
        label="Client Contact"
        prop="client_contact"
      />
      <el-table-column
        label="Client Type"
        prop="client_type"
      />
      <el-table-column
        v-if="isSGDB"
        label="CN Client"
      >
        <template v-slot="{row}">
          <span v-if="row.project && row.project.exported_to === 'CN'">
            {{ row.project.outsource_client_name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Start Date"
      >
        <template slot-scope="scope">
          {{ scope.row.start_date }}
        </template>
      </el-table-column>
      <el-table-column
        label="Leads Added"
        prop="count_leads_added"
      />

      <el-table-column
        label="Advisors Submitted"
        min-width="135"
        prop="count_advisor_submitted"
      />

      <el-table-column
        label="Advisor Submitted (Custom Sourced)"
        min-width="135"
        prop="count_advisor_submitted_recent_created_advisor"
      />
      <el-table-column
        label="Advisors Selected"
        prop="count_advisor_selected"
      />
      <el-table-column
        label="Selected / Submitted"
        prop="percentage_selected_submitted"
      />
      <el-table-column
        label="Advisors Scheduled"
        prop="count_advisor_scheduled"
        min-width="120" />
      <el-table-column
        label="Completed"
        prop="count_advisor_completed"
      />
      <el-table-column
        label="Time to First Delivery"
        prop="first_delivery_time"
      />
    </el-table>

    <pagination
                :total="total"
                :page.sync="searchQuery.page"
                :limit.sync="searchQuery.size"
                :page-sizes="customPageSize"
                @pagination="getList" />
  </div>
</template>
<script>
import ReportAPI from '@/api/report'
import userAPI from '@/api/user'
import Pagination from '@/components/Pagination'
import { isEmpty } from '@/utils/tool'
import moment from 'moment-timezone'
import ClientAPI from '@/api/client'
import { mapGetters, mapState } from 'vuex'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkProject from '@/components/RouterLink/Project'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'

export default {
  name: 'Index',
  components: { Pagination, RouterLinkClient, RouterLinkProject, UserSearch, ClientSearch },
  mixins: [SelectMixin],
  data() {
    return {
      loading: false,
      tableData: [],
      selectLoading: false,
      contactsList: [],
      download_ids: [],
      customPageSize: [50, 100, 150, 200, 250, 300],
      searchQuery: {
        page: 1,
        size: 50,
        keyword: '',
        name: '',
        team_id: '',
        is_proactive: undefined,
        sub_type: undefined,
        status: '',
        user_ids: [],
        am_ids: [],
        industry: '',
        start_date: moment().startOf('month').format('YYYY-MM-DD'),
        end_date: moment().endOf('month').format('YYYY-MM-DD'),
        client_type: '',
        client_id: '',
        client_id_not_in: '',
        client_contact_ids: '',
        outsource_client_name_contains: '',
      },
      total: 0,
      team_list: [],
      role_list: [],
      total_data: undefined,
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'client_options',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  mounted() {
    this.getTeamList()
    this.getClientContacts()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },
    getList() {
      this.loading = true
      this.total_data = undefined
      const params = this.formatParams(this.searchQuery)

      return ReportAPI.getProjectData(params)
        .then(data => {
          this.tableData = data['items']
          this.tableData.forEach(item => {
            item.id = item.project_id
          })
          this.total = data.count
          this.total_data = [
            { label: 'Leads Added', value: data['total_leads_added'] },
            { label: 'Advisor Scheduled', value: data['total_advisor_scheduled'] },
            { label: 'Advisor Selected', value: data['total_advisor_selected'] },
            { label: 'Advisor Completed', value: data['total_advisor_completed'] },
            { label: 'Advisor Submitted', value: data['total_advisor_submitted'] },
            { label: 'Selected Submitted', value: data['total_percentage_selected_submitted'] },
            { label: 'Project Count', value: data['count'] },
            { label: 'Average Leads Added Per Project', value: data['average_leads_per_project'].toFixed(2) },
            { label: 'Average Submitted per Project', value: data['average_submitted_per_project'].toFixed(2) },
            { label: 'Average Time to First Deliver', value: data['average_time_first_deliver'].toFixed(2) },
            { label: 'Custom Sourced Experts', value: data['total_advisor_recent_created'] },
          ]
        })
        .finally(() => {
          this.loading = false
        })
    },
    downloadData() {
      const params = this.formatParams(this.searchQuery)
      if (this.download_ids.length > 0) params.ids = this.download_ids.join()
      delete params.page
      delete params.size
      return ReportAPI.downloadProjectData(params)
    },

    getTeamList(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        page: 1,
        size: 15,
      }
      return userAPI.getUserTeamList(params).then(data => {
        this.team_list = data.list
      })
    },

    getClientContacts(val) {
      this.selectLoading = true
      const params = {
        name_like: val || undefined,
        page: 1,
        size: 15,
      }

      return ClientAPI.searchClient(params)
        .then(data => {
          this.selectLoading = false
          this.contactsList = data.list
        })
    },
    handleSelectionChange(val) {
      this.download_ids = val.map(item => item.project_id)
    },

    formatParams(input) {
      const result = {}
      const query_list = []

      const time_zone = this.isSGDB ? 'Asia/Singapore' : 'America/New_York'
      const pass_keys = ['page', 'size', 'is_proactive', 'sub_type', 'outsource_client_name_contains']

      for (const key in input) {
        const value = input[key]
        if (pass_keys.includes(key) || isEmpty(value)) {
          continue
        }

        if (key === 'start_date') {
          const start = moment.tz(moment(value).startOf('day'), time_zone).valueOf()
          const result = `start_date:(>=${start})`
          query_list.push(result)
        } else if (key === 'end_date') {
          query_list.push(`start_date:(<=${moment.tz(moment(value).endOf('day'), time_zone).valueOf()})`)
        } else if (key === 'user_ids') {
          result['member.user_ids'] = value.length > 0 ? value.join() : undefined
          result['member.role'] = value.length > 0 ? 'PROJECT_MANAGER' : undefined
        } else if (key === 'am_ids') {
          result['client.account_managers.user.ids'] = value.length > 0 ? value.join() : undefined
        } else if (key === 'team_id') {
          result['manager.user.team_member.team.ids'] = value.join()
        } else if (key === 'client_id_not_in') {
          query_list.push('exclude' + ':(' + value.join(' ') + ')')
          result.client_id_not_in = value.join()
        } else if (typeof value === 'object') {
          const ids = value.join(' ')
          query_list.push(key + ':(' + ids + ')')
        } else if (key === 'keyword') {
          query_list.push(`"${value}"~0`)
        } else if (key === 'name') {
          query_list.push(`${key}:("${value}"~0)`)
        } else {
          query_list.push(key + ':(' + value + ')')
        }
      }

      result.search_words = query_list.map(item => {
        if (item.includes('exclude')) {
          return ['!(' + item.replace('exclude', 'client_id') + ')'].join('')
        } else {
          return ['(', item, ')'].join('')
        }
      }).join(' AND ') || '*'

      result.sort = 'start_date desc,create_at desc'
      result.is_proactive = typeof input.is_proactive === 'boolean' ? input.is_proactive : undefined
      result.sub_type = input.sub_type || undefined
      result.outsource_client_name_contains = input.outsource_client_name_contains || undefined
      result.page = input.page
      result.size = input.size

      return result
    },
  },
}
</script>

<style scoped>
.date-picker {
  width: 200px !important;
}
</style>
