<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="handleSearch" />
      <el-date-picker
        v-model="searchQuery.date"
        type="month"
        format="MM/yyyy"
        value-format="yyyy-MM"
        placeholder="Date"
        :clearable="false"
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <team-search
        v-model="searchQuery.team_id"
        class="filter-item w-200px"
        @change="handleSearch"
      />
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
    >
      <el-table-column
        label="Client Name"
      >
        <template v-slot="{row}">
          <router-link-client :data="{ id: row.client_id, name: row.client_name }" />
        </template>
      </el-table-column>
      <el-table-column
        label="Total Calls"
        prop="total_calls"
        width="110"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.completed_task_ids.length" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="id in row.completed_task_ids"
                :key="id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(id)"
              >
                {{ id }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.total_calls }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.total_calls }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Pipeline"
        prop="scheduled_calls"
        width="110"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.scheduled_task_ids.length" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="id in row.scheduled_task_ids"
                :key="id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(id)"
              >
                {{ id }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.scheduled_calls }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.scheduled_calls }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Client Hours"
        prop="client_hours"
        width="110"
      />
      <el-table-column
        label="Billable Hours"
        prop="billable_hours"
        width="110"
      />
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getDashboard"
    />
  </div>
</template>

<script>
import ReportAPI from '@/api/report'
import { mapState } from 'vuex'
import moment from 'moment-timezone'
import Pagination from '@/components/Pagination/index.vue'
import RefreshButton from '@/components/RefreshButton/index.vue'
import RouterTools from '@/utils/tool-router'
import TeamSearch from '@/components/SelectRemoteSearch/UserTeamSearch/index.vue'
import RouterLinkClient from '@/components/RouterLink/Client.vue'

export default {
  name: 'CallTrackerClientDashboard',
  components: { RouterLinkClient, TeamSearch, Pagination, RefreshButton },
  data() {
    return {
      loading: true,
      total: 0,
      searchQuery: {
        page: 1,
        size: 100,
        team_id: undefined,
        date: moment().toISOString(),
      },
      tableData: [],
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  async mounted() {
    this.searchQuery.team_id = this.isSGDB ? undefined : 4
    // this.searchQuery.team_id = this.options.team_list.find(team => team.name.toLowerCase().includes('us'))?.id
    RouterTools.resolveRouteQuery(this.$route, this.searchQuery)
    await this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      this.getDashboard()
    },
    getDashboard() {
      const params = {
        ...this.searchQuery,
        date: moment(this.searchQuery.date).endOf('month').toISOString(),
        team_id: this.searchQuery.team_id || undefined,
      }
      RouterTools.saveQueryToRouter(this.searchQuery)
      this.loading = true
      return ReportAPI.getCallTrackerClientDashboard(params)
        .then(data => {
          this.total = data.count
          this.tableData = data.items
        })
        .finally(() => {
          this.loading = false
        })
    },
    routeToCallTracker(id) {
      this.$router.push({ name: 'CallTrackerIndex', query: { id, team_id: undefined } })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-link + .el-link {
  margin-left: 8px;
}

.calls-tooltip-content {
  max-width: 500px;
}
</style>
