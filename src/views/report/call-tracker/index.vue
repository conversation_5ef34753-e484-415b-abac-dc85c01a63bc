<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="Call Tracker" name="CallTrackerIndex" />
      <el-tab-pane v-if="allowDashboard" label="Dashboard" name="CallTrackerDashboard" />
      <el-tab-pane v-if="allowClientDashboard" label="Client Dashboard" name="CallTrackerClientDashboard" />
      <el-tab-pane v-if="allowSetTargets" label="Team Targets" name="CallTrackerSetTargets" />
      <el-tab-pane v-if="allowSetTargets" label="User Targets" name="CallTrackerSetUserTargets" />
      <el-tab-pane v-if="isAdmin" label="Client Hour Adjusters" name="CallTrackerClientHourAdjusters" />
    </el-tabs>

    <router-view
      :team-list="options.team_list"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import userAPI from '@/api/user'

export default {
  name: 'CallTrackerIndex',
  data() {
    return {
      activeName: 'CallTrackerIndex',
      options: {
        team_list: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    isAdmin() {
      return this.roles.some(item => ['Admin'].includes(item.role.name))
    },
    allowSetTargets() {
      return this.roles.some(item => ['Admin', 'SetTarget'].includes(item.role.name))
    },
    allowDashboard() {
      return this.roles.some(item => ['Admin', 'CallTrackerDashboard'].includes(item.role.name))
    },
    allowClientDashboard() {
      return this.roles.some(item => ['Admin', 'CallTrackerClientDashboard'].includes(item.role.name))
    },
  },
  watch: {
    '$route': {
      handler(to) {
        this.setActiveTab(to.name)
      },
      immediate: true,
    },
  },
  mounted() {
    this.getTeamList()
  },
  methods: {
    setActiveTab(route_name) {
      this.activeName = route_name
    },
    handleClick(val) {
      this.$router.push({ name: val.name })
    },
    getTeamList(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        page: 1,
        size: 999,
      }
      return userAPI.getUserTeamList(params)
        .then(data => {
          this.options.team_list = data.list
        })
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
