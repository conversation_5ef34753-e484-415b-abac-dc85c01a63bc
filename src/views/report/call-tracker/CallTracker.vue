<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <client-search
        v-model="searchQuery.client_ids"
        multiple
        class="w-200px filter-item"
        @change="search" />
      <project-search v-model="searchQuery.project_ids" class="w-200px filter-item" @change="search" />
      <el-select
        v-model="searchQuery.project_sub_type_in"
        multiple
        clearable
        placeholder="Project Sub Type"
        class="filter-item w-200px"
        @change="search"
      >
        <el-option
          v-for="lang in project_options.sub_type"
          :key="lang.value"
          :label="lang.label"
          :value="lang.value"
        />
      </el-select>
      <el-input
        v-model="searchQuery.id"
        clearable
        placeholder="Task ID"
        class="filter-item w-200px"
        @change="search"
      />
      <advisor-search v-model="searchQuery.advisor_ids" class="w-200px filter-item" @change="search" />
      <el-select
        v-model="searchQuery.status_in"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Status"
        @change="search">
        <el-option
          v-for="item in call_status"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="date_range"
        type="daterange"
        class="filter-item"
        start-placeholder="Start Date"
        end-placeholder="End Date"
        :default-time="['00:00:00', '23:59:59']"
        @change="search" />
      <user-search
        v-model="searchQuery.lead_id"
        class="w-200px filter-item"
        clearable
        placeholder="Lead"
        @change="search" />
      <user-search
        v-model="searchQuery.support_id"
        class="w-200px filter-item"
        clearable
        placeholder="Support"
        @change="search" />
      <team-search
        v-model="searchQuery.team_id"
        class="filter-item w-200px"
        @change="search"
      />
      <el-input
        v-model="searchQuery['project.outsource_client_name_contains']"
        clearable
        placeholder="CN Client"
        class="filter-item w-200px"
        @change="search"
      />
      <el-button
        type="primary"
        :loading="downloadLoading"
        :disabled="downloadDisabled"
        plain
        class="filter-item"
        @click="downloadData()"
      >
        Download
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData"
      @sort-change="handleSortData">
      <el-table-column
        label="Client Name"
        min-width="130"
        prop="client_id"
        sortable="custom"
      >
        <template slot-scope="scope">
          <router-link-client :data="{ id: scope.row.client_id, name: scope.row.client_name }" />
        </template>
      </el-table-column>
      <el-table-column
        label="CN Client"
        prop="cn_client"
        min-width="130"
      />
      <el-table-column
        label="Project Name"
        min-width="130"
        prop="project_id"
        sortable="custom">
        <template v-slot="{row}">
          <router-link
            :to="getTaskRouterInfo(row)"
            target="_blank"
            class="text-truncate link"
          >
            {{ row.project_name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Advisor Name"
        min-width="130"
        prop="advisor_id"
        sortable="custom">
        <template slot-scope="scope">
          <router-link-advisor :data="{ id: scope.row.advisor_id, full_name: scope.row.advisor_name }" />
        </template>
      </el-table-column>
      <el-table-column
        prop="project_sub_type"
        label="Project Sub Type"
        min-width="120"
      />
      <el-table-column
        prop="task_sub_type"
        label="Task Sub Type"
        min-width="120"
      >
        <template v-slot="{row}">
          {{ row.task_sub_type | getLabel(project_options.task.sub_type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Rate"
        sortable="custom"
        prop="advisor.rate"
        width="80">
        <template slot-scope="scope">
          {{ scope.row.rate }}
        </template>
      </el-table-column>
      <el-table-column
        prop="lead_uid"
        label="Lead"
        sortable="custom"
        min-width="120"
      >
        <template v-slot="{row}">
          <span v-if="!hasPermission">
            {{ row.lead }}
          </span>
          <el-dropdown
            v-else
            placement="bottom-start"
            trigger="click"
            class="fs-12"
            @command="updateSupportMember($event, row, 'lead')"
          >
            <el-link :underline="false" @click="actionGetMembersAndAdvisor(row, 'lead')">
              {{ row.lead }}
              <i v-if="row.lead_loading" class="el-icon-loading" />
              <i v-else class="el-icon-arrow-down el-icon--right" />
            </el-link>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="member in row.support_members"
                :key="member.id"
                :disabled="member.disabled"
                :command="member"
              >
                {{ member.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column
        prop="support_uid"
        sortable="custom"
        label="Support"
        min-width="120">
        <template v-slot="{row}">
          <span v-if="!hasPermission">
            {{ row.support }}
          </span>
          <el-dropdown
            v-else
            placement="bottom-start"
            trigger="click"
            class="fs-12"
            @command="updateSupportMember($event, row, 'support')"
          >
            <el-link :underline="false" @click="actionGetMembersAndAdvisor(row, 'support')">
              {{ row.support }}
              <i v-if="row.support_loading" class="el-icon-loading" />
              <i v-else class="el-icon-arrow-down el-icon--right" />
            </el-link>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="member in row.support_members"
                :key="member.id"
                :disabled="member.disabled"
                :command="member"
              >
                {{ member.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column
        label="Custom Sourced"
        min-width="70"
      >
        <template v-slot="{row}">
          <i v-if="row.is_custom_sourced_loading" class="el-icon-loading" />
          <template v-else>
            <el-dropdown
              v-if="hasPermission"
              trigger="click"
              class="fs-12"
              @command="updateCustomSourced($event, row)"
            >
              <el-link :type="row.is_custom_sourced ? 'success' : ''" :underline="false">
                {{ row.is_custom_sourced | yesOrNo }}
                <i class="el-icon-arrow-down el-icon--right" />
              </el-link>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="true">Yes</el-dropdown-item>
                <el-dropdown-item :command="false">No</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span v-else :class="row.is_custom_sourced ? 'success' : ''">
              {{ row.is_custom_sourced | yesOrNo }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        min-width="150"
        prop="general_status"
        sortable="custom"
      >
        <template v-slot="{row}">
          <i v-if="row.status_loading" class="el-icon-loading" />
          <template v-else>
            <el-dropdown
              v-if="hasPermission"
              trigger="click"
              class="fs-12"
              @command="actionChangeStatus($event, row)"
            >
              <el-link :type="row.status | getName(call_status, 'value', 'type')" :underline="false">
                {{ row.status | getLabel(call_status) }}
                <i class="el-icon-arrow-down el-icon--right" />
              </el-link>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="FAILED">Failed</el-dropdown-item>
                <el-dropdown-item command="CANCELED">Canceled</el-dropdown-item>
                <el-dropdown-item :command="null">None</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span v-else :class="row.status | getName(call_status, 'value', 'type')">
                {{ row.status | getLabel(call_status) }}
            </span>
          </template>
          <!--          <span v-if="scope.row.status==='ARRANGED' && scope.row.compliance !== 'APPROVED'"> - Pending Approval</span>-->
        </template>
      </el-table-column>
      <el-table-column
        prop="start_time"
        sortable="custom"
        label="Consultation Date"
        min-width="130">
        <template slot-scope="scope">
          {{ scope.row.consultation_date | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="duration"
        label="Duration"
        min-width="70"
      />
      <el-table-column
        prop="effective_billable_hours"
        label="Effective Billable Hours"
        min-width="80"
      />
      <el-table-column
        prop="client_hours"
        label="Client Hours"
        sortable="custom"
        min-width="90">
        <template v-slot="{row, index}">
          <i v-if="row.client_hours_loading" class="el-icon-loading" />
          <template v-else>
            <template v-if="!row.client_hours_editing">
              <span v-if="row.client_hours" class="mr-8">{{ row.client_hours }}</span>
              <el-link
                v-if="hasPermission"
                type="primary"
                :underline="false"
                icon="el-icon-edit"
                @click="actionEditClientHours(row, index)"
              />
            </template>
            <el-input
              v-else
              :ref="`clientHour${index}`"
              v-model="row.client_hours_input"
              type="number"
              :min="0"
              @keyup.enter.native="updateClientHours(row.client_hours_input, row)"
              @blur="updateClientHours(row.client_hours_input, row)"
            />
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="total_charge"
        label="Total Charge"
        min-width="80"
      />
      <el-table-column
        prop="margin"
        label="Margin"
        min-width="70"
      >
        <template v-slot="{row}">
          <span
            :class="{'success': parseInt(row.margin) > 60,
            'warning': parseInt(row.margin) >= 50 && parseInt(row.margin) <= 60,
            'danger': parseInt(row.margin) < 50 }"
          >
            {{ row.margin }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Notes"
        width="60"
      >
        <template v-slot="{row}">
          <div class="flex justify-content-between">
            <el-tooltip
              :disabled="!row.notes.length"
              effect="light"
              placement="left"
              :open-delay="300"
            >
              <div slot="content" class="notes-content">
                <div v-for="note in row.notes" :key="note.id" class="notes-content-item">
                  <div v-html="$sanitizeHtml(note.note)" class="pre-line" />
                  <div class="info text-right">
                    <span v-if="note.create_by">{{ note.create_by.name }}</span>
                    <span>({{ note.type }})</span>
                    <span v-if="note.update_at">- {{ note.update_at | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                  </div>
                </div>
              </div>
              <el-link
                :type="row.notes.length ? 'primary' : ''"
                :underline="false"
                :disabled="!row.notes.length"
              >
                {{ row.notes.length }}
              </el-link>
            </el-tooltip>
            <el-link
              type="primary"
              :underline="false"
              icon="el-icon-circle-plus"
              class="add-note-icon"
              @click="openAddNoteDialog(row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Action" width="60">
        <template slot-scope="scope">
          <el-tooltip
            v-if="['COMPLETED'].includes(scope.row.status)"
            effect="dark"
            content="Open Completed Page"
            placement="top"
          >
            <router-link
              :to="{ name: 'TaskComplete', params: { project_id: scope.row.project_id }, query: { id: scope.row.task_id } }"
              class="text-truncate link"
            >
              <i class="el-icon-finished" />
            </router-link>
          </el-tooltip>
          <!--          <el-tooltip effect="dark" content="Complete" placement="top">-->
          <!--            <el-button-->
          <!--              type="text"-->
          <!--              :disabled="!['ARRANGED','COMPLETED'].includes(scope.row.status)"-->
          <!--              icon="el-icon-finished"-->
          <!--              @click="actionComplete(scope.row)"-->
          <!--            />-->
          <!--          </el-tooltip>-->
        </template>
      </el-table-column>
    </el-table>

    <!--    <el-dialog
          title="Complete Task"
          :visible.sync="completeDialogVisible"
          :close-on-click-modal="false"
          :show-close="false"
          width="30%">
          <div>
            <el-form ref="form"
                     :model="completeForm"
                     label-width="100px"
                     size="mini">
              <el-form-item
                prop="marked_client_hour"
                label="Client hour"
                :rules="{required: true, message: 'Client hour is required', trigger: 'change'}">
                <el-input v-model="completeForm.marked_client_hour" type="number" class="w-200px" />
              </el-form-item>
              <el-form-item
                label="Billable hour"
                prop="marked_billable_hour"
                :rules="{required: true, message: 'Billable hour is required', trigger: 'change'}">
                <el-input v-model="completeForm.marked_billable_hour" type="number" class="w-200px" />
              </el-form-item>
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" :disabled="submitting" @click="completeTask()">Complete</el-button>
            <el-button type="info" @click="completeDialogVisible = false">Cancel</el-button>
          </span>
        </el-dialog>-->

    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />

    <add-tracker-note-dialog
      ref="addTrackerNoteDialog"
      @done="getList"
    />
  </div>
</template>

<script>
import moment from 'moment'
import ReportAPI from '@/api/report'
import ProjectAPI from '@/api/project'
import ConsultantAPI from '@/api/consultant'
import { mapGetters, mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import TeamSearch from '@/components/SelectRemoteSearch/UserTeamSearch'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import AddTrackerNoteDialog from './components/AddTrackerNoteDialog.vue'

export default {
  name: 'CallTracker',
  components: {
    RouterLinkAdvisor,
    RouterLinkClient,
    RefreshButton,
    Pagination,
    ClientSearch,
    AdvisorSearch,
    UserSearch,
    ProjectSearch,
    TeamSearch,
    AddTrackerNoteDialog,
  },
  filters: {
    getStatus(val) {
      const result = val.toLowerCase()
      return [result[0].toUpperCase(), result.slice(1)].join('')
    },
  },
  data() {
    return {
      completeDialogVisible: false,
      loading: false,
      downloadLoading: false,
      submitting: false,
      call_status: [
        {
          value: 'SELECTED',
          label: 'Selected',
          type: 'info',
        },
        {
          value: 'SCHEDULED',
          label: 'Arranged - Pending Approval',
          type: 'warning',
        },
        {
          value: 'ARRANGED',
          label: 'Arranged - Approval',
          type: 'primary',
        },
        {
          value: 'COMPLETED',
          label: 'Completed',
          type: 'success',
        },
        {
          value: 'FAILED',
          label: 'Failed',
          type: 'danger',
        },
        {
          value: 'CANCELED',
          label: 'Canceled',
          type: 'info',
        },
      ],
      tableData: [],
      total: 0,
      date_range: [moment().startOf('month').toISOString(), moment().endOf('day').toISOString()],
      searchQuery: {
        status_in: [],
        client_ids: undefined,
        advisor_ids: undefined,
        project_ids: undefined,
        project_sub_type_in: [],
        id: undefined,
        lead_id: undefined,
        support_id: undefined,
        team_id: undefined,
        consultation_date: undefined,
        'project.outsource_client_name_contains': undefined,
        page: 1,
        size: 100,
        sort: 'start_time desc',
      },
      completeForm: {},
    }
  },
  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
      isSGDB: state => state.app.isSGDB,
    }),
    ...mapGetters([
      'project_options',
      'roles',
    ]),
    hasPermission() {
      return this.roles.some(item => ['Admin', 'UpdateCallTracker'].includes(item.role.name))
    },
    downloadDisabled() {
      return !this.roles.some(item => ['CallTrackerDownload', 'Admin'].includes(item.role.name)) || this.total < 1
    },
  },
  mounted() {
    this.searchQuery.team_id = this.isSGDB ? undefined : 4
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)
    if (this.$route.query.id) this.date_range = []
    this.getList()
    this.resetCompleteForm()
  },
  methods: {
    search() {
      this.searchQuery.page = 1
      this.getList()
    },
    getList() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      this.loading = true
      const params = this.formatParams()
      return ReportAPI.getCallTrackerList(params)
        .then(data => {
          this.tableData = data.list.map(item => {
            item.client_hours_editing = false
            item.client_hours_loading = false
            item.client_hours_input = +item.client_hours
            item.is_custom_sourced_loading = false
            item.status_loading = false
            item.advisor = null
            item.project = null
            item.lead_loading = false
            item.support_loading = false
            item.support_members = []
            return item
          })
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    actionComplete(row) {
      this.completeForm.marked_client_hour = row.client_hours
      this.completeForm.marked_billable_hour = row.billable_hours
      this.completeForm.project_id = row.project_id
      this.completeForm.id = row.task_id
      this.completeDialogVisible = true
    },

    completeTask() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitting = true
          return ProjectAPI.updateTask(this.completeForm)
            .then(() => {
              this.getList()
              this.resetCompleteForm()
            })
            .finally(() => {
              this.submitting = false
              this.completeDialogVisible = false
            })
        } else {
          return false
        }
      })
    },

    resetCompleteForm() {
      this.completeForm = {
        general_status: 'COMPLETED',
        project_id: undefined,
        id: undefined,
        is_marked_complete: true,
        marked_client_hour: 0,
        marked_billable_hour: 0,
      }
    },

    handleSortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.prop + order
        this.getList()
      }
    },

    actionEditClientHours(row, index) {
      row.client_hours_editing = true
      this.$nextTick(() => {
        this.$refs[`clientHour${index}`]?.focus()
      })
    },

    updateClientHours(val, row) {
      if (+row.client_hours === val) {
        row.client_hours_editing = false
        return
      }

      const params = {
        id: row.task_id,
        project_id: row.project_id,
        call_tracker_client_hours: val,
      }
      row.client_hours_loading = true
      return ProjectAPI.updateTask(params)
        .then(() => {
          row.client_hours = val ? val + '.000' : ''
          row.client_hours_editing = false
        })
        .finally(() => {
          row.client_hours_loading = false
        })
    },

    updateCustomSourced(val, row) {
      if (row.is_custom_sourced === val) return

      const params = {
        id: row.task_id,
        project_id: row.project_id,
        custom_sourced_override: val,
      }
      row.is_custom_sourced_loading = true
      return ProjectAPI.updateTask(params)
        .then(() => {
          row.is_custom_sourced = val
        })
        .finally(() => {
          row.is_custom_sourced_loading = false
        })
    },

    async actionGetMembersAndAdvisor(row, type) {
      if (row.advisor || row[`${type}_loading`]) return
      try {
        row[`${type}_loading`] = true
        const res = await Promise.all(
          [ProjectAPI.getProject(row.project_id, 'members.user'), ConsultantAPI.getConsultant(row.advisor_id)])
        row.advisor = res[1]
        row.project = res[0]
        row.support_members = res[0].members.reduce((arr, { user }) => {
          if (!arr.map(({ id }) => id).includes(user.id)) {
            arr.push({
              name: user.name,
              id: user.id,
              disabled: checkSourced(row),
            })
          }
          return arr
        }, [])
      } finally {
        row[`${type}_loading`] = false
      }

      function checkSourced(row) {
        let isAfterOneMonth = false
        const has_completed = row.status === 'COMPLETED'
        if (has_completed && row.advisor.sourced_by_id > 0) {
          const last_month = moment(row.consultation_date).subtract(1, 'months').utc()
          isAfterOneMonth = moment(row.advisor.sourced_at).isAfter(last_month)
        }
        return has_completed && isAfterOneMonth
      }
    },

    updateSupportMember(val, row, type) {
      const params = {
        id: row.task_id,
        project_id: row.project_id,
      }
      params[`${type}_uid`] = val.id
      row[`${type}_loading`] = true
      return ProjectAPI.updateTask(params)
        .then(() => {
          row[type] = val.name
        })
        .finally(() => {
          row[`${type}_loading`] = false
        })
    },

    openAddNoteDialog(row) {
      this.$refs['addTrackerNoteDialog']?.show(row)
    },

    getTaskRouterInfo(item) {
      if (!item.task_id) return ''
      if (item.angle_id && item.project_id) {
        let router_name = 'ProjectTasks'
        if (item.project_sub_type === 'Investor Call') router_name = 'ProjectClientTasksSST'
        if (item.client_id === this.McKinseyId) router_name = 'ProjectClientTasksMcK'
        return {
          name: router_name,
          params: { project_id: item.project_id },
          query: { task_id: item.task_id, angle_id: item.angle_id },
        }
      } else {
        return {
          name: 'ProjectLeads',
          params: { project_id: item.project_id },
          query: { task_id: item.task_id },
        }
      }
    },

    formatParams() {
      const params = Object.assign({}, this.searchQuery)
      params.client_ids = params.client_ids.join() || undefined
      params.advisor_ids = params.advisor_ids.join() || undefined
      params.project_ids = params.project_ids.join() || undefined
      params.project_sub_type_in = params.project_sub_type_in.join() || undefined
      params.start_time_gte = this.date_range?.length ? this.date_range[0] : undefined
      params.start_time_lte = this.date_range?.length ? this.date_range[1] : undefined

      const call_tracker_status = ['FAILED', 'CANCELED']
      params.general_status_in = params.status_in.filter(item => !call_tracker_status.includes(item)).join() || undefined
      params.call_tracker_status_in = params.status_in.filter(item => call_tracker_status.includes(item)).join() || undefined
      delete params.status_in

      return params
    },

    downloadData() {
      const params = this.formatParams()
      delete params.size
      delete params.page
      delete params.sort

      this.downloadLoading = true

      return ReportAPI.downloadCallTrackerData(params)
        .finally(() => {
          this.downloadLoading = false
        })
    },

    actionChangeStatus(val, row) {
      const params = {
        id: row.task_id,
        project_id: row.project_id,
        call_tracker_status: val,
      }
      row.status_loading = true
      return ProjectAPI.updateTask(params)
        .then(this.getList)
        .finally(() => {
          row.status_loading = false
        })
    },
  },
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";

.el-tag + .el-tag {
  margin-left: $gap-sm/2;
}

.notes-content {
  max-width: calc(100vw - 250px);
  max-height: calc(100vh - 100px);
  overflow: auto;
  padding-right: 8px;
}

.notes-content-item + .notes-content-item {
  margin-top: 5px;
}

.add-note-icon {
  display: none;
}

.el-table__row:hover {
  .add-note-icon {
    display: block;
  }
}
</style>
