<template>
  <el-dialog
    title="Add Note"
    :visible.sync="dialogVisible"
  >
    <el-form>
      <el-form-item style="margin-bottom: 0;">
        <el-input
          v-model="form.note"
          type="textarea"
          placeholder="Note"
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="dialogVisible = false">Close</el-button>
      <el-button type="primary" :disabled="!form.note" :loading="submitting" @click="handleActionApproval">
        Confirm
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import ReportAPI from '@/api/report'

export default {
  name: 'AddTrackerNoteDialog',
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      data: null,
      form: {
        note: '',
      },
    }
  },
  methods: {
    show(data) {
      this.form.note = ''
      this.data = data
      this.dialogVisible = true
    },
    handleActionApproval() {
      const params = {
        task_id: this.data.task_id,
        ...this.form,
      }
      this.submitting = true
      return ReportAPI.saveCallTrackerNote(params)
        .then(() => {
          this.$message.success('success')
          this.$emit('done')
          this.dialogVisible = false
        })
        .finally(() => {
          this.submitting = false
        })
    },
  },
}
</script>
