<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="handleSearch" />
      <el-date-picker
        v-model="searchQuery.date"
        type="month"
        format="MM/yyyy"
        value-format="yyyy-MM"
        placeholder="Date"
        :clearable="false"
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <team-search
        v-model="searchQuery.team_id"
        class="filter-item w-200px"
        @change="handleSearch"
      />
    </div>

    <el-descriptions
      v-if="searchQuery.team_id"
      v-loading="loading"
      :column="4"
      size="mini"
      border
      style="max-width: 1000px;"
    >
      <el-descriptions-item label="Target" :span="4">
        {{ targetDash.target_client_hours }} CH
      </el-descriptions-item>
      <el-descriptions-item label="Total Support CH">
        {{ targetDash.total_client_hours }}
      </el-descriptions-item>
      <el-descriptions-item label="100% Run Rate">
        {{ targetDash.target_client_hours_run_rate }}
      </el-descriptions-item>
      <el-descriptions-item label="Target CH Per Day">
        {{ targetDash.target_client_hours_per_day }}
      </el-descriptions-item>
      <el-descriptions-item label="Days Completed">
        {{ targetDash.work_days_complete }}
      </el-descriptions-item>
      <el-descriptions-item label="Percentage of Target">
        {{ targetDash.percent_client_hours_target_complete }}
      </el-descriptions-item>
      <el-descriptions-item label="Projected @ Current Run Rate">
        {{ targetDash.projected_client_hours_at_current_rate }}
      </el-descriptions-item>
      <el-descriptions-item label="CH Per Day">
        {{ targetDash.client_hours_per_day }}
      </el-descriptions-item>
      <el-descriptions-item label="Days Remaining">
        {{ targetDash.work_days_in_month - targetDash.work_days_complete }}
      </el-descriptions-item>
      <el-descriptions-item label="Percentage of Month Complete">
        {{ targetDash.percent_month_complete }}
      </el-descriptions-item>
      <el-descriptions-item label="Run Rate">
        {{ targetDash.client_hours_run_rate }}
      </el-descriptions-item>
      <el-descriptions-item label="Pipeline Rest of Month">
        {{ targetDash.pipeline }}
      </el-descriptions-item>
      <el-descriptions-item label="Total Business Days">
        {{ targetDash.work_days_in_month }}
      </el-descriptions-item>
    </el-descriptions>

    <el-divider>Individual Performance Summary</el-divider>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
    >
      <el-table-column
        label="User"
        prop="name"
      />
      <el-table-column
        label="Team"
        prop="team.name"
      >
      </el-table-column>
      <el-table-column
        label="Supp Calls"
        prop="support_call_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.support_call_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.support_call_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.support_call_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.support_call_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Supp CH"
        prop="support_client_hours"
        width="90"
      />
      <el-table-column
        label="Ld Calls"
        prop="lead_call_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.lead_call_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.lead_call_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.lead_call_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.lead_call_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Ld CH"
        prop="lead_client_hours"
        width="90"
      />
      <el-table-column
        label="Supp Pipeline"
        prop="support_pipeline_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.support_pipeline_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.support_pipeline_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.support_pipeline_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.support_pipeline_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Lead Pipeline"
        prop="lead_pipeline_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.lead_pipeline_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.lead_pipeline_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.lead_pipeline_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.lead_pipeline_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="CS Support"
        prop="support_custom_sourced_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.support_custom_sourced_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.support_custom_sourced_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.support_custom_sourced_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.support_custom_sourced_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="CS Lead"
        prop="lead_custom_sourced_count"
        width="90"
      >
        <template v-slot="{row}">
          <el-tooltip v-if="row.lead_custom_sourced_count" effect="light" placement="top">
            <div slot="content" class="calls-tooltip-content">
              <el-link
                v-for="item in row.lead_custom_sourced_ids"
                :key="item.task_id"
                type="primary"
                :underline="false"
                icon="el-icon-link"
                @click="routeToCallTracker(item.task_id)"
              >
                {{ item.advisor_name }}
              </el-link>
            </div>
            <el-link type="primary" :underline="false">
              {{ row.lead_custom_sourced_count }}
            </el-link>
          </el-tooltip>
          <template v-else>{{ row.lead_custom_sourced_count }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="Run Rate"
        prop="run_rate"
        width="90"
      />
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getDashboard"
    />
  </div>
</template>

<script>
import ReportAPI from '@/api/report'
import { mapState } from 'vuex'
import moment from 'moment-timezone'
import Pagination from '@/components/Pagination/index.vue'
import RefreshButton from '@/components/RefreshButton/index.vue'
import RouterTools from '@/utils/tool-router'
import TeamSearch from '@/components/SelectRemoteSearch/UserTeamSearch/index.vue'

export default {
  name: 'CallTrackerDashboard',
  components: { TeamSearch, Pagination, RefreshButton },
  data() {
    return {
      loading: true,
      total: 0,
      searchQuery: {
        page: 1,
        size: 100,
        team_id: undefined,
        date: moment().toISOString(),
      },
      tableData: [],
      targetDash: {},
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  async mounted() {
    this.searchQuery.team_id = this.isSGDB ? 13 : 4
    // this.searchQuery.team_id = this.options.team_list.find(team => team.name.toLowerCase().includes('us'))?.id
    RouterTools.resolveRouteQuery(this.$route, this.searchQuery)
    await this.getDashboard()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      this.getDashboard()
    },
    getDashboard() {
      const params = {
        ...this.searchQuery,
        date: moment(this.searchQuery.date).endOf('month').toISOString(),
        team_id: this.searchQuery.team_id || undefined,
      }
      RouterTools.saveQueryToRouter(this.searchQuery)
      this.loading = true
      return ReportAPI.getCallTrackerDashboard(params)
        .then(data => {
          this.total = data.employee_count
          this.tableData = data.employee_summary
          this.$set(this, 'targetDash', data.target_dash)
        })
        .finally(() => {
          this.loading = false
        })
    },
    routeToCallTracker(id) {
      console.log(id)
      this.$router.push({ name: 'CallTrackerIndex', query: { id, team_id: undefined } })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-link + .el-link {
  margin-left: 8px;
}

.calls-tooltip-content {
  max-width: 500px;
}
</style>
