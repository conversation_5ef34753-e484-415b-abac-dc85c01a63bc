<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="handleSearch" />
      <el-date-picker
        v-model="searchQuery.month"
        type="month"
        format="MM/yyyy"
        value-format="yyyy-MM"
        placeholder="Date"
        :clearable="false"
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <user-search
        v-model="searchQuery.user_id"
        placeholder="User"
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <el-button
        type="primary"
        class="filter-item"
        @click="handleOpenDialog()"
      >
        Set User Target
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
    >
      <el-table-column
        label="Month"
        prop="month"
      />
      <el-table-column
        label="User"
        prop="user.name"
      />
      <el-table-column
        label="Client Hours Target"
        prop="client_hours_target"
      />
      <el-table-column
        label="Action"
        width="80px"
      >
        <template v-slot="{row}">
          <el-link type="primary" icon="el-icon-edit" @click="actionEditCallsTarget(row)" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getTargetsList"
    />

    <el-dialog
      title="Set User Target"
      :visible.sync="dialogVisible"
      width="365px"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="125px"
      >
        <el-form-item label="Month" prop="month" required>
          <el-date-picker
            v-model="form.month"
            type="month"
            format="MM/yyyy"
            value-format="yyyy-MM"
            placeholder="Date"
            :clearable="false"
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="User" prop="user_id" required>
          <user-search
            v-model="form.user_id"
            placeholder="User"
            class="w-200px"
          />
        </el-form-item>
        <el-form-item label="Target Client Hour" prop="client_hours_target" required>
          <el-input-number v-model="form.client_hours_target" :min="0" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitting" @click="actionSetTarget">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ReportAPI from '@/api/report'
import moment from 'moment-timezone'
import RefreshButton from '@/components/RefreshButton/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch/index.vue'

export default {
  name: 'CallTrackerSetUserTargets',
  components: { UserSearch, Pagination, RefreshButton },
  data() {
    return {
      loading: false,
      total: 0,
      searchQuery: {
        page: 1,
        size: 50,
        user_id: undefined,
        month: moment().format('YYYY-MM'),
      },
      tableData: [],

      dialogVisible: false,
      submitting: false,
      form: this.initForm(),
    }
  },
  mounted() {
    this.getTargetsList()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      this.getTargetsList()
    },
    getTargetsList() {
      const params = {
        ...this.searchQuery,
        extra: 'user',
      }
      this.loading = true
      return ReportAPI.getUserTarget(params)
        .then(data => {
          this.total = data.count
          this.tableData = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    initForm() {
      return {
        id: undefined,
        month: moment().format('YYYY-MM'),
        client_hours_target: undefined,
        user_id: undefined,
      }
    },
    handleOpenDialog(val) {
      this.dialogVisible = true
      this.form = val || this.initForm()
      this.$refs['form']?.clearValidate()
    },
    actionEditCallsTarget(row) {
      const form = {
        id: row.id,
        client_hours_target: row.client_hours_target,
        month: row.month,
        user_id: row.user_id,
      }
      this.handleOpenDialog(form)
    },
    actionSetTarget() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = { ...this.form }
          this.submitting = true
          return ReportAPI.updateUserTarget(params)
            .then(() => {
              this.getTargetsList()
              this.dialogVisible = false
            })
            .finally(() => {
              this.submitting = false
            })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
