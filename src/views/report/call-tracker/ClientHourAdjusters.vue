<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="handleSearch" />
      <client-search
        v-model="searchQuery.client_id"
        clearable
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <el-select
        v-model="searchQuery.type"
        filterable
        clearable
        placeholder="Select Type"
        class="filter-item w-200px"
        @change="handleSearch"
      >
        <el-option
          v-for="item in options.type"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <el-button
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="handleOpenDialog()"
      >
        Create Adjuster
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
    >
      <el-table-column
        label="ID"
        prop="id"
        width="80"
      />
      <el-table-column
        label="Client ID"
        prop="client_id"
        width="80"
      >
        <template v-slot="{row}">
          <span v-if="row.client_id">{{ row.client_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Client Name"
      >
        <template v-slot="{row}">
          <router-link-client :data="row.client" />
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
        prop="type"
      />
      <el-table-column
        label="Adjuster"
        prop="adjuster"
      />
      <el-table-column
        label="Update Date"
        prop="update_at"
      >
        <template v-slot="{row}">
          {{ row.update_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column
        label="Action"
        width="80px"
      >
        <template v-slot="{row}">
          <el-link type="primary" icon="el-icon-edit" @click="actionEditAdjuster(row)" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getAdjustersList"
    />

    <el-dialog
      :title="form.id ? 'Edit Adjuster' : 'Create Adjuster'"
      :visible.sync="dialogVisible"
      width="340px"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="70px"
      >
        <el-form-item label="Type" prop="type">
          {{ form.type }}
        </el-form-item>
        <el-form-item v-if="form.type === 'CLIENT_ADJUSTER'" label="Client" prop="client_id">
          <client-search
            v-if="!form.id"
            v-model="form.client_id"
            class="w-200px"
          />
          <span v-else>{{ form.client_id }}</span>
        </el-form-item>
        <el-form-item label="Adjuster" prop="adjuster">
          <el-input-number v-model="form.adjuster" :step="0.1" :min="0" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitting" @click="actionSetTarget">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ReportAPI from '@/api/report'
import RefreshButton from '@/components/RefreshButton/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch/index.vue'
import RouterLinkClient from '@/components/RouterLink/Client.vue'

export default {
  name: 'CallTrackerClientHourAdjusters',
  components: { RouterLinkClient, ClientSearch, Pagination, RefreshButton },
  data() {
    return {
      loading: false,
      total: 0,
      searchQuery: {
        page: 1,
        size: 50,
        client_id: undefined,
        type: undefined,
        extra: 'client',
      },
      options: {
        type: [
          'CLIENT_ADJUSTER',
          'CUSTOM_SOURCED_ADJUSTER',
          'INVESTOR_CALL_ADJUSTER',
          'LEAD_AND_SUPPORT_ADJUSTER',
        ],
      },
      tableData: [],

      dialogVisible: false,
      submitting: false,
      form: this.initForm(),
    }
  },
  mounted() {
    this.getAdjustersList()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      this.getAdjustersList()
    },
    getAdjustersList() {
      const params = {
        ...this.searchQuery,
        type: this.searchQuery.type || undefined,
        client_id: this.searchQuery.client_id || undefined,
      }
      this.loading = true
      return ReportAPI.getClientHourAdjuster(params)
        .then(data => {
          this.total = data.count
          this.tableData = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    initForm() {
      return {
        id: undefined,
        type: 'CLIENT_ADJUSTER',
        client_id: undefined,
        adjuster: 1,
      }
    },
    handleOpenDialog(val) {
      this.dialogVisible = true
      this.form = val || this.initForm()
      this.$refs['form']?.clearValidate()
    },
    actionEditAdjuster(row) {
      const form = {
        id: row.id,
        type: row.type,
        client_id: row.client_id,
        adjuster: row.adjuster,
      }
      this.handleOpenDialog(form)
    },
    actionSetTarget() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = { ...this.form }
          this.submitting = true
          return ReportAPI.updateClientHourAdjuster(params)
            .then(() => {
              this.getAdjustersList()
              this.dialogVisible = false
            })
            .finally(() => {
              this.submitting = false
            })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
