<template>
  <div class="app-container">
    <div class="filter-container flex">
      <div class="flex-1">
        <refresh-button class="filter-item" @action="loadData" />
        <el-switch
          v-model="form.type"
          class="filter-item"
          active-text="Advisor"
          inactive-text="Contact"
          active-value="advisor"
          inactive-value="contact"
          inactive-color="#409EFF"
          @change="actionSwitchMode" />
      </div>
      <div>
        <el-button class="filter-item" icon="el-icon-download" @click="actionDownload">Download</el-button>
      </div>
    </div>

    <div v-if="isModeChanging">
      <loading-status :status="isLoading" />
    </div>
    <div v-else>
      <!-- Contact Table -->
      <el-table
        v-if="contactMode"
        v-loading="isLoading"
        border
        stripe
        row-class-name="cursor-pointer"
        :data="tableData"
        @row-click="actionShowDetail">
        <el-table-column
          prop="id"
          label="ID"
          min-width="40" />
        <el-table-column
          prop="create_at"
          label="Date"
          width="120">
          <template slot-scope="scope">
            {{scope.row.create_at | momentFormat('long')}}
          </template>
        </el-table-column>
        <el-table-column
          prop="client_contact_id"
          label="Client Contact"
          min-width="60">
          <template v-if="scope.row.task" slot-scope="scope">
            <div class="no-wrap">{{scope.row.task.client_contact.name}}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="Project"
          min-width="120">
          <template v-if="scope.row.task" slot-scope="scope">
            <router-link-project :data="scope.row.task.project" />
          </template>
        </el-table-column>
        <el-table-column
          prop="communication_rate"
          label="Communication"
          min-width="60" />
        <el-table-column
          prop="expertise_rate"
          label="Expertise"
          min-width="60" />
        <el-table-column
          prop="professionalism_rate"
          label="Professionalism"
          min-width="60" />
        <el-table-column
          prop="comment"
          label="Comment"
          min-width="60">
          <template slot-scope="scope">
            <div class="text-nowrap-ellipsis">{{scope.row.comment}}</div>
          </template>
        </el-table-column>
      </el-table>

      <!-- Advisor Table -->
      <el-table
        v-if="advisorMode"
        v-loading="isLoading"
        border
        stripe
        row-class-name="cursor-pointer"
        :data="tableData"
        @row-click="actionShowDetail">
        <el-table-column
          prop="id"
          label="ID"
          min-width="40" />
        <el-table-column
          prop="create_at"
          label="Date"
          width="120">
          <template slot-scope="scope">
            {{scope.row.create_at | momentFormat('long')}}
          </template>
        </el-table-column>
        <el-table-column
          label="Project"
          min-width="120">
          <template v-if="scope.row.task" slot-scope="scope">
            <router-link-project :data="scope.row.task.project" />
          </template>
        </el-table-column>
        <el-table-column
          prop="task_id"
          label="Task ID"
          min-width="60" />
        <el-table-column
          prop="advisor_id"
          label="Advisor"
          min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.task" class="no-wrap">
              {{scope.row.task.advisor['name_prefix'] || ''}}
              {{scope.row.task.advisor['full_name']}}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="client_contact_id"
          label="Client Contact"
          min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.task" class="no-wrap">
              {{scope.row.task.client_contact.name}}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="overall_grade"
          label="Overall Grade"
          min-width="60" />
        <el-table-column
          prop="compensation_rank"
          label="Compensation"
          min-width="60" />
        <el-table-column
          prop="exposure_rank"
          label="Exposure"
          min-width="60" />
        <el-table-column
          prop="knowledge_rank"
          label="Knowledge"
          min-width="60" />
        <el-table-column
          prop="with_other_expert_network"
          label="Other Network"
          min-width="120">
          <template slot-scope="scope">
            {{scope.row['with_other_expert_network'] | yesOrNo}}
          </template>
        </el-table-column>
        <el-table-column
          prop="proposed_improvement_area"
          label="Improvement"
          min-width="60">
          <template slot-scope="scope">
            <div class="text-nowrap-ellipsis">{{scope.row['proposed_improvement_area']}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="improvement_suggestion"
          label="Suggestion"
          min-width="60">
          <template slot-scope="scope">
            <div class="text-nowrap-ellipsis">{{scope.row['improvement_suggestion']}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="further_recommendation"
          label="Recommendation"
          min-width="60">
          <template slot-scope="scope">
            <div class="text-nowrap-ellipsis">{{scope.row['further_recommendation']}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="additional_feedback"
          label="Additional Feedback"
          min-width="60">
          <template slot-scope="scope">
            <div class="text-nowrap-ellipsis">{{scope.row['additional_feedback']}}</div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="form.page"
        :limit.sync="form.size"
        @pagination="loadData" />
    </div>

    <detail-dialog ref="detailDialog" />
  </div>
</template>

<script>
import { AppHttpService } from '@/utils/request'
import Pagination from '@/components/Pagination/index'
import DetailDialog from '@/views/report/feedback/DetailDialog'
import { downloadDocument } from '@/utils/tool'
import LoadingStatus from '@/components/LoadingStatus/index'
import RouterLinkProject from '@/components/RouterLink/Project'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'

export default {
  name: 'Feedback',
  components: { RefreshButton, RouterLinkProject, LoadingStatus, DetailDialog, Pagination },
  data() {
    return {
      isLoading: false,
      isModeChanging: false,
      form: {
        type: 'advisor', // contact
        extra: 'task,task.advisor,task.project,task.client_contact',
        size: 20,
        page: 1,
      },
      tableData: [],
      total: NaN,
    }
  },
  computed: {
    advisorMode() {
      return this.form.type === 'advisor'
    },
    contactMode() {
      return this.form.type === 'contact'
    },
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.form)
    return this.loadData()
  },
  methods: {
    actionSwitchMode(mode) {
      this.isModeChanging = true

      return this.loadData(mode)
        .finally(() => {
          this.isModeChanging = false
        })
    },
    loadData() {
      RouteTools.saveQueryToRouter(this.form)

      const api = this.form.type === 'advisor' ? '/advisor_feedbacks' : '/contact_feedbacks'

      this.isLoading = true

      return AppHttpService({
        url: api,
        method: 'get',
        params: this.form,
      })
        .then(data => {
          this.tableData = data['list']
          this.total = data['count']
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    actionShowDetail(row) {
      return this.$refs.detailDialog.show(row, this.form.type)
    },
    actionDownload() {
      const api = this.form.type === 'advisor' ? '/download/advisor_feedbacks' : '/download/contact_feedbacks'
      return AppHttpService({
        url: api,
        method: 'post',
        responseType: 'blob',
      })
        .then(data => {
          return downloadDocument(data)
        })
    },
  },
}
</script>
