<template>
  <el-dialog
    title="Feedback Detail"
    :visible.sync="dialogVisible">

    <!-- Contact Data -->
    <el-form v-if="contactMode" label-width="180px">
      <el-form-item label="ID">
        <strong>Feedback ID:</strong> {{data.id}}
        <strong>Task ID:</strong> {{data.task_id}}
        <strong>Advisor ID:</strong> {{data.advisor_id}}
      </el-form-item>
      <el-form-item label="Date">
        {{data.create_ate| momentFormat('long')}}
      </el-form-item>
      <el-form-item label="Client Contact">
        {{data.task.client_contact.name}}
      </el-form-item>
      <el-form-item label="Project">
        <router-link-project :data="data.task.project" />
      </el-form-item>
      <el-divider />
      <el-form-item label="Communication">
        {{data['communication_rate']}}
      </el-form-item>
      <el-form-item label="Expertise">
        {{data['expertise_rate']}}
      </el-form-item>
      <el-form-item label="Professionalism">
        {{data['professionalism_rate']}}
      </el-form-item>
      <el-form-item label="Comment">
        {{data.comment || '-'}}
      </el-form-item>
    </el-form>

    <!-- Advisor Data -->
    <el-form v-if="advisorMode" label-width="180px">
      <el-form-item label="ID">
        {{data.id}}
      </el-form-item>
      <el-form-item label="Date">
        {{data.create_at | momentFormat('long')}}
      </el-form-item>
      <el-form-item label="Project">
        <router-link-project :data="data.task.project" />
      </el-form-item>
      <el-form-item label="Task">
        {{data.task_id}}
      </el-form-item>
      <el-form-item label="Advisor">
        <router-link-advisor :data="data.task.advisor" />
      </el-form-item>
      <el-form-item label="Client Contact">
        {{data.task.client_contact.name}}
      </el-form-item>
      <el-divider />
      <el-form-item label="Overall Grade">
        {{data['overall_grade']}}
      </el-form-item>
      <el-form-item label="Compensation Rank">
        {{data['compensation_rank']}}
      </el-form-item>
      <el-form-item label="Exposure Rank">
        {{data['exposure_rank']}}
      </el-form-item>
      <el-form-item label="Knowledge Rank">
        {{data['knowledge_rank']}}
      </el-form-item>
      <el-form-item label="Other Network">
        {{data['with_other_expert_network'] | yesOrNo}}
      </el-form-item>
      <el-form-item label="Improvement">
        {{data['proposed_improvement_area'] || '-'}}
      </el-form-item>
      <el-form-item label="Suggestion">
        {{data['improvement_suggestion'] || '-'}}
      </el-form-item>
      <el-form-item label="Recommendation">
        {{data['further_recommendation'] || '-'}}
      </el-form-item>
      <el-form-item label="Additional Feedback">
        {{data['additional_feedback'] || '-'}}
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Close</el-button>
    </div>
  </el-dialog>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'

export default {
  name: 'DetailDialog',
  components: { RouterLinkAdvisor, RouterLinkProject },
  data() {
    return {
      dialogVisible: false,
      data: {},
      type: undefined,
    }
  },
  computed: {
    advisorMode() {
      return this.type === 'advisor'
    },
    contactMode() {
      return this.type === 'contact'
    },
  },
  methods: {
    show(data, type) {
      this.data = data
      this.type = type
      this.dialogVisible = true
    },
  },
}
</script>
