<!--suppress JSUnresolvedReference, HtmlDeprecatedAttribute -->
<template>
  <div class="app-container">
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div>
        <refresh-button class="filter-item" @action="searchList" />
        <user-search
          v-model="searchQuery.ids"
          class="inline-block filter-item w-150px"
          placeholder="user"
          clearable
          multiple />
        <el-select
          v-model="searchQuery.roles"
          clearable
          multiple
          class="w-200px filter-item"
          placeholder="Role">
          <el-option
            v-for="item in role_list"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <el-select
          v-model="searchQuery.team_id"
          class="filter-item w-200px"
          placeholder="Team"
          clearable
          multiple
          remote
          filterable
          :remote-method="getTeamList"
        >
          <el-option
            v-for="item in team_list"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <el-date-picker
          v-model="searchQuery.date_range"
          type="daterange"
          class="filter-item"
          start-placeholder="Start Date"
          end-placeholder="End Date"
          :default-time="['00:00:00', '23:59:59']" />
        <el-switch
          v-model="searchQuery.require_id_list"
          class="filter-item"
          inactive-text="With IDs"
          active-color="#13ce66"
          inactive-color="#E0E0E0" />
        <el-button type="primary" class="filter-item" :disabled="loading" @click="searchList()">Search</el-button>
        <el-button type="primary" class="filter-item" :disabled="total<1" @click="downloadData()">Download</el-button>
      </div>
      <div class="tabs-stackOverflow filter-item">
        <span v-for="(value,key) in total_data" :key="key" class="ml-3">
          {{ key }}: <span class="primary">{{ value }}</span>
        </span>
      </div>
    </div>
    <el-table
      ref="advisorData"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['advisorData'])"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column
        label="Name"
        min-width="100"
        prop="name"
      >
        <template slot-scope="scope">
          <div v-if="teamManagerGreenShadow(scope.row)" style="height: 1.2rem">
            <el-tooltip effect="dark" content="Team Manager" placement="top">
              <span class="green-shadow">
                <span style="padding:5px">{{ scope.row.name }}</span>
              </span>
            </el-tooltip>
          </div>
          <span v-else>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Projects as Support"
        min-width="140"
      >
        <template slot-scope="scope">
          {{ scope.row.count_project_as_support }}
          <ids-list
            v-if="shouldShowIcon(scope.row.ids_list_project_as_support)"
            :id-array="scope.row.ids_list_project_as_support"
            :projects="map_project"
            id-type="project" />
        </template>
      </el-table-column>
      <el-table-column
        label="Projects as Manager"
        min-width="140"
        prop="count_project_as_manager"
      >
        <template slot-scope="scope">
          {{ scope.row.count_project_as_manager }}
          <ids-list
            v-if="shouldShowIcon(scope.row.ids_list_project_as_manager)"
            :id-array="scope.row.ids_list_project_as_manager"
            :projects="map_project"
            id-type="project" />
        </template>
      </el-table-column>

      <el-table-column
        label="Leads Contacted"
        min-width="135"
      >
        <template slot-scope="scope">
          {{ scope.row.count_leads_contacted }}
          <ids-list
            v-if="shouldShowIcon(scope.row.task_ids_list_leads_contacted)"
            :id-array="scope.row.task_ids_list_leads_contacted"
            :tasks="map_task"
            id-type="task" />
        </template>
      </el-table-column>

      <el-table-column
        label="Leads Sent TC"
        prop="count_leads_sent_tc"
      >
        <template slot-scope="scope">
          {{ scope.row.count_leads_sent_tc }}
          <ids-list
            v-if="shouldShowIcon(scope.row.ids_list_leads_sent_tc)"
            :id-array="scope.row.ids_list_leads_sent_tc"
            :experts="map_expert"
            id-type="expert" />
        </template>
      </el-table-column>
      <el-table-column
        label="Accept TC"
        prop="count_leads_accept_tc"
      >
        <template slot-scope="scope">
          {{ scope.row.count_leads_accept_tc }}
          <ids-list
            v-if="shouldShowIcon(scope.row.ids_list_leads_accept_tc)"
            :id-array="scope.row.ids_list_leads_accept_tc"
            :experts="map_expert"
            id-type="expert" />
        </template>
      </el-table-column>
      <el-table-column
        label="Sent TC/Accept TC"
        prop="percentage_accept_sent"
        min-width="120" />
      <el-table-column label="Advisors Consulted" align="center">
        <el-table-column
          label="Total"
          width="70"
          prop="count_advisor_consulted"
        />
        <el-table-column
          label="In-network"
        >
          <template slot-scope="scope">
            {{ scope.row.count_advisor_consulted_in_network }}
            <ids-list
              v-if="shouldShowIcon(scope.row.ids_list_advisor_consulted_in_network)"
              :id-array="scope.row.ids_list_advisor_consulted_in_network"
              :experts="map_expert"
              id-type="expert" />
          </template>
        </el-table-column>
        <el-table-column
          label="custom sourced"
        >
          <template slot-scope="scope">
            {{ scope.row.count_advisor_consulted_custom_sourced }}
            <ids-list
              v-if="shouldShowIcon(scope.row.ids_list_advisor_consulted_custom_sourced)"
              :id-array="scope.row.ids_list_advisor_consulted_custom_sourced"
              :experts="map_expert"
              id-type="expert" />
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="Consultations Completed" align="center">
        <el-table-column
          label="Total"
          width="70"
          prop="count_task_consulted"
        />
        <el-table-column
          label="In-network"
          align="center"
        >
          <el-table-column label="Calls" align="center">
            <template slot-scope="scope">
              {{ scope.row.count_task_consulted_in_network }}
              <ids-list
                v-if="shouldShowIcon(scope.row.task_ids_list_consulted_in_network)"
                :id-array="scope.row.task_ids_list_consulted_in_network"
                :tasks="map_task"
                id-type="task" />
            </template>
          </el-table-column>
          <el-table-column label="Hours" align="center">
            <template slot-scope="scope">
              {{ scope.row.consultation_completed_hours_in_network }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="Custom Sourced"
          align="center"
        >
          <el-table-column label="Calls" align="center">
            <template slot-scope="scope">
              {{ scope.row.count_task_consulted_custom_sourced }}
              <ids-list
                v-if="shouldShowIcon(scope.row.task_ids_list_consulted_custom_sourced)"
                :id-array="scope.row.task_ids_list_consulted_custom_sourced"
                :tasks="map_task"
                id-type="task" />
            </template>
          </el-table-column>
          <el-table-column label="Hours" align="center">
            <template slot-scope="scope">
              {{ scope.row.consultation_completed_hours_custom_sourced }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column
        label="Conversion Rate (Accept TC/Consultations Completed)"
        prop="percentage_conversion"
        min-width="140"
      />
      <el-table-column
        label="Conversion Rate (Accept TC/Leads Contacted)"
        prop="percentage_accept_tc_vs_contacted"
        min-width="100" />
      <el-table-column
        label="Avg Signed TC Rate(USD) "
        prop="avg_signed_tc_rate_usd"
      />
    </el-table>

    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
  </div>
</template>
<script>
import ReportAPI from '@/api/report'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import AuthorityAPI from '@/api/authority'
import moment from 'moment'
import userAPI from '@/api/user'
import IdsList from './IdsList'

export default {

  name: 'Index',
  components: { RefreshButton, Pagination, UserSearch, IdsList },
  data() {
    return {
      loading: false,
      tableData: [],
      searchQuery: {
        roles: [],
        page: 1,
        size: 50,
        require_id_list: false,
        ids: [],
        team_id: [],
        date_range: [moment().startOf('month').toISOString(), moment().endOf('month').toISOString()],
      },
      total: 0,
      role_list: [],
      team_list: [],
      download_ids: [],
      map_task: Map,
      map_project: Map,
      map_expert: Map,
      total_data: undefined,
    }
  },
  computed: {
    showDetail() {
      return this.searchQuery.require_id_list
    },
  },
  mounted() {
    this.getRolesList()
    this.getTeamList()
  },
  methods: {
    searchList() {
      this.searchQuery.page = 1
      this.getList()
    },

    shouldShowIcon(ids) {
      return this.showDetail && ids.length > 0
    },

    getRolesList() {
      return AuthorityAPI.getRolesList().then(data => {
        this.role_list = data
      })
    },
    getTeamList(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        page: 1,
        size: 15,
      }
      return userAPI.getUserTeamList(params).then(data => {
        this.team_list = data.list
      })
    },
    convertListToMap(list, keySelector) {
      console.log('converting')
      const map = new Map()
      // 遍历对象列表
      list.forEach(item => {
        // 使用Lambda函数从对象中获取键值
        const key = keySelector(item)
        // 检查键是否存在
        if (key !== undefined) {
          // 将键-对象的映射添加到Map中
          map.set(key, item)
        }
      })
      console.log(`map size:${map.size}`)
      return map
    },
    getList() {
      this.loading = true
      const { page, size, require_id_list } = this.searchQuery
      const params = { page, size, require_id_list }
      this.formatParams(params)

      return ReportAPI.getAdvisorData(params)
        .then(data => {
          this.tableData = data['items']
          this.total = data.count

          this.map_task = this.convertListToMap(
            data.all_tasks,
            it => it.task_id,
          )
          this.map_project = this.convertListToMap(
            data.all_projects,
            it => it.id,
          )
          this.map_expert = this.convertListToMap(
            data.all_experts,
            it => it.id,
          )

          this.total_data = {
            '# Advisor Consulted': data['total_advisor_consulted'],
            '# Leads Accept TC': data['total_leads_accept_tc'],
            '# Leads Contacted': data['total_leads_contacted'],
            '# Leads Sent TC': data['total_leads_sent_tc'],
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    downloadData() {
      const params = {}
      this.formatParams(params)
      if (this.download_ids.length > 0) params.ids = this.download_ids.join()
      return ReportAPI.downloadAdvisorData(params)
    },

    handleSelectionChange(val) {
      this.download_ids = val.map(item => item.id)
    },

    teamManagerGreenShadow(user) {
      if (this.searchQuery.team_id) {
        const team_manager_ids = this.team_list.filter(item => this.searchQuery.team_id.includes(item.id))
          .map(item => item.manager_id)
        return team_manager_ids.includes(user.id)
      } else {
        return false
      }
    },

    formatParams(params) {
      const { date_range, ids, roles, team_id } = this.searchQuery
      params.start = date_range && date_range.length ? date_range[0] : undefined
      params.end = date_range && date_range.length ? date_range[1] : undefined
      params['ids'] = ids.length > 0 ? ids.join() : undefined
      params['user_role.role_ids'] = roles.length > 0 ? roles.join() : undefined
      params['team_member.team.ids'] = team_id.length > 0 ? team_id.join() : undefined
    },

  },
}
</script>

<style scoped>
.green-shadow {
  box-shadow: 3px 3px 5px 3px #75ba75;
  border-radius: 2px;
}
</style>
