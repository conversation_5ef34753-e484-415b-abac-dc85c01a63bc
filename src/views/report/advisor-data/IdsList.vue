<template>
  <div class="inline-block">
    <el-button type="text" class="el-icon-document" @click="dialogVisible = true" />

    <el-dialog
      :append-to-body="true"
      title=""
      :visible.sync="dialogVisible"
      width="40%"
    >
      <template #title>
        {{ idType }} list
      </template>
      <div class="word-break scrollbar-narrow" style="max-height:500px;overflow: auto">
        <template v-if="idArray.length">
          <div
            v-for="id in idArray"
            :key="id"
            class="ml-normal w-150px mt-8 inline-block mb-8">
            <router-link
              v-if="idType === 'expert'"
              class="link"
              :title="getExpertDisplay(id)"
              :to="{ name:'ConsultantDetail', params: {advisor_id: id, name:'' }}">
              {{ getExpertDisplay(id) }}
            </router-link>
            <router-link
              v-if="idType === 'project'"
              class="link"
              :title="getProjectDisplay(id)"
              :to="{ name:'ProjectDetail', params: {project_id: id, name:'' }}">
              {{ getProjectDisplay(id) }}
            </router-link>
            <span v-if="idType === 'task'">
              <el-link :underline="false" type="primary" @click="linkToTask(id)">
                {{ getTaskAdvisorDisplay(id) }}
              </el-link>
            </span>
          </div>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'IdsList',
  props: {
    idArray: Array,
    idType: String,
    tasks: [Map, undefined],
    projects: [Map, undefined],
    experts: [Map, undefined],
  },
  data() {
    return { dialogVisible: false }
  },
  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
    }),
  },
  methods: {
    getTaskAdvisorDisplay(task_id) {
      const name = this.tasks?.get(task_id)?.advisor_name
      return `${name}(${task_id})`
    },
    getProjectDisplay(project_id) {
      const name = this.projects?.get(project_id)?.name
      return `${name}(${project_id})`
    },
    getExpertDisplay(expert_id) {
      const name = this.experts?.get(expert_id)?.name
      return `${name}(${expert_id})`
    },
    linkToTask(task_id) {
      const target_info = this.tasks?.get(task_id)
      if (!target_info) return
      const router_name = target_info.client_id === this.McKinseyId ? 'ProjectClientTasksMcK' : 'ProjectClientTasks'
      const routeData = this.$router.resolve({
        name: router_name,
        params: {
          project_id: target_info.project_id,
        },
        query: {
          angle_id: target_info.angle_id,
          task_id: target_info.task_id,
        },
        target: '_blank',
      })
      window.open(routeData.href, '_blank')
    },
  },
}
</script>

<style scoped lang="scss">
.word-break {
  word-break: break-word;
}

::v-deep .el-dialog__header {
  text-align: center;
}

</style>
