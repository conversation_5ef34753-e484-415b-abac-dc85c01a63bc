<template>
  <div>
    <el-form v-show="isEdit" ref="form" :model="form" :rules="rules" label-width="150px" class="form-data">
      <el-form-item label="User Name" prop="username">
        <el-input v-model="form.username" />
      </el-form-item>
      <el-form-item label="First Name" prop="family_name">
        <el-input v-model="form.family_name" />
      </el-form-item>
      <el-form-item label="Last Name" prop="given_name">
        <el-input v-model="form.given_name" />
      </el-form-item>
      <el-form-item label="Email" prop="email">
        <el-input v-model="form.email" />
      </el-form-item>
      <el-form-item label="Phone" prop="phone_number">
        <el-input v-model="form.phone_number" />
      </el-form-item>
      <el-form-item label="Position" prop="position">
        <el-select
            v-model="form.position_id"
            filterable>
          <el-option
              v-for="item in options_position"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="Avatar">
         <el-upload
          class="avatar-uploader"
          accept=".png, .jpg, .jpeg"
          :action="upload_url"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload">
          <div slot="tip">Click to upload</div>
          <div v-loading="avatar_loading" class="avatar">
            <el-avatar v-if="form.picture" shape="square" :size="80" fit="contain" :src="form.picture" />
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="Signature Role" prop="signature">
        <tinymce
          ref="signature_content"
          v-model="form.signature"
          :toolbar="toolbar"
          insert-logo
          p-origin-margin />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-check" :loading="save_loading" @click="onSave">Save</el-button>
        <el-button type="text" @click="onCancel">Cancel</el-button>
      </el-form-item>
    </el-form>

    <el-form v-show="!isEdit" label-width="150px" class="form-data">
      <el-form-item label="User Name">{{ form.username }}</el-form-item>
      <el-form-item label="First Name">{{ form.family_name }}</el-form-item>
      <el-form-item label="Last Name">{{ form.given_name }}</el-form-item>
      <el-form-item label="Email">{{ form.email }}</el-form-item>
      <el-form-item label="Phone">
        <a :href="'tel:'+form.phone_number">{{ form.phone_number }}</a>
      </el-form-item>
      <el-form-item label="Position">
        <span v-if="form.position">{{ form.position.name }}</span>
      </el-form-item>
      <el-form-item label="Avatar">
        <el-avatar v-if="form.picture" shape="square" :size="80" fit="contain" :src="form.picture" />
      </el-form-item>
      <el-form-item label="Signature Role">
        <div v-html="$sanitizeHtml(form.signature)" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-edit" @click="editProfile">Edit</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import API from '@/api/user'
import Tinymce from '@/components/Tinymce'
import { mapGetters } from 'vuex'

export default {
  name: 'Profile',
  components: { Tinymce },
  props: {
    'data': Object,
  },
  data() {
    return {
      avatar_loading: false,
      save_loading: false,
      isEdit: false,
      options_position: [],
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      form: {
        username: '',
        given_name: '',
        family_name: '',
        email: '',
        phone_number: '',
        position_id: '',
        position: '',
        picture: '',
        signature: '',
      },
      initForm: null,
      rules: {
        username: [{ required: true, trigger: 'change' }],
        given_name: [{ required: true, trigger: 'change' }],
        family_name: [{ required: true, trigger: 'change' }],
        email: [{ required: true, trigger: 'change' }, { type: 'email', trigger: 'change' }],
        // phone_number: [{ type: 'number', trigger: 'change' }]
      },
      toolbar: ['undo redo | fontselect fontsizeselect forecolor | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
    }
  },
  computed: {
    ...mapGetters(['uid']),
  },
  watch: {
    'data': {
      handler(val) {
        this.getUserProfile(val)
      },
      immediate: true,
    },
  },
  methods: {
    getUserProfile(data) {
      if (!data) return
      for (const key in this.form) {
        this.form[key] = data[key]
      }
      this.initForm = JSON.parse(JSON.stringify(this.form))
    },
    updateUserProfile() {
      this.save_loading = true
      this.form.signature = this.form.signature.split('<p>').join('<div>').split('</p>').join('</div>')
      return API.updateUserProfile(this.uid, this.form).then(data => {
        this.initForm = JSON.parse(JSON.stringify(this.form))
        this.isEdit = false
        this.$store.commit('user/SET_NAME', data.name)
        this.$message({
          message: 'Update profile successfully',
          type: 'success',
        })
      }).finally(() => {
        this.$emit('reload')
        this.$refs['signature_content'].setContent('')
        this.save_loading = false
      })
    },

    editProfile() {
      this.$refs['signature_content'].setContent(this.form.signature)
      this.getUserPosition()
      this.isEdit = true
    },

    getUserPosition() {
      return API.getUserPositionList()
        .then(data => {
          this.options_position = data['list']
        })
    },
    handleAvatarSuccess(response) {
      if (response.msg === 'ok') {
        this.$nextTick(() => {
          this.form.picture = response.data.url
          // this.$set(this.form, 'picture', response.data.url)
          this.avatar_loading = false
        })
        this.form.picture = response.data.url
      } else {
        this.$message(response.message)
      }
    },
    beforeAvatarUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          this.form.picture = ''
          this.avatar_loading = true
          return resolve(true)
        }
      })
    },
    onSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.updateUserProfile()
        } else {
          return false
        }
      })
    },
    onCancel() {
      Object.assign(this.form, this.initForm)
      this.isEdit = false
    },
  },
}
</script>

<style scoped lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.avatar {
  width: 80px;
  height: 80px;
  display: block;
  border:1px dashed #d9d9d9;
  background-color: #fbfdff;
}
</style>
