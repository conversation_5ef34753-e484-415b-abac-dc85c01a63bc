<template>
  <div v-loading="loading" class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.name"
        :name="item.name">
        <template v-slot:label>
          <el-icon :name="item.icon" class="mr-8" />
          {{ item.label }}
        </template>
        <component
          :is="item.componentName"
          v-if="item.name === activeTab"
          :data="data"
          @reload="getUserProfile" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProfileInfo from './Info'
import ProfilePreference from './Preference'
import ProfilePassword from './Password'
import API from '@/api/user'

export default {
  name: 'Profile',
  components: { ProfileInfo, ProfilePreference, ProfilePassword },
  data() {
    return {
      activeTab: 'info',
      tabs: [
        {
          name: 'info',
          componentName: 'ProfileInfo',
          label: 'Profile',
          icon: 'document',
        }, {
          name: 'preference',
          componentName: 'ProfilePreference',
          label: 'Preference',
          icon: 'finished',
        }, {
          name: 'password',
          componentName: 'ProfilePassword',
          label: 'Password',
          icon: 'key',
        }],
      loading: false,
      data: null,
    }
  },
  mounted() {
    this.getUserProfile()
  },
  methods: {
    getUserProfile() {
      this.loading = true
      return API.getUserProfile({ extra: 'position,preference' }).then(data => {
        this.data = data
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .profile-subtitle {
  font-size: 16px;
  font-weight: 700;
  margin: 0 20px 20px;
}
</style>
