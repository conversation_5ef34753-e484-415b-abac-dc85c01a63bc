<template>
  <div>
    <div class="profile-subtitle">Change Password</div>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :disabled="submitting"
      label-width="170px"
      class="form-data"
    >
      <el-form-item label="New Password" prop="password">
        <el-input
          v-model="form.password"
          show-password
          placeholder="Please enter new password"
          class="w-300px"
        />
      </el-form-item>
      <el-form-item label="Confirm New Password" prop="confirm_password">
        <el-input
          v-model="form.confirm_password"
          show-password
          placeholder="Please confirm new password"
          class="w-300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-check"
          :loading="submitting"
          @click="onSave"
        >
          Submit & Re-Login
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import AuthAPI from '@/api/auth'

export default {
  name: 'ProfilePassword',
  data() {
    const validateNewPassword = (rule, value, callback) => {
      const reg = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*[`~!@$%^&*()_\-+=<>.?:"{}].*).{8,}$/
      if (!value) {
        callback(new Error('New password is required'))
      } else if (value.length < 8) {
        callback(new Error('New password must be at least 8 characters'))
      } else if (!reg.test(value)) {
        callback(
          new Error('New password requires at least 1 number, 1 lower case letter, 1 capital letter and 1 symbol'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('Confirm password is required'))
      } else if (value !== this.form.password) {
        callback(new Error('Confirm password must be same as the new password'))
      } else {
        callback()
      }
    }

    return {
      submitting: false,
      form: {
        password: '',
        confirm_password: '',
      },
      rules: {
        password: [{ required: true, validator: validateNewPassword, trigger: 'blur' }],
        confirm_password: [{ required: true, validator: validateConfirmPassword, trigger: 'blur' }],
      },
    }
  },
  methods: {
    onSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitting = true
          return AuthAPI.changePassword({ password: this.form.password })
            .then(async () => {
              this.$message.success('Success to change password. Please login again.')
              await this.$store.dispatch('user/logout')
              await this.$router.push(`/login`)
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
</style>
