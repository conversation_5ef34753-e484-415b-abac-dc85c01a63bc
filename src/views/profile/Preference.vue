<template>
  <div>
    <div class="profile-subtitle">Client Tasks</div>

    <el-form v-show="isEdit" ref="form" :model="form" label-width="240px" class="form-data">
      <el-form-item label="Lock Action Column In Client Tasks">
        <el-switch v-model="form.lock_action_column_in_client_tasks" active-color="#13ce66" />
      </el-form-item>
      <el-form-item label="Enable Pagination">
        <el-switch v-model="form.enable_client_task_page_pagination" active-color="#13ce66" />
      </el-form-item>
      <el-form-item label="Enable In-App Notifications">
        <el-switch v-model="form.enable_in_app_notifications" active-color="#13ce66" />
      </el-form-item>
      <el-form-item label="Default Page Size">
        <el-select
          v-if="form.enable_client_task_page_pagination"
          v-model="form.client_task_page_default_size">
          <el-option
            v-for="item in options.default_size"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
        <slot v-else>-</slot>
      </el-form-item>
      <el-form-item label="Default Order By">
        <el-select v-model="form.client_task_page_order_by">
          <el-option
            v-for="item in project_options.task.order_by"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-check" :loading="submitting" @click="onSave">Save</el-button>
        <el-button type="text" @click="onCancel">Cancel</el-button>
      </el-form-item>
    </el-form>

    <el-form v-show="!isEdit" label-width="240px" class="form-data">
      <el-form-item label="Lock Action Column In Client Tasks">
        {{form.lock_action_column_in_client_tasks | yesOrNo}}
      </el-form-item>
      <el-form-item label="Enable Pagination">
        <span v-if="form.enable_client_task_page_pagination" class="success">Yes</span>
        <span v-else class="danger">No</span>
      </el-form-item>
      <el-form-item label="Enable In-App Notifications">
        {{form.enable_in_app_notifications | yesOrNo}}
      </el-form-item>
      <el-form-item label="Default Page Size">
        <slot v-if="form.enable_client_task_page_pagination">
          {{ form.client_task_page_default_size }}
        </slot>
        <slot v-else>-</slot>
      </el-form-item>
      <el-form-item label="Default Order By">
        {{ form.client_task_page_order_by | getLabel(project_options.task.order_by) }}
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-edit" @click="editProfile">Edit</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import API from '@/api/user'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment/moment'

export default {
  name: 'ProfilePreference',
  props: {
    'data': Object,
  },
  data() {
    return {
      submitting: false,
      isEdit: false,
      form: {
        lock_action_column_in_client_tasks: false,
        enable_client_task_page_pagination: true,
        enable_in_app_notifications: false,
        client_task_page_default_size: 20,
        client_task_page_order_by: 'rank asc',
      },
      initForm: null,
      options: {
        default_size: [20, 50, 100, 200, 300],
      },
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.info,
    }),
    ...mapGetters([
      'project_options',
      'uid',
    ]),
  },
  watch: {
    'data': {
      handler(val) {
        this.getUserProfile(val)
      }, immediate: true,
    },
  },
  methods: {
    getUserProfile(data) {
      if (!data) return
      const { preference } = data
      if (preference) {
        Object.assign(this.form, preference)
      }
      this.initForm = JSON.parse(JSON.stringify(this.form))
    },
    updateUserPreference() {
      this.submitting = true

      if (this.form.enable_in_app_notifications) this.form.enable_in_app_notification_time = moment().toISOString()

      return API.updateUserPreference(this.uid, this.form).then(data => {
        this.initForm = JSON.parse(JSON.stringify(this.form))
        this.isEdit = false
        const info = { ...this.userInfo }
        info.preference = data
        this.$store.commit('user/SET_INFO', info)
        this.$message({
          message: 'Update profile preference successfully',
          type: 'success',
        })
      }).finally(() => {
        this.$emit('reload')
        this.submitting = false
      })
    },

    editProfile() {
      this.isEdit = true
    },

    onSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.updateUserPreference()
        } else {
          return false
        }
      })
    },
    onCancel() {
      Object.assign(this.form, this.initForm)
      this.isEdit = false
    },
  },
}
</script>

<style scoped>
.preference-subtitle {
  font-size: 16px;
  font-weight: 700;
  margin: 0 20px 20px;
}
</style>
