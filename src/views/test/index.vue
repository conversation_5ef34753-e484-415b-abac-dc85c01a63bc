<template>
  <div>
    <pre>{{data}}</pre>
  </div>
</template>

<script>
import { AppHttpService } from '@/utils/request'

export default {
  name: 'Test',
  data() {
    return {
      data: undefined,
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      return AppHttpService
        .get(`/projects/1/tasks/2`, {
          params: {
            extra: 'email_records,email_records.email_record,events,client_contact_reviews,client_compliance_reviews,latest_submitted_sq,latest_submitted_ca',
          },
        })
        .then(data => {
          this.data = this.dataFormat(data)
        })
    },
    dataFormat: (data) => {
      const events = data.events
      const email = data['email_records']

      /*
        Event:
          type
          target_status

        Email:
          email_content_type
       */

      const output_data = {
        outreach: {
          email: [],
          outreach: [],
        },
        consultant: {
          email: [],
          terms_conditions: [],
          screening_question: [],
          compliance_question: [],
          advisor_schedule: [],
        },
        client: {
          email: [],
          contact_approve: [],
          client_contact_schedule: [],
        },
        compliance: {
          email: [],
          compliance_approve: [],
        },
        arrange: {
          email: [],
          arrange: [],
        },
        complete: {
          email: [],
          complete: [],
          client_feedback: [],
          consultant_feedback: [],
        },
      }

      categorizeEvent(events, output_data)
      categorizeEmail(email, output_data)

      return output_data

      function categorizeEvent(events, output_data) {
        events.forEach(function(item) {
          const eventsCategory = getEventsCategory(item)
          const [level_1, level_2] = eventsCategory.split('>')

          output_data[level_1][level_2].push(item)
        })

        function getEventsCategory(event) {
          const type = event.type

          const type_mapper = {
            OUTREACH: 'outreach>outreach',
            ARRANGE: 'arrange>arrange',
            COMPLETE: 'complete>complete',

            SQ_SEND: 'consultant>screening_question',
            SQ_RESPOND: 'consultant>screening_question',
            CA_SEND: 'consultant>screening_question',
            CA_RESPOND: 'consultant>screening_question',
            ADVISOR_SCHEDULE_SEND: 'consultant>advisor_schedule',
            ADVISOR_SCHEDULE_RESPOND: 'consultant>advisor_schedule',
            CLIENT_COMPLIANCE_SEND: 'consultant>compliance_question',

            CLIENT_COMPLIANCE_APPROVE: 'compliance>compliance_approve',
            CLIENT_COMPLIANCE_REJECT: 'compliance>compliance_approve',

            CONTACT_SEND: 'client>contact_approve',
            CONTACT_APPROVE: 'client>contact_approve',
            CONTACT_REJECT: 'client>contact_approve',
            CONTACT_SCHEDULE_SEND: 'client>client_contact_schedule',
            CONTACT_SCHEDULE_RESPOND: 'client>client_contact_schedule',

            ADVISOR_FEEDBACK_SEND: 'complete>client_feedback',
            ADVISOR_FEEDBACK_RESPOND: 'complete>client_feedback',
            CONTACT_FEEDBACK_SEND: 'complete>consultant_feedback',
            CONTACT_FEEDBACK_RESPOND: 'complete>consultant_feedback',
          }

          return type_mapper[type]
        }
      }

      /**
       * Email 分组
       * @param email
       * @param output_data
       */
      function categorizeEmail(email, output_data) {
        email.forEach(function(item) {
          const emailCategory = getEmailCategory(item['email_content_type'])

          if (emailCategory) {
            output_data[emailCategory].email.push(item)
          }
        })

        function getEmailCategory(type) {
          const mapper = {
            PORTAL_ADVISOR_PRE_CALL: 'consultant',
            PORTAL_ADVISOR_FEEDBACK: 'consultant',
            PORTAL_COMPLIANCE: 'compliance',
            PORTAL_CONTACT_PRE_CALL: 'client',
            PORTAL_CONTACT_POST_CALL: 'client',
            CLIENT_CONTACT_LOGIN_CODE: 'client',
            TASK_ARRANGE: 'arrange',
            TASK_COMPLETE: 'complete',
            PROJECT_OUTREACH: 'outreach',
            CUSTOM: '',
          }

          return mapper[type]
        }
      }
    },
  },
}
</script>

<style scoped>

</style>
