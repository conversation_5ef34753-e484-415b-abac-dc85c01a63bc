<template>
  <div>
    <el-table :data="list"
              style="width: 100%">
      <el-table-column prop="advisor"
                       label="Advisor">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column prop="project"
                       label="Project">
        <template slot-scope="scope">
          <router-link-project :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column prop="general_status"
                       label="Status">
        <template slot-scope="scope">
          {{scope.row.compliance_passed_jit?'Approved':'Pending Approval'}}
        </template>
      </el-table-column>
    </el-table>
    <pagination :page.sync="page.page"
                :total.sync="page.total"
                :limit="page.size"
                :pager-count="5"
                layout="prev, pager, next"
                @pagination="loadData()" />
  </div>
</template>

<script>
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import callPanelMixin from '@/views/home_v2/components/mixins/call_panel.mixin'

export default {
  name: 'CallPending',
  components: { RouterLinkProject, RouterLinkAdvisor, Pagination },
  mixins: [callPanelMixin],
  data() {
    return {
      params: {
        lead_or_support_id: this.$store.getters.uid,
        angle_id_ne: 0,
        general_status_in: 'SELECTED,SCHEDULED',
        'project.status_in': 'ACTIVE,ON_HOLD',
        client_compliance_status_not_in: 'REJECTED',
        capvision_compliance_status_in: 'SENT,APPROVED,DIRECT_APPROVED',
        extra: 'advisor,project,compliance_passed_jit',
      },
    }
  },
}
</script>
