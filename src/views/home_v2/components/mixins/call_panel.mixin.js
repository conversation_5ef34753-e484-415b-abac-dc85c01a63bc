/**
 * call_panel.mixin.js
 * 2021/3/26
 * <AUTHOR>
 * copyright 2014-2021, All rights reserved.
 */
import { AppHttpService } from '@/utils/request'

export default {
  data() {
    return {
      list: [],
      page: {
        page: 1,
        size: 10,
        total: 0,
      },
    }
  },
  mounted() {
    return this.loadData()
  },
  methods: {
    loadData() {
      const params = this.getParams()
      return AppHttpService
        .get('/tasks', { params })
        .then(data => {
          this.list = data['list']
          this.page.total = data['count']
        })
    },
    getParams() {
      return Object.assign({}, this.params, {
        page: this.page.page,
        size: this.page.size,
      })
    },
  },
}
