<template>
  <div>
    <el-row :gutter="20" class="mt-8">

      <!-- CN payment -->
      <el-col>
        <el-card class="no-border">
          <div class="title">CN Project Payment v2</div>
          <div class="filter-container">
            <div class="flex justify-content-center align-items-center flex-wrap"
                 style="width:100%;">
              <div>
                <refresh-button class="filter-item" @action="getList" />
                <advisor-search
                  v-model="form.advisor_ids"
                  class="filter-item"
                  collapse-tags
                  use-es-search
                  is-ts-id
                  @change="handleSearch" />
                <el-input
                  v-model="form.project_name_contains"
                  placeholder="Project Name"
                  class="filter-item w-200px"
                  @input="debounceSearch"
                />
                <el-select
                  v-model="form.status_in"
                  multiple
                  class="filter-item"
                  placeholder="Status"
                  @change="handleSearch">
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
                <el-input
                  v-model="form.task_ids"
                  placeholder="task IDs(separated by a comma)"
                  class="filter-item w-200px"
                  @input="debounceSearch"
                />
              </div>
            </div>
          </div>
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            stripe
            :span-method="objectSpanMethod"
          >
            <el-table-column label="ID" width="120" prop="id" />
            <el-table-column label="Task ID" width="120" prop="topic_id" />
            <el-table-column label="Project">
              <template slot-scope="scope">
                <el-link
                  type="primary text-truncate"
                  :underline="false"
                  @click="openToCNProject(scope.row)">
                  {{ scope.row.topic.detail_snapshot.project_name }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="Advisor">
              <template slot-scope="scope">
                <router-link-advisor :data="scope.row.advisor" />
              </template>
            </el-table-column>
            <el-table-column label="Task Date" width="85">
              <template slot-scope="scope">
                {{ scope.row.topic.task_start_time | momentFormat() }}
              </template>
            </el-table-column>
            <el-table-column label="Minutes" width="80">
              <template slot-scope="scope">
                {{ scope.row.minutes }}
              </template>
            </el-table-column>
            <el-table-column label="Rate" width="80">
              <template slot-scope="scope">
                {{ scope.row.rate }}
              </template>
            </el-table-column>
            <el-table-column label="Amount" width="80">
              <template slot-scope="scope">
                {{ scope.row.amount }}
              </template>
            </el-table-column>
            <el-table-column label="Currency" width="80">
              <template slot-scope="scope">
                {{ scope.row.currency_name }}
              </template>
            </el-table-column>
            <el-table-column label="Type" width="120">
              <template slot-scope="scope">
                {{ scope.row.subtopic_name }}
              </template>
            </el-table-column>
            <el-table-column label="Items" width="70">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="applyItemsDialogShow(scope.row)">
                  {{ scope.row.items.length }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="Action" width="60">
              <template slot-scope="scope">
                <el-tooltip effect="dark" content="Complete" placement="top">
                  <!--可以重复发-->
                  <el-link
                    v-if="scope.row.status!=='ADVISOR_CONFIRMED'"
                    type="primary"
                    :underline="false"
                    icon="el-icon-finished"
                    @click="applyDialogShow(scope.row)" />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            :total="total"
            :page.sync="searchQuery.page"
            :limit.sync="searchQuery.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="Complete Payment"
      :visible.sync="paymentDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="50%"
    >
      <el-form v-loading="dialog_loading">
        <el-form-item v-if="w9_status!==0" label="Form W-9">
          <div v-if="[1,3,4].includes(w9_status)">
            <div v-if="w9_status===3">Under signing...</div>
            <div v-if="w9_status===4">Signed</div>
            <div class="info">Experts have received ${{ calculate_advisor_paid_this_year }} this year.</div>
          </div>
          <div v-if="w9_status===2">
            <el-checkbox v-model="should_send_w9_form">Send "Form W-9"
            </el-checkbox>
            <div class="info">Expert's payment has over ${{ calculate_advisor_paid_this_year }} this
              year,need to send w9 forms to experts to fill in tax
              information.
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-collapse class="box-card">
            <el-collapse-item name="consultant">
              <template slot="title">
                <el-checkbox
                  v-model="portal.is_send"
                  disabled>
                  Payment Confirmation Email
                </el-checkbox>
                <el-select
                  v-model="portal.template"
                  value-key="id"
                  class="w-300px ml-8 mr-8"
                  style="height: 48px;"
                  @change="handleTemplateChange">
                  <el-option
                    v-for="template in template_options"
                    :key="template.id"
                    :label="template.name"
                    :value="template" />
                </el-select>
              </template>
              <div v-loading="email_loading">
                <to-cc-editor :data="portal.email" class="mb-8" />
                <el-input v-model="portal.email.subject" class="mb-8" placeholder="Subject" />
                <tinymce ref="tinymce_editor" v-model="portal.email.content" :toolbar="toolbar" />
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary"
                   :disabled="dialog_loading || email_loading"
                   @click="actionPayment()">Submit</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="Payment Items"
      :visible.sync="paymentItemsDialogVisible"
    >
      <el-table
        v-if="payment_data"
        :data="payment_data.items"
        border
        stripe
      >
        <el-table-column label="ID" width="130" prop="id" />
        <el-table-column label="Date" width="90">
          <template slot-scope="scope">
            {{ scope.row.create_at | momentFormat() }}
          </template>
        </el-table-column>
        <el-table-column label="Minutes" width="80">
          <template slot-scope="scope">
            {{ scope.row.minutes }}
          </template>
        </el-table-column>
        <el-table-column label="Amount" width="80">
          <template slot-scope="scope">
            {{ scope.row.amount }}
          </template>
        </el-table-column>
        <el-table-column label="Currency" width="80">
          <template slot-scope="scope">
            {{ scope.row.currency_name }}
          </template>
        </el-table-column>
        <el-table-column label="Status">
          <template slot-scope="scope">
            {{ scope.row.status | getLabel(options) }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="paymentItemsDialogVisible = false">Close</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import PaymentAPI from '@/api/payment'
import EmailAPI from '@/api/email'
import Pagination from '@/components/Pagination/index'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RefreshButton from '@/components/RefreshButton/index'
import Tinymce from '@/components/Tinymce'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/tool'

export default {
  name: 'CNPayment',
  components: { RefreshButton, Pagination, AdvisorSearch, RouterLinkAdvisor, Tinymce, ToCcEditor },
  data() {
    return {
      loading: false,
      dialog_loading: false,
      email_loading: false,
      toolbar: ['fontselect fontsizeselect | undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      options: [
        {
          value: 'INITIAL',
          label: 'Initial',
        },
        {
          value: 'ADVISOR_CONFIRMED',
          label: 'Advisor Confirmed',
        }, {
          value: 'ADVISOR_CONFIRMING',
          label: 'Advisor Confirming',
        }, {
          value: 'PAYING',
          label: 'Paying',
        }, {
          value: 'PAID',
          label: 'Paid',
        }],
      total: 0,
      searchQuery: {
        page: 1,
        size: 20,
        extra: 'payments.items,payments.advisor.location_id_path,payments.advisor.paid_usd_in_this_year_jit,payments.advisor', // .w9_form
      },
      form: {
        status_in: ['ADVISOR_CONFIRMING', 'INITIAL'],
        project_name_contains: undefined,
        advisor_ids: [],
        group_by_task: true,
        task_ids: '',
      },
      tableData: [],
      payment_data: null,
      w9_status: 0,
      should_send_w9_form: false,
      calculate_advisor_paid_this_year: 0,
      paymentDialogVisible: false,
      template_options: [],
      portal: {
        template: null,
        is_send: true,
        email: {
          subject: '',
          content: '',
        },
      },
      paymentItemsDialogVisible: false,
    }
  },
  computed: {
    ...mapGetters([
      'info',
    ]),

    isFinance() {
      return this.info.roles.find(item => item.role.name === 'Finance')
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const params = {
        ids: this.form.task_ids ? this.form.task_ids.split(',') : undefined,
        project_name: this.form.project_name_contains || undefined,
        region: 'CN',
        payments: {
          region: 'US',
          items: {
            status_in: this.form.status_in.length ? this.form.status_in : undefined,
          },
          advisor_tsid_in: this.form.advisor_ids.length ? this.form.advisor_ids : undefined,
        },
        or: this.isFinance ? [] : [{ 'pm_email1': this.info.email }, { 'pm_email2': this.info.email }],
      }

      this.loading = true
      return PaymentAPI.getUsPaymentCnDBList(params, this.searchQuery)
        .then(data => {
          let result = []
          data.list.forEach(topic => {
            topic.payments.forEach((payment, index) => {
              if (!index) payment.rowspan = topic.payments.length
              payment.topic = { ...topic }
              delete payment.topic.payments
            })
            result = [...result, ...topic.payments]
          })

          this.tableData = result
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2, 3, 4, 11].includes(columnIndex)) {
        return row.rowspan ? [row.rowspan, 1] : [0, 0]
      }
    },

    checkW9Status(data) {
      if (!data.advisor) return
      const advisor = data.advisor
      const is_us_advisor = advisor.location_id_path && advisor.location_id_path.split(' ').includes('174')
      if (!is_us_advisor) return
      // Complete时 如果是美国专家 校验 W9 状态值:
      // 1:未达标 => 当年累计达到 $600
      // 2:达标 未发送W9
      // 3:达标 已发送 专家未签
      // 4:达标 专家已签 无需再发送（一旦签过，后续无需再签）
      this.calculate_advisor_paid_this_year = advisor.paid_usd_in_this_year_jit + data.amount
      if (advisor.w9_form) {
        switch (advisor.w9_form.status) {
          case 'INIT':
            this.w9_status = this.calculate_advisor_paid_this_year < 600 ? 1 : 2
            this.should_send_w9_form = this.calculate_advisor_paid_this_year > 600
            break
          case 'REQUESTED':
            this.w9_status = 3
            break
          case 'SIGNED':
            this.w9_status = 4
        }
      } else {
        this.w9_status = this.calculate_advisor_paid_this_year < 600 ? 1 : 2
        this.should_send_w9_form = this.calculate_advisor_paid_this_year > 600
      }
    },
    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },
    debounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },

    applyDialogShow(item) {
      this.payment_data = item
      this.paymentDialogVisible = true
      this.dialog_loading = true
      this.getEmailTemplateList()
        .then(() => {
          this.checkW9Status(item)
          return this.handleTemplateChange()
        })
        .finally(() => {
          this.dialog_loading = false
        })
    },

    actionPayment() {
      const { should_send_w9_form, portal } = this
      const params = {
        should_send_w9_form,
        advisor_portal: {
          portal: {
            type: 'ADVISOR_POST_CALL',
          },
          email: portal.email,
          email_template_tags: [
            'PAYMENT_CONFIRM',
          ],
        },
      }

      this.dialog_loading = true

      return PaymentAPI.usPaymentFormApply(this.payment_data.topic_id, params)
        .then(data => {
          this.$message({
            message: 'Submit successful',
            type: 'success',
          })
          this.getList()
        }, () => {})
        .finally(() => {
          this.dialog_loading = false
          this.actionCancel()
        })
    },

    actionCancel() {
      this.paymentDialogVisible = false
      this.payment_data = null
      this.calculate_advisor_paid_this_year = 0
      this.w9_status = 0
      this.portal = {
        template: null,
        is_send: true,
        email: {
          subject: '',
          content: '',
        },
      }
    },

    handleTemplateChange() {
      this.email_loading = true
      return this.generateMail(this.portal.template.id)
        .then(data => {
          this.portal.email = data
          this.portal.email.content = '<div style="font-size: 11pt; font-family: Calibri;">' +
            this.portal.email.content +
            '</div>'

          if (!this.$refs.tinymce_editor) return
          this.$refs.tinymce_editor.confirmLoaded()
            .then(() => {
              this.$nextTick(() => {
                this.$refs.tinymce_editor.setContent(this.portal.email.content)
              })
            })
        })
        .finally(() => {
          this.email_loading = false
        })
    },

    generateMail(id) {
      const { payment_data } = this
      const data = {
        advisor_id: payment_data.advisor.id,
      }
      Object.assign(data, {
        context_params: {
          payment: {
            PAYMENT_HOURLY_RATE: `${payment_data.rate} ${payment_data.currency_name}/H`,
            PAYMENT_TIME: `${payment_data.minutes} minutes`,
            PAYMENT_AMOUNT: `${payment_data.amount} ${payment_data.currency_name}`,
            PAYMENT_PROJECT_NAME: payment_data.topic.project_name,
            // PAYMENT_TASK_MANAGER_NAME: payment_data.task_manager_name,
            // PAYMENT_TASK_MANAGER_SIGNATURE: payment_data.task_manager_email,
            // PAYMENT_TASK_MANAGER_EMAIL: payment_data.task_manager_email,
          },
        },
      })
      return EmailAPI.getEmailByTemplate(id, data)
    },

    getEmailTemplateList() {
      const params = { has_permission: true, content_type: 'PORTAL_ADVISOR_POST_CALL' }
      return EmailAPI.getEmailList(params).then(data => {
        this.template_options = data.list.filter(
          item => item.tags.includes('PAYMENT_CONFIRM') && !item.tags.includes('ADVISOR_FEEDBACK'))
        if (this.payment_data.project_module_enum === 'SURVEY') {
          this.portal.template = this.template_options.find(item => item.tags.includes('SURVEY')) || this.template_options[0]
        } else {
          this.portal.template = this.template_options.find(item => !item.tags.includes('SURVEY')) || this.template_options[0]
        }
      })
    },

    openToCNProject(data) {
      const project_id = data.topic.project_tsid?.split('-')?.[1]
      if (project_id) {
        const cn_db_url = import.meta.env.VITE_APP_URL_DB + '#/project/consultation/index/detail/tasklist?'
        const newWin = window.open('about:blank')
        newWin.location.href = cn_db_url + `id=${project_id}`
      }
    },

    applyItemsDialogShow(item) {
      if (!item.items.length) return
      this.payment_data = item
      this.paymentItemsDialogVisible = true
    },
  },

}
</script>

<style scoped>
.title {
  text-align: center;
  color: #333333;
  font-weight: bold;
  padding-bottom: 4px;
  text-decoration: underline;
  text-underline-position: under;
  text-decoration-color: #d4d4d4;
}

.w-300px{
  width:300px;
}
</style>
