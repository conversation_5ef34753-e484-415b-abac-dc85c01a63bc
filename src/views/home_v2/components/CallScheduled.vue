<template>
  <div>
    <el-table :data="list"
              style="width: 100%">
      <el-table-column prop="advisor"
                       label="Advisor">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column prop="project"
                       label="Project">
        <template slot-scope="scope">
          <router-link-project :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column prop="time"
                       label="Time">
        <template slot-scope="scope">
          <div :title="scope.row.schedule.start_time | momentFormat('long')">
            {{ scope.row.schedule.start_time | momentFormat('M/D hA') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="loopUp_url"
                       label="LoopUp Meeting">
        <template v-if="scope.row.loopup_room" slot-scope="scope">
          <a
            class="link"
            :href="'https://'+scope.row.loopup_room.url"
            target="_blank">
            {{ scope.row.loopup_room.name }}
            <el-icon name="right" />
          </a>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page.sync="page.page"
                :total.sync="page.total"
                :limit="page.size"
                :pager-count="5"
                layout="prev, pager, next"
                @pagination="loadData()" />
  </div>
</template>

<script>
import callPanelMixin from '@/views/home_v2/components/mixins/call_panel.mixin.js'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import moment from 'moment-timezone'

export default {
  name: 'CallScheduled',
  components: { RouterLinkAdvisor, RouterLinkProject, Pagination },
  mixins: [callPanelMixin],
  data() {
    return {
      params: {
        lead_or_support_id: this.$store.getters.uid,
        general_status_in: 'ARRANGED',
        extra: 'advisor,schedule,loopup_room,project',
        /* 距离访谈结束未满 1h 时入此列表 */
        'schedule.end_time_gt': moment().subtract(1, 'hour').format('X'),
      },
    }
  },
}
</script>
