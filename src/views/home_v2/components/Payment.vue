<template>
  <div>
    <el-row :gutter="20" class="mt-8">

      <!-- CN payment -->
      <el-col>
        <el-card class="no-border">
          <div class="title mb-8">Payment</div>
          <div class="filter-container">
            <div class="flex justify-content-center align-items-center flex-wrap w-100">
              <div>
                <refresh-button class="filter-item" @action="getList" />
                <advisor-search
                  v-model="form.advisor_ids"
                  class="filter-item"
                  collapse-tags
                  @change="handleSearch"
                />
                <project-search
                  v-model="form.project_ids"
                  class="filter-item w-200px"
                  @change="debounceSearch"
                />
                <el-select
                  v-model="form.status_in"
                  multiple
                  clearable
                  class="filter-item"
                  placeholder="Status"
                  @change="handleSearch"
                >
                  <el-option
                    v-for="item in options.status"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
                <el-input
                  v-model="form.task_ids"
                  clearable
                  placeholder="task IDs(separated by a comma)"
                  class="filter-item w-200px"
                  @input="debounceSearch"
                />
              </div>
            </div>
          </div>
          <payment-list
            v-loading="loading"
            :table-data="tableData"
            select-disabled
          />

          <pagination
            :total="total"
            :page.sync="searchQuery.page"
            :limit.sync="searchQuery.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import API from '@/api/payment'
import Pagination from '@/components/Pagination/index'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import RefreshButton from '@/components/RefreshButton/index'
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/tool'
import PaymentList from '@/views/finance/payment/components/PaymentList2.vue'
import RouteTools from '@/utils/tool-router'
import _ from 'lodash'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch/index.vue'

export default {
  name: 'Payment',
  components: { ProjectSearch, PaymentList, RefreshButton, Pagination, AdvisorSearch },
  data() {
    return {
      loading: false,
      options: {
        status: [
          {
            value: 'INITIAL',
            label: 'Initial',
          }, {
            value: 'ADVISOR_CONFIRMING',
            label: 'Advisor Confirming',
          }, {
            value: 'ADVISOR_CONFIRMED',
            label: 'Advisor Confirmed',
          }, {
            value: 'PAYING',
            label: 'Paying',
          }, {
            value: 'PAID',
            label: 'Paid',
          }, {
            value: 'PAY_FAILED',
            label: 'Pay Failed',
          }, {
            value: 'CANCELLED',
            label: 'Cancelled',
          }, {
            value: null,
            label: 'Total',
          },
        ],
      },
      total: 0,
      searchQuery: {
        page: 1,
        size: 20,
        extra: [
          'form.account',
          'form.account2_no_mosaic_jit',
          'form.virtual_incentives_payment_info',
          'topic.referral.to_advisor',
          'topic.project',
          'topic.task',
          'payment.advisor',
          'events.create_by',
          'event',
        ].join(),
      },
      form: {
        aggregation: ['status_name'],
        ids: undefined,
        task_ids: undefined,
        task_ts_ids: undefined,
        project_ts_ids: undefined,
        project_sub_type: undefined,
        status: null,
        advisor_ids: [],
        project_ids: [],
        payment_region: undefined,
        topic_region: undefined,
        pay_method_in: [],
        status_in: ['ADVISOR_CONFIRMING', 'INITIAL'],
      },
      tableData: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'info',
    ]),
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const saveQuery = { ...this.form }
      delete saveQuery.aggregation
      RouteTools.saveQueryToRouter(saveQuery)

      const query = { ...this.searchQuery }
      if (this.form.pay_method_in.length) query.pay_method_in = this.form.pay_method_in.join()

      const params = this.formatPaymentParams(true)

      this.formatDateParams(params.topic, this.task_date_range, 'task_start_time')
      // this.formatDateParams(params, this.paying_at_range, 'paying_at')
      this.formatDateParams(params, this.paid_at_range, 'paid_at')

      this.loading = true
      return API.getPaymentItemsList(params, query)
        .then(data => {
          this.tableData = data.list.map(item => {
            if (item.form) item.form.account = item.form.account2_no_mosaic_jit || item.form.account
            item.latest_pay_date = getPaymentStatusTime(item.events, 'PAY')
            item.latest_pay_succeeded_date = getPaymentStatusTime(item.events, 'PAY_SUCCESS')
            item.latest_pay_failed_date = getPaymentStatusTime(item.events, 'PAY_FAIL')
            return item
          })
          this.total = data.count
          if (this.initAggregation && data.aggregations) {
            this.aggregations.total = 0
            this.aggregations.status_name = data.aggregations.status_name
            data.aggregations.status_name.forEach(item => {
              this.aggregations.total += item.count
            })
          }
        })
        .finally(() => {
          this.loading = false
        })

      function getPaymentStatusTime(list, type) {
        const target_list = _.sortBy(list.filter(item => item.type === type), 'create_at').reverse()
        return target_list.length ? target_list[0].create_at : null
      }
    },
    formatPaymentParams(needAggregation) {
      const params = {
        ids: this.form.ids ? this.form.ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
        status_name_in: this.form.status_in || undefined,
        payment: {
          advisor: {
            ids: this.form.advisor_ids.length ? this.form.advisor_ids : undefined,
          },
          region: this.form.payment_region || undefined,
        },
        topic: {
          region: this.form.topic_region || undefined,
          project: {
            ids: this.form.project_ids.length ? this.form.project_ids : undefined,
            sub_type: this.form.project_sub_type || undefined,
            tsid_in: this.form.project_ts_ids ? this.form.project_ts_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
          },
          task: {
            ids: this.form.task_ids ? this.form.task_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
            tsid_in: this.form.task_ts_ids ? this.form.task_ts_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
            or: [{ lead_id: this.uid }, { support_id: this.uid }],
          },
        },
        // events: {
        // },
      }
      if (needAggregation) params.aggregation = this.form.aggregation
      return params
    },
    formatDateParams(params, dateRange, type) {
      params[type + '_gte'] = dateRange && dateRange.length ? dateRange[0] : undefined
      params[type + '_lte'] = dateRange && dateRange.length ? dateRange[1] : undefined
    },
    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },
    debounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },
  },

}
</script>

<style scoped>
.title {
  text-align: center;
  color: #333333;
  font-weight: bold;
  padding-bottom: 4px;
  text-decoration: underline;
  text-underline-position: under;
  text-decoration-color: #d4d4d4;
}

.w-300px{
  width:300px;
}
</style>
