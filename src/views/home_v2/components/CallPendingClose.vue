<template>
  <div>
    <el-table :data="list"
              style="width: 100%">
      <el-table-column prop="advisor"
                       label="Advisor">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column prop="time"
                       label="Time">
        <template slot-scope="scope">
          <div :title="scope.row.schedule.start_time | momentFormat('long')">
            {{ scope.row.schedule.start_time | momentFormat('M/D hA') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="project"
                       label="Project">
        <template slot-scope="scope">
          <router-link-project :data="scope.row.project" />
        </template>
      </el-table-column>
    </el-table>
    <pagination :page.sync="page.page"
                :total.sync="page.total"
                :limit="page.size"
                :pager-count="5"
                layout="prev, pager, next"
                @pagination="loadData()" />
  </div>
</template>

<script>
import callPanelMixin from '@/views/home_v2/components/mixins/call_panel.mixin.js'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import moment from 'moment-timezone'

export default {
  name: 'CallPendingClose',
  components: { RouterLinkAdvisor, RouterLinkProject, Pagination },
  mixins: [callPanelMixin],
  data() {
    return {
      params: {
        lead_or_support_id: this.$store.getters.uid,
        general_status_in: 'ARRANGED',
        'project.status_in': 'ACTIVE,ON_HOLD',
        extra: 'advisor,project,schedule',
        /* 访谈结束 1h 后归入此列表 */
        'schedule.end_time_lt': moment().subtract(1, 'hour').format('X'),
      },
    }
  },
}
</script>
