<template>
  <div>
    <div class="progress"
         :class="[bar.color, {loading: loading}]">
      <div class="progress-bar" :style="{width: bar.width}">
        <div class="progress-text">{{ current }} H / {{ target }} H</div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment-timezone'

// RA
// 红色<=0.75黄色 <=1绿色 <=1.25金色 <=1.6彩虹色
// SRA
// 1 1.25 1.5 1.85
// Manager
// 1.5   1.75  2.0 2.35

/* 注意: steps[1] 为业务基准值 */
const rules = [
  {
    role: 'RA',
    level: 1,
    steps: [0.75, 1, 1.25, 1.6],
  },
  {
    role: 'SRA',
    level: 2,
    steps: [1, 1.25, 1.5, 1.85],
  },
  {
    role: 'Manager',
    level: 3,
    steps: [1.5, 1.75, 2, 2.35],
  },
]

export default {
  name: 'WorkProgress',
  props: {
    target: Number,
    current: Number,
    role: String,
    loading: Boolean,
  },
  data() {
    return {
      bar: {
        color: undefined,
        width: 0,
      },
    }
  },
  computed: {
    todayStandardHour() {
      const rule = this.getRule(this.role)
      const role_rate = rule ? rule.steps[1] : 1
      return this.target * this.getCurrentDayPercent() * role_rate
    },
  },
  watch: {
    target() {
      this.updateBar()
    },
    current() {
      this.updateBar()
    },
    role() {
      this.updateBar()
    },
  },
  mounted() {
    this.updateBar()
  },
  methods: {
    updateBar() {
      this.updateBarWidth(this.role)
      this.updateBarColor()
    },

    updateBarWidth(role) {
      let standard_monthly_workload = this.target
      const rule = this.getRule(role)
      if (rule) {
        standard_monthly_workload = this.target * rule.steps[1]
      }

      let percent = (this.current / standard_monthly_workload) * 100

      /* Bar max overflow to 140% */
      if (percent > 140) {
        percent = 141
      }
      this.bar.width = `${percent}%`
    },

    updateBarColor() {
      // const work_day_percent = this.getCurrentDayPercent()
      // const work_days_target = this.target * work_day_percent
      // const client_hour_percent = this.current / work_days_target

      // 默认每天一个小时，工作日
      // const today_workdays = this.businessDaysIntoMonth(moment())
      // const client_hour_percent = this.current / today_workdays
      const client_hour_percent = this.current / this.target

      this.bar.color = this.getBarColor(client_hour_percent, this.role)
      this.bar.text = this.getRankText(client_hour_percent, this.role)
    },

    getBarColor(percent, role) {
      const step = this.getStep(percent, role)
      return `step_${step + 1}`
    },

    getStep(percent, role) {
      let step = 0
      const rule = this.getRule(role)

      if (rule) {
        const steps = rule.steps
        for (let i = 0; i < steps.length; i++) {
          step = i
          if (percent >= steps[i]) {
            step = i + 1
          } else {
            break
          }
        }
      }

      return step
    },

    getRule(role) {
      return rules.find(item => item.role === role)
    },

    getCurrentDayPercent() {
      const total_workdays = this.businessDaysIntoMonth(moment().endOf('month'))
      const today_workdays = this.businessDaysIntoMonth(moment())

      return today_workdays / total_workdays
    },

    getRankText(percent, role) {
      const word_library = [
        [],
        [],
        ['Good Job!', 'Sweet!', 'Beautiful!', 'Great!', 'Cheers!'],
        ['Excellent!', '"Five-star"', 'Prime!', 'Golden!'],
        ['Amazing!!!', '👍', '🎉', 'Wonderful!!!', 'Cooooooooool!!!'],
      ]

      const step = this.getStep(percent, role)
      const words = word_library[step]

      let word = `${this.current} H`

      if (words.length > 0) {
        word = words[Math.round(Math.random() * (words.length - 1))]
      }

      return word
    },

    businessDaysIntoMonth(end) {
      return workdaysCount(moment().startOf('month'), end)

      function workdaysCount(start, end) {
        const first = start.clone().endOf('week') // end of first week
        const last = end.clone().startOf('week') // start of last week
        const days = last.diff(first, 'days') * 5 / 7 // this will always multiply of 7
        let wfirst = first.day() - start.day() // check first week
        if (start.day() === 0) --wfirst // -1 if start with sunday
        let wlast = end.day() - last.day() // check last week
        if (end.day() === 6) --wlast // -1 if end with saturday
        return wfirst + Math.floor(days) + wlast // get the total
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.progress {
  width: 71%;
  height: 25px;

  //--border-radius: 3px;
  //--border-width: 1px;
  //width: 600px;
  //margin-top: 16px;
  //border: var(--border-width) solid #b0b0b0;
  //box-shadow: inset 0 3px 5px 0 #d3d0d0;
  //border-radius: var(--border-radius);

  &.loading {
    animation: loading-background-flow linear 1s infinite;
    background-image: linear-gradient(305deg, rgba(112, 112, 112, .15) 25%, transparent 25%, transparent 50%, rgba(112, 112, 112, .15) 50%, rgba(112, 112, 112, .15) 75%, transparent 75%, transparent);
    background-size: 3em 4em;
  }

  .progress-bar {
    height: 100%;
    box-shadow: inset 0 6px 5px 0 rgb(248 248 248 / 41%), inset 0px -3px 5px 0 rgb(0 0 0 / 20%);
    background-image: linear-gradient(305deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 3em 4em;
    border-radius: calc(var(--border-radius) - var(--border-width));
    position: relative;
    cursor: pointer;

    .progress-text {
      display: none;
      color: #FFF;
      text-shadow: 1px 1px 1px #3b3b3b63, 0px -1px 1px #3b3b3b1a, -1px -1px 6px #3b3b3b30;
      bottom: 0;
      font-size: 40px;
      font-weight: bold;
      font-style: italic;
      text-align: right;
      line-height: 0;
      margin-right: 20px;
      white-space: nowrap;
    }

    &:hover {
      .progress-text {
        display: block;
      }
    }
  }

  /*
   Red
   Yellow
   Green
   Gold
   Rainbow
  */
  &.step_1 {
    .progress-bar {
      background-color: #bf0000;
    }
  }

  &.step_2 {
    .progress-bar {
      background-color: #f1e763;
    }
  }

  &.step_3 {
    .progress-bar {
      background-color: #2cad01;
    }
  }

  &.step_4 {
    .progress-bar {
      background-color: #efcc0a;
    }
  }

  &.step_5 {
    .progress-bar {
      background: linear-gradient(
          332deg,
          rgba(255, 0, 0, 1) 0%,
          rgba(255, 154, 0, 1) 10%,
          rgba(208, 222, 33, 1) 20%,
          rgba(79, 220, 74, 1) 30%,
          rgba(63, 218, 216, 1) 40%,
          rgba(47, 201, 226, 1) 50%,
          rgba(28, 127, 238, 1) 60%,
          rgba(95, 21, 242, 1) 70%,
          rgba(186, 12, 248, 1) 80%,
          rgba(251, 7, 217, 1) 90%,
          rgba(255, 0, 0, 1) 100%
      );
    }
  }
}

@keyframes loading-background-flow {
  0% {
    background-position-x: 0;
  }
  100% {
    background-position-x: 3em;
  }
}

</style>
