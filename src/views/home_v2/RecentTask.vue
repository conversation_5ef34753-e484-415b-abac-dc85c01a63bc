<template>
  <el-row :gutter="20" class="mt-8">
    <el-col>
      <el-card class="no-border">
        <div class="title">Recent Tasks</div>
        <task-dashboard recentTasks />
        <el-button>
          <router-link :to="'/task_dashboard'">
            View All
          </router-link>
        </el-button>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import TaskDashboard from '@/views/client/task-dashboard/index.vue'

export default {
  name: 'ClientTaskDashboard',
  components: {
    TaskDashboard,
  },
  props: {},
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.task-status-dropdown {
  white-space: nowrap;
  font-size: 12px;
}

.task-priority-dropdown {
  white-space: nowrap;
  font-size: 12px;
}

.title {
  text-align: center;
  color: #333333;
  font-weight: bold;
  padding-bottom: 4px;
  text-decoration: underline;
  text-underline-position: under;
  text-decoration-color: #d4d4d4;
}
</style>
