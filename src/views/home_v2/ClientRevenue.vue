<template>
  <el-row :gutter="20" class="mt-8">
    <el-col>
      <el-card class="no-border">
        <div class="title">Client Revenue</div>
        <client-revenue-dashboard />
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import ClientRevenueDashboard from '@/views/client/client-revenue-dashboard/index.vue'

export default {
  name: 'ClientRevenue',
  components: {
    ClientRevenueDashboard,
  },
}
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  color: #333333;
  font-weight: bold;
  padding-bottom: 8px;
  text-decoration: underline;
  text-underline-position: under;
  text-decoration-color: #d4d4d4;
}
</style>
