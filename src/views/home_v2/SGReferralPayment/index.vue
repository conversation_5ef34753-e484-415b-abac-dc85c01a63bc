<template>
  <el-row :gutter="20" class="mt-8">

    <!-- CN payment -->
    <el-col>
      <el-card class="no-border">
        <div class="title">Referral Payment</div>
        <div class="filter-container">
          <div class="mt-normal mb-normal">
            <refresh-button class="filter-item" @action="getList" />
            <el-select
              v-model="searchQuery.from_advisor_id"
              filterable
              clearable
              remote
              placeholder="Referrer"
              class="remote-select filter-item"
              :loading="searchLoading"
              :remote-method="searchAdvisor"
              no-data-text="No matching data"
              @change="handleSearch"
            >
              <el-option
                v-for="item in advisor_list"
                :key="item.source.id"
                :label="item.source.name"
                :value="item.source.id">
                {{ item.source.name }} <span class="info pl-5">{{ item.source.id }}</span>
              </el-option>
            </el-select>
            <el-select
              v-model="searchQuery.to_advisor_id"
              filterable
              clearable
              remote
              placeholder="Recommended Advisor"
              class="remote-select"
              :loading="searchLoading"
              :remote-method="searchAdvisor"
              no-data-text="No matching data"
              @change="handleSearch"
            >
              <el-option
                v-for="item in advisor_list"
                :key="item.source.id"
                :label="item.source.name"
                :value="item.source.id">
                {{ item.source.name }} <span class="info pl-5">{{ item.source.id }}</span>
              </el-option>
            </el-select>
            <!--            <el-input-->
            <!--              v-model="searchQuery.task_id"-->
            <!--              class="filter-item w-200px"-->
            <!--              placeholder="Task ID"-->
            <!--              @input="handleSearch()" />-->
            <!--            <el-checkbox v-model="searchQuery.is_cn_task" class="filter-item">CN Task</el-checkbox>-->
            <SGReferralFee class="filter-item pull-right" @update="getList" />
          </div>

        </div>
        <payment-list v-loading="loading"
                      :table-data="tableData" />

        <pagination
          :total="total"
          :page.sync="searchQuery.page"
          :limit.sync="searchQuery.size"
          @pagination="getList"
        />
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import API from '@/api/payment'
import Pagination from '@/components/Pagination/index'
import PaymentList from './paymentList'
import RefreshButton from '@/components/RefreshButton/index'
import SGReferralFee from './SGReferralFee'
import { mapGetters, mapState } from 'vuex'
import ConsultantAPI from '@/api/consultant'

export default {
  name: 'Payment',
  components: { RefreshButton, Pagination, PaymentList, SGReferralFee },
  data() {
    return {
      searchLoading: false,
      loading: false,
      total: 0,
      searchQuery: {
        page: 1,
        size: 20,
        to_advisor_id: undefined,
        from_advisor_id: undefined,
        task_id: undefined,
        is_cn_task: false,
        extra: [
          'from_advisor',
          'to_advisor',
          'payments',
        ].join(),
      },
      tableData: [],
      advisor_list: [],
    }
  },
  computed: {
    ...mapGetters(['uid']),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  mounted() {
    this.searchAdvisor()
    this.getList()
  },
  methods: {
    getList() {
      const params = Object.assign({}, this.searchQuery)
      params['create_by_id'] = this.uid
      this.searchQuery.is_cn_task
        ? params.cn_first_task_id = this.searchQuery.task_id
        : params.first_task_id = this.searchQuery.task_id

      delete params.is_cn_task
      delete params.task_id

      this.loading = true
      return API.getReferralPaymentList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },

    searchAdvisor(query = '') {
      const params = {
        page: 1,
        size: 15,
      }

      if (query) {
        params['name'] = query
      }

      this.searchLoading = true
      return ConsultantAPI.searchConsultant(params).then(data => {
        this.advisor_list = data.list
      }).finally(() => {
        this.searchLoading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">

::v-deep .el-select input {
  min-height: 28px;
}

.title {
  text-align: center;
  color: #333333;
  font-weight: bold;
  padding-bottom: 4px;
  text-decoration: underline;
  text-underline-position: under;
  text-decoration-color: #d4d4d4;
}
</style>
