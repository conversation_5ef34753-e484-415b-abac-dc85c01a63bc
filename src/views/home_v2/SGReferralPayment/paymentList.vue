<template>
  <el-table
    ref="paymentTable"
    border
    stripe
    :data="tableData"
  >
    <el-table-column label="ID" width="60">
      <template slot-scope="{row}">
        <span v-if="row.payments.length">{{ row.payments[0].id }}</span>
      </template>
    </el-table-column>
    <el-table-column label="Task ID" width="70">
      <template slot-scope="{row}">{{ row.first_task_id }}</template>
    </el-table-column>
    <el-table-column label="CN Task ID" width="100">
      <template slot-scope="{row}">{{ row.cn_first_task_id }}</template>
    </el-table-column>
    <el-table-column label="Referrer">
      <template v-if="scope.row.from_advisor_id" slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.from_advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.from_advisor.id,}}"
        >
          {{ scope.row.from_advisor['name_prefix'] || ''}}
          {{ scope.row.from_advisor['full_name'] }}
          <span class="info pl-5"> {{ scope.row.from_advisor.id }} </span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Recommended Advisor">
      <template v-if="scope.row.to_advisor_id" slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.to_advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.to_advisor.id,}}"
        >
          {{ scope.row.to_advisor['name_prefix'] || ''}}
          {{ scope.row.to_advisor['full_name'] }}
          <span class="info pl-5"> {{ scope.row.to_advisor.id }} </span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Fee">
      <template slot-scope="scope">
        {{ scope.row.fee }}
      </template>
    </el-table-column>
    <el-table-column label="Currency">
      <template slot-scope="scope">
        {{ scope.row.currency }}
      </template>
    </el-table-column>
    <el-table-column label="Status">
      <template slot-scope="scope">
        <span v-if="scope.row.payments.length">{{ scope.row.payments[0].status | getLabel(options) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="Create Time">
      <template slot-scope="scope">
        {{ scope.row.create_at | momentFormat() }}
      </template>
    </el-table-column>

  </el-table>
</template>

<script>
export default {
  name: 'PaymentList',
  props: {
    tableData: Array,
  },
  data() {
    return {
      options: [
        {
          value: 'INITIAL',
          label: 'Initial',
        }, {
          value: 'ADVISOR_CONFIRMED',
          label: 'Advisor Confirmed',
        }, {
          value: 'ADVISOR_CONFIRMING',
          label: 'Advisor Confirming',
        },
        {
          value: 'PAYING',
          label: 'Paying',
        }, {
          value: 'PAY_FAILED',
          label: 'Pay Failed',
        }, {
          value: 'PAID',
          label: 'Paid',
        }],
    }
  },

}
</script>

<style scoped lang="scss">
</style>
