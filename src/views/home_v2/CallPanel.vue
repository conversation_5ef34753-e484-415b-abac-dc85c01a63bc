<template>
  <div>
    <el-row :gutter="20"
            class="callPanel-row">

      <!-- Call Pending -->
      <el-col :span="8"
              class="callPanel-item">
        <el-card class="no-border callPanel-card">
          <div class="title">Call Pending</div>
          <call-pending />
        </el-card>
      </el-col>

      <!-- Call Scheduled -->
      <el-col :span="8"
              class="callPanel-item">
        <el-card class="no-border callPanel-card">
          <div class="title">Call Scheduled</div>
          <call-scheduled />
        </el-card>
      </el-col>

      <!-- Call Pending Close -->
      <el-col :span="8"
              class="callPanel-item">
        <el-card class="no-border callPanel-card">
          <div class="title">Call Pending Close</div>
          <call-pending-close />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CallPending from '@/views/home_v2/components/CallPending'
import CallScheduled from '@/views/home_v2/components/CallScheduled'
import CallPendingClose from '@/views/home_v2/components/CallPendingClose'

export default {
  name: 'CallPanel',
  components: { CallPendingClose, CallScheduled, CallPending },
}
</script>

<style lang="scss" scoped>
.callPanel-row {
  display: flex;
  align-items: stretch;

  .callPanel-item {

    .callPanel-card {
      height: 100%;

      .title {
        text-align: center;
        color: #333333;
        font-weight: bold;
        padding-bottom: 4px;
        text-decoration: underline;
        text-underline-position: under;
        text-decoration-color: #d4d4d4;
      }
    }
  }
}
</style>
