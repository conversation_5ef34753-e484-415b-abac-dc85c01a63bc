<template>
  <div class="app-container">
    <!-- Row 1 – Workload -->
    <workload v-if="has_permission" class="mb-normal" />

    <!-- Row 2 -->
    <call-panel v-if="has_permission" />

    <!-- Row 3 -->
    <Payment v-if="has_permission" />

    <!-- Row 4 -->
    <SGReferralPayment v-if="isSGDB && has_permission" />

    <!-- Row 5 -->
    <RecentTask v-if="has_am_permission" />

    <!-- Row 6 -->
    <ClientRevenue v-if="has_am_permission" />
  </div>
</template>

<script>
import Workload from '@/views/home_v2/Workload'
import CallPanel from '@/views/home_v2/CallPanel'
import Payment from '@/views/home_v2/components/Payment'
import SGReferralPayment from '@/views/home_v2/SGReferralPayment'
import RecentTask from '@/views/home_v2/RecentTask.vue'
import ClientRevenue from '@/views/home_v2/ClientRevenue.vue'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'HomeV2',
  components: { CallPanel, Workload, Payment, SGReferralPayment, RecentTask, ClientRevenue },
  computed: {
    ...mapGetters(['roles']),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    has_permission() {
      return this.roles.some(item => ['SRM, RM, SRA, CA', 'Research Associate', 'Admin', 'PH', 'Survey', 'PH_Survey', 'PH_Survey_Manager'].includes(item.role.name))
    },
    has_am_permission() {
      return this.roles.some(item => ['AM / Sales', 'Admin'].includes(item.role.name))
    },
  },
}
</script>
