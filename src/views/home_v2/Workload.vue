<template>
  <div v-if="displayWorkload">
    <div class="title">
      Workload
      <span v-if="!isLoading">- {{ progressPercentage }}%</span>
    </div>
    <work-progress class="flex-1 bottom-bar"
                   :loading="isLoading"
                   :target="progress.target"
                   :current="progress.client_hours"
                   :role="progress.role" />
    <!--    <div style="position: absolute; right: 0; top: 0;">-->
    <!--      <div>workload value:</div>-->
    <!--      <input v-model.number="progress.client_hours"-->
    <!--             type="range"-->
    <!--             min="0"-->
    <!--             max="60">-->
    <!--    </div>-->
  </div>
</template>

<script>
import WorkProgress from '@/views/home_v2/components/WorkProgress'
import { AppHttpService } from '@/utils/request'

export default {
  name: 'Workload',
  components: { WorkProgress },
  data() {
    return {
      isLoading: false,
      progress: {
        target: 0,
        client_hours: 0,
        role: this.getUserRole(),
      },
    }
  },
  computed: {
    displayWorkload() {
      return !!this.progress.role
    },
    progressPercentage() {
      const value = this.progress.client_hours / this.progress.target * 100
      const remainder = this.progress.target % this.progress.client_hours
      return remainder ? value?.toFixed(2) : value
    },
  },
  mounted() {
    this.loadWorkload()
  },
  methods: {
    loadWorkload() {
      this.isLoading = true
      return AppHttpService
        .get('/users/kpi/client_hour_indicator1/me')
        .then(data => {
          this.progress.target = data['expect_client_hours']
          this.progress.client_hours = data['client_hours']
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    getUserRole() {
      const position_id = this.$store.getters.positionId

      /* 职位规则 */
      // Research Associate -> RA
      // Senior Research Associate -> SRA
      // Research Manager -> Manager
      const isRA = position_id === 3
      const isSRA = position_id === 2
      const isManager = position_id === 1

      let role

      if (isRA) {
        role = 'RA'
      }
      if (isSRA) {
        role = 'SRA'
      }
      if (isManager) {
        role = 'Manager'
      }

      // return role
      return 'RA' || role
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  color: #3a3a3a;
  font-weight: bold;
  letter-spacing: 0.3px;
  margin-bottom: -12px;
}

.bottom-bar {
  width: 100%;
  --border-radius: 3px;
  --border-width: 1px;
  margin-top: 16px;
  border: var(--border-width) solid #b0b0b0;
  box-shadow: inset 0 3px 5px 0 #d3d0d0;
  border-radius: var(--border-radius);
}
</style>
