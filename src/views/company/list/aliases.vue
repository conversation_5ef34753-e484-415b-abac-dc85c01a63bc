<template>
  <div>
    <el-table
      ref="companyTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      row-class-name="hover"
    >
      <el-table-column
        prop="alias"
        label="Alias"
        min-width="130" />
      <el-table-column
        prop="company"
        label="Company"
        min-width="130">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.company.name"
            :to="{ name:'CompanyDetail', params: { company_id: scope.row.company_id}}">
            {{ scope.row.company.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="company.industry"
        label="Industry"
        min-width="100">
        <template slot-scope="scope">
          {{ scope.row.company.industry | getLabel(company_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="type"
        label="Type"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.company.type | getLabel(company_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="classification"
        label="Classification"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.company.classification | getLabel(company_options.classification_type) }}
        </template>
      </el-table-column>
      <el-table-column label="Stock Exchange" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.company.exchange || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="Stock Code" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.company.stock_code || '-' }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total"
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import CompanyAPI from '@/api/company'
import Mixins from '../mixins/list'
import RouteTools from '@/utils/tool-router'

export default {
  name: 'Aliases',
  mixins: [Mixins],
  data() {
    return {
      tabName: 'aliases',
      permission: false,
      searchQuery: {
        page: 1,
        size: 10,
        alias_contains: null,
        name_or_stock_code_contains: undefined,
      },
      extra: [
        'company',
      ].join(),
    }
  },

  methods: {
    getList() {
      this.loading = true
      this.$emit('searchQuery', this.searchQuery)
      this.searchQuery.extra = this.extra
      RouteTools.saveQueryToRouter({ tab: this.tabName, ...this.searchQuery })
      // this.searchQuery.alias_contains = this.searchQuery.name_or_stock_code_contains

      const params = this.formatParams(this.searchQuery)
      return CompanyAPI.getCompanyAliasesList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    formatParams(searchQuery) {
      return {
        'alias_contains': searchQuery.name_or_stock_code_contains,
        extra: this.extra,
        is_banned: true,
        page: searchQuery.page,
        size: searchQuery.size,
      }
    },
  },
}
</script>

<style scoped>

</style>
