<template>
  <div>
    <el-table
      ref="companyTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      row-class-name="hover"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange">
      <el-table-column
        column-key="name"
        prop="name"
        sortable="column"
        label="English Name"
        min-width="130">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name:'CompanyDetail', params: { company_id: scope.row.id }}">
            {{ scope.row.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        column-key="chinese_name"
        prop="chinese_name"
        sortable="column"
        label="Chinese Name"
        min-width="130">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.chinese_name"
            class="text-truncate link"
            :title="scope.row.chinese_name"
            :to="{ name:'CompanyDetail', params: { company_id: scope.row.id }}">
            {{ scope.row.chinese_name }}
          </router-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        :filters="company_options.industry_filters"
        :filter-multiple="false"
        column-key="industry"
        prop="industry"
        label="Industry"
        min-width="100">
        <template slot-scope="scope">
          {{ scope.row.industry | getLabel(company_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        :filters="company_options.type_filters"
        :filter-multiple="false"
        column-key="type"
        prop="type"
        label="Type"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(company_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        :filters="company_options.classification"
        :filter-multiple="false"
        column-key="classification"
        prop="classification"
        label="Classification"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.classification | getLabel(company_options.classification_type) }}
        </template>
      </el-table-column>
      <el-table-column label="Stock Exchange" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.exchange || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="Stock Code" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.stock_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        :filters="company_options.black_status_filters"
        :filter-multiple="false"
        column-key="banned"
        prop="banned"
        label="Status"
        min-width="180">
        <template slot-scope="scope">
          <span style="display: inline-block;width:50px">{{ scope.row.blacklist_record ? 'Blacklist' : 'TBA' }}</span>
          <ban-btn
            v-if="permission"
            class="hover-visible hover-visible-inline"
            :disabled="!permission"
            :company="scope.row" />
        </template>
      </el-table-column>
      <el-table-column label="Created By" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.creator ? scope.row.creator.name : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        column-key="create_at"
        prop="create_at"
        sortable="column"
        label="Created Time"
        min-width="130">
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total"
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import moment from 'moment-timezone'
import { contractMomentFormat } from '@/utils/tool'
import CompanyAPI from '@/api/company'
import RouteTools from '@/utils/tool-router'
import Mixins from '../mixins/list'

export default {
  name: 'BasicList',
  mixins: [Mixins],
  data() {
    return {
      tabName: 'basic',
      permission: false,
      searchQuery: {
        page: 1,
        size: 10,
        banned: undefined,
        name_or_stock_code_contains: undefined,
      },
      extra: [
        'blacklist_record.operator',
        'create_user_id',
        'creator',
        'aliases',
      ].join(),
    }
  },
  mounted() {
    this.getPermission()
  },
  methods: {
    getPermission() {
      this.permission = this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
    getList() {
      this.loading = true
      this.$emit('searchQuery', this.searchQuery)
      this.searchQuery.extra = this.extra
      RouteTools.saveQueryToRouter({ tab: this.tabName, ...this.searchQuery })

      const params = this.formatParams(this.searchQuery)
      return CompanyAPI.getCompanyList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
    formatParams(searchQuery) {
      const params = {}
      const filterKeys = ['ban_time_lte', 'ban_time_gte']

      for (const key in searchQuery) {
        if (Object.hasOwnProperty.call(searchQuery, key)) {
          let target_value = searchQuery[key]
          let target_key = key

          if (!searchQuery[key] || filterKeys.includes(key)) {
            target_value = undefined
          }

          if (key === 'banned') {
            if (searchQuery[key] === 'Blacklist') {
              target_key = 'is_banned'
              target_value = true
            } else if (searchQuery[key] === 'TBA') {
              target_key = 'is_banned'
              target_value = false
            } else if (searchQuery[key]) {
              target_key = 'is_banned'
              target_value = undefined
            }
          }

          if (key === 'create_at_lte' && searchQuery[key]) {
            target_value = moment.tz(searchQuery[key], this.timeZone)
              .toISOString()
          }

          if (key === 'create_at_gte' && searchQuery[key]) {
            target_value = contractMomentFormat(moment(searchQuery[key])
              .format('YYYY-MM-DD'), 'start')
          }

          params[target_key] = target_value
        }
      }

      return params
    },
  },
}
</script>
