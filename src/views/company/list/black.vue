<template>
  <div>
    <el-table
      ref="companyTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange">
      <el-table-column
        column-key="company.name"
        prop="company.name"
        sortable="column"
        label="English Name"
        min-width="130">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.company.name"
            :to="{ name:'CompanyDetail', params: { company_id: scope.row.company_id }}">
            {{ scope.row.company.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        column-key="company.chinese_name"
        prop="company.chinese_name"
        sortable="column"
        label="Chinese Name"
        min-width="130">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.company.chinese_name"
            class="text-truncate link"
            :title="scope.row.company.chinese_name"
            :to="{ name:'CompanyDetail', params: { company_id: scope.row.company_id }}">
            {{ scope.row.company.chinese_name }}
          </router-link>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="Industry">
        <template slot-scope="scope">
          {{scope.row.industry | getLabel(company_options.industry) }}
        </template>
      </el-table-column> -->
      <el-table-column
        label="Aliases"
        min-width="125">
        <template slot-scope="scope">
          {{ getCompanyAliases(scope.row.company.aliases) }}
        </template>
      </el-table-column>
      <el-table-column
        column-key="company.type"
        :filters="company_options.type_filters"
        :filter-multiple="false"
        label="Type"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.company.type | getLabel(company_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        :filters="company_options.classification"
        :filter-multiple="false"
        column-key="company.classification"
        prop="classification"
        label="Classification"
        min-width="125">
        <template slot-scope="scope">
          {{ scope.row.company.classification | getLabel(company_options.classification_type) }}
        </template>
      </el-table-column>
      <el-table-column
        column-key="company.blacklist.create_at"
        prop="company.create_at"
        sortable="column"
        label="Ban Date"
        min-width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.company.blacklist_record"
          >{{
            scope.row.company.blacklist_record.create_at |
              momentFormat('MM/DD/YYYY HH:mm A')
          }}</span>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column label="Reason" min-width="130">
        <template slot-scope="scope">
          <span v-if="scope.row"> {{ scope.row.reason }}</span>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column label="File" min-width="150">
        <template slot-scope="scope">
          <a
            class="text-truncate link"
            :title="scope.row.file_name"
            @click="downloadFile(scope.row.file_url, scope.row.file_name)"
          >{{ scope.row.file_name }}</a>
          <el-upload
            v-loading="scope.row.uploadLoading"
            accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
            :show-file-list="false"
            :action="upload_url"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :data="{'uid':uid}">
            <div class="flex">
              <el-button size="xs" type="success" icon="el-icon-upload" @click="actionCheckItem(scope.row)">
                Upload
              </el-button>
            </div>
          </el-upload>
        </template>
      </el-table-column>
      <el-table-column label="Operator" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row &&
            scope.row.operator"
          >{{ scope.row.operator.name }}</span>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="total"
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import moment from 'moment-timezone'
import { contractMomentFormat, fileUrlDownload } from '@/utils/tool'
import CompanyAPI from '@/api/company'
import RouteTools from '@/utils/tool-router'
import Mixins from '../mixins/list'

export default {
  name: 'BlackList',
  mixins: [Mixins],
  data() {
    return {
      tabName: 'black',
      searchQuery: {
        page: 1,
        size: 10,
      },
      extra: [
        'operator',
        'company.creator',
        'company.aliases',
        'company.blacklist_record',
        'create_user_id',
        'creator',
      ].join(),
      checkItem: undefined,
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/blacklist',
      uploadLoading: false,
      file_maxSize: 20,
    }
  },
  methods: {
    getList() {
      this.loading = true
      this.$emit('searchQuery', this.searchQuery)
      this.searchQuery.extra = this.extra
      RouteTools.saveQueryToRouter({ tab: this.tabName, ...this.searchQuery })

      const params = this.formatParams(this.searchQuery)
      return CompanyAPI.getCompanyBlackList(params)
        .then(data => {
          this.tableData = data.list.map(item => {
            item.uploadLoading = false

            return item
          })
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
    actionCheckItem(row) {
      this.checkItem = row
    },
    handleSuccess(response) {
      if (response.msg === 'ok') {
        this.updateCompany(response.data)
      } else {
        this.$message(response.message)
        this.checkItem.uploadLoading = false
      }
    },
    updateCompany(response) {
      return CompanyAPI.updateCompany({
        id: this.checkItem.company_id,
        blacklist_record: {
          id: this.checkItem.company.blacklist_record.id,
          file_name: response.file_name,
          file_url: response.url,
        },
      })
        .then(data => {
          this.checkItem.file_name = response.file_name
          this.checkItem.file_url = response.url
          // this.getList()
        })
        .finally(() => {
          this.checkItem.uploadLoading = false
        })
    },
    beforeUpload(file) {
      this.checkItem.uploadLoading = true
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    downloadFile(url, fileName) {
      fileUrlDownload(url, fileName)
    },

    getCompanyAliases(list) {
      if (list.length < 1) {
        return 'N/A'
      }
      return list.map(item => item.alias).join(', ')
    },

    formatParams(searchQuery) {
      const params = {}
      const filterKeys = ['create_at_gte', 'create_at_lte', 'ban_time_lte', 'ban_time_gte']
      const nameKeys = ['name_or_stock_code_contains']

      for (const key in searchQuery) {
        if (Object.hasOwnProperty.call(searchQuery, key)) {
          let target_value = searchQuery[key]
          const target_key = key
          if (nameKeys.includes(key) && target_value) {
            params['company.' + target_key] = target_value
          }

          if (key === 'ban_time_lte' && searchQuery[key]) {
            target_value = moment.tz(searchQuery[key], this.timeZone).toISOString()
            params['blacklist.create_at_lte'] = target_value
          }

          if (key === 'ban_time_gte' && searchQuery[key]) {
            target_value = contractMomentFormat(moment(searchQuery[key]).format('YYYY-MM-DD'), 'start')
            params['blacklist.create_at_gte'] = target_value
          }

          if (!searchQuery[key] || filterKeys.includes(key) || nameKeys.includes(key)) {
            target_value = undefined
          }

          params[target_key] = target_value
        }
      }

      return params
    },
  },
}
</script>
