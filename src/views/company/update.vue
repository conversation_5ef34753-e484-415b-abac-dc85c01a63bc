<template>
  <div :class="{'app-container': !isEdit && !isDialog}">
    <el-form
      ref="form"
      :class="{'form-data': !isDialog}"
      :model="form"
      :rules="rules"
      label-width="140px">
      <el-form-item label="English Name" prop="name">
        <el-input ref="name" v-model="form.name" />
      </el-form-item>
      <el-form-item label="Chinese Name" prop="chinese_name">
        <el-input ref="chinese_name" v-model="form.chinese_name" />
      </el-form-item>
      <el-form-item label="Industry" prop="industry">
        <el-select v-model="form.industry" filterable placeholder="Search">
          <el-option
            v-for="item in company_options.industry"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Classification " prop="classification ">
        <el-select v-model="form.classification" placeholder="Search">
          <template v-for="item in company_options.classification_type">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="Type" prop="type">
        <el-select v-model="form.type" filterable placeholder="Search">
          <template v-for="item in company_options.type">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item v-if="permission" label="Aliases" prop="aliases">
        <company-alias :aliases.sync="form.aliases" />
      </el-form-item>
      <el-form-item label="Stock Exchange"
                    prop="exchange"
                    :required="['LISTED', 'PART_OF_LISTED_GROUP'].includes(form.type)">
        <el-select v-model="form.exchange" clearable filterable>
          <el-option v-for="item in options.stock_exchanges"
                     :key="item"
                     :label="item"
                     :value="item" />
        </el-select>

        <!-- <el-input v-model="form.exchange" /> -->
      </el-form-item>
      <el-form-item label="Stock Code"
                    prop="stock_code"
                    :required="['LISTED', 'PART_OF_LISTED_GROUP'].includes(form.type)">
        <el-input v-model="form.stock_code" />
      </el-form-item>
      <el-form-item v-if="!isEdit">
        <el-button type="primary" icon="el-icon-check" @click="createCompany">Save</el-button>
        <el-button v-if="!isDialog" type="text" @click="onSave()">Save & New</el-button>
      </el-form-item>
      <el-form-item v-else>
        <el-button type="primary" icon="el-icon-check" :loading="submitting" @click="onSave()">Save</el-button>
        <el-button type="text" @click="$emit('save')">Cancel</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import CompanyAPI from '@/api/company'
import ComplianceAPI from '@/api/compliance'
import { mapGetters } from 'vuex'
import Mixin from '@/components/FormPage/mixin'
import CompanyAlias from './components/CompanyAlias'
import _ from 'lodash'

export default {
  name: 'CreateCompany',
  components: { CompanyAlias },
  mixins: [Mixin],
  props: {
    isDialog: Boolean,
    isEdit: Boolean,
    data: Object,
  },
  data() {
    const listedValidator = (rule, value, callback) => {
      const dict = {
        'exchange': 'stock exchange',
        'stock_code': 'stock code',
      }
      const vm = this
      setTimeout(() => {
        if (['LISTED', 'PART_OF_LISTED_GROUP'].includes(vm.form.type) && !value) {
          callback(new Error(`${dict[rule.field]} is required`))
        } else {
          callback()
        }
      }, 100)
    }

    return {
      form: this.initForm(),
      permission: false,
      rules: {
        name: [{ required: true, trigger: 'change', message: 'english name is required' }],
        industry: [{ required: true, trigger: 'change' }],
        type: [{ required: true, trigger: 'change' }],
        exchange: [{ validator: listedValidator, trigger: 'change' }],
        stock_code: [{ validator: listedValidator, trigger: 'change' }],
      },
      options: {
        stock_exchanges: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'company_options',
      'roles',
    ]),
  },
  mounted() {
    this.permission = this.roles.some(role => {
      return [1, 4].includes(role.role_id)
    })
    this.getStockExchangesList()
    this.handleData()
  },
  methods: {
    initForm() {
      return {
        name: '',
        classification: null,
        chinese_name: '',
        industry: '',
        type: '',
        exchange: '',
        stock_code: '',
        aliases: [
          { alias: '', match_options: ['MATCH_WORD', 'IGNORE_CASE'] },
        ],
      }
    },
    getStockExchangesList() {
      return ComplianceAPI.getAppConfigEntriesByKey('STOCK_EXCHANGE')
        .then(data => {
          if (data) {
            const stock_exchanges = data.value.replaceAll(/[,，]/g, '').split('\n').map(str => str.trim())
            this.options.stock_exchanges = _.sortBy(stock_exchanges)
          }
        })
    },
    actionAddAlias() {
      this.form.aliases.push({ alias: '', match_options: ['MATCH_WORD', 'IGNORE_CASE'] })
    },
    handleData() {
      if (this.data) {
        for (const key in this.form) {
          if (Object.hasOwnProperty.call(this.data, key)) {
            this.form[key] = this.data[key]
          }
        }
        if (this.form.aliases.length < 1) this.actionAddAlias()
        this.form.id = this.data.id
      }
    },
    handleSave(val) {
      val.aliases = val.aliases.filter(item => item.alias.trim())
      if (this.isEdit) {
        return CompanyAPI.updateCompany(val).then(() => {
          this.$emit('save', val)
        })
      } else {
        return CompanyAPI.createCompany(val).then(res => {
          this.$emit('create', res)
        })
      }
    },
    createCompany() {
      this.onSave(this.isDialog ? '' : 'CompanyList')
    },
  },
}
</script>

<style scoped>
.w-600px {
  width: 600px;
}
</style>
