<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-input
        v-model="searchQuery.name_or_stock_code_contains"
        style="width: 150px;"
        class="filter-item"
        placeholder="Name"
        @input="handleSearch" />
      <el-date-picker
        v-if="searchQuery.tab === 'basic'"
        v-model="searchQuery.create_at_gte"
        type="date"
        format="yyyy-MM-dd"
        class="filter-item w-120px"
        placeholder="Start Create Date"
        value-format="yyyy-MM-dd"
        @change="handleSearch" />
      <el-date-picker
        v-if="searchQuery.tab === 'basic'"
        v-model="searchQuery.create_at_lte"
        type="date"
        format="yyyy-MM-dd"
        class="filter-item w-120px"
        placeholder="End Create Date"
        value-format="yyyy-MM-dd"
        @change="handleSearch" />
      <el-date-picker
        v-if="searchQuery.tab === 'black'"
        v-model="searchQuery.ban_time_gte"
        type="date"
        format="yyyy-MM-dd"
        class="filter-item w-120px"
        placeholder="Start Ban Date"
        value-format="yyyy-MM-dd"
        @change="handleSearch" />
      <el-date-picker
        v-if="searchQuery.tab === 'black'"
        v-model="searchQuery.ban_time_lte"
        type="date"
        format="yyyy-MM-dd"
        class="filter-item w-120px"
        placeholder="End Ban Date"
        value-format="yyyy-MM-dd"
        @change="handleSearch" />
      <el-button
        class="filter-item"
        type="success"
        icon="el-icon-plus"
        @click="actionGotoCreate()">New
      </el-button>
    </div>

    <el-tabs v-model="searchQuery.tab" type="card" @tab-click="handleClick">
      <el-tab-pane label="Company List" name="basic">
        <basic-list @searchQuery="updateBasicSearchQuery" />
      </el-tab-pane>
      <el-tab-pane label="Do Not Contact List" name="black">
        <black-list @searchQuery="updateBlackSearchQuery" />
      </el-tab-pane>
      <el-tab-pane label="Aliases" name="aliases">
        <aliases-list @searchQuery="updateAliasesSearchQuery" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import { debounce } from '@/utils/tool'
import waves from '@/directive/waves'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'
import BasicList from './list/basic'
import BlackList from './list/black'
import AliasesList from './list/aliases'

export default {
  name: 'Company',
  components: {
    RefreshButton,
    BasicList,
    BlackList,
    AliasesList,
  },
  directives: { waves },
  data() {
    return {
      activeName: 'basic',
      searchQuery: {
        tab: 'basic',
        name_or_stock_code_contains: undefined,
        create_at_gte: null,
        create_at_lte: undefined,
        ban_time_gte: undefined,
        ban_time_lte: undefined,
      },
      tempSearchQuery: {
        basic: {},
        black: {},
      },
      tabClickFlag: {
        basic: 0,
        black: 0,
        aliases: 0,
      },
    }
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery, ['tab, page, size'])
    this.tabClickFlag[this.searchQuery.tab]++
    this.getList()
  },
  beforeDestroy() {
    this.$off()
  },
  methods: {
    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },
    handleClick() {
      if (!this.tabClickFlag[this.searchQuery.tab]) {
        this.getList()
      }

      this.$emit('tabClick', this.searchQuery.tab)
      this.searchQuery = Object.assign({ tab: this.searchQuery.tab }, this.tempSearchQuery[this.searchQuery.tab])
      this.tabClickFlag[this.searchQuery.tab]++
    },
    getList() {
      this.$emit('getList', this.searchQuery)
    },
    actionGotoCreate() {
      this.$router.push({ name: 'CreateCompany' })
    },
    updateBasicSearchQuery(val) {
      this.tempSearchQuery['aliases'] = Object.assign({ tab: 'basic' }, val)
    },
    updateBlackSearchQuery(val) {
      this.tempSearchQuery['black'] = Object.assign({ tab: 'black' }, val)
    },
    updateAliasesSearchQuery(val) {
      this.tempSearchQuery['aliases'] = Object.assign({ tab: 'aliases' }, val)
    },
  },
}
</script>

<style lang="scss" scoped>
.my-company {
  border: 1px solid #cfd4de;
  border-radius: 6px;
  padding: 20px 10px;
  margin-bottom: 10px;

  .item {
    margin: 5px 10px;
  }
}

::v-deep .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 160px;
}
</style>
