import { mapState, mapGetters } from 'vuex'
import RouteTools from '@/utils/tool-router'
import Pagination from '@/components/Pagination'
import BanBtn from '../components/BanBtn'

export default {
  components: {
    BanBtn,
    Pagination,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      itemData: null,
      total: 0,
      company_status_type: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'company_options',
      'roles',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },
  mounted() {
    this.$parent.$parent.$parent.$on('getList', ({ tab, is_banned, ...searchQuery }) => {
      if (tab === this.tabName) {
        Object.assign(this.searchQuery, searchQuery)
        this.initTable()
        this.getList()
      }
    })
    this.$parent.$parent.$parent.$on('tabClick', tab => {
      if (tab === this.tabName) {
        RouteTools.saveQueryToRouter({ tab: this.tabName, ...this.searchQuery })
      }
    })
  },
  methods: {
    initTable() {
      this.$refs.companyTable.clearSort()
      this.$refs.companyTable.clearFilter()
      this.searchQuery.sort = undefined
      this.searchQuery.type = undefined
    },
    handleSortChange({ order, prop }) {
      const dict = {
        'ascending': 'asc',
        'descending': 'desc',
      }

      if (!order) {
        this.searchQuery.sort = undefined
      } else {
        this.searchQuery.sort = `${prop} ${dict[order]}`
      }

      this.getList()
    },
    handleFilterChange(filters) {
      for (const key in filters) {
        if (Object.hasOwnProperty.call(filters, key)) {
          this.searchQuery[key] = filters[key].length ? filters[key][0] : undefined
        }
      }
      this.getList()
    },
  },
}
