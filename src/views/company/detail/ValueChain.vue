<template>
  <div v-loading="loading">
    <el-tree
      ref="valueChainTree"
      node-key="root_id"
      :default-expanded-keys="[1, 2, 3, 4, 5, 6, 7, 8]"
      :props="defaultProps"
      lazy
      :load="loadNode"
    >
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <b v-if="data.root_id">{{ node.label }}</b>
        <span v-else class="link">
          {{ node.label }}
          <router-link
            v-if="data.company"
            class="link ml-3"
            :to="{ name: 'CompanyValueChain', params: { company_id: data.company.id }}">
            <i class="el-icon-link" />
          </router-link>
        </span>
        <el-link
          v-if="data.root_id && data.root_id !== 4"
          type="primary"
          :underline="false"
          icon="el-icon-plus"
          @click.stop.native="openAppendDialog(node, data)">
          Append
        </el-link>
        <el-popconfirm
          v-if="data.company"
          icon="el-icon-info"
          icon-color="#F56C6C"
          :title="`Confirm to Delete: ${node.label}?`"
          cancel-button-text="Cancel"
          confirm-button-text="Confirm"
          confirm-button-type="danger"
          @confirm="deleteItem(node, data)"
        >
          <el-link
            slot="reference"
            type="danger"
            :underline="false"
            icon="el-icon-delete"
            @click.stop.native="">
            Delete
          </el-link>
        </el-popconfirm>
      </div>
    </el-tree>

    <append-company-dialog
      ref="valueChainCompanyDialog"
      :company="company"
      @append="afterAppendCompany"
    />
  </div>
</template>

<script>
import CompanyAPI from '@/api/company.js'
import AppendCompanyDialog from '@/views/company/detail/components/AppendCompanyDialog'

export default {
  name: 'CompanyValueChain',
  components: { AppendCompanyDialog },
  props: {
    company: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      loading: true,
      treeData: this.initFirstLevelTreeData(),
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'is_leaf',
      },

      currentNode: null,
    }
  },
  mounted() {
    this.getCompanyValueChain(this.$route.params.company_id, this.treeData)
  },
  methods: {
    initFirstLevelTreeData() {
      return [
        {
          root_id: 1,
          label: 'Competitors',
          value: 'competitors',
          children: [],
        },
        {
          root_id: 2,
          label: 'Customers',
          value: 'customers',
          children: [],
        },
        {
          root_id: 3,
          label: 'Suppliers',
          value: 'suppliers',
          children: [],
        },
        {
          root_id: 4,
          label: 'Distributors',
          value: 'distributors',
          children: [],
        },
        {
          root_id: 5,
          label: 'Other Related Companies',
          value: 'other_related',
          children: [],
        },
        {
          root_id: 6,
          label: 'Subsidiary',
          value: 'subsidiaries',
          children: [],
        },
      ]
    },
    getCompanyValueChain(company_id, treeData, resolve) {
      if (!resolve) {
        this.loading = true
      }
      const params = {
        sub_type_in: 'COMPETITOR,CUSTOMER,SUBSIDIARY,SUPPLIER,DISTRIBUTE_FOR,DISTRIBUTE_BY,OTHER',
        max_level: 2,
      }
      return CompanyAPI.getCompanyValueChain(company_id, params)
        .then(data => {
          for (const obj of treeData) {
            const key = obj.value
            if (key !== 'distributors') {
              obj.children = this.formatNodeChildren(data[key], company_id)
            } else {
              obj.children = [{
                root_id: 7,
                label: 'Distributes for',
                value: 'distributes_for',
                is_leaf: !data['distributes_for'].length,
                children: this.formatNodeChildren(data['distributes_for'], company_id),
              }, {
                root_id: 8,
                label: 'Distributed by',
                value: 'distributed_by',
                is_leaf: !data['distributed_by'].length,
                children: this.formatNodeChildren(data['distributed_by'], company_id),
              }]
            }
          }
          return resolve ? resolve(treeData) : treeData
        })
        .finally(() => {
          this.loading = false
        })
    },
    formatNodeChildren(val, company_id) {
      return val.map(item => {
        item.parent_company_id = company_id
        item.label = item.company.name
        const count_keys = [
          'competitors_count',
          'customers_count',
          'distributed_by_count',
          'distributes_for_count',
          'other_related_count',
          'subsidiaries_count',
          'suppliers_count',
        ]
        item.is_leaf = !count_keys.some(key => item[key])
        return item
      })
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve(this.treeData)
      }
      if (node.level >= 1) {
        if (node.data.company) {
          const treeData = this.initFirstLevelTreeData()
          this.getCompanyValueChain(node.data.company.id, treeData, resolve)
        } else {
          return resolve(node.data.children)
        }
        return resolve([])
      }
    },

    openAppendDialog(node) {
      this.currentNode = node
      this.$refs['valueChainCompanyDialog'].openDialog(node)
    },
    afterAppendCompany(val) {
      this.currentNode.data.children = this.formatNodeChildren(val, this.currentNode.data.parent_company_id)
      this.currentNode.loaded = false
      this.currentNode.expand()
    },
    deleteItem(node, data) {
      const nodeParentData = node.parent.data
      const index = nodeParentData.children.findIndex(item => item.company.id === data.company.id)
      if (index > -1) {
        nodeParentData.children.splice(index, 1)
      }
      const company_id = data.parent_company_id || this.company.id
      const params = {
        [nodeParentData.value]: nodeParentData.children.map(item => ({ id: item.company.id })),
      }
      return CompanyAPI.updateCompanyValueChain(company_id, params)
        .then(() => {
          node.parent.loaded = false
          node.parent.expand()
        })
    },
  },
}
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
