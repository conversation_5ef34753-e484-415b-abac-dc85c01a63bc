<template>
  <div class="components-container">
    <h2>{{ company.name }}</h2>

    <el-tabs
      v-if="!isGKMRole"
      v-model="activeName"
      type="card"
      @tab-click="tabClick"
    >
      <el-tab-pane
        v-for="item in tabs"
        :key="item.router_name"
        :name="item.router_name"
      >
        <span slot="label"> {{ item.label }}</span>
      </el-tab-pane>
    </el-tabs>

    <router-view
      v-if="isCompanyLoaded"
      :company.sync="company"
    />

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { RouteTools } from '@/utils/tool'
import CompanyAPI from '@/api/company.js'

export default {
  name: 'CompanyDetail',
  data() {
    return {
      company_id: this.$route.params.company_id,
      company: {
        creator: {},
      },
      isLoading: true,
      activeName: 'Profile',
      tabs: [
        { label: 'Profile', router_name: 'CompanyProfile' },
        { label: 'Value Chain', router_name: 'CompanyValueChain' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'company_options',
      'roles',
    ]),
    isCompanyLoaded() {
      return this.company && Object.prototype.hasOwnProperty.call(this.company, 'id')
    },
    isGKMRole() {
      return this.roles.find(item => item.role.name === 'GKM') && this.roles.length === 1
    },
  },
  watch: {
    $route: {
      handler(to) {
        this.activeName = to.name
      },
      immediate: true,
    },
  },
  mounted() {
    this.getCompanyData()
  },
  methods: {
    getCompanyData() {
      this.isLoading = true
      return CompanyAPI.getCompany(this.company_id)
        .then(data => {
          this.company = data
          return RouteTools.setRouterMetaTitle(data.name, this.$route)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    tabClick(tab) {
      this.$router.push({
        name: tab.name,
      })
    },
  },
}
</script>

<style scoped>
</style>
