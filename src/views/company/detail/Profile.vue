<template>
  <div>
    <el-form
      v-if="!editCompany"
      label-position="right"
      label-width="150px">
      <el-form-item label="English Name">{{ company.name || '-' }}</el-form-item>
      <el-form-item label="Chinese Name">{{ company.chinese_name || '-' }}</el-form-item>
      <el-form-item label="Industry">{{ company.industry | getLabel(company_options.industry) }}</el-form-item>
      <el-form-item label="Type">{{ company.type | getLabel(company_options.type) }}</el-form-item>
      <el-form-item label="Aliases">
        <div
v-for="(item,index) in getCompanyAliases(company.aliases)"
             :key="index"
             v-html="$sanitizeHtml(item)" />
      </el-form-item>
      <el-form-item label="Classification">
        {{ company.classification | getLabel(company_options.classification_type) }}
      </el-form-item>
      <el-form-item label="Stock Exchange">{{ company.exchange || '-' }}</el-form-item>
      <el-form-item label="Stock Code">{{ company.stock_code || '-' }}</el-form-item>
      <el-form-item label="Status">
        <span class="mr-8">{{ company.blacklist_record ? 'Blacklist' : 'TBA' }}</span>
        <ban-btn v-if="permission" :company.sync="company" :disabled="!permission" />
      </el-form-item>
      <el-form-item
        v-if="company.blacklist_record"
        label="Black List Time">
        {{ company.blacklist_record.create_at| momentFormat() }}
      </el-form-item>
      <el-form-item label="Created By">{{ company.creator ? company.creator.name : '-' }}</el-form-item>
      <el-form-item label="Created Date">{{ company.create_at | momentFormat }}</el-form-item>
      <el-form-item>
        <el-button
          v-if="!isGKMRole"
          size="mini"
          icon="el-icon-edit"
          @click="editCompany=true"
        >
          Edit
        </el-button>
      </el-form-item>
    </el-form>
    <edit-company
v-else
                  :data="company"
                  is-edit
                  style="max-width: 700px;"
                  @save="updateDisplay" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Filters from '@/utils/filters'
import EditCompany from '../update'
import BanBtn from '../components/BanBtn'

export default {
  name: 'CompanyProfile',
  components: {
    EditCompany,
    BanBtn,
  },
  props: {
    company: {
      type: Object,
      default() {
        return {
          creator: {},
        }
      },
    },
  },
  data() {
    return {
      permission: false,
      editCompany: false,
    }
  },
  computed: {
    ...mapGetters([
      'company_options',
      'roles',
    ]),

    // GKM角色 只能看看专家的基本资料 和搜索专家
    isGKMRole() {
      return this.roles.find(item => item.role.name === 'GKM') && this.roles.length === 1
    },
  },
  mounted() {
    this.getPermission()
  },
  methods: {
    getPermission() {
      this.permission = this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
    updateDisplay(data) {
      const company = {}
      Object.assign(company, this.company, data)
      this.$emit('update:company', company)
      this.editCompany = false
    },

    getCompanyAliases(list) {
      if (!list || list.length < 1) {
        return ['N/A']
      }
      return list.map(item => {
        const match_text = item.match_options.map(i => Filters.transformKey(i.toLowerCase()))
        const format_match_text = match_text.length ? `<span class="info"> (${match_text})</span>` : ''
        return item.alias + format_match_text
      })
    },
  },
}
</script>

<style scoped>
</style>
