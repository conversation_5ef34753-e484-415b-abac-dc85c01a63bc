<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="Append"
    width="400px"
    @close="form.company_ids = []">
    <company-search
      v-model="form.company_ids"
      class="w-100"
      placeholder="Search Company"
      multiple
      clearable />
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="dialogVisible = false">
        Cancel
      </el-button>
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!form.company_ids.length"
        @click="appendItem"
      >
        Append
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import CompanyAPI from '@/api/company.js'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'

export default {
  name: 'AppendCompanyDialog',
  components: { CompanySearch },
  props: {
    company: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      currentNode: null,
      form: {
        company_ids: [],
      },
    }
  },
  methods: {
    openDialog(node) {
      this.currentNode = node
      this.dialogVisible = true
    },
    async appendItem() {
      try {
        this.loading = true
        // 更新Relation数据
        const company_id = this.currentNode.parent.data?.company
          ? this.currentNode.parent.data.company.id
          : this.company.id
        const origin_company_ids = this.currentNode.data.children.map(item => item.company.id)
        const relation_type = this.currentNode.data.value
        const params = {
          [relation_type]: [...new Set([...origin_company_ids, ...this.form.company_ids])].map(
            id => ({ id })),
        }
        await CompanyAPI.updateCompanyValueChain(company_id, params)
        // 重新获取Relation并更新节点视图
        const relationData = await CompanyAPI.getCompanyValueChain(company_id, {
          sub_type_in: 'COMPETITOR,CUSTOMER,SUBSIDIARY,SUPPLIER,DISTRIBUTE_FOR,DISTRIBUTE_BY,OTHER',
          max_level: 2,
        })
        this.dialogVisible = false
        this.$emit('append', relationData[relation_type])
      } finally {
        this.loading = false
      }
    },
  },
}
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
