<template>
  <div v-if="aliases.length">
    <div
      v-for="(item,index) in aliases"
      :key="index"
      class="mb-8"
    >
      <el-input v-model="item.alias" type="text" class="mr-normal w-150px inline-block" />
      <el-checkbox-group v-model="item.match_options" class="inline-block">
        <el-checkbox label="MATCH_WORD">Match Word</el-checkbox>
        <el-checkbox label="IGNORE_CASE">Ignore Case</el-checkbox>
      </el-checkbox-group>
      <span class="pl-5">
        <el-link v-if="aliases.length > 1"
                 :underline="false"
                 type="danger"
                 @click="actionDeleteKey(index)">
          <el-icon name="minus" />
        </el-link>
        <el-link v-if="aliases.length === index+1 "
                 :underline="false"
                 type="primary"
                 @click="actionAddAlias">
          <el-icon name="plus" />
        </el-link>
      </span>
    </div>
  </div>
</template>

<script>

export default {
  name: 'CompanyAlias',
  props: {
    aliases: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      form: {
        aliases: [
          {
            alias: '',
            match_options: ['MATCH_WORD', 'IGNORE_CASE'],
          },
        ],
      },
    }
  },
  mounted() {
    this.aliases.length < 1 ? this.actionAddAlias() : ''
  },
  methods: {
    actionAddAlias() {
      this.aliases.push({ alias: '', match_options: ['MATCH_WORD', 'IGNORE_CASE'] })
    },
    actionDeleteKey(index) {
      this.aliases.splice(index, 1)
    },
  },
}
</script>

<style scoped>

</style>
