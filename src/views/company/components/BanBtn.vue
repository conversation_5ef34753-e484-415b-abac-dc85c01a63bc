<template>
  <div class="inline-block">
    <el-button
      v-if="!company.blacklist_record"
      type="danger"
      size="xs"
      v-bind="$attrs"
      v-on="$listeners"
      @click="showBanDialog(company)">
      <svg-icon icon-class="ban" />
      Ban
    </el-button>
    <el-button
      v-if="company.blacklist_record"
      type="warning"
      size="xs"
      @click="actionUnblockCompany('Unblock')">
      <svg-icon icon-class="permission" />
      Unblock
    </el-button>

    <el-dialog
      title="Ban Company"
      append-to-body
      :visible.sync="banDialog"
      :show-close="false"
      :close-on-click-modal="false"
      width="30%">
      <company-alias :aliases.sync="form.key_list" class="mb-8" />
      <el-input
        v-model="form.banReason"
        class="mb-8"
        type="textarea"
        :rows="6"
        placeholder="Reason" />
      <el-upload
        accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
        :action="upload_url"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :data="{'uid':uid}"
        :limit="1"
        :file-list="fileList">
        <div class="flex">
          <el-button
            size="xs"
            type="success"
            icon="el-icon-upload">
            Upload
          </el-button>
          <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
        </div>
      </el-upload>
      <span slot="footer">
        <el-button type="text" size="mini" @click="hideBanDialog">Cancel</el-button>
        <el-button type="danger" size="mini" @click="actionBanCompany">OK</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { fileUrlDownload } from '@/utils/tool'
import { mapGetters } from 'vuex'
import CompanyAPI from '@/api/company.js'
import CompanyAlias from '@/views/company/components/CompanyAlias'
import _ from 'lodash'

export default {
  name: 'BanBtn',
  components: { CompanyAlias },
  props: {
    company: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      banDialog: false,
      form: {
        banReason: '',
        file_url: '',
        file_name: '',
        key_list: [
          { 'alias': '', match_options: ['MATCH_WORD', 'IGNORE_CASE'] },
        ],
      },
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/blacklist',
      file_maxSize: 20,
      fileList: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  methods: {
    actionBanCompany() {
      this.updateCompany({
        id: this.company.id,
        aliases: this.form.key_list.filter(item => item.alias.trim()),
        blacklist_record: {
          reason: this.form.banReason,
          operator_uid: this.uid,
          file_url: this.form.file_url,
          file_name: this.form.file_name,
        },
      })
        .then(data => {
          this.hideBanDialog()
          Object.assign(this.company, {
            blacklist_record: {
              reason: this.form.banReason,
              operator_uid: this.uid,
              create_at: new Date(),
            },
            aliases: this.form.key_list.filter(item => item.alias.trim()),
          })
        })
    },
    actionUnblockCompany(status) {
      this.confirmDialog(status)
        .then(() => {
          return CompanyAPI.deleteBlacklistOne(this.company)
        })
        .then(data => {
          Object.assign(this.company, {
            blacklist_record: undefined,
          })
        })
    },
    confirmDialog(status) {
      return this.$confirm(`Confirm ${status}?`, 'Warning', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
        cancelButtonClass: 'el-button--text',
      })
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    handleSuccess(response) {
      if (response.msg === 'ok') {
        this.form.file_name = response.data.file_name
        this.form.file_url = response.data.url
      } else {
        this.$message(response.message)
      }
    },
    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`)
      }
    },
    handleRemove(file, fileList) {
      this.form.file_name = ''
      this.form.file_url = ''
    },
    updateCompany(data) {
      return CompanyAPI.updateCompany(data)
    },
    hideBanDialog() {
      this.banDialog = false
    },
    showBanDialog(company) {
      if (company.blacklist_record) {
        this.form.banReason = company.blacklist_record.reason
      }
      company.aliases.length > 0 ? this.form.key_list = _.cloneDeep(company.aliases) : ''
      this.banDialog = true
    },
    downloadFile(url, fileName) {
      fileUrlDownload(url, fileName)
    },
  },
}
</script>
