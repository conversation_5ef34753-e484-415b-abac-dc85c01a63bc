<template>
  <div class="app-container">
    <el-tabs v-model="active_tab" type="card" @tab-click="tabClick">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name" />
    </el-tabs>

    <router-view />
  </div>
</template>

<script>
export default {
  name: 'Permission',
  data() {
    return {
      tabs: [{
        name: 'PermissionRoles',
        label: 'Roles',
      }, {
        name: 'PermissionRouter',
        label: 'Router',
      }],
    }
  },
  computed: {
    active_tab: {
      get: function() {
        return this.$route.name
      },
      set: function() {
        return this.$route.name
      },
    },
  },
  methods: {
    tabClick(tab) {
      this.$router.push({
        name: tab.name,
      })
    },
  },
}
</script>

<style scoped>

</style>
