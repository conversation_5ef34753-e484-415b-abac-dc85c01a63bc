<template>
  <el-dialog
    :title="title"
    width="700px"
    v-bind="$attrs"
    @open="openDialog"
    @closed="closeDialog"
    v-on="$listeners"
  >
    <el-form
      ref="permission-form"
      :model="form"
      :rules="rules"
      :disabled="loading"
      label-width="120px"
      class="permissions-form">
      <div v-if="updateMode">
        <el-form-item label="ID" prop="id">
          {{form.id}}
        </el-form-item>
      </div>
      <el-form-item label="Name" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="Value" prop="value">
        <el-input v-model="form.value" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="actionCancel">Cancel</el-button>
      <el-button type="primary" :loading="loading" @click="actionSubmit">Save</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AuthorityAPI from '@/api/authority'

export default {
  name: 'CreateUpdateRouter',
  props: {
    options: Array,
    data: Object,
  },
  data() {
    return {
      loading: false,
      checkAll: false,
      isIndeterminate: true,
      form: {
        name: '',
        value: '',
      },
      rules: {
        name: [{ required: true, trigger: 'change' }],
        value: [{ required: true, trigger: 'change' }],
      },
    }
  },
  computed: {
    updateMode() {
      return !!this.data
    },
    title() {
      return this.updateMode ? 'Update' : 'Create'
    },
  },
  mounted() {
  },
  methods: {
    openDialog() {
      if (!this.data) return
      Object.assign(this.form, this.data)
    },
    closeDialog() {
      this.form = {
        name: '',
        value: '',
      }
      this.$refs['permission-form'].resetFields()
    },
    actionCancel() {
      this.$emit('cancel')
    },
    actionSubmit() {
      this.$refs['permission-form'].validate((valid) => {
        if (valid) {
          const data = {
            name: this.form.name,
            value: this.form.value,
          }
          const request = this.data
            ? AuthorityAPI.updatePermission(this.form.id, data)
            : AuthorityAPI.createPermission(data)

          this.loading = true
          return request.then(() => {
            this.$message({
              message: 'success',
              type: 'success',
            })
            this.$emit('done')
          }).finally(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.permission-form {
  max-height: calc(85vh - 150px);
  overflow-y: auto;
}
</style>
