<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="init" />
      <el-button type="success" class="filter-item" icon="el-icon-plus" @click="actionCreate">New</el-button>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="data"
      @row-click="actionUpdate"
    >
      <el-table-column label="Id" prop="id" width="55" />
      <el-table-column label="Name" prop="name" />
      <el-table-column label="Value" prop="value" />
      <el-table-column label="Action" width="100">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            :title="`Confirm do delete (name: ${scope.row.name})?`"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="actionDelete(scope.row, scope.$index)"
          >
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click.stop />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <create-update
      :visible.sync="dialogVisible"
      :data="selectedItem"
      @cancel="dialogVisible = false"
      @done="dialogDone"
    />
  </div>
</template>

<script>
import AuthorityAPI from '@/api/authority'
import CreateUpdate from './components/CreateUpdate'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'PermissionRouter',
  components: { RefreshButton, CreateUpdate },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      data: [],
      selectedItem: null,
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.loading = true
      return AuthorityAPI
        .getPermissionsList()
        .then(data => {
          this.data = data
        })
        .finally(() => {
          this.loading = false
        })
    },
    actionCreate() {
      this.selectedItem = null
      this.dialogVisible = true
    },
    actionUpdate(row) {
      this.selectedItem = row
      this.dialogVisible = true
    },
    async actionDelete(row, index) {
      await AuthorityAPI
        .deletePermission(row.id)
      this.data.splice(index, 1)
      this.$message({
        message: 'success',
        type: 'success',
      })
    },
    dialogDone() {
      this.dialogVisible = false
      return this.init()
    },
  },
}
</script>

<style scoped>

</style>
