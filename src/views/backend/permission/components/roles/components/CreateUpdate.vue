<template>
  <el-dialog
    :title="title"
    width="700px"
    v-bind="$attrs"
    @open="openDialog"
    @closed="closeDialog"
    v-on="$listeners"
  >
    <el-form ref="role-form" :model="form" :rules="rules" :disabled="loading" label-width="120px" class="roles-form">
      <el-form-item label="Name" prop="name">
        <el-input v-model="form.name" :disabled="!!form.id" />
      </el-form-item>
      <el-form-item label="Permissions" prop="permissions_id">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          -- Select All --
        </el-checkbox>
        <el-checkbox-group v-model="form.permissions_id" @change="handleCheckedChange">
          <el-checkbox
            v-for="item in options"
            :key="item.id"
            :label="item.id"
            style="display: block;">
            {{item.name}}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="actionCancel">Cancel</el-button>
      <el-button type="primary" :loading="loading" @click="actionSubmit">Submit</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AuthorityAPI from '@/api/authority'

export default {
  name: 'CreateUpdateRole',
  props: {
    options: Array,
    data: Object,
  },
  data() {
    return {
      loading: false,
      checkAll: false,
      isIndeterminate: true,
      form: {
        name: '',
        permissions: [],
        permissions_id: [],
      },
      rules: {
        name: [{ required: true, trigger: 'change' }],
        permissions_id: [{ required: true, message: 'Permission is required', trigger: 'change' }],
      },
    }
  },
  computed: {
    updateMode() {
      return !!this.data
    },
    title() {
      return this.updateMode ? 'Update' : 'Create'
    },
  },
  mounted() {
  },
  methods: {
    openDialog() {
      if (!this.data) return
      Object.assign(this.form, this.data)
      this.handleCheckedChange(this.form.permissions_id)
    },
    closeDialog() {
      this.form = {
        name: '',
        permissions: [],
        permissions_id: [],
      }
      this.$refs['role-form'].resetFields()
    },
    handleCheckAllChange(val) {
      this.form.permissions_id = val ? this.options.map(item => item.id) : []
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      this.checkAll = val.length === this.options.length
      this.isIndeterminate = val.length > 0 && val.length < this.options.length
    },
    actionCancel() {
      this.$emit('cancel')
    },
    actionSubmit() {
      this.$refs['role-form'].validate((valid) => {
        if (valid) {
          const data = {
            name: this.form.name,
            permissions: this.form.permissions_id.map(item => ({ permission_id: item })),
          }
          const request = this.data ? AuthorityAPI.updateRole(this.form.id, data) : AuthorityAPI.createRole(data)
          this.loading = true
          return request.then(() => {
            this.$message({
              message: 'success',
              type: 'success',
            })
            this.$emit('done')
          }).finally(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.roles-form {
  max-height: calc(85vh - 150px);
  overflow-y: auto;
}
</style>
