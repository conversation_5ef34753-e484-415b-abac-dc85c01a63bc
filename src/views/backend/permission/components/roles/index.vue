<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="init" />
      <el-button class="filter-item" type="success" icon="el-icon-plus" @click="actionCreateRole">New</el-button>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="data"
      @row-click="actionUpdateRole"
    >
      <el-table-column label="Id" prop="id" width="55" />
      <el-table-column label="Name" prop="name" />
      <el-table-column label="Action" width="100">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            :title="`Are you sure to delete this role (name: ${scope.row.name})?`"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="actionDeleteRole(scope.row, scope.$index)"
          >
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click.stop />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <create-update
      :visible.sync="dialogVisible"
      :options="options.permissions"
      :data="selectedItem"
      @cancel="dialogVisible = false"
      @done="dialogDone"
    />
  </div>
</template>

<script>
import AuthorityAPI from '@/api/authority'
import CreateUpdate from './components/CreateUpdate'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'PermissionRoles',
  components: { RefreshButton, CreateUpdate },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      options: {
        permissions: [],
      },
      data: [],
      selectedItem: null,
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.loading = true
      return Promise.all([this.getRolesList(), this.getPermissionsList()]).finally(() => {
        this.loading = false
      })
    },
    async getRolesList() {
      const data = await AuthorityAPI.getRolesList()
      this.data = data.map(item => {
        item.permissions_id = item.permissions.map(permission => permission.permission_id)
        return item
      })
    },
    async getPermissionsList() {
      this.options.permissions = await AuthorityAPI.getPermissionsList()
    },
    actionCreateRole() {
      this.selectedItem = null
      this.dialogVisible = true
    },
    actionUpdateRole(row) {
      this.selectedItem = row
      this.dialogVisible = true
    },
    async actionDeleteRole(row, index) {
      await AuthorityAPI.deleteRole(row.id)
      this.data.splice(index, 1)
      this.$message({
        message: 'success',
        type: 'success',
      })
    },
    dialogDone() {
      this.dialogVisible = false
      this.loading = true
      return this.getRolesList().finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
