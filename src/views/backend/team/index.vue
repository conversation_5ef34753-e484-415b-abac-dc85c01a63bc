<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-input
        v-model="searchQuery.name_contains"
        class="w-200px filter-item"
        placeholder="Name"
        @input="handleSearch"
      />
      <el-button type="success" class="filter-item" icon="el-icon-plus" @click="actionCreate">New</el-button>
    </div>
    <el-table v-loading="loading"
              :data="tableData"
              border
              stripe>
      <el-table-column label="ID" prop="id" width="50" />
      <el-table-column label="Name" prop="name">
        <template slot-scope="scope">
          <router-link
            :key="scope.row.id"
            class="text-truncate link"
            :to="{ name:'TeamDetail', params: { id: scope.row.id }}">
            {{ scope.row.name || '' }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Manager" prop="manager">
        <template slot-scope="scope">
          <span v-if="scope.row.manager_id"> {{ scope.row.manager.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Description" prop="description" />
      <el-table-column label="Actions" width="80">
        <template slot-scope="scope">
          <el-link
            type="primary"
            size="mini"
            icon="el-icon-edit"
            :underline="false"
            @click="actionUpdate(scope.row)"
          />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            :title="`Confirm do remove ${scope.row.name}?`"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="actionRemove(scope.row.id)"
          >
            <el-link
              slot="reference"
              class="ml-3"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click.stop />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <el-dialog :visible.sync="isDialogVisible"
               width="600px"
               title="Create Team">
      <el-form ref="form"
               :rules="rules"
               :model="form"
               label-width="120px">
        <el-form-item label="Name" prop="name">
          <el-input v-model="form.name" required />
        </el-form-item>
        <el-form-item label="Manager" prop="manager_id">
          <user-search v-model="form.manager_id"
                       placeholder="Manger" />
        </el-form-item>
        <el-form-item label="Description" prop="description">
          <el-input v-model="form.description" type="textarea" :row="3" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="submitDisabled" @click="actionSubmit">Submit</el-button>
        <el-button type="text" @click="actionCancel">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import userAPI from '@/api/user'
import RefreshButton from '@/components/RefreshButton/index'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import Pagination from '@/components/Pagination'
import { debounce } from '@/utils/tool'

export default {
  name: 'Index',
  components: {
    Pagination,
    RefreshButton,
    UserSearch,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchQuery: {
        name_contains: undefined,
        page: 1,
        size: 20,
        type: 'EMPLOYEE_TEAM',
        extra: 'team_members.user,manager',
      },
      isDialogVisible: false,
      form: {
        name: null,
        manager_id: null,
        type: 'EMPLOYEE_TEAM',
        description: '',
      },
      rules: {
        name: [{ required: true, trigger: 'blur' }],
        type: [{ required: true, trigger: 'blur' }],
      },
      type_list: [
        { label: 'AM', value: 'AM' },
        { label: 'Employee Team', value: 'EMPLOYEE_TEAM' },
        { label: 'Other', value: 'OTHER' },
      ],
    }
  },

  computed: {
    submitDisabled() {
      return !this.form.name || !this.form.name.trim()
    },
  },

  mounted() {
    this.getList()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      debounce(() => {
        this.getList()
      }, 500)
    },
    getList() {
      this.loading = true
      const params = this.searchQuery
      return userAPI.getUserTeamList(params).then(data => {
        this.tableData = data.list
        this.total = data.count
      }).finally(() => {
        this.loading = false
      })
    },
    actionUpdate(item) {
      this.form = item
      this.isDialogVisible = true
    },
    actionCreate() {
      this.initForm()
      this.isDialogVisible = true
    },

    actionCancel() {
      this.initForm()
      this.isDialogVisible = false
    },

    initForm() {
      this.form = {
        name: null,
        type: 'EMPLOYEE_TEAM',
        description: '',
      }
    },

    actionSubmit() {
      if (!this.form.name.trim()) {
        return
      }
      const request = this.form.id ? userAPI.updateUserTeam(this.form) : userAPI.addUserTeam(this.form)
      return request.then(data => {
        this.$message.success('success')
      }).finally(() => {
        this.getList()
        this.isDialogVisible = false
      })
    },

    actionRemove(id) {
      return userAPI.deleteUserTeam(id).then(data => {
        this.$message.success('success')
      }).finally(() => {
        this.getList()
      })
    },
  },
}
</script>

<style scoped>

</style>
