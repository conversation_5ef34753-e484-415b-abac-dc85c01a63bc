<template>
  <div class="app-container">
    <div class="team_name"><span class="id-font"># {{ searchQuery.team_id }}</span> {{ team_name }}</div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-input
        v-model="searchQuery.name_like"
        class="w-200px filter-item"
        placeholder="Member Name"
        @input="handleSearch"
      />

      <el-button type="success" class="filter-item" icon="el-icon-plus" @click="actionAdd">Add</el-button>
    </div>
    <el-table v-loading="loading"
              :data="tableData"
              border
              stripe>
      <el-table-column label="UID" prop="uid" width="80">
        <template slot-scope="scope">
          {{ scope.row.user.id }}
        </template>
      </el-table-column>
      <el-table-column label="Name">
        <template slot-scope="scope">
          {{ scope.row.user.name }}
        </template>
      </el-table-column>
      <el-table-column label="CN User">
        <template slot-scope="scope">
          {{ scope.row.user.is_cn | yesOrNo('Yes') }}
        </template>
      </el-table-column>
      <el-table-column label="Email">
        <template slot-scope="scope">
          {{ scope.row.user.personal_email }}
        </template>
      </el-table-column>
      <el-table-column label="Actions" width="80">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            :title="`Confirm do remove ${scope.row.user.name}?`"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="actionRemove(scope.row.user)"
          >
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click.stop />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <el-dialog :visible.sync="isDialogVisible"
               width="600px"
               :title="'Add members to '+ team_name">
      <el-form ref="form"
               :model="form"
               label-width="80px">
        <el-form-item label="Names" prop="name">
          <user-search
            v-model="form.ids"
            clearable
            class="multiple-select"
            multiple />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="!form.ids.length" @click="actionSubmit">Submit</el-button>
        <el-button type="text" @click="actionCancel">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import userAPI from '@/api/user'
import RefreshButton from '@/components/RefreshButton/index'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import Pagination from '@/components/Pagination'
import { debounce } from '@/utils/tool'
import _ from 'lodash'

export default {
  name: 'TeamDetail',
  components: {
    Pagination,
    RefreshButton,
    UserSearch,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchQuery: {
        team_id: this.$route.params.id,
        name_like: undefined,
        page: 1,
        size: 20,
        extra: 'user,team',
      },
      team_name: '',
      isDialogVisible: false,
      form: {
        ids: [],
      },
    }
  },

  mounted() {
    this.getList()
  },
  methods: {
    handleSearch() {
      this.searchQuery.page = 1
      debounce(() => {
        this.getList()
      }, 500)
    },
    getList() {
      this.loading = true
      const params = _.cloneDeep(this.searchQuery)
      params['user.name_like'] = params.name_like
      return userAPI.getTeamMembers(params).then(data => {
        this.tableData = data.list
        this.total = data.count
        !this.team_name ? this.team_name = data.list[0].team.name : ''
      }).finally(() => {
        this.loading = false
      })
    },
    actionAdd() {
      this.isDialogVisible = true
    },

    actionCancel() {
      this.isDialogVisible = false
    },

    actionSubmit() {
      const params = this.form.ids.map(item => {
        return { member_id: item }
      })
      return userAPI.addTeamMember(this.searchQuery.team_id, params).then(data => {
        this.$message.success('success')
      }).finally(() => {
        this.getList()
        this.isDialogVisible = false
      })
    },

    actionRemove(user) {
      const params = [{ member_id: user.id }]
      return userAPI.deleteTeamMember(this.searchQuery.team_id, params).then(data => {
        this.$message.success('success')
      }).finally(() => {
        this.getList()
      })
    },
  },
}
</script>

<style scoped>
.team_name {
  font-weight: 600;
  font-size: 24px;
  margin-bottom: 10px;
}

.id-font {
  color: #409eff;
}
</style>
