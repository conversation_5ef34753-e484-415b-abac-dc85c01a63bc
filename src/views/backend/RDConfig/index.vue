<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="searchQuery.type"
        clearable
        filterable
        placeholder="Type"
        class="filter-item w-200px"
        @change="handleSearch"
      >
        <el-option
          v-for="item in type_options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="searchQuery.key"
        clearable
        placeholder="Key"
        class="filter-item w-200px"
        @change="handleSearch"
      />
      <el-button
        type="success"
        icon="el-icon-plus"
        :disabled="!isRD"
        class="filter-item"
        @click="actionEdit()"
      >
        New
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      stripe
      border
      class="headerFixed w-100"
      row-class-name="cursor-pointer"
      :data="config_data"
      @row-click="actionEdit"
    >
      <el-table-column
        label="Type"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.type }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Key"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.key }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Value"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.value }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :visible.sync="editDialogVisible"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="System Setting"
      width="35%"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="70px">
        <el-form-item label="Type" prop="type">
          <el-select v-model="form.type">
            <el-option
              v-for="item in type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Key" prop="key">
          <el-input v-model="form.key" />
        </el-form-item>
        <el-form-item label="Value" prop="value">
          <el-select v-if="['LEADS_EMAIL_VENDOR','ALLOW_OUTREACH_USER_LEADS_EMAIL_VENDOR'].includes(form.key)" v-model="form.value">
            <el-option
              v-for="item in vendor_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
          <el-input v-else v-model="form.value" type="textarea" :rows="5" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addConfig()">Save</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ComplianceAPI from '@/api/compliance'
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/tool'

export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      editDialogVisible: false,
      config_data: [],
      type_options: [
        { label: 'EMAIL_ADDRESS', value: 'EMAIL_ADDRESS' },
        { label: 'GLOBAL_LEGAL_SETTING', value: 'GLOBAL_LEGAL_SETTING' },
        { label: 'SYSTEM', value: 'SYSTEM' },
        { label: 'OTHER', value: 'OTHER' },
      ],
      vendor_options: [
        { label: 'Postmark', value: 'POSTMARK' },
        { label: 'Resend', value: 'RESEND' },
        { label: 'Mailgun', value: 'MAILGUN' },
        { label: 'Mailjet', value: 'MAILJET' },
        { label: 'Postmark + Resend A/B Test', value: 'POSTMARK_RESEND_RANDOM' },
        { label: 'Postmark + Mailgun A/B Test', value: 'POSTMARK_MAILGUN_RANDOM' },
        { label: 'Postmark + Mailjet A/B Test', value: 'POSTMARK_MAILJET_RANDOM' },
      ],
      form: this.initForm(),
      rules: {
        'key': [
          {
            required: true, validator: (rule, value, callback) => {
              if (!value && this.isDialogVisible) {
                callback(new Error('key is required'))
              } else {
                callback()
              }
            },
            trigger: ['change'],
          }],
        'value': [
          {
            required: true, validator: (rule, value, callback) => {
              if (!value && this.isDialogVisible) {
                callback(new Error('value is required'))
              } else {
                callback()
              }
            },
            trigger: ['change'],
          }],
      },
      searchQuery: {
        type: '',
        key: '',
      },
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    isRD() {
      return this.roles.filter(item => item.role_id === 7).length
    },
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      this.loading = true
      const params = {
        type: this.searchQuery.type || undefined,
        key: this.searchQuery.key || undefined,
      }
      return ComplianceAPI.getGlobalSetting(params).then(data => {
        this.config_data = data
      }).finally(() => {
        this.loading = false
      })
    },

    handleSearch() {
      debounce(() => {
        this.getData()
      }, 500)
    },

    actionEdit(item) {
      if (!this.isRD) return
      this.form = item ? Object.assign(this.form, item) : this.initForm()
      this.editDialogVisible = true
    },

    addConfig() {
      return ComplianceAPI.updateAllowedToSendBeforeTCSigned({ 'value': this.form.value, 'type': this.form.type },
        this.form.key).then(() => {
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
        this.getData()
      }).finally(() => {
        this.actionCancel()
      })
    },

    actionCancel() {
      this.editDialogVisible = false
      this.form = this.initForm()
    },

    initForm() {
      return {
        type: null,
        key: null,
        value: null,
      }
    },
  },
}
</script>

<style scoped>

</style>
