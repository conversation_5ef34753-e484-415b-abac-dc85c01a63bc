<template>
  <div>
    <el-button type="success" icon="el-icon-plus" @click="actionCreateUser">Create</el-button>

    <el-dialog :visible.sync="isDialogVisible" width="800px">
      <div slot="title">
        <span v-if="updateMode">Update</span>
        <span v-else>Create</span>
        User
      </div>
      <el-form
ref="form"
               v-loading="isLoading"
               :model="form"
               :rules="rules"
               label-width="150px"
               :disabled="isSubmitting">
        <el-form-item v-if="updateMode" label="ID">
          {{ form.id }}
        </el-form-item>
        <el-form-item label="Username" prop="username">
          <el-input v-model="form.username" />
        </el-form-item>
        <el-form-item label="Role" prop="roles">
          <el-checkbox-group v-model="form.roles">
            <el-checkbox v-for="item in options.role_list" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Department" prop="is_cn">
          <el-radio-group v-model="form.is_cn">
            <el-radio :label="false">US Team</el-radio>
            <el-radio :label="true">CN Team</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="Position" prop="position_id">
          <el-select
v-model="form.position_id"
                     filterable>
            <el-option
v-for="item in options.position_list"
                       :key="item.id"
                       :label="item.name"
                       :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Team">
          <el-select
            v-model="form.team_ids"
            class="filter-item w-200px"
            placeholder="Team"
            clearable
            multiple
            remote
            filterable
            :remote-method="getTeamList"
          >
            <el-option
v-for="item in options.team_list"
                       :key="item.id"
                       :label="item.name"
                       :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="Email" prop="email">
          <el-input v-model="form.email" type="email" />
        </el-form-item>
        <el-form-item label="Capvision-Pro Email" prop="outreach_email">
          <el-input v-model="form.outreach_email" type="email" />
        </el-form-item>
        <el-form-item label="Personal Email" prop="personal_email">
          <el-input v-model="form.personal_email" type="email" />
        </el-form-item>
        <el-form-item label="Name" prop="given_name">
          <el-input v-model="form.given_name" class="w-120px" placeholder="Given name" />
          <el-input v-model="form.family_name" class="w-120px" placeholder="Family name" />
        </el-form-item>

        <div v-if="!updateMode">
          <el-form-item label="Time zone">
            <el-select
v-model="form.zone_id_string"
                       clearable
                       filterable>
              <el-option v-for="item in options.timezone_list" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </div>

        <div v-if="updateMode">
          <el-form-item label="Avatar">
            <el-upload
              class="avatar-uploader"
              accept=".png, .jpg, .jpeg"
              :action="upload_url"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload">
              <div slot="tip">Click to upload</div>
              <div v-loading="avatar_loading" class="avatar">
                <el-avatar v-if="form.picture" shape="square" :size="80" fit="contain" :src="form.picture" />
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="Gender">
            <el-radio v-model="form.gender" label="1">Male</el-radio>
            <el-radio v-model="form.gender" label="2">Female</el-radio>
          </el-form-item>
          <el-form-item label="Phone number">
            <el-input v-model="form.phone_number" />
          </el-form-item>
          <el-form-item label="Language">
            <el-radio v-model="form.language_code" label="en">en</el-radio>
            <el-radio v-model="form.language_code" label="zh">zh</el-radio>
          </el-form-item>
          <el-form-item label="Locale">
            <el-radio v-model="form.locale" label="en-US">US</el-radio>
            <el-radio v-model="form.locale" label="zh-CN">China</el-radio>
          </el-form-item>
          <el-form-item label="Time zone">
            <el-select
v-model="form.zone_id_string"
                       clearable
                       filterable>
              <el-option v-for="item in options.timezone_list" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="Blocked">
            <el-checkbox v-model="form.is_blocked">Blocked</el-checkbox>
          </el-form-item>
        </div>
        <el-form-item label="Signature Role" prop="signature">
          <tinymce ref="signature_content" v-model="form.signature" :height="100" :toolbar="toolbar" p-origin-margin />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <fieldset :disabled="isSubmitting">
          <el-button type="primary" icon="el-icon-check" @click="saveData()">
            <span v-if="updateMode">Update</span>
            <span v-else>Save</span>
          </el-button>
        </fieldset>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import UserAPI from '@/api/user'
import AuthorityAPI from '@/api/authority'
import Mixin from '@/components/FormPage/mixin'
import Tool from '@/utils/tool'

export default {
  name: 'CreateUpdate',
  components: { Tinymce },
  mixins: [Mixin],
  data() {
    return {
      isDialogVisible: false,
      isLoading: true,
      avatar_loading: false,
      isSubmitting: false,
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      form: {},
      options: {
        timezone_list: this.$store.getters.timezone_list,
      },
      rules: {
        username: [{ required: true, trigger: 'blur' }],
        roles: [{ required: true, trigger: 'blur' }],
        position_id: [{ required: true, trigger: 'blur' }],
        email: [{ required: true, type: 'email', trigger: 'blur' }],
        outreach_email: [{ required: true, type: 'email', trigger: 'blur', message: 'Capvision-Pro Email is required' }],
        personal_email: [{ required: true, type: 'email', trigger: 'blur' }],
        given_name: [
          {
            required: true, validator: (rule, value, callback) => {
              if ((!value || !this.form.family_name) && this.isDialogVisible) {
                callback(new Error('Name is required'))
              } else {
                callback()
              }
            },
            trigger: 'change',
          }],
      },
      toolbar: ['undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
    }
  },
  computed: {
    updateMode() {
      return this.form && this.form.id
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.isLoading = true
      return this
        .loadOptions()
        .finally(() => {
          this.isLoading = false
        })
    },
    loadOptions() {
      return Promise.all([this.getRolesList(), this.getUserPositionList(), this.getTeamList()])
    },

    getRolesList() {
      return AuthorityAPI.getRolesList().then(data => {
        this.options.role_list = data
      })
    },

    getTeamList(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        page: 1,
        size: 15,
      }
      return UserAPI.getUserTeamList(params).then(data => {
        this.options.team_list = data['list']
      })
    },

    getUserPositionList() {
      return UserAPI.getUserPositionList().then(data => {
        this.options.position_list = data['list']
      })
    },

    initForm() {
      this.form = {
        username: '',
        email: '',
        outreach_email: '',
        personal_email: '',
        position_id: null,
        roles: [],
        is_cn: false,
        signature: '',
        team_ids: [],
        given_name: '',
        family_name: '',
        zone_id_string: Tool.timeZone,
      }
    },

    actionCreateUser() {
      this.initForm()
      this.isDialogVisible = true
    },
    handleAvatarSuccess(response) {
      if (response.msg === 'ok') {
        this.form.picture = response.data.url
        this.avatar_loading = false
      } else {
        this.$message(response.message)
      }
    },
    beforeAvatarUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          this.form.picture = ''
          this.avatar_loading = true
          return resolve(true)
        }
      })
    },
    saveData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const form = this.form

          const params_profile = Object.assign({}, form)
          params_profile.roles = params_profile.roles.map(item => {
            return {
              role_id: item,
            }
          })
          params_profile.team_members = params_profile.team_ids.map(item => {
            return {
              team_id: item,
            }
          })
          delete params_profile.permissions
          delete params_profile.team_ids

          this.isSubmitting = true

          return saveAPI(params_profile)
            .then(data => {
              this.isDialogVisible = false
              this.$emit('done', data)
              this.initForm()
            })
            .finally(() => {
              this.isSubmitting = false
            })
        }
      })

      function saveAPI(form) {
        return form.id
          ? UserAPI.updateUserProfile(form.id, form)
          : UserAPI.createUser(form)
      }

      // function updateUserRole(data, form) {
      //   const user_id = data.id
      //   const params = form.roles.map(item => {
      //     return {
      //       role_id: item,
      //     }
      //   })
      //
      //   return AuthorityAPI
      //     .updateUserRole(user_id, params)
      // }
    },

    update(data) {
      this.form = formatUser(Object.assign({}, data))
      if (this.$refs['signature_content']) {
        this.$refs['signature_content'].setContent(data.signature)
      }
      this.isDialogVisible = true

      function formatUser(data) {
        // roles 转换为可编辑 id_list
        data.roles = data.roles.map(item => {
          return item.role.id
        })
        data.team_ids = data.team_members.map(item => {
          return item.team_id
        })

        return data
      }
    }
    ,
  },
}
</script>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.avatar {
  width: 80px;
  height: 80px;
  display: block;
  border:1px dashed #d9d9d9;
  background-color: #fbfdff;
}
</style>
