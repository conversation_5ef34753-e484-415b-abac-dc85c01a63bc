<template>
  <div class="app-container">
    <el-row type="flex" justify="space-between" class="filter-container">
      <form @submit.prevent="searchData">
        <fieldset :disabled="isSubmitting">
          <refresh-button class="filter-item" @action="loadData" />
          <el-input v-model="form.keyword" class="w-200px filter-item" placeholder="Keywords" />
          <el-button type="primary" class="filter-item" native-type="submit" icon="el-icon-search">Search</el-button>
        </fieldset>
      </form>
      <create-update ref="createUpdate" class="filter-item" @done="loadData" />
    </el-row>
    <el-table
      :data="list"
      stripe
      class="w-100"
      row-class-name="cursor-pointer"
      @row-click="actionEditUser">
      <el-table-column fixed prop="id" label="ID" width="40" align="right" />
      <el-table-column fixed prop="picture" label="Avatar" width="60">
        <template slot-scope="scope">
          <el-image
            class="user-avatar"
            :src="scope.row.picture"
            fit="cover">
            <div slot="error" class="el-image__error text-center">-</div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column fixed prop="name" label="Name" width="180">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row['is_blocked']" size="mini" type="danger" effect="dark">Blocked</el-tag>
            {{ scope.row.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="is_cn" label="CN User">
        <template slot-scope="scope">
          {{ scope.row.is_cn | yesOrNo('Yes') }}
        </template>
      </el-table-column>
      <el-table-column prop="roles" label="Roles">
        <template slot-scope="scope">
          {{ getRuleHTML(scope.row.roles) }}
        </template>
      </el-table-column>
      <el-table-column prop="position_id" label="Position">
        <template v-if="scope.row.position_id" slot-scope="scope">
          {{ scope.row.position.name }}
        </template>
      </el-table-column>
      <el-table-column label="Team">
        <template v-if="scope.row.team_members.length" slot-scope="scope">
          <span v-for="(item,index) in scope.row.team_members" :key="index">
            <span v-if="item.team"><span v-if="index>0">, </span>{{ item.team.name }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="email" label="Email" min-width="200" />
      <el-table-column prop="outreach_email" label="Capvision-Pro Email" min-width="200" />
      <el-table-column prop="personal_email" label="Personal email" min-width="200" />
      <el-table-column prop="phone_number" label="Phone number" width="160">
        <template slot-scope="scope">
          <a :href="'tel:'+scope.row.phone_number">{{ scope.row.phone_number }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="locale" label="Locale" width="100" />
      <el-table-column prop="is_active" label="Active" width="70" align="center">
        <template slot-scope="scope">
          <b>{{ scope.row['is_active'] | yesOrNo('No') }}</b>
        </template>
      </el-table-column>
      <el-table-column prop="is_blocked" label="Blocked" width="70" align="center">
        <template slot-scope="scope">
          <b>{{ scope.row['is_blocked'] | yesOrNo('Yes') }}</b>
        </template>
      </el-table-column>
      <el-table-column prop="create_at" label="Create at" width="120">
        <template slot-scope="scope">
          <el-tooltip
            effect="dark"
            :content="scope.row.create_at | momentFormat('long')"
            placement="bottom-end">
            <span>{{ fromNow(scope.row.create_at) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="form.page"
      :limit.sync="form.size"
      :auto-scroll="false"
      @pagination="loadData"
    />
  </div>
</template>

<script>
import UserAPI from '@/api/user'
import moment from 'moment-timezone'
import Pagination from '@/components/Pagination'
import CreateUpdate from '@/views/backend/user/components/CreateUpdate'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'

export default {
  name: 'UserList',
  components: { RefreshButton, CreateUpdate, Pagination },
  data() {
    return {
      list: [],
      form: {
        username: undefined,
        email: undefined,
        keyword: undefined,
        page: 1,
        size: 20,
      },
      total: 0,
      isSubmitting: false,
    }
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.form)
    this.loadData()
  },
  methods: {
    searchData() {
      this.form.page = 1
      this.loadData()
    },

    loadData() {
      RouteTools.saveQueryToRouter(this.form)
      const params = {
        extra: ['roles', 'roles.role', 'position', 'team_members.team'].join(),
      }
      const form = this.form
      Object.keys(form).forEach(key => {
        const value = form[key]
        if (![undefined, ''].includes(value)) {
          params[key] = value
        }
      })

      this.isSubmitting = true

      return UserAPI
        .getUsersList(params)
        .then(data => {
          this.list = data.list
          this.total = data.count
        })
        .finally(() => {
          this.isSubmitting = false
        })
    },
    fromNow(data) {
      return moment(data).fromNow()
    },
    actionEditUser(data) {
      this.$refs.createUpdate.update(data)
    },
    getRuleHTML(data) {
      return data.map(item => {
        return item.role.name
      }).join(', ')
    },
  },
}
</script>

<style scoped>
.user-avatar {
  width: 40px;
  height: 40px;
  display: block;
}

fieldset {
  width: 400px;
}
</style>
