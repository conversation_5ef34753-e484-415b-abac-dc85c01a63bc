<template>
  <div class="app-container custom-flex-box">
    <div>
      <el-collapse-transition>
        <div v-show="!focusMode1" class="filter-container">
          <el-input
            v-model="query.searches[0].value"
            placeholder="Keywords"
            class="filter-item w-408px"
            @keyup.native.enter="search()"
          />
          <el-checkbox
            v-model="query.searches[1].value"
            class="filter-item"
            type="checkbox"
            @change="search()">Precise
          </el-checkbox>
          <el-checkbox
            v-model="query.searches[2].value"
            class="filter-item"
            :disabled="excludeDisabled"
            @change="search()">
            Exclude experts with the same current and former employer
          </el-checkbox>
          <div class="info filter-item float-right">
            <div v-show="$route.query.client_id">Press Shift+z view client rules</div>
            <div>Press Shift+x view your projects</div>
          </div>

          <template v-for="(item, index) in query.searches">
            <search-item
              v-if="index>2"
              :key="index"
              v-model="item.value"
              :item-type="item.type"
              :item-options="item.options"
              :item-placeholder="item.text"
              :item-key="item.key"
              @keyup.native.enter="search()"
            />
            <br v-if="searchItemBreak(index)" :key="item.key">
          </template>

          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="search()">Search</el-button>
          <template v-if="!isGKMRole">
            <el-button class="filter-item" type="success" icon="el-icon-plus" @click="actionGotoCreate">New</el-button>
            <add-to-target-project
              v-if="isAddAdvisor"
              ref="addToTargetProject"
              class="filter-item"
              :parent-disabled.sync="addToProjectDisabled"
              :select-advisors.sync="select_advisors"
              @update="clearSelection()"
            />
            <add-to-project
              v-if="!isAddAdvisor"
              ref="addToProject"
              class="filter-item"
              :parent-disabled.sync="addToProjectDisabled"
              :advisors.sync="select_advisors"
              @update="clearSelection()" />
            <el-button
class="filter-item"
                       type="primary"
                       plain
                       icon="el-icon-document-copy"
                       @click="actionCopySearchData">
              Copy Search
            </el-button>
            <el-tooltip
              :disabled="total<10000"
              content="The maximum number cannot exceed 10,000."
              placement="top">
              <div class="filter-item inline-block">
                <el-button
                  type="primary"
                  :disabled="total>10000"
                  :loading="add_all_advisors_loading"
                  @click="actionAddAllAdvisorToProject">
                  Add All Advisors to Project
                </el-button>
              </div>
            </el-tooltip>

            <add-to-cart :advisors="select_advisors" />
          </template>
        </div>
      </el-collapse-transition>
      <div class="text-center anchor_toggle_button">
        <div class="button_togglePanel horizontal" :class="{ focusMode: focusMode1 }" @click="focusMode1 = !focusMode1">
          <el-icon class="icon" name="d-arrow-left" />
        </div>
      </div>
      <div class="flex justify-content-between">
        <div>
          <el-tooltip
            v-if="rateCurrencyData.length"
            effect="light"
            content="Sort by"
            placement="top-start"
          >
            <div slot="content">
              <rate-currency-table :table-data="rateCurrencyData" />
            </div>
            <el-link
              type="primary"
              :underline="false"
              icon="el-icon-money"
              class="fs-12"
            >
              Rate Currency
              <span v-if="rateCurrency">- {{ rateCurrency.USD_median }} USD</span>
            </el-link>
          </el-tooltip>
        </div>
        <div>
          <span class="info fs-12">Sort:</span>
          <el-select
            v-model="custom_sort"
            clearable
            class="filter-item mb-8"
            collapse-tags
            placeholder="Sort"
            @change="handleSort(custom_sort)">
            <el-option
              v-for="item in sort_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <el-row type="flex" justify="space-between" class="flex-grow">
      <el-col style="width: 250px;margin-right:10px" :class="[focusMode2 ? 'transform_x' : 'position_relative']">
        <el-card :body-style="{ padding: '10px 20px' }" :class="{'opacity':focusMode2}">
          <div slot="header">
            <span>Location</span>
          </div>
          <div style="font-size: 12px;color: #999;line-height: 26px;">Continents</div>
          <div v-if="!locationData.continent.length" style="padding-left: 10px; font-size: 12px;">No Data</div>
          <div>
            <el-checkbox-group v-model="checkedContinents" @change="handleCheckedContinentsChange">
              <el-checkbox
                v-for="continent in locationData.continent"
                :key="continent.id"
                :label="continent.id"
                style="width:100%"
              >
                <span>{{ continent.name }}</span>
                <sup
                  class="el-badge__content el-badge__content--primary is-fixed"
                  style="transform: translateY(0) translateX(0);"
                >{{ continent.value || 0 }}</sup>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div style="font-size: 12px;color: #999;line-height: 26px;">{{
            locationData.country.length ? 'countries' : ''
          }}
          </div>
          <div style="max-height: 400px; min-height:400px;overflow: hidden;overflow-y: auto;">
            <el-checkbox-group v-model="checkedCounties" @change="handleCheckedCountiesChange">
              <el-checkbox
                v-for="country in locationData.country"
                :key="country.id"
                :label="country"
                style="width: 100%;">
                <div>
                  <span>{{ country.name }}</span>
                  <sup
                    class="el-badge__content el-badge__content--primary is-fixed"
                    style="transform: translateY(0) translateX(0);"
                  >{{ country.value || 0 }}</sup>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-card>
        <div class="text-center anchor_toggle_button">
          <div
class="button_togglePanel vertical"
               :class="{ focusMode: focusMode2 }"
               @click="focusMode2 = !focusMode2">
            <el-icon class="icon" name="d-arrow-left" />
          </div>
        </div>
      </el-col>
      <el-col :span="focusMode2 ? 24 : 20">
        <el-table
          ref="selectAdvisorTable"
          v-loading="loading"
          border
          stripe
          height="99%"
          :data="tableData"
          class="line-height-normal vertical-top"
          :row-key="getRowKeys"
          :row-class-name="$options.filters.rowSetIndex"
          @select="tableShiftMultiple"
          @sort-change="sortData"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" :selectable="checkSelectable" :reserve-selection="true" />
          <el-table-column label="Personal Info" :width="searchContainsSQ ? 170:270" show-overflow-tooltip>
            <template slot-scope="scope">
              <info-merge :info="scope.row.source" :is-sq-table="searchContainsSQ" />
            </template>
          </el-table-column>
          <el-table-column label="Employment History" :min-width="searchContainsSQ ? 170:220">
            <template slot-scope="scope">
              <consultant-previous type="employment" :data="scope.row.source" />
            </template>
          </el-table-column>
          <el-table-column label="Background" :min-width="searchContainsSQ ? 170:200">
            <template slot-scope="scope">
              <consultant-previous type="background" :data="scope.row.source" />
              <div v-if="scope.row.source.notes" class="mt-3"><b>Note:</b><span class="danger"> {{scope.row.source.notes}}</span></div>
            </template>
          </el-table-column>
          <slot v-if="!searchContainsSQ">
            <el-table-column label="Latest Sent TC Time" width="100">
              <template slot-scope="scope">
                {{ scope.row.source.latest_sent_tc_time|momentFormat('MM/DD/YYYY hh:mm A') }}
              </template>
            </el-table-column>
            <el-table-column
              label="Sourced By"
              align="center"
              sortable="custom"
              prop="sourced_by_id"
              width="80">
              <template slot-scope="scope">
                {{ scope.row.source.sourced_by_username }}
              </template>
            </el-table-column>
            <el-table-column
              label="Completed Calls"
              align="center"
              width="80"
              sortable="custom"
              prop="consultations_count"
            >
              <template slot-scope="scope">
                {{ scope.row.source.consultations_count }}
              </template>
            </el-table-column>
          </slot>
          <el-table-column v-if="searchContainsSQ" label="Linkedin Profile Summary">
            <template slot-scope="scope">
              <div v-html="$sanitizeHtml(scope.row.source.linkedin_profile_summary)" />
            </template>
          </el-table-column>
          <template v-if="!isGKMRole">
            <slot v-if="searchContainsSQ">
              <el-table-column label="Relevant Screening Question Responses" min-width="220">
                <template slot-scope="scope">
                  <SQSlither :sq-list="scope.row.source.match_sq_qas" />
                </template>
              </el-table-column>
            </slot>
            <el-table-column :label="searchContainsSQ?'All SQs':'SQ'" align="center" width="60">
              <template slot-scope="scope">
                <SQMatch
                  v-if="scope.row.source.sq_qas_highlight.length"
                  :inquiry-list="scope.row.source.sq_qas_highlight" />
              </template>
            </el-table-column>
          </template>

        </el-table>
      </el-col>
    </el-row>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
    <el-drawer
      :visible.sync="ruleRawer"
      direction="rtl">
      <expert-rule class="rule-rawer" :client-id="$route.query.client_id" type="expert" />
    </el-drawer>
    <el-drawer
      :visible.sync="projectDrawer"
      direction="rtl"
      size="20%">
      <short-project-info :project-id="project_id" />
    </el-drawer>

  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import CartAPI from '@/api/cart'
import { copyUtil, debounce, getLocationById, highlightHtmlV2, isEmpty, RouteTools } from '@/utils/tool'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import SearchItem from '@/components/SearchItem'
import SQMatch from './components/SearchSQ'
import InfoMerge from './components/ConsultantInfo'
import ConsultantPrevious from './components/ConsultantPrevious'
import ExpertRule from '@/components/ComplianceWarning/Index'
import { mapGetters, mapState } from 'vuex'
import AddToProject from './components/addToProject'
import AddToTargetProject from '@/views/consultant/components/AddToTargetProject'
import ShortProjectInfo from '@/views/consultant/components/ShortProjectInfo'
import SQSlither from '@/views/consultant/components/SQSlither'
import _ from 'lodash'
import moment from 'moment'
import AddToCart from '@/components/ShoppingCart/AddToCart'
import RateCurrencyTable from './components/RateCurrencyTable.vue'

export default {
  name: 'Consultant',
  components: {
    Pagination,
    AddToProject,
    SearchItem,
    SQMatch,
    InfoMerge,
    ConsultantPrevious,
    ExpertRule,
    ShortProjectInfo,
    AddToTargetProject,
    SQSlither,
    AddToCart,
    RateCurrencyTable,
  },
  directives: { waves },
  filters: {
    getLocation(val, option) {
      let result = 'N/A'
      option.forEach(item => {
        if (item.id === val) {
          result = item.name
        }
      })
      return result
    },
  },

  data() {
    return {
      last_select_index: null,
      shiftPress: false,
      ruleRawer: false,
      projectDrawer: false,
      leftLocationFlag: false,
      searchContainsSQ: false,
      add_all_advisors_loading: false,
      checkedContinents: [],
      checkedCounties: [],
      checkedAdvisor: [],
      searchQuery: {
        page: 1,
        size: 20,
        sort: undefined,
      },
      statusData: [],
      locationData: { continent: {}, country: {} },
      rateCurrency: null,
      rateCurrencyData: [],
      query: {
        page: 1,
        size: 20,
        searches: [
          { value: '', key: '', text: 'Keywords' },
          { value: '', key: 'precise_search', text: 'precise_search' },
          { value: '', key: 'should_exclude_current_company_matched', text: 'should_exclude_current_company_matched' },
          { value: '', key: 'name', type: 'text', text: 'Name' },
          { value: '', key: 'background', type: 'text', text: 'Background' },
          { value: '', key: 'phone', type: 'text', text: 'Phone' },
          { value: '', key: 'company', text: 'Employer' },
          {
            value: {
              input_value: null,
              range_value: this.getPeriodOptions()[2].range_value,
            },
            key: 'company_period',
            text: 'Period',
            type: 'select-date',
            options: this.getPeriodOptions(),
          },
          { value: '', key: 'email', type: 'text', text: 'Email' },
          {
            value: [],
            key: 'industry',
            type: 'industry',
            text: 'Industry',
          },
          {
            value: ['ADVISOR'],
            key: 'type',
            text: 'Type',
            type: 'select-multiple',
            options: this.$store.state.options.advisor.type,
          },
          { value: '', key: 'title', type: 'text', text: 'Title' },
          {
            value: {
              input_value: null,
              range_value: this.getPeriodOptions()[2].range_value,
            },
            key: 'title_period',
            text: 'Period',
            type: 'select-date',
            options: this.getPeriodOptions(),
          },
          { value: '', key: 'consultations_count', text: 'Consultations  eq: 1-5', type: 'num-rate' },
          { value: '', key: 'rate', text: 'Hourly Rate   eq: 1-5', type: 'num-rate' },
          { value: '', key: 'feedback_avg_rate', text: 'Feedback Rating  eq: 1-5', type: 'num-rate' },
          {
            value: [],
            key: 'current_company_type',
            type: 'select-multiple',
            text: 'Company Type',
            options: this.$store.state.options.company.type,
          },
          { value: [], key: 'location_include', text: 'Location (Include)', type: 'location' },
          { value: [], key: 'location_exclude', text: 'Location (Exclude)', type: 'location' },
          // { value: '', key: 'rm', text: 'RM' },
          { value: '', key: 'sourced_by_id', text: 'Sourced By', type: 'userid' },
          { value: '', key: 'screening_search_words', text: 'Screening Question' },
          { value: [], key: 'user_advisor_tags', text: 'Tags', type: 'user_advisor_tags' },
          { value: '', key: 'npi', text: 'NPI #', type: 'textarea' },
          {
            value: [],
            key: 'spoken_language',
            text: 'Spoken Language',
            type: 'select-multiple-allowCreate',
            options: this.$store.state.options.advisor.spoken_language,
          },
          {
            value: '',
            key: 'tc_status',
            text: 'Limit results to experts who have been sent T&C but have not signed',
            type: 'checkbox',
          },
          {
            value: '',
            key: 'is_only_search_screening_response',
            text: 'Do not search question text, only responses',
            type: 'checkbox',
          },
        ],
        previous_company: null,
        previous_position: null,
      },
      loading: false,
      total: NaN,
      tableData: [],
      focusMode1: false,
      focusMode2: true,
      select_advisors: [],
      custom_sort: undefined,
      sort_options: [
        { label: 'Most Recently Onboarded', value: 'latest_signed_tc_time desc' },
        { label: 'Most Recently Responded', value: 'latest_respond_time desc' },
        { label: 'Relevancy', value: undefined },
        { label: 'Completed Calls (Most to least)', value: 'consultations_count desc' },
        { label: 'TC Sent (most recent to oldest)', value: 'latest_sent_tc_time desc' },
      ],
      jobs_highlight_words: [],
      sq_highlight_words: [],
      addToProjectDisabled: false,
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
      'uid',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),

    isGKMRole() {
      return this.roles.find(item => item.role.name === 'GKM') && this.roles.length === 1
    },

    isAddAdvisor() {
      return this.$route.name === 'AddConsultant'
    },

    project_id() {
      return this.$route.query.project_id
    },

    add_type() {
      return this.$route.query.type
    },

    // 仅当 employer 勾选 former 时，才启用
    excludeDisabled() {
      const employer = this.query.searches.find(item => item.key === 'company_period')
      return employer.value.input_value !== false
    },
  },

  watch: {
    'excludeDisabled': {
      handler(newVal) {
        if (newVal) {
          this.query.searches[2].value = ''
        }
      },
    },
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)

    if (this.searchQuery.searches) {
      const format = JSON.parse(unescape(this.searchQuery.searches))
      this.query.searches.forEach((item, index) => {
        item.value = format[index]
      })
    }
    delete this.searchQuery.searches

    this.loading = true
    this.$store
      .dispatch('options/getAllCountryList')
      .then(() => {
        this.search(true)
      })
      .catch(err => {
        console.log(err)
      })
    if (this.isAddAdvisor) {
      this.query.searches[10].value = [this.add_type]
    }

    // 加载按键监听
    document.addEventListener('keypress', this.keyPress)
    document.addEventListener('mousedown', this.keyPress)
  },

  beforeDestroy() {
    // 卸载按键监听
    document.removeEventListener('keypress', this.keyPress)
    document.removeEventListener('mousedown', this.keyPress)
  },
  methods: {
    getRowKeys(row) {
      return row.source.id
    },
    checkSelectable(row) {
      return row.source.status !== 'BLACKLIST'
    },

    async getList(not_save_params = false) {
      this.loading = true
      this.tableData = []
      let params = await this.formatParams()
      const { page, size, sort } = this.searchQuery
      params = Object.assign({}, params, { page, size, sort })
      if (!not_save_params) {
        const query_searches = this.query.searches.map(item => {
          if (['user_advisor_tags'].includes(item.type)) return []
          return item.value
        })
        RouteTools.saveQueryToRouter(Object.assign(this.searchQuery, { searches: JSON.stringify(query_searches) }))
      }

      return ConsultantAPI.searchConsultant(params)
        .then(data => {
          if (data.total) {
            this.leftLocationFlag ? this.updateLeftLocationValue(data) : this.initLeftLocation(data)
            this.statusData = data.aggs.status
            this.rateCurrency = data.aggs.rate_currency
            this.rateCurrencyData = data.aggs.rate_currency ? [data.aggs.rate_currency, data.aggs.rate_currency] : []
          }
          this.total = data.total
          this.tableData = data['list'].map(item => {
            const result = _.clone(item['source'])
            for (const key in item['highlight']) {
              item['highlight'][key].forEach((words) => {
                result[key] = words
              })
            }
            result.jobs.forEach(item => {
              item.company = highlightHtmlV2(item.company, this.jobs_highlight_words)
              item.title = highlightHtmlV2(item.title, this.jobs_highlight_words)
              item.description = highlightHtmlV2(item.description, this.jobs_highlight_words)
            })

            result.sq_qas_highlight = _.cloneDeep(result.sq_qas).map(item => {
              item.answer_text = highlightHtmlV2(item.answer_text, this.sq_highlight_words)
              item.question_title = highlightHtmlV2(item.question_title, this.sq_highlight_words)
              return item
            })

            result.match_sq_qas = this.sq_highlight_words.length > 0 ? this.filterSQ(result.sq_qas) : result.sq_qas

            return { source: result }
          })
          this.$nextTick(() => {
            this.$refs.selectAdvisorTable.bodyWrapper.scrollTop = 0
          })
        })
        .then(this.getUserAdvisorTags)
        .finally(() => {
          this.loading = false
        })
    },

    formatHighlightWords(value) {
      if (!value) return
      const reg = /(OR|AND|NOT)/
      const trim_value = value.trim().replace(/\s+/g, ' ').replace(/\*/g, '')

      // "ab c" OR qw e =》['ab c','qw','e']  双引号内的内容精确搜索
      // ab c OR qw e =》['ab','c','qw','e']
      if (/".*"/.test(trim_value)) {
        const precise_words_list = trim_value.match(/\"(.*?)\"/g).map(item => item.replace(/"/g, ''))
        const other_words = trim_value.replace(/\"(.*?)\"/g, '').split(' ')
        const other_words_list = other_words.map(item => item.trim()).filter(item => !!item && !reg.test(item))

        return precise_words_list.concat(other_words_list)
      } else {
        return trim_value.split(' ').map(item => item.trim()).filter(item => !reg.test(item))
      }
    },

    filterSQ(sq_list) {
      let result

      const highlight_reg = new RegExp(`${this.sq_highlight_words.join('|')}`, 'i')
      result = sq_list.filter(item => highlight_reg.test(item.answer_text) || highlight_reg.test(item.question_title))

      const sq_search_words = this.query.searches.find(item => item.key === 'screening_search_words')
        .value
        .split(' ')
        .map(item => item.trim())
      const reg_all = /[OR|AND|NOT]/g
      const reg_and = /AND/g
      const sq_reg_all = sq_search_words.filter(item => reg_all.test(item)).length
      const sq_reg_and = sq_search_words.filter(item => reg_and.test(item)).length

      // 单独处理 逻辑运算只包含AND时的情况 ：datadog AND rapid7 =》必须包含两者
      //                              ：data dog AND rapid7 =》必须包含(data|dog）和 rapid7
      //                              ：'data dog' AND rapid7 =》必须包含(data dog) 和 rapid7
      // 其余情况 统一视为 所有单词 或
      if (sq_reg_all === sq_reg_and && sq_reg_and > 0) {
        const temp = this.query.searches.find(item => item.key === 'screening_search_words').value.split('AND')
        const match = temp.map(item => {
          const keyword = item.toLowerCase().trim()
          if (/^".*"$/.test(keyword)) {
            return `(?=.*${keyword.replace(/"/g, '')})`
          } else {
            return `(?=.*${keyword.replace(/\s+/g, '|')})`
          }
        }).join('')

        const highlight_and_reg = new RegExp(`${match}.*`, 'i')
        result = result.filter(item => {
          return highlight_and_reg.test(item.question_title + item.answer_text)
        })
      }

      return result.map(item => {
        item.answer_text = highlightHtmlV2(item.answer_text, this.sq_highlight_words)
        item.question_title = highlightHtmlV2(item.question_title, this.sq_highlight_words)
        return item
      })
    },
    async formatParams() {
      const searches = this.query.searches

      let location_ids = []
      let excluded_location_ids = []
      let screening_search_words
      let linkedin_industry_ids
      let is_only_search_screening_response
      let user_advisor_tag_names = []
      const search_params = {}
      const company = {}
      const title = {}
      let email_words
      let phone_words
      let should_exclude_current_company_matched
      this.jobs_highlight_words = []
      this.sq_highlight_words = []

      // 当搜索值包含 keywords || sq 时，展示包含SQ 详情的表格
      this.searchContainsSQ = searches.filter(
        item => item.value && ['', 'screening_search_words'].includes(item.key)).length > 0

      searches
        .filter(item => !isEmpty(item.value))
        .forEach(item => {
          if (item.key === '') {
            this.sq_highlight_words.push(...this.formatHighlightWords(item.value))
            this.jobs_highlight_words.push(...this.formatHighlightWords(item.value))
            search_params['keywords'] = item.value
          } else if (item.key === 'tc_status' && item.value) {
            search_params[item.key] = 'SENT'
          } else if (item.key === 'precise_search') {
            search_params['is_precise'] = item.value
          } else if (item.key === 'should_exclude_current_company_matched') {
            should_exclude_current_company_matched = item.value
          } else if (item.key === 'company') {
            company.company_words = item.value
            this.jobs_highlight_words.push(...this.formatHighlightWords(item.value))
          } else if (item.key === 'company_period') {
            company.company_is_current = item.value.input_value
            company.company_start = item.value.range_value[0] || undefined
            company.company_end = item.value.range_value[1] === moment().format('YYYY-MM')
              ? 'Present' : (item.value.range_value[1] || undefined)
          } else if (item.key === 'title') {
            title.title_words = item.value
            this.jobs_highlight_words.push(...this.formatHighlightWords(item.value))
          } else if (item.key === 'title_period') {
            title.title_is_current = item.value.input_value
            title.title_start = item.value.range_value[0] || undefined
            title.title_end = item.value.range_value[1] === moment().format('YYYY-MM')
              ? 'Present' : (item.value.range_value[1] || undefined)
          } else if (item.key === 'location_include') {
            location_ids = item.value
          } else if (item.key === 'location_exclude') {
            excluded_location_ids = item.value
          } else if (item.key === 'screening_search_words') {
            this.sq_highlight_words.push(...this.formatHighlightWords(item.value))
            screening_search_words = item.value
          } else if (item.key === 'is_only_search_screening_response') {
            is_only_search_screening_response = screening_search_words ? true : undefined
          } else if (['rate', 'consultations_count', 'feedback_avg_rate'].includes(item.key)) {
            search_params[item.key + '_gte'] = item.value[0]
            if (item.value[1]) {
              search_params[item.key + '_lte'] = item.value[1]
            }
          } else if (item.key === 'phone') {
            phone_words = item.value
          } else if (item.key === 'email') {
            email_words = item.value
          } else if (item.key === 'industry') {
            linkedin_industry_ids = item.value.join()
          } else if (item.key === 'user_advisor_tags') {
            user_advisor_tag_names = item.value.map(tag => tag.name).filter(name => name)
          } else if (item.key === 'npi') {
            search_params['npi'] = item.value.trim()
          } else {
            if (['name', 'background', 'company', 'title'].includes(item.key)) {
              search_params[item.key] = item.value
              this.jobs_highlight_words.push(...this.formatHighlightWords(item.value))
            } else {
              search_params[item.key] = typeof item.value === 'object' ? item.value.join() : item.value
            }
          }
        })

      location_ids = await this.formatParamsLocation(this.checkedContinents, this.checkedCounties, location_ids)
      excluded_location_ids = await this.formatParamsLocation(this.checkedContinents, this.checkedCounties, excluded_location_ids)
      return Object.assign({
        ...search_params,
        screening_search_words,
        is_only_search_screening_response,
        linkedin_industry_ids,
        email: email_words,
        phone: phone_words,
        should_exclude_current_company_matched: should_exclude_current_company_matched,
        location_ids: location_ids.length ? location_ids : undefined,
        excluded_location_ids: excluded_location_ids.length ? excluded_location_ids : undefined,
        user_advisor_tag_names: user_advisor_tag_names.length ? user_advisor_tag_names : undefined,
      }, company, title)
    },
    async formatParamsLocation(checkedContinents, checkedCounties, location_ids) {
      const location_list = await getLocationById(location_ids)
      const top_location_continents = location_list.filter(item => !item.level)
        .map(item => item.id)
      const top_location_counties = location_list.filter(item => item.level)

      const checked_counties_parent_ids = [...checkedCounties, ...top_location_counties].map(item => item.parent_id)
      const search_counties_ids = [...checkedCounties, ...top_location_counties].map(item => item.id)
      const search_continents_ids = [...checkedContinents, ...top_location_continents].filter(
        item => !checked_counties_parent_ids.includes(item))
      return Array.from(new Set([...search_continents_ids, ...search_counties_ids]))
        .join()
    },

    initLeftLocation(data) {
      this.locationData.continent = this.$store.state.options.allContinentList.filter(
        item => Object.keys(data.aggs.location_id_continent)
          .indexOf(String(item.id)) > -1)
      // console.log(this.locationData.continent)
      this.locationData.continent.forEach(item => {
        item.value = data.aggs.location_id_continent[item.id]
      })
      this.locationData.country = this.$store.state.options.allCountryList.filter(
        item => Object.keys(data.aggs.location_id_country)
          .indexOf(String(item.id)) > -1)
      this.locationData.country.forEach(item => {
        item.value = data.aggs.location_id_country[item.id]
      })
    },
    updateLeftLocationValue(data) {
      this.locationData.continent.forEach(item => {
        item.value = data.aggs.location_id_continent[item.id]
      })
      this.locationData.country.forEach(item => {
        item.value = data.aggs.location_id_country[item.id]
      })
    },

    handleCheckedContinentsChange() {
      this.leftLocationFlag = true
      this.handleSearch()
    },
    handleCheckedCountiesChange() {
      this.leftLocationFlag = true
      this.handleSearch()
    },
    search(not_save_params = false) {
      this.leftLocationFlag = false
      this.checkedContinents = []
      this.checkedCounties = []
      this.getList(not_save_params)
    },
    sortData(val) {
      if (val) {
        this.custom_sort = undefined
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.prop + order
        this.getList()
      }
    },
    handleSort(val) {
      this.$refs.selectAdvisorTable.clearSort()
      this.searchQuery.sort = val
      this.getList()
    },
    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },

    handleSelectionChange(val) {
      const advisor_ids = val.map(item => item.source.id).join()
      const params = { ids: advisor_ids, page: 1, size: val.length }
      if (val.length > 0) {
        return ConsultantAPI.getConsultantList(params)
          .then(data => {
            this.select_advisors = data.list
          })
      } else {
        this.select_advisors = []
      }
    },
    tableShiftMultiple(val, row) {
      const in_select = val.find(item => item.source.id === row.source.id)
      if (in_select) {
        if (this.shiftPress) {
          if (this.last_select_index > -1) {
            const start_index = Math.min(this.last_select_index, row.table_index)
            const end_index = Math.max(this.last_select_index, row.table_index)
            const select_list = this.tableData.filter(
              item => (item.table_index < end_index && item.table_index >
                start_index && item.source.status !== 'BLACKLIST' && !val.find(i => i.source.id === item.source.id)))

            select_list.forEach(item => {
              val.push(item)
              this.$refs.selectAdvisorTable.toggleRowSelection(item)
            })
          }
        } else {
          this.last_select_index = row.table_index
        }
      }
    },

    actionCopySearchData() {
      copyUtil(window.location.href)
        .then(() => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
          this.subject_copied = true
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },

    async actionAddAllAdvisorToProject() {
      this.add_all_advisors_loading = true
      let params = await this.formatParams()
      // size:10000 后端借口最大值
      params = Object.assign({}, params, this.searchQuery, { page: 1, size: 10000 })

      return ConsultantAPI.searchConsultantByEs(params)
        .then(data => {
          this.select_advisors = data.list
        })
        .then(() => {
          // 点击AddAllAdvisorToProject btn 时，禁用 add to project & add-to-target-project 按钮
          // 在对应组建关闭弹窗时 addToProjectDisabled => false
          this.addToProjectDisabled = true
          if (this.isAddAdvisor) {
            this.$refs.addToTargetProject.checkBeforeAddToProject()
          } else {
            this.$refs.addToProject.actionAddToProject()
          }
          this.add_all_advisors_loading = false
        })
    },

    actionGotoCreate() {
      this.$router.push({ name: 'CreateConsultant' })
    },

    clearSelection() {
      this.$refs.selectAdvisorTable.clearSelection()
    },

    keyPress(e) {
      this.shiftPress = e.shiftKey
      if (e.charCode === 88 && e.shiftKey) {
        this.projectDrawer = !this.projectDrawer
      }
      if (!this.$route.query.client_id) return
      if (e.charCode === 90 && e.shiftKey) {
        this.ruleRawer = !this.ruleRawer
      }
    },
    actionTogglePanel() {
      this.focusMode = !this.focusMode
    },

    searchItemBreak(index) {
      if (document.body.clientWidth < 1540) {
        return [2, 5, 7, 10, 12, 16, 20].includes(index)
      } else {
        return [2, 7, 12, 16, 20].includes(index)
      }
    },

    getPeriodOptions() {
      return [
        {
          label: 'Currents',
          value: true,
          start_disabled: true,
          end_disabled: false,
          range_value: [
            moment().subtract(6, 'month').format('yyyy-MM'),
            moment().format('yyyy-MM')],
        },
        {
          label: 'Formers',
          value: false,
          start_disabled: false,
          end_disabled: false,
          range_value: [
            moment().startOf('year').subtract(2, 'year').format('yyyy-MM'),
            moment().subtract(1, 'month').format('yyyy-MM')],
        },
        {
          label: 'Currents and Formers',
          value: null,
          start_disabled: false,
          end_disabled: true,
          range_value: [
            moment().startOf('year').subtract(2, 'year').format('yyyy-MM'),
            moment().format('yyyy-MM')],
        },
      ]
    },

    getUserAdvisorTags() {
      const user_advisor_tags = this.query.searches.find(item => item.key === 'user_advisor_tags')?.value
      const search_tags = user_advisor_tags.map(tag => tag.name).filter(name => name)

      const advisor_tags = Array.from(new Set(this.tableData.map(item => item.source.user_advisor_tags).flat()))
        .filter(item => item)
      if (advisor_tags.length) {
        return CartAPI.getAdvisorTags({
          name_in: advisor_tags,
        }).then(({ list }) => {
          this.tableData.forEach(item => {
            item.source.user_advisor_tags = item.source.user_advisor_tags.map(tag => ({
              name: tag,
              color: search_tags.includes(tag) ? 'highlight' : list.find(option => option.name === tag)?.color,
            }))
          })
        })
      }
    },
  },

}
</script>

<style lang="scss" scoped>
.custom-flex-box {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 66px)
}

.flex-grow {
  flex-grow: 1;
}

.badge-box {
  border: 1px solid #cfd4de;
  border-radius: 6px;
  padding: 20px 10px;
  margin-top: 10px;

  .item {
    margin: 5px 10px;
  }
}

.collapse-wrap-leave-active,
.collapse-wrap-enter-active {
  transition: all 0.2s;
}

.collapse-wrap-enter,
.collapse-wrap-leave-to {
  max-height: 0;
}

.collapse-wrap-enter-to,
.collapse-wrap-leave {
  max-height: 250px;
}

.anchor_toggle_button {
  z-index: 999;

  .button_togglePanel {
    display: inline-block;
    -webkit-appearance: none;
    background-color: #3f9eff;
    color: #fff;
    border: none;
    opacity: 0.2;
    cursor: pointer;
    border-radius: 0 0 3px 3px;

    &:hover {
      opacity: 1;
    }
  }

  .horizontal {
    height: 1em;
    width: 10em;

    .icon {
      transform: rotate(90deg);
      vertical-align: top;
    }

    &.focusMode {
      .icon {
        transform: rotate(270deg);
      }
    }
  }

  .vertical {
    height: 10em;
    width: 1em;
    line-height: 10em;
    position: absolute;
    top: 25%;
    right: 0;

    .icon {
      transform: rotate(0deg);
    }

    &:hover {
      opacity: 1;
    }

    &.focusMode {
      .icon {
        transform: rotate(180deg);
      }
    }
  }
}

.float-right {
  float: right;
}

.el-card__body .el-checkbox span {
  font-size: 12px;
}

.position_relative {
  position: relative;
}

.transform_x {
  transform: translateX(-252px);
  position: absolute;
}

.opacity {
  opacity: 0;
}

::v-deep .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 200px;
}

::v-deep .el-rate__icon {
  font-size: 12px;
  margin-right: 2px;
}

::v-deep .el-rate__text {
  font-size: 12px;
}

.rule-rawer {
  ::v-deep li {
    padding-bottom: 5px;
  }
}
</style>
