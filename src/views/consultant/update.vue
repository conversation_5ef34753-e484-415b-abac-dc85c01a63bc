<template>
  <div class="app-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :class="{'form-data':!isDialog}"
      class="update-form"
      label-width="110px"
      :disabled="submitting">
      <el-alert v-if="cn_advisor_exist_in_us" type="error" :closable="false" class="mb-8">
        This expert already exists in the system :
        <router-link-advisor class="ml-8" :data="cn_advisor" />
      </el-alert>
      <el-form-item label="Name" style="margin-bottom: 0;" required>
        <el-row :gutter="10" type="flex" justify="space-between" :class="{'w-80':isDialog}">
          <el-col :span="12">
            <el-form-item
              prop="firstname">
              <el-input v-model="form.firstname" placeholder="First Name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="lastname">
              <el-input v-model="form.lastname" placeholder="Last Name" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="Prefix" prop="name_prefix">
        <el-select
          v-model="form.name_prefix"
          clearable
          class="w-100">
          <el-option
            v-for="item in advisor_options.name_prefix"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="Rate" prop="default_rate">
        <el-input
          v-model="form.default_rate"
          type="number"
          :min="0"
          :disabled="import_form_cn_db"
          :class="{'w-80':isDialog}"
          placeholder="Rate" />
      </el-form-item>
      <el-form-item label="Currency">
        <el-select
          v-model="form.rate_currency"
          :disabled="import_form_cn_db"
          class="w-100">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="Minimum Charge" prop="time_minimum">
        <el-select
          v-model="form.time_minimum"
          class="w-100"
        >
          <el-option
            v-for="item in advisor_options.time_minimum_charge"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="Location"
        prop="location_id">
        <location
          v-model="form.location_id"
          :class="{'w-80': isDialog}"
          @change="actionLocationChange" />
      </el-form-item>
      <el-form-item label="Locale">
        <el-select v-model="form.locale">
          <el-option v-for="lang in lang_type_options" :key="lang.value" :label="lang.label" :value="lang.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Time zone">
        <el-select
          v-model="form.zone_id_string"
          clearable
          filterable>
          <el-option v-for="lang in timezone_list" :key="lang.id" :label="lang.name" :value="lang.id" />
        </el-select>
      </el-form-item>
      <div v-if="!import_form_cn_db" class="contact-warning-label warning">
        Must have one of the following
      </div>
      <el-form-item
        v-for="(con, index) in form.contact_infos"
        :key="`contact-${index}`"
        :show-message="false"
        :label="!index ? 'Contact' : ''"
        prop="contact_infos">
        <el-form-item
          class="form-item"
          :class="{'w-80':isDialog}"
          :prop="`contact_infos[${index}].value`"
          :rules="contactRules[con.type]"
        >
          <el-input v-model="con.value" class="input-with-select" :class="{'w-500px':!isDialog}">
            <template slot="prepend">
              <div class="text-center" style="min-width: 70px;">
                {{ con.type | getLabel(contact_info_options) }}
              </div>
            </template>
          </el-input>

        </el-form-item>
        <el-link
          v-if="con.origin"
          type="primary"
          icon="el-icon-plus"
          class="icon-font"
          :underline="false"
          @click="addContact(con.type, index)" />
        <el-link
          v-if="!con.origin"
          type="danger"
          icon="el-icon-delete"
          class="icon-font"
          :underline="false"
          @click="removeContact(con, index)" />
        <el-tooltip v-if="isDisplaySetMain(con)" class="item" effect="dark" content="Main Contact" placement="right">
          <el-link
            v-if="con.is_main"
            type="success"
            class="icon-font"
            :underline="false"
            icon="el-icon-star-on"
          />
          <el-link
            v-else
            class="icon-font"
            :underline="false"
            icon="el-icon-star-off"
            @click="setMainContact(con, index)" />
        </el-tooltip>
      </el-form-item>
      <el-form-item
        v-for="(exp, index) in form.jobs"
        :key="`experience-${index}`"
        :label="!index ? 'Experience' : ''"
        required>
        <el-row>
          <el-form-item
            class="form-item"
            :class="{'w-80':isDialog}"
            :prop="`jobs[${index}].company`"
            :rules="{required: true, message: 'Company is required', trigger: 'change'}"
          >
            <el-select
              v-model="exp.company"
              value-key="id"
              class="remote-select"
              :class="[{'is-reverse': is_visible}]"
              filterable
              remote
              reserve-keyword
              placeholder="Company"
              loading-text="loading..."
              :remote-method="getCompanyOptions"
              no-data-text="No matching data"
              @change="(val)=> {exp.company_id = val.id}"
            >
              <el-option v-for="item in companyList.data" :key="item.id" :label="item.name" :value="item">
                <span>{{ item.name }}</span>
                <span class="ml-3 mr-normal pull-right" style="color: #8492a6;">{{ item.stock_code }}</span>
              </el-option>
              <template slot="empty">
                <p class="el-select-dropdown__empty">
                  <el-link
                    type="primary"
                    :underline="false"
                    @click="goToCreateCompany(companySelectInput,'', index)">
                    Create <strong>{{ companySelectInput }}</strong> ...
                  </el-link>
                </p>
              </template>
            </el-select>
            <el-checkbox
              v-model="exp.is_independent"
              class="ml-8"
              @change="(val)=> handleIndependentConsultant(val, exp, index)">Independent Consultant
            </el-checkbox>
          </el-form-item>
          <el-link
            v-if="exp.company_name && !exp.company_id"
            type="primary"
            :underline="false"
            @click="goToCreateCompany(exp.company_name, exp.chinese_name, index)">
            Create Company: {{ exp.company_name }}
          </el-link>
        </el-row>
        <el-form-item
          :class="{'is-dialog-custom':isDialog}"
          class="form-item"
          :prop="'jobs.' + index + '.position'"
          :rules="{required: true, message: 'Position is required', trigger: 'change'}"
        >
          <el-input
            v-model="exp.position"
            :class="{'w-200px':!isDialog}"
            placeholder="Position" />
        </el-form-item>
        <el-form-item
          class="form-item"
          :prop="'jobs.' + index + '.start_date'"
          :rules="{required: true, message: 'Start date is required', trigger: 'change'}"
        >
          <el-date-picker
            v-model="exp.start_date"
            type="month"
            placeholder="Start Date"
            format="MM/yyyy"
            value-format="yyyy-MM"
          />
        </el-form-item>
        -
        <div class="inline-block">
          <el-form-item
            class="form-item"
            :prop="'jobs.' + index"
            :rules="{required: true, validator: endTimeValidator , trigger: 'change'}"
            style="margin-top: 15px;"
          >
            <el-date-picker
              v-model="exp.end_date"
              :disabled="exp.is_current"
              type="month"
              placeholder="End Date"
              format="MM/yyyy"
              value-format="yyyy-MM"
            />
          </el-form-item>
          <el-form-item
            class="form-item ml-8"
            :prop="'jobs.' + index + '.is_current'"
          >
            <el-checkbox v-model="exp.is_current" @change="handleCurrentToggle(exp)">Current
            </el-checkbox>
          </el-form-item>
        </div>
        <el-link
          v-if="form.jobs.length > 1"
          type="danger"
          icon="el-icon-delete"
          class="icon-font"
          :underline="false"
          @click="removeFormItem(form.jobs, index)" />
      </el-form-item>
      <el-form-item label="">
        <el-link
          type="primary"
          icon="el-icon-plus"
          class="icon-font"
          :underline="false"
          @click="addExperience" />
      </el-form-item>
      <el-form-item v-if="isEdit">
        <template #label>
          Background
          <el-tooltip effect="dark" content="Auto Generate From Employment History" placement="top">
            <el-link type="primary" :underline="false" @click="autoGenerateBackground">Auto Generate</el-link>
          </el-tooltip>
          <background-format ref="background-format" :data="form" class="display-none" />
        </template>
        <el-input v-model="form.background" type="textarea" :rows="6" placeholder="Background" />
      </el-form-item>
      <div v-if="!isEdit" class="text-center">
        <el-button type="primary" icon="el-icon-check" :disabled="cn_advisor_exist_in_us" @click="onSave()">
          {{isDialog ? 'Create' : 'Save'}}
        </el-button>
        <el-button v-if="!advisor_tc && !isDialog" :disabled="cn_advisor_exist_in_us" type="text" @click="onSave()">
          Save & New
        </el-button>
      </div>
      <div v-if="isEdit" class="text-center">
        <el-button type="primary" icon="el-icon-check" @click="onSave()">Save</el-button>
        <el-button type="text" @click="$emit('save')">Cancel</el-button>
      </div>
    </el-form>

    <el-dialog
      title="Create Company"
      width="800px"
      append-to-body
      :visible.sync="dialogVisible"
      @open="openCompanyDialog">
      <create-company
        ref="create-company"
        is-dialog
        :data="{name: companySelectInput,chinese_name:companySelectInputChinese}"
        @create="afterCreateCompany" />
    </el-dialog>
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import CompanyAPI from '@/api/company'
import FormMixin from '@/components/FormPage/mixin'
import _ from 'lodash'
import Mixin from './mixins/form'
import Location from '@/components/Location'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import { mapGetters, mapState } from 'vuex'
import BackgroundFormat from '@/components/FormatProfile/Background'
import moment from 'moment-timezone'
import createCompany from '@/views/company/update'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import { getLocationById } from '@/utils/tool'
import TcAPI from '@/api/tc'

export default {
  name: 'UpdateConsultant',
  components: { Location, BackgroundFormat, createCompany, RouterLinkAdvisor },
  mixins: [FormMixin, Mixin, SelectMixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
    isDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const contactValidator = (rule, value, callback) => {
      if (!value.filter(item => item.value).length) {
        callback(new Error('Must have one of the following'))
      } else {
        callback()
      }
    }
    const phoneValidator = (rule, value, callback) => {
      const reg = /^\+?\d+(?:-?\d+){5,}$/
      if (!reg.test(value) && value !== '') {
        callback(new Error('Please enter a valid phone number'))
      } else {
        return callback()
      }
    }
    const emailValidator = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      }, 100)
    }
    return {
      dialogVisible: false,
      companySelectInput: '',
      companySelectInputChinese: '',
      jobEditingIndex: 0,
      form: this.initForm(),
      selectLoading: false,
      companyList: {
        data: [],
        count: 0,
      },
      rules: {
        firstname: [{ required: true, message: 'First name is required', trigger: 'change' }],
        lastname: [{ required: true, message: 'Last name is required', trigger: 'change' }],
        contact_infos: [{ required: true, validator: contactValidator, trigger: 'change' }],
        location_id: [{ required: true, message: 'Location is required', trigger: 'change' }],

      },
      contactRules: {
        EMAIL: [{ validator: emailValidator, trigger: 'blur' }],
        PHONE: [{ validator: phoneValidator, trigger: 'blur' }],
        LINKEDIN_URL: [{ required: false, message: 'Please input the value', trigger: 'blur' }],
      },
      advisor_tc: null,
      import_form_cn_db: false,
      cn_advisor_exist_in_us: false,
      cn_advisor: null,
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
      },
    }
  },
  computed: {
    ...mapGetters([
      'lang_type_options',
      'timezone_list',
      'advisor_options',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },

  mounted() {
    this.handleIsEdit()
    this.handleImportData()
  },
  deactivated() {
    this.resetData()
  },
  methods: {
    initForm() {
      return {
        firstname: '',
        lastname: '',
        background: '',
        name_prefix: '',
        type: 'LEAD',
        location_id: null,
        time_minimum: 'NO_MINIMUM',
        locale: 'en-US',
        zone_id_string: 'America/New_York',
        contact_infos: this.initContactInfo(),
        jobs: [
          {
            company: null,
            company_id: '',
            position: '',
            is_current: true,
            is_independent: false,
            start_date: '',
            end_date: '',
          }],
      }
    },
    endTimeValidator(rule, value, callback) {
      if (!value.end_date && !value.is_current) {
        callback(new Error('end date is required'))
      } else {
        callback()
      }
    },
    handleIsEdit() {
      if (this.isEdit && this.data) {
        Object.assign(this.form, this.data)
        this.initSelect()
        this.formatContactInfo()
      }
    },
    initSelect() {
      const editJobIds = this.data.jobs.map(item => {
        return item.company.id
      }).join()
      this.loadGetCompany({
        ids: editJobIds,
        page: 1,
        size: 100,
      })
    },
    addContact(val, index) {
      this.form.contact_infos.splice(index + 1, 0, {
        type: val,
        value: '',
        is_main: false,
      })
    },

    removeContact(val, index) {
      this.form.contact_infos.splice(index, 1)
      if (val.is_main) {
        this.form.contact_infos.find(item => item.type === val.type && item.origin).is_main = true
      }
    },
    addExperience() {
      this.form.jobs.push({
        company: null,
        company_id: '',
        position: '',
        start_date: '',
        end_date: '',
        is_current: false,
        is_independent: false,
      })
    },
    removeFormItem(form, index) {
      form.splice(index, 1)
    },
    setMainContact(item, index) {
      if (item.is_main && this.form.contact_infos.length > 1) {
        this.$set(item, 'is_main', false)
      } else {
        this.$set(item, 'is_main', true)
        this.form.contact_infos.forEach((info, ind) => {
          if (info.type === item.type && ind !== index) {
            this.$set(info, 'is_main', false)
          }
          return info
        })
      }
    },

    changeType(select, index) {
      const data = _.cloneDeep(this.form.contact_infos)
      data.splice(index, 1)
      const list = data.filter(item => item.type === select.type)
      const has_main = list.filter(item => item.is_main).length
      if (!list.length || !has_main) {
        this.form.contact_infos[index].is_main = true
      }
      if (has_main) {
        this.form.contact_infos[index].is_main = false
      }
    },

    handleIndependentConsultant(val, exp, expIndex) {
      if (val) {
        // 独立专家 company id=> dev:97  release:97
        const value = {
          name: 'Independent Consultant',
          id: 97,
        }
        const hasIndex = this.companyList.data.findIndex(item => item.id === 97)
        hasIndex < 0 ? this.companyList.data.push(value) : ''
        this.form.jobs[expIndex].company = value
        this.form.jobs[expIndex].company_id = 97
        this.form.jobs[expIndex].position = 'Independent Consultant'
      } else {
        this.form.jobs[expIndex].company = null
        this.form.jobs[expIndex].company_id = 0
        this.form.jobs[expIndex].position = ''
      }
    },

    handleCurrentToggle(exp) {
      if (exp.is_current) {
        exp.end_date = moment.tz(moment(), this.timeZone).format('YYYY-MM')
      }
    },
    handleSave(val) {
      val.firstname = val.firstname.trim()
      val.lastname = val.lastname.trim()
      val.contact_infos = val.contact_infos.filter(item => item.value)

      if (this.isDialog) {
        val.background = this.formatGenerateBackground(val.jobs)
        val.latest_manually_update_at = moment().toISOString()

        return ConsultantAPI.createConsultantV2(JSON.parse(JSON.stringify(val))).then((data) => {
          this.$emit('save', data)
          if (data.entity.id) this.$message.success('Create successful')
          return false
        })
      }

      if (this.isEdit) {
        val.background = this.formatGenerateBackground(val.jobs)
        return ConsultantAPI.updateConsultant(val).then((data) => {
          this.goToProfile(data.id)
          this.$emit('save', val)
        })
      } else {
        if (this.import_form_cn_db) {
          val.rate = this.form.default_rate
          return ConsultantAPI.createConsultantFromCNDB({
            advisor: val,
            advisor_tc: this.advisor_tc,
          }).then(data => {
            this.goToProfile(data.id)
          })
        } else {
          val.latest_manually_update_at = moment().toISOString()
          return ConsultantAPI.createConsultant(val).then(data => {
            this.goToProfile(data.id)
          })
        }
      }
    },

    goToProfile(id) {
      this.$router.push({ name: 'ConsultantDetail', params: { advisor_id: id } })
    },
    formatGenerateBackground(jobs) {
      const companies = []
      const formatted_jobs = []
      const background = []
      handleFormatJobs()

      for (let i = 0; i < formatted_jobs.length; i++) {
        const job = formatted_jobs[i]
        let template = ''
        if (job.length === 1) {
          if (job[0].is_current) {
            template = `Since ${displayTime(job[0].start_date)}, this advisor is`
          } else {
            template = `From ${displayTime(job[0].start_date)} to ${handleCurrent(job[0])}, this advisor was `
          }
          template += `a ${job[0].position} at ${job[0].company.name}. `
        } else {
          if (job[0].is_current) {
            template = `Since ${displayTime(job[job.length - 1].start_date)}, `
          } else {
            template = `From ${displayTime(job[job.length - 1].start_date)} to ${handleCurrent(job[0])}, `
          }
          template += `the advisor held numerous positions at ${job[0].company.name} -- `
          for (let j = 0; j < job.length; j++) {
            template +=
              `${job[j].position} (${displayTime(job[j].start_date)} - ${handleCurrent(job[j])})${j !== job.length - 1
                ? '; '
                : '. '}`
          }
        }
        background.push(template)
      }

      return background.join('')

      function handleFormatJobs() {
        if (jobs.length) {
          jobs.forEach(item => {
            if (!item.company) return

            const index = companies.indexOf(item.company.name)
            if (index === -1) {
              companies.push(item.company.name)
              formatted_jobs.push([item])
            } else {
              formatted_jobs[index].push(item)
            }
          })
        }
      }

      function handleCurrent(item) {
        return item.is_current ? 'Current' : displayTime(item.end_date)
      }

      function displayTime(input) {
        if (!input) {
          return
        }

        if (['current'].includes(String(input).toLowerCase())) {
          return input
        }

        return moment(input).format('MMM YYYY')
      }
    },
    getCompanyOptions(val) {
      if (!val) return
      this.companySelectInput = val
      this.loadGetCompanyByEs(val)
    },
    loadGetCompany(params) {
      this.selectLoading = true
      return CompanyAPI.getCompanyList(params)
        .then(data => {
          this.companyList.data = data.list
          this.companyList.count = data.count
        })
        .finally(() => {
          this.selectLoading = false
        })
    },
    loadGetCompanyByEs(val) {
      this.selectLoading = true
      const params = {
        keyword: val,
        page: 1,
        size: 20,
      }
      return CompanyAPI.getCompanyListByEs(params)
        .then(data => {
          this.companyList.data = data.list.map(item => item.source)
          this.companyList.count = data.total
        })
        .finally(() => {
          this.selectLoading = false
        })
    },
    goToCreateCompany(name, chinese_name, index) {
      this.companySelectInput = name
      this.companySelectInputChinese = chinese_name
      this.jobEditingIndex = index
      this.dialogVisible = true
    },
    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },
    afterCreateCompany(val) {
      this.dialogVisible = false
      const job = this.form.jobs[this.jobEditingIndex]
      job.company = val
      job.company_id = val.id
      this.companyList.data.push(val)
    },
    autoGenerateBackground() {
      const background = this.$refs['background-format']
      background.handleFormatProfile()
      this.$nextTick(() => {
        this.form.background = background.$el.innerText.trim().replace(/\s+/g, ' ')
      })
    },
    checkExistInUS(ndb_id) {
      if (!ndb_id) return
      return ConsultantAPI.getConsultantList({ ndb_id: ndb_id }).then(data => {
        if (data.list.length > 0) {
          this.cn_advisor_exist_in_us = true
          this.cn_advisor = data.list[0]
        }
      })
    },
    async handleImportData() {
      const query = this.$route.query

      if (!query) return

      if (query.origin === 'cn_db') {
        this.import_form_cn_db = true
        const cn_tc_id = +query['advisor_tc[cn_db_tc_id]']

        this.rules.contact_infos = [{ required: false }]

        await this.checkExistInUS(+query.ndb_id)
        if (this.cn_advisor_exist_in_us) return

        const form = {
          ndb_id: query.ndb_id,
          is_cn: query.is_cn,
          firstname: query.firstname,
          lastname: query.lastname,
          background: query.background,
          type: cn_tc_id ? 'ADVISOR' : 'LEAD',
          location_id: null,
          zone_id_string: query.zone_id_string,
          locale: 'en-US',
          contact_infos: [],
          jobs: [],
          bank_account: {},
        }

        // 处理 contact_infos ->CN 导入 不需要contact info
        // const contacts_obj = [
        // {
        //   type: 'EMAIL',
        //   key: 'email[]',
        // }, {
        //   type: 'PHONE',
        //   key: 'mobile[]',
        // },
        // {
        //   type: 'LINKEDIN_URL',
        //   key: 'linkedin[]',
        // }]

        // contacts_obj.forEach(obj => {
        //   if (!query[obj.key]) return
        //
        //   const contact = (typeof (query[obj.key]) === 'string') ? [query[obj.key]] : query[obj.key]
        //
        //   const res = contact.map((item, index) => ({
        //     type: obj.type,
        //     value: item,
        //     is_main: !index,
        //   }))
        //   form.contact_infos = [...form.contact_infos, ...res]
        // })
        //
        // if (!form.contact_infos.length) {
        //   form.contact_infos = [
        //     {
        //       type: 'LINKEDIN_URL',
        //       value: '',
        //       is_main: true,
        //       origin: true,
        //     }]
        // }

        // 处理 location
        if (query.location) {
          this.$store
            .dispatch('options/getAllCountryList')
            .then(data => {
              const arr = query.location.split('-')
              const search_str = arr[arr.length - 1]

              const location = data.find(item => item.name.includes(search_str) || item.cn_name.includes(search_str))
              if (location) {
                form.location_id = location.id
                form.locale = (location.id === 108 || location.parent_id === 108) ? 'zh-CN' : 'en-US'
              }
              this.form.location_id = form.location_id
              this.form.locale = form.locale
            })
        }

        // 处理 jobs
        if (query['jobs_count']) {
          const company_us_search = []

          for (let i = 0; i < +query['jobs_count']; i++) {
            company_us_search.push(
              CompanyAPI.getCompanyList({ name_or_stock_code_contains: query['jobs[' + i + '][company]'] }))
          }

          Promise.all(company_us_search)
            .then(res => {
              const company_ids = []

              for (let i = 0; i < +query['jobs_count']; i++) {
                const is_current = query['jobs[' + i + '][end_date]'] === 'Current'

                form.jobs.push({
                  company: res[i].list.length ? res[i].list[0] : null,
                  company_id: res[i].list.length ? res[i].list[0].id : '',
                  company_name: query['jobs[' + i + '][company]'],
                  chinese_name: query['jobs[' + i + '][company_chinese]'],
                  position: query['jobs[' + i + '][position]'],
                  start_date: query['jobs[' + i + '][start_date]'],
                  end_date: is_current ? '' : query['jobs[' + i + '][end_date]'],
                  is_current,
                })

                if (res[i].list.length && !company_ids.includes(res[i].list[0].id)) {
                  company_ids.push(res[i].list[0].id)
                  this.companyList.data.push(res[i].list[0])
                }
              }

              if (!form.jobs.length) delete form.jobs
            })
        }

        // 处理 bank_account
        const countries = ['US', 'UK', 'BRAZIL', 'CANADA', 'INDIA', 'VIETNAM', 'RUSSIA', 'SINGAPORE', 'CHINA', 'MEXICO']
        if (query['bank_account[account_name]']) {
          for (const key in query) {
            if (key.includes('bank_account')) {
              const bank_key = key.replace('bank_account', '').slice(1, -1)
              form.bank_account[bank_key] = query[key]
            }
          }
          const account_type = this.$store.state.options.account_type_options.find(
            item => item.id === form.bank_account['account_type'])
          form.bank_account['account_type'] = account_type.value
          const receiver_type = this.$store.state.options.receiver_type_options.find(
            item => item.id === form.bank_account['receiver_type'])
          form.bank_account['receiver_type'] = receiver_type.value
          if (!countries.includes(form.bank_account['bank_area'])) {
            form.bank_account['bank_area'] = 'REST_OF_WORD'
          }
          if (!form.bank_account.account_type) {
            form.bank_account.account_type = null
          }
        }

        // 处理 TC
        if (cn_tc_id) {
          const respond_time = query['advisor_tc[respond_time]'] ? moment.tz(Number(query['advisor_tc[respond_time]']),
            this.timeZone).toISOString() : null
          this.advisor_tc = {
            cn_db_tc_id: cn_tc_id,
            comment: query['advisor_tc[comment]'],
            respond_time: respond_time,
            create_time: respond_time,
            rate: +query['advisor_tc[rate]'],
            rate_currency: query['advisor_tc[rate_currency]'],
          }
          this.getTcIdInUSDb(cn_tc_id)
          form.default_rate = +query['advisor_tc[rate]']
          form.rate_currency = query['advisor_tc[rate_currency]']
        }

        Object.assign(this.form, form)
      }
    },

    getTcIdInUSDb(cn_tc_id) {
      return TcAPI.getTcIdInUSDb(cn_tc_id).then(data => {
        if (!data) {
          this.$notify({
            title: 'Tips',
            message: 'The TC signed by the expert, for which no equivalent TC was found in the US DB, will be matched to the Current TC of the US.',
            duration: 0,
          })
        }
      })
    },

    initContactInfo() {
      return [
        {
          type: 'EMAIL',
          value: '',
          is_main: true,
          origin: true,
        }, {
          type: 'PHONE',
          value: '',
          is_main: true,
          origin: true,
        }, {
          type: 'LINKEDIN_URL',
          value: '',
          is_main: true,
          origin: true,
        }]
    },

    formatContactInfo() {
      if (!this.form.contact_infos.length) {
        this.form.contact_infos = this.initContactInfo()
      } else {
        const list = this.form.contact_infos
        const type_list = ['EMAIL', 'PHONE', 'LINKEDIN_URL']

        type_list.forEach(type => {
          if (list.find(item => item.type === type)) {
            this.form.contact_infos.find(item => item.type === type).origin = true
          } else {
            this.form.contact_infos.push({
              type: type,
              value: '',
              is_main: true,
              origin: true,
            })
          }
        })
      }
    },

    /**
     * 是否显示 "设置主要联系方式" 按钮
     * 同一种联系方式出现多次时，提供 "set main" 选项
     * @param data
     * @returns {boolean}
     */
    isDisplaySetMain(data) {
      return this.form.contact_infos.filter(item => {
        return item.type === data.type
      }).length > 1
    },

    actionLocationChange(location_id) {
      return getLocationById(location_id)
        .then(data => {
          if (data) {
            if (data['id_path'].includes('108')) {
              this.form.locale = 'zh-CN'
            } else {
              this.form.locale = 'en-US'
            }
          }
        })
    }
    ,
  },
}
</script>

<style scoped lang="scss">

.contact-warning-label {
  margin-left: 110px;
  display: block;
  vertical-align: middle;
  font-size: 12px;
  /*color: #606266;*/
  line-height: 14px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.w-80 {
  width: 80%;
}

.w-500px {
  width: 500px;
}

.icon-font {
  font-size: 12px;
  margin: 0 5px;
}

.is-dialog-custom {
  margin-top: 15px;
  width: 80%;
  display: block !important;
}

.el-date-editor.el-input {
  width: auto;
}

::v-deep .form-item {
  display: inline-block;
  margin-bottom: 0;

  > .el-form-item__content {
    margin-left: 0;
  }
}
</style>
