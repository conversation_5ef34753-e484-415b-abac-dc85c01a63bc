import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      rules: {
        firstname: [{ required: true, trigger: 'change' }],
        lastname: [{ required: true, trigger: 'change' }],
        type: [{ required: true, trigger: 'change' }],
        status: [{ required: true, trigger: 'change' }],
      },
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
      'contact_info_options',
    ]),
  },
}
