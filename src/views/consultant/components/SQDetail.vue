<template>
  <div>
    <el-tag type="info" class="tag-btn" @click="initSQ()">questionnaire</el-tag>

    <questionnaire-show :branch-id="id" has-answer @close="id=null" />
  </div>
</template>

<script>
import QuestionnaireShow from '@/components/Question/QuestionnaireShowV2'

export default {
  name: 'SQDetail',
  components: { QuestionnaireShow },
  props: {
    instanceId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      id: null,
    }
  },

  methods: {
    initSQ() {
      this.$set(this, 'id', this.instanceId)
    },

  },

}
</script>

<style scoped lang="scss">

  .tag-btn {
    background-color: #5ea3ea;
    color: #fff;

    &:hover {
      background-color: #42B983;
      cursor: pointer;
    }
  }
</style>
