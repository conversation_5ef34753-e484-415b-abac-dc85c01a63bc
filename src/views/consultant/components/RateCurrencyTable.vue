<template>
  <el-table :data="tableData" border>
    <el-table-column width="90">
      <template v-slot="{$index}">
        {{ $index ? 'Rate Median' : 'Count' }}
      </template>
    </el-table-column>
    <el-table-column label="USD">
      <template v-slot="{row, $index}">
        {{ row[`USD${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
    <el-table-column label="RMB">
      <template v-slot="{row, $index}">
        {{ row[`RMB${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
    <el-table-column label="JPY">
      <template v-slot="{row, $index}">
        {{ row[`JPY${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
    <el-table-column label="EUR">
      <template v-slot="{row, $index}">
        {{ row[`EUR${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
    <el-table-column label="GBP">
      <template v-slot="{row, $index}">
        {{ row[`GBP${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
    <el-table-column label="SGD">
      <template v-slot="{row, $index}">
        {{ row[`SGD${$index ? '_median' : ''}`] }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'RateCurrencyTable',
  props: {
    tableData: Array,
  },
}
</script>
