<template>
  <div class="inline-block">
    <el-button
      class="filter-item"
      type="primary"
      :loading="loading"
      :disabled="disabledAddToProject"
      @click="checkBeforeAddToProject">
      {{ `${btnText[add_type]} To Project` }}
    </el-button>
    <el-dialog
      :visible.sync="warningDialogVisible"
      title="Add To Project"
      width="50%"
      @close="dialogClose()">
      <div>
        <slot v-for="(val, key) in banned_advisors">
          <el-alert
            v-if="val && val.length"
            :key="key"
            type="warning"
            show-icon
            class="mb-8">
            <template #title>
              These experts will be filtered, because
              <slot v-if="key === 'ADVISOR'">
                they are
              </slot>
              <slot v-if="key === 'CURRENT_JOB_COMPANY'">
                their current company
              </slot>
              <slot v-if="key === 'LOCATION'">
                their location
              </slot>
              in the
              <router-link
                class="text-truncate link"
                target="_blank"
                :to="{ name: 'ClientBlackList', params: { client_id: client_id }}"
              >
                client black list
              </router-link>
              <slot v-if="['ADVISOR', 'LOCATION'].includes(key)">
                , or have been banned in all clients
              </slot>
            </template>
            <span v-for="(item, index) in val" :key="index">
              <router-link-advisor :data="item" />
              <slot v-if="index + 1 !== val.length">,</slot>
            </span>
          </el-alert>
        </slot>
        <slot v-for="item in disabled_data">
          <el-alert
            v-if="item.list.length"
            :key="item.key"
            type="warning"
            show-icon
            class="mb-8">
            <template #title>
              {{item.prompt}}
              <span v-for="(temp,index) in item.list" :key="index">
                <router-link-advisor :data="getAdvisorData(temp.advisor_id)" />
                <slot v-if="index + 1 !== item.list.length">,</slot>&nbsp;
              </span>
            </template>
          </el-alert>
        </slot>
        <el-alert
          v-if="allow_list.length"
          title="These experts are allowed to be added:"
          type="success"
          show-icon
          class="mb-8">
          <span v-for="(item, index) in allow_list" :key="index">
            <router-link-advisor
              :data="getAdvisorData(item.task.advisor_id)" />
            <slot v-if="index+1 !== allow_list.length">,</slot>&nbsp;
          </span>
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!allow_list.length"
          @click="addToProject(allow_list)">Add To Project</el-button>
        <el-button type="text" @click="warningDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import { RouteTools } from '@/utils/tool'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import { disableAddToProjectPromptFormat } from '@/utils/tool'

export default {
  name: 'AddToTargetProject',
  components: { RouterLinkAdvisor },
  props: {
    selectAdvisors: {
      type: Array,
      default() {
        return []
      },
    },
    parentDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      btnText: {
        LEAD: 'Add Lead',
        ADVISOR: 'Add Advisor',
      },
      warningDialogVisible: false,
      disabled_data: [],
      allow_list: [],
      banned_advisors: {},
    }
  },
  computed: {
    add_type() {
      return this.$route.query.type
    },
    isCommonProject() {
      return this.$route.query.is_common
    },

    addAdvisorOrigin() {
      return this.$route.query.origin
    },

    addAdvisorOriginId() {
      return this.$route.query.origin_id
    },

    project_id() {
      return this.$route.query.project_id
    },

    client_id() {
      return this.$route.query.client_id
    },

    disabledAddToProject() {
      return !this.selectAdvisors.length || this.parentDisabled
    },
  },
  methods: {
    async checkBeforeAddToProject() {
      if (this.selectAdvisors.length === 0) {
        return
      }
      try {
        this.loading = true
        this.allow_list = []
        // check 黑名单
        await this.checkClientBlackList()
        const banned_advisors_ids = Object.values(this.banned_advisors)?.flat()
        const advisors = this.selectAdvisors.filter(item => {
          return !banned_advisors_ids.some(advisor => advisor.id === item.id)
        })
        // check before
        const params = this.formatAddToProjectParams(advisors)
        const data = await ProjectAPI.checkBeforeAdvisorAddToProject(this.project_id, params)

        const validated_types = [
          'duplicate_in_angle',
          'empty_employment_history_advisor',
          'employees_conflict_investment_target',
          'with_not_current_region_advisor',
          'outsource_limit_advisor',
        ]
        this.disabled_data = disableAddToProjectPromptFormat(validated_types, data)

        const allow_to_add = !validated_types.some(type => data[type].length) && !banned_advisors_ids.length
        if (allow_to_add) {
          return this.addToProject(params)
        } else {
          this.allow_list = params.filter(item => allowToAddAdvisor(validated_types, item.task.advisor_id, data))
          this.warningDialogVisible = true
        }
      } finally {
        this.loading = false
      }

      function allowToAddAdvisor(validated_types, advisor_id, data) {
        return !validated_types.some(key => data[key] && data[key].find(item => parseInt(item.advisor_id) === parseInt(advisor_id)))
      }
    },

    getAdvisorData(advisor_id) {
      return this.selectAdvisors.find(item => item.id === advisor_id)
    },

    checkClientBlackList() {
      this.banned_advisors = {}
      return ClientAPI.checkClientBlockList('ADVISOR', {
        client_id: this.client_id,
        advisor_ids: this.selectAdvisors.map(advisor => advisor.id),
      })
        .then(data => {
          for (const reason in data.advisor_blocked_reason_map) {
            const banned_advisors = this.selectAdvisors.filter(advisor => data.advisor_blocked_reason_map[reason].includes(advisor.id))
            this.$set(this.banned_advisors, reason, banned_advisors)
          }
        })
    },

    addToProject(params) {
      this.loading = true
      return ProjectAPI.addAdvisor(this.project_id, params)
        .then(() => {
          return RouteTools.removeCurrentRoute(this.$route)
        })
        .then(() => {
          let route_name
          if (this.isCommonProject) {
            route_name = this.add_type === 'ADVISOR' ? 'ConsultantTasks' : 'CommonProjectLeads'
          } else {
            route_name = this.add_type === 'ADVISOR' ? this.addAdvisorOrigin : 'ProjectLeads'
          }
          this.$emit('update')
          this.$router.push({
            name: route_name,
            params: {
              project_id: this.project_id,
            },
          })
        })
    },

    dialogClose() {
      // 添加所有搜索结果到项目的弹窗关闭后 清空数值
      if (this.parentDisabled) {
        this.$emit('update:parentDisabled', false)
        this.$emit('update:selectAdvisors', [])
        this.$emit('update')
      }
    },

    formatAddToProjectParams(advisors) {
      if (this.isCommonProject) {
        return advisors.map(item => {
          return {
            task: {
              advisor_id: item.id,
              type: 'ADVISOR_TASK',
            },
          }
        })
      } else if (['ProjectClientTasksSST', 'ProjectScreenedExperts', 'ProjectClientTasksMcK', 'ProjectClientTasks', 'ProjectTasks'].includes(this.addAdvisorOrigin)) {
        return advisors.map(item => {
          return {
            task: {
              angle_id: this.addAdvisorOriginId,
              advisor_id: item.id,
              client_contact_id: null,
              willingness: 'UNKNOWN',
            },
          }
        })
      } else {
        return advisors.map(item => {
          return {
            task: {
              advisor_id: item.id,
              client_contact_id: null,
              willingness: 'UNKNOWN',
            },
          }
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
