<template>
  <div>
    <el-button type="text" class="el-icon-tickets" @click="getSQData()" />

    <el-dialog
      :visible.sync="SQDialogVisible"
      :close-on-click-modal="false"
      title="Screening Questions / Responses for Previous Projects"
      width="50%"
      append-to-body
    >
      <div class="dialog-body scrollbar-narrow">
        <div v-for="(item, index) in questionnaire_list" :key="index" class="SQ-item">
          <div class="flex justify-content-between">
            <div v-if="item.task" class="link pa-5px" @click="linkToTask(item)">{{linkToTaskText(item)}}</div>
            <SQDetail class="detail-tag" :instance-id="item.instance_id" />
          </div>
          <div v-for="(question, indexKey) in item.question_list" :key="indexKey" class="q-list">
            <span><b><span v-html="question.question_title" /></b></span>
            <p v-if="question.answer_text" class="q-answer">
              <span v-html="question.answer_text" />
            </p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SQDetail from './SQDetail'
import _ from 'lodash'
import InquiryAPI from '@/api/inquiry'
import { mapState } from 'vuex'
import Filters from '@/utils/filters'

export default {
  name: 'SearchSQ',
  components: { SQDetail },
  props: {
    inquiryList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },

  data() {
    return {
      SQDialogVisible: false,
      project_list: [],
      questionnaire_list: [],
    }
  },

  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
    }),
  },

  methods: {
    getSQData() {
      const params = {
        ids: [...new Set(this.inquiryList.map(item => item.instance_id))].join(),
        extra: 'task.project,task.angle,questions',
      }
      return InquiryAPI.getInstances(params).then(data => {
        this.project_list = data.list.map(item => {
          return {
            instance_id: item.id,
            project: item.task?.project,
            task: item.task,
            submit_time: item.submit_time,
          }
        })
      }).finally(() => {
        this.formatList(_.cloneDeep(this.inquiryList))
        this.SQDialogVisible = true
      })
    },

    // 把同一个问卷的问题 合并到一起
    formatList(list) {
      if (list === []) return []
      const arr = []
      list.map((item) => {
        const index = arr.findIndex(arr => (arr.instance_id === item.instance_id))
        const match_project = this.project_list.find(project => project.instance_id === item.instance_id)
        if (index > -1) {
          arr[index].question_list.push(item)
        } else {
          arr.push({ instance_id: item.instance_id,
            question_list: [item],
            project: match_project?.project,
            task: match_project?.task,
            submit_time: match_project?.submit_time })
        }
      })
      this.questionnaire_list = arr
    },

    linkToTaskText(item) {
      if (!item?.project || !item?.task) return ''
      return `${item.project?.name}-${item.task?.angle?.name || ''}-${Filters.momentFormat(item.submit_time, 'MM/DD/YYYY')}`
    },

    linkToTask(item) {
      if (!item.task?.project_id) return
      let target_router_name = ''

      if (item.project.sub_type === 'Investor Call') {
        target_router_name = 'ProjectClientTasksSST'
      } else if (item.client_id === this.McKinseyId) {
        target_router_name = 'ProjectClientTasksMcK'
      } else {
        target_router_name = 'ProjectClientTasks'
      }

      if (!item.task.angle_id) {
        target_router_name = 'ProjectLeads'
      }

      this.$router.push({
        name: target_router_name,
        params: {
          project_id: item.task.project_id,
        },
        query: {
          task_id: item.task?.id || undefined,
          angle_id: item.task.angle_id || undefined,
        },
      })
    },

  },
}
</script>

<style scoped lang="scss">
.SQ-item {
  position: relative;
  margin: 10px;
  border: 1px solid #5a5e66;
  border-radius: 4px;

  .detail-tag {
   margin-left: auto;
  }

  .project-tag {
    position: absolute;
    right: 90px;
    top: 3px;
    font-size: 14px;
  }

}

.SQ-item div:nth-child(2) {
  border-top: 0;
}

.q-list {
  padding: 15px 10px 10px 10px;
  border-top: 1px solid #eae5e5;
}

.q-answer {
  background-color: #f3f1f1;
  border-radius: 4px;
  padding-left: 10px;
}

.dialog-body {
  max-height: 500px;
  overflow: auto;
}

.pa-5px{
  padding:5px
}
</style>
