<template>
  <div>
    <div :ref="`height_${data.id}`" :class="{'content-height':type!=='background'}">
      <div v-if="type==='employment'">
        <div v-for="(item, index) in sort_jobs" :key="index" class="bottom-padding">
          <span :class="{'bold':item.is_current}">
            <span v-html="$sanitizeHtml(item.company)" /> | <span v-html="$sanitizeHtml(item.title)" />
            {{
              ` | ${jobDateFormat(item.start_date)} – ${item.is_current
                ? 'current'
                : jobDateFormat(item.end_date)}`
            }}
            <div v-if="item.description" class="info" v-html="$sanitizeHtml(item.description)" />
          </span>
        </div>
      </div>
      <div v-if="type==='background'">
        <span v-html="$sanitizeHtml(data.background)" />
      </div>
    </div>
    <div v-if="text_over && type==='employment'" class="more-btn">
      <el-link v-if="!text_more" :underline="false" type="primary" @click="showMore">More</el-link>
      <el-link v-if="text_more" :underline="false" type="primary" @click="showCollapse">Collapse</el-link>
    </div>
  </div>
</template>

<script>
import moment from 'moment-timezone'
import _ from 'lodash'

export default {
  name: 'ConsultantPrevious',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
    highlightWords: {
      type: Array,
      default: () => {
        return []
      },
    },
    type: String,
  },
  data() {
    return {
      text_over: false,
      text_more: false,
      sort_jobs: [],
    }
  },

  mounted() {
    this.sort_jobs = this.sortJobs(this.data.jobs)
    // background 移除 show more/employment 暂未找到合适高度方式 show more 失效
    setTimeout(() => {
      const text_content = this.$refs[`height_${this.data.id}`]
      const scrollHeight = text_content.scrollHeight

      this.$set(this, 'text_over', scrollHeight > text_content.clientHeight)
    }, 300)
  },

  methods: {
    sortJobs(list) {
      let current_list = []
      let other_list = []
      list.forEach(item => {
        if (item.is_current) {
          current_list.push(item)
        } else {
          other_list.push(item)
        }
      })
      other_list = _.orderBy(
        other_list,
        'end_date', 'desc',
      )
      current_list = _.orderBy(
        current_list,
        'start_date', 'desc',
      )
      return current_list.concat(other_list)
    },
    showMore() {
      this.text_more = true
      const text_content = this.$refs[`height_${this.data.id}`]
      text_content.style.maxHeight = text_content.scrollHeight + 'px'
    },

    showCollapse() {
      this.text_more = false
      const text_content = this.$refs[`height_${this.data.id}`]
      text_content.style.maxHeight = '90px'
    },

    jobDateFormat(date) {
      if (!date) return date
      const list = date.split('-')
      switch (list.length) {
        case 2:
          return moment(date)
            .format('MM/YYYY')
        case 3:
          return moment(date)
            .format('MM/DD/YYYY')
        default:
          return date
      }
    },
  },
}
</script>

<style scoped lang="scss">
.content-height {
  max-height: inherit;
  //max-height: 90px;
  overflow-y: hidden;
}

.more-btn {
  /*float: right;*/
  padding-right: 4px;
}

.bold{
  font-weight: bold;
  color: #333;
}

.bottom-padding{
  padding-bottom: 1em;
}
</style>
