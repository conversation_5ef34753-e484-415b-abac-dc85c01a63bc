<template>
  <div>
    <div class="flex justify-content-center mb-5px">
      <div class="profile-name">
        <el-tag
          v-if="showLeadTag"
          class="tag-lead"
          type="info"
          effect="dark"
          size="mini"
        >L
        </el-tag>
        <el-tag
          v-if="info.status === 'BLACKLIST'"
          class="tag-lead"
          type="danger"
          effect="dark"
          size="mini"
        >
          <svg-icon icon-class="ban" />
          Ban
        </el-tag>
        <router-link
          class="consultant-name link"
          :to="{ name: 'ConsultantDetail', params: { advisor_id: info.id } }">
          <span v-html="$sanitizeHtml(info.name)" />
        </router-link>
      </div>
      <div v-if="!isSqTable" class="text-right">
        <div v-if="info.rate" class="consultant-rate" :title="`Rate ${info.rate}`">
          {{ info.rate_currency|getLabel(currency_option) }}{{ info.rate }}
        </div>
        <div class="consultant-location">{{ info.location }}</div>
      </div>
    </div>
    <div v-if="info.user_advisor_tags && info.user_advisor_tags.length" class="white-space-normal">
      <div
        v-for="tag in info.user_advisor_tags"
        :key="tag.name"
        class="consultant-tag"
        :class="`${tag.color}-color`">
        {{ tag.name }}
      </div>
    </div>
    <div v-if="isSqTable" class="text-nowrap-hidden">
      <div>
        <span v-if="info.rate" class="consultant-rate" :title="`Rate ${info.rate}`">
          {{ info.rate_currency|getLabel(currency_option) }}{{ info.rate }}
        </span>
        <span class="consultant-location">{{ info.location }}</span>
      </div>
      <div><b>TC</b> - {{info.latest_sent_tc_time|momentFormat('MM/DD/YY')}}</div>
      <div><b>CC</b> - {{info.consultations_count}}</div>
      <div><b>SB</b> - {{info.sourced_by_username}}</div>
    </div>
    <div v-if="!isSqTable">
      <div class="text-nowrap-hidden">
        <span v-html="$sanitizeHtml(info.title)" />
      </div>
      <div class="text-nowrap-hidden">
        <span v-html="$sanitizeHtml(info.company)" />
      </div>
    </div>
  </div>

</template>

<script>
export default {
  name: 'ConsultantInfo',
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      },
    },
    isSqTable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currency_option: [
        { label: '$', value: 'USD' },
        { label: 'S$', value: 'SGD' },
        { label: 'RM', value: 'MYR' },
        { label: '¥', value: 'RMB' },
        { label: '€', value: 'EUR' },
        { label: '£', value: 'GBP' },
        { label: 'J.¥', value: 'JPY' },
      ],
    }
  },
  computed: {
    showLeadTag() {
      return this.info.type === 'LEAD'
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>

.profile-name {
  .tag-lead {
    height: auto;
    line-height: 12px;
    padding: 0 2px;
    border-radius: 3px;

    &:hover:after {
      content: 'ead'
    }
  }

  .consultant-name {
    vertical-align: baseline;
  }
}

.consultant-rate {
  display: inline-block;
  color: #c5a52c;
  transform: scale(0.9);
  transform-origin: right center;
}

.consultant-location {
  display: inline-block;
  color: #86868b;
}

.white-space-normal {
  white-space: normal;
}

.consultant-tag {
  display: inline-flex;
  align-items: center;
  flex-shrink: 1;
  height: 20px;
  border-radius: 3px;
  padding-left: 5px;
  padding-right: 5px;
  line-height: 120%;
  margin: 0 5px 5px 0;
}
</style>
