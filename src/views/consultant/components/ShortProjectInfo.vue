<template>
  <div>
    <project-search
      v-model="project_id"
      class="w-200px filter-item"
      :multiple="false"
      only-user-project
      @change="handleSearch" />
    <div v-if="projectData" v-loading="searchLoading" style="margin-top: 50px;">
      <div class="pre-wrap" style="font-size:12px; line-height: 1.4;">{{ projectData.request_content }}</div>
    </div>
  </div>
</template>

<script>
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import ProjectAPI from '@/api/project'

export default {
  name: 'ShortProjectInfo',
  components: { ProjectSearch },
  props: {
    projectId: [Number, String],
  },
  data() {
    return {
      project_id: this.projectId || null,
      searchLoading: false,
      projectData: null,
    }
  },
  mounted() {
    if (this.projectId) this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.searchLoading = true
      return ProjectAPI.getProject(this.project_id).then(data => {
        this.projectData = data
      }).finally(() => {
        this.searchLoading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item--mini.el-form-item {
  margin-bottom: 5px;
}
</style>
