<template>
  <div class="add-btn">
    <el-button
      v-if="!taskAngle"
      type="primary"
      size="mini"
      :disabled="disabledAddToProject"
      @click="actionAddToProject()">Add to Project
    </el-button>
    <ico-button
      v-if="taskAngle"
      class="filter-item"
      tooltip="Add To Project"
      no-border
      @action="actionAddToProject">
      <el-icon slot="ico" class="el-icon-plus" />
    </ico-button>

    <el-dialog
      title="Add to Project"
      append-to-body
      :visible.sync="projectDialogVisible"
      :close-on-click-modal="false"
      width="30%"
      @close="dialogClose()"
    >
      <div v-loading="dialogLoading">
        <el-select
          v-model="select_project"
          filterable
          clearable
          remote
          placeholder="Project Name"
          class="remote-select"
          :loading="searchLoading"
          :remote-method="searchProject"
          no-data-text="No matching data"
          @change="handleChangeProject"
        >
          <el-option
            v-for="item in project_option"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <div v-if="select_project" class="inline-block">
          <el-select
            v-model="select_angle"
            filterable
            clearable
            placeholder="Angle"
          >
            <el-option
              v-for="item in angle_option"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
          <el-button v-if="taskAngle" type="text" icon="el-icon-plus" @click="createDialogShow=true">Create</el-button>
        </div>

        <slot v-for="(val, key) in banned_advisors">
          <el-alert
            v-if="val && val.length"
            :key="key"
            type="warning"
            show-icon
            class="mt-8">
            <template #title>
              These experts will be filtered, because
              <slot v-if="key === 'ADVISOR'">
                they are
              </slot>
              <slot v-if="key === 'CURRENT_JOB_COMPANY'">
                of their current company
              </slot>
              <slot v-if="key === 'LOCATION'">
                of their location
              </slot>
              in the
              <router-link
                class="text-truncate link"
                target="_blank"
                :to="{ name: 'ClientBlackList', params: { client_id: select_project_client_id }}"
              >
                client black list
              </router-link>
              <slot v-if="['ADVISOR', 'LOCATION'].includes(key)">
                , or have been banned in all clients
              </slot>
            </template>
            <span v-for="(item, index) in val" :key="index">
              <router-link-advisor :data="item" />
              <slot v-if="index + 1 !== val.length">,</slot>
            </span>
          </el-alert>
        </slot>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="submitDisabled"
          :loading="dialogLoading || loading"
          @click="checkBeforeAddToProject()">Submit</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="Create Angle"
      append-to-body
      width="480px"
      :close-on-click-modal="false"
      :visible.sync="createDialogShow"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="150px"
        size="mini">
        <el-form-item label="Task Angle Name" prop="name" required>
          <el-input ref="name" v-model="form.name" />
        </el-form-item>
        <el-form-item label="Support Members" prop="members" required>
          <el-select
            v-model="form.members"
            class="multiple-select"
            placeholder="search"
            value-key="user_id"
            multiple
            learable
            filterable>
            <el-option
              v-for="item in members_option"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="actionAngleCancel">Cancel</el-button>
        <el-button type="primary" @click="actionAngleCreate">Confirm</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="warningDialogVisible"
      append-to-body
      title="Add To Project"
      width="50%">
      <div>
        <slot v-for="item in disabled_data">
          <el-alert
            v-if="item.list.length"
            :key="item.key"
            type="warning"
            show-icon
            class="mb-8">
            <template #title>
              {{item.prompt}}
              <span v-for="(temp,index) in item.list" :key="index">
                <router-link-advisor :data="getAdvisorData(temp.advisor_id)" />
                <slot v-if="index + 1 !== item.list.length">,</slot>&nbsp;
              </span>
            </template>
          </el-alert>
        </slot>
        <el-alert
          v-if="allow_list.length"
          title="These experts are allowed to be added:"
          type="success"
          show-icon
          class="mb-8">
          <span v-for="(item, index) in allow_list" :key="index">
            <router-link-advisor
              :data="getAdvisorData(item.task.advisor_id)" />
            <slot v-if="index+1 !== allow_list.length">,</slot>
          </span>
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!allow_list.length"
          @click="addToProject(allow_list)">Add To Project</el-button>
        <el-button type="text" @click="warningDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/project'
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import { mapGetters } from 'vuex'
import IcoButton from '@/components/IcoButton'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import { disableAddToProjectPromptFormat } from '@/utils/tool'

export default {
  name: 'AddToProject',
  components: { IcoButton, RouterLinkAdvisor },
  props: {
    taskAngle: {
      type: String,
      default: '',
    },
    advisors: {
      type: Array,
      default() {
        return []
      },
    },
    inBlacklist: {
      type: Boolean,
      default: false,
    },

    parentDisabled: {
      type: Boolean,
      default: false,
    },

    initProject: Object,
  },
  data() {
    return {
      loading: false,
      warningDialogVisible: false,
      dialogLoading: false,
      select_project: undefined,
      select_project_client_id: undefined,
      select_angle: undefined,
      projectDialogVisible: false,
      createDialogShow: false,
      searchLoading: false,
      project_option: [],
      angle_option: [],
      members_option: [],
      disabled_data: [],
      allow_list: [],
      banned_advisors: {},
      form: {
        name: this.taskAngle,
        members: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'name',
    ]),

    disabledAddToProject() {
      return this.advisors.length < 1 || this.inBlacklist || this.parentDisabled
    },

    /* 如果所选专家都是未签署过consultation TC 的专家 则允许不选择angle就可以添加到项目的leads list,
       如果包含签署过consultation TC 的专家， 则只能添加到项目的Angle中 */
    submitDisabled() {
      const has_advisor_type = this.advisors.find(item => item.tc_term_valid.CONSULTATION)
      const disabled = has_advisor_type ? !this.select_angle : !this.select_project
      return disabled || this.dialogLoading || this.loading
    },
  },

  methods: {
    checkBeforeAddToProject() {
      const banned_advisors_ids = Object.values(this.banned_advisors)?.flat()
      const advisors = this.advisors.filter(item => {
        return !banned_advisors_ids.some(advisor => advisor.id === item.id)
      })
      if (!advisors.length) {
        this.actionCancel()
        return
      }
      this.allow_list = []
      this.loading = true
      const params = advisors.map(item => {
        return {
          task: {
            advisor_id: item.id,
            angle_id: this.select_angle,
            client_contact_id: null,
            willingness: 'UNKNOWN',
          },
        }
      })

      return ProjectAPI.checkBeforeAdvisorAddToProject(this.select_project, params)
        .then(data => {
          const validated_types = [
            'duplicate_in_angle',
            'empty_employment_history_advisor',
            'employees_conflict_investment_target',
            'with_not_current_region_advisor',
            'outsource_limit_advisor',
          ]
          this.disabled_data = disableAddToProjectPromptFormat(validated_types, data)
          const allow_to_add = !validated_types.some(type => data[type].length)
          if (allow_to_add) {
            return this.addToProject(params)
          } else {
            this.allow_list = params.filter(item => allowToAddAdvisor(validated_types, item.task.advisor_id, data))
            this.warningDialogVisible = true
          }
        })
        .finally(() => {
          this.loading = false
        })

      function allowToAddAdvisor(validated_types, advisor_id, data) {
        return !validated_types.some(key => data[key] && data[key].find(item => parseInt(item.advisor_id) === parseInt(advisor_id)))
      }
    },

    getAdvisorData(advisor_id) {
      return this.advisors.find(item => item.id === advisor_id)
    },

    actionAddToProject() {
      if (!this.initProject) {
        this.banned_advisors = {}
      }
      this.projectDialogVisible = true
      this.searchProject().then(() => {
        if (!this.initProject) return
        this.select_project = this.initProject.id
        const project = this.project_option.find(item => item.id === this.initProject.id)
        if (!project) {
          this.project_option.unshift({
            name: this.initProject.name,
            id: this.initProject.id,
            angles: this.initProject.angles,
            members: this.initProject.members,
          })
        }
        this.matchAngle()
      })
    },

    handleChangeProject(val) {
      this.matchAngle()
      this.select_project_client_id = 0
      this.banned_advisors = {}
      if (val) {
        this.checkClientBlackList()
      }
    },

    matchAngle() {
      this.select_angle = undefined
      this.angle_option = this.project_option.find(item => +item.id === +this.select_project).angles
      this.members_option = this.project_option.find(item => +item.id === +this.select_project).members.map(item => {
        return { id: item.uid, name: item.user.name }
      })
    },

    checkClientBlackList() {
      const project = this.project_option.find(item => item.id === this.select_project)
      this.select_project_client_id = project.client_id
      return ClientAPI.checkClientBlockList('ADVISOR', {
        client_id: project.client_id,
        advisor_ids: this.advisors.map(advisor => advisor.id),
      })
        .then(data => {
          for (const reason in data.advisor_blocked_reason_map) {
            const banned_advisors = this.advisors.filter(advisor => data.advisor_blocked_reason_map[reason].includes(advisor.id))
            this.$set(this.banned_advisors, reason, banned_advisors)
          }
        })
    },

    addToProject(params) {
      this.dialogLoading = true

      return API.addAdvisor(this.select_project, params).then(data => {
        this.$message({
          message: 'Add successful',
          type: 'success',
        })
        this.projectDialogVisible = false
        this.warningDialogVisible = false
        this.$emit('update')
      }).finally(() => {
        this.dialogLoading = false
      })
    },

    actionCancel() {
      this.projectDialogVisible = false
      this.select_project = undefined
      this.select_angle = undefined
    },

    actionAngleCreate() {
      const params = {
        project_id: this.select_project,
        name: this.form.name,
        inquiry_branch_id: '',
        members: this.form.members.map(item => {
          return { user_id: item }
        }),
      }

      return ProjectAPI.saveAngle(params)
        .then((data) => {
          this.angle_option.push({ id: data.id, name: data.name })
          this.select_angle = data.id
        })
        .finally(() => {
          this.actionAngleCancel()
        })
    },

    actionAngleCancel() {
      this.createDialogShow = false
      this.form.name = this.taskAngle
      this.form.members = []
    },

    dialogClose() {
      // 添加所有搜索结果到项目的弹窗关闭后 清空数值
      if (this.parentDisabled) {
        this.$emit('update:parentDisabled', false)
        this.$emit('update:advisors', [])
        this.$emit('update')
      }
    },

    searchProject(val) {
      const params = {
        page: 1,
        size: 20,
        member_ids: this.uid,
        name_like: val || undefined,
        extra: 'angles,members.user',
      }
      this.searchLoading = true
      return API.getProjectList(params)
        .then(data => {
          this.project_option = data.list.map(item => {
            return { name: item.name, id: item.id, angles: item.angles, members: item.members, client_id: item.client_id }
          })
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
  },
}
</script>

<style scoped>
.add-btn {
  display: inline-block;
  word-spacing: 0;
}
</style>
