<template>
  <el-carousel v-if="sqList.length" :autoplay="false" :arrow="showArrow" indicator-position="none" @change="setActiveItem">
    <el-carousel-item v-for="(item,index) in target" :key="item.id">
      <div class="sq-box scrollbar-narrow">
        <div class="color-black">
          <b v-html="item.question_title" />
          <el-tag v-if="index===sqList.length-1 && showArrow==='always'" type="info" class="xs-tag" size="mini">end</el-tag>
        </div>
        <div class="answer-bgc" v-html="item.answer_text" />
        <div class="sq-checkbox">
          <el-button
            type="success"
            plain
            :icon="item.checked ? 'el-icon-check' : 'el-icon-plus'"
            @click="selectCopyExperts(item)"
          />
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
export default {
  name: 'SQSlither',
  props: {
    sqList: {
      type: Array,
      default() {
        return []
      },
    },
  },

  data() {
    return {
      target: this.sqList.slice(0, 2),
    }
  },

  computed: {
    showArrow() {
      return this.sqList.length > 1 ? 'always' : 'never'
    },
  },
  methods: {
    selectCopyExperts(item) {
      this.$emit('select', item)
    },

    // 初始化取前两条数据，用户点击箭头时 赋全值，减少内存占用
    setActiveItem() {
      this.target = this.sqList
    },
  },
}
</script>

<style scoped lang="scss">
.xs-tag{
  padding:0 2px;
}

.sq-box {
  overflow: auto;
  height: 100%;
  position: relative;

  .sq-checkbox {
    display: none;
    position: absolute;
    bottom: 0;
    right: 5px;

    ::v-deep .el-button--mini {
      padding: 6px;
    }
  }

  &:hover .sq-checkbox {
    display: block;
  }
}

.color-black {
  color: #333333;
}

.answer-bgc {
  margin-top: 3px;
  padding: 0 4px;
  border-radius: 2px;
  background-color: #f3f2f2;
}

::v-deep .el-carousel__container{
  height:150px;
}

::v-deep .el-carousel__item{
  padding-left: 16px;
  padding-right: 14px;
}

::v-deep .el-carousel__arrow--left{
  left: -14px;
}

::v-deep .el-carousel__arrow--right{
  right: -14px;
}

::v-deep .el-carousel__arrow{
  border:0;
  outline:0;
  background-color: unset;
  position: absolute;
  top:50%;
  color: unset;
  z-index: 10;
  font-size: 14px;
}
</style>
