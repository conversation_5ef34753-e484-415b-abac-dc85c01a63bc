<template>
<div>
  <el-switch
    v-if="HasEditPermission"
    v-model="ban_advisor"
    inactive-color="#13ce66"
    active-color="#ff4949"
    :active-text="ban_advisor ? 'Ban':'Active'"
    class="ml-3"
    @change="actionBanAdvisor" />
  <el-tooltip
        v-if="!HasEditPermission && ban_advisor"
        class="item ml-3"
        effect="dark"
        content="This expert is disabled and cannot be added to projects, to compliance or arrangements."
        placement="top">
    <el-button type="danger" class="el-button--xs">
      <svg-icon icon-class="warning" />
      Ban
    </el-button>
  </el-tooltip>
  <el-popover
    placement="bottom-start"
    width="700"
    :open-delay="300"
    trigger="hover">
    <el-table
      border
      stripe
      :data="statusLogList"
    >
      <el-table-column label="Timestamp" width="120">
        <template slot-scope="scope">
        {{ scope.row.create_at | momentFormat('MM/DD/YYYY HH:MM') }}
      </template>
      </el-table-column>
      <el-table-column label="Type" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.type | getLabel(type_option) }}</span>
      </template>
      </el-table-column>
      <el-table-column
        label="User"
        prop="user_name"
      />
      <el-table-column label="Project">
        <template slot-scope="scope">
          <router-link-project v-if="scope.row.project_id" :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column label="Project Type">
        <template slot-scope="scope">
          {{ scope.row.project && scope.row.project.type | getLabel(project_options.type) }}
        </template>
      </el-table-column>
      <el-table-column label="Status">
        <template slot-scope="scope">
         <span class="capitalize"> {{ scope.row.status.toLowerCase() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Note" prop="note" />
    </el-table>
    <el-icon slot="reference" class="info" name="tickets" />
  </el-popover>

  <el-dialog
    :show-close="false"
    :append-to-body="true"
    :visible.sync="statusDialog"
    title="Status change"
    width="660px">
    <el-form label-width="150px">
      <el-form-item v-if="showOption" label="Lead Status">
        <el-select v-model="target_status">
        <el-option
            v-for="item in advisor_options.status"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Note">
        <el-input v-model="note" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
          <el-button type="text" :disabled="save_loading" @click="resetData">Cancel</el-button>
          <el-button type="primary" :loading="save_loading" :disabled="!target_status || !note" @click="updateStatus(target_status)">Save</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import RouterLinkProject from '@/components/RouterLink/Project'
import { mapGetters } from 'vuex'

export default {
  name: 'ExpertStatus',
  components: { RouterLinkProject },
  props: {
    HasEditPermission: {
      type: Boolean,
      default: false,
    },
    advisorInfo: Object,
  },
  data() {
    return {
      save_loading: false,
      ban_advisor: true,
      statusDialog: false,
      target_status: null,
      showOption: false,
      note: '',
      type_option: [
        { label: 'Manual DB Update', value: 'MANUAL_DB_UPDATE' },
        { label: 'Unsubscribe Link', value: 'UNSUBSCRIBE_LINK' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
      'project_options',
      'uid',
    ]),

    statusLogList() {
      return this.advisorInfo?.status_change_logs
    },
  },
  mounted() {
    this.ban_advisor = this.advisorInfo?.status === 'BLACKLIST'
  },

  methods: {
    actionBanAdvisor() {
      this.statusDialog = true
      if (this.ban_advisor) {
        this.target_status = 'BLACKLIST'
      } else {
        this.advisorInfo.type === 'ADVISOR' ? this.target_status = 'ADVISOR' : this.showOption = true
      }
    },
    updateStatus(status) {
      this.save_loading = true
      const params = {
        status: status,
        note: this.note,
      }
      return ConsultantAPI.updateConsultantStatus(this.advisorInfo.id, params)
        .then(data => {
          this.advisorInfo.status = status
          this.advisorInfo.status_change_logs = data.status_change_logs
          this.$message({
            message: 'Save Successful!',
            type: 'success',
          })
          this.resetData()
        })
        .finally(() => {
          this.save_loading = false
        })
    },

    resetData() {
      this.statusDialog = false
      this.target_status = null
      this.note = ''
      this.showOption = false
      this.ban_advisor = this.advisorInfo.status === 'BLACKLIST'
    },
  },
}
</script>

<style scoped>

</style>
