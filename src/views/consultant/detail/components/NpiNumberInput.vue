<template>
  <el-input
    type="number"
    placeholder="10 digits"
    v-bind="$attrs"
    @input="validateAdvisorNPI"
    v-on="$listeners"
  >
    <template v-slot:suffix>
      <i v-if="loading" class="el-input__icon el-icon-loading" />
      <template v-else>
        <el-tooltip
          v-if="invalidTooltipVisible"
          effect="dark"
          content="Invalid"
          placement="top"
        >
          <i class="el-input__icon el-icon-error danger" />
        </el-tooltip>
        <el-tooltip
          v-if="npiStatus === 'VALID'"
          effect="dark"
          content="Valid"
          placement="top"
        >
          <i class="el-input__icon el-icon-success success" />
        </el-tooltip>
      </template>
    </template>
  </el-input>
</template>

<script>
import ConsultantAPI from '@/api/consultant'

export default {
  name: 'NpiNumberInput',
  props: {
    advisorId: Number,
    loading: Boolean,
    npiStatus: {
      type: String,
      default: 'INITIAL',
    },
  },
  computed: {
    invalidTooltipVisible() {
      return this.$attrs.value?.length === 10 && this.npiStatus === 'INITIAL'
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.validateAdvisorNPI(this.$attrs.value)
    })
  },
  methods: {
    validateAdvisorNPI(val) {
      this.$emit('update:npiStatus', 'INITIAL')
      if (val?.length !== 10) return

      this.$emit('update:loading', true)
      return ConsultantAPI.validateNpiNumber({
        npi_number: val,
        advisor_id: this.advisorId,
      }).then(result => {
        this.$emit('update:npiStatus', result === 'SUCCESS' ? 'VALID' : 'INITIAL')
      }).finally(() => {
        this.$emit('update:loading', false)
      })
    },
  },
}
</script>

<style lang="scss">
</style>
