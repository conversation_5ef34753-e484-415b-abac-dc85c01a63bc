<template>
  <div v-if="advisor && advisor.npi">
    <el-tooltip
      effect="light"
      :disabled="!advisorNpiRegistry"
      placement="right"
      :open-delay="300"
    >
      <div v-if="advisorNpiRegistry" slot="content" class="npi-content">
        <div class="npi-title">
          {{ advisorNpiRegistry.name_prefix }}
          {{ advisorNpiRegistry.first_name }}
          {{ advisorNpiRegistry.last_name }}
          {{ advisorNpiRegistry.credential }}
        </div>
        <el-descriptions :column="2" style="max-width: 450px;">
          <el-descriptions-item label="Gender">
            {{ advisorNpiRegistry.gender }}
          </el-descriptions-item>
          <el-descriptions-item label="NPI">
            {{ advisorNpiRegistry.npi_number }}
          </el-descriptions-item>
          <el-descriptions-item label="Last Updated">
            {{ advisorNpiRegistry.last_updated }}
          </el-descriptions-item>
          <el-descriptions-item label="Certification Date">
            {{ advisorNpiRegistry.certification_date }}
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions :column="1" size="mini" border class="mt-8">
          <el-descriptions-item label="NPI">
            {{ advisorNpiRegistry.npi_number }}
          </el-descriptions-item>
          <el-descriptions-item label="Enumeration Date">
            {{ advisorNpiRegistry.enumeration_date }}
          </el-descriptions-item>
          <el-descriptions-item label="NPI Type">
            {{ advisorNpiRegistry.npi_type }}
          </el-descriptions-item>
          <el-descriptions-item label="Sole Proprietor">
            {{ advisorNpiRegistry.sole_proprietor }}
          </el-descriptions-item>
          <el-descriptions-item label="Status">
            {{ advisorNpiRegistry.status }}
          </el-descriptions-item>
          <el-descriptions-item label="Mailing Address">
            <div>{{ advisorNpiRegistry.mailing_address_one }}</div>
            <div>{{ advisorNpiRegistry.mailing_address_two }}</div>
            <div>{{ advisorNpiRegistry.mailing_city }}, {{ advisorNpiRegistry.mailing_state }}
              {{ advisorNpiRegistry.mailing_postal_code }}
            </div>
            <div>{{ advisorNpiRegistry.mailing_country_name }}</div>
            <br>
            <div>Phone: {{ advisorNpiRegistry.mailing_telephone_number }} |
              Fax:{{ advisorNpiRegistry.mailing_fax_number }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="Primary Practice Address	">
            <div>{{ advisorNpiRegistry.primary_address_one }}</div>
            <div>{{ advisorNpiRegistry.primary_address_two }}</div>
            <div>{{ advisorNpiRegistry.primary_city }}, {{ advisorNpiRegistry.primary_state }}
              {{ advisorNpiRegistry.primary_postal_code }}
            </div>
            <div>{{ advisorNpiRegistry.primary_country_name }}</div>
            <br>
            <div>Phone: {{ advisorNpiRegistry.primary_telephone_number }} |
              Fax:{{ advisorNpiRegistry.primary_fax_number }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="Taxonomy">
            <el-descriptions direction="vertical" :column="5" border>
              <el-descriptions-item label="Primary Taxonomy">
                {{ advisorNpiRegistry.taxonomies_primary ? 'Yes' : 'NO' }}
              </el-descriptions-item>
              <el-descriptions-item label="Selected Taxonomy">
                {{ advisorNpiRegistry.taxonomies_code }} -
                {{ advisorNpiRegistry.taxonomies_desc }}
              </el-descriptions-item>
              <el-descriptions-item label="State" :span="2">
                {{ advisorNpiRegistry.taxonomies_state }}
              </el-descriptions-item>
              <el-descriptions-item label="License Number">
                {{ advisorNpiRegistry.taxonomies_license }}
              </el-descriptions-item>
            </el-descriptions>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <span :class="npiStatusFormat('npi_class')">
        {{ advisor.npi }}
      </span>
    </el-tooltip>
    <el-tooltip
      effect="dark"
      :content="npiStatusFormat('content')"
      placement="top"
      class="ml-8"
    >
      <span :class="npiStatusFormat('type')">
        <i :class="npiStatusFormat('icon')" />
      </span>
    </el-tooltip>
    <a v-if="advisorNpiRegistry" :href="npiLinkPrefix + advisor.npi" target="_blank" class="link ml-8">
      <i class="el-icon-link" />
    </a>
  </div>
</template>

<script>
export default {
  name: 'NpiNumber',
  props: {
    advisor: Object,
  },
  data() {
    return {
      npiLinkPrefix: 'https://npiregistry.cms.hhs.gov/provider-view/',
    }
  },
  computed: {
    advisorNpiRegistry() {
      return this.advisor?.advisor_npi_registry
    },
  },
  methods: {
    npiStatusFormat(key) {
      const obj = {
        'VALID': {
          npi_class: 'cursor-pointer success',
          content: 'Valid',
          type: 'success',
          icon: 'el-icon-success',
        },
        'INVALID': {
          npi_class: '',
          content: 'Invalid',
          type: 'danger',
          icon: 'el-icon-error',
        },
        'DUPLICATE': {
          npi_class: 'cursor-pointer warning',
          content: 'Duplicate NPI',
          type: 'warning',
          icon: 'el-icon-info',
        },
      }
      return obj[this.advisor?.npi_status][key]
    },
  },
}
</script>

<style lang="scss">
.npi-content {
  max-width: calc(100vw - 250px);
  max-height: calc(100vh - 100px);
  overflow: auto;

  .npi-title {
    font-weight: bold;
    margin-bottom: .5rem;
  }
}
</style>
