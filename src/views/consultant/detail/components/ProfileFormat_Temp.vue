<template>
  <div>
    <button type="button" class="consultant-profile-btn" @click="showProfile">Profile Format</button>

    <el-dialog
      title="Formatted Profile"
      :visible.sync="dialogVisible"
      width="800px">
      <div class="profile-body">
        <div style="font-size: 11pt; font-family: Calibri;">
          <div>
            <b style="font-size: 14pt;">{{ data.name_prefix || ''}} {{ data.firstname }} {{ data.lastname }}</b>
            –
            <i>{{ data.location.name }}</i>
          </div>
          <div>
            <b>Employment History</b>
            <table cellspacing="0" cellpadding="0" style="font-size: 11pt; font-family: Calibri;">
              <tr v-for="job in data.jobs" :key="job.id">
                <td style="padding: 0 8px 0 24px;" align="center" valign="top">&bull;</td>
                <td align="left" valign="top">
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    job.is_current
                      ? 'Current'
                      : displayTime(job.end_date)
                  }}
                </td>
              </tr>
            </table>
          </div>
          <br>
          <div>
            <b>Background</b>
            <p v-if="data.background" style="margin: 0">
              {{ data.background }}
            </p>
            <p v-else style="margin: 0">
              {{ data.name_prefix || ''}} {{ data.firstname }} {{ data.lastname }} has ___ years of experience in ___________.
              <slot v-for="job in formatted_jobs">
                <slot v-if="job.length === 1">
                  <slot v-if="job[0].is_current">
                    Since {{ displayTime(job[0].start_date) }}, this advisor is
                  </slot>
                  <slot v-else>
                    From {{ displayTime(job[0].start_date) }} to {{ displayTime(job[0].end_date) }}, this advisor was
                  </slot>
                  the {{ job[0].position }} at {{ job[0].company.name }}.
                </slot>
                <slot v-else>
                  <slot v-if="job[0].is_current">
                    Since {{ displayTime(job[job.length - 1].start_date) }},
                  </slot>
                  <slot v-else>
                    From {{ displayTime(job[job.length - 1].start_date) }} to {{ displayTime(job[0].end_date) }},
                  </slot>
                  the advisor held numerous positions at {{ job[0].company.name }} --
                  <slot v-for="(item,index) in job">{{ item.position }} ({{ displayTime(item.start_date) }} -
                    {{ displayTime(item.end_date) }})
                    <slot v-if="index !== job.length - 1">;</slot>
                  </slot>
                  .
                </slot>
              </slot>
            </p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="copyProfile">Copy</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment-timezone'

export default {
  name: 'ProfileFormat',
  props: {
    data: Object,
  },
  data() {
    return {
      dialogVisible: false,
      formatted_jobs: [],
    }
  },
  methods: {
    showProfile() {
      if (this.data.jobs && this.data.jobs.length) {
        const companies = []
        const formatted_jobs = []
        this.data.jobs.forEach(item => {
          const index = companies.indexOf(item.company.name)
          if (index === -1) {
            companies.push(item.company.name)
            formatted_jobs.push([item])
          } else {
            formatted_jobs[index].push(item)
          }
        })
        this.formatted_jobs = formatted_jobs
      }

      this.dialogVisible = true
    },
    /**
     * 日期格式化为 "Aug 2019"
     * @param input
     * @returns {string|*}
     */
    displayTime(input) {
      if (!input) {
        return
      }

      if (['current'].includes(String(input).toLowerCase())) {
        return input
      }

      return moment(input).format('MMM YYYY')
    },
    copyProfile() {
      const temp = document.createElement('div')
      temp.innerHTML = document.getElementsByClassName('profile-body')[0].innerHTML
      document.body.appendChild(temp)

      const selection = window.getSelection()
      selection.removeAllRanges()

      const range = document.createRange()
      range.selectNode(temp)
      selection.addRange(range)

      if (document.execCommand('Copy')) {
        document.execCommand('Copy')
        this.$message({
          type: 'success',
          message: 'Copy successfully!',
        })
      }

      document.body.removeChild(temp)

      this.dialogVisible = false
    },
  },
}
</script>

<style scoped>
.consultant-profile-btn {
  color: #fff;
  font-size: .8rem;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(90deg, rgba(17, 198, 227, 1) 0%, rgba(17, 198, 227, 1) 0%, rgba(0, 227, 190, 1) 100%, rgba(0, 227, 190, 1) 100%);
  border: none;
  border-radius: 36px;
  transition: all .3s;
  outline: none;
  cursor: pointer;
}

.consultant-profile-btn:hover {
  filter: brightness(1.1);
}

.profile-body {
  line-height: 1.5;
}
</style>
