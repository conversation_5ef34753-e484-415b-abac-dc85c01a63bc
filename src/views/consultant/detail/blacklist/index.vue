<template>
  <div>
    <div class="mb-8">
      <client-search
        v-model="form.client_ids"
        multiple
        class="w-300px"
      />
      <el-button
        type="primary"
        :disabled="!form.client_ids.length"
        :loading="submitting"
        @click="addToBlackList"
      >
        Add To Black List
      </el-button>
      <el-switch
        v-model="is_blocked"
        active-text="Add to Black List of All Clients"
        active-color="#F56C6C"
        class="ml-3"
        @change="actionChangeIsBlocked"
      />
    </div>
    <el-table
      v-show="!is_blocked"
      v-loading="loading"
      border
      stripe
      :data="list">
      <el-table-column label="Client">
        <template slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column label="Date" width="100">
        <template slot-scope="scope">
          {{ scope.row.update_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="80">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Remove from Black List？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="removeClientBlackList(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getAdvisorInBlackList"
    />
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import RouterLinkClient from '@/components/RouterLink/Client'
import Pagination from '@/components/Pagination'

export default {
  name: 'AdvisorBlackList',
  components: { ClientSearch, RouterLinkClient, Pagination },
  props: {
    data: Object,
  },
  data() {
    return {
      loading: false,
      submitting: false,
      form: {
        client_ids: [],
      },
      is_blocked: false,
      searchQuery: {
        page: 1,
        size: 10,
      },
      total: 0,
      list: [],
    }
  },
  mounted() {
    this.checkIsBlocked()
    this.getAdvisorInBlackList()
  },

  methods: {
    checkIsBlocked() {
      return ConsultantAPI.checkAdvisorIsBlocked(this.data.id, 'ADVISOR')
        .then(data => {
          this.is_blocked = data.is_blocked
        })
    },
    getAdvisorInBlackList() {
      this.loading = true
      const params = {
        target_id: this.data.id,
        type: 'ADVISOR',
        ...this.searchQuery,
        client_id_not_in: '0',
        extra: 'client',
      }
      return ConsultantAPI.getAdvisorBlackList(params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    addToBlackList() {
      this.submitting = true
      return ConsultantAPI.addToClientBlackList(this.data.id, 'ADVISOR', this.form)
        .then(() => {
          this.form.client_ids = []
          this.searchQuery.page = 1
          this.getAdvisorInBlackList()
        })
        .finally(() => {
          this.submitting = false
        })
    },
    removeClientBlackList(item) {
      this.loading = true
      const params = {
        client_ids: [item.client_id],
      }
      return ConsultantAPI.removeFromClientBlackList(this.data.id, 'ADVISOR', params)
        .then(() => {
          this.getAdvisorInBlackList()
        })
    },
    actionChangeIsBlocked(val) {
      const requestName = val ? 'blockAdvisorInAllClient' : 'unblockAdvisorInAllClient'
      return ConsultantAPI[requestName](this.data.id, 'ADVISOR')
        .then(this.getAdvisorInBlackList)
    },
  },
}
</script>

<style scoped>

</style>
