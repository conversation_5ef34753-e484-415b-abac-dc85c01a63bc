<template>
  <div>
    <el-table
      stripe
      class="headerFixed"
      :data="list">
      <el-table-column
        label="Project"
      >
        <template slot-scope="scope">
          <router-link-project v-if="scope.row.project_id" :data="scope.row.project" />
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
        prop="email_content_type"
      >
        <template slot-scope="scope">
          {{ getCommunicationType(scope.row.advisor_portal) }}
        </template>
      </el-table-column>
      <el-table-column label="Subject">
        <template slot-scope="scope">
          {{ scope.row.email_record.subject }}
        </template>
      </el-table-column>
      <el-table-column label="Content">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="light" placement="left" :open-delay="200">
            <div slot="content" class="message-content">
              <span v-html="scope.row.email_record.content" />
            </div>
            <el-button>message</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="Date"
      >
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="page"
      :limit.sync="size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'

export default {
  name: 'CommunicationIndex',
  components: { RouterLinkProject, Pagination },
  data() {
    return {
      list: [],
      advisor_id: this.$route.params.advisor_id,
      page: 1,
      size: 20,
      total: 0,
      step_options: [
        { name: 'Terms & Conditions', value: 'TC' },
        { name: 'Pre Call Client Agreement', value: 'CA' },
        { name: 'Screening Questions', value: 'SQ' },
        { name: 'Availability', value: 'AG' },
        { name: 'Payment Confirm', value: 'PAYMENT_CONFIRM' },
        { name: 'Post Call Client Agreement', value: 'POST_CA' },
        { name: 'Feedback', value: 'FEEDBACK' },
      ],
    }
  },
  mounted() {
    this.getList()
  },

  methods: {
    getList() {
      const params = {
        page: this.page,
        size: this.size,
        extra: 'email_record,email_record.from_name,project,advisor_portal',
      }
      return ConsultantAPI.consultantCommunications(this.advisor_id, params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
    },

    getCommunicationType(data) {
      if (!data) return 'N/A'
      const result = data.expect_steps.map(item => {
        const value = this.step_options.find(step => step.value === item)
        if (value) return value.name
      })
      return result.join(', ')
    },
  },
}
</script>

<style scoped>
.message-content {
  width: 500px;
  pointer-events: none;
}
</style>
