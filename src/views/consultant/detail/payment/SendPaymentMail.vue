<template>
  <div>
    <el-button
      class="inline-block"
      type="success"
      @click="dialogVisible = true">
      <svg-icon icon-class="email" />
      Send Payment Confirm
    </el-button>

    <el-dialog
      title="Send Payment Confirm"
      :visible.sync="dialogVisible"
      width="1000px"
      @open="openDialog">
      <el-form v-loading="loading" label-width="100px" class="form-data">
        <tinymce v-model="email.subject" inline inline-header />
        <tinymce ref="tinymce_send_payment" v-model="email.content" inline is-email />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="send_loading" @click="actionSend">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email'
import Tinymce from '@/components/Tinymce'
import ConsultantAPI from '@/api/consultant'

export default {
  name: 'SendPaymentMail',
  components: { Tinymce },
  props: {
    advisor: Object,
    paymentDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      advisorId: this.advisor.id,
      dialogVisible: false,
      loading: false,
      send_loading: false,
      email_template: undefined,
      email: {
        subject: '',
        content: '',
      },
    }
  },
  computed: {
    existNoEmail() {
      return !(this.advisor.contact_infos.findIndex(item => item.type === 'EMAIL') > -1)
    },
  },
  methods: {
    openDialog() {
      this.loading = true
      return this.getEmailTemplateList().then(() => {
        this.getEmailContent()
      })
        .finally(() => {
          this.loading = false
        })
    },

    async getEmailTemplateList() {
      const params = {
        has_permission: true,
        content_type: 'PORTAL_ADVISOR_BANK_ACCOUNT',
      }
      return EmailAPI.getEmailList(params).then(data => {
        this.email_template = data.list[0]
      })
    },

    async getEmailContent() {
      const params = {
        advisor_id: this.advisorId,
      }
      this.loading = true
      this.email = await EmailAPI.getEmailByTemplate(this.email_template.id, params)
      if (this.$refs['tinymce_send_payment']) {
        this.$refs['tinymce_send_payment'].setContent(this.email.content)
      }
      this.loading = false
    },
    actionSend() {
      if (this.existNoEmail) {
        return this.$alert('This consultant has no email', 'Notice', {
          confirmButtonText: 'OK',
          type: 'error',
        })
      }
      this.$confirm('Confirm to send emails?', 'Notice', {
        type: 'warning',
      })
        .then(() => {
          const params = {
            portal: {
              type: 'ADVISOR_BANK_ACCOUNT',
              advisor_id: this.advisorId,
              payload: {
                id: this.advisorId,
                role: 'ADVISOR',
                advisor_payment_form_id: this.paymentDetail.id || null,
                project_sub_type: this.paymentDetail.project_sub_type || 'Consultation',
              },
            },
            email: this.email,
            email_template_tags: this.email_template.tags,
          }

          this.send_loading = true
          return ConsultantAPI.sendPaymentConfirmToConsultant(params)
            .then(() => {
              this.$message({
                type: 'success',
                message: 'Success to send emails!',
              })
              this.$emit('update')
              this.dialogVisible = false
            })
            .finally(() => {
              this.send_loading = false
            })
        })
        .catch(() => {
        })
    },
  },

}
</script>

<style scoped>

</style>
