<template>
  <div v-if="data">
    <div v-if="!payment_forms.length">
      <SendPaymentMail
        :advisor="data" />
    </div>
    <div v-if="payment_forms.length" class="flex">
      <div v-for="item in payment_forms" :key="item.id" class="payment-item">
        <div class="mb-3">
          {{ item.project_sub_type }}
          <el-tag v-if="item.pay_method_name === 'VIRTUAL_INCENTIVES'" class="ml-8">Gift Card</el-tag>
        </div>
        <div>
          <el-button
            v-if="hasPaymentPermission && item.data"
            type="primary"
            :disabled="!item.is_hide"
            plain
            @click="dataView(item)">View
          </el-button>
          <payment-info
            v-if="item.pay_method_name !== 'VIRTUAL_INCENTIVES'"
            class="inline-block"
            :payment-info="item"
            :payment-type="item.pay_method_name"
            @update="getPaymentInfo" />
          <SendPaymentMail
            class="inline-block"
            :payment-detail="item"
            :advisor="data" />
        </div>

        <el-form
          v-loading="item.loading"
          class="form-data payment-box"
          label-position="left"
          label-width="150px">
          <el-form-item
            v-for="(value, key) in item.data"
            :key="key"
            :label="key | transformKey">
            <template v-if="!['id','advisor_id'].includes(key)">
              <span v-if="['create_at','update_at'].includes(key)">{{ value | momentFormat() }}</span>
              <span v-else>{{ value || '-' }}</span>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>

  </div>
</template>

<script>
import _ from 'lodash'
import PaymentInfo from '@/views/project/consultation/detail/task-complete/components/PaymentInfo'
import ConsultantAPI from '@/api/consultant'
import SendPaymentMail from './SendPaymentMail'
import { mapGetters } from 'vuex'

export default {
  name: 'ConsultantPayment',
  components: { PaymentInfo, SendPaymentMail },
  props: {
    data: Object,
  },
  data() {
    return {
      payment_forms: [],
      need_hide: [
        'account_number',
        'cmnd_code',
        'cpf_code',
        'ifsc_code',
        'sort_code',
        'swift_code',
        'tax_id_code',
        'transit_number',
        'email_address',
        'address1',
        'address2',
        'city',
        'region'],
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    hasPaymentPermission() {
      return this.roles.some(item => {
        return ['Admin', 'Finance'].includes(item.role.name)
      })
    },
  },
  mounted() {
    this.getPaymentInfo()
  },
  methods: {
    getPaymentInfo() {
      const extra = ['bank_account.account2_mosaic_jit', 'virtual_incentives_payment_info']
      return ConsultantAPI.getConsultantPaymentInfo(this.data.id, extra.join())
        .then((data) => {
          this.payment_forms = data.map(item => {
            item.data = this.formatData(item)
            item.is_hide = true
            item.loading = false
            return item
          })
        })
    },
    formatData(data) {
      const result = {}
      if (data) {
        let target = {}
        if (data.pay_method_name === 'DIRECT_DEPOSIT') {
          target = data.bank_account?.account2_mosaic_jit || data.bank_account?.account2_no_mosaic_jit || {}
        } else if (data.pay_method_name === 'VIRTUAL_INCENTIVES') {
          target = data.virtual_incentives_payment_info
        } else {
          target = data.payment_detail
        }

        _.forEach(target, (value, key) => {
          if (!['id', 'advisor_id'].includes(key)) {
            result[key] = value
          }
          if (key === 'bank_area' && ['Macau', 'Hong Kong'].includes(value)) {
            result[key] = value + ' (China)'
          }
        })
      }

      return result
    },

    dataView(item) {
      const params = {
        'owner_id': this.data.id,
        'owner_type': 'ADVISOR',
        'user_id': this.uid,
        'type': 'PAYMENT_FORM',

      }
      item.is_hide = false
      item.loading = true
      return ConsultantAPI.privacyView(params)
        .then(data => {
          item.data = this.formatData(data.privacy_info)
        })
        .finally(() => {
          item.loading = false
        })
    },
  },
}
</script>

<style scoped>
.payment-item{
  width:50%;
  padding: 10px;
  border:1px solid #c9c5c5;
  margin: 10px 5px;
}

.payment-box::v-deep .el-form-item {
  margin-bottom: 3px;
}

.payment-box {
  margin-top: 20px;
  padding: 10px;
}
</style>
