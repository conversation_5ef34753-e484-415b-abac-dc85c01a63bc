<template>
  <div>
    <el-table
      stripe
      class="headerFixed"
      :data="list"
      @sort-change="sortData">
      <el-table-column
        label="Date Added"
        width="150"
        sortable="custom"
        prop="screening_summary_update_time"
      >
        <template slot-scope="scope">
          {{ scope.row.screening_summary_update_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column
        label="Project"
        width="200"
      >
        <template slot-scope="scope">
          <router-link-project v-if="scope.row.project_id" :data="scope.row.project" />
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Summary"
      >
        <template slot-scope="scope">
          {{ scope.row.screening_summary }}
        </template>
      </el-table-column>
      <el-table-column
        label="Actions"
        width="150"
      >
        <template slot-scope="scope">
          <el-button type="primary" class="el-button--xs" plain @click="copySummary(scope.row.screening_summary)">Copy Summary</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="page"
      :limit.sync="size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import ProjectAPI from '@/api/project'
import { copyUtil } from '@/utils/tool'
export default {
  name: 'SummaryList',
  components: { RouterLinkProject, Pagination },
  props: {
    sqSummaryCount: [Number, String],
  },
  data() {
    return {
      list: [],
      advisor_id: this.$route.params.advisor_id,
      sort: 'screening_summary_update_time desc',
      page: 1,
      size: 20,
      total: 0,
    }
  },
  mounted() {
    this.getList()
      .then(() => {
        this.$emit('update:sqSummaryCount', this.total)
      })
  },

  methods: {
    getList() {
      const params = {
        page: this.page,
        size: this.size,
        advisor_id: this.advisor_id,
        screening_summary_is_blank: false,
        sort: this.sort,
        extra: 'project',
      }
      return ProjectAPI.getConsultationList(params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
    },
    sortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.sort = val.prop + order
        this.getList()
      }
    },

    copySummary(val) {
      return copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
          this.subject_copied = true
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
  },
}
</script>

<style scoped>

</style>
