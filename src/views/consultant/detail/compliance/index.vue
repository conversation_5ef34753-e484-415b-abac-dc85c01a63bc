<template>
  <div>
    <div class="filter-container">
      <send-tc
        class="filter-item"
        :advisor-id="data.id"
        :rate="advisor_rate"
        :currency="rate_currency"
        :advisor="data"
        @update="updateList()"
      />
    </div>
    <el-table
      border
      stripe
      :data="tableData">
      <el-table-column label="Rate" prop="rate">
        <template slot-scope="scope">
          <span>{{ scope.row.rate }} {{ scope.row.rate_currency }}</span>
          <el-tag v-if="current_tc_id === scope.row.id" type="success" class="ml-8">Current</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Type">
        <template slot-scope="scope">
          {{ scope.row.tc_type | getLabel(tc_type_options) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Name"
      >
        <template slot-scope="scope">
          <a
            class="text-truncate link"
            :title="scope.row.content.title"
            @click="actionGotoTCDetail(scope.row)">{{ scope.row.content.title }}</a>
        </template>
      </el-table-column>
      <el-table-column label="Status">
        <template slot-scope="scope">
          <span v-if="scope.row.approved" class="success">Signed</span>
          <span v-else class="info">Sent</span>
        </template>
      </el-table-column>
      <el-table-column label="Sign Date">
        <template slot-scope="scope">
          {{ scope.row.respond_time| momentFormat('MM/DD/YYYY hh:mm A') }}
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="TCDialogVisible"
      :close-on-click-modal="false"
      width="70%"
      center
    >
      <slot slot="title">
        <div v-if="allowDownloadTC" class="text-left mb-3">
          <el-button class="el-button--xs" type="primary" @click="printPDF">Print PDF</el-button>
        </div>
        {{ selected_tc.title }}
      </slot>
      <div id="tc-content" class="tc-body scrollbar-narrow" v-html="selected_tc.content" />

    </el-dialog>
  </div>
</template>

<script>
import SendTc from './components/SendTC'
import _ from 'lodash'
import { mapGetters } from 'vuex'

export default {
  name: 'ConsultantComplianceIndex',
  components: { SendTc },
  props: {
    data: Object,
  },
  data() {
    return {
      TCDialogVisible: false,
      selected_tc: {},
      tableData: [],
      current_tc_id: null,
      advisor_rate: null,
      rate_currency: 'USD',
      tc_type_options: [
        { value: 'DEFAULT', label: 'Default' },
        { value: 'SURVEY', label: 'Survey' },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'roles',
    ]),
    allowDownloadTC () {
      const permission = this.roles.some(item => {
        return ['Admin', 'Advisor_TC_Download'].includes(item.role.name)
      })
      return permission && this.selected_tc.approved
    },
  },
  mounted() {
    this.getTcList()
  },
  methods: {
    getTcList() {
      this.tableData = _.orderBy(this.data['tc_list'], 'respond_time', 'desc')
      this.current_tc_id = this.data['latest_signed_tc'] ? this.data['latest_signed_tc'].id : 0
      this.advisor_rate = this.data.rate
      this.rate_currency = this.data.rate_currency
    },
    actionGotoTCDetail(item) {
      this.TCDialogVisible = true
      this.selected_tc = Object.assign({ approved: item.approved }, item.content)
    },

    updateList() {
      this.$emit('update')
    },

    printPDF() {
      const content = document.getElementById('tc-content').innerHTML
      const iframe = document.createElement('iframe')

      iframe.style.position = 'absolute'
      iframe.style.width = '0px'
      iframe.style.height = '0px'
      iframe.style.border = 'none'

      document.body.appendChild(iframe)
      const doc = iframe.contentWindow.document

      doc.open()
      doc.write('<html><head><title>Print</title></head><body>')
      doc.write(content)
      doc.write('</body></html>')
      doc.close()

      iframe.contentWindow.focus()
      iframe.contentWindow.print()

      setTimeout(() => document.body.removeChild(iframe), 1000) // 打印完成后移除 iframe
    },
  },
}
</script>

<style scoped>
.tc-body {
  height: 500px;
  overflow: auto;
}
</style>
