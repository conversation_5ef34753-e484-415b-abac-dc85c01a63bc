<template>
  <div>
    <el-tooltip
      :disabled="!isSendTCDisabled"
      :content="messageTcDisabled"
      :enterable="false"
      placement="top">
      <div class="inline-block">
        <el-button
          type="success"
          :disabled="isSendTCDisabled"
          @click="dialogVisible = true">
          <svg-icon icon-class="email" />
          Send TC
        </el-button>
      </div>
    </el-tooltip>

    <el-dialog
      title="Send TC"
      :visible.sync="dialogVisible"
      width="1000px"
      @open="openDialog">
      <el-form ref="form" v-loading="loading" label-width="100px" :model="form" :rules="rules" class="form-data">
        <el-form-item label="TC">
          <tc-search
            v-model="form.tc"
            :init-request="false"
            :init-list="options.initTcList"
            class="w-100"
            @change="handleTcChange" />
        </el-form-item>
        <el-form-item label="Locale">
          <el-select
            v-model="form.tc_template"
            value-key="id"
            class="w-100">
            <slot v-if="form.tc">
              <el-option
                v-for="item in form.tc.templates"
                :key="item.id"
                :label="item.locale"
                :value="item" />
            </slot>
          </el-select>
        </el-form-item>
        <transition name="fade">
          <el-form-item v-if="displayAttachment"
                        label="Attachment">
            <el-link type="primary"
                     :href="form.tc_template.file_url">
              {{ form.tc_template.file_name }}
            </el-link>
          </el-form-item>
        </transition>
        <el-form-item label="Rate" prop="rate">
          <el-input
            v-model="form.rate"
            type="number"
            :step="150"
            :min="0"
            class="w-100" />
        </el-form-item>
        <el-form-item label="Currency" prop="rate_currency">
          <el-select
            v-model="form.rate_currency"
            class="w-100">
            <el-option
              v-for="item in options.currency"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Email Template">
          <el-select v-model="form.email_template" value-key="id" class="w-100" @change="getEmailContent">
            <el-option
              v-for="template in options.emailTemplates"
              :key="template.id"
              :label="template.display_name"
              :value="template" />
          </el-select>
        </el-form-item>
        <el-input v-model="email.subject" class="email-subject-input" />
        <tinymce ref="tinymce_send_tc" v-model="email.content" inline is-email />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary"
                   :loading="send_loading"
                   @click="actionSend">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TcAPI from '@/api/tc'
import ConsultantAPI from '@/api/consultant'
import EmailAPI from '@/api/email'
import TcSearch from '@/components/SelectRemoteSearch/TcSearch'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'SendTC',
  components: { TcSearch, Tinymce },
  props: {
    advisorId: [String, Number],
    rate: [String, Number],
    currency: String,
    advisor: Object,
  },
  data() {
    const rateValidator = (rule, value, callback) => {
      const reg = /^\d+(?=\.{0,1}\d+$|$)/
      if (reg.test(value)) {
        callback()
      } else {
        callback(new Error('The rate must be a number greater than or equal to 0'))
      }
    }
    return {
      dialogVisible: false,
      loading: false,
      send_loading: false,
      options: {
        initTcList: [],
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        emailTemplates: [],
      },
      form: {},
      rules: {
        rate: [{ required: true, validator: rateValidator, trigger: 'change' }],
        rate_currency: [{ required: true, trigger: 'change' }],
      },
      email: {
        subject: '',
        content: '',
      },
    }
  },
  computed: {
    /**
     * CANNOT SEND TC UNLESS "Current" employer added
     * @returns {boolean}
     */
    isSendTCDisabled() {
      return !this.advisor || this.existNoCurrentJod
    },

    messageTcDisabled() {
      return this.existNoCurrentJod ? 'Cannot send TC unless "Current" experience added' : ''
    },
    existNoCurrentJod() {
      return !this.advisor.jobs.find(item => {
        return item.is_current
      })
    },

    displayAttachment() {
      return this.form.tc_template && this.form.tc_template.file_url
    },

    existNoEmail() {
      return !(this.advisor.contact_infos.findIndex(item => item.type === 'EMAIL') > -1)
    },
  },
  methods: {
    openDialog() {
      this.loading = true
      this.init()
      return Promise
        .all([this.getInitTcList(), this.getEmailTemplateList()])
        .then(() => {
          this.getEmailContent()
        })
        .finally(() => {
          this.loading = false
        })
    },
    init() {
      this.form = {
        tc: null,
        tc_template: null,
        rate: this.rate,
        rate_currency: this.currency,
        email_template: null,
      }
    },
    getInitTcList() {
      const params = {
        is_current: true,
        type: 'DEFAULT',
      }
      return TcAPI.getTCList(params)
        .then(data => {
          this.options.initTcList = data
          if (data.length) {
            this.form.tc = this.options.initTcList.find(tc => tc.is_current)
            this.form.tc_template = this.form.tc.templates[0]
          }
        })
    },
    handleTcChange(val) {
      this.form.tc_template = val.templates[0]
    },
    async getEmailTemplateList() {
      const params = {
        has_permission: true,
        content_type: 'PORTAL_ADVISOR_PRE_CALL',
      }
      const data = await EmailAPI.getEmailList(params)
      this.options.emailTemplates = data.list.filter(
        item => item.tags.includes('TC') && item.tags.includes('TC_PROJECT'))
      this.form.email_template = this.options.emailTemplates[0]
    },
    async getEmailContent() {
      const params = {
        advisor_id: this.advisorId,
      }
      this.loading = true
      this.email = await EmailAPI.getEmailByTemplate(this.form.email_template.id, params)
      if (this.$refs['tinymce_send_tc']) {
        this.$refs['tinymce_send_tc'].setContent(this.email.content)
      }
      this.loading = false
    },
    actionSend() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.existNoEmail) {
            return this.$alert('This consultant has no email', 'Notice', {
              confirmButtonText: 'OK',
              type: 'error',
            })
          }
          this.$confirm('Confirm to send emails?', 'Notice', {
            type: 'warning',
          })
            .then(() => {
              const params = {
                portal: {
                  type: 'ADVISOR_PRE_CALL',
                  advisor_id: this.advisorId,
                  advisor_tc: {
                    tc_id: this.form.tc.id,
                    tc_template_id: this.form.tc_template.id,
                    rate: this.form.rate,
                    locale: this.form.tc_template.locale,
                    rate_currency: this.form.rate_currency,
                  },
                },
                email: this.email,
                email_template_tags: this.form.email_template.tags,
              }

              /* 添加邮件附件 (TC).pdf */
              if (this.form.tc_template.file_url) {
                params.email.attachments = [
                  {
                    name: this.form.tc_template.file_name,
                    file_path: this.form.tc_template.file_url,
                  },
                ]
              }
              this.send_loading = true
              return ConsultantAPI.sendConsultantPortal(params)
                .then(() => {
                  this.$message({
                    type: 'success',
                    message: 'Success to send emails!',
                  })
                  this.$emit('update')
                  this.dialogVisible = false
                })
                .finally(() => {
                  this.send_loading = false
                })
            })
            .catch(() => {
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
/* 邮件编辑 主题 */
.email-subject-input {
  margin-bottom: 5px;
  margin-top: 5px;

  ::v-deep .el-input__inner {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }
}
</style>
