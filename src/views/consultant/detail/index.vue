<template>
  <div v-loading="loading" class="components-container">
    <div v-if="advisor && !DeletedExpert">
      <div class="flex justify-content-between">
        <div class="flex align-items-center mb-8">
          <div>
            <h2 class="consultant-header"># <span class="id-font">{{ advisor.id }}</span>
              {{ advisor.name_prefix || '' }} {{ advisor.firstname }} {{ advisor.lastname }}</h2>
            {{ getPosition() }}
          </div>

          <div class="flex flex-column ml-3">
            <div class="flex align-items-center">
              <el-tooltip
                v-if="showSurveyTag"
                class="item mr-8"
                effect="dark"
                content="This expert was recruited for a survey and has not received the training PowerPoint or had their employment verified over the phone."
                placement="top-start">
                <el-tag type="danger" size="small">Survey Recruit</el-tag>
              </el-tooltip>
              <div v-if="advisor.latest_signed_tc" class="tc-current">
                TC:{{ advisor.latest_signed_tc.is_current_tc_jit ? 'Current' : 'Old' }}
              </div>
              <div class="ml-8">
                <el-tag v-if="advisor.tc_expired" type="warning">Advisor - TC Update</el-tag>
                <el-tag v-if="advisor.type === 'ADVISOR' && !advisor.tc_expired">Advisor</el-tag>
                <el-tag v-if="advisor.type === 'LEAD'" type="info">Lead</el-tag>
              </div>
            </div>
            <top-advisor-tags
              v-if="advisor.user_advisor_tag_maps && !isGKMRole"
              :advisor="advisor"
              :tags-editing.sync="tags_editing"
              :append-to-body="true"
              style="margin-top: 2px;"
              @after-edit-tags="afterEditTags"
            />
          </div>
          <expert-status v-if="advisor.id" :advisor-info.sync="advisor" :has-edit-permission="HasEditPermission" class="ban-container" />
          <add-to-cart
            v-if="advisor.id && !isGKMRole"
            :advisor-id="advisor.id"
            class="ml-3"
          />

          <span v-if="advisor.feedback_rating_jit" class="ml-3">Feedback: {{ advisor.feedback_rating_jit }} / 5</span>
        </div>
        <div>
          <el-popconfirm
            v-if="HasDeletePermission"
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Caution: If you delete this expert’s information it can not be retrieved.This includes all project, payment and profile information. Are you sure you want to delete this expert?"
            cancel-button-text="Cancel"
            confirm-button-text="Yes, Delete Expert"
            confirm-button-type="danger"
            @confirm="deleteAdvisor()"
          >
            <el-button slot="reference" type="danger" class="ml-8">Delete Expert</el-button>
          </el-popconfirm>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="Profile" name="profile">
          <profile v-if="advisor.id" :is-g-k-m-role="isGKMRole" :data.sync="advisor" @update="updateDetail()" />
        </el-tab-pane>
        <template v-if="!isGKMRole">
          <el-tab-pane label="Communication" name="communication">
            <communication />
          </el-tab-pane>
          <el-tab-pane label="Payment" name="payment">
            <consultant-payment v-if="advisor.id" :data="advisor" />
          </el-tab-pane>
          <el-tab-pane label="Compliance" name="compliance">
            <consultant-compliance v-if="advisor.id" :data="advisor" @update="updateDetail()" />
          </el-tab-pane>
          <el-tab-pane :label="sqSummaryTabTitle" name="summary">
            <summary-list v-if="advisor.id" :sq-summary-count.sync="sq_summary_count" />
          </el-tab-pane>
          <el-tab-pane label="Black List" name="blacklist">
            <black-list v-if="advisor.id" :data="advisor" />
          </el-tab-pane>
        </template>
      </el-tabs>
    </div>

    <div v-if="advisor && DeletedExpert">
      <div class="danger mt-8 mb-normal"> Expert Deleted by {{ advisor.gdpr_delete_by.name }} at
        {{ advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A') }}.
      </div>
      <div v-for="(item,index) in advisor.contact_infos" :key="index" class="mt-8 info">
        <span class="capitalize w-120px"> {{ item.type.toLowerCase() }}:</span>
        {{ item.value }}
      </div>
    </div>

    <el-alert v-if="!advisor" type="error" :closable="false" show-icon>
      This expert was not found.
    </el-alert>

    <router-view />

  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import FormMixin from '../mixins/form'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import { RouteTools } from '@/utils/tool'
import ConsultantPayment from '@/views/consultant/detail/payment/ConsultantPayment'
import ConsultantCompliance from '@/views/consultant/detail/compliance/index'
import Communication from '@/views/consultant/detail/communication/index'
import SummaryList from '@/views/consultant/detail/taskSQSummary/SummaryList'
import BlackList from '@/views/consultant/detail/blacklist/index'
import Profile from './profile/index'
import AddToCart from '@/components/ShoppingCart/AddToCart'
import TopAdvisorTags from '@/components/ShoppingCart/components/TopAdvisors/components/TopAdvisorTags'
import CartAPI from '@/api/cart'
import ExpertStatus from '@/views/consultant/detail/components/ExpertStatus'

export default {
  name: 'ConsultantDetail',
  components: {
    ConsultantPayment, ConsultantCompliance, Profile, Communication, SummaryList, BlackList,
    AddToCart, TopAdvisorTags, ExpertStatus,
  },
  mixins: [FormMixin],
  beforeRouteLeave(to, from, next) {
    this.$route.query.tab = this.activeName
    next()
  },
  data() {
    return {
      id: this.$route.params.advisor_id,
      advisor: {},
      loading: false,
      activeName: this.$route.query.tab || 'profile',
      sq_summary_count: 0,
      tags_editing: false,
      extra: [
        'contact_infos.email_validation_records',
        'location',
        'rate',
        'jobs',
        'tc_list',
        'tc_list.content',
        'latest_signed_tc',
        'jobs.company',
        'feedback_rating_jit',
        'contact_count_jit',
        'completed_consultation_count',
        'sourced_by',
        'latest_signed_tc.is_current_tc_jit',
        'update_by',
        'gdpr_delete_by.delete_at',
        'linkedin_industry_code',
        'user_advisor_tag_maps.tag',
        'advisor_npi_registry',
        'status_change_logs.project',
      ].join(),
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),

    sqSummaryTabTitle() {
      const summary_count = this.sq_summary_count > 0 ? ` (${this.sq_summary_count})` : ''
      return 'SQ Summary' + summary_count
    },

    DeletedExpert() {
      return this.advisor.gdpr_delete_by
    },
    HasEditPermission() {
      return this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
    isGKMRole() {
      return this.roles.find(item => item.role.name === 'GKM') && this.roles.length === 1
    },
    HasDeletePermission() {
      return this.roles.some(role => {
        return role.role?.name === 'AllowDeleteExpert'
      })
    },
    showSurveyTag() {
      return this.advisor.tc_term_valid && this.advisor.tc_term_valid.SURVEY && !this.advisor.tc_term_valid.CONSULTATION
    },
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.loading = true
      const params = { extra: this.extra }
      return ConsultantAPI.getConsultant(this.id, params)
        .then(data => {
          if (!data) {
            this.advisor = null
            return RouteTools.setRouterMetaTitle(`Expert not found`, this.$route)
          }
          data.user_advisor_tag_maps = data.user_advisor_tag_maps?.filter(map => map.tag)
          data.jobs = this.sortJobs(data.jobs)
          this.advisor = data
          this.getPosition()

          return RouteTools.setRouterMetaTitle(`${data['firstname']} ${data['lastname']}`, this.$route)
        })
        .finally(() => {
          this.loading = false
        })
    },

    getPosition() {
      if (this.advisor.jobs && this.advisor.jobs.length) {
        const job_list = _.sortBy(this.advisor.jobs, 'start_date')
        return job_list[job_list.length - 1].position
      }
    },

    sortJobs(list) {
      let current_list = []
      let other_list = []
      list.forEach(item => {
        if (item.is_current) {
          current_list.push(item)
        } else {
          other_list.push(item)
        }
      })
      other_list = _.orderBy(
        other_list,
        'end_date', 'desc',
      )
      current_list = _.orderBy(
        current_list,
        'start_date', 'desc',
      )
      return current_list.concat(other_list)
    },

    updateDetail() {
      this.advisor = {}
      this.getDetail()
    },

    deleteAdvisor() {
      return ConsultantAPI.deleteAdvisor(this.id)
        .then(() => {
          this.getDetail()
        })
    },

    afterEditTags(tags) {
      this.tags_editing = false

      const params = []
      tags.forEach(tag => {
        const origin_tag = this.advisor.user_advisor_tag_maps.find(item => item.tag_id === tag.id)
        if (origin_tag) {
          params.push({ id: origin_tag.id })
        } else {
          if (tag.id >= 1) {
            params.push({ tag_id: tag.id })
          } else {
            params.push({
              tag: {
                name: tag.name,
                color: tag.color,
              },
            })
          }
        }
      })
      return CartAPI.updateAdvisorTags(this.advisor.id, params)
        .then(data => {
          this.advisor.user_advisor_tag_maps = data.user_advisor_tag_maps
        })
    },
  },
}
</script>

<style scoped>
.consultant-header {
  margin-top: 0;
  margin-bottom: 3px;
}

.id-font {
  color: #42B983;
}

.tc-current {
  background-color: #2A56A5;
  color: #fff;
  border-radius: 4px;
  padding: 0 20px;
  height: 1.5rem;
  line-height: 1.5rem;
}

.ban-container {
  height: 1.5rem;
  line-height: 1.5rem;
}
</style>
