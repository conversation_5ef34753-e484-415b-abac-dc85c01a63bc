<template>
  <div class="profile-item">
    <div>
      <div class="mb-8">
        <b>Basic Information</b>
        <el-button v-if="!isEdit && !isGKMRole && !advisor.is_outsource_statistic_advisor" class="el-icon-edit" @click="handleIsEdit"> Edit</el-button>
        <div v-if="isEdit" class="inline-block">
          <el-button class="el-icon-check" type="primary" :loading="submitting" :disabled="npiLoading" @click="onSave()"> Save</el-button>
          <el-button @click="cancelEdit"> Cancel</el-button>
        </div>
      </div>

      <el-form
        v-if="!isEdit"
        class="form-data show-form"
        label-position="left"
        label-width="150px">
        <el-form-item label="First Name">{{ advisor.firstname }}</el-form-item>
        <el-form-item label="Last Name">{{ advisor.lastname }}</el-form-item>
        <el-form-item label="Name Prefix">{{ advisor.name_prefix || ''}}</el-form-item>
        <el-form-item label="Rate">{{ advisor.rate }}
          <span v-if="advisor.rate && advisor.rate_currency"> / </span>
          {{ advisor.rate_currency }}
        </el-form-item>
        <el-form-item label="Minimum Charge">{{ advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }}</el-form-item>
        <el-form-item label="NPI #">
          <npi-number :advisor="advisor" />
        </el-form-item>
        <el-form-item label="Spoken Language">{{ advisor.spoken_language_list.join(', ') }}</el-form-item>
        <el-form-item label="Type">{{ advisor.type | getLabel(advisor_options.type) }}</el-form-item>
        <el-form-item label="Location">{{ advisor.location && advisor.location.name }}</el-form-item>
        <el-form-item label="Locale">{{ advisor.locale | getLabel(lang_type_options) }}</el-form-item>
        <el-form-item label="Industry"><span
          v-if="advisor.linkedin_industry_code">{{ advisor.linkedin_industry_code.label }}</span></el-form-item>
        <el-form-item label="Time zone">{{ advisor.zone_id_string }}</el-form-item>
        <el-form-item label="Notes" class="danger">{{ advisor.notes }}</el-form-item>
        <el-form-item label="Ts ID">{{ advisor.tsid }}</el-form-item>
      </el-form>

      <el-form
        v-if="isEdit"
        ref="form"
        :model="form"
        :rules="rules"
        label-position="left"
        class="update-form form-data"
        label-width="150px"
      >
        <el-form-item label="Name" style="margin-bottom: 0;" required>
          <el-row :gutter="10" type="flex" justify="space-between">
            <el-col :span="12">
              <el-form-item
                prop="firstname">
                <el-input v-model="form.firstname" placeholder="First Name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="lastname">
                <el-input v-model="form.lastname" placeholder="Last Name" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Prefix" prop="name_prefix">
          <el-select
            v-model="form.name_prefix"
            clearable
            class="w-100">
            <el-option
              v-for="item in advisor_options.name_prefix"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Minimum Charge" prop="time_minimum">
          <el-select
            v-model="form.time_minimum"
            class="w-100"
          >
            <el-option
              v-for="item in advisor_options.time_minimum_charge"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item key="npi" label="NPI #" prop="npi">
          <npi-number-input
            v-model="form.npi"
            :npi-status.sync="form.npi_status"
            :advisor-id="advisor.id"
            :loading.sync="npiLoading"
          />
        </el-form-item>
        <el-form-item
          label="Spoken Language">
          <el-select
            v-model="form.spoken_language_list"
            multiple
            filterable
            clearable
            allow-create
            default-first-option
            placeholder="Search"
            class="w-100"
          >
            <el-option
              v-for="lang in advisor_options.spoken_language"
              :key="lang.value"
              :label="lang.label"
              :value="lang.value" />
            <el-option
              v-for="lang in options.spoken_language_other"
              :key="lang"
              :label="lang"
              :value="lang" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="Location"
          prop="location_id">
          <location v-model="form.location_id"
                    @change="setLocaleByLocation" />
        </el-form-item>
        <el-form-item label="Locale">
          <el-select v-model="form.locale">
            <el-option v-for="lang in lang_type_options" :key="lang.value" :label="lang.label" :value="lang.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="Industry"
          prop="industry">
          <industry-search v-model="form.linkedin_industry_code_id" :origin-list="[form.linkedin_industry_code]" />
        </el-form-item>
        <el-form-item label="Time zone">
          <el-select v-model="form.zone_id_string"
                     clearable
                     filterable>
            <el-option v-for="lang in timezone_list" :key="lang.id" :label="lang.name" :value="lang.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Notes">
          <el-input
            ref="notes"
            v-model="form.notes"
            type="textarea"
            :min-rows="3"
          />
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import FormMixin from '@/components/FormPage/mixin'
import Mixin from '../../mixins/form'
import _ from 'lodash'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import Location from '@/components/Location'
import IndustrySearch from '@/components/SelectRemoteSearch/IndustrySearch'
import ConsultantAPI from '@/api/consultant'
import NpiNumber from '@/views/consultant/detail/components/NpiNumber'
import NpiNumberInput from '@/views/consultant/detail/components/NpiNumberInput'
import { getLocationById } from '@/utils/tool'

export default {
  name: 'BasicInfo',
  components: { Location, IndustrySearch, NpiNumber, NpiNumberInput },
  mixins: [FormMixin, Mixin, SelectMixin],
  props: {
    isGKMRole: Boolean,
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  data() {
    return {
      isEdit: false,
      npiLoading: false,
      advisor: _.cloneDeep(this.data),
      form: this.initForm(),
      rules: {
        firstname: [{ required: true, message: 'First name is required', trigger: 'change' }],
        lastname: [{ required: true, message: 'Last name is required', trigger: 'change' }],
        location_id: [{ required: true, message: 'Location is required', trigger: 'change' }],
        npi: [{
          validator: (rule, value, callback) => {
            if (value && !/^\d{10}$/.test(value)) {
              callback(new Error('NPI # is 10 digits.'))
            } else {
              callback()
            }
          }, trigger: 'blur',
        }],
      },
      options: {
        spoken_language_other: [],
      },
    }
  },

  computed: {
    ...mapGetters([
      'advisor_options',
      'lang_type_options',
      'timezone_list',
    ]),
  },

  methods: {
    handleSave(form) {
      const params = {
        id: form.id,
        firstname: form.firstname,
        lastname: form.lastname,
        name_prefix: form.name_prefix,
        location_id: form.location_id,
        linkedin_industry_code_id: form.linkedin_industry_code_id,
        locale: form.locale,
        zone_id_string: form.zone_id_string,
        notes: form.notes,
        time_minimum: form.time_minimum,
        npi: form.npi,
        npi_status: form.npi_status,
        spoken_language_list: form.spoken_language_list,
      }

      return ConsultantAPI.updateConsultant(params)
        .then(() => {
          this.isEdit = false
          this.$emit('update')
        })
    },

    initForm() {
      return {
        firstname: '',
        lastname: '',
        name_prefix: '',
        type: 'LEAD',
        location_id: null,
        linkedin_industry_code_id: null,
        time_minimum: false,
        locale: 'en-US',
        zone_id_string: 'America/New_York',
        notes: '',
        npi: '',
        npi_status: 'INITIAL',
        spoken_language_list: [],
      }
    },

    setLocaleByLocation(location_id) {
      return getLocationById(location_id)
        .then(data => {
          if (data) {
            if (data['id_path'].includes('108')) {
              this.form.locale = 'zh-CN'
            } else {
              this.form.locale = 'en-US'
            }
          }
        })
    },

    endTimeValidator(rule, value, callback) {
      if (!value.end_date && !value.is_current) {
        callback(new Error('end date is required'))
      } else {
        callback()
      }
    },

    handleIsEdit() {
      this.isEdit = true
      if (this.advisor.id) {
        Object.assign(this.form, this.advisor)
        this.options.spoken_language_other = this.form.spoken_language_list.filter(
          item => !this.advisor_options.spoken_language.some(lang => lang.value === item))
      }
    },

    cancelEdit() {
      Object.assign(this.advisor, this.data)
      this.isEdit = false
    },
  },

}
</script>

<style scoped>

.profile-item {
  border: 1px solid #c9c5c5;
  padding: 15px;
  margin: 10px 5px;
}

.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}
</style>
