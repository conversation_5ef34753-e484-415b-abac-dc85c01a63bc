<template>
  <div class="profile-item">

    <div>
      <div class="mb-8">
        <b>Relationship Summary</b>
      </div>
      <el-form
        class="form-data show-form"
        label-position="left"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="Sourced By :">
              <slot v-if="!sourced_by_edit">
                <span v-if="data.sourced_by">{{ data.sourced_by.name }}</span>
                <span v-else>N/A</span>
                <el-link v-if="hasPermission"
                         :underline="false"
                         type="primary"
                         class="el-icon-edit ml-3"
                         @click="handleIsEdit" />
              </slot>
              <slot v-else>
                <user-search v-model="sourced_by" class="w-150px" />
                <div class="inline-block">
                  <el-button class="el-icon-check ml-3"
                             type="primary"
                             :loading="submitting"
                             @click="updateSourcedBy()" />
                  <el-button class="el-icon-close" @click="sourced_by_edit = false" />
                </div>
              </slot>
            </el-form-item>
            <el-form-item label="Sourced Date :">
              <span v-if="data.sourced_at">{{ data.sourced_at | momentFormat() }}</span>
              <span v-else>N/A</span></el-form-item>
            <el-form-item label="Update By :">
              <span v-if="data.update_by">{{ data.update_by.name }}</span>
              <span v-else>N/A</span></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Requests Contacted :">{{ data.contact_count_jit }}</el-form-item>
            <el-form-item label="Consultations Completed :">{{ data.completed_consultation_count }}</el-form-item>
            <el-form-item label="Recruited By US KM :">
              <slot v-if="(!isSGDB && data.origin === 'US') || (isSGDB && data.origin === 'SEA')">Yes</slot>
              <slot v-if="(!isSGDB && data.origin !== 'US') || (isSGDB && data.origin !== 'SEA')">
                No <el-tag>{{ data.origin }}</el-tag>
              </slot>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
  </div>

</template>

<script>
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import ConsultantAPI from '@/api/consultant'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'RelationshipSummary',
  components: { UserSearch },
  props: {
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      submitting: false,
      sourced_by_edit: false,
      sourced_by: this.data.sourced_by?.id,
      db_url: import.meta.env.VITE_APP_URL_DB + '/#/consultant/index/detail/view' + '?',
      DB_name: import.meta.env.VITE_APP_DB_NAME + ' DB',
    }
  },
  computed: {
    ...mapGetters([
      'info',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    hasPermission() {
      return this.info.roles.find(item => item.role.name === 'AllowModifyExpertSourceBy')
    },
  },
  methods: {
    handleIsEdit() {
      this.sourced_by = this.data.sourced_by?.id
      this.sourced_by_edit = true
    },

    updateSourcedBy() {
      this.submitting = true
      const params = {
        id: this.data.id,
        sourced_by_id: this.sourced_by,
      }

      return ConsultantAPI.updateConsultant(params)
        .then(() => {
          this.sourced_by_edit = false
          this.$emit('update')
        })
        .finally(() => {
          this.submitting = false
        })
    },
  },
}
</script>

<style scoped>
::v-deep .el-form-item {
  margin-bottom: 3px;
}

::v-deep .el-input--mini .el-input__inner {
  height: 24px;
  line-height: 24px;
}
</style>
