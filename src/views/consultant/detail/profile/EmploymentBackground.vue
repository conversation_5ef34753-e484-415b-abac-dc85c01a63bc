<template>
  <div class="profile-item">
    <div>
      <div class="mb-8">
        <b>Employment History & Background</b>
        <el-button v-if="!isEdit && !isGKMRole && !advisor.is_outsource_statistic_advisor" icon="el-icon-edit" @click="handleIsEdit"> Edit</el-button>
        <div v-if="isEdit" class="inline-block">
          <el-button icon="el-icon-check" type="primary" :loading="submitting" @click="onSave()"> Save</el-button>
          <el-button @click="cancelEdit"> Cancel</el-button>
        </div>
      </div>
      <!--show-->
      <el-form
        v-if="!isEdit"
        class="form-data show-form"
        label-position="left"
        label-width="110px">
        <el-form-item label="Experience">
          <div class="line-height-normal">
            <div v-for="job in advisor.jobs" :key="job.id" class="mb-3">
              <router-link
                v-if="job.company"
                class="link"
                :to="{ name: 'CompanyDetail', params: { company_id: job.company.id } }">
                {{ job.company.name }}
              </router-link>
              {{
                ` | ${job.position} | ${jobDateFormat(job.start_date)} ~
                  ${job.is_current ? 'current' : jobDateFormat(job.end_date)} `
              }}
              {{ getStockCode(job.company) }}
              <div v-if="job.description" class="info pre-line" v-html="$sanitizeHtml(job.description)" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="Linkedin Profile Summary">
          <div class="pre-line line-height-normal">{{ advisor.linkedin_profile_summary }}</div>
        </el-form-item>
        <el-form-item label="Background">
          <div class="pre-line line-height-normal">{{ advisor.background }}</div>
        </el-form-item>
      </el-form>
      <!--edit-->
      <el-form
        v-if="isEdit"
        ref="form"
        :model="form"
        :rules="rules"
        class="update-form"
        label-width="110px"
        label-position="left"
        :disabled="submitting">
        <el-form-item
          v-for="(exp, index) in form.jobs"
          :key="`experience-${index}`"
          :label="!index ? 'Experience' : ''"
          class="show-form"
          required>
          <el-form-item>
            <el-row class="inline-block">
              <el-form-item
                class="form-item inline-block"
                :prop="`jobs[${index}].company`"
                :rules="{required: true, message: 'Company is required', trigger: 'change'}"
              >
                <el-select
                  v-model="exp.company"
                  value-key="id"
                  class="remote-select"
                  :class="[{'is-reverse': is_visible}]"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="Company"
                  loading-text="loading..."
                  :remote-method="getCompanyOptions"
                  no-data-text="No matching data"
                  @change="(val)=> {exp.company_id = val.id}"
                >
                  <el-option v-for="item in companyList.data" :key="item.id" :label="item.name" :value="item">
                    <span>{{ item.name }}</span>
                    <span class="ml-3 mr-normal pull-right" style="color: #8492a6;">{{ item.stock_code }}</span>
                  </el-option>
                  <template slot="empty">
                    <p class="el-select-dropdown__empty">
                      <el-link
                        type="primary"
                        :underline="false"
                        @click="goToCreateCompany(companySelectInput, index)">
                        Create <strong>{{ companySelectInput }}</strong> ...
                      </el-link>
                    </p>
                  </template>
                </el-select>
              </el-form-item>
              <el-link
                v-if="exp.company_name && !exp.company_id"
                type="primary"
                :underline="false"
                @click="goToCreateCompany(exp.company_name, index)">
                Create Company: {{ exp.company_name }}
              </el-link>
            </el-row>
            <el-form-item
              class="form-item inline-block"
              :prop="'jobs.' + index + '.position'"
              :rules="{required: true, message: 'Position is required', trigger: 'change'}"
            >
              <el-input
                v-model="exp.position"
                class="w-200px"
                placeholder="Position" />
            </el-form-item>
            <el-checkbox
v-model="exp.is_independent"
                         class="ml-8"
                         @change="(val)=> handleIndependentConsultant(val, exp, index)">Independent Consultant
            </el-checkbox>
          </el-form-item>
          <el-form-item class="show-form">
            <el-form-item
              class="form-item inline-block"
              :prop="'jobs.' + index + '.start_date'"
              :rules="{required: true, message: 'Start date is required', trigger: 'change'}"
            >
              <el-date-picker
                v-model="exp.start_date"
                type="month"
                placeholder="Start Date"
                format="MM/yyyy"
                value-format="yyyy-MM"
              />
            </el-form-item>
            -
            <div class="inline-block">
              <el-form-item
                class="form-item inline-block"
                :prop="'jobs.' + index"
                :rules="{required: true, validator: endTimeValidator , trigger: 'change'}"
                style="margin-top: 15px;"
              >
                <el-date-picker
                  v-model="exp.end_date"
                  :disabled="exp.is_current"
                  type="month"
                  placeholder="End Date"
                  format="MM/yyyy"
                  value-format="yyyy-MM"
                />
              </el-form-item>
              <el-form-item
                class="form-item ml-8 inline-block"
                :prop="'jobs.' + index + '.is_current'"
              >
                <el-checkbox v-model="exp.is_current" @change="handleCurrentToggle(exp)">Current
                </el-checkbox>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item class="form-item">
            <div class="flex">
              <el-input v-model="exp.description" type="textarea" :rows="2" class="flex-1" placeholder="description" />
              <div>
                <el-link
                  v-if="form.jobs.length > 1"
                  type="danger"
                  icon="el-icon-delete"
                  class="icon-font"
                  :underline="false"
                  @click="removeFormItem(form.jobs, index)" />
                <el-link
                  type="primary"
                  icon="el-icon-plus"
                  class="icon-font"
                  :underline="false"
                  @click="addExperience" />
              </div>
            </div>
          </el-form-item>
        </el-form-item>
        <el-form-item label="Linkedin Profile Summary">
          <el-input v-model="form.linkedin_profile_summary" type="textarea" :rows="6" placeholder="Linkedin Profile Summary" />
        </el-form-item>
        <el-form-item>
          <template #label>
            Background
            <el-tooltip effect="dark" content="Auto Generate From Employment History" placement="top">
              <el-link type="primary" :underline="false" @click="autoGenerateBackground">Auto Generate</el-link>
            </el-tooltip>
            <background-format ref="background-format" :data="form" class="display-none" />
          </template>
          <el-input v-model="form.background" type="textarea" :rows="6" placeholder="Background" />
        </el-form-item>
      </el-form>

      <el-dialog
        title="Create Company"
        width="600px"
        append-to-body
        :visible.sync="dialogVisible"
        @open="openCompanyDialog">
        <create-company
          ref="create-company"
          is-dialog
          :data="{name: companySelectInput}"
          @create="afterCreateCompany" />
      </el-dialog>
    </div>

  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import CompanyAPI from '@/api/company'
import FormMixin from '@/components/FormPage/mixin'
import Mixin from '../../mixins/form'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import { mapGetters, mapState } from 'vuex'
import BackgroundFormat from '@/components/FormatProfile/Background'
import moment from 'moment-timezone'
import createCompany from '@/views/company/update'
import _ from 'lodash'

export default {
  name: 'EmploymentBackground',
  components: { BackgroundFormat, createCompany },
  mixins: [FormMixin, Mixin, SelectMixin],
  props: {
    isGKMRole: Boolean,
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      advisor: _.cloneDeep(this.data),
      isEdit: false,
      dialogVisible: false,
      companySelectInput: '',
      jobEditingIndex: 0,
      selectLoading: false,
      form: this.initForm(),
      companyList: {
        data: [],
        count: 0,
      },
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    ...mapGetters([
      'company_options',
      'uid',
    ]),

  },

  methods: {
    initForm() {
      return {
        jobs: [
          {
            company: null,
            company_id: '',
            position: '',
            is_current: true,
            is_independent: false,
            start_date: '',
            end_date: '',
          }],
        linkedin_profile_summary: '',
        background: '',
      }
    },

    async handleIsEdit() {
      this.isEdit = true
      if (this.advisor.id) {
        this.advisor.jobs.forEach(item => {
          if (item.company.id === 97) {
            item.is_independent = true
          }
        })
        Object.assign(this.form, this.advisor)
        this.initSelect()
      }
    },

    endTimeValidator(rule, value, callback) {
      if (!value.end_date && !value.is_current) {
        callback(new Error('end date is required'))
      } else {
        callback()
      }
    },

    jobDateFormat(date) {
      const list = date.split('-')
      switch (list.length) {
        case 2:
          if (!list[1] || ['undefined', 'null', ' '].includes(list[1])) {
            return moment(list[0])
              .format('YYYY')
          } else {
            return moment(date)
              .format('MM/YYYY')
          }
        case 3:
          return moment(date)
            .format('MM/DD/YYYY')
        default:
          return date
      }
    },

    initSelect() {
      if (this.data.jobs && this.data.jobs.length) {
        const editJobIds = this.data.jobs.map(item => {
          return item.company.id
        })
          .join()
        this.loadGetCompany({
          ids: editJobIds,
          page: 1,
          size: 100,
        })
      } else {
        this.form.jobs = [
          {
            company: null,
            company_id: '',
            position: '',
            is_current: true,
            is_independent: false,
            start_date: '',
            end_date: '',
          }]
      }
    },

    getStockCode(company) {
      if (company && company.exchange) {
        return ' | ' + company.exchange + ' - ' + company['stock_code']
      }
      if (company && company.type === 'PRIVATE') {
        return ' | ' + 'Private'
      }
    },

    addExperience() {
      this.form.jobs.push({
        company: null,
        company_id: '',
        position: '',
        description: '',
        start_date: '',
        end_date: '',
        is_current: false,
        is_independent: false,
      })
    },

    handleIndependentConsultant(val, exp, expIndex) {
      if (val) {
        // 独立专家 company id=> dev:97  release:97
        const value = {
          name: 'Independent Consultant',
          id: 97,
        }
        const hasIndex = this.companyList.data.findIndex(item => item.id === 97)
        hasIndex < 0 ? this.companyList.data.push(value) : ''
        this.form.jobs[expIndex].company = value
        this.form.jobs[expIndex].company_id = 97
        this.form.jobs[expIndex].position = 'Independent Consultant'
      } else {
        this.form.jobs[expIndex].company = null
        this.form.jobs[expIndex].company_id = ''
        this.form.jobs[expIndex].position = ''
      }
    },

    handleCurrentToggle(exp) {
      if (exp.is_current) {
        exp.end_date = moment.tz(moment(), this.timeZone).format('YYYY-MM')
      }
    },

    handleSave(val) {
      const { jobs, background, id, linkedin_profile_summary } = val
      const params = { jobs, background, id, linkedin_profile_summary }

      if (this.isJobsEdited(jobs)) {
        params.latest_manually_update_at = moment().toISOString()
      }

      return ConsultantAPI.updateConsultant(params)
        .then(() => {
          this.$emit('update')
          this.isEdit = false
        })
    },

    isJobsEdited(jobs) {
      if (jobs.length !== this.data.jobs.length) {
        return true
      }
      return jobs.filter(item => checkItem(item, this.data.jobs)).length > 0

      function checkItem(data, origin_list) {
        let flag = false
        const origin_company = origin_list?.find(item => item.id === data.id)
        if (origin_company) {
          const check_keys = ['company_id', 'position', 'end_date', 'is_current', 'start_date']
          check_keys.forEach(key => {
            if (data[key] !== origin_company[key]) {
              flag = true
            }
          })
        } else {
          flag = true
        }
        return flag
      }
    },

    getCompanyOptions(val) {
      if (!val) return
      this.companySelectInput = val
      this.loadGetCompanyByEs(val)
    },

    loadGetCompany(params) {
      this.selectLoading = true
      return CompanyAPI.getCompanyList(params)
        .then(data => {
          this.companyList.data = data.list
          this.companyList.count = data.count
        })
        .finally(() => {
          this.selectLoading = false
        })
    },

    loadGetCompanyByEs(val) {
      this.selectLoading = true
      const params = {
        keyword: val,
        page: 1,
        size: 20,
      }
      return CompanyAPI.getCompanyListByEs(params)
        .then(data => {
          this.companyList.data = data.list.map(item => item.source)
          this.companyList.count = data.total
        })
        .finally(() => {
          this.selectLoading = false
        })
    },

    goToCreateCompany(name, index) {
      this.companySelectInput = name
      this.jobEditingIndex = index
      this.dialogVisible = true
    },

    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },

    afterCreateCompany(val) {
      this.dialogVisible = false
      const job = this.form.jobs[this.jobEditingIndex]
      job.company = val
      job.company_id = val.id
      this.companyList.data.push(val)
    },

    autoGenerateBackground() {
      const background = this.$refs['background-format']
      background.handleFormatProfile()
      this.$nextTick(() => {
        this.form.background = background.$el.innerText.trim()
          .replace(/\s+/g, ' ')
      })
    },

    removeFormItem(form, index) {
      form.splice(index, 1)
    },

    cancelEdit() {
      Object.assign(this.advisor, this.data)
      this.isEdit = false
    },

  },
}
</script>

<style scoped>
.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}

.icon-font {
  font-size: 12px;
  margin: 0 5px;
}

</style>
