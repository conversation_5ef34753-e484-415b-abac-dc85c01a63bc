<template>
  <div>
    <el-row>
      <el-col :span="isGKMRole ? 24 : 14">
        <RelationshipSummary :data.sync="data" @update="updateAdvisor" />
        <BasicInfo :is-g-k-m-role="isGKMRole" :data.sync="data" @update="updateAdvisor" />
        <EmploymentBackground :is-g-k-m-role="isGKMRole" :data.sync="data" @update="updateAdvisor" />
      </el-col>
      <el-col v-if="!isGKMRole" :span="10">
        <contact-info :data-user="data" user-type="ADVISOR" class="profile-item" @update="updateAdvisor" />
        <project :advisor-id="id" :data="data" @update="updateAdvisor" />
      </el-col>
    </el-row>

  </div>
</template>

<script>
import BasicInfo from './BasicInfo'
import EmploymentBackground from './EmploymentBackground'
import project from './projectV2'
import RelationshipSummary from './RelationshipSummary'
import ContactInfo from '@/components/ContactInfo/index'

export default {
  name: 'ConsultantIndex',
  components: { BasicInfo, EmploymentBackground, project, ContactInfo, RelationshipSummary },
  props: {
    data: Object,
    isGKMRole: Boolean,
  },
  data() {
    return {
      id: this.$route.params.advisor_id,
      advisor: this.data,
    }
  },

  mounted() {},

  methods: {
    updateAdvisor() {
      this.$emit('update')
    },
  },
}
</script>

<style scoped>
.profile-item {
  border: 1px solid #c9c5c5;
  padding: 15px;
  margin: 10px 5px;
}

/deep/ .el-button--mini {
  padding: 3px 6px;
}

</style>
