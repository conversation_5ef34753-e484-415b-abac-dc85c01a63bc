<template>
  <div class="profile-item">
    <div class="mb-8">
      <b>Relevant Projects</b>
      <add-to-project :advisors="[data]" :in-blacklist="blacklistStatus" @update="$emit('update')" />
    </div>

    <el-table
      stripe
      class="headerFixed"
      :data="project_list">
      <el-table-column
        label="Date Added"
      >
        <template slot-scope="scope">
          {{ getAddDate(scope.row.joined_tasks) | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column
        label="Project"
      >
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.project.name"
            :to="getRouterInfo(scope.row)">
            {{scope.row.project.name}}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Client"
      >
        <template slot-scope="{row}">
          <el-tooltip v-if="row.project.client" placement="top" :open-delay="500" :content="row.project.client.name">
            <router-link-client :data="row.project.client" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Status"
      >
        <template slot-scope="scope">
          <span :class="scope.row.project.status | getProjectStausClass(filterStatusOptions)">
            {{ scope.row.project.status | getLabel(filterStatusOptions) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Consults"
      >
        <template slot-scope="scope">
          {{ scope.row.project_completed_task_count }}
        </template>
      </el-table-column>
      <el-table-column
        label="Screening Questions & Responses"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="getProjectLatestSQ(scope.row.joined_tasks).length"
            placement="left"
            :open-delay="200">
            <div slot="content" class="sq-tooltip scrollbar-narrow">
              <show-answered-questions where="advisor_profile" :list="getProjectLatestSQ(scope.row.joined_tasks)" class="flex" />
            </div>
            <el-button type="text" class="el-icon-tickets" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" :page.sync="page" :limit.sync="size" @pagination="getList" />
  </div>
</template>

<script>
import _ from 'lodash'
import { mapGetters, mapState } from 'vuex'
import AddToProject from '../../components/addToProject'
import ConsultantAPI from '@/api/consultant.js'
import Pagination from '@/components/Pagination'
import ShowAnsweredQuestions from '@/components/Question/ShowAnsweredQuestions'
import RouterLinkClient from '@/components/RouterLink/Client'

export default {
  name: 'ProjectV2',
  components: { AddToProject, Pagination, ShowAnsweredQuestions, RouterLinkClient },
  props: {
    advisorId: {
      type: [String, Number],
    },
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      project_list: [],
      total: 0,
      page: 1,
      size: 10,
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
    ]),

    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
    }),

    blacklistStatus() {
      return this.data.status && this.data.status === 'BLACKLIST'
    },
    filterStatusOptions() {
      const result = _.cloneDeep(this.project_options.status)
      result.push({ value: 'TOTAL', color: 'primary', label: 'Total' })
      return result
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const params = {
        page: this.page,
        size: this.size,
        extra: 'joined_tasks.latest_submitted_sq.qa_list_snapshot,project.client',
      }
      return ConsultantAPI.getAdvisorProjectList(+this.advisorId, params)
        .then(data => {
          this.project_list = data.list
          this.total = data.count
        })
    },

    getAddDate(list) {
      const result = list.filter(item => item.advisor_id === +this.advisorId)
      if (result.length) return result[0]['create_at']
      return 'N/A'
    },

    // 目前是该项目的总访谈数
    getConsults(list) {
      const result = list.filter(item => item.general_status === 'COMPLETED')
      return result.length
    },

    getProjectLatestSQ(list) {
      let sq_list = []
      if (list) {
        let result = []
        list.forEach(item => {
          if (item.latest_submitted_sq && item.latest_submitted_sq.id) {
            result.push(item.latest_submitted_sq)
          }
        })
        if (result.length) {
          result = _.sortBy(result, 'submit_time')
          sq_list = result[0].qa_list_snapshot
        }
      }
      return sq_list
    },

    getRouterInfo(item) {
      const angle_list = item.joined_tasks.filter(i => i.angle_id)
      if (angle_list.length > 0) {
        const angle_ids = _.uniq(angle_list.map(i => i.angle_id)).join()

        let router_name = 'ProjectTasks'
        if (item.project.sub_type === 'Investor Call') router_name = 'ProjectClientTasksSST'
        if (item.project.client_id === this.McKinseyId) router_name = 'ProjectClientTasksMcK'

        return {
          name: router_name,
          params: { project_id: item.project.id },
          query: { advisor_id: this.advisorId, angle_ids: angle_ids },
        }
      } else {
        return {
          name: 'ProjectLeads',
          params: { project_id: item.project.id },
          query: { task_id: item.joined_tasks[0].id },
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.status-count {
  display: inline-block;
  opacity: 0.6;
  transform: scale(0.9);
}

.headerFixed table tbody {
  display: block;
  overflow-y: auto;
}

.headerFixed table thead tr, table tbody tr {
  box-sizing: border-box;
  table-layout: fixed;
  display: table;
  width: 100%;
}

.sq-tooltip{
  height:500px;
  overflow: auto;
  width:500px;
}
</style>
