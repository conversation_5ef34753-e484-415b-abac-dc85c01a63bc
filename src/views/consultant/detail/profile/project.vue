<template>
  <div>
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div />
      <div class="status-tags filter-item tabs-stackOverflow">
        <template v-for="(item, index) in filterStatusOptions">
          <el-tag
            :key="index"
            :class="{active: (select_status === item.value) }"
            @click.native="filterStatusSearch(item)"
          >
            {{ item.label }}
            <span class="status-count">{{ getAggregationsCount(item.value, 'status', aggregations) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData">
      <el-table-column
        label="Name"
      >
        <template slot-scope="scope">
          <router-link-project :data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
      >
        <template slot-scope="scope">
          <span :class="scope.row.status | getProjectStausClass(filterStatusOptions)">
            {{ scope.row.status | getLabel(filterStatusOptions) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Client"
      >
        <template v-if="scope.row.client != null" slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
      >
        <template slot-scope="scope">
          {{ scope.row.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
      >
        <template slot-scope="scope">
          <div class="capitalize">{{ scope.row.type.toLowerCase() }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
      >
        <template slot-scope="scope">
          {{ scope.row.members | getMembers('PROJECT_MANAGER') }}
        </template>
      </el-table-column>

      <el-table-column
        label="Primary Contact"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.display_data.project_client_contacts }}</div>
        </template>
      </el-table-column>

    </el-table>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
  </div>
</template>

<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkProject from '@/components/RouterLink/Project'

export default {
  name: 'Project',
  components: { Pagination, RouterLinkClient, RouterLinkProject },
  props: {
    id: {
      type: [String, Number],
    },
  },
  data() {
    return {
      loading: false,
      initAggregationsFlag: true,
      select_status: 'TOTAL',
      tableData: [],
      total: 0,
      searchQuery: {
        page: 1,
        size: 10,
        advisor_id: this.id,
        extra: 'members.user,client,project_client_contacts.client_contact,project_options',
        aggregation: 'status',
      },
      aggregations: {
        status: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'uid',
      'name',
    ]),
    filterStatusOptions() {
      const result = _.cloneDeep(this.project_options.status)
      result.push({ value: 'TOTAL', color: 'primary', label: 'Total' })
      return result
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      return ProjectAPI.getProjectList(this.searchQuery)
        .then(data => {
          formatProjectList(data['list'])

          this.tableData = data['list']
          this.total = data['count']

          if (this.initAggregationsFlag && data['aggregations']) {
            this.calAggregationsCount(data['aggregations'].status)
            this.aggregations.status = data['aggregations'].status
          }
        }).finally(() => {
          this.loading = false
        })

      function formatProjectList(list) {
        list.forEach(item => {
          item.display_data = {
            project_client_contacts: getClientContacts(item),
          }
        })

        function getClientContacts(data) {
          return data.project_client_contacts.map(item => {
            return item.client_contact_type === 'PRIMARY' && item.client_contact && item.client_contact.name || ''
          }).join('')
        }
      }
    },
    calAggregationsCount(val) {
      let active_count = 0
      let total_count = 0
      val.forEach(item => {
        if (item.value !== 'CLOSED') {
          active_count += item.count
        }
        total_count += item.count
      })
      val.push({
        value: 'ACTIVE',
        count: active_count,
      })
      val.push({
        value: 'TOTAL',
        count: total_count,
      })
    },
    getAggregationsCount(val, type, aggregations) {
      let result = 0
      aggregations[type].forEach(item => {
        if (item.value === val) {
          result = item.count
        }
      })
      return result
    },
    filterStatusSearch(val) {
      const status = val.value
      this.select_status = val.value
      this.searchQuery.status_in = null
      this.searchQuery.status = null

      if (status === 'ACTIVE') {
        this.searchQuery.status_in = this.filterStatusOptions.filter(
          item => !['CLOSED', 'TOTAL', 'ACTIVE'].includes(item.value))
          .map(item => item.value)
          .join()
      } else if (status === 'TOTAL') {
        this.searchQuery.status = null
      } else {
        this.searchQuery.status = status
      }
      this.searchQuery.page = 1
      this.initAggregationsFlag = false
      this.getList()
    },

  },
}
</script>

<style lang="scss" scoped>
.status-count {
  display: inline-block;
  opacity: 0.6;
  transform: scale(0.9);
}
</style>
