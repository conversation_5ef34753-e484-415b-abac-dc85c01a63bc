<template>
  <div>
    <div class="filter-container">
      <el-input
        v-model="query['name_like']"
        class="w-200px filter-item"
        placeholder="Project Title"
        @input="handleSearch"
      />
      <el-select
        v-model="query['client_contacts.ids']"
        clearable
        multiple
        filterable
        class="w-200px filter-item"
        placeholder="Client User"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client.client_contacts"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-if="isConsultingFirm"
        v-model="query.case_type"
        placeholder="Case Type"
        class="w-200px filter-item"
        clearable
        @change="handleSearch">
        <el-option
          v-for="item in project_options.case_type"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <user-search
        v-model="query['manager.user.ids']"
        multiple
        clearable
        placeholder="Project Manager"
        class="w-200px filter-item"
        @change="handleSearch" />
      <el-input
        v-model="query['code_contains']"
        class="w-200px filter-item"
        placeholder="Project Code"
        @input="handleSearch"
      />
      <el-popconfirm
        v-if="downloadPermission"
        icon="el-icon-info"
        icon-color="#F56C6C"
        title="Please note that this information is internal and confidential.  Do not share this information with external parties."
        cancel-button-text="Cancel"
        confirm-button-text="I agree"
        @confirm="downloadList()"
      >
        <el-button slot="reference"
                   type="primary"
                   class="filter-item pull-right"
                   icon="el-icon-download">Download
        </el-button>
      </el-popconfirm>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData"
      @sort-change="handleSortData"
    >
      <el-table-column label="Project Title (ID)">
        <template #default="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.project.name"
            :to="{ name:'ProjectInformation', params: { project_id: scope.row.project_id }}">
            {{ scope.row.project.name }} ( # {{ scope.row.project_id }} )
          </router-link>
        </template>
      </el-table-column>
      <el-table-column v-if="isConsultingFirm" label="Case Type" width="100">
        <template #default="scope">
          {{ scope.row.project.case_type | getLabel(project_options.case_type) }}
        </template>
      </el-table-column>
      <el-table-column label="Create Time" width="140" sortable="custom" prop="create_at">
        <template #default="scope">
          {{ scope.row.project.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}
        </template>
      </el-table-column>
      <el-table-column label="Client User" sortable="custom" prop="client_contacts.name">
        <template #default="scope">
          <slot v-for="(contact, index) in scope.row.project.client_contacts">
            <slot v-if="index">,</slot>
            <router-link
              :key="contact.id"
              class="text-truncate link"
              :to="{ name:'ClientContactDetail', params: { contact_id: contact.id }}">
              {{ contact.name || '' }}
            </router-link>
          </slot>
        </template>
      </el-table-column>
      <el-table-column label="Project Manager" width="140" sortable="custom" prop="manager.user.name">
        <template #default="scope">
          {{ scope.row.project.manager.user.name }}
        </template>
      </el-table-column>
      <el-table-column label="Status" width="100">
        <template #default="scope">
          {{ scope.row.project.status | getLabel(project_options.status) }}
        </template>
      </el-table-column>
      <el-table-column label="Project Code" width="120" sortable="custom" prop="code">
        <template #default="scope">
          {{ scope.row.project.code }}
        </template>
      </el-table-column>
      <el-table-column label="Attached Leads" width="85" sortable="custom" prop="count_attached_leads">
        <template #default="scope">
          {{ scope.row.count_attached_leads }}
        </template>
      </el-table-column>
      <el-table-column label="Accepted T&C" width="85" sortable="custom" prop="count_accepted_tc">
        <template #default="scope">
          {{ scope.row.count_accepted_tc }}
        </template>
      </el-table-column>
      <el-table-column label="Screened" width="85" sortable="custom" prop="count_screened">
        <template #default="scope">
          {{ scope.row.count_screened }}
        </template>
      </el-table-column>
      <el-table-column label="Sent To Client" width="85" sortable="custom" prop="count_screened">
        <template #default="scope">
          {{ scope.row.count_sent_to_client }}
        </template>
      </el-table-column>
      <el-table-column label="Client Selected" width="95" sortable="custom" prop="count_selected">
        <template #default="scope">
          {{ scope.row.count_selected }}
        </template>
      </el-table-column>
      <el-table-column label="Scheduled" width="95" sortable="custom" prop="count_scheduled">
        <template #default="scope">
          {{ scope.row.count_scheduled }}
        </template>
      </el-table-column>
      <el-table-column label="Completed" width="85" sortable="custom" prop="count_completed">
        <template #default="scope">
          {{ scope.row.count_completed }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="query.page"
      :limit.sync="query.size"
      @pagination="getList"
    />
  </div>

</template>

<script>
import { mapGetters } from 'vuex'
import ClientAPI from '@/api/client'
import Pagination from '@/components/Pagination'
import { debounce } from '@/utils/tool'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'

export default {
  name: 'Projects',
  components: {
    Pagination, UserSearch,
  },
  props: {
    client: Object,
  },

  data() {
    return {
      loading: false,
      total: 0,
      query: {
        page: 1,
        size: 10,
        sort: 'create_at desc',
        name_like: '',
        'manager.user.ids': [],
        'client_contacts.ids': [],
        code_contains: '',
      },
      tableData: [],
    }
  },

  computed: {
    ...mapGetters([
      'project_options',
      'info',
    ]),
    is_GUS_client() {
      return this.client?.am_team_id === 2
    },

    isConsultingFirm() {
      return this.client?.type === 'CONSULTING_FIRM'
    },
    downloadPermission() {
      /* Admin /AdminExcludeCompliance/ AllowClientProjectListDownload*/
      const isAdmin = this.info.roles.find(item => ['Admin', 'AdminExcludeCompliance'].includes(item.role.name))
      const AllowClientProjectListDownload = this.info.roles.find(item => item.role.name === 'AllowClientProjectListDownload')
      return isAdmin || AllowClientProjectListDownload
    },
  },

  mounted() {
    this.getList()
  },

  methods: {
    getList() {
      this.loading = true
      const params = this.formatParams()
      return ClientAPI.getClientProjectList(params)
        .then(data => {
          this.tableData = data.items
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
    formatParams() {
      const params = Object.assign({
        client_id: this.client.id,
      }, this.query)

      params['manager.user.ids'] = params['manager.user.ids'].join(',')
      params['client_contacts.ids'] = params['client_contacts.ids'].join(',')

      for (const key in params) {
        if (typeof params[key] === 'string') {
          params[key] = params[key].trim() || undefined
        }
      }
      return params
    },
    handleSearch() {
      debounce(() => {
        this.query.page = 1
        this.getList()
      }, 500)
    },
    handleSortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.query.sort = val.prop + order
        this.getList()
      }
    },

    downloadList() {
      const params = this.formatParams()
      params.client_id = this.client.id
      return ClientAPI.clientProjectsDownload(params)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
