<template>
  <div>
    <div class="mb-8 flex justify-content-between">
      <div>
        <note-tag-search
          v-if="tag_list.length"
          v-model="search_tags"
          :tag-list="tag_list"
          placeholder="Tag"
          :allow-create="false"
          @change="getNoteList" />
        <el-select
          v-model="note_type"
          clearable
          class="filter-item"
          placeholder="Type"
          @change="getNoteList"
        >
          <el-option
            v-for="item in client_options.note_types"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="client_contact_id"
          placeholder="Client User"
          clearable
          remote
          class="filter-item"
          filterable
          :remote-method="getContactList"
          @change="getNoteList"
        >
          <el-option
            v-for="item in options.client_contact_list"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <el-button
          type="success"
          icon="el-icon-plus"
          class="filter-item"
          @click="editNote()">New
        </el-button>
      </div>
      <default-notification :client-id="clientId" :default-notification-emails.sync="default_emails" />
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="note_list"
    >
      <el-table-column label="Content" prop="content" min-width="250">
        <template slot-scope="scope">
          <div v-html="$sanitizeHtml(scope.row.content)" />
        </template>
      </el-table-column>
      <el-table-column label="Contact/Type" prop="target_type">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.target_type==='CLIENT_CONTACT'"
            class="text-truncate link"
            :to="{ name:'ClientContactDetail', params: { contact_id: scope.row.client_contact_id }}">
            {{ scope.row.client_contact.name || '' }}
          </router-link>
          <router-link-project v-if="scope.row.target_type==='PROJECT'" :data="scope.row.project" />
          <div v-if="scope.row.target_type==='CLIENT'">
            <router-link
              v-for="(item, index) in scope.row.client_contact_refs"
              :key="item.client_contact_id"
              class="link"
              :to="{ name:'ClientContactDetail', params: { contact_id: item.client_contact_id }}">
              {{ item.client_contact.name || '' }}
              <span v-if="index+1 !== scope.row.client_contact_refs.length" class="info">, </span>
            </router-link>
            <span v-if="scope.row.client_contact_refs.length>0"> / </span>
            {{ scope.row.target_type | getLabel(client_options.note_types) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Communication Channel">
        <template slot-scope="scope">
          <slot v-if="scope.row.communication_method === 'OTHER'">
            {{ scope.row.communication_method_details }}
          </slot>
          <slot v-else>{{ scope.row.communication_method | getLabel(client_options.note_communication_methods) }}</slot>
        </template>
      </el-table-column>
      <el-table-column label="Tags">
        <template slot-scope="scope">
          {{ scope.row.tags | getNames(tag_list) }}
        </template>
      </el-table-column>
      <el-table-column label="Create By" width="85">
        <template slot-scope="scope">
          <span v-if="scope.row.create_by">{{ scope.row.create_by.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Create Time" width="85">
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="70">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            class="item"
            :disabled="scope.row.target_type !== 'CLIENT'"
            @click="editNote(scope.row)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="red"
            title="Are you sure you want to delete this note?"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteNote(scope.row.id, scope.$index)"
          >
            <el-link
              slot="reference"
              type="danger"
              class="item"
              :disabled="scope.row.target_type !== 'CLIENT'"
              :underline="false"
              icon="el-icon-delete" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="page"
      :limit.sync="size"
      @pagination="getNoteList"
    />

    <el-dialog
      :visible.sync="isDialogVisible"
      title="Note"
      width="50%">
      <el-form
        ref="noteValidateForm"
        :model="form"
        label-width="110px">
        <el-form-item label="Client User" prop="client_contact_refs">
          <el-select
            v-model="form.client_contacts"
            placeholder="Client User"
            clearable
            multiple
            remote
            filterable
            class="multiple-select"
            :remote-method="getContactList"
          >
            <el-option
              v-for="item in options.client_contact_list"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-for="(item, index) in form.emails"
          v-show="!form.id"
          :key="index"
          :label="!index ? 'Notifications' : ''"
          :prop="'emails.' + index + '.value'"
          :rules="[{ validator: checkEmail, trigger: ['change','blur'] }]">
          <el-input
            v-model="item.value"
            class="w-200px"
          />
          <el-link
            v-if="index === form.emails.length-1"
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addEmailItem" />
          <el-link
            v-if="index"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeEmailItem(index)" />
        </el-form-item>
        <div class="flex">
          <el-form-item label="Communication Channel" prop="communication_method" required>
            <el-select
              v-model="form.communication_method"
              placeholder="Please select"
              clearable
              filterable
              class="w-200px"
            >
              <el-option
                v-for="item in client_options.note_communication_methods"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="form.communication_method === 'OTHER'"
            prop="communication_method_details"
            required
            label-width="0"
            class="flex-1 ml-8"
          >
            <el-input
              v-model="form.communication_method_details"
              type="text"
              placeholder="Please add Additional Detail"
              clearable
            />
          </el-form-item>
        </div>
        <el-form-item label="Tags" prop="tags">
          <note-tag-search v-model="form.tags" :tag-list="tag_list" class="multiple-select" />
        </el-form-item>
        <el-form-item label="Content" prop="content" required>
          <tinymce ref="note_content" v-model="form.content" :height="400" :toolbar="toolbar" />
        </el-form-item>
        <el-form-item v-show="!form.id" label="Send Mail">
          <el-checkbox v-model="send_mail" :disabled="sendToClientThroughCapvisionPro" />
          <div v-if="sendToClientThroughCapvisionPro" class="warning line-height-normal">
            The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com), it will not send relevant emails through the system.
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="!form.content.trim()"
          :loading="submitting"
          @click="actionSubmit">Save</el-button>
        <el-button type="text" @click="isDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import Pagination from '@/components/Pagination'
import NoteTagSearch from '@/components/ClientNotes/NoteTagSearch'
import { mapGetters } from 'vuex'
import RouterLinkProject from '@/components/RouterLink/Project'
import DefaultNotification from './components/NoteNotifications'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'Notes',
  components: { RouterLinkProject, NoteTagSearch, Pagination, DefaultNotification, Tinymce },
  props: {
    clientId: [Number, String, Object],
    client: Object,
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      })
    }
    return {
      checkEmail: validateEmail,
      toolbar: ['undo redo | bold italic link | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      submitting: false,
      isDialogVisible: false,
      send_mail: true,
      search_tags: [],
      note_type: null,
      note_list: [],
      client_contact_id: null,
      tag_list: [],
      default_emails: [].concat(this.client.account_note_notification_emails),
      form: this.initForm(),
      options: { client_contact_list: [] },
      total: 0,
      page: 1,
      size: 20,
      extra: [
        'create_by',
        'tag_refs',
        'client_contact',
        'project',
        'client_contact_refs',
        'client_contact_refs.client_contact',
      ],
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
    ]),

    sendToClientThroughCapvisionPro() {
      const has_rule = this.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        this.client.preference?.tpa_client
    },
  },

  watch: {
    'sendToClientThroughCapvisionPro': {
      handler(newVal) {
        if (newVal) {
          this.send_mail = false
        }
      }, immediate: true,
    },
  },

  mounted() {
    this.getNoteList()
    this.getTagList()
    this.getContactList()
  },

  methods: {
    initForm() {
      return {
        tags: [],
        content: '',
        client_contacts: [],
        emails: this.client.account_note_notification_emails.length > 0 ? this.client.account_note_notification_emails.map(item => {
          return { value: item }
        }) : [{ value: '' }],
        communication_method: '',
        communication_method_details: '',
      }
    },
    getNoteList() {
      this.loading = true
      const params = {
        client_id: this.clientId,
        'tag_refs.tag.ids': this.search_tags.join() || undefined,
        target_type: this.note_type || undefined,
        related_client_contact_id: this.client_contact_id || undefined,
        page: this.page,
        size: this.size,
        extra: 'create_by,tag_refs,client_contact,project,client_contact_refs,client_contact_refs.client_contact',
      }
      return ClientAPI.getClientNoteList(params).then(data => {
        this.note_list = data.list.map(item => {
          item.tags = item.tag_refs.map(i => i.tag_id)
          item.client_contacts = item.client_contact_refs.map(i => i.client_contact_id)
          item.emails = item.notification_emails.map(i => {
            return { value: i }
          })
          return item
        })
        this.total = data.count
      }).finally(() => {
        this.loading = false
      })
    },

    getContactList(val) {
      const params = {
        keyword: val || undefined,
      }
      return ClientAPI.getContactList(this.clientId, params)
        .then((data) => {
          this.options.client_contact_list = data.list
        })
    },

    getTagList() {
      return ClientAPI.getClientNoteTags().then(data => {
        this.tag_list = data.list
      })
    },

    editNote(val) {
      if (val) {
        this.form = Object.assign({}, val)
        if (this.form.emails.length < 1) {
          this.form.emails = [{ value: '' }]
        }
        this.send_mail = false
      } else {
        this.form = this.initForm()
        this.form.emails = this.default_emails.length > 0 ? this.default_emails.map(item => {
          return { value: item }
        }) : [{ value: '' }]
      }
      if (this.$refs['note_content']) this.$refs['note_content'].setContent(this.form.content)
      this.isDialogVisible = true
      this.$nextTick(() => {
        this.$refs.noteValidateForm?.clearValidate()
      })
    },

    addEmailItem() {
      this.form.emails.push({ value: '' })
    },

    removeEmailItem(index) {
      this.form.emails.splice(index, 1)
    },

    actionSubmit() {
      this.$refs.noteValidateForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.form.id || undefined,
            client_id: this.clientId,
            target_type: 'CLIENT',
            tag_refs: this.form.tags.map(item => {
              return { 'tag_id': item }
            }),
            client_contact_refs: this.form.client_contacts.map(item => {
              return { 'client_contact_id': item }
            }),
            notification_emails: this.send_mail ? this.form.emails.map(item => item.value) : [],
            content: this.form.content,
            communication_method: this.form.communication_method,
            communication_method_details: this.form.communication_method === 'OTHER'
              ? this.form.communication_method_details
              : undefined,
          }

          this.submitting = true
          const request = params.id ? ClientAPI.updateClientNote(params) : ClientAPI.addClientNote(params)
          request.then((data) => {
            this.$message({
              message: 'Save successful',
              type: 'success',
            })
            this.getNoteList()
            this.getTagList()
          }).finally(() => {
            this.submitting = false
            this.isDialogVisible = false
          })
        } else {
          return false
        }
      })
    },

    deleteNote(note_id, index) {
      return ClientAPI.deleteClientNote(note_id).then(() => {
        this.note_list.splice(index, 1)
      })
    },
  },
}
</script>

<style scoped>

</style>
