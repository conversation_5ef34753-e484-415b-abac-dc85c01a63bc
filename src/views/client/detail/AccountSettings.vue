<template>
  <div>
    <el-card header="Client Portal">
      <el-form
        :disabled="disabledEdit"
        label-width="200px">
        <el-form-item label="Enable Access">
          <el-switch
            v-model="form.enable_client_portal"
            @change="actionChangeForm('enable_client_portal')" />
        </el-form-item>
        <el-form-item label="Require Registration">
          <el-switch
            v-model="form.client_portal_require_registration"
            @change="actionChangeForm('client_portal_require_registration')" />
        </el-form-item>
        <el-form-item label="Client Portal Link Expires After">
          <el-select
            v-model="form.client_portal_token_duration"
            class="w-200px"
            @change="actionChangeForm('client_portal_token_duration')"
          >
            <el-option
              v-for="item in duration_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Client Portal View">
          <el-select
            v-model="form.view_option"
            class="w-200px"
            @change="actionChangeForm('view_option')"
          >
            <el-option
              v-for="item in portal_view_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Enable Expire">
          <el-switch
            v-model="form.enable_expire_delete_recordings_transcripts"
            :disabled="DisableTranscriptAndRecordSetting"
            @change="actionChangeForm('enable_expire_delete_recordings_transcripts')" />
          <el-tooltip
            v-if="DisableTranscriptAndRecordSetting"
            effect="dark"
            placement="top">
            <div slot="content" class="w-300px">The client’s legal rules prohibit transcription and recording.(Transcript/Notetaking Rules / Disable Transcripts and Recordings)</div>
            <el-link type="primary" class="pl-5" :underline="false" icon="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="form.enable_expire_delete_recordings_transcripts" label="Recordings Expire After:">
          <el-select
            v-model="form.recordings_expire_duration_minutes"
            class="w-200px"
            @change="actionChangeForm('recordings_expire_duration_minutes')"
          >
            <el-option
              v-for="item in expiredDaysOptions()"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.enable_expire_delete_recordings_transcripts" label="Transcripts Expire After:">
          <el-select
            v-model="form.transcripts_expire_duration_minutes"
            class="w-200px"
            @change="actionChangeForm('transcripts_expire_duration_minutes')"
          >
            <el-option
              v-for="item in expiredDaysOptions()"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card header="Preference" class="mt-3">
      <el-form
        :disabled="disabledEdit"
        label-width="200px">
        <el-form-item label="To Client & Portal">
          <el-checkbox
            v-model="form.display_nominal_charge_rate_on_portal"
            @change="actionChangeDisplayNominalChargeRate">
            Display Nominal Charge Rate
          </el-checkbox>
          <br>
          <el-checkbox
            v-model="form.display_additional_multiplier_on_portal"
            @change="actionChangeForm('display_additional_multiplier_on_portal')"
          >
            Display Additional Charge Multiplier
          </el-checkbox>
        </el-form-item>
        <el-form-item label="To Client Default">
          <el-select
            v-model="form.to_client_default_profiles_format"
            class="w-300px"
            @change="actionChangeForm('to_client_default_profiles_format')"
          >
            <slot v-for="item in client_options.to_client_default_profiles_format">
              <el-option
                v-if="form.display_nominal_charge_rate_on_portal ? true : !item.need_nominal_charge"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </slot>
          </el-select>
        </el-form-item>
        <el-form-item label="Include One-Click Dial in Client Invite (Zoom)">
          <el-checkbox
            v-model="form.include_one_click_dial_in_client_invite"
            @change="actionChangeForm('include_one_click_dial_in_client_invite')" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import _ from 'lodash'
import ClientAPI from '@/api/client'
import { mapGetters } from 'vuex'

export default {
  name: 'AccountSettings',
  props: {
    client: {
      type: Object,
      required: true,
    },
    disabledEdit: Boolean,
  },
  data() {
    return {
      // 单位 分钟
      duration_options: [
        { label: '1 week', value: 10080 },
        { label: '2 week', value: 20160 },
        { label: '3 week', value: 30240 },
        { label: '4 week', value: 40320 },
        { label: '6 week', value: 60480 },
        { label: '8 week', value: 80640 },
        { label: '12 week', value: 120960 },
        { label: 'Never', value: -1 },
      ],
      portal_view_options: [
        { label: 'Matrix View', value: 'MATRIX' },
        { label: 'List View', value: 'LIST' },
        { label: 'Vertical View', value: 'VERTICAL' },
      ],
      form: {
        id: undefined,
        enable_client_portal: false,
        client_portal_require_registration: false,
        client_portal_token_duration: 1,
        display_nominal_charge_rate_on_portal: false,
        display_additional_multiplier_on_portal: false,
        enable_expire_delete_recordings_transcripts: false,
        recordings_expire_duration_minutes: 0,
        transcripts_expire_duration_minutes: 0,
        view_option: 'MATRIX',
        to_client_default_profiles_format: 'FULL_PROFILES',
        include_one_click_dial_in_client_invite: false,
      },
    }
  },
  computed: {
    ...mapGetters([
      'client_options',
    ]),

    DisableTranscriptAndRecordSetting() {
      const transcript_notetaking_rules = this.client.compliance_preference?.rule?.compliance_rules?.transcript_notetaking_rules
      return transcript_notetaking_rules?.master_switch && transcript_notetaking_rules?.disable_transcripts_and_recordings
    },
  },
  mounted() {
    this.initForm()
  },
  methods: {
    initForm() {
      for (const key in this.form) {
        this.form[key] = this.client[key]
      }
      const preference = this.client.preference
      this.form.client_portal_token_duration = preference?.client_portal_token_duration
      this.form.display_additional_multiplier_on_portal = preference?.display_additional_multiplier_on_portal
      this.form.display_nominal_charge_rate_on_portal = preference?.display_nominal_charge_rate_on_portal
      this.form.enable_expire_delete_recordings_transcripts = preference?.enable_expire_delete_recordings_transcripts
      this.form.recordings_expire_duration_minutes = preference?.recordings_expire_duration_minutes
      this.form.transcripts_expire_duration_minutes = preference?.transcripts_expire_duration_minutes
      this.form.view_option = preference?.view_option
      this.form.to_client_default_profiles_format = preference?.to_client_default_profiles_format
      this.form.include_one_click_dial_in_client_invite = preference?.include_one_click_dial_in_client_invite
    },

    actionChangeDisplayNominalChargeRate() {
      if (!this.form.display_nominal_charge_rate_on_portal) {
        this.form.display_additional_multiplier_on_portal = false
        if (['SUMMARY_CHARGE', 'SUMMARY_CHARGE_AND_AVERAGE_CPI'].includes(this.form.to_client_default_profiles_format)) {
          this.form.to_client_default_profiles_format = 'FULL_PROFILES'
        }
      }
      this.actionChangeForm('display_nominal_charge_rate_on_portal')
    },
    actionChangeForm(key) {
      const params = _.cloneDeep(this.form)

      params['preference'] = Object.assign({}, this.client.preference,
        {
          client_portal_token_duration: params.client_portal_token_duration,
          display_nominal_charge_rate_on_portal: params.display_nominal_charge_rate_on_portal,
          display_additional_multiplier_on_portal: params.display_additional_multiplier_on_portal,
          enable_expire_delete_recordings_transcripts: params.enable_expire_delete_recordings_transcripts,
          recordings_expire_duration_minutes: params.recordings_expire_duration_minutes,
          transcripts_expire_duration_minutes: params.transcripts_expire_duration_minutes,
          view_option: params.view_option,
          to_client_default_profiles_format: params.to_client_default_profiles_format,
          include_one_click_dial_in_client_invite: params.include_one_click_dial_in_client_invite,
        })

      return ClientAPI.updateClient(params)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Save Successful!',
          })
          this.$emit('update')
        })
        .catch(() => {
          this.form[key] = false
        })
    },

    expiredDaysOptions() {
      return [...new Array(90).keys()].filter(i => i > 0).map(item => {
        return { value: item * 24 * 60, label: `${item} Day(s)` }
      })
    },
  },
}
</script>

<style scoped>

</style>
