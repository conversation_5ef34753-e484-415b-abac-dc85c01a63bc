<template>
  <el-popover
    placement="top-start"
    width="250"
    :open-delay="300"
    trigger="hover"
    title="SendGrid"
    @show="getRecentOutreachRecord">
    <div v-loading="loading">
      <slot v-if="contentIsArray">
        <div v-for="(i,index) in content" :key="index" class="flex justify-content-between">
          <div class="capitalize">{{ i.result_status }}</div>
          <div class="text-right">{{ i.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}</div>
        </div>
      </slot>
      <slot v-else>{{ content }}</slot>
    </div>
    <el-icon slot="reference" name="s-opportunity" :class="emailStatus" />
  </el-popover>
</template>

<script>
import ClientAPI from '@/api/client'
import _ from 'lodash'

export default {
  name: 'ContactOutreachStatus',
  props: {
    emailStatus: String,
    emailRecords: Array,
    item: Object,
  },
  data() {
    return {
      content: null,
      loading: false,
    }
  },
  computed: {
    contentIsArray() {
      return Array.isArray(this.content)
    },
  },
  methods: {
    getRecentOutreachRecord() {
      if (this.emailStatus === 'info') {
        this.content = 'Initial'
        return
      }

      this.loading = true
      const params = {
        ids: this.item.id,
        extra: 'email_address_score.email_record_trackings',
      }
      return ClientAPI.getContactOne(this.item.id, params).then(data => {
        if (data.email_address_score) {
          const list = _.orderBy(data.email_address_score.email_record_trackings, 'create_at')
          this.content = list.slice(0, 5)
          this.content.forEach(item => {
            item.result_status = item.status.toLowerCase()
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
