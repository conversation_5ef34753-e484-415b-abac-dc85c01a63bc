<template>
  <div class="inline-block">
    <el-dropdown
      placement="bottom-start"
      trigger="hover"
      @command="action"
    >
      <el-link type="primary" :underline="false" icon="el-icon-key" />
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :command="'create'" :disabled="disabled">
          Create Account...
        </el-dropdown-item>
        <el-dropdown-item :command="'reset'" :disabled="disabled">
          Reset Password...
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-dialog
      title="Compliance Account"
      :visible.sync="display"
      width="600">
      <div v-if="typeCreate">
        Click Yes below to send the login information email to the Compliance Portal User with a new password.
      </div>
      <div v-if="typeReset">Are you sure you want to reset this Compliance Portal User’s password?</div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="text"
          @click="display = false">
          Cancel
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="actionConfirm">
          <span v-if="typeCreate">Send</span>
          <span v-if="typeReset">Reset</span>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { AppHttpService } from '@/utils/request'

export default {
  name: 'ComplianceAccountActions',
  props: {
    id: {
      type: Number,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: 'create',
      display: false,
      loading: false,
    }
  },
  computed: {
    typeCreate() {
      return this.type === 'create'
    },
    typeReset() {
      return this.type === 'reset'
    },
  },
  methods: {
    action(type) {
      this.type = type
      this.display = true
    },
    actionConfirm() {
      this.loading = true

      return AppHttpService
        .patch(`/clients/client_contacts/${this.id}/initial_portal_account`)
        .then(() => {
          this.display = false

          let title = ''

          if (this.typeCreate) {
            title = 'Account create success!'
          }

          if (this.typeReset) {
            title = 'Password reset success!'
          }

          this.$notify({
            title: title,
            type: 'success',
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style scoped>
  ::v-deep .el-dropdown {
    font-size: inherit;
  }

</style>
