<template>
  <el-popover
    placement="top"
    width="450"
    :open-delay="300"
    trigger="hover"
    @show="getOutreachRecord">
    <div v-loading="loading" style="min-height:20px;">
      <div v-for="(i,index) in records" :key="index">
        <span>Sent: {{ formatTime(i.create_at) }}</span>
        <span class="pl-5">| Opened:
          <span v-if="i.is_opened">Yes | Opened on: {{ formatTime(i.opened_at) }}</span>
          <span v-else>No</span>
        </span>
        <span v-if="i.is_clicked" class="pl-5">| Clicked: Yes | Clicked on:{{ formatTime(i.clicked_at) }}
        </span>
      </div>
    </div>
    <div slot="reference">
      <span v-if="isOpened" class="success">Yes</span>
      <slot v-else>
        <span v-if="clientOutreachRecords.length>0">No</span>
      </slot>
    </div>

  </el-popover>
</template>

<script>
import _ from 'lodash'
import Filters from '@/utils/filters'
import ClientAPI from '@/api/client'

export default {
  name: 'ContactOutreachOpened',
  props: { item: Object },
  data() {
    return {
      records: null,
      loading: false,
    }
  },
  computed: {
    clientOutreachRecords() {
      return this.item.communication_records.filter(
        item => item.email_content_type === 'CLIENT_OUTREACH')
    },
    isOpened() {
      return this.clientOutreachRecords.length > 0 && this.clientOutreachRecords.filter(
        item => item.client_contact_tracking &&
          ['OPEN', 'CLICK'].includes(item.client_contact_tracking.status)).length > 0
    },
  },
  methods: {
    formatTime(time) {
      if (time) {
        return Filters.momentFormat(time, 'MM/DD/YYYY') + ' at ' + Filters.momentFormat(time, 'h:mm A')
      }
    },
    getOutreachRecord() {
      if (this.item.communication_records.length < 1) {
        this.records = 'Initial'
        return
      }
      this.loading = true
      const params = {
        ids: this.item.id,
        extra: 'communication_records.client_contact_tracking.send_grid_events',
      }
      return ClientAPI.getContactOne(this.item.id, params).then(data => {
        const outreach_records = data.communication_records.filter(
          item => item.email_content_type === 'CLIENT_OUTREACH')

        const format_list = outreach_records.map(item => {
          item.is_opened = false
          item.is_clicked = false
          if (item.client_contact_tracking && item.client_contact_tracking.send_grid_events) {
            item.is_opened = ['OPEN', 'CLICK'].includes(item.client_contact_tracking.status)
            item.is_clicked = item.client_contact_tracking.status === 'CLICK'
            item.clicked_at = item.client_contact_tracking.send_grid_events.find(i => i.event === 'click')?.create_at
            item.opened_at = item.client_contact_tracking.send_grid_events.find(i => i.event === 'open')?.create_at ||
              item.clicked_at
          }

          return item
        })

        this.records = _.orderBy(format_list, 'create_at')
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
