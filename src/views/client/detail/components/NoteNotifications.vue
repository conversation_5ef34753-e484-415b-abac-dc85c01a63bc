<template>
  <div>
    <el-popover
      :append-to-body="true"
      placement="left"
      trigger="hover"
      width="200">
      <div>
        <div v-if="defaultNotificationEmails.length > 0">
          <div v-for="(item,index) in defaultNotificationEmails" :key="index">
            <a class="link" :href="'mailto:'+item">{{ item }}</a>
          </div>
        </div>
        <span v-else>No Data.</span>
        <el-button
          class="mt-normal pull-right"
          icon="el-icon-edit"
          @click="editEmails" />
      </div>
      <el-button slot="reference">Notification Emails
      </el-button>
    </el-popover>

    <el-dialog :visible.sync="isDialogVisible"
               title="Note"
               width="50%">
      <el-form ref="emailsValidateForm"
               :model="form"
               label-width="100px">
        <el-form-item
          v-for="(item, index) in form.emails"
          :key="index"
          :label="!index ? 'Notifications' : ''"
          :prop="'emails.' + index + '.value'"
          :rules="[{ validator: checkEmail, trigger: ['change','blur'] }]">
          <el-input
            v-model="item.value"
            class="w-200px"
          >
          </el-input>
          <el-link
            v-if="index === form.emails.length-1"
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addEmailItem" />
          <el-link
            v-if="index"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeEmailItem(index)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary"
                   @click="actionSubmit">Save</el-button>
        <el-button type="text" @click="isDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'

export default {
  name: 'NoteNotifications',
  props: {
    clientId: [Number, String, Object],
    defaultNotificationEmails: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      })
    }
    return {
      checkEmail: validateEmail,
      loading: false,
      isDialogVisible: false,
      default_emails: this.defaultNotificationEmails || [],
      form: this.initForm(),
    }
  },
  methods: {
    initForm() {
      return {
        emails: this.defaultNotificationEmails.length > 0 ? this.defaultNotificationEmails.map(item => {
          return { value: item }
        }) : [{ value: '' }],
      }
    },

    editEmails() {
      this.form = this.initForm()
      this.isDialogVisible = true
    },

    addEmailItem() {
      this.form.emails.push({ value: '' })
    },

    removeEmailItem(index) {
      this.form.emails.splice(index, 1)
    },
    actionSubmit() {
      this.$refs.emailsValidateForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.clientId,
            account_note_notification_emails: this.form.emails.map(item => item.value),
          }

          ClientAPI.updateClient(params)
            .then((data) => {
              this.$emit('update:defaultNotificationEmails', data.account_note_notification_emails)
              this.$message({
                message: 'Save successful',
                type: 'success',
              })
            })
            .finally(() => {
              this.isDialogVisible = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>

</style>
