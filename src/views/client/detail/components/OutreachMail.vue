<template>
  <div class="inline-block">
    <el-dropdown trigger="click" placement="bottom-start" @command="handleCommand">
      <el-button type="primary" :disabled="sendToClientThroughCapvisionPro" @click="handleDropdown">
        <svg-icon icon-class="email" />
        Outreach<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-if="templateLoading" disabled>
          <i class="el-icon-loading" />Loading...
        </el-dropdown-item>
        <el-dropdown-item v-if="!templateLoading && !templateList.length" disabled>No Data</el-dropdown-item>
        <el-dropdown-item v-for="item in templateList" :key="item.id" :command="item">{{ item.name }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-dialog
      :title="selectedTemplate ? selectedTemplate.name : ''"
      :visible.sync="dialogVisible"
      append-to-body
      width="60%"
      @closed="emailData = []">
      <div>
        <el-collapse v-if="emailData.length" v-loading="emailLoading" accordion>
          <el-collapse-item v-for="(item, index) in emailData" :key="index" :name="index">
            <template slot="title">
              <el-popconfirm
                title="Confirm removal of this user?"
                confirm-button-text="Confirm"
                cancel-button-text="Cancel"
                @confirm="actionRemoveUser(index)"
              >
                <el-button slot="reference"
                           type="danger"
                           icon="el-icon-close"
                           circle
                           size="mini"
                           class="remove-btn"
                           @click.stop />
              </el-popconfirm>
              <router-link
                class="text-truncate link"
                :to="{ name:'ClientContactDetail', params: { contact_id: item.user.id }}">
                {{ item.user.name || '' }}
              </router-link>
              <span v-if="!item.user.has_email" class="danger">(No Email)</span>
              <span v-if="item.user.last_contact_within_seven_days"
                    class="warning pl-5"> (Last contact within 7 days)</span>
            </template>
            <div>
              <to-cc-editor :data="item" />
              <tinymce v-model="item.subject" inline inline-header />
              <tinymce v-model="item.content" :height="300" inline is-email />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary"
                   :disabled="!emailData.length"
                   :loading="sendLoading"
                   @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email.js'
import ClientAPI from '@/api/client'
import Tinymce from '@/components/Tinymce'
import Filters from '@/utils/filters'
import _ from 'lodash'
import moment from 'moment'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import { mapState } from 'vuex'

export default {
  name: 'OutreachMail',
  components: { ToCcEditor, Tinymce },
  props: {
    clientId: [Number, String],
    selectUser: Array,
    sendToClientThroughCapvisionPro: Boolean,
  },
  data() {
    return {
      templateLoading: false,
      templateList: [],
      selectedTemplate: null,
      emailLoading: false,
      dialogVisible: false,
      emailData: [],
      sendLoading: false,
      send_mail_users: [],
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },

  methods: {
    handleDropdown() {
      if (!this.templateList.length) {
        const params = {
          reference_ids: this.clientId,
          reference_type: 'CLIENT',
          content_type: 'CLIENT_OUTREACH',
          page: 1,
          size: 999,
          has_permission: true,
          include_default_reference_type: true,
          uid: this.$store.state.user.uid,
        }

        this.templateLoading = true
        return EmailAPI.getEmailList(params).then(data => {
          this.templateList = data.list
        }).finally(() => {
          this.templateLoading = false
        })
      }
    },
    handleCommand(template) {
      this.initEmailList()
      this.generateEmails(template)
      this.dialogVisible = true
    },
    initEmailList() {
      this.send_mail_users = _.cloneDeep(this.selectUser)
      this.send_mail_users.forEach(item => {
        item.last_contact_within_seven_days = this.checkContactUser(item)
      })
    },

    checkContactUser(item) {
      if (!item.last_communication_time) return false
      const last_time = moment(item.last_communication_time)
      return moment().diff(last_time, 'days') <= 7
    },

    actionRemoveUser(index) {
      this.emailData.splice(index, 1)
    },

    generateEmails(template) {
      this.selectedTemplate = template
      console.log(this.send_mail_users)
      const params = this.send_mail_users.map(item => ({
        client_id: this.clientId,
        contact_id: item.id,
        user_id: null,
      }))

      this.emailLoading = true
      return EmailAPI.getEmailsByTemplate(this.selectedTemplate.id, params).then(data => {
        data.forEach((item, index) => {
          item.user = this.send_mail_users[index]
          item.user.has_email = !!item.user.contact_infos.find(item => item.type === 'EMAIL')
        })
        this.emailData = data
      }).finally(() => {
        this.emailLoading = false
      })
    },
    sendEmails() {
      const existFrequentContact = this.emailData.filter(item => item.user.last_contact_within_seven_days).length > 0
      const alert_message = existFrequentContact
        ? 'Existing members have contacted within seven days, are you sure to continue sending?'
        : 'Confirm to send emails?'

      if (this.checkMailAddress()) return

      this.$confirm(alert_message, 'Notice', {
        type: 'warning',
      }).then(() => {
        const params = {
          batch: this.emailData.map(item => {
            return {
              client_contact_id: item.user.id,
              email: {
                prefer_vendor: this.isSGDB ? null : 'SEND_GRID',
                from: item.from,
                from_name: item.from_name,
                subject: item.subject,
                content: item.content,
                to_list: item.to_list,
                cc_list: item.cc_list,
                bcc_list: item.bcc_list,
                reply_to_list: item.reply_to_list,
              },
            }
          }),
        }
        this.sendLoading = true
        return ClientAPI.mailOutreach(params).then((data) => {
          if (data.length > 0) {
            this.$message({
              type: 'success',
              message: 'Sending successfully!',
            })
            this.dialogVisible = false
          } else {
            this.$message({
              type: 'error',
              message: 'Sending failed!',
            })
          }
        }).finally(() => {
          this.sendLoading = false
        })
      }).catch(() => {
      })
    },

    checkMailAddress() {
      const error_address = this.emailData.filter(
        item => (!Filters.checkValidMail(item.to_list) || !Filters.checkValidMail(item.cc_list)))

      if (error_address.length > 0) {
        const error_users = error_address.map(item => item.user.full_name).join()
        const message = `Please check: <span class="danger">${error_users}</span>`
        this.$alert(message,
          'Incorrect email address', {
            confirmButtonText: 'OK',
            type: 'error',
            dangerouslyUseHTMLString: true,
          })
      }
      return error_address.length > 0
    },
  },
}
</script>

<style scoped lang="scss">
.remove-btn {
  margin-right: 5px;
}

::v-deep .el-button--mini.is-circle {
  padding: 2px;
}
</style>

