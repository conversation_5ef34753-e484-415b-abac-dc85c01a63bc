<template>
  <div class="inline-block filter-item">
    <el-link
      v-if="isEdit"
      type="primary"
      :underline="false"
      icon="el-icon-edit"
      class="mr-8"
      @click="showDialog" />
    <el-link
      v-if="!isEdit&&showClock"
      type="primary"
      :underline="false"
      icon="el-icon-alarm-clock"
      @click="showDialog" />
    <el-button
      v-if="!showClock"
      type="success"
      icon="el-icon-plus"
      @click="showDialog">
      New Task
    </el-button>

    <el-dialog
      :title="isEdit?'Update Task Dashboard':'Add Task Dashboard'"
      :visible.sync="dialogVisible"
      class="text-left"
      width="50%">
      <el-form ref="form" :model="form" label-width="125px">
        <el-form-item
          prop="assignee_id"
          label="Assignee"
          align="left"
          :rules="[{ required: true, message: 'Assignee is required.', trigger: 'submit' },]">
          <user-search
            v-model="form.assignee_id"
            :extra-member="currentUser"
            role="am"
            placeholder="Assignee" />
        </el-form-item>
        <el-form-item
          prop="priority"
          label="Priority"
          align="left">
          <el-select
            v-model="form.priority">
            <el-option
              v-for="item in priority_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Client" prop="client_contact_refs">
          <client-search
            v-model="form.client_id"
            placeholder="Client"
            class="w-200px"
            @change="clientSelected"
          />
        </el-form-item>
        <el-form-item label="Client User" prop="client_contact_refs">
          <el-select
            v-model="form.client_contact_id"
            :loading="contactLoading"
            placeholder="Client User"
            clearable
            remote
            filterable
            :remote-method="getContactList"
            class="multiple-select"
          >
            <el-option
              v-for="item in client_contact_list"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="reminder_at"
          label="Reminder Date"
          align="left">
          <el-date-picker
            v-model="form.reminder_at"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="Reminder Date"
          />
        </el-form-item>
        <el-form-item
          prop="complete_at"
          label="Completion Date"
          align="left">
          <el-date-picker
            v-model="form.complete_at"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="Completion Date"
          />
        </el-form-item>
        <el-form-item
          prop="content"
          label="Content"
          :rules="[{ required: true, trigger: 'submit' },]">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            autofocus
            class="mt-8" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="hideDialog">Cancel</el-button>
        <el-button v-if="isEdit" type="primary" @click="save">Update</el-button>
        <el-button v-if="!isEdit" type="primary" @click="save">Save</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import ClientAPI from '@/api/client'
import moment from 'moment'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch/index.vue'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'AddTaskDashboard',
  components: { ClientSearch, UserSearch },
  props: {
    tableRowData: {
      type: Object,
      default() {
        return {}
      },
    },
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
    dataClient: {
      type: Object,
      default() {
        return {}
      },
    },
    addButton: Boolean,
    showClock: {
      type: Boolean,
      default: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      contactLoading: false,
      dialogVisible: false,
      form: {
        assignee_id: this.showClock ? '' : this.tableRowData.id,
        client_id: '',
        client_contact_id: '',
        content: '',
        reminder_at: null,
        complete_at: null,
        status: 'INITIAL',
        priority: undefined,
      },
      status_options: [
        { label: 'Initial', value: 'INITIAL' },
        { label: 'On Hold', value: 'ON_HOLD' },
        { label: 'In Progress', value: 'IN_PROGRESS' },
        { label: 'Follow Up', value: 'FOLLOW_UP' },
        { label: 'Completed', value: 'COMPLETED' }],
      priority_options: [
        { label: 'Urgent', value: 'URGENT' },
        { label: 'High', value: 'HIGH' },
        { label: 'Medium', value: 'MEDIUM' },
        { label: 'Low', value: 'LOW' }],
      client_contact_list: [],
      dialogLoading: false,
    }
  },

  computed: {
    ...mapGetters([
      'info',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    currentUser() {
      return [{ user: { name: this.info.name, id: this.info.id }, uid: this.info.id }]
    },
  },

  methods: {
    async showDialog() {
      await this.initData()
      this.dialogVisible = true
    },

    initData() {
      if (this.isEdit) {
        this.form.assignee_id = this.tableRowData.assignee_id
        this.form.status = this.tableRowData.status
        this.form.priority = this.tableRowData.priority
        this.form.client_contact_id = this.tableRowData.client_contact_id || ''
        this.form.client_id = this.tableRowData.client_id || ''
        this.form.reminder_at = this.tableRowData.reminder_at || null
        this.form.complete_at = this.tableRowData.complete_at || null
        this.form.content = this.tableRowData.content || undefined
      } else {
        this.form.assignee_id = this.info.id
        this.form.client_id = this.dataClient?.id
        this.form.client_contact_id = this.dataUser?.id
        if (this.dataUser.id) {
          this.form.client_id = this.dataUser?.client_id
        }
      }
      this.getContactList()
    },

    save() {
      const params = Object.assign({}, this.form)
      params.client_contact_id = this.form.client_contact_id
      params.reminder_at = moment.tz(params.reminder_at, this.timeZone).toISOString()
      params.complete_at = moment.tz(params.complete_at, this.timeZone).toISOString()
      if (this.isEdit) { params.id = this.tableRowData.id }

      this.$refs['form'].validate(valid => {
        if (valid) {
          return (!this.isEdit ? ClientAPI.addTaskDashboard(params) : ClientAPI.updateTaskDashboard(params))
            .then(data => {
              this.$message({
                message: !this.isEdit ? 'Add successful' : 'Update successful',
                type: 'success' })
            })
            .finally(() => {
              this.hideDialog()
              this.$emit('hide-dialog')
            })
        } else {
          return false
        }
      })
    },

    hideDialog() {
      this.form = {
        assignee: this.dataUser,
        assignee_id: this.dataUser.id,
        status: undefined,
        priority: undefined,
        client_contact_id: undefined,
        client_id: undefined,
        reminder_at: undefined,
        complete_at: undefined,
        content: '',
      }
      this.dialogVisible = false
    },

    clientSelected() {
      this.form.client_contact_id = null
      this.getContactList()
    },

    getContactList(val) {
      this.client_contact_list = []
      const params = {
        client_id: this.form.client_id || undefined,
        keyword: val || undefined,
      }

      this.contactLoading = true
      return ClientAPI.getFullContactList(params)
        .then((data) => {
          this.client_contact_list = data.list
        })
        .finally(() => {
          this.contactLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
