<template>
  <div>
    <div class="filter-container">
      <el-button
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        :loading="templateLoading"
        @click="actionCreate"
      >New
      </el-button
      >
    </div>
    <el-row v-loading="loading" :gutter="16">
      <el-col v-for="(item, index) in tableData" :key="item.id" :md="12">
        <el-card class="box-card" shadow="hover">
          <el-row
            slot="header"
            type="flex"
            align="middle"
            justify="space-between"
            style="    height: 28px;"
          >
            <span
              v-if="!item.is_edit"
              class="card-header text-truncate"
              :title="item.name"
            >{{ item.name }}</span>
            <el-input
              v-if="item.is_edit"
              v-model="item.edit_form.name"
              placeholder="Name"
              class="w-200px" />
            <div v-if="item.reference_id" class="no-wrap">
              <el-link
                v-if="!item.is_edit"
                type="primary"
                :underline="false"
                icon="el-icon-edit"
                class="mr-8"
                @click="activeEdit(item)"
              />
              <el-popconfirm
                v-if="!item.is_edit"
                icon="el-icon-info"
                icon-color="#F56C6C"
                title="Confirm to Delete？"
                cancel-button-text="Cancel"
                confirm-button-text="Confirm"
                confirm-button-type="danger"
                @confirm="deleteEmail(item)"
              >
                <el-link
                  slot="reference"
                  type="danger"
                  :underline="false"
                  icon="el-icon-delete"
                />
              </el-popconfirm>
              <el-link
                v-if="item.is_edit"
                type="primary"
                :underline="false"
                :disabled="disabledSave(item.edit_form)"
                icon="el-icon-check"
                class="mr-8"
                @click="activeSave(item)"
              />
              <el-link
                v-if="item.is_edit"
                type="primary"
                :underline="false"
                icon="el-icon-refresh-left"
                @click="activeCancelSave(item)"
              />
            </div>
          </el-row>
          <el-scrollbar style="height: 100%">
            <div class="card-email">
              <div v-if="!item.is_edit">
                <h3 class="email-subject">{{ item.subject_template }}</h3>
                <div class="email-content" v-html="item.content_template" />
              </div>
              <div v-if="item.is_edit && !loading">
                <div
                  v-if="!linkedin"
                  class="text-center mb-8">
                  <span class="mr-8">Title:</span>
                  <el-input
                    v-model="item.edit_form.subject_template"
                    placeholder="Subject"
                    class="w-200px"
                  />
                </div>
                <div
                  v-if="linkedin"
                  class="text-center mb-8">
                  {{ item.edit_form.subject_template }}
                </div>
                <div style="min-height: 200px">
                  <tinymce
                    :ref="`tinymce_content-${index}`"
                    v-model="item.edit_form.content_template"
                    inline
                    is-email
                  />
                </div>
              </div>
            </div>
          </el-scrollbar>
          <slot v-if="!item.is_edit">
            <el-divider />
            <SendTestEmail :template-id="item.id" :client-id="clientId" />
          </slot>
        </el-card>
      </el-col>
    </el-row>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import API from '@/api/email'
import Tinymce from '@/components/Tinymce'
import Pagination from '@/components/Pagination'
import SendTestEmail from '@/components/SendTestEmail'

export default {
  name: 'OutreachTemplates',
  components: {
    Pagination,
    Tinymce,
    SendTestEmail,
  },
  props: {
    clientId: [Number, String],
    linkedin: Boolean,
  },
  data() {
    return {
      loading: false,
      templateLoading: false,
      tableData: [],
      total: 0,
      searchQuery: {
        uid: undefined,
        reference_id: this.clientId,
        reference_type: 'CLIENT',
        page: 1,
        size: 10,
        content_type: null,
        extra: 'create_by',
        include_default_reference_type: true,
        has_permission: true,
      },
      defaultTemplate: {
        name: null,
        subject_template: null,
        content_template: null,
        create_by_id: this.uid,
        reference_id: +this.clientId,
        reference_type: 'CLIENT',
        content_type: 'CLIENT_OUTREACH',
      },
    }
  },
  computed: {
    ...mapGetters(['uid']),
    content_type() {
      return this.linkedin ? 'LINKEDIN_MESSAGE_CLIENT' : 'CLIENT_OUTREACH'
    },
  },
  mounted() {
    this.init()
    this.handleDefaultContent()
  },
  methods: {
    init() {
      this.searchQuery.uid = this.uid
      this.searchQuery.content_type = this.content_type
      this.defaultTemplate.content_type = this.content_type

      this.getList()
    },
    getList() {
      this.loading = true
      return API.getEmailList(this.searchQuery)
        .then((data) => {
          this.tableData = data.list.map((item) => {
            item.is_edit = false
            item.edit_form = {}

            return item
          })
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
    actionCreate() {
      this.tableData.unshift({
        name: '',
        subject_template: '',
        content_template: '',
        is_edit: true,
        reference_id: this.clientId,
        edit_form: this.defaultTemplate,
      })
    },
    deleteEmail(item) {
      if (+item.create_by_id !== +this.uid) {
        this.$alert(
          `You cannot delete this template created by ${
            item.create_by ? item.create_by.name : item.create_by_id
          }.`,
          'Notice',
          {
            confirmButtonText: 'OK',
            type: 'error',
          },
        )
      } else {
        return API.deleteEmail(item.id).then(() => {
          this.getList()
        })
      }
    },
    activeEdit(item) {
      item.edit_form = _.cloneDeep(item)
      this.showSavePanel(item)
    },
    activeSave(item) {
      const params = item.edit_form
      this.handleSave(params)
        .then(data => {
          this.$message({
            message: 'Save successful',
            type: 'success',
          })
          Object.assign(item, data)
          this.hideSavePanel(item)
        })
    },

    handleSave(params) {
      if (params.id) {
        console.log(params)
        return API.updateEmail(params)
      } else {
        return API.createEmail(params)
      }
    },

    activeCancelSave(item) {
      if (!item.id) {
        this.tableData.shift()
      }
      this.hideSavePanel(item)
    },

    showSavePanel(item) {
      item.is_edit = true
    },
    hideSavePanel(item) {
      item.is_edit = false
    },
    disabledSave(form) {
      return !(form.name && form.subject_template && form.content_template)
    },
    handleDefaultContent() {
      this.templateLoading = true
      return this
        .getTemplatePlaceholder(this.content_type)
        .then(list => {
          if (Array.isArray(list) && list.length > 0) {
            Object.assign(this.defaultTemplate, _.pick(list[0], [
              'name',
              'display_name',
              'content_type',
              'subject_template',
              'content_template',
              'from_template',
              'from_name_template',
              'to_template',
              'cc_template',
              'bcc_template',
              'reply_to_template',
            ]), {
              create_by_id: this.uid,
              reference_id: this.clientId,
              reference_type: 'CLIENT',
            })
          }
        })
        .finally(() => {
          this.templateLoading = false
        })
    },
    getTemplatePlaceholder(contentType) {
      const params = {
        tags_contains_all: 'NEW_TEMPLATE_PLACEHOLDER',
        is_system_template: true,
        content_type: contentType,
        pagination: false,
      }
      return API
        .getEmailList(params)
    },

  },
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";

.el-radio {
  margin-right: 0;
}

.search-input {
  width: 250px;
  margin-right: $gap-md/2;
}

.shared-switch {
  margin-left: $gap-md/2;
}

.box-card {
  margin-bottom: $gap-sm;

  .card-header {
    font-size: 14px;
  }

  .card-email {
    height: 300px;
  }
}

::v-deep .el-scrollbar__view {
  .mce-container,
  .mce-container *,
  .mce-widget,
  .mce-widget *,
  .mce-reset {
    white-space: pre-wrap;
  }
}

.character-num {
  text-align: right;
  font-size: 12px;
  margin-right: 10px;
}

</style>
