<template>
  <el-dialog
    :visible.sync="contactDialogVisible"
    width="620px"
    :title="dialog_title"
    @open="resetForm"
  >
    <el-form ref="contactValidateForm" :model="client_contacts" label-width="100px">
      <el-form-item
        prop="firstname"
        label="First Name"
        :rules="[{ required: true, message: 'Please enter first name.', trigger: 'change' },]">
        <el-input
          v-model="client_contacts.firstname"
          placeholder="First Name"
          class="w-200px"
        />
      </el-form-item>
      <el-form-item
        prop="lastname"
        label="Last Name"
        :rules="[{ required: true, message: 'Please enter last name.', trigger: 'change' },]">
        <el-input
          v-model="client_contacts.lastname"
          placeholder="Last Name"
          class="w-200px"
        />
      </el-form-item>
      <el-form-item
        prop="location_id"
        label="Location"
        :rules="[{ required: true, message: 'Location is required.', trigger: 'change' },]">
        <location v-model="client_contacts.location_id" class="w-200px filter-item" />
      </el-form-item>
      <el-form-item label="Time zone">
        <el-select
          v-model="client_contacts.zone_id_string"
          class="w-200px"
          clearable
          filterable>
          <el-option v-for="item in timezone_list" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item
        prop="office_id"
        label="Office">
        <el-select
          v-model="client_contacts.office_id"
          class="w-200px filter-item"
          @change="checkLocation()"
        >
          <el-option
            v-for="item in office_option"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        prop="types"
        label="Type"
        :rules="[{ required: true, message: 'Type is required.', trigger: 'change' },]">
        <el-checkbox-group
          v-model="client_contacts.types"
          class="checkbox-group-vertical flex flex-wrap">
          <el-checkbox
            v-for="item in client_options.contact_person_type"
            :key="item.value"
            :label="item.value"
            style="margin-right: 1rem;"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        v-if="bcg"
        prop="roles"
        label="Role"
        :rules="[{ required: true, message: 'Role is required.', trigger: 'change' }]"
      >
        <el-checkbox-group
          v-model="client_contacts.roles"
          class="checkbox-group-vertical flex flex-wrap"
        >
          <el-checkbox
            v-for="item in client_options.contact_bcg_roles"
            :key="item.value"
            :label="item.value"
            style="margin-right: 1rem;"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="Status" required prop="status">
        <el-select
          v-model="client_contacts.status"
          class="w-200px filter-item"
          placeholder="Status"
        >
          <el-option
            v-for="item in client_options.contact_status"
            :key="item.value"
            :disabled="item.value !== 'LEFT_COMPANY'"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!--        <el-form-item label="Product" required prop="product_of_interests">-->
      <!--          <el-select-->
      <!--            v-model="client_contacts.product_of_interests"-->
      <!--            multiple-->
      <!--            class="w-200px filter-item"-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="item in client_options.product"-->
      <!--              :key="item.value"-->
      <!--              :label="item.label"-->
      <!--              :value="item.value"-->
      <!--            />-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--        <el-form-item label="Coverage" required prop="coverages">-->
      <!--          <el-select-->
      <!--            v-model="client_contacts.coverages"-->
      <!--            class="w-200px filter-item"-->
      <!--            multiple-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="item in client_options.coverage"-->
      <!--              :key="item.value"-->
      <!--              :label="item.label"-->
      <!--              :value="item.value"-->
      <!--            />-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <el-form-item
        v-for="(item, index) in client_contacts.contact_infos"
        :key="index"
        :label="!index ? 'Contact' : ''"
        required
        :prop="'contact_infos.' + index + '.value'"
        :rules="[{ validator: getValidType(item.type), trigger: ['change'] }]">
        <el-input
          v-model="item.value"
          class="input-with-select contact-value-input"
        >
          <el-select slot="prepend" v-model="item.type" style="width: 100px;" @change="changeType(item,index)">
            <el-option
              v-for="option in contact_info_options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-input>
        <el-link
          v-if="!index"
          type="primary"
          icon="el-icon-plus"
          class="icon-font"
          :underline="false"
          @click="addContactItem" />
        <el-link
          v-if="index"
          type="danger"
          icon="el-icon-delete"
          class="icon-font"
          :underline="false"
          @click="removeContactItem(index)" />
        <el-tooltip class="item" effect="dark" content="Main Contact" placement="right">
          <el-link
            v-if="item.is_main"
            type="success"
            class="icon-font"
            :underline="false"
            icon="el-icon-star-on"
          />
          <el-link
            v-else
            class="icon-font"
            :underline="false"
            icon="el-icon-star-off"
            @click="setMainContact(item, index)" />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="Funds" prop="whale_wisdom_filer_maps">
        <whale-wisdom-filer-search
          v-model="client_contacts.whale_wisdom_filer_maps"
          style="width: 400px;"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        icon="el-icon-check"
        :disabled="!client_contacts.firstname || !client_contacts.lastname"
        :loading="submitting"
        @click="submitForm"
      >
        Save
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import ClientAPI from '@/api/client'
import Tool from '@/utils/tool'
import WhaleWisdomFilerSearch from '@/components/SelectRemoteSearch/WhaleWisdomFilerSearch/index.vue'
import Location from '@/components/Location'
import { mapGetters } from 'vuex'
import _ from 'lodash'

export default {
  components: { WhaleWisdomFilerSearch, Location },
  props: {
    bcg: Boolean,
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('please input the value'))
      } else {
        const reg = /^\+?\d+(?:-?\d+){5,}$/
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error('please enter a valid phone number'))
        }
      }
    }
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      if (value === '') {
        callback(new Error('please input the value'))
      } else {
        setTimeout(() => {
          const more_than_one = /,|;|，|；/g
          if (value.match(more_than_one) && value.match(more_than_one).length) {
            callback(new Error('Only one entry can be made into an input box.'))
          } else if (mailReg.test(value)) {
            callback()
          } else {
            callback(new Error('Please enter the correct email address'))
          }
        }, 100)
      }
    }
    return {
      checkPhone: validatePhone,
      checkEmail: validateEmail,
      client_id: this.$route.params.client_id,
      contactDialogVisible: false,
      loading: false,
      submitting: false,
      dialog_title: '',
      client_contacts: {},
      office_option: [],
      contact_info_options: [
        { value: 'EMAIL', label: 'Email' },
        { value: 'PHONE', label: 'Phone' },
        { value: 'LINKEDIN_URL', label: 'Linkedin' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'client_options',
      'timezone_list',
    ]),
  },
  mounted() {
    this.getOffices()
  },
  methods: {
    openDialog(item) {
      this.dialog_title = item ? 'Update' : 'Create'
      if (item) {
        this.actionEdit(item)
      } else {
        this.createContact()
      }
    },
    quickCreateEditBcgContact(item, client_id) {
      this.dialog_title = item.contact ? 'Update' : 'Create'
      this.client_id = client_id
      this.getOffices()
      if (item.contact) {
        this.actionEdit(item.contact)
      } else {
        this.createContact()
        this.client_contacts.contact_infos[0].value = item.email
      }
    },
    createContact() {
      this.client_contacts = {
        firstname: '',
        lastname: '',
        types: ['COMMON'],
        roles: [],
        coverages: [],
        product_of_interests: [],
        status: 'PROSPECT',
        office_id: undefined,
        location_id: undefined,
        zone_id_string: Tool.timeZone,
        contact_infos: [
          {
            owner_type: 'CLIENT_CONTACT',
            type: 'EMAIL',
            value: '',
            is_main: true,
          },
        ],
        whale_wisdom_filer_maps: [],
      }
      this.contactDialogVisible = true
    },

    actionEdit(item) {
      this.client_contacts = _.cloneDeep(item)
      this.client_contacts.roles = this.client_contacts.roles || []
      this.contactDialogVisible = true
    },

    getOffices() {
      if (!this.client_id) return
      return ClientAPI.getClientOffices(this.client_id).then(data => {
        this.office_option = data['list']
      })
    },

    resetForm() {
      const form = this.$refs['contactValidateForm']
      if (form) form.resetFields()
    },

    checkLocation() {
      if (!this.client_contacts.location_id) {
        this.client_contacts.location_id = this.office_option.find(
          item => item.id === this.client_contacts.office_id)['location_id']
      }
    },

    getValidType(type) {
      if (['EMAIL', 'PHONE'].includes(type)) return type === 'EMAIL' ? this.checkEmail : this.checkPhone
      return ''
    },

    changeType(select, index) {
      const contact_list = this.client_contacts.contact_infos
      const list = contact_list.filter(item => item.type === select.type)
      const has_main = list.filter(item => item.is_main).length
      if (list.length < 2) {
        this.client_contacts.contact_infos[index].is_main = true
      } else if (has_main && list.length > 1) {
        this.client_contacts.contact_infos[index].is_main = false
      } else {
        this.client_contacts.contact_infos.find(item => item.type === select.type).is_main = true
      }
      this.checkMainContact()
    },

    addContactItem() {
      this.client_contacts.contact_infos.push({
        owner_type: 'CLIENT_CONTACT',
        type: 'EMAIL',
        value: '',
        is_main: false,
      })
      this.checkMainContact()
    },

    removeContactItem(index) {
      this.client_contacts.contact_infos.splice(index, 1)
      this.checkMainContact()
    },

    checkMainContact() {
      const data = _.cloneDeep(this.client_contacts.contact_infos)
      const email_list = data.filter(item => item.type === 'EMAIL')
      const phone_list = data.filter(item => item.type === 'PHONE')

      function checkMain(o_list, list) {
        if (!list.length) return []
        const has_main = list.filter(item => item.is_main).length
        if (!has_main) {
          o_list.find(item => item.type === list[0].type).is_main = true
        }
      }

      checkMain(this.client_contacts.contact_infos, phone_list)
      checkMain(this.client_contacts.contact_infos, email_list)
    },

    // 每种类型的联系方式 只允许有一个 main联系方式
    setMainContact(item, index) {
      if (item.is_main && this.client_contacts.contact_infos.length > 1) {
        this.$set(item, 'is_main', false)
      } else {
        this.$set(item, 'is_main', true)
        this.client_contacts.contact_infos.forEach((info, ind) => {
          if (info.type === item.type && ind !== index) {
            this.$set(info, 'is_main', false)
          }
          return info
        })
      }
    },

    submitForm() {
      this.$refs.contactValidateForm.validate((valid) => {
        if (valid) {
          const {
            firstname,
            lastname,
            types,
            roles,
            office_id,
            contact_infos,
            coverages,
            product_of_interests,
            location_id,
            zone_id_string,
            whale_wisdom_filer_maps,
          } = this.client_contacts
          if (this.client_contacts.id) {
            const params = {
              firstname,
              lastname,
              types,
              roles,
              office_id,
              contact_infos,
              coverages,
              product_of_interests,
              location_id,
              zone_id_string,
              whale_wisdom_filer_maps,
            }

            this.submitting = true
            return ClientAPI.updateClientContact(this.client_id, this.client_contacts.id, params)
              .then(() => {
                this.$message({
                  type: 'success',
                  message: 'Update successfully!',
                })
                this.contactDialogVisible = false
                this.$emit('refresh')
              })
              .finally(() => {
                this.submitting = false
              })
          } else {
            const params = []
            const contact_data = Object.assign({}, this.client_contacts)
            params.push(contact_data)

            this.submitting = true
            return ClientAPI.addClientContact(this.client_id, params)
              .then(() => {
                this.$message({
                  type: 'success',
                  message: 'Added successfully!',
                })
                this.contactDialogVisible = false
                this.$emit('refresh')
              })
              .finally(() => {
                this.submitting = false
              })
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>
