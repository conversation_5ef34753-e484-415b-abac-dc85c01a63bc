<template>
  <table-list :client-id="client.id" :client-name="client.name" :is-client-am="isClientAm" :disabled-edit="disabledEdit" />
</template>

<script>
import TableList from '../../finance/contract/components/TableList'
import { mapGetters } from 'vuex'

export default {
  name: 'Contract',
  components: { TableList },
  props: {
    client: Object,
    disabledEdit: Boolean,
  },
  data() {
    return {
      isClientAm: false,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  created() {
    this.isClientAm = this.client['account_managers'].findIndex(item => item.uid === this.uid) > -1
  },
}
</script>

<style scoped>

</style>
