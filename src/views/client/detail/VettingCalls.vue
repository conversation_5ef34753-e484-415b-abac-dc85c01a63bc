<template>
  <div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData"
    >
      <el-table-column label="Project Title (ID)">
        <template #default="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.project.name"
            :to="{ name:'ProjectInformation', params: { project_id: scope.row.project_id }}">
            {{ scope.row.project.name }} ( # {{ scope.row.project_id }} )
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Project Angle Name">
        <template #default="scope">
          {{ scope.row.angle.name }}
        </template>
      </el-table-column>
      <el-table-column label="Expert Name">
        <template #default="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column label="Vetting Call Status">
        <template #default="scope">
          <el-tooltip class="item"
                      effect="dark"
                      :content="scope.row.investor_call.update_at|momentFormat()"
                      placement="top">
            <span class="cursor-pointer">{{ getVettingCallStatus(scope.row) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Expert Honorarium">
        <template #default="scope">
          <span v-if="scope.row.rate">{{ scope.row.rate }}/{{ scope.row.rate_currency }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="query.page"
      :limit.sync="query.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ProjectAPI from '@/api/project'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'

export default {
  name: 'VettingCalls',
  components: {
    Pagination,
    RouterLinkAdvisor,
  },
  props: {
    clientId: {
      type: [String, Number],
    },
  },
  data() {
    return {
      loading: false,
      total: 0,
      query: {
        page: 1,
        size: 10,
        sort: 'create_at desc',
      },
      tableData: [],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        client_id: this.clientId,
        'investor_call.id_is_null': false,
        'project.sub_type': 'Investor Call',
        extra: 'advisor,project,angle,investor_call',
      }
      return ProjectAPI.getConsultationList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    getVettingCallStatus(item) {
      const sst_status = item.investor_call
      if (sst_status.completed) {
        return 'Completed'
      } else if (sst_status.scheduled) {
        return 'Scheduled'
      } else if (sst_status.client_requested) {
        return 'Client Requested'
      } else {
        return ''
      }
    },
  },
}
</script>

<style scoped>

</style>
