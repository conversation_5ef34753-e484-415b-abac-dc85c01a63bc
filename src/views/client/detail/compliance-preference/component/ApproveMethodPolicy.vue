<template>
  <div>
    <div>
      <div>
        Approve Method
      </div>
      <div class="ml-3 mt-8 mb-8">
        <el-radio-group v-model="data.approve_method">
          <el-radio
            v-for="item in options.approve_method"
            :key="item.value"
            :label="item.value">
            {{item.label}}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <div>
      <div>
        Approve Policy
      </div>
      <div class="ml-3 mt-8 mb-8">
        <el-radio-group v-model="data.approve_policy">
          <el-radio
            v-for="item in options.approve_policy"
            :key="item.value"
            :label="item.value">
            {{item.label}}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ApproveMethodPolicy',
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      options: {
        approve_method: [
          { value: 'EMAIL', label: 'EMAIL' },
          { value: 'PORTAL', label: 'PORTAL' },
        ],
        approve_policy: [
          { value: 'NO_NEED', label: 'NO_NEED' },
          { value: 'CAPVISION_APPROVAL', label: 'CAPVISION_APPROVAL' },
          { value: 'CAPVISION_AND_CLIENT_APPROVAL_SOME', label: 'CAPVISION_AND_CLIENT_APPROVAL_SOME' },
          { value: 'CAPVISION_AND_CLIENT_APPROVAL_ALL', label: 'CAPVISION_AND_CLIENT_APPROVAL_ALL' },
        ],
      },
    }
  },
}
</script>

<style scoped>

</style>
