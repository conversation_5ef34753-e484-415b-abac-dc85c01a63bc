<template>
  <div>
    <el-checkbox v-model="data.master_switch">
      {{ showName }}
    </el-checkbox>
    <div v-if="data.master_switch" class="ml-3 mt-8">
      Allowed?
      <el-select v-model="data.allowed" class="mb-8">
        <el-option
          v-for="item in options.is_allowed"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </el-select>
      <div v-if="data.allowed !== 'ALLOWS'" class="ml-3">
        <div>
          <el-checkbox v-model="data.has_former_employee_window" class="mb-8">Former employee window</el-checkbox>
          <div v-if="data.has_former_employee_window" class="ml-3">
            <el-select
              v-model="data.former_employee_window_detail"
              class="width-auto  mb-8">
              <el-option
                v-for="item in options.empty_window"
                :key="item.value"
                :value="item.value"
                :label="item.label" />
            </el-select>
            months
          </div>
        </div>
        <div>
          Expert level
          <el-select v-model="data.expert_level" class="mb-8">
            <el-option
              v-for="item in options.expert_level"
              :key="item.value"
              :value="item.value"
              :label="item.label" />
          </el-select>
        </div>
      </div>
      <div class="mb-8">
        <el-input v-model="data.detail_text" type="textarea" placeholder="detail" class="detail-input" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpertRestrictionsGranularItem',
  props: {
    showName: String,
    data: Object,
  },
  data() {
    return {
      options: {
        is_allowed: [
          { value: 'ALLOWS', label: 'Yes' },
          { value: 'DOES NOT ALLOW', label: 'No' },
          { value: 'REQUIRES SPECIAL APPROVAL FOR', label: 'Special approval' },
        ],
        empty_window: [
          { value: 6, label: 6 },
          { value: 12, label: 12 },
          { value: 18, label: 18 },
        ],
        expert_level: [
          { value: 'All', label: 'All' },
          { value: 'High', label: 'High' },
          { value: 'Low', label: 'Low' },
        ],
      },
    }
  },
}
</script>

<style scoped>
.detail-input {
  width: 85%;
}
</style>
