<template>
  <div>
    <div v-for="(item, index) in list" :key="index">
      <el-tag type="info" closable class="mb-8" @close="actionRemove(index)">
        {{item.name}} &nbsp;&nbsp;[ {{item.email}} ]
      </el-tag>
    </div>
    <div>
      <el-input v-model="form.name" placeholder="Name" class="w-200px" />
      <el-input v-model="form.email" placeholder="Email" class="w-200px" />
      <el-button :disabled="!form.email || !form.name" @click="actionAdd(form)">Add</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NameEmail',
  props: {
    list: Array,
  },
  data() {
    return {
      form: {
        name: undefined,
        email: undefined,
      },
    }
  },

  methods: {
    actionAdd(form) {
      this.list.push(form)
      this.form = {
        name: undefined,
        email: undefined,
      }
    },
    actionRemove(index) {
      this.list.splice(index, 1)
    },
  },
}
</script>

<style scoped>

</style>
