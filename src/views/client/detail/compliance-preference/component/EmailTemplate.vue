<template>
  <div>
    <div>
      <div>
        Hello <span>{{clientAm}}</span>,<br>
        The compliance rules for <span>{{clientName}}</span> have been approved by Capvision Compliance.
      </div>
      <p>
        Thank you,<br>
        <span>{{name}}</span>
      </p>
    </div>
    <div
      v-if="
        rules.communication_rules.email_only ||
          rules.communication_rules.phone_allowed ||
          rules.communication_rules.we_chat_allowed
      "
    >
      <h4 :style="h4_style">Communication Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr
            v-if="
              rules.communication_rules.email_only ||
                rules.communication_rules.phone_allowed ||
                rules.communication_rules.we_chat_allowed
            "
          >
            <th :style="th_td_style">Communication Rules</th>
            <td :style="th_td_style">
              {{ rules.communication_rules.email_only ? "Email Only; " : "" }}
              {{ rules.communication_rules.phone_allowed ? "Phone; " : "" }}
              {{
                rules.communication_rules.we_chat_allowed
                  ? "Messaging Platforms Allowed (such as <PERSON><PERSON><PERSON>, WhatsApp, Slack)"
                  : ""
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="
        rules.language_rules.compliance_team_language.master_switch ||
          rules.language_rules.communication_with_users_analysts.master_switch
      "
    >
      <h4 :style="h4_style">Language Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr
            v-if="rules.language_rules.compliance_team_language.master_switch"
          >
            <th :style="th_td_style">Compliance team language</th>
            <td :style="th_td_style">
              Email text:
              <span>{{
                rules.language_rules.compliance_team_language.email_text
              }}</span>
              <br>
              Expert bios/CA answers, etc:
              <span>{{
                rules.language_rules.compliance_team_language
                  .expert_bios_ca_answers_etc
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              rules.language_rules.communication_with_users_analysts
                .master_switch
            "
          >
            <th :style="th_td_style">
              Communication with users/analysts (as a compliance requirement,
              not about the user's preference)
            </th>
            <td :style="th_td_style">
              Email text:
              <span>{{
                rules.language_rules.communication_with_users_analysts
                  .email_text
              }}</span>
              <br>
              Expert bios/CA answers, etc. :
              <span>{{
                rules.language_rules.communication_with_users_analysts
                  .expert_bios_ca_answers_etc
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="
        rules.email_copying_rules.all_emails.master_switch ||
          rules.email_copying_rules.all_emails.master_switch ||
          rules.email_copying_rules.negotiating_call_schedule.master_switch
      "
    >
      <h4 :style="h4_style">Email Copying Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr v-if="rules.email_copying_rules.all_emails.master_switch">
            <th :style="th_td_style">All Emails</th>
            <td :style="th_td_style">
              <span
                v-for="(item, index) in rules.email_copying_rules.all_emails
                  .list"
                :key="index"
              >
                {{
                  `${item.name}: ${item.email} ${
                    rules.email_copying_rules.all_emails.list.length - 1 ===
                    index
                      ? ""
                      : ";"
                  }`
                }}
              </span>
            </td>
          </tr>
          <tr
            v-if="rules.email_copying_rules.recommending_experts.master_switch"
          >
            <th :style="th_td_style">Recommending Experts</th>
            <td :style="th_td_style">
              <span
                v-for="(item, index) in rules.email_copying_rules
                  .recommending_experts.list"
                :key="index"
              >
                {{
                  `${item.name}: ${item.email} ${
                    rules.email_copying_rules.recommending_experts.list.length -
                    1 ===
                    index
                      ? ""
                      : ";"
                  }`
                }}
              </span>
            </td>
          </tr>
          <tr
            v-if="
              rules.email_copying_rules.negotiating_call_schedule.master_switch
            "
          >
            <th :style="th_td_style">Negotiating Call Schedule</th>
            <td :style="th_td_style">
              <span
                v-for="(item, index) in rules.email_copying_rules
                  .negotiating_call_schedule.list"
                :key="index"
              >
                {{
                  `${item.name}: ${item.email} ${
                    rules.email_copying_rules.negotiating_call_schedule.list
                      .length -
                    1 ===
                    index
                      ? ""
                      : ";"
                  }`
                }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-if="rules.internal_email_copy_rules.all_emails">
      <h4 :style="h4_style">Internal Email Copy Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr v-if="rules.internal_email_copy_rules.all_emails">
            <th :style="th_td_style">Internal Email Copy Rules</th>
            <td :style="th_td_style">
              <span>All Emails</span>
              <span>{{
                `${rules.internal_email_copy_rules.am ? "; AM" : ""}`
              }}</span>
              <span>{{
                `${
                  rules.internal_email_copy_rules.compliance_team
                    ? "; Compliance Team"
                    : ""
                }`
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="
        rules.project_creation_rules.who_can_start_a_project.master_switch ||
          rules.project_creation_rules.is_any_project_code_or_other_similar ||
          rules.project_creation_rules.will_projects_have_a_budget ||
          rules.project_creation_rules
            .do_projects_auto_expire__close_after_a_certain_amount_of_time
      "
    >
      <h4 :style="h4_style">Project Creation Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr
            v-if="
              rules.project_creation_rules.who_can_start_a_project.master_switch
            "
          >
            <th :style="th_td_style">Who can start a project</th>
            <td :style="th_td_style">
              <span>{{
                rules.project_creation_rules.who_can_start_a_project.users_type
              }}</span
              ><br>
              <div
                v-if="
                  [
                    'Any User - special CC rules',
                    'Special persons only',
                  ].includes(
                    rules.project_creation_rules.who_can_start_a_project
                      .users_type
                  )
                "
              >
                <span
                  v-for="(item, index) in rules.project_creation_rules
                    .who_can_start_a_project.special_user_detail"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.project_creation_rules.who_can_start_a_project
                        .special_user_detail.length -
                      1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
          <tr
            v-if="
              rules.project_creation_rules.is_any_project_code_or_other_similar
            "
          >
            <th :style="th_td_style">Any project code or other similar</th>
            <td :style="th_td_style">
              <span>{{
                rules.project_creation_rules
                  .any_project_code_or_other_similar_detail
              }}</span
              ><br>
            </td>
          </tr>
          <tr v-if="rules.project_creation_rules.will_projects_have_a_budget">
            <th :style="th_td_style">Any project code or other similar</th>
            <td :style="th_td_style">
              <span>{{
                rules.project_creation_rules.projects_have_a_budget_detail
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.project_creation_rules
                .do_projects_auto_expire__close_after_a_certain_amount_of_time
            "
          >
            <th :style="th_td_style">
              Do projects auto-expire/close after a certain amount of time
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.project_creation_rules
                  .projects_auto_expire__close_after_a_certain_amount_of_time_detail
              }}</span
              ><br>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="
        rules.expert_restrictions.listed_company.master_switch ||
          rules.expert_restrictions.government_affiliated_employees
            .master_switch ||
          rules.expert_restrictions.franchisee_distributor_supplier
            .master_switch ||
          rules.expert_restrictions.healthcare_experts_clinical_trials
            .master_switch ||
          show_expert_restrictions_expert_position ||
          show_expert_restrictions_repeat_experts ||
          show_expert_restrictions_investment_target ||
          show_expert_restrictions_letter_from_employer_required ||
          show_expert_restrictions_custom_restriction_list
      "
      id="expertRestrictions"
    >
      <h4 :style="h4_style">Expert Restrictions</h4>
      <table :style="table_style">
        <tbody>
          <tr v-if="rules.expert_restrictions.listed_company.master_switch">
            <th :style="th_td_style">Listed company</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.listed_company.listed_allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.listed_company.master_switch &&
                rules.expert_restrictions.listed_company.listed_allowed !==
                'ALLOWS' &&
                rules.expert_restrictions.listed_company
                  .has_geographic_restrictions
            "
          >
            <th :style="th_td_style">
              Listed company - Geographic restrictions
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.listed_company
                  .geographic_restrictions_detail
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.listed_company.master_switch &&
                rules.expert_restrictions.listed_company.listed_allowed !==
                'ALLOWS' &&
                rules.expert_restrictions.listed_company
                  .has_former_employee_window
            "
          >
            <th :style="th_td_style">
              Listed company - Former employee window
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.listed_company.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.listed_company.master_switch &&
                rules.expert_restrictions.listed_company.listed_allowed !==
                'ALLOWS' &&
                rules.expert_restrictions.listed_company.has_ipos
            "
          >
            <th :style="th_td_style">
              Listed company - IPOs (window to consider listed)
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.listed_company.ipos_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.listed_company.master_switch &&
                rules.expert_restrictions.listed_company.listed_allowed !==
                'ALLOWS' &&
                rules.expert_restrictions.listed_company.definition.length
            "
          >
            <th :style="th_td_style">
              Listed company - Definition of a "listed company"
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.listed_company.definition.join(
                  "; "
                )}`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.listed_company.master_switch &&
                rules.expert_restrictions.listed_company.listed_allowed !==
                'ALLOWS'
            "
          >
            <th :style="th_td_style">
              Listed company - Consultant to a listed company
            </th>
            <td :style="th_td_style">
              <span>{{
                `${
                  rules.expert_restrictions.listed_company
                    .can_consultant_to_a_listed_company
                    ? "YES"
                    : "NO"
                }`
              }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch
            "
          >
            <th :style="th_td_style">Government affiliated employees</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.government_affiliated_employees
                  .allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .has_former_employee_window
            "
          >
            <th :style="th_td_style">
              Government affiliated employees - Former employee window
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .master_switch
            "
          >
            <th :style="th_td_style">Government affiliated employees - SOE</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.government_affiliated_employees.soe
                  .allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .master_switch &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .has_former_employee_window
            "
          >
            <th :style="th_td_style">SOE - Former employee window</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.soe.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .master_switch &&
                rules.expert_restrictions.government_affiliated_employees.soe
                  .allowed !== 'ALLOWS'
            "
          >
            <th :style="th_td_style">SOE - Expert level</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.soe.expert_level}`
              }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.master_switch
            "
          >
            <th :style="th_td_style">
              Government affiliated employees - Public Hospital
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.has_former_employee_window
            "
          >
            <th :style="th_td_style">
              Public Hospital - Former employee window
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.public_hospital.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_hospital.allowed !== 'ALLOWS'
            "
          >
            <th :style="th_td_style">Public Hospital - Expert level</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.public_hospital.expert_level}`
              }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.master_switch
            "
          >
            <th :style="th_td_style">
              Government affiliated employees - Public University
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.has_former_employee_window
            "
          >
            <th :style="th_td_style">
              Public University - Former employee window
            </th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.public_university.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .public_university.allowed !== 'ALLOWS'
            "
          >
            <th :style="th_td_style">Public University - Expert level</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.public_university.expert_level}`
              }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .master_switch
            "
          >
            <th :style="th_td_style">
              Government affiliated employees - Other (research institutes,
              think tanks, industry associations, etc.)
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.government_affiliated_employees.other
                  .allowed
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .master_switch &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .has_former_employee_window
            "
          >
            <th :style="th_td_style">Other - Former employee window</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.other.former_employee_window_detail || 0} months`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.government_affiliated_employees
                .master_switch &&
                rules.expert_restrictions.government_affiliated_employees
                  .allowed !== 'ALLOWS' &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .master_switch &&
                rules.expert_restrictions.government_affiliated_employees.other
                  .allowed !== 'ALLOWS'
            "
          >
            <th :style="th_td_style">Other - Expert level</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.government_affiliated_employees.other.expert_level}`
              }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.franchisee_distributor_supplier
                .master_switch
            "
          >
            <th :style="th_td_style">Franchisee/Distributor/Supplier</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.franchisee_distributor_supplier.relevance_threshold_percentage_detail} %`
              }}</span
              ><br>
            </td>
          </tr>
          <tr
            v-if="
              rules.expert_restrictions.healthcare_experts_clinical_trials
                .master_switch
            "
          >
            <th :style="th_td_style">Healthcare Experts - Disable</th>
            <td :style="th_td_style">
              <span
              >{{
                 `${
                   rules.expert_restrictions.healthcare_experts_clinical_trials
                     .can_doctors
                     ? "Doctors;"
                     : ""
                 }`
               }}
                {{
                  `${
                    rules.expert_restrictions.healthcare_experts_clinical_trials
                      .can_other_medical_professionals
                      ? "Other medical professionals;"
                      : ""
                  }`
                }}
                {{
                  `${
                    rules.expert_restrictions.healthcare_experts_clinical_trials
                      .can_medical_tech_services_pharma_etc
                      ? "Medical tech/services, Pharma, etc. (industry)"
                      : ""
                  }`
                }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              rules.expert_restrictions.healthcare_experts_clinical_trials
                .master_switch
            "
          >
            <th :style="th_td_style">Clinical Trials</th>
            <td :style="th_td_style">
              <span>{{
                `${rules.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials}`
              }}</span
              ><br>
              <span
              >Restriction
                {{
                  `${rules.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials_restriction}`
                }}</span
              ><br>
            </td>
          </tr>

          <tr
            v-if="
              show_expert_restrictions_expert_position &&
                show_expert_restrictions_expert_position_c_leval
            "
          >
            <th :style="th_td_style">Expert position - C-leval</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.expert_position
                  .restriction_position_C_level
              }}</span>
              <div
                v-if="
                  rules.expert_restrictions.expert_position
                    .restriction_position_C_level === 'Listed companies'
                "
              >
                <span>{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_C_level_listed
                }}</span>
                <span
                  v-if="
                    rules.expert_restrictions.expert_position
                      .restriction_position_C_level_listed ===
                      'Former employee window'
                  "
                >{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_C_level_listed_empty || 0
                }}
                  months</span
                >
              </div>
            </td>
          </tr>

          <tr
            v-if="
              show_expert_restrictions_expert_position &&
                show_expert_restrictions_expert_position_vp
            "
          >
            <th :style="th_td_style">
              Expert position - VP/Director/General Manager and above
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.expert_position
                  .restriction_position_VP_and_other
              }}</span>
              <div
                v-if="
                  rules.expert_restrictions.expert_position
                    .restriction_position_VP_and_other === 'Listed companies'
                "
              >
                <span>{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_VP_and_other_listed
                }}</span>
                <span
                  v-if="
                    rules.expert_restrictions.expert_position
                      .restriction_position_VP_and_other_listed ===
                      'Former employee window'
                  "
                >{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_VP_and_other_listed_empty || "0"
                }}
                  months</span
                >
              </div>
            </td>
          </tr>

          <tr
            v-if="
              show_expert_restrictions_expert_position &&
                show_expert_restrictions_expert_position_finance_staff
            "
          >
            <th :style="th_td_style">Expert position - Finance staff</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.expert_position
                  .restriction_position_finance
              }}</span>
              <div
                v-if="
                  rules.expert_restrictions.expert_position
                    .restriction_position_finance === 'Listed companies'
                "
              >
                <span>{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_finance_listed
                }}</span>
                <span
                  v-if="
                    rules.expert_restrictions.expert_position
                      .restriction_position_finance_listed ===
                      'Former employee window'
                  "
                >{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_finance_listed_empty || "0"
                }}
                  months</span
                >
              </div>
            </td>
          </tr>

          <tr
            v-if="
              show_expert_restrictions_expert_position &&
                show_expert_restrictions_expert_position_audit_staff
            "
          >
            <th :style="th_td_style">Expert position - Audit staff</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.expert_position
                  .restriction_position_audit
              }}</span>
              <div
                v-if="
                  rules.expert_restrictions.expert_position
                    .restriction_position_audit === 'Listed companies'
                "
              >
                <span>{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_audit_listed
                }}</span>
                <span
                  v-if="
                    rules.expert_restrictions.expert_position
                      .restriction_position_audit_listed ===
                      'Former employee window'
                  "
                >{{
                  rules.expert_restrictions.expert_position
                    .restriction_position_audit_listed_empty || "0"
                }}
                  months</span
                >
              </div>
            </td>
          </tr>

          <tr v-if="show_expert_restrictions_repeat_experts">
            <th :style="th_td_style">Repeat Experts</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.repeat_experts.firm_wide_or_per_user
              }}</span>
              <br>
              <span
              >restriction call number and time period:
                {{
                  rules.expert_restrictions.repeat_experts
                    .restriction_call_number_and_time_period.call_count || "-"
                }}</span
              >
              <br>
              <span
              >calls in the last:
                {{
                  rules.expert_restrictions.repeat_experts
                    .restriction_call_number_and_time_period.time || "0"
                }}
                {{
                  rules.expert_restrictions.repeat_experts
                    .restriction_call_number_and_time_period.time_type || "0"
                }}</span
              >
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_repeat_experts &&
                show_expert_restrictions_repeat_experts_has_approval_to_go_over
            "
          >
            <th :style="th_td_style">Repeat Experts - Approval to go over</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.repeat_experts
                  .approval_to_go_over_type
              }}</span>
              <span
                v-if="
                  rules.expert_restrictions.repeat_experts
                    .approval_to_go_over_type === 'Ask for approval'
                "
              ><br>
                {{
                  rules.expert_restrictions.repeat_experts
                    .approval_to_go_over_detail || "-"
                }}</span
              >
              <br>
            </td>
          </tr>

          <tr v-if="show_expert_restrictions_investment_target">
            <th :style="th_td_style">
              Investment Target - User must give target
            </th>
            <td :style="th_td_style">
              <span>{{
                show_expert_restrictions_investment_target_is_user_must_give_target
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_investment_target &&
                rules.expert_restrictions.investment_target.is_banned
            "
          >
            <th :style="th_td_style">Investment Target - Banned</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.investment_target.is_banned_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_investment_target &&
                rules.expert_restrictions.investment_target
                  .has_former_employee_window
            "
          >
            <th :style="th_td_style">
              Investment Target - Former employee window
            </th>
            <td :style="th_td_style">
              <span
              >{{
                rules.expert_restrictions.investment_target
                  .former_employee_window_detail || "0"
              }}
                months</span
              >
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_letter_from_employer_required &&
                show_expert_restrictions_letter_from_employer_required_current_employer
            "
          >
            <th :style="th_td_style">
              Letter from employer required - Current employer
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.letter_from_employer_required
                  .current_employer.conditions
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_letter_from_employer_required &&
                show_expert_restrictions_letter_from_employer_required_current_employer &&
                show_expert_restrictions_letter_from_employer_required_current_employer_can_self_consent
            "
          >
            <th :style="th_td_style">Current employer - Can self consent</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.letter_from_employer_required
                  .current_employer.above_what_level_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_letter_from_employer_required &&
                show_expert_restrictions_letter_from_employer_required_former_employer
            "
          >
            <th :style="th_td_style">
              Letter from employer required - Former employer
            </th>
            <td :style="th_td_style">
              <span
              >Left in last
                {{
                  rules.expert_restrictions.letter_from_employer_required
                    .former_employer.left_in_last_month || 0
                }}
                months</span
              >
            </td>
          </tr>
          <tr
            v-if="
              show_expert_restrictions_letter_from_employer_required &&
                show_expert_restrictions_letter_from_employer_required_former_employer &&
                show_expert_restrictions_letter_from_employer_required_former_employer_can_self_consent
            "
          >
            <th :style="th_td_style">Former employer - Can self consent</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.letter_from_employer_required
                  .former_employer.above_what_level_detail
              }}</span>
            </td>
          </tr>

          <tr
            v-if="
              show_expert_restrictions_custom_restriction_list &&
                show_expert_restrictions_custom_restriction_list_where
            "
          >
            <th :style="th_td_style">Custom restriction list - Link</th>
            <td :style="th_td_style">
              <span>{{
                show_expert_restrictions_custom_restriction_list_where
              }}</span>
            </td>
          </tr>
          <tr v-if="show_expert_restrictions_custom_restriction_list">
            <th :style="th_td_style">Custom restriction list</th>
            <td :style="th_td_style">
              <span>{{
                rules.expert_restrictions.custom_restriction_list
                  .current_employees_or_formers_too
              }} employees</span>
              <span
                v-if="
                  rules.expert_restrictions.custom_restriction_list
                    .current_employees_or_formers_too === 'Current and Formers'
                "
              >{{
                rules.expert_restrictions.custom_restriction_list
                  .period_to_include_formers || 0
              }}
                months</span
              >
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="
        show_recommend_email_has_custom_language_need_to_include_in_the_recommend_email ||
          show_recommend_email_is_label_experts ||
          show_recommend_email_has_special_email_subject_rules
      "
    >
      <h4 :style="h4_style">Recommend Email</h4>
      <table :style="table_style">
        <tbody>
          <tr
            v-if="
              show_recommend_email_has_custom_language_need_to_include_in_the_recommend_email
            "
          >
            <th :style="th_td_style">
              Include the following in recommend email
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.recommend_email
                  .custom_language_need_to_include_in_the_recommend_email
              }}</span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Label experts (expert employers)</th>
            <td :style="th_td_style">
              {{ show_recommend_email_is_label_experts ? "YES" : "NO" }}
            </td>
          </tr>
          <tr v-if="show_recommend_email_has_special_email_subject_rules">
            <th :style="th_td_style">
              Include Email Subject rules in recommend email
            </th>
            <td :style="th_td_style">
              {{ show_recommend_email_special_email_subject_rules }}
            </td>
          </tr>
          <!--          <tr>-->
          <!--            <th :style="th_td_style">Short Form Profiles</th>-->
          <!--            <td :style="th_td_style">-->
          <!--              {{ show_recommend_email_is_short_form_profiles ? "YES" : "NO" }}-->
          <!--            </td>-->
          <!--          </tr>-->
        </tbody>
      </table>
    </div>

    <div
      v-if="
        show_pre_call_ca_usage_master_switch &&
          (show_pre_call_has_ca_valid_once_answered ||
            show_pre_call_has_ca_valid_follow_up)
      "
    >
      <h4 :style="h4_style">(Pre-call) CA Usage/Expiration Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr v-if="show_pre_call_has_ca_valid_once_answered">
            <th :style="th_td_style">
              How long is the CA valid for once answered (until first call)
            </th>
            <td :style="th_td_style">
              <span>{{ rules.pre_call_ca_usage.ca_valid_once_answered }}</span>
              <span
                v-if="
                  rules.pre_call_ca_usage.ca_valid_once_answered ===
                    'Time period'
                "
              >:
                {{
                  rules.pre_call_ca_usage
                    .ca_valid_once_answered_time_period_count || 0
                }}
                {{
                  rules.pre_call_ca_usage
                    .ca_valid_once_answered_time_period_type
                }}</span
              >
            </td>
          </tr>
          <tr v-if="show_pre_call_has_ca_valid_follow_up">
            <th :style="th_td_style">
              How long is the CA valid for if there is a follow up call
            </th>
            <td :style="th_td_style">
              <span>{{ rules.pre_call_ca_usage.ca_valid_follow_up }}</span>
              <span
                v-if="
                  rules.pre_call_ca_usage.ca_valid_follow_up === 'Time period'
                "
              >:
                {{
                  rules.pre_call_ca_usage.ca_valid_follow_up_time_period_count || 0
                }}
                {{
                  rules.pre_call_ca_usage.ca_valid_follow_up_time_period_type
                }}</span
              >
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-if="show_approval">
      <h4 :style="h4_style">Approval</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Approval method</th>
            <td :style="th_td_style">
              <span>{{ rules.approval.approval_method }}</span>
              <div v-if="rules.approval.approval_method === 'Email'">
                {{ rules.approval.who_send }} send <br>
                <span v-if="rules.approval.is_client_have_portal_access"
                >client have Portal access</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Names and Emails to send approval to</th>
            <td :style="th_td_style">
              <span
                v-if="!rules.approval
                .names_and_emails_to_send_approval_to.length">-</span>
              <span
                v-for="(item, index) in rules.approval
                  .names_and_emails_to_send_approval_to"
                :key="index"
              >
                {{
                  `${item.name}: ${item.email} ${
                    rules.approval.names_and_emails_to_send_approval_to.length -
                    1 ===
                    index
                      ? ""
                      : ";"
                  }`
                }}
              </span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">
              Names and Emails of those that can approve
            </th>
            <td :style="th_td_style">
              <span>{{ rules.approval.who_can_approval }}</span>
              <div
                v-if="rules.approval.who_can_approval === 'Only certain people'"
              >
                <span
                  v-for="(item, index) in rules.approval
                    .names_and_emails_to_approval"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.approval.names_and_emails_to_approval.length - 1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Capvision Approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.approval.has_capvision_approval ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_has_calls_for_client_to_approve">
            <th :style="th_td_style">Calls for Client to Approve</th>
            <td :style="th_td_style">
              <span>{{ rules.approval.calls_for_client_to_approve_type }}</span>
              <div
                v-if="
                  rules.approval.calls_for_client_to_approve_type ===
                    'Conditional'
                "
              >
                {{ rules.approval.client_conditional_approve_type }} <br>
                <span
                  v-if="
                    rules.approval.client_conditional_approve_type === 'Other'
                  "
                >{{
                  rules.approval.client_conditional_approve_others_detail
                }}</span>
                <span
                  v-if="
                    rules.approval.client_conditional_approve_type === 'Listed company experts'
                  "
                >{{
                  rules.approval.client_conditional_approve_type_former_employee_window_detail
                }}
                  months</span>
              </div>
              <div
                v-if="
                  rules.approval.calls_for_client_to_approve_type ===
                    'Internal to Client'
                "
              >
                {{ rules.approval.internal_to_client_detail }}
              </div>
            </td>
          </tr>
          <tr v-if="show_has_in_person_meetings">
            <th :style="th_td_style">In-person meetings</th>
            <td :style="th_td_style">
              <span>{{ rules.approval.in_person_meetings_type }}</span>
              <div
                v-if="
                  rules.approval.in_person_meetings_type ===
                    'Requires approval regardless of call approval rules'
                "
              >
                <span
                  v-for="(item, index) in rules.approval
                    .in_person_meetings_approval_list"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.approval.in_person_meetings_approval_list.length -
                      1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>

          <tr v-if="show_has_include_details_on_previous_calls_by_expert">
            <th :style="th_td_style">
              Include details on previous calls by expert
            </th>
            <td :style="th_td_style">
              <span v-if="show_has_calls_by_expert_with_all_clients"
              >all clients: last
                {{
                  rules.approval.all_clients_relevant_time_period_month || '0'
                }}
                months;
              </span>
              <span v-if="show_has_calls_by_expert_with_this_client_only"
              >this client only: last
                {{
                  rules.approval.client_only_relevant_time_period_month || '0'
                }}
                months</span
              >
            </td>
          </tr>

          <tr v-if="show_has_written_content">
            <th :style="th_td_style">
              Written content (report, web link, anything) expert wants to send
              user after a call
            </th>
            <td :style="th_td_style">
              <div>
                Capvision compliance review first:
                <span>{{
                  show_is_capvision_compliance_review_first ? "YES" : "NO"
                }}</span>
              </div>
              <div
                v-if="rules.approval.is_client_compliance_needs_to_pre_approve"
              >
                Client compliance needs to pre-approve:
                <span>{{
                  rules.approval.client_compliance_needs_to_pre_approve_detail
                }}</span>
              </div>
              <div
                v-if="
                  rules.approval.is_copy_client_compliance_when_sending_to_user
                "
              >
                Copy client compliance when sending to user:
                <span>{{
                  rules.approval
                    .copy_client_compliance_when_sending_to_user_detail
                }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">CA Answers in the Approval email</th>
            <td :style="th_td_style">
              <span>{{
                show_has_ca_answers_in_the_approval_email ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_has_internal_copy_rules">
            <th :style="th_td_style">Internal copy rules</th>
            <td :style="th_td_style">
              <span>{{
                rules.approval.has_internal_copy_rules_am ? "AM; " : ""
              }}</span>
              <span>{{
                rules.approval.has_internal_copy_rules_capvision_compliance
                  ? "Capvision Compliance"
                  : ""
              }}</span>
            </td>
          </tr>

          <tr
            v-if="show_can_send_ca_and_bio_even_if_they_do_not_require_approval"
          >
            <th :style="th_td_style">
              Send CA and bio even if they don't require approval
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.approval
                  .send_ca_and_bio_even_if_they_do_not_require_approval_to_who
              }}</span>
              <div
                v-if="
                  rules.approval
                    .send_ca_and_bio_even_if_they_do_not_require_approval_to_who ===
                    'Special list'
                "
              >
                <span
                  v-for="(item, index) in rules.approval
                    .send_ca_and_bio_even_if_they_do_not_require_approval_special_list"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.approval
                        .send_ca_and_bio_even_if_they_do_not_require_approval_special_list
                        .length -
                      1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- send_ca_and_bio_even_if_they_do_not_require_approval_special_list -->
    <div v-if="show_call_scheduling_emails">
      <h4 :style="h4_style">Call Scheduling Emails</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Schedule with</th>
            <td :style="th_td_style">
              <span>{{ rules.call_scheduling_emails.schedule_with }}</span>
              <div
                v-if="
                  rules.call_scheduling_emails.schedule_with === 'With other'
                "
              >
                When scheduling emails, do not talk to the user, schedule them with:
                {{ rules.call_scheduling_emails.schedule_with_other_detail || '-'}}
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">
              Wait for any discussion of scheduling until after approval is
              received
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.call_scheduling_emails
                  .is_wait_for_any_discussion_of_scheduling_until_after_approval_is_received
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              rules.call_scheduling_emails
                .can_send_placeholder_calendar_invite_before_approval
            "
          >
            <th :style="th_td_style">
              Can send placeholder calendar invite before approval? (Pending
              calendar invites do not contain dial-in details)
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.call_scheduling_emails
                  .send_placeholder_calendar_invite_before_approval_type
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Transcript/Notetaking Rules -->
    <div v-if="show_transcript_notetaking_rules">
      <h4 :style="h4_style">Transcript/Notetaking Rules</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Allowed</th>
            <td :style="th_td_style">
              <span>{{
                rules.transcript_notetaking_rules.allowed ? "YES" : "NO"
              }}</span>
              <div v-if="rules.transcript_notetaking_rules.allowed">
                <span
                >spend to
                  {{ rules.transcript_notetaking_rules.send_to }}</span
                >
              </div>
              <div
                v-if="rules.transcript_notetaking_rules.allowed &&
                rules.transcript_notetaking_rules.send_to === 'Other'">
                The transcript/recording should be sent to :
                <span
                  v-for="(item, index) in rules.transcript_notetaking_rules
                    .send_to_detail"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.transcript_notetaking_rules.send_to_detail.length -
                      1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Allowed to use the (Chinese) note-taking/transcript service?</th>
            <td :style="th_td_style">
              <span
              >{{
                rules.transcript_notetaking_rules
                  .allowed_to_use_chinese_transcript_service
                  ? "YES"
                  : "NO"
              }}
              </span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Copy client compliance when sending</th>
            <td :style="th_td_style">
              <span
              >{{
                 rules.transcript_notetaking_rules
                   .is_copy_client_compliance_when_sending
                   ? "YES"
                   : "NO"
               }}
                <br>

                <span
                  v-if="
                    rules.transcript_notetaking_rules
                      .is_copy_client_compliance_when_sending
                  "
                >{{
                  rules.transcript_notetaking_rules
                    .copy_client_compliance_when_sending_detail
                }}
                <span v-if="rules.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts &&
                 rules.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts.length">
                  CC on all transcript/recording emails to analysts:
                  {{ rules.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts.join() }}
                </span>
                </span>

              </span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Require compliance review of Recordings and Transcripts before client analyst can access
            </th>
            <td :style="th_td_style">
              <span
              >{{
                 rules.transcript_notetaking_rules
                   .is_require_compliance_review_of_recordings_and_transcripts_first
                   ? "YES"
                   : "NO"
               }}
                <br>

                <span
                  v-if="
                    rules.transcript_notetaking_rules
                      .require_compliance_review_email_list
                  "
                >Copy to: {{
                  rules.transcript_notetaking_rules
                    .require_compliance_review_email_list.join()
                }}</span
                >
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Arrange Email -->
    <div v-if="show_arrange_email">
      <h4 :style="h4_style">Arrange Email</h4>
      <table :style="table_style">
        <tbody>
          <tr
            v-if="
              rules.arrange_email
                .has_any_special_language_that_needs_to_be_put_in
            "
          >
            <th :style="th_td_style">
              Any special language that needs to be put in
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.arrange_email
                  .any_special_language_that_needs_to_be_put_in_detail
              }}</span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">When to copy client compliance</th>
            <td :style="th_td_style">
              <span>{{
                rules.arrange_email.when_to_copy_client_compliance
              }}</span>
              <div
                v-if="
                  rules.arrange_email.when_to_copy_client_compliance ===
                    'Other rule'
                "
              >
                :{{ rules.arrange_email.copy_client_compliance_detail }}
              </div>
              <div
                v-if="rules.arrange_email
                .copy_client_compliance_list.length">
                Copy to:
                <span
                  v-for="(item, index) in rules.arrange_email
                    .copy_client_compliance_list"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.arrange_email.copy_client_compliance_list.length -
                      1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">
              Include CA answers in the Arrange email
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.arrange_email.is_include_ca_answers_in_the_arrange_email
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="rules.arrange_email.is_allowed_to_use_their_own_conf_system"
          >
            <th :style="th_td_style">Allowed to use their own conf system</th>
            <td :style="th_td_style">
              <span>{{
                rules.arrange_email.allowed_to_use_their_own_conf_system_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              rules.arrange_email
                .can_custom_template_or_custom_insert_into_our_template
            "
          >
            <th :style="th_td_style">
              Custom template or custom insert into our template
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.arrange_email
                  .custom_template_or_custom_insert_into_our_template_detail
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Arrange Email -->
    <div v-if="show_calendar_invite">
      <h4 :style="h4_style">Calendar Invite</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Don't send</th>
            <td :style="th_td_style">
              <span>{{
                rules.calendar_invite.is_do_not_send ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="!rules.calendar_invite.is_do_not_send">
            <th :style="th_td_style">When to copy others (besides user)</th>
            <td :style="th_td_style">
              <span>{{ rules.calendar_invite.when_to_copy_others }}</span>
              <div
                v-if="
                  rules.calendar_invite.when_to_copy_others === 'Other rule'
                "
              >
                :{{ rules.calendar_invite.when_to_copy_others_detail }}
              </div>
              <div>
                <span
                  v-for="(item, index) in rules.calendar_invite
                    .copy_others_list"
                  :key="index"
                >
                  {{
                    `${item.name}: ${item.email} ${
                      rules.calendar_invite.copy_others_list.length - 1 ===
                      index
                        ? ""
                        : ";"
                    }`
                  }}
                </span>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Include Angle Name in Client Calendar Invite Subject</th>
            <td :style="th_td_style">
              <span v-if="!rules.calendar_invite.is_include_angle_name_in_subject">NO</span>
              <span v-else>{{ rules.calendar_invite.include_angle_name_position }}</span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Include Client Agreement (CA) Q&A in Client Compliance invites if auto-approved for ECOM</th>
            <td :style="th_td_style">
              <span>{{ rules.calendar_invite.is_include_client_agreement_in_compliance_invite_if_auto_approved ? 'YES' : 'NO' }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Post-Call Process -->
    <div v-if="show_post_call_process">
      <h4 :style="h4_style">Post-Call Process</h4>
      <table :style="table_style">
        <tbody>
          <tr v-if="rules.post_call_process.has_post_call_ca">
            <th :style="th_td_style">Post-call CA</th>
            <td :style="th_td_style">
              <span>{{ rules.post_call_process.post_call_ca_detail }}</span>
            </td>
          </tr>
          <tr v-if="rules.post_call_process.has_post_call_work_for_client">
            <th :style="th_td_style">Post-call work for client</th>
            <td :style="th_td_style">
              <div>
                content:
                <span>{{
                  rules.post_call_process
                    .post_call_work_for_client__what_content || '-'
                }}</span>
              </div>
              <div>
                copy:
                <span>{{
                  rules.post_call_process
                    .post_call_work_for_client__anyone_to_copy || '-'
                }}</span>
              </div>
              <div>
                answer:
                <span>{{
                  rules.post_call_process
                    .post_call_work_for_client__what_to_do_with_the_answer || '-'
                }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Research Reports/Surveys (Custom) -->
    <div v-if="show_research_reports_surveys">
      <h4 :style="h4_style">Research Reports/Surveys (Custom)</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Approval required for the proposal</th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_proposal ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_proposal">
            <th :style="th_td_style">Capvision compliance approval first</th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_proposal_is_capvision_compliance_approval_first
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_approval_required_for_the_proposal &&
                show_approval_required_for_the_proposal_is_who_to_ask_for_approval
            "
          >
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_proposal
                  .who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_proposal">
            <th :style="th_td_style">RM sends or Capvision compliance sends</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_proposal
                  .rm_sends_or_capvision_compliance_sends
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_approval_required_for_the_proposal &&
                show_approval_required_for_the_proposal_has_any_requirements_except_to_attach_the_proposal
            "
          >
            <th :style="th_td_style">
              Any requirements except to attach the proposal
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_proposal
                  .any_requirements_except_to_attach_the_proposal_detail
              }}</span>
            </td>
          </tr>

          <tr>
            <th :style="th_td_style">
              Approval required for the questionnaire
            </th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_questionnaire ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_questionnaire">
            <th :style="th_td_style">Capvision compliance approval first</th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_questionnaire_is_capvision_compliance_approval_first
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_approval_required_for_the_questionnaire &&
                show_approval_required_for_the_questionnaire_is_always_required_just_if_capvision_writes_it
            "
          >
            <th :style="th_td_style">
              Always required? Just if Capvision writes it
            </th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_questionnaire
                  .always_required_just_if_capvision_writes_it_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_approval_required_for_the_questionnaire &&
                show_approval_required_for_the_questionnaire_is_who_to_ask_for_approval
            "
          >
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_questionnaire
                  .who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_questionnaire">
            <th :style="th_td_style">RM sends or Capvision compliance sends</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_questionnaire
                  .rm_sends_or_capvision_compliance_sends
              }}</span>
            </td>
          </tr>

          <tr>
            <th :style="th_td_style">Approval required for the deliverable</th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_deliverable ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_deliverable">
            <th :style="th_td_style">Capvision compliance approval first</th>
            <td :style="th_td_style">
              <span>{{
                show_approval_required_for_the_deliverable_is_capvision_compliance_approval_first
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_approval_required_for_the_deliverable &&
                show_approval_required_for_the_deliverable_is_who_to_ask_for_approval
            "
          >
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_deliverable
                  .who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
          <tr v-if="show_approval_required_for_the_deliverable">
            <th :style="th_td_style">RM sends or Capvision compliance sends</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports_surveys
                  .approval_required_for_the_deliverable
                  .rm_sends_or_capvision_compliance_sends
              }}</span>
            </td>
          </tr>

          <tr
            v-if="
              show_research_reports_surveys_when_sending_to_the_user_copy_compliance_team_or_othersl
            "
          >
            <th :style="th_td_style">
              When sending to the user, copy compliance team or others
            </th>
            <td :style="th_td_style">
              <div
                v-if="
                  rules.research_reports_surveys
                    .when_sending_to_the_user_copy_compliance_team_or_others
                    .has_emails_to_copy
                "
              >
                Emails to copy:
                <span>{{
                  rules.research_reports_surveys
                    .when_sending_to_the_user_copy_compliance_team_or_others
                    .emails_to_copy_detail
                }}</span>
              </div>
              <div
                v-if="
                  rules.research_reports_surveys
                    .when_sending_to_the_user_copy_compliance_team_or_others
                    .has_internal_emails_to_copy
                "
              >
                Internal emails to copy:
                <span>{{
                  rules.research_reports_surveys
                    .when_sending_to_the_user_copy_compliance_team_or_others
                    .internal_emails_to_copy_detail
                }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Research Reports (Non-Custom) -->
    <div v-if="show_research_reports">
      <h4 :style="h4_style">Research Reports (Non-Custom)</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Language Rules</th>
            <td :style="th_td_style">
              <span>{{ rules.research_reports.language_rules }}</span>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Approval required before delivery</th>
            <td :style="th_td_style">
              <span>{{
                show_research_reports_is_approval_required_before_deliveryl
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="show_research_reports_is_approval_required_before_deliveryl"
          >
            <th :style="th_td_style">Capvision compliance approval first</th>
            <td :style="th_td_style">
              <span>{{
                show_research_reports_is_capvision_compliance_approval_first
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_research_reports_has_who_to_ask_for_approval">
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.research_reports.who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Events -->
    <div v-if="show_events">
      <h4 :style="h4_style">Events</h4>
      <table :style="table_style">
        <tbody>
          <tr>
            <th :style="th_td_style">Should they get event advertisements</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.should_they_get_event_advertisements
              }}</span>
              <div
                v-if="
                  rules.events.should_they_get_event_advertisements ===
                    'Yes with rules'
                "
              >
                <div v-if="rules.events.can_listed_companies">
                  Former period:
                  <span>{{ rules.events.can_former_period_count || 0 }}</span>
                  <span>{{ rules.events.can_former_period_type }}</span>
                </div>
                <div>
                  {{ rules.events.should_they_get_event_advertisements_rules }}
                </div>
              </div>
            </td>
          </tr>
          <tr>
            <th :style="th_td_style">Cost approvals</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.cost_approvals.master_switch ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              rules.events.cost_approvals.master_switch &&
                rules.events.cost_approvals.is_cost_approval_required
            "
          >
            <th :style="th_td_style">Cost approval required (besides user)</th>
            <td :style="th_td_style">
              {{ rules.events.cost_approvals.cost_approval_required_detail }}
            </td>
          </tr>
          <tr v-if="rules.events.cost_approvals.master_switch">
            <th :style="th_td_style">Cost approvals - Who to ask</th>
            <td :style="th_td_style">
              {{ rules.events.cost_approvals.who_to_ask }}
            </td>
          </tr>

          <tr>
            <th :style="th_td_style">Live Events</th>
            <td :style="th_td_style">
              <span>{{ show_live_events ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_live_events &&
                show_live_events_can_the_user_attend_live_events
            "
          >
            <th :style="th_td_style">Can the user attend live events</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.live_events.can_the_user_attend_live_events_type
              }}</span>
            </td>
          </tr>
          <tr v-if="show_live_events">
            <th :style="th_td_style">Do they need approval to attend live</th>
            <td :style="th_td_style">
              <span>{{
                show_live_events_do_they_need_approval_to_attend_live
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_live_events &&
                show_live_events_do_they_need_approval_to_attend_live &&
                show_live_events_has_who_to_ask_for_approval
            "
          >
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.live_events.who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_live_events &&
                show_live_events_do_they_need_approval_to_attend_live &&
                show_live_events_has_what_to_send_in_the_approval_request
            "
          >
            <th :style="th_td_style">What to send in the approval request</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.live_events
                  .what_to_send_in_the_approval_request_detail
              }}</span>
            </td>
          </tr>

          <tr>
            <th :style="th_td_style">Event transcripts</th>
            <td :style="th_td_style">
              <span>{{ show_event_transcripts ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr v-if="show_event_transcripts">
            <th :style="th_td_style">Can the user request/get transcripts</th>
            <td :style="th_td_style">
              <span>{{
                show_event_transcripts_can_the_user_request_get_transcripts
                  ? "YES"
                  : "NO"
              }}</span>
            </td>
          </tr>
          <tr v-if="show_event_transcripts">
            <th :style="th_td_style">Do they need approval</th>
            <td :style="th_td_style">
              <span>{{
                show_event_transcripts_do_they_need_approval ? "YES" : "NO"
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_event_transcripts &&
                show_event_transcripts_do_they_need_approval &&
                show_event_transcripts_has_who_to_ask_for_approval
            "
          >
            <th :style="th_td_style">Who to ask for approval</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.event_transcripts.who_to_ask_for_approval_detail
              }}</span>
            </td>
          </tr>
          <tr
            v-if="
              show_event_transcripts &&
                show_event_transcripts_do_they_need_approval &&
                show_event_transcripts_has_what_to_send_in_the_approval_request
            "
          >
            <th :style="th_td_style">What to send in the approval request</th>
            <td :style="th_td_style">
              <span>{{
                rules.events.event_transcripts
                  .what_to_send_in_the_approval_request_detail
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'EmailTemplate',
  props: {
    rules: {
      type: Object,
      default() {
        return {}
      },
    },
    clientAm: {
      type: String,
      default: '',
    },
    clientName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      th_td_style: {
        width: '50%',
        padding: '5px',
        border: '1px solid #ddd',
        textAlign: 'center',
      },
      table_style: {
        width: '100%',
        padding: '5px',
        border: '1px solid #ddd',
        borderCollapse: 'collapse',
        borderSpacing: '0px',
      },
      h4_style: {
        textAlign: 'center',
      },
    }
  },
  computed: {
    ...mapGetters([
      'name',
    ]),
    show_expert_restrictions_expert_position() {
      return this.rules.expert_restrictions.expert_position.master_switch
    },
    show_expert_restrictions_expert_position_c_leval() {
      return this.rules.expert_restrictions.expert_position.restriction_position.includes(
        'C-level',
      )
    },
    show_expert_restrictions_expert_position_c_leval_listed_companies() {
      return (
        this.rules.expert_restrictions.expert_position
          .restriction_position_C_level === 'Listed companies'
      )
    },

    show_expert_restrictions_expert_position_vp() {
      return this.rules.expert_restrictions.expert_position.restriction_position.includes(
        'VP/Director/General Manager and above',
      )
    },
    show_expert_restrictions_expert_position_vp_listed_companies() {
      return (
        this.rules.expert_restrictions.expert_position
          .restriction_position_VP_and_other === 'Listed companies'
      )
    },

    show_expert_restrictions_expert_position_finance_staff() {
      return this.rules.expert_restrictions.expert_position.restriction_position.includes(
        'Finance staff',
      )
    },
    show_expert_restrictions_expert_position_finance_staff_listed_companies() {
      return (
        this.rules.expert_restrictions.expert_position
          .restriction_position_finance === 'Listed companies'
      )
    },

    show_expert_restrictions_expert_position_audit_staff() {
      return this.rules.expert_restrictions.expert_position.restriction_position.includes(
        'Audit staff',
      )
    },
    show_expert_restrictions_expert_position_audit_staff_listed_companies() {
      return (
        this.rules.expert_restrictions.expert_position
          .restriction_position_audit === 'Listed companies'
      )
    },

    show_expert_restrictions_repeat_experts() {
      return this.rules.expert_restrictions.repeat_experts.master_switch
    },
    show_expert_restrictions_repeat_experts_has_approval_to_go_over() {
      return this.rules.expert_restrictions.repeat_experts
        .has_approval_to_go_over
    },

    show_expert_restrictions_investment_target() {
      return this.rules.expert_restrictions.investment_target.master_switch
    },
    show_expert_restrictions_investment_target_is_user_must_give_target() {
      return this.rules.expert_restrictions.investment_target
        .is_user_must_give_target
    },

    show_expert_restrictions_letter_from_employer_required() {
      return this.rules.expert_restrictions.letter_from_employer_required
        .master_switch
    },
    show_expert_restrictions_letter_from_employer_required_current_employer() {
      return this.rules.expert_restrictions.letter_from_employer_required
        .current_employer.master_switch
    },
    show_expert_restrictions_letter_from_employer_required_current_employer_can_self_consent() {
      return this.rules.expert_restrictions.letter_from_employer_required
        .current_employer.can_self_consent
    },
    show_expert_restrictions_letter_from_employer_required_former_employer() {
      return this.rules.expert_restrictions.letter_from_employer_required
        .former_employer.master_switch
    },
    show_expert_restrictions_letter_from_employer_required_former_employer_can_self_consent() {
      return this.rules.expert_restrictions.letter_from_employer_required
        .former_employer.can_self_consent
    },

    show_expert_restrictions_custom_restriction_list() {
      return this.rules.expert_restrictions.custom_restriction_list
        .master_switch
    },
    show_expert_restrictions_custom_restriction_list_where() {
      return this.rules.expert_restrictions.custom_restriction_list
        .provided_and_in_our_system
    },

    show_recommend_email_has_custom_language_need_to_include_in_the_recommend_email() {
      return this.rules.recommend_email
        .has_custom_language_need_to_include_in_the_recommend_email
    },
    show_recommend_email_is_label_experts() {
      return this.rules.recommend_email.is_label_experts
    },
    show_recommend_email_has_special_email_subject_rules() {
      return this.rules.recommend_email.has_special_email_subject_rules
    },
    show_recommend_email_special_email_subject_rules() {
      return this.rules.recommend_email.special_email_subject_rules
    },
    // show_recommend_email_is_short_form_profiles() {
    //   return this.rules.recommend_email.is_short_form_profiles
    // },

    show_pre_call_ca_usage_master_switch() {
      return this.rules.pre_call_ca_usage.master_switch
    },
    show_pre_call_has_ca_valid_once_answered() {
      return this.rules.pre_call_ca_usage.has_ca_valid_once_answered
    },
    // show_pre_call_ca_valid_once_answered() {
    //   return this.rules.pre_call_ca_usage.has_ca_valid_once_answered
    // },
    show_pre_call_has_ca_valid_follow_up() {
      return this.rules.pre_call_ca_usage.has_ca_valid_follow_up
    },

    show_approval() {
      return this.rules.approval.master_switch
    },
    show_has_capvision_approval() {
      return this.rules.approval.has_capvision_approval
    },
    show_has_calls_for_client_to_approve() {
      return this.rules.approval.has_calls_for_client_to_approve
    },
    show_has_in_person_meetings() {
      return this.rules.approval.has_in_person_meetings
    },
    show_has_include_details_on_previous_calls_by_expert() {
      return this.rules.approval
        .has_include_details_on_previous_calls_by_expert
    },
    show_has_written_content() {
      return this.rules.approval.has_written_content
    },
    show_has_calls_by_expert_with_all_clients() {
      return this.rules.approval.has_calls_by_expert_with_all_clients
    },
    show_has_calls_by_expert_with_this_client_only() {
      return this.rules.approval.has_calls_by_expert_with_this_client_only
    },
    show_is_capvision_compliance_review_first() {
      return this.rules.approval.is_capvision_compliance_review_first
    },
    show_has_ca_answers_in_the_approval_email() {
      return this.rules.approval.has_ca_answers_in_the_approval_email
    },
    show_has_internal_copy_rules() {
      return this.rules.approval.has_internal_copy_rules
    },
    show_can_send_ca_and_bio_even_if_they_do_not_require_approval() {
      return this.rules.approval
        .can_send_ca_and_bio_even_if_they_do_not_require_approval
    },

    show_call_scheduling_emails() {
      return this.rules.call_scheduling_emails.master_switch
    },

    show_arrange_email() {
      return this.rules.arrange_email.master_switch
    },

    show_transcript_notetaking_rules() {
      return this.rules.transcript_notetaking_rules.master_switch
    },
    show_calendar_invite() {
      return this.rules.calendar_invite.master_switch
    },

    show_post_call_process() {
      return this.rules.post_call_process.master_switch
    },

    show_research_reports_surveys() {
      return this.rules.research_reports_surveys.master_switch
    },
    show_approval_required_for_the_proposal() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_proposal.master_switch
    },
    show_approval_required_for_the_proposal_is_capvision_compliance_approval_first() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_proposal
        .is_capvision_compliance_approval_first
    },
    show_approval_required_for_the_proposal_is_who_to_ask_for_approval() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_proposal.is_who_to_ask_for_approval
    },
    show_approval_required_for_the_proposal_has_any_requirements_except_to_attach_the_proposal() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_proposal
        .has_any_requirements_except_to_attach_the_proposal
    },
    show_approval_required_for_the_questionnaire() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_questionnaire.master_switch
    },
    show_approval_required_for_the_questionnaire_is_capvision_compliance_approval_first() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_questionnaire
        .is_capvision_compliance_approval_first
    },
    show_approval_required_for_the_questionnaire_is_always_required_just_if_capvision_writes_it() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_questionnaire
        .is_always_required_just_if_capvision_writes_it
    },
    show_approval_required_for_the_questionnaire_is_who_to_ask_for_approval() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_questionnaire.is_who_to_ask_for_approval
    },
    show_approval_required_for_the_deliverable() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_deliverable.master_switch
    },
    show_approval_required_for_the_deliverable_is_capvision_compliance_approval_first() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_deliverable
        .is_capvision_compliance_approval_first
    },
    show_approval_required_for_the_deliverable_is_who_to_ask_for_approval() {
      return this.rules.research_reports_surveys
        .approval_required_for_the_deliverable.is_who_to_ask_for_approval
    },

    show_research_reports_surveys_when_sending_to_the_user_copy_compliance_team_or_othersl() {
      return this.rules.research_reports_surveys
        .when_sending_to_the_user_copy_compliance_team_or_others.master_switch
    },

    show_research_reports() {
      return this.rules.research_reports.master_switch
    },
    show_research_reports_is_approval_required_before_deliveryl() {
      return this.rules.research_reports.is_approval_required_before_delivery
    },
    show_research_reports_is_capvision_compliance_approval_first() {
      return this.rules.research_reports.is_capvision_compliance_approval_first
    },
    show_research_reports_has_who_to_ask_for_approval() {
      return this.rules.research_reports.has_who_to_ask_for_approval
    },

    show_events() {
      return this.rules.events.master_switch
    },
    show_live_events() {
      return this.rules.events.live_events.master_switch
    },
    show_live_events_can_the_user_attend_live_events() {
      return this.rules.events.live_events.can_the_user_attend_live_events
    },
    show_live_events_do_they_need_approval_to_attend_live() {
      return this.rules.events.live_events.do_they_need_approval_to_attend_live
    },
    show_live_events_has_who_to_ask_for_approval() {
      return this.rules.events.live_events.has_who_to_ask_for_approval
    },
    show_live_events_has_what_to_send_in_the_approval_request() {
      return this.rules.events.live_events
        .has_what_to_send_in_the_approval_request
    },

    show_event_transcripts() {
      return this.rules.events.event_transcripts.master_switch
    },
    show_event_transcripts_can_the_user_request_get_transcripts() {
      return this.rules.events.event_transcripts
        .can_the_user_request_get_transcripts
    },
    show_event_transcripts_do_they_need_approval() {
      return this.rules.events.event_transcripts.do_they_need_approval
    },
    show_event_transcripts_has_who_to_ask_for_approval() {
      return this.rules.events.event_transcripts.has_who_to_ask_for_approval
    },
    show_event_transcripts_has_what_to_send_in_the_approval_request() {
      return this.rules.events.event_transcripts
        .has_what_to_send_in_the_approval_request
    },
  },
}
</script>
