<template>
  <div>
    <div v-if="hasRule">
      <div>
        <common-rule :type-rule="client_compliance_rules" show-title />
        <communication :type-rule="client_compliance_rules" show-title />
        <language-rule :type-rule="client_compliance_rules" show-title />
        <general-rule :type-rule="client_compliance_rules" show-title />
        <email-copy :type-rule="client_compliance_rules" show-title />
        <internal-copy :type-rule="client_compliance_rules" :client-am="clientAm" show-title />
        <project-creation :type-rule="client_compliance_rules" show-title />
        <pre-call-ca :type-rule="client_compliance_rules" show-title />
        <post-call :type-rule="client_compliance_rules" show-title />
        <call-scheduling-email :type-rule="client_compliance_rules" show-title />
        <calendar-invite :type-rule="client_compliance_rules" show-title />
        <transcription-note :type-rule="client_compliance_rules" show-title />
        <expert-restriction :type-rule="client_compliance_rules" show-title />
        <recommend :type-rule="client_compliance_rules" show-title />
        <approval :type-rule="client_compliance_rules" :client-am="clientAm" show-title />
        <arrange :type-rule="client_compliance_rules" show-title />
        <survey-rules :type-rule="client_compliance_rules" show-title />
      </div>
    </div>
    <div v-if="!hasRule" class="messageBox">No Data</div>
  </div>
</template>

<script>
import Approval from '@/components/ComplianceWarning/components/Approval'
import Arrange from '@/components/ComplianceWarning/components/Arrange'
import Recommend from '@/components/ComplianceWarning/components/RecommendEmail'
import EmailCopy from '@/components/ComplianceWarning/components/EmailCopyRule'
import ProjectCreation from '@/components/ComplianceWarning/components/ProjectCreation'
import CallSchedulingEmail from '@/components/ComplianceWarning/components/CallSchedulingEmail'
import PreCallCa from '@/components/ComplianceWarning/components/PreCallCA'
import CalendarInvite from '@/components/ComplianceWarning/components/CalendarInvite'
import ExpertRestriction from '@/components/ComplianceWarning/components/ExpertRestriction'
import Communication from '@/components/ComplianceWarning/components/Communication'
import InternalCopy from '@/components/ComplianceWarning/components/InternalCopy'
import LanguageRule from '@/components/ComplianceWarning/components/Language'
import GeneralRule from '@/components/ComplianceWarning/components/GeneralRule'
import PostCall from '@/components/ComplianceWarning/components/PostCall'
import CommonRule from '@/components/ComplianceWarning/components/CommonRule'
import TranscriptionNote from '@/components/ComplianceWarning/components/TranscriptNotetaking'
import SurveyRules from '@/components/ComplianceWarning/components/Survey'

export default {
  name: 'ShowRules',
  components: {
    CommonRule,
    EmailCopy,
    Approval,
    Arrange,
    Recommend,
    ProjectCreation,
    CallSchedulingEmail,
    PreCallCa,
    CalendarInvite,
    ExpertRestriction,
    Communication,
    InternalCopy,
    LanguageRule,
    GeneralRule,
    PostCall,
    TranscriptionNote,
    SurveyRules,
  },
  props: {
    clientComplianceRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
    clientAm: String,
    hasRule: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      client_compliance_rules: {},
    }
  },
  mounted() {
    this.client_compliance_rules = this.clientComplianceRules
  },
}
</script>

<style scoped>

</style>
