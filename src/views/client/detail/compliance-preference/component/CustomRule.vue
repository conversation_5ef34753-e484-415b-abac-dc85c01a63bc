<template>
  <div>
    <div
      v-for="(item, index) in clone_list"
      :key="index"
      draggable="true"
      class="container-item"
      @dragstart="dragStart(item)"
      @dragenter="dragenter(item)"
      @dragend="dragEnd(item)">
      <div class="mb-8">
        <el-tag type="info" size="mini">{{ item.where | getLabel(options.what_section__where_to_show_the_rule) }}
        </el-tag>
        <span class="item-icon">
          <el-link :underline="false" type="primary" class="el-icon-edit ml-8 mr-8" @click="editRule(item)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteRule(index)">
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              icon="el-icon-delete" />
          </el-popconfirm>
        </span>
      </div>
      <div v-if="item.edit" class="item-edit">
        <tinymce v-model="edit_body" :toolbar="tinymce_toolbar" :height="100" class="mb-8" font-size-change />
        <el-button type="success" @click="saveRule(item)">Ok</el-button>
        <el-button type="text" @click="item.edit = false">Cancel</el-button>
      </div>
      <div v-else class="custom-rule-content" v-html="item.body" />
    </div>
    <div>
      What section/where to show the rule?
      <el-select v-model="form.where">
        <el-option
          v-for="item in options.what_section__where_to_show_the_rule"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </el-select>
      <tinymce
        ref="tinymce"
        v-model="form.body"
        class="mt-8 mb-8"
        :height="100"
        font-size-change
        :toolbar="tinymce_toolbar" />
      <el-button type="primary" :disabled="!form.body" @click="actionAddNewRule">Add</el-button>
    </div>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce/index'
import Settings from '@/settings'
import _ from 'lodash'

export default {
  name: 'CustomRule',
  components: { Tinymce },
  props: {
    list: Array,
  },
  data() {
    return {
      tinymce_toolbar: Settings.tinymce_config.toolbar_sm,
      options: {
        what_section__where_to_show_the_rule: [
          { value: 'General Rule', label: 'General Rule' },
          { value: 'expert_restrictions', label: 'Restrictions' },
          { value: 'recommend_email', label: 'Recommend' },
          { value: 'approval', label: 'Approval' },
          { value: 'call_scheduling_emails', label: 'Scheduling' },
          { value: 'arrange_email', label: 'Arrange' },
          { value: 'post_call_process', label: 'Post-call' },
        ],
      },
      form: {
        where: 'General Rule',
        body: undefined,
      },
      oldNum: 0,
      newNum: null,
      clone_list: [],
      edit_body: null,
    }
  },

  watch: {
    'list': {
      handler(newVal) {
        this.clone_list = _.clone(newVal).filter(e => {
          return e
        })
      }, immediate: true,
    },
  },

  methods: {
    actionAddNewRule() {
      this.clone_list.push(Object.assign({}, this.form))
      this.$refs.tinymce.setContent('')
      this.$emit('update:list', this.clone_list)
      this.$emit('update')
    },

    dragStart(value) {
      this.oldNum = value
    },

    dragEnd(value) {
      if (this.oldNum !== this.newNum) {
        const oldIndex = this.clone_list.indexOf(this.oldNum)
        const newIndex = this.clone_list.indexOf(this.newNum)

        this.clone_list.splice(oldIndex, 1)
        this.clone_list.splice(newIndex, 0, this.oldNum)
        this.$emit('update:list', this.clone_list)
      }
    },
    dragenter(value) {
      this.newNum = value
    },

    editRule(item) {
      this.$set(item, 'edit', true)
      this.edit_body = item.body
    },

    saveRule(item) {
      item.body = this.edit_body
      this.edit_body = null
      item.edit = false
      this.$emit('update:list', this.clone_list)
      this.$emit('update')
    },

    deleteRule(index) {
      this.clone_list.splice(index, 1)
      this.$emit('update:list', this.clone_list)
      this.$emit('update')
    },
  },
}
</script>

<style lang="scss" scoped>
.container-item {
  border: 1px solid #CCC;
  border-radius: 5px;
  margin-bottom: 0.5em;

  .item-icon {
    display: inline-block;
  }

  &:hover {
    cursor: all-scroll;

    .item-icon {
      display: inline-block;

      &:hover {
        cursor: pointer;
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  .item-edit {
    padding: 5px 10px;
  }

}

.custom-rule-content {
  text-indent: 2rem;
  margin-bottom: 1em;
}
</style>
