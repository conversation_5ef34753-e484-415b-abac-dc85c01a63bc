<template>
  <div class="components-container">
      <h3>
        <router-link-client :data="{id:$route.params.client_id,name:$route.query.client_name}" />
        <span v-if="isComplianceRule">Compliance Rules (#{{$route.params.client_id}})</span>
        <span v-if="isCA">Client Agreement (#{{$route.query.entity_id}})</span>
      </h3>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="data.list"
    >
      <el-table-column label="Timestamp" width="150px">
        <template slot-scope="scope">
          {{scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm a')}}
        </template>
      </el-table-column>
      <el-table-column label="User" width="100px">
        <template slot-scope="scope">
          {{scope.row.user.name}}
        </template>
      </el-table-column>
      <el-table-column label="Attribute Name">
        <template slot-scope="scope">
         <div class="break-word">{{scope.row.field_path}}</div>
        </template>
      </el-table-column>
      <el-table-column label="Old Value" prop="old_value">
        <template slot-scope="scope">
         <span v-if="isComplianceRule">{{scope.row.old_value}}</span>
          <show-question v-if="isCA && scope.row.old_value" :form="JSON.parse(scope.row.old_value)" />
        </template>
      </el-table-column>
      <el-table-column label="New Value" prop="new_value">
        <template slot-scope="scope">
          <span v-if="isComplianceRule"> {{scope.row.new_value}}</span>
          <show-question v-if="isCA && scope.row.new_value" :form="JSON.parse(scope.row.new_value)" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="data.count"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :auto-scroll="false"
      :page-sizes="customPageSize"
      @pagination="getHistory"
    />
</div>
</template>

<script>
import ClientAPI from '@/api/client'
import ShowQuestion from '@/views/compliance/client-agreement/components/showQuestion'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'

export default {
  name: 'EditHistory',
  components: { ShowQuestion, Pagination, RouterLinkClient },
  data() {
    return {
      loading: false,
      customPageSize: [20, 50, 100],
      data: {
        list: [],
        count: 0,
      },
      searchQuery: {
        page: 1,
        size: 20,
      },
    }
  },
  computed: {
    isComplianceRule() {
      return this.$route.query.type === 'COMPLIANCE_RULE'
    },
    isCA() {
      return this.$route.query.type === 'CA'
    },
  },
  mounted() {
    this.getHistory()
  },
  methods: {
    getHistory() {
      this.loading = true
      const params = Object.assign(this.searchQuery, {
        extra: 'user',
        client_id: this.$route.params.client_id,
        entity_type: this.$route.query.type,
        entity_id: this.$route.query.entity_id || undefined,
      })

      return ClientAPI.getComplianceChangeLog(params)
        .then(data => {
          this.data = data
        })
        .finally(() => {
          this.loading = false
        })
    },
  },

}
</script>

<style scoped>

</style>
