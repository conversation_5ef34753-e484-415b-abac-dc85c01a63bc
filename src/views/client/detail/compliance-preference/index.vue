<template>
  <div style="height:100%;">
    <div v-loading="loading" class="flex justify-content-center flex-wrap rules-box">
      <div v-if="permission" class="short-container">
        <div ref="stickyBox" class="left-fixed scrollbar-narrow">
          <el-button type="primary" class="mb-8" @click="actionShowRules()">Preview Rules</el-button>
          <div
            v-for="item in options.shortcut_options"
            :key="item.value"
            class="shortcut-box">
            <span
              :class="item.value === shortcut_active ? 'primary':''"
              class="shortcut-text"
              @click="scrollToView(item.value)">
              {{ item.label }}
            </span>
          </div>
          <div class="mt-8">
            <el-button
              type="primary"
              :loading="isSubmitting"
              icon="el-icon-check"
              :disabled="!permission"
              @click="actionSave">Save
            </el-button>
            <el-link
              type="primary"
              :disabled="!permission"
              class="mt-3"
              @click="actionGoToHistory">Update History
            </el-link>
          </div>
        </div>

      </div>
      <div v-if="isPreviewRules" class="rule-form scrollbar-narrow">
        <show-rules
          v-if="hasRule"
          :client-am="clientAM"
          :client-compliance-rules="clientComplianceRules"
          :has-rule="hasRule" />
        <div v-else>No data</div>
      </div>
      <form
        v-show="!isPreviewRules"
        id="ruleForm"
        :disabled="isSubmitting"
        :class="{'disable-form':!permission}"
        class="rule-form scrollbar-narrow"
      >
        <!-- Custom Text Box Rule -->
        <el-card id="CustomTextBoxRule" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active === 'CustomTextBoxRule'}">Custom Text Box Rule</div>
          <custom-rule :list.sync="data.rule.list" @update="actionSave" />
        </el-card>

        <!-- Communication Rules -->
        <!-- 21-05-16 美国要求不限制   待定-->
        <el-card id="CommunicationRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active === 'CommunicationRules'}">Communication Rules</div>
          <div>
            <el-checkbox
              v-model="data.communication_rules.email_only"
              :disabled="data.communication_rules.phone_allowed || data.communication_rules.we_chat_allowed"
              class="mb-8">
              Email Only?
            </el-checkbox>
          </div>
          <div>
            <el-checkbox
              v-model="data.communication_rules.phone_allowed"
              :disabled="data.communication_rules.email_only || data.communication_rules.we_chat_allowed"
              class="mb-8">
              Phone allowed?
            </el-checkbox>
          </div>
          <div>
            <el-checkbox
              v-model="data.communication_rules.we_chat_allowed"
              :disabled="!data.communication_rules.phone_allowed"
              @change="data.communication_rules.phone_allowed = true">
              Messaging Platforms Allowed (such as WeChat, WhatsApp, Slack)
            </el-checkbox>
          </div>
        </el-card>

        <!-- Language Rules -->
        <el-card id="LanguageRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='LanguageRules'}">Language Rules</div>
          <div>
            <div>
              <el-checkbox v-model="data.language_rules.compliance_team_language.master_switch" class="mb-8">
                Compliance team language
              </el-checkbox>
              <div v-if="data.language_rules.compliance_team_language.master_switch" class="ml-2e">
                <div class="mb-8">
                  <div class="mb-8">
                    Email text
                  </div>
                  <el-radio-group v-model="data.language_rules.compliance_team_language.email_text">
                    <el-radio
                      v-for="item in options.language_rules"
                      :key="item.value"
                      :label="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <div class="mb-8">
                  <div class="mb-8">
                    Expert bios/CA answers, etc.
                  </div>
                  <el-radio-group v-model="data.language_rules.compliance_team_language.expert_bios_ca_answers_etc">
                    <el-radio
                      v-for="item in options.language_rules"
                      :key="item.value"
                      :label="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.language_rules.communication_with_users_analysts.master_switch" class="mb-8">
                Communication with users/analysts (as a compliance requirement, not about the user's preference)
              </el-checkbox>
              <div v-if="data.language_rules.communication_with_users_analysts.master_switch" class="ml-2e">
                <div class="mb-8">
                  <div class="mb-8">
                    Email text
                  </div>
                  <el-radio-group v-model="data.language_rules.communication_with_users_analysts.email_text">
                    <el-radio
                      v-for="item in options.language_rules"
                      :key="item.value"
                      :label="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <div>
                  <div class="mb-8">
                    Expert bios/CA answers, etc.
                  </div>
                  <el-radio-group
                    v-model="data.language_rules.communication_with_users_analysts.expert_bios_ca_answers_etc">
                    <el-radio
                      v-for="item in options.language_rules"
                      :key="item.value"
                      :label="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Email Copying Rules -->
        <el-card id="EmailCopyingRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='EmailCopyingRules'}">External Email Copy Rules
          </div>
          <div>
            <div class="mb-8">
              <el-checkbox v-model="data.email_copying_rules.all_emails.master_switch">All Emails</el-checkbox>
              <div v-if="data.email_copying_rules.all_emails.master_switch" class="ml-2e mt-8">
                <name-email :list="data.email_copying_rules.all_emails.list" />
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox v-model="data.email_copying_rules.recommending_experts.master_switch">Recommending Experts
              </el-checkbox>
              <div v-if="data.email_copying_rules.recommending_experts.master_switch" class="ml-2e mt-8">
                <name-email :list="data.email_copying_rules.recommending_experts.list" />
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.email_copying_rules.negotiating_call_schedule.master_switch">
                Negotiating Call Schedule
              </el-checkbox>
              <div v-if="data.email_copying_rules.negotiating_call_schedule.master_switch" class="ml-2e mt-8">
                <name-email :list="data.email_copying_rules.negotiating_call_schedule.list" />
              </div>
            </div>
          </div>
        </el-card>

        <!-- Internal Email Copy Rules -->
        <el-card id="InternalEmailCopyRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='InternalEmailCopyRules'}">Internal Email Copy
            Rules
          </div>
          <div>
            <el-checkbox v-model="data.internal_email_copy_rules.all_emails">All Emails</el-checkbox>
            <div v-if="data.internal_email_copy_rules.all_emails" class="ml-2e mt-8">
              <el-checkbox v-model="data.internal_email_copy_rules.am">AM</el-checkbox>
              <el-checkbox v-model="data.internal_email_copy_rules.compliance_team">Compliance Team</el-checkbox>
            </div>
          </div>
        </el-card>

        <!-- Project creation rules -->
        <el-card id="ProjectCreationRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='ProjectCreationRules'}">Project Creation Rules
          </div>
          <div>
            <div class="mb-8">
              <el-checkbox v-model="data.project_creation_rules.who_can_start_a_project.master_switch">
                Who can start a project?
              </el-checkbox>
              <div v-if="data.project_creation_rules.who_can_start_a_project.master_switch" class="ml-2e mt-8">
                <div>
                  <el-radio-group v-model="data.project_creation_rules.who_can_start_a_project.users_type">
                    <el-radio
                      v-for="item in options.who_can_start_a_project__user_type"
                      :key="item.value"
                      :label="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <name-email
                  v-if="['Any User - special CC rules', 'Special persons only'].includes(data.project_creation_rules.who_can_start_a_project.users_type)"
                  :list="data.project_creation_rules.who_can_start_a_project.special_user_detail"
                  class="mt-8" />
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox v-model="data.project_creation_rules.is_any_project_code_or_other_similar">
                Any project code or other similar?
              </el-checkbox>
              <div v-if="data.project_creation_rules.is_any_project_code_or_other_similar" class="ml-2e">
                <el-input
                  v-model="data.project_creation_rules.any_project_code_or_other_similar_detail"
                  type="textarea"
                  class="mt-8"
                  placeholder="What to call this code to match client's terminology" />
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox v-model="data.project_creation_rules.will_projects_have_a_budget">
                Will projects have a budget?
              </el-checkbox>
              <div v-if="data.project_creation_rules.will_projects_have_a_budget" class="ml-2e">
                <el-input
                  v-model="data.project_creation_rules.projects_have_a_budget_detail"
                  type="textarea"
                  class="mt-8"
                  placeholder="If so, how to extend?" />
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox
                v-model="data.project_creation_rules.do_projects_auto_expire__close_after_a_certain_amount_of_time">
                Do projects auto-expire/close after a certain amount of time?
              </el-checkbox>
              <div
                v-if="data.project_creation_rules.do_projects_auto_expire__close_after_a_certain_amount_of_time"
                class="ml-2e">
                <el-input
                  v-model="data.project_creation_rules.projects_auto_expire__close_after_a_certain_amount_of_time_detail"
                  type="textarea"
                  class="mt-8"
                  placeholder="If so, how long?" />
              </div>
            </div>
            <div>
              <el-checkbox
                v-model="data.project_creation_rules.remind_user_to_add_a_project_case_code">
                Remind user to add a project/case code?
              </el-checkbox>
              <div
                v-if="data.project_creation_rules.remind_user_to_add_a_project_case_code"
                class="ml-2e">
                <el-checkbox-group
                  v-model="data.project_creation_rules.remind_user_to_add_a_project_case_code_project_types">
                  <el-checkbox label="Survey">Surveys</el-checkbox>
                  <el-checkbox label="Consultation">Consultations</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Expert Restrictions -->
        <el-card id="ExpertRestrictions" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='ExpertRestrictions'}">Expert Restrictions</div>
          <div>
            <!-- Listed company -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.listed_company.master_switch">Listed company</el-checkbox>
              <div v-if="data.expert_restrictions.listed_company.master_switch" class="ml-2e">
                <div class="mb-8 mt-8">
                  Allowed?
                  <el-select v-model="data.expert_restrictions.listed_company.listed_allowed">
                    <el-option
                      v-for="item in options.is_allowed"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
                <div v-if="data.expert_restrictions.listed_company.listed_allowed !== 'ALLOWS'" class="ml-3">
                  <div>
                    <el-checkbox
                      v-model="data.expert_restrictions.listed_company.has_geographic_restrictions"
                      class="mb-8">
                      Geographic restrictions?
                    </el-checkbox>
                    <div v-if="data.expert_restrictions.listed_company.has_geographic_restrictions" class="ml-2e mb-8">
                      <el-input
                        v-model="data.expert_restrictions.listed_company.geographic_restrictions_detail"
                        type="textarea" />
                    </div>
                  </div>

                  <div>
                    <el-checkbox
                      v-model="data.expert_restrictions.listed_company.has_former_employee_window"
                      class="mb-8">
                      Former employee window
                    </el-checkbox>
                    <div v-if="data.expert_restrictions.listed_company.has_former_employee_window" class="ml-2e">
                      <el-input
                        v-model="data.expert_restrictions.listed_company.former_employee_window_detail"
                        type="number"
                        class="mb-8 custom-former-employee-input">
                        <template slot="append">months</template>
                      </el-input>
                    </div>
                  </div>
                  <div class="mb-8">
                    <el-checkbox
                      v-model="data.expert_restrictions.listed_company.has_ipos">
                      IPOs (window to consider listed)
                    </el-checkbox>
                    <div
                      v-if="data.expert_restrictions.listed_company.has_ipos"
                      class="ml-2e">
                      <el-input-number v-model.number="data.expert_restrictions.listed_company.ipos_detail" />
                      months
                    </div>
                  </div>
                  <div class="mb-8">
                    Definition of a "listed company"
                    <div class="ml-2e mt-8">
                      <el-checkbox-group v-model="data.expert_restrictions.listed_company.definition">
                        <el-checkbox
                          v-for="item in options.definition_listed_company"
                          :key="item.value"
                          :value="item.value"
                          :label="item.label"
                        />
                      </el-checkbox-group>
                    </div>
                  </div>
                  <div>
                    <el-checkbox v-model="data.expert_restrictions.listed_company.can_consultant_to_a_listed_company">
                      Consultant to a listed company
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </div>

            <!-- Government affiliated employees -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.government_affiliated_employees.master_switch">
                Government affiliated employees
              </el-checkbox>
              <div v-if="data.expert_restrictions.government_affiliated_employees.master_switch" class="ml-3 mt-8">
                <div class="ml-3">
                  <div>
                    Allowed?
                    <el-select
                      v-model="data.expert_restrictions.government_affiliated_employees.allowed"
                      class="mb-8">
                      <el-option
                        v-for="item in options.is_allowed"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label" />
                    </el-select>
                  </div>
                  <!--                  v-if="data.expert_restrictions.government_affiliated_employees.allowed !== 'ALLOWS'"-->
                  <div
                    class="ml-3">
                    <div>
                      <el-checkbox
                        v-model="data.expert_restrictions.government_affiliated_employees.has_former_employee_window">
                        Former employee window
                      </el-checkbox>
                      <div v-if="data.expert_restrictions.government_affiliated_employees.has_former_employee_window">
                        <el-input
                          v-model="data.expert_restrictions.government_affiliated_employees.former_employee_window_detail"
                          type="number"
                          class="mb-8 custom-former-employee-input">
                          <template slot="append">months</template>
                        </el-input>
                      </div>
                    </div>
                    <!-- SOE -->
                    <expert-restrictions-granular-item
                      show-name="SOE"
                      :data="data.expert_restrictions.government_affiliated_employees.soe"
                    />

                    <!-- Public Hospital -->
                    <expert-restrictions-granular-item
                      show-name="Public Hospital"
                      :data="data.expert_restrictions.government_affiliated_employees.public_hospital"
                    />

                    <!-- Public University -->
                    <expert-restrictions-granular-item
                      show-name="Public University"
                      :data="data.expert_restrictions.government_affiliated_employees.public_university"
                    />

                    <!-- Other (research institutes, think tanks, industry associations, etc.) -->
                    <expert-restrictions-granular-item
                      show-name="Other (research institutes, think tanks, industry associations, etc.)"
                      :data="data.expert_restrictions.government_affiliated_employees.other" />
                  </div>
                </div>
              </div>
            </div>

            <!-- Franchisee/Distributor/Supplier -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.franchisee_distributor_supplier.master_switch">
                Franchisee/Distributor/Supplier
              </el-checkbox>
              <div
                v-if="data.expert_restrictions.franchisee_distributor_supplier.master_switch"
                class="ml-2e mt-8 ">
                Relevance Threshold (percentage)
                <el-input
                  v-model="data.expert_restrictions.franchisee_distributor_supplier.relevance_threshold_percentage_detail"
                  type="number"
                  class="w-200px ml-3"
                  placeholder="percentage">
                  <template slot="append"> %</template>
                </el-input>

              </div>
            </div>

            <!-- Healthcare Experts/Clinical Trials -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.healthcare_experts_clinical_trials.master_switch">
                Healthcare Experts/Clinical Trials
              </el-checkbox>
              <div
                v-if="data.expert_restrictions.healthcare_experts_clinical_trials.master_switch"
                class="ml-2e mt-8">
                <div class="mb-8">
                  Clinical Trials
                  <el-select
                    v-model="data.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials"
                    :disabled="clinical_trials_disabled">
                    <el-option
                      v-for="item in options.clinical_trials"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value" />
                  </el-select>
                  Restriction{{
                    data.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials_restriction
                  }}
                  <el-select
                    v-model="data.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials_restriction"
                    @change="checkHealthcareExpertsClinicalTrials()">
                    <el-option
                      v-for="item in options.clinical_trials_restriction"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value" />
                  </el-select>
                </div>
                If you select Representative Disable :
                <el-checkbox v-model="data.expert_restrictions.healthcare_experts_clinical_trials.can_doctors">
                  Doctors
                </el-checkbox>
                <el-checkbox
                  v-model="data.expert_restrictions.healthcare_experts_clinical_trials.can_other_medical_professionals">
                  Other medical professionals
                </el-checkbox>
                <el-checkbox
                  v-model="data.expert_restrictions.healthcare_experts_clinical_trials.can_medical_tech_services_pharma_etc">
                  Medical tech/services, Pharma, etc. (industry)
                </el-checkbox>

              </div>
            </div>

            <!-- Expert position -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.expert_position.master_switch">
                Expert position
              </el-checkbox>
              <div v-if="data.expert_restrictions.expert_position.master_switch" class="ml-2e mt-8">
                <div>
                  <el-checkbox
                    v-model="data.expert_restrictions.expert_position.no_restriction">
                    No Restriction
                  </el-checkbox>
                </div>
                Restriction :
                <el-checkbox-group
                  v-model="data.expert_restrictions.expert_position.restriction_position"
                  :disabled="data.expert_restrictions.expert_position.no_restriction"
                  size="medium">
                  <el-checkbox
                    v-for="item in options.restriction_position"
                    :key="item.value"
                    :label="item.label"
                    class="ml-2e mt-8 custom-check-position"
                    style="display: block">
                    {{ item.label }}
                    <div
                      v-show="data.expert_restrictions.expert_position.restriction_position.includes(item.value)"
                      class="ml-2e  mt-8">
                      <el-select
                        v-model="data.expert_restrictions.expert_position['restriction_position_'+item.params_value] "
                        class="ml-3">
                        <el-option
                          v-for="company_type in options.restriction_position_company_listed"
                          :key="company_type.value"
                          :label="company_type.name"
                          :value="company_type.value" />
                      </el-select>
                      <div
                        v-show="data.expert_restrictions.expert_position['restriction_position_'+item.params_value] === 'Listed companies'"
                        class="inline-block">
                        <el-select
                          v-model="data.expert_restrictions.expert_position['restriction_position_'+item.params_value+'_listed']">
                          <el-option
                            v-for="listed_condition in options.restriction_position_listed_condition "
                            :key="listed_condition.value"
                            :label="listed_condition.name"
                            :value="listed_condition.value" />
                        </el-select>
                        <div
                          v-show="data.expert_restrictions.expert_position['restriction_position_'+item.params_value+'_listed'] === 'Former employee window'"
                          class="inline-block">
                          <el-input
                            v-model="data.expert_restrictions.expert_position['restriction_position_'+item.params_value+'_listed_empty']"
                            type="number"
                            class="custom-former-employee-input">
                            <template slot="append">months</template>
                          </el-input>
                        </div>
                      </div>

                    </div>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>

            <!-- Repeat Experts -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.repeat_experts.master_switch">Repeat Experts
              </el-checkbox>
              <div v-if="data.expert_restrictions.repeat_experts.master_switch" class="ml-2e">
                <div class="mb-8">
                  Firm wide or per user
                  <el-select v-model="data.expert_restrictions.repeat_experts.firm_wide_or_per_user">
                    <el-option
                      v-for="item in options.firm_wide_or_per_user"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
                <div class="mb-8">
                  Restriction call number and time period :
                  <el-input
                    v-model="data.expert_restrictions.repeat_experts.restriction_call_number_and_time_period.call_count"
                    class="width-auto" />
                  calls in the last
                  <el-input
                    v-model="data.expert_restrictions.repeat_experts.restriction_call_number_and_time_period.time"
                    class="width-auto" />
                  <el-select
                    v-model="data.expert_restrictions.repeat_experts.restriction_call_number_and_time_period.time_type">
                    <el-option
                      v-for="item in options.time_type"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
                <div>
                  <el-checkbox v-model="data.expert_restrictions.repeat_experts.has_approval_to_go_over">
                    Approval to go over
                  </el-checkbox>
                  <el-select
                    v-if="data.expert_restrictions.repeat_experts.has_approval_to_go_over"
                    v-model="data.expert_restrictions.repeat_experts.approval_to_go_over_type"
                    class="ml-3">
                    <el-option
                      v-for="item in options.approval_to_go_over"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                  <div v-if="data.expert_restrictions.repeat_experts.has_approval_to_go_over" class="mt-8">
                    <el-input
                      v-if="data.expert_restrictions.repeat_experts.approval_to_go_over_type === 'Ask for approval'"
                      v-model="data.expert_restrictions.repeat_experts.approval_to_go_over_detail"
                      type="textarea" />
                  </div>
                </div>
              </div>
            </div>

            <!-- Investment Target -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.investment_target.master_switch">
                Investment Target
              </el-checkbox>
              <div v-if="data.expert_restrictions.investment_target.master_switch" class="ml-2e mt-8">
                <div>
                  <el-checkbox
                    v-model="data.expert_restrictions.investment_target.is_user_must_give_target"
                    class="mb-8">
                    User must give target
                  </el-checkbox>
                </div>
                <div>
                  <el-checkbox
                    v-model="data.expert_restrictions.investment_target.show_investment_target_to_expert"
                    class="mb-8">
                    Show investment target to Expert
                  </el-checkbox>
                </div>
                <div>
                  <el-checkbox v-model="data.expert_restrictions.investment_target.is_banned" class="mb-8">Banned
                  </el-checkbox>
                  <div v-if="data.expert_restrictions.investment_target.is_banned" class="ml-2e mb-8">
                    <el-input
                      v-model="data.expert_restrictions.investment_target.is_banned_detail"
                      type="textarea" />
                  </div>
                </div>
                <div>
                  <el-checkbox v-model="data.expert_restrictions.investment_target.has_former_employee_window">
                    Former employee window
                  </el-checkbox>
                  <div v-if="data.expert_restrictions.investment_target.has_former_employee_window" class="ml-2e">
                    <el-input
                      v-model="data.expert_restrictions.investment_target.former_employee_window_detail"
                      type="number"
                      class="mb-8 custom-former-employee-input">
                      <template slot="append">months</template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>

            <!-- Letter from employer required -->
            <div class="mb-8">
              <el-checkbox v-model="data.expert_restrictions.letter_from_employer_required.master_switch">
                Letter from employer required
              </el-checkbox>
              <div v-if="data.expert_restrictions.letter_from_employer_required.master_switch" class="ml-2e mt-8">
                <!-- Current employer -->
                <div class="mb-8">
                  <el-checkbox
                    v-model="data.expert_restrictions.letter_from_employer_required.current_employer.master_switch">
                    Current employer
                  </el-checkbox>
                  <div
                    v-if="data.expert_restrictions.letter_from_employer_required.current_employer.master_switch"
                    class="ml-2e">
                    <div class="mb-8 mt-8">
                      Conditions:
                      <el-select
                        v-model="data.expert_restrictions.letter_from_employer_required.current_employer.conditions">
                        <el-option
                          v-for="item in options.current_employee_condition"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </div>
                    <div>
                      <el-checkbox
                        v-model="data.expert_restrictions.letter_from_employer_required.current_employer.can_self_consent"
                        class="mb-8">
                        Can self consent?
                      </el-checkbox>
                      <div
                        v-if="data.expert_restrictions.letter_from_employer_required.current_employer.can_self_consent"
                        class="ml-2e">
                        Above what level?
                        <div class="mb-8 ml-3 mt-8">
                          <span class="hint">C Suite only? VP/Director/General Manager and above?</span>
                          <!--                      <el-radio-group-->
                          <!--                        v-model="data.expert_restrictions.letter_from_employer_required.current_employer.above_what_level"-->
                          <!--                        class="mt-8">-->
                          <!--                        <el-radio-->
                          <!--                          v-for="item in options.employer_self_consent_level"-->
                          <!--                          :key="item.value"-->
                          <!--                          :label="item.value">-->
                          <!--                          {{item.label}}-->
                          <!--                        </el-radio>-->
                          <!--                      </el-radio-group>-->
                          <el-input
                            v-model="data.expert_restrictions.letter_from_employer_required.current_employer.above_what_level_detail"
                            type="textarea" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Former employer -->
                <div>
                  <el-checkbox
                    v-model="data.expert_restrictions.letter_from_employer_required.former_employer.master_switch">
                    Former employer
                  </el-checkbox>
                  <div
                    v-if="data.expert_restrictions.letter_from_employer_required.former_employer.master_switch"
                    class="ml-2e">
                    <div class="mb-8 mt-8">
                      Left in last
                      <el-input-number
                        v-model="data.expert_restrictions.letter_from_employer_required.former_employer.left_in_last_month"
                        controls-position="right" />
                      months
                    </div>
                    <div>
                      <el-checkbox
                        v-model="data.expert_restrictions.letter_from_employer_required.former_employer.can_self_consent"
                        class="mb-8">
                        Can self consent?
                      </el-checkbox>
                      <div
                        v-if="data.expert_restrictions.letter_from_employer_required.former_employer.can_self_consent"
                        class="ml-2e">
                        Above what level?
                        <div class="mb-8 ml-3 mt-8">
                          <span class="hint">C Suite only? VP/Director/General Manager and above?</span>
                          <!--                      <el-radio-group-->
                          <!--                        v-model="data.expert_restrictions.letter_from_employer_required.former_employer.above_what_level"-->
                          <!--                        class="mb-8">-->
                          <!--                        <el-radio-->
                          <!--                          v-for="item in options.employer_self_consent_level"-->
                          <!--                          :key="item.value"-->
                          <!--                          :label="item.value">-->
                          <!--                          {{item.label}}-->
                          <!--                        </el-radio>-->
                          <!--                      </el-radio-group>-->
                          <el-input
                            v-model="data.expert_restrictions.letter_from_employer_required.former_employer.above_what_level_detail"
                            type="textarea" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Custom restriction list (portfolio companies, etc.)? -->
            <div>
              <el-checkbox v-model="data.expert_restrictions.custom_restriction_list.master_switch">
                Custom restriction list (portfolio companies, etc.)?
              </el-checkbox>
              <div v-if="data.expert_restrictions.custom_restriction_list.master_switch" class="ml-2e">
                <div class="mb-8 mt-8">
                  Where is the list?(This client does not allow calls with employees of certain companies. This list
                  is
                  available at:)
                  <div class="ml-2e">
                    <div class="mb-8 mt-8">
                      <span class="hint">Provided and in our system? Online (link)?</span>
                      <el-input
                        v-model="data.expert_restrictions.custom_restriction_list.provided_and_in_our_system"
                        type="textarea" />
                    </div>
                    <!--                <div>-->
                    <!--                  Online (link)?-->
                    <!--                  <el-input-->
                    <!--                    v-model="data.expert_restrictions.custom_restriction_list.online_link"-->
                    <!--                    type="textarea" />-->
                    <!--                </div>-->
                  </div>
                </div>
                <div>
                  Current employees or formers too?
                  <div>
                    <el-select
                      v-model="data.expert_restrictions.custom_restriction_list.current_employees_or_formers_too"
                      class="mt-8 ml-2e">
                      <el-option
                        v-for="item in options.current_employees_or_formers_too"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label" />
                    </el-select>
                  </div>
                  <div
                    v-if="data.expert_restrictions.custom_restriction_list.current_employees_or_formers_too === 'Current and Formers'"
                    class="ml-2e mt-8">
                    Period to include formers?
                    <el-input-number
                      v-model="data.expert_restrictions.custom_restriction_list.period_to_include_formers"
                      class="ml-3" />&nbsp;month(s)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Recommend email -->
        <el-card id="RecommendEmail" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='RecommendEmail'}">Recommend Email</div>
          <div class="mb-8">
            <el-checkbox
              v-model="data.recommend_email.has_custom_language_need_to_include_in_the_recommend_email">
              Custom language need to include in the recommend email?
            </el-checkbox>
            <div
              v-if="data.recommend_email.has_custom_language_need_to_include_in_the_recommend_email"
              class="ml-2e mt-8">
              You must include the following in your recommend email:
              <el-input
                v-model="data.recommend_email.custom_language_need_to_include_in_the_recommend_email"
                type="textarea" />
            </div>
          </div>
          <div class="mb-8">
            <el-checkbox v-model="data.recommend_email.is_label_experts">
              Label experts (expert employers)
            </el-checkbox>
          </div>
          <div>
            <el-checkbox v-model="data.recommend_email.has_special_email_subject_rules" class="mb-8">
              Special Email Subject rules?
            </el-checkbox>
            <div v-if="data.recommend_email.has_special_email_subject_rules" class="ml-2e">
              Your recommend email must use the following format in the email subject:
              <el-input v-model="data.recommend_email.special_email_subject_rules" type="textarea" />
            </div>
          </div>
          <!--          <div class="mb-8">-->
          <!--            <el-checkbox v-model="data.recommend_email.is_short_form_profiles">-->
          <!--              Short Form Profiles-->
          <!--            </el-checkbox>-->
          <!--          </div>-->
        </el-card>

        <!-- (Pre-call) CA Usage/Expiration Rules -->
        <el-card id="PreCall" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='PreCall'}" class="card-title">
            <el-checkbox v-model="data.pre_call_ca_usage.master_switch">(Pre-call) CA Usage/Expiration Rules
            </el-checkbox>
          </div>
          <div v-if="data.pre_call_ca_usage.master_switch">

            <!-- How long is the CA valid for once answered (until first call)? -->
            <div class="mb-8">
              <el-checkbox v-model="data.pre_call_ca_usage.has_ca_valid_once_answered">
                How long is the CA valid for once answered (until first call)?
              </el-checkbox>
              <div v-if="data.pre_call_ca_usage.has_ca_valid_once_answered" class="ml-2e mt-8">
                <el-select v-model="data.pre_call_ca_usage.ca_valid_once_answered">
                  <el-option
                    v-for="item in options.ca_valid_once_answered"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>

                <div v-if="data.pre_call_ca_usage.ca_valid_once_answered === 'Time period'" class="ml-2e mt-8 mb-8">
                  Time period
                  <el-input-number v-model="data.pre_call_ca_usage.ca_valid_once_answered_time_period_count" />
                  <el-select v-model="data.pre_call_ca_usage.ca_valid_once_answered_time_period_type">
                    <el-option
                      v-for="item in options.time_period"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
              </div>
            </div>

            <!-- How long is the CA valid for if there is a follow up call? -->
            <div>
              <el-checkbox v-model="data.pre_call_ca_usage.has_ca_valid_follow_up" class="mb-8">
                How long is the CA valid for if there is a follow up call?
              </el-checkbox>
              <div v-if="data.pre_call_ca_usage.has_ca_valid_follow_up" class="ml-2e">
                <el-select v-model="data.pre_call_ca_usage.ca_valid_follow_up">
                  <el-option
                    v-for="item in options.ca_usage_expiration_rules"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
              </div>
              <div v-if="data.pre_call_ca_usage.ca_valid_follow_up === 'Time period'" class="ml-2e mt-8">
                Time period
                <el-input-number v-model="data.pre_call_ca_usage.ca_valid_follow_up_time_period_count" />
                <el-select v-model="data.pre_call_ca_usage.ca_valid_follow_up_time_period_type">
                  <el-option
                    v-for="item in options.time_period"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Approval -->
        <el-card id="Approval" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='Approval'}" class="card-title">
            <el-checkbox v-model="data.approval.master_switch">Approval</el-checkbox>
          </div>
          <div v-show="data.approval.master_switch">
            <div>
              who approves
              <el-select v-model="approval_object" class="mb-8 w-200px">
                <el-option value="CAPVISION_ONLY" label="Capvision Only" />
                <el-option value="CAPVISION_CLIENT" label="Both Capvision and Client" />
              </el-select>
            </div>
            <div v-show="approval_object==='CAPVISION_CLIENT'" id="approval-common">
              <div>
                <div class="mb-8">
                  Approval method
                </div>
                <div>
                  <el-select v-model="data.approval.approval_method" class="mb-8">
                    <el-option
                      v-for="item in options.approval_method"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
                <div v-if="data.approval.approval_method === 'Email'" class="ml-2e">
                  <div>
                    <div class="mb-8">
                      Who send?
                      <el-select v-model="data.approval.who_send" class="ml-8 w-200px">
                        <el-option
                          v-for="item in options.approval_email_who_send"
                          :key="item.value"
                          :value="item.value"
                          :label="item.label" />
                      </el-select>
                    </div>
                  </div>
                  <div>
                    <div class="mb-8">
                      Does client have Portal access?
                    </div>
                    <div>
                      <el-checkbox v-model="data.approval.is_client_have_portal_access" class="mb-8">Portal Access
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <!--  这里关系到后端取邮件模板的数据，不能随意改动数据结构-->
                Names and Emails to send approval to
                <div class="ml-2e mt-8 mb-8">
                  <name-email :list="data.approval.names_and_emails_to_send_approval_to" class="mb-8" />
                </div>
              </div>
              <div>
                Names and Emails of those that can approve
                <el-select v-model="data.approval.who_can_approval" class="mb-8 w-200px">
                  <el-option
                    v-for="item in options.who_can_approval"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
                <div v-if="data.approval.who_can_approval === 'Only certain people'" class="ml-2e">
                  <name-email :list="data.approval.names_and_emails_to_approval" class="mb-8" />
                </div>
              </div>
              <div class="mb-8">
                <el-checkbox v-model="data.approval.has_capvision_approval_after_client">
                  Client approval after Capvision approval
                </el-checkbox>
              </div>
              <div class="mb-8">
                <el-checkbox v-model="data.approval.has_capvision_approval" :disabled="capvision_approval_disabled">
                  Capvision Approval
                </el-checkbox>
              </div>
              <div>
                <el-checkbox
                  v-model="data.approval.has_calls_for_client_to_approve"
                  :disabled="client_approval_disabled"
                  class="mb-8">
                  Calls for Client to Approve
                </el-checkbox>
                <div v-if="data.approval.has_calls_for_client_to_approve" class="ml-2e">
                  <el-select
                    v-model="data.approval.calls_for_client_to_approve_type"
                    :disabled="client_approval_disabled"
                    class="mb-8 w-200px">
                    <el-option
                      v-for="item in options.calls_for_client_to_approve_type"
                      :key="item.value"
                      :disabled="item.value === 'Internal to Client' && data.approval.approval_method === 'Portal'"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                  <div v-if="data.approval.calls_for_client_to_approve_type === 'Internal to Client'">
                    <el-input v-model="data.approval.internal_to_client_detail" type="textarea" class="mb-8" />
                  </div>
                  <div v-if="data.approval.calls_for_client_to_approve_type === 'Conditional'" class="inline-block">
                    <el-select v-model="data.approval.client_conditional_approve_type" class="mb-8 w-200px">
                      <el-option
                        v-for="item in options.client_conditional_approve_type"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label" />
                    </el-select>
                  </div>
                  <el-input-number
                    v-if="data.approval.client_conditional_approve_type === 'Listed company experts'"
                    v-model="data.approval.client_conditional_approve_type_former_employee_window_detail"
                    class="mb-8 custom-former-employee-input">
                    <template slot="append">months</template>
                  </el-input-number>
                  <el-input
                    v-if="data.approval.client_conditional_approve_type === 'Other' && data.approval.calls_for_client_to_approve_type === 'Conditional'"
                    v-model="data.approval.client_conditional_approve_others_detail"
                    class="mb-8"
                    placeholder="detail"
                    type="textarea" />
                  <el-input
                    v-if="data.approval.client_conditional_approve_type === 'Based on CA' && data.approval.calls_for_client_to_approve_type === 'Conditional'"
                    v-model="data.approval.client_conditional_approve_based_on_ca_detail"
                    placeholder="detail"
                    class="mb-8"
                    type="textarea" />
                </div>
              </div>

              <el-checkbox v-model="data.approval.has_in_person_meetings" class="mb-8">In-person meetings</el-checkbox>
              <div v-if="data.approval.has_in_person_meetings" class="ml-2e">
                <el-select v-model="data.approval.in_person_meetings_type" class="mb-8 width-auto">
                  <el-option
                    v-for="item in options.in_person_meetings_type"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
                <!--          <div v-if="data.approval.in_person_meetings_type === 'Special approval rules'" class="mb-8">-->
                <!--            there are special approval rules:-->
                <!--            <el-input-->
                <!--              v-model="data.approval.in_person_meetings_type_detail"-->
                <!--              type="textarea" />-->
                <!--          </div>-->

                <div
                  v-if="data.approval.in_person_meetings_type === 'Requires approval regardless of call approval rules'">
                  <name-email
                    :list="data.approval.in_person_meetings_approval_list"
                    class="mb-8 ml-2e" />
                </div>
              </div>
              <div>
                <div>
                  <el-checkbox v-model="data.approval.has_include_details_on_previous_calls_by_expert" class="mb-8">
                    Include details on previous calls by expert
                  </el-checkbox>
                </div>
                <div v-if="data.approval.has_include_details_on_previous_calls_by_expert" class="ml-2e">
                  <div class="mb-8">
                    <el-checkbox v-model="data.approval.has_calls_by_expert_with_all_clients">
                      Calls by expert with all clients?
                    </el-checkbox>
                  </div>
                  <div v-if="data.approval.has_calls_by_expert_with_all_clients" class="ml-2e">
                    <div class="mb-8">
                      Relevant Time Period : last
                      <el-input-number
                        v-model="data.approval.all_clients_relevant_time_period_month"
                        class="ml-8 mr-8" />
                      months
                    </div>
                  </div>
                  <div>
                    <el-checkbox v-model="data.approval.has_calls_by_expert_with_this_client_only" class="mb-8">
                      Calls by expert with this client only?
                    </el-checkbox>
                  </div>
                  <div v-if="data.approval.has_calls_by_expert_with_this_client_only" class="ml-2e">
                    <div class="mb-8">
                      Relevant Time Period : last
                      <el-input-number
                        v-model="data.approval.client_only_relevant_time_period_month"
                        class="ml-8 mr-8" />
                      months
                    </div>
                  </div>
                </div>
              </div>
              <div class="mb-8">
                <el-checkbox v-model="data.approval.has_written_content">Written content (report, web link,
                  anything) expert wants to send user after a call
                </el-checkbox>
                <div v-if="data.approval.has_written_content" class="ml-2e mt-8">
                  <div>
                    <el-checkbox v-model="data.approval.is_capvision_compliance_review_first" class="mb-8">
                      Capvision compliance review first?
                    </el-checkbox>
                  </div>
                  <div>
                    <el-checkbox v-model="data.approval.is_client_compliance_needs_to_pre_approve" class="mb-8">
                      Client compliance needs to pre-approve?
                    </el-checkbox>
                  </div>
                  <div v-if="data.approval.is_client_compliance_needs_to_pre_approve" class="ml-2e mb-8">
                    <span class="hint"> After Capvision compliance approval:</span>
                    <el-input
                      v-model="data.approval.client_compliance_needs_to_pre_approve_detail"
                      type="textarea" />
                  </div>
                  <div>
                    <el-checkbox v-model="data.approval.is_copy_client_compliance_when_sending_to_user">
                      Copy client compliance when sending to user?
                    </el-checkbox>
                  </div>
                  <div v-if="data.approval.is_copy_client_compliance_when_sending_to_user" class="ml-2e mt-8">
                    <span class="hint">Copy the following people when sending the content to the user:</span>
                    <el-input
                      v-model="data.approval.copy_client_compliance_when_sending_to_user_detail"
                      type="textarea" />
                  </div>
                </div>
              </div>
              <div class="mb-8">
                <el-checkbox v-model="data.approval.has_ca_answers_in_the_approval_email">
                  CA Answers in the Approval email?
                </el-checkbox>
              </div>
              <div class="mb-8">
                <el-checkbox v-model="data.approval.has_internal_copy_rules">
                  Internal copy rules?
                </el-checkbox>
                <div v-if="data.approval.has_internal_copy_rules" class="ml-2e mt-8">
                  <el-checkbox v-model="data.approval.has_internal_copy_rules_am">AM</el-checkbox>
                  <el-checkbox v-model="data.approval.has_internal_copy_rules_capvision_compliance">Capvision Compliance
                  </el-checkbox>
                </div>
              </div>
              <div>
                <el-checkbox
                  v-model="data.approval.can_send_ca_and_bio_even_if_they_do_not_require_approval"
                  class="mb-8">
                  Send CA and bio even if they don't require approval?
                </el-checkbox>
                <div v-if="data.approval.can_send_ca_and_bio_even_if_they_do_not_require_approval" class="ml-2e">
                  To who?
                  <el-select v-model="data.approval.send_ca_and_bio_even_if_they_do_not_require_approval_to_who">
                    <el-option
                      v-for="item in options.send_ca_and_bio_even_if_they_do_not_require_approval_to_who_list"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                  <name-email
                    v-if="data.approval.send_ca_and_bio_even_if_they_do_not_require_approval_to_who === 'Special list'"
                    :list="data.approval.send_ca_and_bio_even_if_they_do_not_require_approval_special_list"
                    class="mt-8" />
                </div>
                <div>
                  <el-checkbox
                    v-model="data.approval.require_chaperone"
                    class="mb-8"
                  >
                    Requires Chaperone
                  </el-checkbox>
                  <div v-if="data.approval.require_chaperone" class="ml-2e">
                    Names and Emails of chaperones:
                    <name-email :list="data.approval.names_and_emails_of_chaperones" />
                  </div>
                </div>
              </div>
            </div>

            <div v-if="isInvestmentBankClient" class="mt-3">
              <b>Customized approval for investment bank type clients</b>
              <div class="mt-3">
                Internal Calls (Consultation Project Type)
                <div class="mt-8">
                  <el-select v-model="data.approval.consultation_project_rule.approve_policy" class="block w-200px">
                    <el-option value="CAPVISION_APPROVAL" label="ICOM" />
                    <el-option value="CAPVISION_AND_CLIENT_APPROVAL_ALL" label="ICOM & ECOM" />
                    <el-option :value="null" label="N/A" />
                  </el-select>

                  <div v-if="data.approval.consultation_project_rule.approve_policy">
                    <el-checkbox v-model="data.approval.consultation_project_rule.pre_ca.required">
                      Client Agreement Required
                    </el-checkbox>
                    <div
                      v-if="data.approval.consultation_project_rule.approve_policy ==='CAPVISION_AND_CLIENT_APPROVAL_ALL'">
                      <div v-if="data.approval.consultation_project_rule.pre_ca.required">
                        <el-checkbox
                          v-model="data.approval.consultation_project_rule.pre_ca.send_to_client_legal_in_email"
                          class="block">
                          Send Client Agreement to External Compliance
                        </el-checkbox>
                        <el-checkbox
                          v-if="data.approval.consultation_project_rule.pre_ca.send_to_client_legal_in_email"
                          v-model="data.approval.consultation_project_rule.pre_ca.send_to_client_legal_in_email_attachment"
                          class="block">
                          Send Client Agreement in PDF to External Compliance
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mt-3">
                Investor Calls
                <div class="mt-8">
                  <el-select v-model="data.approval.investor_call_project_rule.approve_policy" class="block w-200px">
                    <el-option value="CAPVISION_APPROVAL" label="ICOM" />
                    <el-option value="CAPVISION_AND_CLIENT_APPROVAL_ALL" label="ICOM & ECOM" />
                    <el-option :value="null" label="N/A" />
                  </el-select>
                  <div v-if="data.approval.investor_call_project_rule.approve_policy">
                    <el-checkbox v-model="data.approval.investor_call_project_rule.pre_ca.required" class="block">
                      Client Agreement Required
                    </el-checkbox>
                    <div
                      v-if="data.approval.investor_call_project_rule.approve_policy ==='CAPVISION_AND_CLIENT_APPROVAL_ALL'">
                      <div v-if="data.approval.investor_call_project_rule.pre_ca.required">
                        <el-checkbox
                          v-model="data.approval.investor_call_project_rule.pre_ca.send_to_client_legal_in_email"
                          class="block">
                          Send Client Agreement to External Compliance
                        </el-checkbox>
                        <el-checkbox
                          v-if="data.approval.investor_call_project_rule.pre_ca.send_to_client_legal_in_email"
                          v-model="data.approval.investor_call_project_rule.pre_ca.send_to_client_legal_in_email_attachment"
                          class="block">
                          Send Client Agreement in PDF to External Compliance
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </el-card>

        <!-- Call Scheduling emails -->
        <el-card id="CallScheduling" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='CallScheduling'}" class="card-title">
            <el-checkbox v-model="data.call_scheduling_emails.master_switch">Call Scheduling Emails</el-checkbox>
          </div>
          <div v-if="data.call_scheduling_emails.master_switch">
            <div class="mb-8">
              Schedule with
              <el-select v-model="data.call_scheduling_emails.schedule_with" class="mb-8">
                <el-option
                  v-for="item in options.call_scheduling_with"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
              <div v-if="data.call_scheduling_emails.schedule_with === 'With other'" class="ml-2e">
                When scheduling emails, do not talk to the user, schedule them with:
                <el-input
                  v-model="data.call_scheduling_emails.schedule_with_other_detail"
                  type="textarea"
                  placeholder="Include the user when emailing a PA? Or discuss with PA only?" />
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox
                v-model="data.call_scheduling_emails.is_wait_for_any_discussion_of_scheduling_until_after_approval_is_received">
                Wait for any discussion of scheduling until after approval is received?
              </el-checkbox>
            </div>
            <div>
              <el-checkbox
                v-model="data.call_scheduling_emails.can_send_placeholder_calendar_invite_before_approval">
                <!--  美国需求，该条rule 应用在arrange task，是否可以在专家未签TC或task没有审核通过的状态下发送arrange email, 字段变更 approval=>approval or TC signature-->
                Can send placeholder calendar invite before approval or TC signature?
              </el-checkbox>
              <div
                v-if="data.call_scheduling_emails.can_send_placeholder_calendar_invite_before_approval"
                class="ml-2e mt-8">
                <el-select
                  v-model="data.call_scheduling_emails.send_placeholder_calendar_invite_before_approval_type"
                >
                  <el-option
                    v-for="item in options.send_placeholder_calendar_invite_before_approval_type"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label" />
                </el-select>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Transcript/Notetaking rules -->
        <el-card id="TranscriptNotetaking" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='TranscriptNotetaking'}" class="card-title">
            <el-checkbox v-model="data.transcript_notetaking_rules.master_switch">Transcript/Notetaking Rules
            </el-checkbox>
          </div>
          <div v-if="data.transcript_notetaking_rules.master_switch">
            <div>
              <el-checkbox v-model="data.transcript_notetaking_rules.allowed" class="mb-8">Allowed?</el-checkbox>
            </div>
            <div v-if="data.transcript_notetaking_rules.allowed" class="mb-8  ml-2e">
              Who to send to?
              <el-select v-model="data.transcript_notetaking_rules.send_to">
                <el-option
                  v-for="item in options.transcript_notetaking_rules_send_to"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
              <div v-if="data.transcript_notetaking_rules.send_to === 'Other'" class="mt-8">
                The transcript/recording should be sent to :
                <name-email :list="data.transcript_notetaking_rules.send_to_detail" class="mt-8" />
              </div>
            </div>
            <div>
              <el-checkbox
                v-model="data.transcript_notetaking_rules.allowed_to_use_chinese_transcript_service"
                class="mb-8">
                Allowed to use the (Chinese) note-taking/transcript service?
              </el-checkbox>
            </div>
            <div>
              <el-checkbox
                v-model="data.transcript_notetaking_rules.is_copy_client_compliance_when_sending"
                class="mb-8">
                Copy client compliance when sending?
              </el-checkbox>
              <div v-if="data.transcript_notetaking_rules.is_copy_client_compliance_when_sending" class="ml-2e">
                <div class="mb-3">
                  <span class="hint">Compliance Notes</span>
                  <el-input
                    v-model="data.transcript_notetaking_rules.copy_client_compliance_when_sending_detail"
                    type="textarea" />
                </div>
                <div class="mb-3">
                  <span>CC on all transcript/recording emails to analysts:</span>
                  <div class="flex" style="line-height: 28px;">
                    <EmailSelect
                      prohibit-remote-search
                      class="flex-1"
                      :origin-list.sync="data.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts" />
                  </div>
                </div>
              </div>

            </div>
            <div>
              <el-checkbox
                v-model="data.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first"
                class="mb-8">
                Require compliance review of Recordings and Transcripts before client analyst can access
              </el-checkbox>
              <div
                v-if="data.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first"
                class="ml-2e">
                <span class="hint">Enter compliance team email address(s) who must review :</span>
                <div>
                  <div class="flex" style="line-height: 28px;">
                    <UserEmailSelect
                      class="flex-1"
                      :options="clientContactEmailList"
                      :origin-list.sync="data.transcript_notetaking_rules.require_compliance_review_email_list" />
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-8">
              <el-checkbox
                v-model="data.transcript_notetaking_rules.disable_transcripts_and_recordings">
                Disable Transcripts and Recordings
              </el-checkbox>
            </div>
          </div>
        </el-card>

        <!-- ARRANGE -->
        <el-card id="Arrange" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='Arrange'}" class="card-title">
            <el-checkbox v-model="data.arrange_email.master_switch">Arrange Email</el-checkbox>
          </div>
          <div v-if="data.arrange_email.master_switch">
            <div class="mb-8">
              <el-checkbox v-model="data.arrange_email.has_any_special_language_that_needs_to_be_put_in">
                Any special language that needs to be put in
              </el-checkbox>
              <el-input
                v-if="data.arrange_email.has_any_special_language_that_needs_to_be_put_in"
                v-model="data.arrange_email.any_special_language_that_needs_to_be_put_in_detail"
                class="ml-2e mt-8"
                type="textarea" />
            </div>
            <div>
              When to copy client compliance
              <el-select v-model="data.arrange_email.when_to_copy_client_compliance" class="mb-8 w-200px">
                <el-option
                  v-for="item in options.when_to_copy_client_compliance"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
              <div class="ml-2e">
                <el-input
                  v-if="data.arrange_email.when_to_copy_client_compliance === 'Other rule'"
                  v-model="data.arrange_email.copy_client_compliance_detail"
                  type="textarea" />
                <div>
                  Copy to :
                  <name-email :list="data.arrange_email.copy_client_compliance_list" class="mb-8 mt-8" />
                </div>
              </div>
            </div>
            <div class="mb-8">
              <el-checkbox v-model="data.arrange_email.is_include_ca_answers_in_the_arrange_email">
                Include CA answers in the Arrange email?
              </el-checkbox>
            </div>
            <div class="mb-8">
              <el-checkbox v-model="data.arrange_email.is_allowed_to_use_their_own_conf_system">
                Allowed to use their own conf system?
              </el-checkbox>
              <el-input
                v-if="data.arrange_email.is_allowed_to_use_their_own_conf_system"
                v-model="data.arrange_email.allowed_to_use_their_own_conf_system_detail"
                placeholder="They will provide details per call? Or we have a 'Capvision' line they've given us?"
                class="ml-2e mt-8"
                type="textarea" />
            </div>
            <div>
              <el-checkbox v-model="data.arrange_email.can_custom_template_or_custom_insert_into_our_template">
                Custom template or custom insert into our template?
              </el-checkbox>
              <el-input
                v-if="data.arrange_email.can_custom_template_or_custom_insert_into_our_template"
                v-model="data.arrange_email.custom_template_or_custom_insert_into_our_template_detail"
                class="ml-2e mt-8"
                type="textarea" />
            </div>
            <div class="mt-8">
              <el-checkbox v-model="data.arrange_email.is_emails_and_invites_from_capvision_pro">
                Emails and Invites from capvision-pro.com
              </el-checkbox>
            </div>
            <div class="mt-8">
              <el-checkbox v-model="data.arrange_email.is_automatically_add_client_compliance_role">
                When using the 'Zoom' or 'Twilio' bridge, automatically add the 'Client Compliance' role.
              </el-checkbox>
            </div>
          </div>
        </el-card>

        <!-- Calendar Invite -->
        <el-card id="CalendarInvite" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='CalendarInvite'}" class="card-title">
            <el-checkbox v-model="data.calendar_invite.master_switch">Calendar Invite</el-checkbox>
          </div>
          <div v-if="data.calendar_invite.master_switch">
            <div>
              <el-checkbox v-model="data.calendar_invite.is_do_not_send">
                Don't send? (Client will do their own calendars)
              </el-checkbox>
            </div>
            <div v-if="!data.calendar_invite.is_do_not_send" class="mt-8 ml-2e">
              When to copy others (besides user)
              <el-select v-model="data.calendar_invite.when_to_copy_others">
                <el-option
                  v-for="item in options.expert_type_list"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
              <el-input
                v-if="data.calendar_invite.when_to_copy_others === 'Other rule'"
                v-model="data.calendar_invite.when_to_copy_others_detail"
                class="mt-8"
                type="textarea" />
              <name-email :list="data.calendar_invite.copy_others_list" class="mb-8 mt-8" />
            </div>
            <div>
              <el-checkbox v-model="data.calendar_invite.is_include_angle_name_in_subject">
                Include Angle Name in Client Calendar Invite Subject
              </el-checkbox>
            </div>
            <div v-if="data.calendar_invite.is_include_angle_name_in_subject" class="mt-8 ml-2e">
              <el-radio-group v-model="data.calendar_invite.include_angle_name_position">
                <el-radio
                  v-for="item in options.include_angle_name_position"
                  :key="item.value"
                  :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </div>
            <div class="mt-8">
              <el-checkbox
                v-model="data.calendar_invite.is_include_client_agreement_in_compliance_invite_if_auto_approved">
                Include Client Agreement (CA) Q&A in Client Compliance invites if auto-approved for ECOM
              </el-checkbox>
            </div>
          </div>
        </el-card>

        <!-- Post-Call Process -->
        <el-card id="PostCall" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='PostCall'}" class="card-title">
            <el-checkbox v-model="data.post_call_process.master_switch">Post-Call Process</el-checkbox>
          </div>
          <div v-if="data.post_call_process.master_switch">
            <div class="mb-8">
              <el-checkbox v-model="data.post_call_process.has_post_call_ca">
                Post-call CA?
              </el-checkbox>
              <div v-if="data.post_call_process.has_post_call_ca" class="ml-2e mt-8">
                What to do with the answer?
                <el-input
                  v-model="data.post_call_process.post_call_ca_detail"
                  type="textarea" />
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.post_call_process.has_post_call_work_for_client">
                Post-call work for client?
              </el-checkbox>
              <div v-if="data.post_call_process.has_post_call_work_for_client" class="ml-2e mt-8">
                What content?
                <el-input
                  v-model="data.post_call_process.post_call_work_for_client__what_content"
                  type="textarea"
                  class="mb-8" />
                Anyone to copy?
                <el-input
                  v-model="data.post_call_process.post_call_work_for_client__anyone_to_copy"
                  type="textarea"
                  class="mb-8" />
                What to do with the answer?
                <el-input
                  v-model="data.post_call_process.post_call_work_for_client__what_to_do_with_the_answer"
                  type="textarea" />
              </div>
            </div>
          </div>
        </el-card>

        <!-- Research Reports/Surveys (Custom) -->
        <el-card id="ReportsSurveys" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='ReportsSurveys'}" class="card-title">
            <el-checkbox v-model="data.research_reports_surveys.master_switch">Research Reports/Surveys (Custom)
            </el-checkbox>
          </div>
          <div v-if="data.research_reports_surveys.master_switch">
            <div class="mb-8">
              <el-checkbox v-model="data.research_reports_surveys.approval_required_for_the_proposal.master_switch">
                Approval required for the proposal?
              </el-checkbox>
              <div
                v-if="data.research_reports_surveys.approval_required_for_the_proposal.master_switch"
                class="ml-2e">
                <el-checkbox
                  v-model="data.research_reports_surveys.approval_required_for_the_proposal.is_capvision_compliance_approval_first"
                  class="mb-8 mt-8">
                  Capvision compliance approval first?
                </el-checkbox>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.approval_required_for_the_proposal.is_who_to_ask_for_approval">
                    Who to ask for approval?
                  </el-checkbox>
                  <span class="hint">(The proposal requires  xxx  to ask for approval?)</span>
                  <el-input
                    v-if="data.research_reports_surveys.approval_required_for_the_proposal.is_who_to_ask_for_approval"
                    v-model="data.research_reports_surveys.approval_required_for_the_proposal.who_to_ask_for_approval_detail"
                    class="ml-2e mt-8"
                    type="textarea" />
                </div>
                <div class="mt-8">
                  RM sends or Capvision compliance sends?
                  <el-select
                    v-model="data.research_reports_surveys.approval_required_for_the_proposal.rm_sends_or_capvision_compliance_sends">
                    <el-option
                      v-for="item in options.rm_sends_or_capvision_compliance_sends"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.approval_required_for_the_proposal.has_any_requirements_except_to_attach_the_proposal"
                    class="mt-8">
                    Any requirements except to attach the proposal?
                  </el-checkbox>
                  <el-input
                    v-if="data.research_reports_surveys.approval_required_for_the_proposal.has_any_requirements_except_to_attach_the_proposal"
                    v-model="data.research_reports_surveys.approval_required_for_the_proposal.any_requirements_except_to_attach_the_proposal_detail"
                    class="ml-2e"
                    type="textarea" />
                </div>
              </div>
            </div>

            <div class="mb-8">
              <el-checkbox
                v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.master_switch">
                Approval required for the questionnaire?
              </el-checkbox>
              <div
                v-if="data.research_reports_surveys.approval_required_for_the_questionnaire.master_switch"
                class="ml-2e mt-8">
                <el-checkbox
                  v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.is_capvision_compliance_approval_first">
                  Capvision compliance approval first?
                </el-checkbox>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.is_always_required_just_if_capvision_writes_it"
                    class="mt-8">
                    Always required? Just if Capvision writes it?
                  </el-checkbox>
                  <el-input
                    v-if="data.research_reports_surveys.approval_required_for_the_questionnaire.is_always_required_just_if_capvision_writes_it"
                    v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.always_required_just_if_capvision_writes_it_detail"
                    class="ml-2e mt-8"
                    type="textarea" />
                </div>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.is_who_to_ask_for_approval"
                    class="mt-8">
                    Who to ask for approval?
                  </el-checkbox>
                  <span class="hint">(The questionnaire requires  xxx  to ask for approval?)</span>
                  <el-input
                    v-if="data.research_reports_surveys.approval_required_for_the_questionnaire.is_who_to_ask_for_approval"
                    v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.who_to_ask_for_approval_detail"
                    class="ml-2e mt-8"
                    type="textarea" />
                </div>
                <div class="mt-8">
                  RM sends or Capvision compliance sends?
                  <el-select
                    v-model="data.research_reports_surveys.approval_required_for_the_questionnaire.rm_sends_or_capvision_compliance_sends">
                    <el-option
                      v-for="item in options.rm_sends_or_capvision_compliance_sends"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
              </div>
            </div>

            <div class="mb-8">
              <el-checkbox
                v-model="data.research_reports_surveys.approval_required_for_the_deliverable.master_switch">
                Approval required for the deliverable?
              </el-checkbox>
              <div
                v-if="data.research_reports_surveys.approval_required_for_the_deliverable.master_switch"
                class="ml-2e mt-8">
                <el-checkbox
                  v-model="data.research_reports_surveys.approval_required_for_the_deliverable.is_capvision_compliance_approval_first">
                  Capvision compliance approval first?
                </el-checkbox>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.approval_required_for_the_deliverable.is_who_to_ask_for_approval"
                    class="mt-8">
                    Who to ask for approval?
                  </el-checkbox>
                  <span class="hint">(The deliverable requires  xxx  to ask for approval?)</span>
                  <el-input
                    v-if="data.research_reports_surveys.approval_required_for_the_deliverable.is_who_to_ask_for_approval"
                    v-model="data.research_reports_surveys.approval_required_for_the_deliverable.who_to_ask_for_approval_detail"
                    class="ml-2e"
                    type="textarea" />
                </div>
                <div class="mt-8">
                  RM sends or Capvision compliance sends?
                  <el-select
                    v-model="data.research_reports_surveys.approval_required_for_the_deliverable.rm_sends_or_capvision_compliance_sends">
                    <el-option
                      v-for="item in options.rm_sends_or_capvision_compliance_sends"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>
              </div>
            </div>

            <div>
              <el-checkbox
                v-model="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.master_switch">
                When sending to the user, copy compliance team or others?
              </el-checkbox>
              <div
                v-if="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.master_switch"
                class="ml-2e mt-8">
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.has_emails_to_copy">
                    Emails to copy
                  </el-checkbox>
                  <el-input
                    v-if="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.has_emails_to_copy"
                    v-model="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.emails_to_copy_detail"
                    class="ml-2e mb-8"
                    type="textarea" />
                </div>
                <div>
                  <el-checkbox
                    v-model="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.has_internal_emails_to_copy"
                    class="mt-8">
                    Internal emails to copy
                  </el-checkbox>
                  <el-input
                    v-if="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.has_internal_emails_to_copy"
                    v-model="data.research_reports_surveys.when_sending_to_the_user_copy_compliance_team_or_others.internal_emails_to_copy_detail"
                    class="ml-2e"
                    type="textarea" />
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Research Reports (Non-Custom) -->
        <el-card id="ReportsNonCustom" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active==='ReportsNonCustom'}" class="card-title">
            <el-checkbox v-model="data.research_reports.master_switch">Research Reports (Non-Custom)</el-checkbox>
          </div>
          <div v-if="data.research_reports.master_switch">
            Language Rules? (can the compliance team read Chinese)
            <el-input v-model="data.research_reports.language_rules" type="textarea" class="ml-2e mt-8 mb-8" />
            <div>
              <el-checkbox v-model="data.research_reports.is_approval_required_before_delivery">
                Approval required before delivery?
              </el-checkbox>
              <div v-if="data.research_reports.is_approval_required_before_delivery" class="ml-2e mt-8">
                <div>
                  <el-checkbox v-model="data.research_reports.is_capvision_compliance_approval_first">
                    Capvision compliance approval first?
                  </el-checkbox>
                </div>
                <div>
                  <el-checkbox v-model="data.research_reports.has_who_to_ask_for_approval" class="mt-8">
                    Who to ask for approval?
                  </el-checkbox>
                </div>
                <div class="ml-2e mt-8">
                  <span class="hint">(The report requires  xxx  to ask for approval?)</span>
                  <el-input
                    v-if="data.research_reports.has_who_to_ask_for_approval"
                    v-model="data.research_reports.who_to_ask_for_approval_detail"
                    type="textarea" />
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Events -->
        <el-card id="Events" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active === 'Events'}" class="card-title">
            <el-checkbox v-model="data.events.master_switch">Events</el-checkbox>
          </div>
          <div v-if="data.events.master_switch">
            <div class="mb-8">
              Should they get event advertisements?
              <el-select v-model="data.events.should_they_get_event_advertisements">
                <el-option
                  v-for="item in options.should_they_get_event_advertisements"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
              <div v-if="data.events.should_they_get_event_advertisements === 'Yes with rules'" class="ml-2e mt-8">
                <el-checkbox v-model="data.events.can_listed_companies">Listed companies?</el-checkbox>
                <div v-if="data.events.can_listed_companies" class="ml-2e mt-8">
                  Former period :
                  <el-input-number
                    v-model="data.events.can_former_period_count"
                    controls-position="right" />
                  <el-select
                    v-model="data.events.can_former_period_type">
                    <el-option
                      v-for="item in options.time_type"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label" />
                  </el-select>
                </div>

                <el-input
                  v-model="data.events.should_they_get_event_advertisements_rules"
                  type="textarea"
                  class="mt-8" />
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.events.cost_approvals.master_switch" class="mb-8">Cost approvals?
              </el-checkbox>
              <div v-if="data.events.cost_approvals.master_switch" class="ml-2e">
                <el-checkbox v-model="data.events.cost_approvals.is_cost_approval_required" class="mb-8">
                  Cost approval required (besides user)?
                </el-checkbox>
                <el-input
                  v-if="data.events.cost_approvals.is_cost_approval_required"
                  v-model="data.events.cost_approvals.cost_approval_required_detail"
                  class="ml-2e mb-8"
                  type="textarea" />
                <div>
                  Who to ask?
                  <el-input
                    v-model="data.events.cost_approvals.who_to_ask"
                    type="textarea"
                    class="mb-8" />
                </div>
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.events.live_events.master_switch">Live Events</el-checkbox>
              <div v-if="data.events.live_events.master_switch" class="ml-2e mt-8">
                <div>
                  <el-checkbox v-model="data.events.live_events.can_the_user_attend_live_events" class="mb-8">
                    Can the user attend live events?
                  </el-checkbox>
                  <div v-if="data.events.live_events.can_the_user_attend_live_events" class="ml-2e">
                    <el-select v-model="data.events.live_events.can_the_user_attend_live_events_type" class="mb-8">
                      <el-option
                        v-for="item in options.user_attend_live_events_type"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label" />
                    </el-select>
                  </div>
                </div>

                <div>
                  <el-checkbox v-model="data.events.live_events.do_they_need_approval_to_attend_live" class="mb-8">
                    Do they need approval to attend live?
                  </el-checkbox>
                  <div v-if="data.events.live_events.do_they_need_approval_to_attend_live" class="ml-2e">
                    <div>
                      <el-checkbox v-model="data.events.live_events.has_who_to_ask_for_approval" class="mb-8">
                        Who to ask for approval?
                      </el-checkbox>
                      <el-input
                        v-if="data.events.live_events.has_who_to_ask_for_approval"
                        v-model="data.events.live_events.who_to_ask_for_approval_detail"
                        class="ml-2e mb-8"
                        type="textarea" />
                    </div>
                    <div>
                      <el-checkbox
                        v-model="data.events.live_events.has_what_to_send_in_the_approval_request"
                        class="mb-8">
                        What to send in the approval request?
                      </el-checkbox>
                      <el-input
                        v-if="data.events.live_events.has_what_to_send_in_the_approval_request"
                        v-model="data.events.live_events.what_to_send_in_the_approval_request_detail"
                        class="ml-2e"
                        type="textarea" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <el-checkbox v-model="data.events.event_transcripts.master_switch" class="mt-8">Event transcripts
              </el-checkbox>
              <div v-if="data.events.event_transcripts.master_switch" class="ml-2e mt-8">
                <el-checkbox
                  v-model="data.events.event_transcripts.can_the_user_request_get_transcripts"
                  class="mb-8">
                  Can the user request/get transcripts?
                </el-checkbox>

                <div>
                  <el-checkbox v-model="data.events.event_transcripts.do_they_need_approval">
                    Do they need approval?
                  </el-checkbox>
                  <div v-if="data.events.event_transcripts.do_they_need_approval" class="ml-2e mt-8">
                    <div>
                      <el-checkbox v-model="data.events.event_transcripts.has_who_to_ask_for_approval">
                        Who to ask for approval?
                      </el-checkbox>
                      <el-input
                        v-if="data.events.event_transcripts.has_who_to_ask_for_approval"
                        v-model="data.events.event_transcripts.who_to_ask_for_approval_detail"
                        class="ml-2e mt-8"
                        type="textarea" />
                    </div>
                    <div>
                      <el-checkbox
                        v-model="data.events.event_transcripts.has_what_to_send_in_the_approval_request"
                        class="mt-8">
                        What to send in the approval request?
                      </el-checkbox>
                      <el-input
                        v-if="data.events.event_transcripts.has_what_to_send_in_the_approval_request"
                        v-model="data.events.event_transcripts.what_to_send_in_the_approval_request_detail"
                        class="ml-2e mt-8"
                        type="textarea" />
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </el-card>

        <!-- survey project -->
        <el-card id="SurveyProjectRules" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active === 'SurveyProjectRules'}" class="card-title">
            <el-checkbox v-model="data.survey_rules.master_switch">Survey</el-checkbox>
          </div>
          <div v-if="data.survey_rules.master_switch">
            <SurveyRules :data="data" />
          </div>
        </el-card>

        <!-- CNDBWorkflow -->
        <el-card id="CNDBWorkflow" shadow="never">
          <div slot="header" :class="{'active-rule':shortcut_active === 'CNDBWorkflow'}" class="card-title">
            <el-checkbox v-model="data.cn_db_workflow_close">Close Workflow</el-checkbox>
          </div>
          <div class="ml-3">
            <el-checkbox
              v-if="data.cn_db_workflow"
              v-model="data.cn_db_workflow.exclude_advisor_contact_info"
              :disabled="data.cn_db_workflow_close"
            >Exclude email/phone when sending to CN DB
            </el-checkbox>
          </div>
        </el-card>

      </form>
      <email-template
        ref="rulesView"
        :rules="data"
        :client-am="clientAM"
        :client-name="client.name"
        class="display-none" />
    </div>
  </div>
</template>

<script>
/**
 * @file 每次修改此文件应到 .\component\EmailTemplate.vue 修改相应rule
 * EmailTemplate.vue 是此页面的文本转义，用于发送邮件
 * 祝你好运 =P
 */

import { mapGetters } from 'vuex'
import NameEmail from '@/views/client/detail/compliance-preference/component/NameEmail'
import ExpertRestrictionsGranularItem
  from '@/views/client/detail/compliance-preference/component/ExpertRestrictionsGranularItem'
import ClientAPI from '@/api/client'
import CustomRule from '@/views/client/detail/compliance-preference/component/CustomRule'
import EmailTemplate from './component/EmailTemplate'
import ShowRules from './component/ShowRules'
import SurveyRules from './component/SurveyRules'
import UserEmailSelect from '@/components/EmailEditor/ToCCUser'
import EmailSelect from '@/components/EmailEditor/ToCC'
import _ from 'lodash'

export default {
  name: 'CompliancePreference',
  components: {
    ShowRules,
    CustomRule,
    ExpertRestrictionsGranularItem,
    NameEmail,
    EmailTemplate,
    SurveyRules,
    EmailSelect,
    UserEmailSelect,
  },
  props: {
    client: {
      type: Object,
    },
  },

  data() {
    return {
      options: {
        shortcut_options: [
          { value: 'CustomTextBoxRule', label: 'Custom Text' },
          { value: 'CommunicationRules', label: 'Communication' },
          { value: 'LanguageRules', label: 'Language' },
          { value: 'EmailCopyingRules', label: 'External Email Copy' },
          { value: 'InternalEmailCopyRules', label: 'Internal Email Copy' },
          { value: 'ProjectCreationRules', label: 'Project Creation' },
          { value: 'ExpertRestrictions', label: 'Expert Restrictions' },
          { value: 'RecommendEmail', label: 'Recommend Email' },
          { value: 'PreCall', label: 'Pre Call' },
          { value: 'Approval', label: 'Approval' },
          { value: 'CallScheduling', label: 'Call Scheduling' },
          { value: 'TranscriptNotetaking', label: 'Transcript/Notetaking' },
          { value: 'Arrange', label: 'Arrange' },
          { value: 'CalendarInvite', label: 'Calendar Invite' },
          { value: 'PostCall', label: 'Post Call' },
          { value: 'ReportsSurveys', label: 'Reports (Custom)' },
          { value: 'ReportsNonCustom', label: 'Reports (Non-Custom)' },
          { value: 'Events', label: 'Events' },
          { value: 'SurveyProjectRules', label: 'Survey' },
          { value: 'CNDBWorkflow', label: 'CN DB Workflow' },
        ],
        what_section__where_to_show_the_rule: [
          { value: 'General Rule', label: 'General Rule' },
          { value: 'Restrictions', label: 'Restrictions' },
          { value: 'Recommend', label: 'Recommend' },
          { value: 'Approval', label: 'Approval' },
          { value: 'Scheduling', label: 'Scheduling' },
          { value: 'Arrange', label: 'Arrange' },
          { value: 'Post-call', label: 'Post-call' },
        ],
        expert_type_list: [
          { value: 'All', label: 'All' },
          { value: 'Only that needed approval', label: 'Only that needed approval' },
          { value: 'Listed company expert', label: 'Listed company expert' },
          { value: 'Other rule', label: 'Other rule' },
        ],
        language_rules: [
          { value: 'Any language', label: 'Any language' },
          { value: 'English only - no Chinese', label: 'English only - no Chinese' },
          { value: 'English required - can also show Chinese', label: 'English required - can also show Chinese' },
          { value: 'Chinese only', label: 'Chinese only' },
          { value: 'Both languages must be shown', label: 'Both languages must be shown' },
        ],
        who_can_start_a_project__user_type: [
          { value: 'Any User', label: 'Any User' },
          { value: 'Any User - special CC rules', label: 'Any User - special CC rules' },
          { value: 'Only Users in DB', label: 'Only Users in DB' },
          {
            value: 'Only Listed Users - See Client Alert for details',
            label: 'Only Listed Users - See Client Alert for details',
          },
          { value: 'Special persons only', label: 'Special persons only' },
        ],
        time_period: [
          { value: 'days', label: 'days' },
          { value: 'weeks', label: 'weeks' },
          { value: 'months', label: 'months' },
        ],
        time_type: [
          { value: 'years', label: 'years' },
          { value: 'months', label: 'months' },
          { value: 'days', label: 'days' },
        ],
        is_allowed: [
          { value: 'ALLOWS', label: 'Yes' },
          { value: 'DOES NOT ALLOW', label: 'No' },
          { value: 'REQUIRES SPECIAL APPROVAL FOR', label: 'Special approval' },
        ],
        definition_listed_company: [
          { value: 'Subsidiary company', label: 'Subsidiary' },
          { value: 'Parent company', label: 'Parent' },
          { value: 'Any company in the same Group', label: 'Part of listed group' },
        ],
        expert_level: [
          { value: 'ALL', label: 'All' },
          { value: 'ONLY HIGH LEVEL', label: 'High' },
          { value: 'ONLY LOWER LEVEL', label: 'Low' },
        ],
        clinical_trials: [
          { value: 'Not allowed', label: 'Not allowed' },
          { value: 'Not allowed if relevant to project topic', label: 'Not allowed if relevant to project topic' },
          { value: 'Requires special approval', label: 'Requires special approval' },
        ],
        clinical_trials_restriction: [
          { value: 'Current trials', label: 'Current trials' },
          { value: 'Completed but not published trials', label: 'Completed but not published trials' },
          { value: 'Both', label: 'Both' },
        ],
        restriction_position: [
          { value: 'C-level', label: 'C-level', params_value: 'C_level' },
          {
            value: 'VP/Director/General Manager and above',
            label: 'VP/Director/General Manager and above',
            params_value: 'VP_and_other',
          },
          { value: 'Finance staff', label: 'Finance staff', params_value: 'finance' },
          { value: 'Audit staff', label: 'Audit staff', params_value: 'audit' },
        ],
        restriction_position_company_listed: [
          { value: 'All companies', label: 'All companies' },
          { value: 'Listed companies', label: 'Listed companies' },
        ],
        restriction_position_listed_condition: [
          { value: 'Not allowed', label: 'Not allowed' },
          { value: 'Need approval', label: 'Need approval' },
          { value: 'Former employee window', label: 'Former employee window' },
        ],
        firm_wide_or_per_user: [
          { value: 'anyone in the firm', label: 'Firm wide' },
          { value: 'each user', label: 'Per user' },
        ],
        approval_to_go_over: [
          { value: 'Never', label: 'Never' },
          { value: 'Ask for approval', label: 'Ask for approval' },
        ],
        current_employee_condition: [
          { value: 'All experts', label: 'All experts' },
          { value: 'Listed company experts', label: 'Listed company only' },
          { value: 'Target company experts', label: 'Target company only' },
        ],
        employer_self_consent_level: [
          { value: 'C Suite only', label: 'C Suite only' },
          { value: 'VP/Director/General Manager and above', label: 'VP/Director/General Manager and above' },
        ],
        current_employees_or_formers_too: [
          { value: 'Current', label: 'Current' },
          { value: 'Current and Formers', label: 'Current and Formers' },
        ],
        ca_usage_expiration_rules: [
          { value: 'One call only', label: 'One time use' },
          { value: 'For the project', label: 'For the project' },
          { value: 'Time period', label: 'Time period' },
        ],
        ca_valid_once_answered: [
          { value: 'For the project', label: 'For the project' },
          { value: 'Time period', label: 'Time period' },
        ],
        approval_method: [
          { value: 'Portal', label: 'Portal' },
          { value: 'Email', label: 'Email' },
        ],
        approval_email_who_send: [
          { value: 'the KM', label: 'KM' },
          { value: 'Capvision Compliance', label: 'Capvision Compliance Team' },
          { value: 'the client user or other person at the client', label: 'User or other client person' },
        ],
        who_can_approval: [
          { value: 'Anyone that is sent approval email', label: 'Anyone that is sent approval email' },
          { value: 'Only certain people', label: 'Only certain people' },
        ],
        calls_for_client_to_approve_type: [
          { value: 'Internal to Client', label: 'Internal to Client' },
          { value: 'All', label: 'All' },
          { value: 'Conditional', label: 'Conditional' },
        ],
        client_conditional_approve_type: [
          { value: 'Based on CA', label: 'Client Agreement - Require Approval if Hold Only' },
          { value: 'Listed company experts', label: 'Listed company experts' },
          { value: 'Other', label: 'Other' },
        ],
        in_person_meetings_type: [
          { value: 'Banned', label: 'Banned' },
          { value: 'Follow same call approval rules', label: 'Follow same call approval rules' },
          // { value: 'Special approval rules', label: 'Special approval rules' },
          {
            value: 'Requires approval regardless of call approval rules',
            label: 'Requires approval regardless of call approval rules',
          },
        ],
        send_ca_and_bio_even_if_they_do_not_require_approval_to_who_list: [
          { value: 'Same as approval', label: 'Same as approval' },
          { value: 'Special list', label: 'Special list' },
        ],
        call_scheduling_with: [
          { value: 'With User', label: 'With User' },
          { value: 'With other', label: 'With other' },
        ],
        send_placeholder_calendar_invite_before_approval_type: [
          { value: 'Can send', label: 'Can send' },
          { value: 'Never send', label: 'Never send' },
        ],
        transcript_notetaking_rules_send_to: [
          { value: 'User', label: 'User' },
          { value: 'Other', label: 'Other' },
        ],
        when_to_copy_client_compliance: [
          { value: 'All', label: 'All' },
          { value: 'Only that needed approval', label: 'Only that needed approval' },
          { value: 'Listed company expert', label: 'Listed company expert' },
          { value: 'Other rule', label: 'Other rule' },
        ],
        rm_sends_or_capvision_compliance_sends: [
          { value: 'RM/AM send', label: 'RM/AM' },
          { value: 'Capvision compliance send', label: 'Capvision compliance' },
        ],
        should_they_get_event_advertisements: [
          { value: 'Yes', label: 'Yes' },
          { value: 'No', label: 'No' },
          { value: 'Yes with rules', label: 'Yes with rules' },
        ],
        user_attend_live_events_type: [
          { value: 'Phone', label: 'Phone' },
          { value: 'In-person', label: 'In-person' },
          { value: 'Both', label: 'Both' },
        ],
        include_angle_name_position: [
          { value: 'Beginning', label: 'Beginning' },
          { value: 'End', label: 'End' },
        ],
      },
      /**
       * Boolean 以 is_ can_ has_ 打头
       */
      data: {
        rule: {
          list: [],
        },
        communication_rules: {
          email_only: false,
          phone_allowed: false,
          we_chat_allowed: false,
        },
        language_rules: {
          compliance_team_language: {
            master_switch: false,
            email_text: 'Any language',
            expert_bios_ca_answers_etc: 'Any language',
          },
          communication_with_users_analysts: {
            master_switch: false,
            email_text: 'Any language',
            expert_bios_ca_answers_etc: 'Any language',
          },
        },
        email_copying_rules: {
          all_emails: {
            master_switch: false,
            list: [],
          },
          recommending_experts: {
            master_switch: false,
            list: [],
          },
          negotiating_call_schedule: {
            master_switch: false,
            list: [],
          },
        },
        internal_email_copy_rules: {
          all_emails: false,
          am: false,
          compliance_team: false,
        },
        project_creation_rules: {
          who_can_start_a_project: {
            master_switch: false,
            users_type: 'Any User',
            special_user_detail: [],
          },
          is_any_project_code_or_other_similar: false,
          any_project_code_or_other_similar_detail: undefined,
          will_projects_have_a_budget: false,
          projects_have_a_budget_detail: undefined,
          do_projects_auto_expire__close_after_a_certain_amount_of_time: false,
          projects_auto_expire__close_after_a_certain_amount_of_time_detail: undefined,
          remind_user_to_add_a_project_case_code: false,
          remind_user_to_add_a_project_case_code_project_types: [],
        },
        expert_restrictions: {
          listed_company: {
            master_switch: false,
            listed_allowed: 'ALLOWS',
            has_geographic_restrictions: false,
            geographic_restrictions_detail: undefined,
            has_former_employee_window: false,
            former_employee_window_detail: 6,
            has_ipos: false,
            ipos_detail: undefined,
            definition: [],
            can_consultant_to_a_listed_company: false,
          },
          government_affiliated_employees: {
            master_switch: false,
            allowed: 'ALLOWS',
            has_former_employee_window: false,
            former_employee_window_detail: 6,
            soe: {
              master_switch: false,
              allowed: 'ALLOWS',
              has_former_employee_window: false,
              former_employee_window_detail: 6,
              expert_level: 'All',
              detail_text: '',
            },
            public_hospital: {
              master_switch: false,
              allowed: 'ALLOWS',
              has_former_employee_window: false,
              former_employee_window_detail: 6,
              expert_level: 'All',
              detail_text: '',
            },
            public_university: {
              master_switch: false,
              allowed: 'ALLOWS',
              has_former_employee_window: false,
              former_employee_window_detail: 6,
              expert_level: 'All',
              detail_text: '',
            },
            other: {
              master_switch: false,
              allowed: 'ALLOWS',
              has_former_employee_window: false,
              former_employee_window_detail: 6,
              expert_level: 'All',
              detail_text: '',
            },
          },
          franchisee_distributor_supplier: {
            master_switch: false,
            relevance_threshold_percentage_detail: undefined,
          },
          healthcare_experts_clinical_trials: {
            master_switch: false,
            can_doctors: false,
            can_other_medical_professionals: false,
            can_medical_tech_services_pharma_etc: false,
            clinical_trials: 'Not allowed',
            clinical_trials_restriction: 'Current trials',
          },
          expert_position: {
            master_switch: false,
            no_restriction: null,
            restriction_position: [],
            restriction_position_finance: '',
            restriction_position_finance_listed: '',
            restriction_position_finance_listed_empty: 6,
            restriction_position_audit: '',
            restriction_position_audit_listed: '',
            restriction_position_audit_listed_empty: 6,
            restriction_position_C_level: '',
            restriction_position_C_level_listed: '',
            restriction_position_C_level_listed_empty: 6,
            restriction_position_VP_and_other: '',
            restriction_position_VP_and_other_listed: '',
            restriction_position_VP_and_other_listed_empty: 6,
          },
          repeat_experts: {
            master_switch: false,
            firm_wide_or_per_user: 'Firm wide',
            restriction_call_number_and_time_period: {
              call_count: undefined,
              time: undefined,
              time_type: 'days',
            },
            has_approval_to_go_over: false,
            approval_to_go_over_type: 'Never',
            approval_to_go_over_detail: undefined,
          },
          investment_target: {
            master_switch: false,
            is_user_must_give_target: false,
            show_investment_target_to_expert: false,
            is_banned_detail: undefined,
            is_banned: false,
            has_former_employee_window: false,
            former_employee_window_detail: 6,
          },
          letter_from_employer_required: {
            master_switch: false,
            current_employer: {
              master_switch: false,
              conditions: 'All experts',
              can_self_consent: false,
              above_what_level: 'C Suite only',
              above_what_level_detail: '',
            },
            former_employer: {
              master_switch: false,
              left_in_last_month: undefined,
              can_self_consent: false,
              above_what_level: 'C Suite only',
              above_what_level_detail: '',
            },
          },
          custom_restriction_list: {
            master_switch: false,
            has_where_is_the_list: false,
            provided_and_in_our_system: undefined,
            online_link: undefined,
            current_employees_or_formers_too: 'Current',
            period_to_include_formers: undefined,
            period_to_include_formers_period_type: 'days',
          },
        },
        recommend_email: {
          has_custom_language_need_to_include_in_the_recommend_email: false,
          custom_language_need_to_include_in_the_recommend_email: undefined,
          is_label_experts: false,
          has_special_email_subject_rules: false,
          special_email_subject_rules: undefined,
          is_short_form_profiles: false,
        },
        pre_call_ca_usage: {
          master_switch: false,
          has_ca_valid_once_answered: false,
          ca_valid_once_answered: 'For the project',
          ca_valid_once_answered_time_period_count: undefined,
          ca_valid_once_answered_time_period_type: 'days',
          has_ca_valid_follow_up: false,
          ca_valid_follow_up: 'One call only',
          ca_valid_follow_up_time_period_count: undefined,
          ca_valid_follow_up_time_period_type: 'days',
        },
        approval: {
          master_switch: false,
          approval_method: 'Portal',
          who_send: 'KM',
          is_client_have_portal_access: false,
          consultation_project_rule: {
            'approve_policy': null,
            'pre_ca': {
              'required': false,
              'send_to_client_legal_in_email': false,
              'send_to_client_legal_in_email_attachment': false,
            },
            'post_ca': null,
          },
          investor_call_project_rule: {
            'approve_policy': null,
            'pre_ca': {
              'required': false,
              'send_to_client_legal_in_email': false,
              'send_to_client_legal_in_email_attachment': false,
            },
            'post_ca': null,
          },

          names_and_emails_to_send_approval_to: [],
          who_can_approval: 'Anyone that is sent approval email',
          names_and_emails_to_approval: [],
          has_capvision_approval_after_client: false,
          has_capvision_approval: false,
          has_calls_for_client_to_approve: false,
          calls_for_client_to_approve_type: 'Internal to Client',
          internal_to_client_detail: undefined,
          client_conditional_approve_type: 'Based on CA',
          client_conditional_approve_type_former_employee_window_detail: 0,
          client_conditional_approve_others_detail: undefined,
          client_conditional_approve_based_on_ca_detail: undefined,
          has_in_person_meetings: false,
          in_person_meetings_type: 'Banned',
          in_person_meetings_type_detail: undefined,
          in_person_meetings_approval_list: [],
          has_include_details_on_previous_calls_by_expert: false,
          has_calls_by_expert_with_all_clients: false,
          all_clients_relevant_time_period_count: undefined,
          all_clients_relevant_time_period_month: undefined,
          total_number_of_calls_by_this_expert_with_all_clients_last_month: undefined,
          has_calls_by_expert_with_this_client_only: false,
          client_only_relevant_time_period_count: undefined,
          client_only_relevant_time_period_month: undefined,
          has_written_content: false,
          is_capvision_compliance_review_first: false,
          is_client_compliance_needs_to_pre_approve: false,
          client_compliance_needs_to_pre_approve_detail: undefined,
          is_copy_client_compliance_when_sending_to_user: false,
          copy_client_compliance_when_sending_to_user_detail: undefined,
          is_ca_answers_in_the_approval_email: false,
          has_ca_answers_in_the_approval_email: false,
          has_internal_copy_rules: false,
          has_internal_copy_rules_am: false,
          has_internal_copy_rules_capvision_compliance: false,
          can_send_ca_and_bio_even_if_they_do_not_require_approval: false,
          send_ca_and_bio_even_if_they_do_not_require_approval_to_who: 'Same as approval',
          send_ca_and_bio_even_if_they_do_not_require_approval_special_list: [],
          require_chaperone: false,
          names_and_emails_of_chaperones: [],
        },
        call_scheduling_emails: {
          master_switch: false,
          schedule_with: 'With User',
          schedule_with_other_detail: undefined,
          is_wait_for_any_discussion_of_scheduling_until_after_approval_is_received: false,
          can_send_placeholder_calendar_invite_before_approval: false,
          send_placeholder_calendar_invite_before_approval_type: 'Never send',
        },
        transcript_notetaking_rules: {
          master_switch: false,
          allowed_to_use_chinese_transcript_service: false,
          allowed: false,
          send_to: 'User',
          send_to_detail: undefined,
          is_copy_client_compliance_when_sending: false,
          copy_client_compliance_when_sending_detail: '',
          cc_on_all_transcript_recording_emails_to_analysts: [],
          is_require_compliance_review_of_recordings_and_transcripts_first: false,
          require_compliance_review_email_list: [],
          disable_transcripts_and_recordings: false,
        },
        arrange_email: {
          master_switch: false,
          has_any_special_language_that_needs_to_be_put_in: false,
          any_special_language_that_needs_to_be_put_in_detail: undefined,
          when_to_copy_client_compliance: 'All',
          copy_client_compliance_detail: undefined,
          copy_client_compliance_list: [],
          is_include_ca_answers_in_the_arrange_email: false,
          is_allowed_to_use_their_own_conf_system: false,
          allowed_to_use_their_own_conf_system_detail: undefined,
          can_custom_template_or_custom_insert_into_our_template: false,
          is_emails_and_invites_from_capvision_pro: false,
          is_automatically_add_client_compliance_role: false,
          custom_template_or_custom_insert_into_our_template_detail: undefined,
        },
        calendar_invite: {
          master_switch: false,
          is_do_not_send: false,
          when_to_copy_others: 'All',
          copy_others_list: [],
          when_to_copy_others_detail: undefined,
          is_include_angle_name_in_subject: false,
          include_angle_name_position: 'Beginning',
          is_include_client_agreement_in_compliance_invite_if_auto_approved: false,
        },
        post_call_process: {
          master_switch: false,
          has_post_call_ca: false,
          post_call_ca_detail: undefined,
          has_post_call_work_for_client: false,
          post_call_work_for_client__what_content: undefined,
          post_call_work_for_client__anyone_to_copy: undefined,
          post_call_work_for_client__what_to_do_with_the_answer: undefined,
        },
        research_reports_surveys: {
          master_switch: false,
          approval_required_for_the_proposal: {
            master_switch: false,
            is_capvision_compliance_approval_first: false,
            is_who_to_ask_for_approval: false,
            who_to_ask_for_approval_detail: undefined,
            rm_sends_or_capvision_compliance_sends: 'RM/AM Send',
            has_any_requirements_except_to_attach_the_proposal: false,
            any_requirements_except_to_attach_the_proposal_detail: undefined,
          },
          approval_required_for_the_questionnaire: {
            master_switch: false,
            is_capvision_compliance_approval_first: false,
            is_always_required_just_if_capvision_writes_it: false,
            always_required_just_if_capvision_writes_it_detail: undefined,
            is_who_to_ask_for_approval: false,
            who_to_ask_for_approval_detail: undefined,
            rm_sends_or_capvision_compliance_sends: 'RM/AM Send',
          },
          approval_required_for_the_deliverable: {
            master_switch: false,
            is_capvision_compliance_approval_first: false,
            is_who_to_ask_for_approval: false,
            who_to_ask_for_approval_detail: undefined,
            rm_sends_or_capvision_compliance_sends: 'RM/AM Send',
          },
          when_sending_to_the_user_copy_compliance_team_or_others: {
            master_switch: false,
            has_emails_to_copy: false,
            emails_to_copy_detail: undefined,
            has_internal_emails_to_copy: false,
            internal_emails_to_copy_detail: undefined,
          },
        },
        research_reports: {
          master_switch: false,
          language_rules: undefined,
          is_approval_required_before_delivery: false,
          is_capvision_compliance_approval_first: false,
          has_who_to_ask_for_approval: false,
          who_to_ask_for_approval_detail: undefined,
        },
        events: {
          master_switch: false,
          should_they_get_event_advertisements: 'Yes',
          should_they_get_event_advertisements_rules: '',
          can_listed_companies: false,
          can_former_period_count: undefined,
          can_former_period_type: 'days',
          cost_approvals: {
            master_switch: false,
            is_cost_approval_required: false,
            cost_approval_required_detail: undefined,
            who_to_ask: undefined,
          },
          live_events: {
            master_switch: false,
            can_the_user_attend_live_events: false,
            can_the_user_attend_live_events_type: 'Both',
            do_they_need_approval_to_attend_live: false,
            has_who_to_ask_for_approval: false,
            who_to_ask_for_approval_detail: undefined,
            has_what_to_send_in_the_approval_request: false,
            what_to_send_in_the_approval_request_detail: undefined,
          },
          event_transcripts: {
            master_switch: false,
            can_the_user_request_get_transcripts: false,
            do_they_need_approval: false,
            has_who_to_ask_for_approval: false,
            who_to_ask_for_approval_detail: undefined,
            has_what_to_send_in_the_approval_request: false,
            what_to_send_in_the_approval_request_detail: undefined,
          },
        },
        cn_db_workflow_close: false,
        cn_db_workflow: { exclude_advisor_contact_info: false },
        survey_rules: {
          master_switch: false,
          text: null,
        },
      },
      // other_data: {
      //   approve_method: 'PORTAL',
      //   approve_policy: 'CAPVISION_AND_CLIENT_APPROVAL_ALL',
      // },
      form: {
        new_rule: undefined,
        names_and_emails_to_send_approval_to_new: { name: undefined, email: undefined },
      },
      isSubmitting: false,
      client_approval_disabled: false,
      capvision_approval_disabled: false,
      clinical_trials_disabled: false,
      approval_object: null,
      auto_active: true,
      shortcut_active: 'CustomTextBoxRule',
      permission: false,
      clientAM: undefined,
      hasRule: false,
      clientComplianceRules: {},
      isPreviewRules: false,
      chaperones: [],
      loading: false,
    }
  },

  computed: {
    ...mapGetters([
      'uid',
      'roles',
    ]),

    isConsultingFirmClient() {
      return this.client?.type === 'CONSULTING_FIRM'
    },
    isInvestmentBankClient() {
      return this.client?.type === 'INVESTMENT_BANK'
    },
    clientContactEmailList() {
      const result = []
      this.client?.client_contacts?.forEach(item => {
        const list = item.contact_infos_without_mosaic?.filter(
          temp => temp.type === 'EMAIL' && temp.value) || []
        list.forEach(i => {
          result.push({ email: i.value, name: item.name })
        })
      })
      return result
    },
  },

  watch: {
    'data.approval.has_capvision_approval_after_client': {
      handler(newVal) {
        if (newVal) {
          this.data.approval.has_capvision_approval = true
          this.data.approval.has_calls_for_client_to_approve = true
          this.data.approval.calls_for_client_to_approve_type = 'All'
          this.capvision_approval_disabled = true
          this.client_approval_disabled = true
        } else {
          this.capvision_approval_disabled = false
          this.client_approval_disabled = false
        }
      },
    },

    'data.approval.has_calls_for_client_to_approve': {
      handler(newVal) {
        if (newVal) {
          this.data.approval.has_capvision_approval = true
          this.capvision_approval_disabled = true
        } else {
          this.capvision_approval_disabled = false
        }
      },
    },
    'data.cn_db_workflow_close': {
      handler(newVal) {
        if (newVal) {
          this.data.cn_db_workflow.exclude_advisor_contact_info = false
        }
      },
    },
    'client': {
      handler(newVal) {
        this.clientAM = newVal.account_managers.map(item => item.user.name)
          .join(', ')
      }, immediate: true,
    },

    'approval_object': {
      handler(newVal) {
        if (newVal) {
          this.capvision_approval_disabled = true
          if (newVal === 'CAPVISION_ONLY') {
            this.data.approval.has_capvision_approval = true
            this.data.approval.has_calls_for_client_to_approve = false
          } else {
            this.data.approval.has_capvision_approval = true
            this.data.approval.has_calls_for_client_to_approve = true
          }
        }
      },
    },
  },

  mounted() {
    this.loadData()
    this.initAutoActive()
    this.getPermission()
    this.scrollToView(this.$route.query?.view)
  },
  methods: {
    initAutoActive() {
      const element = document.getElementById('ruleForm')
      if (!element) return
      this.auto_active = true
      element.addEventListener('scroll', () => {
        this.autoActive()
      }, false)
    },
    getPermission() {
      this.permission = this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
      this.isPreviewRules = !this.permission
    },
    actionAddNewRule(data) {
      this.data.rule.list.push(data)
      this.form.new_rule = undefined
    },
    loadData() {
      this.loading = true
      const compliance_preference = this.client.compliance_preference || {}
      this.chaperones = compliance_preference.chaperones

      if (compliance_preference.rule) {
        const rule = compliance_preference.rule
        this.formatApproval(compliance_preference)

        const compliance_rules = rule.compliance_rules

        if (compliance_rules) {
          fixOfUpdate(compliance_rules)
          this.$set(this, 'hasRule', true)
          this.$set(this, 'clientComplianceRules', compliance_rules)
          this.data = _.merge(this.data, compliance_rules)

          // 使组件视图得到数据响应
          this.data.rule = { list: compliance_rules.rule.list }

          if (!this.data.survey_rules) {
            this.$set(this.data, 'survey_rules', { master_switch: false, text: null })
          }

          if (!this.data.cn_db_workflow) {
            this.$set(this.data, 'cn_db_workflow', { exclude_advisor_contact_info: false })
          }

          if (!this.data.project_creation_rules.remind_user_to_add_a_project_case_code) {
            this.$set(this.data.project_creation_rules, 'remind_user_to_add_a_project_case_code', true)
            this.$set(this.data.project_creation_rules, 'remind_user_to_add_a_project_case_code_project_types', [])
          }

          this.checkHealthcareExpertsClinicalTrials()
        }
      }

      this.loading = false

      /**
       * 数据格式发生变更，对历史数据的转换处理
       * @param data
       * @returns {*}
       */
      function fixOfUpdate(data) {
        if (!Array.isArray(data.arrange_email.copy_client_compliance_list)) {
          data.arrange_email.copy_client_compliance_list = []
        }

        if (!data.approval.in_person_meetings_approval_list) {
          data.approval.in_person_meetings_approval_list = []
        }

        if (!data.calendar_invite.copy_others_list) {
          data.calendar_invite.copy_others_list = []
        }

        if (!data.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts) {
          data.transcript_notetaking_rules.cc_on_all_transcript_recording_emails_to_analysts = []
        }

        if (!data.approval.names_and_emails_to_approval) {
          data.approval.names_and_emails_to_approval = []
        }
        if (!data.project_creation_rules.who_can_start_a_project.special_user_detail) {
          data.project_creation_rules.who_can_start_a_project.special_user_detail = []
        }

        if (!data.approval.send_ca_and_bio_even_if_they_do_not_require_approval_special_list) {
          data.approval.send_ca_and_bio_even_if_they_do_not_require_approval_special_list = []
        }

        if (!Array.isArray(data.transcript_notetaking_rules.send_to_detail)) {
          data.transcript_notetaking_rules.send_to_detail = []
        }

        if (!data.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first) {
          data.transcript_notetaking_rules.require_compliance_review_email_list = []
        }

        if (data.rule.list.length) {
          const options = [
            { value: 'General Rule', label: 'General Rule' },
            { value: 'expert_restrictions', label: 'Restrictions' },
            { value: 'recommend_email', label: 'Recommend' },
            { value: 'approval', label: 'Approval' },
            { value: 'call_scheduling_emails', label: 'Scheduling' },
            { value: 'arrange_email', label: 'Arrange' },
            { value: 'post_call_process', label: 'Post-call' },
          ]
          data.rule.list.forEach(item => {
            if (!item) return
            const has_item = options.filter(opt => opt.label === item.where)
            if (has_item.length) {
              item.where = has_item[0].value
            }
          })
        }

        return data
      }
    },

    checkHealthcareExpertsClinicalTrials() {
      if (this.data.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials_restriction ===
        'Current trials') {
        this.data.expert_restrictions.healthcare_experts_clinical_trials.clinical_trials = 'Not allowed'
        this.clinical_trials_disabled = true
      } else {
        this.clinical_trials_disabled = false
      }
    },

    /**
     * approval_object 前端本地数据
     */
    formatApproval(rules) {
      if (rules.approve_policy !== 'NO_NEED') {
        this.approval_object = rules.approve_policy === 'CAPVISION_APPROVAL' ? 'CAPVISION_ONLY' : 'CAPVISION_CLIENT'
      }
    },

    formatParams() {
      const approve = this.data.approval
      const approve_method = this.data.approval.approval_method

      if (this.data.project_creation_rules.remind_user_to_add_a_project_case_code_project_types.length < 1) {
        this.data.project_creation_rules.remind_user_to_add_a_project_case_code = false
      }

      let approve_policy
      if (this.data.approval.master_switch) {
        if (approve.has_capvision_approval_after_client) {
          approve_policy = 'CAPVISION_THEN_CLIENT_APPROVAL'
        } else if (approve.has_calls_for_client_to_approve) {
          switch (approve.calls_for_client_to_approve_type) {
            case 'All':
              approve_policy = 'CAPVISION_AND_CLIENT_APPROVAL_ALL'
              break
            case 'Conditional':
              approve_policy = 'CAPVISION_AND_CLIENT_APPROVAL_SOME'
              break
            case 'Internal to Client':
              approve_policy = 'CAPVISION_AND_CLIENT_APPROVAL_INTERNAL'
              break
          }
        } else {
          approve_policy = approve.has_capvision_approval ? 'CAPVISION_APPROVAL' : 'NO_NEED'
        }
      } else {
        approve_policy = 'NO_NEED'
      }

      const chaperones = this.data.approval.require_chaperone
        ? this.data.approval.names_and_emails_of_chaperones.map(item => {
          const origin = this.chaperones.find(i => i.email === item.email && i.name === item.name)
          return { ...item, id: origin?.id }
        })
        : undefined

      return {
        approve_method: approve_method.toUpperCase(),
        approve_policy,
        compliance_rules: this.data,
        require_chaperone: this.data.approval.require_chaperone,
        chaperones,
      }
    },

    /* reset_project_members_has_read
    该字段可用于是否此次更新需要提示项目相关人员查看 project compliance
    不传 默认需要*/
    actionSave() {
      const data = this.formatParams()
      this.isSubmitting = true
      return ClientAPI
        .updateComplianceRules(this.client.id, data)
        .then(async res => {
          this.chaperones = res.chaperones
          this.spendEmailToContactAssociatedPerson()
          await this.updateClientPreference(res)
          this.clientComplianceRules = JSON.parse(JSON.stringify(this.data))
          this.$message({
            showClose: true,
            message: 'Rules is saved successfully!',
            type: 'success',
          })
        }, error => {
          this.$message({
            showClose: true,
            message: error,
            type: 'error',
          })
        })
        .finally(() => {
          this.isSubmitting = false
        })
    },

    spendEmailToContactAssociatedPerson() {
      if (this.data.arrange_email.master_switch && this.data.arrange_email.is_emails_and_invites_from_capvision_pro) {
        this.$message({
          showClose: true,
          message: `The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com),<br/> this time the updated content will not be sent through the system to send emails.`,
          type: 'warning',
          offset: 100,
          dangerouslyUseHTMLString: true,
          duration: 5000,
        })
        return Promise.resolve()
      } else {
        const params = {
          subject: 'Compliance Rule Approval',
          content: this.$refs['rulesView'].$el.innerHTML,
        }
        return ClientAPI.spendEmailToContactAssociatedPerson(this.client.id, params)
      }
    },

    updateClientPreference(data) {
      const target_rules = data.rule.compliance_rules.transcript_notetaking_rules

      if (target_rules.master_switch && target_rules.disable_transcripts_and_recordings) {
        if (!this.clientComplianceRules.transcript_notetaking_rules.disable_transcripts_and_recordings) {
          const params = { id: this.client.id }
          params['preference'] = Object.assign({}, this.client.preference,
            { enable_expire_delete_recordings_transcripts: false })

          return ClientAPI.updateClient(params)
        }
      }
    },

    scrollToView(value) {
      if (!value) return

      this.auto_active = false
      this.isPreviewRules = false

      // 点击目录 定位导航
      const element = document.getElementById('ruleForm')
      const top = document.getElementById(value).offsetTop - 120
      element.scrollTo({ top: top, behavior: 'smooth' })

      this.$set(this, 'shortcut_active', value)
      this.$router.replace({ query: { view: value } })

      setTimeout(() => {
        this.auto_active = true
      }, 1000)
    },

    autoActive() {
      const boxTop = document.getElementById('ruleForm')
        .getBoundingClientRect().top

      this.options.shortcut_options.forEach((item, index) => {
        const top = document.getElementById(item.value)
          .getBoundingClientRect().top
        const result = top - boxTop
        // 计算滑动到顶部的元素  =>高亮
        if (result < 100 && result > 70 && this.auto_active) {
          this.$set(this, 'shortcut_active', item.value)
          // 目录同步滚动
          this.$refs.stickyBox.scrollTo({ top: index * 15, behavior: 'smooth' })
        }
      })
    },

    actionShowRules() {
      this.isPreviewRules = true
      this.shortcut_active = null
    },

    actionGoToHistory() {
      this.$router.push({
        name: 'ClientComplianceRulesHistory',
        params: { client_id: this.client.id },
        query: { client_name: this.client.name, type: 'COMPLIANCE_RULE' },
      })
    },

  },
}
</script>

<style lang="scss" scoped>
.hidden-div {
  position: absolute;
  opacity: 0;
}

.el-card {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep .el-card__body {
    font-size: 14px;

    &:empty {
      padding: 0;
      margin-bottom: -1px;
    }
  }
}

::v-deep {
  .el-checkbox,
  .el-checkbox__label,
  .el-radio,
  .el-radio__label {
    user-select: auto;
  }
}

.ml-2e {
  margin-left: 2em;
}

::v-deep .mce-statusbar {
  display: none;
}

.hint {
  color: #929498;
}

.short-container {
  height: 100%;
  width: 150px;
  align-items: flex-start;
  transform: translate(0, 0);

  .left-fixed {
    width: 150px;
    height: 100%;
    position: fixed;
    padding-bottom: 50px;
    overflow: auto;
  }

  .shortcut-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .shortcut-text {
      font-size: 14px;
      padding: 0.5em 0;
      cursor: pointer;

      @media screen and (min-width: 1367px) {
        &:hover {
          color: #409eff;
        }
      }
    }
  }
}

.rules-box {
  //height: inherit;
  height: calc(100vh - 200px);
}

.rule-form {
  flex: 1;
  height: 100%;
  overflow: auto;
}

.active-rule {
  color: #409eff;

  ::v-deep .el-checkbox__label {
    color: #409eff;
  }
}

.card-title {
  ::v-deep .el-checkbox__label {
    font-size: 16px;
  }
}

::v-deep .el-checkbox__label {
  font-size: 14px;
}

::v-deep .el-radio__label {
  font-size: 14px;
}

::v-deep .custom-check-position .el-checkbox__input {
  vertical-align: top;
}

.custom-former-employee-input {
  width: 180px;
}

.disable-form div {
  pointer-events: none;
}

.block {
  display: block;
}

</style>
