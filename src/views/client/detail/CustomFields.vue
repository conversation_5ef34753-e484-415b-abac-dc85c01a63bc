<template>
  <div>
    <div class="filter-container">
      <el-button
        type="success"
        icon="el-icon-plus"
        class="filter-item"
        @click="editDialogVisible=true">New
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="list">
      <el-table-column label="Name">
        <template slot-scope="scope">
          {{ scope.row.field_display_name }}
        </template>
      </el-table-column>
      <el-table-column label="Type" prop="type" />
      <el-table-column label="Required">
        <template slot-scope="scope">
          {{ scope.row.is_required ? 'Yes' : 'No' }}
        </template>
      </el-table-column>
      <el-table-column label="Value Type" prop="field_value_type" />
      <el-table-column label="Create By">
        <template slot-scope="scope">
          {{ scope.row.create_by.name }}
        </template>
      </el-table-column>
      <el-table-column v-if="editPermission" label="Action" width="80">
        <template slot-scope="scope">
          <el-button type="text" class="el-icon-edit" @click="updateCustomFields(scope.row)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteCustomFields(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="Edit Custom Field"
      :visible.sync="editDialogVisible"
      :show-close="false"
      width="30%"
    >
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="Display Name">
          <el-input v-model="form.field_display_name" class="w-200px" />
        </el-form-item>
        <el-form-item label="Field Name">
          <el-input v-model="form.field_name" class="w-200px" />
        </el-form-item>
        <el-form-item label="Type">
          <el-select v-model="form.type">
            <el-option
              v-for="item in type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Required">
          <el-switch
            v-model="form.is_required"
            active-color="#13ce66"
            inactive-color="#ff4949" />
        </el-form-item>
        <el-form-item label="Input Type">
          <el-select v-model="form.input_type">
            <el-option
              v-for="item in input_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Value Type">
          <el-select v-model="form.field_value_type">
            <el-option
              v-for="item in value_type_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveEdit">Save</el-button>
        <el-button type="info" @click="cancelEdit">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import { mapGetters } from 'vuex'

export default {
  name: 'CustomFields',
  props: {
    clientId: {
      type: [String, Number],
      default: 0,
    },
  },
  data() {
    return {
      loading: false,
      editDialogVisible: false,
      editPermission: false,
      list: [],
      form: {
        type: 'PROJECT',
        field_name: null,
        field_display_name: null,
        is_required: true,
        field_value_type: 'STRING',
      },
      type_options: [
        { value: 'PROJECT', label: 'Project' },
        { value: 'TASK', label: 'Task' },
      ],
      value_type_options: [
        { value: 'STRING', label: 'String' },
        { value: 'NUMBER', label: 'Number' },
        // { value: 'INSTANT', label: 'Instance' },
        // { value: 'OBJECT', label: 'Object' },
        { value: 'LIST_STRING', label: 'List String' },
        // { value: 'LIST_NUMBER', label: 'List Number' },
        // { value: 'LIST_OBJECT', label: 'List Object' },
      ],
      input_type_options: [
        { value: 'TEXT', label: 'Text' },
        { value: 'NUMBER', label: 'Number' },
        { value: 'TEXTAREA', label: 'Textarea' },
        // { value: 'DATE', label: 'Date' },
        // { value: 'SELECT_SINGLE', label: 'Select Single' },
        // { value: 'SELECT_MULTIPLE', label: 'Select Multiple' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
  },
  mounted() {
    this.getList()
    this.getPermission()
  },
  methods: {
    getList() {
      this.loading = true
      const params = {
        client_id: this.clientId,
        extra: 'create_by',
      }
      return ClientAPI.getCustomFields(params).then(data => {
        this.list = data['list']
      }).finally(() => {
        this.loading = false
      })
    },
    getPermission() {
      this.editPermission = this.roles.some(role => {
        return [7].includes(role.role_id)
      })
    },

    deleteCustomFields(item) {
      return ClientAPI.deleteCustomFields(item.id).then(data => {
        this.getList()
      })
    },
    updateCustomFields(item) {
      this.form = Object.assign({}, item)
      this.editDialogVisible = true
    },
    saveEdit() {
      this.form.client_id = this.clientId
      const request = this.form.id ? ClientAPI.updateCustomFields(this.form) : ClientAPI.addCustomFields(this.form)
      return request.then(data => {
        this.$message({
          type: 'success',
          message: 'Save Successful!',
        })
        this.getList()
      }).finally(() => {
        this.cancelEdit()
      })
    },
    cancelEdit() {
      this.editDialogVisible = false
      this.form = {
        type: 'PROJECT',
        field_name: null,
        field_display_name: null,
        is_required: true,
        field_value_type: 'STRING',
      }
    },
  },
}
</script>

<style scoped>

</style>
