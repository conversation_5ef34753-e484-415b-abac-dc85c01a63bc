<template>
  <div v-loading="isLoading" class="app-container">
    <h2 v-if="client" class="client-name"># <span class="id-font">{{ client.id }}</span>
      {{ client.name }}
      <el-tag v-if="survey_only" type="success">Survey Only</el-tag>
      <span v-if="sendToClientThroughCapvisionPro" class="f-14 warning pl-5">
        The client's legal rules require sending relevant emails through the 'capvision-pro.com' mailbox.
      </span>
    </h2>
    <loading-status :status="!isFinished" :data="client" />
    <el-tabs
      v-if="permissionView && isFinished && isClientLoaded"
      v-model="active_tab_label"
      type="card"
      @tab-click="tabClick"
    >
      <el-tab-pane
        v-for="item in tabs"
        :key="item.router_name"
        :name="item.router_name"
      >
        <span slot="label"> {{ item.label }}</span>
      </el-tab-pane>
    </el-tabs>

    <router-view
      v-if="isClientLoaded"
      :client-id="client_id"
      :client="client"
      :disabled-edit="!permissionEdit"
      @update="getProfile" />

    <el-alert
      v-if="!permissionView && isFinished"
      title="No Permission"
      type="error"
      show-icon
      :closable="false" />
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import { RouteTools } from '@/utils/tool'
import LoadingStatus from '@/components/LoadingStatus'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'ClientDetail',
  components: {
    LoadingStatus,
  },

  props: {
    clientId: Number,
  },

  data() {
    return {
      isFinished: false,
      isLoading: true,
      client_id: this.clientId || this.$route.params.client_id,
      client: null,
      active_tab_label: this.$route.query.tab || 'Profile',
      permissionEdit: false,
      permissionView: false,
      survey_only: false,
      extra: [
        'account_managers.user',
        'compliance_preference',
        'location',
        'offices',
        'preference.usage_excel_fields',
        'contracts',
        'whale_wisdom_filer_maps.whale_wisdom_filer',
        'client_db_user_relation.user',
        'client_contacts.contact_infos_without_mosaic',
        'compliance_preference.chaperones',
      ],
      tabs: [
        { label: 'Profile', router_name: 'ClientProfile' },
        { label: 'Client User', router_name: 'ClientUser' },
        { label: 'Projects', router_name: 'ClientProjects' },
        { label: 'Contracts', router_name: 'ClientContracts' },
        { label: 'Client Agreement', router_name: 'ClientAgreement' },
        { label: 'Compliance Rules', router_name: 'ClientComplianceRules' },
        { label: 'Outreach', router_name: 'ClientOutreach' },
        { label: 'P&L', router_name: 'ClientPL' },
        { label: 'Document', router_name: 'ClientDocument' },
        { label: 'Task Dashboard', router_name: 'ClientDetailTaskDashboard' },
        { label: 'Client Usage', router_name: 'ClientUsage' },
        { label: 'Notes', router_name: 'ClientNotes' },
        { label: 'Vetting Calls', router_name: 'ClientVettingCalls' },
        { label: 'Custom Fields', router_name: 'ClientCustomFields' },
        { label: 'Black List', router_name: 'ClientBlackList' },
        { label: 'Account Settings', router_name: 'ClientAccountSettings' },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'uid',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    isClientLoaded() {
      return this.client && Object.prototype.hasOwnProperty.call(this.client, 'id')
    },
    currentRouterLabel() {
      return this.$route.name
    },
    sendToClientThroughCapvisionPro() {
      const has_rule = this.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        this.client.preference?.tpa_client
    },
  },

  watch: {
    '$route': {
      handler(to) {
        this.setActiveTab(to.name)
      },
      immediate: true,
    },
  },

  created() {
    if (!this.isSGDB && this.roles.some(role => role.role?.name === 'AllowClientAMDashboard')) {
      this.tabs.splice(4, 0, { label: 'AM Dashboard', router_name: 'ClientAmDashboard' })
    }
  },

  async mounted() {
    await this.getEditPermission()
    await this.getAmTeamList()
    await this.getProfile()
    this.getViewPermission()
  },

  methods: {
    getProfile() {
      this.isLoading = true
      const extra = this.extra.join()
      return ClientAPI.getClient(this.client_id, extra)
        .then(data => {
          data.am_team = this.getAmTeamName(data['am_team_id'])
          data.am = ''
          if (data['account_managers']) {
            data.am = data['account_managers'].map(item => {
              if (item.user) {
                return item.user.name
              }
            })
              .join(', ')
          }

          this.client = data

          // 有且仅有 生效的survey合同时
          this.survey_only = onlySurveyContract(data.contracts)

          return RouteTools.setRouterMetaTitle(data.name, this.$route)

          function onlySurveyContract(contracts) {
            if (!contracts) return false
            const effective_contracts = contracts.filter(item => item.effective_status === 'EFFECTIVE').length
            const effective_survey_contract = contracts.filter(
              item => item.effective_status === 'EFFECTIVE' && item.applicable_project_types === 'SURVEY').length
            return !!effective_contracts && effective_contracts === effective_survey_contract
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    getViewPermission() {
      const isOnlyAM = this.roles.length === 1 && this.roles[0].role_id === 5
      if (isOnlyAM) {
        this.permissionView = this.client['account_managers']?.some(item => item.uid === this.uid)
      } else {
        this.permissionView = true
      }
      this.isFinished = true
    },

    getEditPermission() {
      /* Only Sales/SKM/Compliance/Admin/AdminExcludeCompliance  edit client Profile, Contracts, Contact, Document */
      this.permissionEdit = this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'Compliance', 'AM / Sales', 'SRM, RM, SRA, CA'].includes(item.role.name)
      })
    },

    getAmTeamList() {
      return ClientAPI.getAmTeamList()
        .then(data => {
          this.am_team_options = data.list.map(item => {
            return { name: item.name, id: item.id }
          })
        })
    },

    getAmTeamName(id) {
      let result = ''
      this.am_team_options.map(item => {
        if (item.id === id) {
          result = item.name
        }
      })
      return result
    },

    setActiveTab(route_name) {
      this.active_tab_label = route_name
    },

    tabClick(tab) {
      this.$router.push({
        name: tab.name,
      })
    },
  },

}
</script>

<style scoped lang="scss">
.client-name {
  margin-top: 0;
}

.id-font {
  color: #42B983;
}

::v-deep .el-tabs__content {
  height: 100%;
}

::v-deep .el-form-item--mini.el-form-item {
  margin-bottom: 14px;
}

</style>
