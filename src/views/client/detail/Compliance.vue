<template>
  <div v-loading="loading">
    <div class="filter-container">
      <el-button
        v-if="CA_list.length < 2"
        :disabled="!permission"
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="goToEditCA()"
      >New
      </el-button>
    </div>

    <el-table
      :data="CA_list"
      border
    >
      <el-table-column label="Client Agreement">
        <el-table-column
          label="Type"
          width="120">
          <template slot-scope="scope">
            {{scope.row.type | getLabel(CA_type_options)}}
          </template>
        </el-table-column>
        <el-table-column
          label="Name"
        >
          <template slot-scope="scope">
            <router-link
              class="link"
              :to="{ name: 'ClientAgreementDetail', query: { client_id: client.id, inquiry_id: scope.row.id } }">
              {{ scope.row.name }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          label="Required"
          align="center"
          width="80">
          <template slot-scope="scope">
            <el-popconfirm
              icon="el-icon-info"
              icon-color="#13ce66"
              title="Are you sure you want to modify the required status?"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="success"
              @confirm="changeRequired(scope.row.client,scope.row.type)"
            >
              <div slot="reference">
                <el-button
                  v-if="scope.row.client['require_'+scope.row.type.toLowerCase()]"
                  type="text"
                  icon="el-icon-star-on"
                  class="success" />
                <el-button v-else slot="reference" type="text" icon="el-icon-star-off" />
              </div>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import InquiryAPI from '@/api/inquiry'
import ClientAPI from '@/api/client'

export default {
  name: 'Compliance',
  props: {
    client: Object,
  },
  data() {
    return {
      CA_list: [],
      CA_type_options: [
        { value: 'PRE_CALL_CA', label: 'Pre Call' },
        { value: 'POST_CALL_CA', label: 'Post Call' },
        // { value: 'LISTED_ONLY_CA', label: 'Listed Only' },
      ],
      permission: false,
      loading: true,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'roles',
    ]),
  },
  mounted() {
    this.getComplianceCA()
    this.getPermission()
  },

  methods: {
    getComplianceCA() {
      this.loading = true
      const params = {
        client_id: this.client.id,
        types: this.CA_type_options.map(item => item.value)
          .join(),
        extra: 'client',
      }
      return InquiryAPI.getInquiryList(params)
        .then(data => {
          this.CA_list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    getPermission() {
      this.permission = this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
    changeRequired(item, type) {
      const data = {
        id: item.id,
      }
      switch (type) {
        case 'LIST_ONLY_CA':
          data.require_list_only_ca = !item.require_list_only_ca
          break
        case 'POST_CALL_CA':
          data.require_post_call_ca = !item.require_post_call_ca
          break
        case 'PRE_CALL_CA':
          data.require_pre_call_ca = !item.require_pre_call_ca
          break
      }

      return ClientAPI.updateClient(data)
        .then(() => {
          this.getComplianceCA()
          this.$message({
            type: 'success',
            message: 'Success',
          })
        })
        .catch(() => {
        })
    },

    goToEditCA() {
      this.$router.push({
        name: 'ClientAgreementCreate',
        query: { client: this.client.name, client_id: this.client.id },
      })
    },

  },
}
</script>

<style scoped>

</style>
