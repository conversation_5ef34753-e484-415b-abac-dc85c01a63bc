<template>
  <div>
    <slot>
      <el-tabs tab-position="left">
        <el-tab-pane label="Email Outreach">
          <email-templates-list :client-id="clientId" />
        </el-tab-pane>
        <el-tab-pane label="Linkedin Request">
          <email-templates-list :client-id="clientId" linkedin />
        </el-tab-pane>
      </el-tabs>
    </slot>

    <router-view />
  </div>
</template>

<script>
import EmailTemplatesList from './components/OutreachTemplates'

export default {
  name: 'Outreach',
  components: { EmailTemplatesList },
  props: { clientId: [Number, String] },
  data() {
    return {
    }
  },
  computed: {
  },
}
</script>

<style scoped>

</style>
