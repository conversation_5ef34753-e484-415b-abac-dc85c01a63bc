<template>
  <div>
    <div class="filter-container">
      <el-button
        v-if="!disabledEdit"
        type="success"
        icon="el-icon-plus"
        class="filter-item"
        @click="uploadDialogVisible=true">New
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="documentList"
    >
      <el-table-column label="Title" prop="title" />
      <el-table-column label="Content" prop="content" />
      <el-table-column label="File">
        <template slot-scope="scope">
          <div v-for="(item,index) in scope.row.attachments" :key="index">
            <el-link
              type="primary"
              :href="item.file_url">{{ item.file_name }}
            </el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Create Time">
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Create By">
        <template slot-scope="scope">
          {{ scope.row.create_by.name }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="70">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteDocument(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="Upload Document"
      :visible.sync="uploadDialogVisible"
      width="40%"
    >
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="Title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="Content">
          <el-input v-model="form.content" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="File">
          <el-upload
            accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
            :action="upload_url"
            :on-success="handleSuccess"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :data="{'uid':uid}"
            :limit="1"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="cancelUpload">Cancel</el-button>
        <el-button type="primary" @click="saveUpload">Create</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import ClientAPI from '@/api/client'
import { mapGetters } from 'vuex'

export default {
  name: 'Document',
  props: {
    clientId: [Number, String],
    disabledEdit: Boolean,
  },
  data() {
    return {
      loading: false,
      uploadDialogVisible: false,
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      file_maxSize: 20,
      fileList: [],
      form: {
        title: '',
        content: '',
        file_name: undefined,
        file_url: undefined,
      },
      documentList: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  mounted() {
    this.getDocumentList()
  },
  methods: {
    getDocumentList() {
      this.loading = true
      return ClientAPI.getDocument(this.clientId).then(data => {
        this.documentList = data['list']
      })
        .finally(() => {
          this.loading = false
        })
    },
    cancelUpload() {
      this.initForm()
      this.uploadDialogVisible = false
    },
    saveUpload() {
      const { title, content, file_name, file_url } = this.form
      const params = {
        title, content,
        attachments: [
          {
            file_name,
            file_url,
          }],
      }
      return ClientAPI.addDocument(this.clientId, params).then(data => {
        this.$message({
          message: 'Create successful',
          type: 'success',
        })
      })
        .finally(() => {
          this.getDocumentList()
          this.cancelUpload()
        })
    },

    deleteDocument(item) {
      return ClientAPI.deleteDocument(this.clientId, item.id).then(data => {
        this.$message({
          message: 'Delete successful',
          type: 'success',
        })
      }).finally(() => {
        this.getDocumentList()
      })
    },

    initForm() {
      this.form = {
        title: '',
        content: '',
        file_name: undefined,
        file_url: undefined,
      }
    },

    handleSuccess(response) {
      if (response.msg === 'ok') {
        this.form.file_name = response.data.file_name
        this.form.file_url = response.data.url
      } else {
        this.$message(response.message)
      }
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`)
      }
    },

    handleRemove(file, fileList) {
      this.form.file_name = ''
      this.form.file_url = ''
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
