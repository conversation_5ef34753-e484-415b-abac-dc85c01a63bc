<template>
  <div>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="flex justify-content-between align-items-center">
        <div>
          <div class="holding-item-title">
            <el-link
              :underline="false"
              class="f-14"
              style="vertical-align: baseline;"
              @click="handleCollapseClick"
            >
              {{ holding.stock_name }} ({{holding.stock_ticker}})
            </el-link>
            <ico-button
              class="filter-item"
              tooltip="Collapse"
              no-border
              is-rotate
              :deg="90"
              @action="handleCollapseClick">
              <el-icon slot="ico" class="el-icon-arrow-right" />
            </ico-button>

            <slot v-if="holding.tag">
              <el-tag
                v-if="(!holding.tag.project_sq_evaluating && !holding.tag.project_sq_stale && isScreenedTab) || (!holding.tag.job_exp_evaluating && !holding.tag.job_exp_stale && !isScreenedTab)"
                type="success"
              >
                Latest
              </el-tag>
              <el-tag
                v-if="(!holding.tag.project_sq_evaluating && holding.tag.project_sq_stale && isScreenedTab) || (!holding.tag.job_exp_evaluating && holding.tag.job_exp_stale && !isScreenedTab)"
                type="warning"
              >
                Stale
              </el-tag>
              <el-tag
                v-if="(holding.tag.project_sq_evaluating && isScreenedTab) || (holding.tag.job_exp_evaluating && !isScreenedTab)"
                type="primary"
              >
                Evaluating...
              </el-tag>
              <!--              <el-tooltip-->
              <!--                v-if="holding.tag_id"-->
              <!--                class="item"-->
              <!--                effect="dark"-->
              <!--                content="Regenerate Relations with the Latest Algorithm(Time Consuming)"-->
              <!--                placement="top-start">-->
              <!--                <el-button-->
              <!--                  :loading="reload_loading"-->
              <!--                  plain-->
              <!--                  :disabled="(holding.tag.project_sq_evaluating && isScreenedTab) || (holding.tag.job_exp_evaluating && !isScreenedTab)"-->
              <!--                  class="ml-0 el-button&#45;&#45;xs el-icon-refresh-left"-->
              <!--                  @click="reevaluateList" />-->
              <!--              </el-tooltip>-->
            </slot>

            <el-tooltip
              v-if="isFollowed"
              class="item"
              effect="dark"
              content="Click To Cancel Following"
              placement="right"
            >
              <el-tag
                type="success"
                effect="dark"
                class="ml-3 cursor-pointer"
                @click.native="$emit('cancel-follow', holding.tag_id)"
              >
                <i class="el-icon-star-on" />
                Following
              </el-tag>
            </el-tooltip>
          </div>
          <div v-if="holding.current_percent_of_portfolio" class="fs-13 flex align-items-center justify-content-between w-172px">
            <b class="success">{{ holding.current_percent_of_portfolio }} %</b>
            <el-tooltip effect="dark" content="Qtr 1st Owned" placement="bottom">
              <b class="info">{{ holding.qtr_first_owned | momentFormat() }}</b>
            </el-tooltip>
          </div>
        </div>
        <div>
          <el-tabs v-model="active_tab_label">
            <el-tab-pane label="Screened" name="Screened" />
            <el-tab-pane label="Formers" name="Formers" />
          </el-tabs>
        </div>
      </div>

      <el-collapse-transition>
        <div v-show="isCollapse" style="margin: 14px;">
          <holding-advisor-table
            ref="holdingAdvisorTable"
            :is-collapse="isCollapse"
            :stock-ticker="holding.stock_ticker"
            :tag-ids="holding.tag_id"
            :active-tab-label="active_tab_label"
            :allow-custom-sort="isScreenedTab"
            v-bind="$attrs"
            v-on="$listeners"
          />
        </div>
      </el-collapse-transition>
    </el-card>
  </div>
</template>

<script>
import ConsultationAPI from '@/api/consultant'
import IcoButton from '@/components/IcoButton'
import HoldingAdvisorTable from './HoldingAdvisorTable'
import { insertNodeAt } from '@/utils/tool'
import Sortable from 'sortablejs'

export default {
  name: 'HoldingItem',
  components: { IcoButton, HoldingAdvisorTable },
  props: {
    holding: {
      type: Object,
      default: () => {
        return {}
      },
    },
    isFollowed: Boolean,
  },
  data() {
    return {
      reload_loading: false,
      isCollapse: false,
      advisor_sortable: null,
      active_tab_label: 'Screened',
    }
  },
  computed: {
    isScreenedTab() {
      return this.active_tab_label === 'Screened'
    },
  },
  beforeDestroy() {
    if (this.advisor_sortable) this.advisor_sortable.destroy()
  },
  methods: {
    handleCollapseClick() {
      if (!this.isCollapse && this.$refs.holdingAdvisorTable.tableData.count < 1) {
        // this.$refs.holdingAdvisorTable.getList()
        this.$nextTick(() => {
          this.loadSortable()
        })
      }
      this.isCollapse = !this.isCollapse
    },

    // reevaluateList() {
    //   const requestName = this.isScreenedTab ? 'reevaluateAdvisorTagsProjectSQ' : 'reevaluateAdvisorTagsJobExperience'
    //   this.reload_loading = true
    //   return ConsultationAPI[requestName](this.holding.tag_id)
    //     .then(data => {
    //       if (data) {
    //         this.$emit('update-tag', data)
    //       }
    //       return this.$refs.holdingAdvisorTable.getList()
    //     })
    //     .finally(() => {
    //       this.reload_loading = false
    //     })
    // },

    clearSelection() {
      this.$refs.holdingAdvisorTable.clearSelection()
    },

    loadSortable(ref_table = this.$refs['holdingAdvisorTable']) {
      const table =
        ref_table &&
        ref_table.$el.querySelector(
          '.el-table__body-wrapper tbody',
        )
      if (!table) return

      var options = {
        group: 'Advisors',
        handle: '.cursor-move',
        filter: '.locked',
        fallbackOnBody: true,
        onChoose: ({ oldIndex, item }) => {
          const target = this.$refs.holdingAdvisorTable.tableData.data[oldIndex]
          item.id = `${target.advisor_id}-${target.tag.id}`
        },
        onStart: evtData => {},
        onUpdate: evtData => {
          onEnd.call(this, evtData)
        },
        onRemove: ({ oldIndex, item, from }) => {
          insertNodeAt(from, item, oldIndex)
          const index = this.$refs.holdingAdvisorTable.tableData.data.findIndex(i => +i.id === +item.id)
          if (index > -1) this.$refs.holdingAdvisorTable.tableData.data.splice(index, 1)
        },
        onAdd: evtData => {
          onAdd.call(this, evtData)
        },
      }

      this.advisor_sortable = new Sortable(table, options)

      function onEnd({ newIndex, oldIndex, item, from }) {
        if (newIndex === oldIndex) return

        this.saveSort(newIndex, oldIndex, item, from)
      }

      function onAdd({ newIndex, oldIndex, item, from }) {
        console.log('add')
      }
    },
    saveSort(newIndex, oldIndex, item, from) {
      const advisor_id = item.id.split('-')[0]
      const params = {
        tag_id: +item.id.split('-')[1],
        user_id: 0,
        advisor_id_position_map: {
          [advisor_id]: newIndex + 0.5,
        },
      }
      return ConsultationAPI.sortAdvisorTagsProjectSQ(params)
    },
  },
}
</script>

<style scoped lang="scss">
.ml-0 {
  margin-left:0 !important;
}

::v-deep .el-card__body {
  padding: 0;
}

::v-deep .el-tabs__header {
  margin-bottom: 0;
}

.holding-item-title {
  margin-bottom: 2px;
}
</style>
