<template>
  <div>
    <div class="flex align-items-center justify-content-between">
      <div>
        <div class="mb-5px">
          <slot v-if="current_filer_name && !is_edit">
            <el-dropdown
              trigger="click"
              placement="bottom-start"
              @command="handleChangeFiler"
            >
              <div class="link">
                <b>{{ current_filer_name }}</b>
                <i class="el-icon-arrow-down el-icon--right" />
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in whale_wisdom_filer_maps"
                  :key="item.id"
                  :command="item">
                  <span v-if="item.whale_wisdom_filer">{{ item.whale_wisdom_filer.filer_name }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <el-link
              v-if="isClient"
              type="primary"
              size="mini"
              class="ml-8 mr-8"
              icon="el-icon-edit"
              :underline="false"
              @click="is_edit=true"
            />

            <el-select
              v-if="whale_wisdom_filer_id"
              v-model="filing_period"
              filterable
              class="ml-8"
              placeholder="-- Search --"
              @change="actionChangeFilingPeriod">
              <el-option
                v-for="item in options.filing_period"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>

            <add-to-project
              :advisors.sync="selected_list"
              @update="clearSelection()" />

            <el-button
              type="primary"
              :disabled="!selected_list.length"
              @click="createProject"
            >
              Create Project
            </el-button>
            <add-to-cart :advisors="selected_list" style="margin-left: 0;" />

            <el-popover
              v-if="isClient"
              v-model="followPopoverVisible"
              :append-to-body="true"
              placement="bottom"
              width="400"
              @after-leave="closeFollowPopover">
              <div class="flex">
                <div class="flex-1">
                  <el-select
                    v-model="follow_tags"
                    multiple
                    filterable
                    clearable
                    class="w-100 mb-8">
                    <el-option
                      v-for="item in holding_list"
                      :key="item.format_id"
                      :label="item.stock_name"
                      :value="item.tag_id">
                      <span>{{ item.stock_name }}</span>
                      <span class="ml-3 mr-normal pull-right" style="color: #8492a6;">{{ item.stock_ticker }}</span>
                    </el-option>
                  </el-select>
                </div>
                <div style="text-align: right; width: 100px;">
                  <el-button type="primary" size="mini" :loading="follow_loading" @click="confirmFollowHolding">Confirm
                  </el-button>
                  <el-button size="mini" type="text" @click="followPopoverVisible = false">Cancel</el-button>
                </div>
              </div>
              <el-button slot="reference" type="success" icon="el-icon-star-off">
                Follow ({{ follow_tags.length }})
              </el-button>
            </el-popover>
          </slot>
          <slot v-if="(!current_filer_name || is_edit) && !loading">
            <whale-wisdom-filer-search
              ref="whaleWisdomFilerSearch"
              v-model="whale_wisdom_filer_maps"
              :filer-list="whale_wisdom_filer_maps"
              class="w-408px"
            />
            <el-button
              type="primary"
              :loading="submitting"
              class="el-icon-check ml-8"
              @click="setFilerID()"
            >
              Save
            </el-button>
            <el-button
              v-if="current_filer_name"
              :disabled="submitting"
              @click="is_edit = false"
            >
              Cancel
            </el-button>
          </slot>

          <el-switch
            v-model="isListView"
            active-text="List View"
            class="ml-3"
            @change="actionToggleView" />

          <el-button
            v-if="!isListView"
            type="info"
            :disabled="loading"
            class="ml-3"
            icon="el-icon-office-building"
            @click="openAddCompanyTopicDialog('addCompanyDialog')"
          >
            Add Company
          </el-button>
          <el-button
            v-if="!isListView"
            type="warning"
            :disabled="loading"
            icon="el-icon-chat-line-square"
            style="margin-left: 0;"
            @click="openAddCompanyTopicDialog('addTopicDialog')"
          >
            Add Topic
          </el-button>
        </div>

        <div class="mb-5px">
          <div class="inline-block">
            <el-select
              v-model="all_filter.project_status"
              multiple
              clearable
              collapse-tags
              placeholder="Project Status"
            >
              <el-option
                v-for="item in options.project_status"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
          </div>
          <div v-if="isListView ? isScreenedTab : true" class="tab-filter-item">
            <holding-advisor-sort :sort.sync="screened_filter.sort" />
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>Screened</b> Tab</div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
          <div v-if="isListView ? !isScreenedTab : true" class="tab-filter-item">
            <el-date-picker
              v-model="formers_filter.start_date_gte"
              type="month"
              format="MM/yyyy"
              placeholder="Former Start"
              value-format="yyyy-MM"
            />
            <el-date-picker
              v-model="formers_filter.end_date_lte"
              type="month"
              format="MM/yyyy"
              placeholder="Former End"
              value-format="yyyy-MM"
            />
            <el-checkbox v-model="formers_filter.same_current_and_former_employer">
              Exclude experts with the same current and former employer
            </el-checkbox>
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>Formers</b> Tab</div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
          <div v-if="isListView" class="tab-filter-item">
            <advisor-search
              v-model="list_view_filter.advisor_id"
              class="w-150px"
              :multiple="false"
              :initial-id="$route.query.advisor_id"
            />
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>List View</b></div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
        </div>
      </div>

      <el-tabs v-if="isListView" v-model="active_tab_label">
        <el-tab-pane label="Screened" name="Screened" />
        <el-tab-pane label="Formers" name="Formers" />
      </el-tabs>
    </div>

    <div v-show="!isListView" v-loading="loading" class="position-relative">
      <el-tabs v-model="active_type" type="card" @tab-click="actionToggleView">
        <el-tab-pane label="Holdings" name="Holdings">
          <span slot="label"><i class="el-icon-data-line" /> &nbsp;Holdings</span>
          <holding-item
            v-for="item in holding_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :is-followed="follow_tags_origin.includes(item.tag_id)"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            :formers-filter="formers_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @cancel-follow="actionCancelFollowing"
            @update-tag="updateTagAfterRegenerate(item, 'tag', $event)"
          />
          <div v-if="!holding_list.length && whale_wisdom_filer_maps.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
        <el-tab-pane label="Companies" name="Companies">
          <span slot="label"><i class="el-icon-office-building" /> &nbsp;Companies</span>
          <company-topic-item
            v-for="item in company_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            :formers-filter="formers_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @update-tag="updateTagAfterRegenerate(item, 'advisor_tag', $event)"
          />
          <div v-if="!company_list.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
        <el-tab-pane label="Topics" name="Topics">
          <span slot="label"><i class="el-icon-chat-line-square" /> &nbsp;Topics</span>
          <company-topic-item
            v-for="item in topic_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @update-tag="updateTagAfterRegenerate(item, 'advisor_tag', $event)"
          />
          <div v-if="!topic_list.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
      </el-tabs>

      <el-dropdown
        v-if="active_type === 'Holdings' && !isListView"
        trigger="click"
        placement="bottom-start"
        class="ml-3"
        style="position: absolute; top: 5px; right: 0;"
        @command="actionSortHoldingList"
      >
        <el-button type="default">
          <i class="el-icon-sort" />
          Holding Sort By {{ sort_type }}
          <i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="Default">Default</el-dropdown-item>
          <el-dropdown-item command="Position Size">Position Size</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <holding-advisor-table
      v-if="isListView"
      ref="holdingAdvisorTable"
      v-loading="loading"
      :is-list-view="isListView"
      :is-collapse="false"
      :active-tab-label="active_tab_label"
      :tag-ids="ticker_tag_ids"
      :all-filter="all_filter"
      :screened-filter="screened_filter"
      :formers-filter="formers_filter"
      :list-view-filter="list_view_filter"
      :holding-list="holding_list"
      @selection-change="itemSelected($event)"
    />

    <add-company-dialog
      ref="addCompanyDialog"
      :client="client"
      :company-list="company_list"
      :topic-list="topic_list"
      @update="updateAdvisorTagMaps"
    />
    <add-topic-dialog
      ref="addTopicDialog"
      :client="client"
      :company-list="company_list"
      :topic-list="topic_list"
      @update="updateAdvisorTagMaps"
    />
  </div>
</template>

<script>
import ToolAPI from '@/api/tool'
import ClientAPI from '@/api/client'
import ConsultantAPI from '@/api/consultant'
import moment from 'moment'
import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'
import AddToProject from '@/views/consultant/components/addToProject'
import HoldingItem from '@/views/client/detail/AMDashboard/HoldingItem'
import HoldingAdvisorSort from './components/HoldingAdvisorSort'
import HoldingAdvisorTable from './HoldingAdvisorTable'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import WhaleWisdomFilerSearch from '@/components/SelectRemoteSearch/WhaleWisdomFilerSearch'
import AddCompanyDialog from './components/AddCompanyDialog'
import AddTopicDialog from './components/AddTopicDialog'
import CompanyTopicItem from './CompanyTopicItem'
import AddToCart from '@/components/ShoppingCart/AddToCart'

export default {
  name: 'AmDashboard',
  components: {
    HoldingItem, AddToProject, HoldingAdvisorSort, HoldingAdvisorTable, AdvisorSearch,
    WhaleWisdomFilerSearch, AddCompanyDialog, AddTopicDialog, CompanyTopicItem, AddToCart,
  },
  props: {
    client: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      loading: false,
      is_edit: false,
      submitting: false,
      whale_wisdom_filer_id: null,
      whale_wisdom_filer_maps: [],
      current_filer_name: null,
      filing_period: moment.tz(moment().add(-2, 'Q'), this.timeZone).endOf('quarter').format('YYYY-MM-DD'),
      options: {
        filing_period: [],
        project_status: [
          'Call Completed',
          'Scheduled',
          'Sent to Client',
          'Screened',
        ],
      },
      selected_list: [],
      selected_holding_list: [],
      holding_list: [],
      holding_list_origin: [],
      company_list: [],
      topic_list: [],
      sort_type: 'Position Size',
      all_stock_ticker: '',
      ticker_tag_ids: '',
      active_tab_label: 'Screened',
      active_type: 'Holdings',

      all_filter: {
        project_status: [],
      },
      screened_filter: {
        sort: 'match_type_bits desc,latest_sq_submit_time desc',
      },
      formers_filter: {
        start_date_gte: '',
        end_date_lte: '',
        same_current_and_former_employer: false,
      },
      list_view_filter: {
        advisor_id: null,
      },

      follow_loading: false,
      follow_id: 0,
      follow_tags: [],
      follow_tags_origin: [],
      followPopoverVisible: false,
      isListView: false,
    }
  },

  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    isScreenedTab() {
      return this.active_tab_label === 'Screened'
    },
    isClient() {
      return !this.client.client_id
    },
  },

  mounted() {
    this.generateFilingPeriodOptions()
    this.initAmDashboard()
  },

  methods: {
    initAmDashboard() {
      if (this.isClient) {
        if (this.$route.query) {
          if (this.$route.query.is_list_view === 'true') this.isListView = true
          if (this.$route.query.is_formers === 'true') this.active_tab_label = 'Formers'
          if (this.$route.query.advisor_id) this.list_view_filter.advisor_id = +this.$route.query.advisor_id
        }
        if (this.client.whale_wisdom_filer_maps.length) {
          this.getHoldingSubscriptions()
        } else {
          this.is_edit = true
          this.$refs['whaleWisdomFilerSearch'].search(this.client.name)
        }
      }

      this.whale_wisdom_filer_maps = [...this.client.whale_wisdom_filer_maps]
      this.whale_wisdom_filer_id = this.client.whale_wisdom_filer_maps?.[0]?.whale_wisdom_filer_id
      this.getFilerList()
    },

    getFilerList() {
      if (!this.whale_wisdom_filer_id) {
        this.current_filer_name = null
        this.holding_list = []
        this.holding_list_origin = []
        this.getHoldingTagsById().then(this.getAllTagIds)
        return
      }

      this.loading = true
      const params = {
        filer_id: this.whale_wisdom_filer_id,
        filing_period: this.filing_period,
      }
      return ToolAPI.getWhaleWisdomHoldings(params)
        .then(data => {
          this.current_filer_name = data.filer_name
          this.holding_list = data.whale_wisdom_data.holdings
            .filter(item => item.current_percent_of_portfolio)
            .map((item, index) => {
              item.format_id = `${item.stock_id}-${index}`
              item.qtr_first_owned = moment('2000-12-31').add(item.quarter_id_owned * 3, 'month')
              return item
            })
          this.holding_list_origin = [...this.holding_list]
          this.all_stock_ticker = this.holding_list_origin.map(holding => holding.stock_ticker).join(',')
          this.actionSortHoldingList(this.sort_type)
        })
        .then(this.getHoldingTagsByTicker)
        .then(this.getHoldingTagsById)
        .then(this.getAllTagIds)
        .finally(() => {
          this.loading = false
        })
    },

    actionChangeFilingPeriod() {
      this.getFilerList()
      localStorage.setItem('am_dashboard_period', this.filing_period)
    },

    itemSelected(val, holding) {
      if (holding) {
        const data = { list: val, format_id: holding.format_id }
        const selected_index = this.selected_holding_list.findIndex(item => item.format_id === data.format_id)

        if (selected_index > -1) {
          this.selected_holding_list[selected_index] = data
        } else {
          this.selected_holding_list.push(data)
        }
        const advisor_list = []
        this.selected_holding_list.forEach(item => {
          advisor_list.push(...item.list.map(i => i.advisor))
        })
        this.selected_list = _.uniq(advisor_list, 'id')
      } else {
        this.selected_list = _.uniq(val.map(i => i.advisor), 'id')
      }
    },

    actionToggleView() {
      this.selected_list = []
      this.$nextTick(() => {
        this.clearSelection()
      })
    },

    clearSelection() {
      if (!this.isListView) {
        this.selected_holding_list.forEach(item => {
          this.$refs[`holdingTable${item.format_id}`][0].clearSelection()
        })
        this.selected_holding_list = []
      } else {
        this.$refs['holdingAdvisorTable'].clearSelection()
      }
    },

    setFilerID() {
      this.submitting = true
      const params = {
        id: this.client.id,
        whale_wisdom_filer_maps: this.whale_wisdom_filer_maps,
      }
      return ClientAPI.updateClient(params)
        .then(() => {
          this.is_edit = false
          this.whale_wisdom_filer_id = this.whale_wisdom_filer_maps[0]?.whale_wisdom_filer_id
          return this.getFilerList()
        })
        .finally(() => {
          this.submitting = false
        })
    },

    // 从 2001 第一个季度开始 到当前的上一个季度 返回季度列表
    generateFilingPeriodOptions() {
      const am_dashboard_period = localStorage.getItem('am_dashboard_period')
      if (am_dashboard_period) { this.filing_period = am_dashboard_period }

      const months = moment().diff(moment('2000-12-31'), 'month')
      const quarter_count = Math.floor(months / 3)

      const list = new Array(quarter_count)
        .fill(undefined)
        .map((none, index) => {
          const time = moment('2000-12-31').add((index + 1) * 3, 'month')
          return {
            label: `Q${time.quarter()} ${moment(time).format('YYYY')}`,
            value: time.format('YYYY-MM-DD'),
          }
        })

      this.options.filing_period = list.reverse()
    },

    actionSortHoldingList(command) {
      this.sort_type = command
      if (command === 'Default') {
        this.holding_list = [...this.holding_list_origin]
      } else {
        this.holding_list = this.holding_list.sort((a, b) => b.current_percent_of_portfolio - a.current_percent_of_portfolio)
      }
    },

    async getHoldingTagsByTicker() {
      let list = []
      if (this.all_stock_ticker) {
        const res = await ConsultantAPI.getAdvisorTagsByTicker({
          'third_party_company_maps.third_party_company.ticker_in': this.all_stock_ticker,
          reevaluate_stale: true,
          size: 999,
          extra: 'third_party_company_maps.third_party_company',
        })
        list = res.list
      }

      this.holding_list.forEach(item => {
        const tag = list.find(tag => {
          return tag.third_party_company_maps.some(m => m.third_party_company.ticker === item.stock_ticker)
        })
        this.$set(item, 'tag', tag)
        this.$set(item, 'tag_id', tag?.id)
      })
    },

    async getHoldingTagsById() {
      const key = this.isClient ? 'client_maps.client_id' : 'client_contact_maps.client_contact_id'
      const { list } = await ConsultantAPI.getAdvisorTagsByTicker({
        [key]: this.client.id,
        reevaluate_stale: true,
        size: 999,
        extra: 'third_party_company_maps.third_party_company',
      })
      list.forEach(item => {
        item.advisor_tag = item
        item.format_id = item.id
      })
      this.company_list = list.filter(item => item.advisor_tag.category === 'COMPANY')
      this.topic_list = list.filter(item => item.advisor_tag.category === 'OTHER')
    },

    getAllTagIds() {
      const ticker_tag_ids = [...this.company_list, ...this.topic_list].map(item => item.advisor_tag.id)
      this.holding_list.forEach(item => {
        const tag = item.tag
        if (tag?.id && !ticker_tag_ids.includes(tag.id)) {
          ticker_tag_ids.push(tag.id)
        }
      })
      this.ticker_tag_ids = ticker_tag_ids.join()
    },

    handleChangeFiler(val) {
      this.holding_list = []
      this.whale_wisdom_filer_id = val.whale_wisdom_filer_id
      this.current_filer_name = val.whale_wisdom_filer.filer_name
      this.getFilerList()
    },

    getHoldingSubscriptions() {
      const params = {
        client_id: this.client.id,
        user_id: this.uid,
      }
      return ConsultantAPI.getAdvisorTagsHoldingSubscriptions(params)
        .then(({ list }) => {
          this.follow_tags = list.length ? list[0].tag_subscriptions.map(item => item.tag_id) : []
          this.follow_tags_origin = [...this.follow_tags]
          this.follow_id = list.length ? list[0].id : 0
        })
    },

    closeFollowPopover() {
      this.follow_tags = [...this.follow_tags_origin]
    },

    async confirmFollowHolding() {
      const params = {
        client_id: this.client.id,
        user_id: this.uid,
        tag_subscriptions: this.follow_tags.map(id => ({ tag_id: id })),
      }
      this.follow_loading = true
      if (!this.follow_id) {
        await ConsultantAPI.saveHoldingSubscriptions(params)
      } else {
        await ConsultantAPI.updateHoldingSubscriptions(this.follow_id, params)
      }
      this.follow_tags_origin = [...this.follow_tags]
      this.follow_loading = false
      this.followPopoverVisible = false
    },

    actionCancelFollowing(tag_id) {
      const index = this.follow_tags.findIndex(id => id === tag_id)
      if (index > -1) {
        this.follow_tags.splice(index, 1)
      }
      this.confirmFollowHolding()
    },

    openAddCompanyTopicDialog(name) {
      this.$refs[name].show()
    },

    createProject() {
      const urlQuery = {
        client: {
          id: this.client.id,
          name: this.client.name,
        },
        advisors: this.selected_list.map(item => item.id),
      }
      const route = this.$router.resolve({ name: 'CreateProject', query: { am_dashboard: JSON.stringify(urlQuery) } })
      window.open(route.href, '_blank')
    },

    async updateAdvisorTagMaps() {
      try {
        this.loading = true
        await this.getHoldingTagsById()
        this.getAllTagIds()
      } finally {
        this.loading = false
      }
    },

    updateTagAfterRegenerate(item, tagKey, val) {
      for (const key in item[tagKey]) {
        if (typeof item[tagKey][key] !== 'object') {
          item[tagKey][key] = val[key]
        }
      }
    },
  },
}
</script>

<style scoped>
.no-data-box{
  width: 100%;
  height: 200px;
  line-height: 200px;
  text-align: center;
  color: #909399;
}

.tab-filter-item {
  display: inline-block;
  background-color: #EBEEF5;
  padding: 5px;
  margin: 2px;
}

::v-deep .el-tabs__header {
  margin-bottom: 0;
}

::v-deep .el-date-editor.el-input {
  width: 180px;
}
</style>

