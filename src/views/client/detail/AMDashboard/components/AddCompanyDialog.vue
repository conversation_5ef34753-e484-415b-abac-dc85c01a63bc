<template>
  <el-dialog
    title="Add Company"
    :visible.sync="dialogVisible"
    width="400px"
  >
    <el-select
      v-model="data"
      :loading="loading"
      :remote-method="search"
      multiple
      filterable
      remote
      default-first-option
      value-key="id"
      placeholder="Search"
      no-data-text="No matching data"
      class="remote-select w-100"
    >
      <el-option
        v-for="item in list"
        :key="item.id"
        :label="item.name"
        :value="item">
        {{ item.name }}
      </el-option>
    </el-select>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button type="primary" :loading="submitting" @click="confirmUpdateCompany">Confirm</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ClientAPI from '@/api/client'
import CompanyAPI from '@/api/company'
import ProjectAPI from '@/api/project'

export default {
  name: 'AddCompanyDialog',
  props: {
    'client': Object,
    'project': Object,
    'companyList': Array,
    'topicList': Array,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      submitting: false,
      data: [],
      list: [],
    }
  },
  computed: {
    originData() {
      return this.project || this.client
    },
    isClient() {
      return !this.client.client_id
    },
  },
  methods: {
    show(type) {
      this.type = type
      this.dialogVisible = true
      this.data = this.companyList
        .map(item => ({
          ...item.advisor_tag,
          origin_id: item.id,
        }))
      this.list = [...this.data]
    },
    search(val) {
      if (!val) return

      this.loading = true
      const params = {
        page: 1,
        size: 20,
        keyword: val,
      }
      return CompanyAPI.getCompanyListByEsDb(params, '?extra=advisor_tag_map')
        .then(({ list }) => {
          this.list = list
        }).finally(() => {
          this.loading = false
        })
    },
    async confirmUpdateCompany() {
      try {
        this.submitting = true
        const params = {
          id: this.originData.id,
          advisor_tag_maps: [
            ...this.topicList.map(item => ({ id: item.id, advisor_tag_id: item.advisor_tag.id })),
            ...this.data.map(item => {
              if (item.advisor_tag_map) {
                return {
                  id: item.origin_id || undefined,
                  advisor_tag_id: item.advisor_tag_map.tag_id,
                }
              } else {
                return {
                  advisor_tag: {
                    name: item.name,
                    category: 'COMPANY',
                    company_maps: [{ company_id: item.id }],
                  },
                }
              }
            }),
          ],
        }
        let res
        if (this.project) {
          res = await ProjectAPI.updateProject(params)
        } else {
          res = this.isClient ? await ClientAPI.updateClient(params) : await ClientAPI.updateClientContact(
            this.client.client_id, this.client.id, params)
        }
        this.$emit('update', res.advisor_tag_maps)
        this.dialogVisible = false
      } finally {
        this.submitting = false
      }
    },
  },
}
</script>
