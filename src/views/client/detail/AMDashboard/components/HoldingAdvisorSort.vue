<template>
  <el-dropdown
    trigger="click"
    placement="bottom-start"
    @command="$emit('update:sort', $event)"
  >
    <el-button type="default">
      <i class="el-icon-sort" />
      Sort By {{ getSortLabel() }}
      <i class="el-icon-arrow-down el-icon--right" />
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="item in options.sort"
        :key="item.value"
        :command="item.value">
        {{item.label}}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'HoldingAdvisorSort',
  props: {
    sort: String,
  },
  data() {
    return {
      options: {
        sort: [{
          label: 'Relevance',
          value: 'match_type_bits desc,latest_sq_submit_time desc',
        }, {
          label: 'Screened Date',
          value: 'latest_sq_submit_time desc',
        }],
      },
    }
  },
  methods: {
    getSortLabel() {
      return this.options.sort.find(item => item.value === this.sort)?.label
    },
  },
}
</script>

<style scoped lang="scss">
</style>
