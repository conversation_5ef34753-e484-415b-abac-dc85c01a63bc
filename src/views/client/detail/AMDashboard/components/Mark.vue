<template>
  <div>
    <a @click="change">
      <div class="circle-btn">
        <div
          class="circle"
          :class="flag | getLabel(mark_options)" />
      </div>
    </a>
  </div>
</template>

<script>
import ConsultationAPI from '@/api/consultant'
export default {
  name: 'DashboardMark',
  props: {
    data: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      flag: 'INITIAL',
      mark_options: [
        { value: 'INITIAL', label: 'info' },
        { value: 'ORANGE', label: 'warning' },
        { value: 'GREEN', label: 'success' },
        { value: 'RED', label: 'danger' },
      ],
    }
  },
  mounted() {
    this.flag = this.data.shared_display_option?.flag || 'INITIAL'
  },
  methods: {
    change() {
      const old_index = this.mark_options.findIndex(item => item.value === this.flag)
      const target_index = old_index + 1 === this.mark_options.length ? 0 : old_index + 1

      const params = [{
        'tag_id': this.data.tag_id,
        'advisor_id': this.data.advisor_id,
        'user_id': 0,
        'hidden': false,
        'flag': this.mark_options[target_index].value,
        'relation_type_name': 'PROJECT_SQ',
      }]
      return ConsultationAPI.advisorTagsProjectSQOptions(params)
        .then(data => {
          this.flag = data[0].flag
        })
    },
  },
}
</script>

<style scoped lang="scss">
.circle-btn {
  width: 20px;
  padding: 5px;
  border-radius: 5px;

&:hover {
   cursor: pointer;
   background: #e2e6ea;
 }

.circle {
  width: 10px;
  height: 10px;
  -moz-border-radius: 50px;
  -webkit-border-radius: 50px;
  border-radius: 50%;

&.danger {
   background: #F56C6C;
 }

&.success {
   background: #67c23a;
 }

&.warning {
   background: #e6a23c;
 }

&.info {
   background: #909399;
 }
}
}
</style>
