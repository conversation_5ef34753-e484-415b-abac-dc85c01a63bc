<template>
  <div class="employment">
    <div v-for="(item, index) in formatJobs()" :key="item.id">
      <div>
        {{
          `${item.position} ${getStockCode(item.company)} | ${jobDateFormat(item.start_date)} -
                  ${item.is_current ? 'Current' : jobDateFormat(item.end_date)} `
        }}
      </div>
      <div class="flex align-items-center justify-content-between">
        <router-link-company :data="item.company" />
        <el-popover
          v-if="index + 1 === formatJobs().length && employments.length"
          placement="right"
          trigger="hover"
        >
          <div style="max-width: 50vw; overflow: auto;">
            <div v-for="job in sorted_employments" :key="job.id">
              <div>
                {{
                  `${job.position} ${getStockCode(job.company)} | ${jobDateFormat(job.start_date)} -
                  ${job.is_current ? 'Current' : jobDateFormat(job.end_date)} `
                }}
              </div>
              <router-link-company :data="job.company" />
            </div>
          </div>
          <el-link
            slot="reference"
            type="primary"
            :underline="false"
            icon="el-icon-more"
          />
        </el-popover>
      </div>
      <el-divider v-if="!index && formatJobs().length === 2" />
    </div>
  </div>
</template>

<script>
import RouterLinkCompany from '@/components/RouterLink/Company'
import _ from 'lodash'
import moment from 'moment-timezone'

export default {
  name: 'Employment',
  components: { RouterLinkCompany },
  props: {
    employments: Array,
    ticker: String,
    tab: String,
    tag: Object,
  },
  data() {
    return {
      sorted_employments: [],
    }
  },
  created() {
    this.sorted_employments = _.orderBy(this.employments, 'start_date', 'desc')
  },
  methods: {
    formatJobs() {
      const current_jobs = this.employments.filter(item => item.is_current)
      const sorted_current_jobs = _.orderBy(current_jobs, 'start_date', 'desc')
      const former_jobs = this.employments.filter(item => !item.is_current)
      const sorted_former_jobs = _.orderBy(former_jobs, 'start_date', 'desc')

      let result = []
      if (this.tab === 'Screened') {
        switch (current_jobs.length) {
          case 0:
            result = _.orderBy(this.employments, 'start_date', 'desc').slice(0, 2)
            break
          case 1:
            result = [...current_jobs, ...sorted_former_jobs.slice(0, 1)]
            break
          default:
            result = sorted_current_jobs.slice(0, 2)
        }
      } else {
        if (sorted_current_jobs.length) {
          result.push(sorted_current_jobs[0])
        }
        let ticker_arr = [this.ticker]
        if (this.tag?.third_party_company_maps?.length) {
          ticker_arr = [this.ticker, ...this.tag.third_party_company_maps.map(m => m.third_party_company.ticker)].filter(ticker => ticker)
        }
        const target_former = former_jobs.find(item => ticker_arr.includes(item.company.stock_code))
        result = [...result, target_former || sorted_former_jobs[0]]
      }
      return result
    },

    jobDateFormat(date) {
      if (!date) return ''
      const list = date.split('-')
      switch (list.length) {
        case 2:
          if (!list[1] || ['undefined', 'null', ' '].includes(list[1])) {
            return moment(list[0])
              .format('YYYY')
          } else {
            return moment(date)
              .format('MMM, YYYY')
          }
        case 3:
          return moment(date)
            .format('MMM DD, YYYY')
        default:
          return date
      }
    },
    getStockCode(company) {
      if (company && company.exchange) {
        return `, ${company.exchange} - ${company['stock_code']}`
      }
      if (company && company.type === 'PRIVATE') {
        return ', Private'
      }

      return ''
    },
  },
}
</script>

<style scoped>
::v-deep .el-divider--horizontal {
  margin: 6px 0;
}
</style>
