<template>
  <el-dialog
    title="Add Topic"
    :visible.sync="dialogVisible"
    width="400px"
  >
    <el-select
      v-model="data"
      :loading="loading"
      :remote-method="search"
      multiple
      filterable
      remote
      allow-create
      default-first-option
      value-key="id"
      placeholder="Search"
      no-data-text="No matching data"
      class="remote-select w-100"
    >
      <el-option
        v-for="item in list"
        :key="item.id"
        :label="item.name"
        :value="item">
        {{ item.name }}
      </el-option>
    </el-select>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button type="primary" :loading="submitting" @click="confirmUpdateTopic">Confirm</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import ClientAPI from '@/api/client'
import ProjectAPI from '@/api/project'

export default {
  name: 'AddTopicDialog',
  props: {
    'client': Object,
    'project': Object,
    'companyList': Array,
    'topicList': Array,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      submitting: false,
      data: [],
      list: [],
    }
  },
  computed: {
    originData() {
      return this.project || this.client
    },
    isClient() {
      return !this.client.client_id
    },
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.data = this.topicList
        .map(item => ({
          ...item.advisor_tag,
          origin_id: item.id,
        }))
      this.list = [...this.data]
    },
    search(val) {
      if (!val) return

      this.loading = true
      const params = {
        page: 1,
        size: 20,
        category: 'OTHER',
        name_contains: val,
      }
      return ConsultantAPI.getAdvisorTagsByTicker(params)
        .then(({ list }) => {
          this.list = list
        }).finally(() => {
          this.loading = false
        })
    },
    async confirmUpdateTopic() {
      try {
        this.submitting = true

        const params = {
          id: this.originData.id,
          advisor_tag_maps: [
            ...this.companyList.map(item => ({ id: item.id, advisor_tag_id: item.advisor_tag.id })),
            ...this.data.map(item => {
              if (typeof item !== 'string') {
                return {
                  id: item.origin_id || undefined,
                  advisor_tag_id: item.id,
                }
              } else {
                return {
                  advisor_tag: {
                    name: item,
                  },
                }
              }
            }),
          ],
        }
        let res
        if (this.project) {
          res = await ProjectAPI.updateProject(params)
        } else {
          res = this.isClient ? await ClientAPI.updateClient(params) : await ClientAPI.updateClientContact(this.client.client_id,
            this.client.id, params)
        }
        this.$emit('update', res.advisor_tag_maps)
        this.dialogVisible = false
      } finally {
        this.submitting = false
      }
    },
  },
}
</script>
