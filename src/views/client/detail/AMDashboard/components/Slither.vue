<template>
  <el-carousel v-if="list.length" :autoplay="false" :arrow="showArrow" indicator-position="none">
    <el-carousel-item v-for="(item,index) in list" :key="index">
      <div class="item-box scrollbar-narrow">

        <div v-if="type === 'SQ'">
          <div class="color-black">
            <b v-html="item.question_title" />
            <el-tag v-if="index===list.length-1 && showArrow==='always'" type="info" class="xs-tag" size="mini">end</el-tag>
          </div>
          <div class="answer-bgc" v-html="item.answer_text" />
        </div>

        <div v-if="type === 'Project'">
          <router-link-project :data="item" wrap />
          <el-tag v-if="index===list.length-1 && showArrow==='always'" type="info" class="xs-tag" size="mini">end</el-tag>
          <div class="color-black">{{item.map_task.create_at | momentFormat('MMM DD, YYYY')}} |
            {{ getAdvisorStatusInTask(item) }}</div>
          <div class="color-black">
            <span v-html="getMatchInfo(item, true)" class="pre-line"></span>
            <span v-if="item.match_types.includes('SQ_PROJECT_MATCH_REQUEST_CONTENT')">
              <el-tooltip placement="top" effect="light">
                <div slot="content" v-html="getMatchInfo(item)" class="pre-line" style="max-width: 80vw;" />
                <el-button type="text" style="padding: 0;">View</el-button>
              </el-tooltip>
            </span>
          </div>
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import { highlightHtml } from '@/utils/tool'
export default {
  name: 'SQSlither',
  components: { RouterLinkProject },
  props: {
    list: {
      type: Array,
      default() {
        return []
      },
    },
    type: String,
  },

  computed: {
    showArrow() {
      return this.list.length > 1 ? 'always' : 'never'
    },
  },

  methods: {
    getMatchInfo(item, intercept) {
      if (item.match_types.includes('SQ_PROJECT_MATCH_NAME')) { return '' }
      if (item.match_types.includes('SQ_PROJECT_MATCH_INVESTMENT_TARGET')) {
        return `<b>Investment Target :</b> ${this.getHighlightedContent(item.investment_target_companies.map(item => item.name).join(', '), item.tag)}`
      }
      if (item.match_types.includes('SQ_SUMMARY')) {
        return `<b>Summary :</b> ${this.getHighlightedContent(item.map_task.screening_summary, item.tag)}`
      }
      if (item.match_types.includes('SQ_PROJECT_MATCH_REQUEST_CONTENT')) {
        return `<b>Request :</b> ${this.getHighlightedContent(item.request_content, item.tag, intercept)}`
      }
    },

    getHighlightedContent(content, tag, intercept) {
      const highlight_match_words = [
        ...tag.third_party_company_maps.map(item => item.third_party_company.ticker),
        ...tag.aliases.map(item => item.alias),
        tag.name,
      ].filter(item => item?.length > 1)
        .sort((a, b) => b.length - a.length)
      let result = highlightHtml(content, highlight_match_words, 'g')

      if (!intercept) {
        return result
      } else {
        const sub_number = 100

        highlight_match_words.some(word => {
          const index = content.indexOf(word)
          if (index > -1) {
            const suffix_ellipsis = index + word.length + sub_number < content.length ? '...' : ''
            const suffix = content.substr(index + word.length, sub_number) + suffix_ellipsis
            const prefix_ellipsis = index - sub_number > 0 ? '...' : ''
            const prefix = prefix_ellipsis + content.substring(index >= sub_number ? index - sub_number : 0, index)
            result = highlightHtml(prefix + word + suffix, [word], 'g')
            return true
          }
        })
        return result
      }
    },

    // If the expert is attached but has not been sent or answered screeners, has not been sent to the client, and has not been selected, display “Attached”.
    // If the expert has been sent screeners, but has not answered them, has not been sent to the client or selected, display “Sent Screeners”
    // If the expert has been sent screeners and has answered screeners, but has not been sent to client or selected or scheduled, display “Screened”.
    // If the expert has been sent to client but has not been selected or scheduled or completed, display “Sent to Client”
    // If the expert has been selected, but has not been scheduled or completed, display “Client Selected”.
    // If the expert has been scheduled but the call is not completed, display “Scheduled”.
    // If the expert has been completed, display “Call Completed”
    getAdvisorStatusInTask(item) {
      const { general_status, sq_status, client_contact_status, scheduling_status } = item.map_task
      if (general_status === 'COMPLETED') {
        return 'Call Completed'
      }

      if (['ARRANGED', 'SCHEDULED'].includes(general_status) || ['SCHEDULED'].includes(scheduling_status)) {
        return 'Scheduled'
      }

      if (client_contact_status === 'APPROVED') {
        return 'Client Selected'
      }
      if (client_contact_status === 'SENT') {
        return 'Sent to Client'
      }
      if (sq_status === 'RESPONDED') {
        return 'Screened'
      }
      if (sq_status === 'SENT') {
        return 'Sent Screeners'
      }

      return 'Attached'
    },
  },
}
</script>

<style scoped lang="scss">
.xs-tag{
  padding:0 2px;
}

.item-box{
  overflow: auto;
  height: 100%;
}
.color-black{
  color: #333333;
}
.answer-bgc{
  margin-top: 3px;
  padding:0 4px;
  border-radius: 2px;
  background-color: #f3f2f2;
}

::v-deep .el-carousel__container{
  height:150px;
}

::v-deep .el-carousel__item{
  padding-left: 16px;
  padding-right: 14px;
}

::v-deep .el-carousel__arrow--left{
  left: -14px;
}

::v-deep .el-carousel__arrow--right{
  right: -14px;
}

::v-deep .el-carousel__arrow{
  border:0;
  outline:0;
  background-color: unset;
  position: absolute;
  top:50%;
  color: unset;
  z-index: 10;
  font-size: 14px;
}
</style>
