<template>
  <div>
    <el-table
      ref="holdingMultipleTable"
      v-loading="loading"
      row-key="id"
      :data="tableData.data"
      stripe
      fit
      highlight-current-row
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData.data, $refs.holdingMultipleTable)"
      @sort-change="handleActivitySort"
      v-on="$listeners"
    >
      <el-table-column v-if="allowCustomSort" key="move" width="35">
        <i class="el-icon-rank cursor-move fs-12" />
      </el-table-column>
      <el-table-column type="selection" min-width="45" />
      <el-table-column v-if="isListView" key="ticker" prop="name" label="Ticker" width="100">
        <template slot-scope="{row}">
          <div class="mb-5px">
            {{ row.tag.name }}
            <slot v-if="row.tag.third_party_company_maps.length">
              (<span v-for="(m, index) in row.tag.third_party_company_maps" :key="m.id"><slot
                v-if="index">,</slot>{{ m.third_party_company.ticker }}</span>)
            </slot>
          </div>
          <div v-if="row.holding_current_percent_of_portfolio" class="success">{{ row.holding_current_percent_of_portfolio }} %</div>
          <div v-if="row.holding_qtr_first_owned" class="info">{{ row.holding_qtr_first_owned | momentFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="Name" width="100">
        <template slot-scope="{row}">
          <router-link-advisor :data="row.advisor" />
          <br>
          <el-tooltip
effect="dark"
                      :open-delay="300"
                      placement="top-start">
            <div slot="content" style="max-width:400px;">
              <span v-html="$sanitizeHtml(row.advisor.background)" />
            </div>
            <el-icon class="el-icon-document primary" />
          </el-tooltip>
          <NewAdvisorTag :advisor="row.advisor" />
        </template>
      </el-table-column>
      <el-table-column width="40" label="Mark">
        <template slot-scope="{row}">
          <dashboard-mark :data="row" />
        </template>
      </el-table-column>
      <el-table-column prop="id" label="Employment" min-width="145">
        <template slot-scope="{row}">
          <employment
            :employments="row.advisor.jobs"
            :ticker="stockTicker"
            :tag="row.tag"
            :tab="activeTabLabel"
          />
        </template>
      </el-table-column>
      <el-table-column label="Relevant Project(s)" min-width="145">
        <template slot-scope="{row}">
          <slither type="Project" :list="row.match_projects" />
        </template>
      </el-table-column>
      <el-table-column label="Relevant Screening Question Response(s)" min-width="145">
        <template slot-scope="{row}">
          <slither v-if="row.match_sq_list.length" type="SQ" :list="row.match_sq_list" />
          <slither v-else type="SQ" :list="row.all_sq_list" />
        </template>
      </el-table-column>
      <el-table-column label="Activity" align="center">
        <el-table-column label="Scr" min-width="40" align="center" sortable="custom" :sort-orders="sortOrders">
          <template slot-scope="{row}">
            <span v-if="row.advisor.activity">{{row.advisor.activity.screened_proj_count}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Sen" min-width="40" align="center" sortable="custom" :sort-orders="sortOrders">
          <template slot-scope="{row}">
            <span v-if="row.advisor.activity">{{row.advisor.activity.sent_proj_count}}</span>
          </template>
        </el-table-column>
        <el-table-column label="Sel" min-width="40" align="center" sortable="custom" :sort-orders="sortOrders">
          <template slot-scope="{row}">
            <span v-if="row.advisor.activity">{{row.advisor.activity.selected_proj_count}}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="Actions" min-width="45">
        <template slot-scope="{row}">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteAdvisor(row)"
          >
            <el-link slot="reference" :disabled="row.delete_loading" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="customPageSize.length"
      :total="tableData.count"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :auto-scroll="false"
      :page-sizes="customPageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import Pagination from '@/components/Pagination'
import Employment from './components/Employment'
import Slither from './components/Slither'
import NewAdvisorTag from '@/components/NewAdvisorTag'
import DashboardMark from './components/Mark'
import ConsultationAPI from '@/api/consultant'
import _ from 'lodash'
import { highlightHtml } from '@/utils/tool'

export default {
  name: 'HoldingAdvisorTable',
  components: { RouterLinkAdvisor, Pagination, Employment, Slither, NewAdvisorTag, DashboardMark },
  props: {
    isListView: Boolean,
    isCollapse: Boolean,
    stockTicker: String,
    tagIds: [Number, String],
    activeTabLabel: {
      type: String,
      default: 'Screened',
    },
    allFilter: {
      type: Object,
      default() {
        return {}
      },
    },
    screenedFilter: {
      type: Object,
      default() {
        return {}
      },
    },
    formersFilter: {
      type: Object,
      default() {
        return {}
      },
    },
    listViewFilter: {
      type: Object,
      default() {
        return {}
      },
    },
    holdingList: Array,
    allowCustomSort: Boolean,
  },
  data() {
    return {
      customPageSize: [10, 20, 50, 100, 200, 300],
      loading: false,
      tableData: {
        data: [],
        count: 0,
      },
      searchQuery: {
        page: 1,
        size: 20,
        extra: [
          'shared_display_option',
          'tag.aliases',
          'tag.third_party_company_maps.third_party_company',
          'advisor.jobs.company',
          'advisor.tc_term_valid',
          'advisor.activity',
        ].join(),
      },
      sortOrders: ['descending', null],
      activitySort: '',
    }
  },
  computed: {
    isActive() {
      return (!this.isListView && this.isCollapse) || this.isListView
    },
    isScreenedTab() {
      return this.activeTabLabel === 'Screened'
    },
  },
  watch: {
    'activeTabLabel': {
      handler() {
        this.resetList()
      },
    },
    'isCollapse': {
      handler() {
        this.resetList()
      },
    },
    'isListView': {
      handler() {
        this.resetList()
      },
      immediate: true,
    },
    'tagIds': {
      handler() {
        this.resetList()
      },
    },
    'allFilter': {
      handler() {
        this.resetList()
      },
      deep: true,
    },
    'screenedFilter': {
      handler() {
        if (this.isScreenedTab) {
          this.resetList()
        }
      },
      deep: true,
    },
    'formersFilter': {
      handler() {
        if (!this.isScreenedTab) {
          this.resetList()
        }
      },
      deep: true,
    },
    'listViewFilter': {
      handler() {
        if (this.isListView) {
          this.resetList()
        }
      },
      deep: true,
    },
  },
  methods: {
    getList() {
      const requestName = this.isScreenedTab ? 'advisorTagsProjectSQ' : 'advisorTagsJobExperience'
      const projectSqKey = this.isScreenedTab ? 'maps' : 'map_project_sq.maps'

      const { page, size, extra } = this.searchQuery
      const params = {
        page, size,
        extra: [
          extra,
          projectSqKey + '.sq_instance',
          projectSqKey + '.task',
          projectSqKey + '.project.investment_target_companies',
        ].join(),
        'tag.ids': this.tagIds,
        hid: false,
        advisor_not_in_project_id: this.allFilter.advisor_not_in_project_id ? this.$route.params.project_id : undefined,
        ...this.formatProjectStatus(),
      }
      if (!this.isScreenedTab) {
        params['advisor.latest_former_job.end_date_gte'] = this.formersFilter.start_date_gte || undefined
        params['advisor.latest_former_job.end_date_lte'] = this.formersFilter.end_date_lte || undefined
        params['advisor.same_current_and_former_employer'] = this.formersFilter.same_current_and_former_employer
      } else {
        params['sort'] = this.isListView ? this.screenedFilter.sort : ('shared_display_order desc,' + this.screenedFilter.sort)
      }
      if (this.activitySort) {
        params['sort'] = this.activitySort
      }
      if (this.isListView) {
        params['advisor.id'] = this.listViewFilter.advisor_id || undefined
      }
      this.loading = true
      return ConsultationAPI[requestName](params)
        .then(data => {
          this.tableData.data = data.list.map(item => {
            const projects = []
            const sq_list = []
            const all_sq_list = []
            const maps = item.maps || item.map_project_sq?.maps
            maps?.forEach(temp => {
              if (temp.match_types.includes('SQ_QUESTION')) {
                sq_list.push(temp.sq_instance)
              } else {
                all_sq_list.push(temp.sq_instance)
              }

              // const match_project_types = temp.match_types.filter(i => i !== 'SQ_QUESTION')
              const match_project_types = temp.match_types
              if (match_project_types.length > 0) {
                const include_index = projects.findIndex(i => i.id === temp.project.id)
                if (include_index > -1) {
                  projects[include_index].match_types = _.uniq([projects[include_index].match_types, ...match_project_types])
                } else {
                  projects.push(Object.assign(temp.project, { match_types: match_project_types, map_task: temp.task, tag: item.tag }))
                }
              }
            })
            item.match_projects = projects
            item.match_sq_list = this.formatSQList(sq_list, item.tag, true)
            item.all_sq_list = this.formatSQList(all_sq_list, item.tag, false)

            if (this.isListView) {
              const holding = this.holdingList.find(holding => holding.tag_id === item.tag_id)
              item.holding_current_percent_of_portfolio = holding?.current_percent_of_portfolio
              item.holding_qtr_first_owned = holding?.qtr_first_owned
            }

            return item
          })
          this.tableData.count = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    formatProjectStatus() {
      const obj = {
        'maps.task.general_status_in': [],
        'maps.task.client_contact_status_in': [],
      }
      if (this.allFilter.project_status.includes('Call Completed')) {
        obj['maps.task.general_status_in'].push('COMPLETED')
      }
      if (this.allFilter.project_status.includes('Scheduled')) {
        obj['maps.task.general_status_in'] = [...obj['maps.task.general_status_in'], 'ARRANGED', 'SCHEDULED']
        obj['maps.task.scheduling_status'] = 'SCHEDULED'
      }
      if (this.allFilter.project_status.includes('Sent to Client')) {
        obj['maps.task.client_contact_status_in'].push('SENT')
      }
      if (this.allFilter.project_status.includes('Screened')) {
        obj['maps.task.client_contact_status_in'].push('INITIAL')
        obj['maps.task.sq_status'] = 'RESPONDED'
      }

      obj['maps.task.general_status_in'] = obj['maps.task.general_status_in'].join(',') || undefined
      obj['maps.task.client_contact_status_in'] = obj['maps.task.client_contact_status_in'].join(',') || undefined

      return obj
    },

    formatSQList(sq_instances, tag, needMatch) {
      const highlight_match_words = [
        ...tag.third_party_company_maps.map(item => item.third_party_company.ticker),
        ...tag.aliases.map(item => item.alias),
        tag.name,
      ].filter(item => item?.length > 1)
      let result = []
      if (sq_instances.length > 0) {
        const sq_list = []
        sq_instances.forEach(item => { sq_list.push(...item.qa_list_snapshot) })
        sq_list.forEach(item => {
          item.answer_text = formatAnswer(item)
        })
        const highlight_reg = new RegExp(`${highlight_match_words.join('|')}`, 'i')
        result = needMatch ? sq_list.filter(item => highlight_reg.test(item.answer_text) || highlight_reg.test(item.title_text)) : sq_list

        result.forEach(item => {
          item.answer_text = highlightHtml(item.answer_text, highlight_match_words, 'g')
          item.question_title = highlightHtml(item.title_text, highlight_match_words, 'g')
        })
      }
      return result

      function formatAnswer(sq) {
        if (!sq.answer || !sq.answer.content) return ''
        if (sq.type === 'RANKING_WITH_EXPLANATION') {
          return `${sq.answer.content.rank} - ${sq.answer.content.result}`
        } else if (sq.type === 'RANKING_SET') {
          return sq.answer.content.result.map(item => `${item.option}: ${item.rank} - ${item.text}`).join('<br/>')
        } else if (sq.type === 'NO_RANKING_SET') {
          return sq.answer.content.result.map(item => `${item.option}: ${item.text}`).join('<br/>')
        } else if (sq.type === 'CHECKBOX') {
          return sq.answer.content.result.map(ans => (sq.options[ans].text)).join('; ')
        } else if (['RADIO', 'YES_NO'].includes(sq.type)) {
          return sq.options[sq.answer.content.result]?.text
        } else {
          return sq.answer.content.result
        }
      }
    },

    deleteAdvisor(row) {
      const params = [{
        'tag_id': row.tag_id,
        'advisor_id': row.advisor_id,
        'user_id': 0,
        'hidden': true,
        'relation_type_name': 'PROJECT_SQ',
      }]
      row.delete_loading = true
      return ConsultationAPI.advisorTagsProjectSQOptions(params)
        .then(() => {
          row.delete_loading = false
          this.getList()
        })
        .finally(() => {
          row.delete_loading = false
        })
    },

    resetList() {
      if (!this.isActive) return

      this.searchQuery.page = 1
      this.tableData.data = []
      this.tableData.count = 0
      if (!this.tagIds) {
        return
      }
      this.getList()
    },

    clearSelection() {
      this.$refs.holdingMultipleTable.clearSelection()
    },

    handleActivitySort({ column, prop, order }) {
      const dict = {
        'ascending': 'asc',
        'descending': 'desc',
      }
      const indicator = {
        'Scr': 'advisor.activity.screened_proj_count ',
        'Sen': 'advisor.activity.sent_proj_count ',
        'Sel': 'advisor.activity.selected_proj_count ',
      }
      this.activitySort = ''
      if (order) {
        this.activitySort = indicator[column.label] + dict[order]
      }
      this.getList()
    },
  },
}
</script>

<style scoped>

</style>
