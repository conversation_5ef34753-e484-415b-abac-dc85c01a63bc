<template>
  <div>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="flex justify-content-between align-items-center">
        <div>
          <div class="holding-item-title">
            <el-link
              :underline="false"
              class="f-14"
              style="vertical-align: baseline;"
              @click="handleCollapseClick"
            >
              {{ holding.advisor_tag.name }}
              <slot v-if="holding.advisor_tag.third_party_company_maps.length">
                (<span v-for="(m, index) in holding.advisor_tag.third_party_company_maps" :key="m.id"><slot
                  v-if="index">,</slot>{{ m.third_party_company.ticker }}</span>)
              </slot>
            </el-link>
            <ico-button
              class="filter-item"
              tooltip="Collapse"
              no-border
              is-rotate
              :deg="90"
              @action="handleCollapseClick">
              <el-icon slot="ico" class="el-icon-arrow-right" />
            </ico-button>

            <slot v-if="holding.advisor_tag">
              <el-tag
                v-if="(!holding.advisor_tag.project_sq_evaluating && !holding.advisor_tag.project_sq_stale && isScreenedTab) || (!holding.advisor_tag.job_exp_evaluating && !holding.advisor_tag.job_exp_stale && !isScreenedTab)"
                type="success"
              >
                Latest
              </el-tag>
              <el-tag
                v-if="(!holding.advisor_tag.project_sq_evaluating && holding.advisor_tag.project_sq_stale && isScreenedTab) || (!holding.advisor_tag.job_exp_evaluating && holding.advisor_tag.job_exp_stale && !isScreenedTab)"
                type="warning"
              >
                Stale
              </el-tag>
              <el-tag
                v-if="(holding.advisor_tag.project_sq_evaluating && isScreenedTab) || (holding.advisor_tag.job_exp_evaluating && !isScreenedTab)"
                type="primary"
              >
                Evaluating...
              </el-tag>
              <!--              <el-tooltip-->
              <!--                v-if="holding.advisor_tag_id"-->
              <!--                class="item"-->
              <!--                effect="dark"-->
              <!--                content="Regenerate Relations with the Latest Algorithm(Time Consuming)"-->
              <!--                placement="top-start">-->
              <!--                <el-button-->
              <!--                  :loading="reload_loading"-->
              <!--                  plain-->
              <!--                  :disabled="(holding.advisor_tag.project_sq_evaluating && isScreenedTab) || (holding.advisor_tag.job_exp_evaluating && !isScreenedTab)"-->
              <!--                  class="ml-0 el-button&#45;&#45;xs el-icon-refresh-left"-->
              <!--                  @click="reevaluateList" />-->
              <!--              </el-tooltip>-->
            </slot>
          </div>
        </div>
        <div>
          <el-tabs v-model="active_tab_label">
            <el-tab-pane label="Screened" name="Screened" />
            <el-tab-pane v-if="isCompanyType" label="Formers" name="Formers" />
          </el-tabs>
        </div>
      </div>

      <el-collapse-transition>
        <div v-show="isCollapse" style="margin: 14px;">
          <holding-advisor-table
            ref="holdingAdvisorTable"
            :is-collapse="isCollapse"
            :tag-ids="holding.advisor_tag.id"
            :active-tab-label="active_tab_label"
            v-bind="$attrs"
            v-on="$listeners"
          />
        </div>
      </el-collapse-transition>
    </el-card>
  </div>
</template>

<script>
import IcoButton from '@/components/IcoButton'
import HoldingAdvisorTable from './HoldingAdvisorTable'

export default {
  name: 'CompanyTopicItem',
  components: { IcoButton, HoldingAdvisorTable },
  props: {
    holding: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      reload_loading: false,
      isCollapse: false,
      advisor_sortable: null,
      active_tab_label: 'Screened',
    }
  },
  computed: {
    isCompanyType() {
      return this.holding.advisor_tag.category === 'COMPANY'
    },
    isScreenedTab() {
      return this.active_tab_label === 'Screened'
    },
  },
  methods: {
    handleCollapseClick() {
      this.isCollapse = !this.isCollapse
    },

    // reevaluateList() {
    //   const requestName = this.isScreenedTab ? 'reevaluateAdvisorTagsProjectSQ' : 'reevaluateAdvisorTagsJobExperience'
    //   this.reload_loading = true
    //   return ConsultationAPI[requestName](this.holding.advisor_tag_id)
    //     .then(data => {
    //       if (data) {
    //         this.$emit('update-tag', data)
    //       }
    //       return this.$refs.holdingAdvisorTable.getList()
    //     })
    //     .finally(() => {
    //       this.reload_loading = false
    //     })
    // },

    clearSelection() {
      this.$refs.holdingAdvisorTable.clearSelection()
    },
  },
}
</script>

<style scoped lang="scss">
.ml-0 {
  margin-left: 0 !important;
}

::v-deep .el-card__body {
  padding: 0;
}

::v-deep .el-tabs__header {
  margin-bottom: 0;
}

.holding-item-title {
  margin-bottom: 2px;
}
</style>
