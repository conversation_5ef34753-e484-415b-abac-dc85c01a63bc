<template>
  <div>
    <div class="filter-container">
      <el-input
        v-model="query.keyword"
        class="w-200px filter-item"
        placeholder="Keyword"
        @input="handleSearch"
      />
      <el-select
        v-model="query.types_contains_any"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Type"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.contact_person_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="query.status"
        clearable
        class="w-200px filter-item"
        placeholder="Status"
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.contact_status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <location
        v-model="query.location_ids"
        region
        class="w-200px filter-item inline-block"
        @change="handleSearch"
      />
      <el-input
        v-model="query['office.name_contains']"
        class="w-200px filter-item"
        placeholder="Office Name"
        @input="handleSearch" />
      <whale-wisdom-filer-search
        v-model="query['whale_wisdom_filer_maps.whale_wisdom_filer_ids']"
        placeholder="Funds"
        class="w-200px filter-item"
        @change="handleSearch"
      />
      <el-button
        v-if="!disabledEdit"
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="actionCreateContact()"
      >New
      </el-button>
      <OutreachMail
        v-show="selectedContacts.length"
        :client-id="client.id"
        :select-user="selectedContacts"
        :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro" />
      <el-dropdown
        trigger="click"
        placement="bottom-start"
        class="filter-item"
        @command="handleChangePortalAccessBatch">
        <el-button type="primary" icon="el-icon-lock" :loading="portalAccessLoading">
          Client Portal Access
          <slot v-if="selectedContacts.length">({{ selectedContacts.length }})</slot>
          <i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="true"><i class="el-icon-check" />Enable</el-dropdown-item>
          <el-dropdown-item :command="false"><i class="el-icon-close" />Disable</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-button
        v-show="selectedContacts.length"
        class="filter-item pull-right"
        type="primary"
        icon="el-icon-download"
        @click="actionDownloadSelected">
        Download
      </el-button>
    </div>
    <el-table
      ref="clientUserTable"
      v-loading="loading"
      border
      stripe
      :data="contactTableData"
      :row-class-name="rowSetIndex"
      @sort-change="sortData"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, contactTableData, $refs['clientUserTable'])"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" min-width="45" />
      <el-table-column label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :to="{ name:'ClientContactDetail', params: { contact_id: scope.row.id }}">
            {{ scope.row.name || '' }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Current Title"
        sortable="custom"
        prop="current_position">
        <template slot-scope="scope">
          {{ scope.row.current_title }}
        </template>
      </el-table-column>
      <el-table-column
        label="Contact"
        min-width="200">
        <template slot-scope="scope">
          <div class="flex">
            <div>
              <div v-for="(item, index) in scope['row'].contact_infos" :key="index">
                <span>{{ item.short_type }} :</span>
                <span v-if="scope['row'].display_view_button">{{ item.value }}</span>
                <span
                  v-if="!scope['row'].display_view_button"
                  :class="{'link': ['LINKEDIN_URL','EMAIL'].includes(item.type)}"
                  @click="openToLink(item.type, item.value)">
                  <a v-if="item.type === 'PHONE'" :href="'tel:'+item.value">{{ item.value }}</a>
                  <span v-else>{{ item.value }}</span>
                </span>
                <el-link
                  v-if="item.is_main"
                  :underline="false"
                  type="success"
                  class="el-icon-star-on vertical-inherit" />
              </div>
            </div>
            <div class="flex align-items-center">
              <el-link
                v-if="scope['row'].display_view_button"
                :underline="false"
                type="primary"
                class="hover-visible ml-8"
                @click="getContactInfo(scope['row'])">
                <el-icon name="view" />
              </el-link>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="Last Contact Time"
        sortable="custom"
        prop="last_communication_time">
        <template slot-scope="scope">
          {{ scope.row.last_communication_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
        </template>
      </el-table-column>
      <el-table-column label="Type">
        <template slot-scope="scope">
          {{ scope.row.types | getLabels(client_options.contact_person_type) }}
        </template>
      </el-table-column>
      <el-table-column v-if="needRoles" label="Role">
        <template slot-scope="scope">
          {{ scope.row.roles | getLabels(client_options.contact_bcg_roles) }}
        </template>
      </el-table-column>
      <el-table-column label="Office / Location" prop="office.name" sortable="custom">
        <template slot-scope="scope">
          <span v-if="scope.row.office_id"> {{ scope.row.office.name }}</span>
          <span v-if="scope.row.location_id"> {{ ' / ' + scope.row.location.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        width="100"
        prop="status"
        sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.status | getLabel(client_options.contact_status) }}
        </template>
      </el-table-column>
      <el-table-column label="Product" width="100">
        <template slot-scope="scope">
          {{ scope.row.product_of_interests | getLabels(client_options.product) }}
        </template>
      </el-table-column>
      <el-table-column label="Coverage" width="100">
        <template slot-scope="scope">
          {{ scope.row.coverages | getLabels(client_options.coverage) }}
        </template>
      </el-table-column>
      <el-table-column label="Funds" width="100">
        <template slot-scope="scope">
          <el-tooltip class="item" placement="left" :open-delay="200">
            <div slot="content">
              <div v-for="item in scope.row.whale_wisdom_filer_maps" :key="item.id">
                <span v-if="item.whale_wisdom_filer">{{ item.whale_wisdom_filer.filer_name }}</span>
              </div>
            </div>
            <div class="text-truncate cursor-pointer">
              <span v-for="item in scope.row.whale_wisdom_filer_maps" :key="item.id">
                <span v-if="item.whale_wisdom_filer">{{ item.whale_wisdom_filer.filer_name }}</span>
              </span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Project Active" width="100">
        <template slot-scope="scope">
          {{ scope.row.project_active_count }}
        </template>
      </el-table-column>
      <el-table-column label="Project Closed" width="100">
        <template slot-scope="scope">
          {{ scope.row.project_closed_count }}
        </template>
      </el-table-column>
      <el-table-column label="Current Month Billable Hours" width="100">
        <template slot-scope="scope">
          {{ scope.row.current_period_usage.current_month.billable_hour }}
        </template>
      </el-table-column>
      <el-table-column label="Quarterly Billable Hours" width="100">
        <template slot-scope="scope">
          {{ scope.row.current_period_usage.current_quarter.billable_hour }}
        </template>
      </el-table-column>
      <el-table-column label="Annual Billable Hours" width="100">
        <template slot-scope="scope">
          {{ scope.row.current_period_usage.current_year.billable_hour }}
        </template>
      </el-table-column>
      <el-table-column label="Enable Client Portal Access" width="70">
        <template slot-scope="scope">
          <el-link
            v-if="client.enable_client_portal"
            :underline="false"
            :type="scope.row.is_blocked ? 'danger' : 'success'"
            :class="scope.row.is_blocked ? 'el-icon-remove' : 'el-icon-success'"
            @click="handleChangePortalAccess(scope.row)" />
          <slot v-else>-</slot>
        </template>
      </el-table-column>
      <el-table-column v-if="!isSGDB" label="Recent Email Status" width="70">
        <template slot-scope="scope">
          <ContactOutreachStatus :item="scope.row" :email-status="scope.row.contact_recent_email_status" />
        </template>
      </el-table-column>
      <el-table-column
v-if="!isSGDB"
                       label="Opened"
                       prop="outreach_opened"
                       sortable="column"
                       :sort-orders="['descending', null]"
                       min-width="90">
        <template slot-scope="scope">
          <contact-outreach-opened :item="scope.row" />
        </template>
      </el-table-column>
      <el-table-column label="Actions" width="100">
        <template slot-scope="scope">
          <div>
            <el-link
              type="primary"
              :underline="false"
              :icon="scope.row.loading ? 'el-icon-loading' : 'el-icon-edit'"
              class="mr-8"
              @click="actionEditContact(scope.row)" />
            <add-task-dashboard show-clock :data-user="scope.row" :data-client="scope.row.client" class="mr-8" />
            <el-popconfirm
              icon="el-icon-info"
              icon-color="#F56C6C"
              title="Confirm to Delete？"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="danger"
              @confirm="deleteContact(scope.row)"
            >
              <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
            </el-popconfirm>
            <compliance-account-actions
              v-if="isLegalAccount(scope.row)"
              :id="scope.row.id"
              :disabled="legal_action_disabled" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="query.page"
      :limit.sync="query.size"
      @pagination="getList"
    />

    <create-contact-dialog
      ref="createContactDialog"
      :bcg="needRoles"
      @refresh="getList"
    />
  </div>
</template>

<script>
import { debounce } from '@/utils/tool'
import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'
import moment from 'moment-timezone'
import ClientAPI from '@/api/client'
import ToolAPI from '@/api/tool'
import ComplianceAPI from '@/api/compliance'
import Location from '@/components/Location'
import ComplianceAccountActions from '@/views/client/detail/components/ComplianceAccountActions'
import AddTaskDashboard from './components/AddTaskDashboard'
import OutreachMail from './components/OutreachMail'
import Pagination from '@/components/Pagination'
import WhaleWisdomFilerSearch from '@/components/SelectRemoteSearch/WhaleWisdomFilerSearch'
import DownloadExcel from '@/utils/download-excel'
import ContactOutreachOpened from '@/views/client/detail/components/ContactOutreachOpened.vue'
import ContactOutreachStatus from '@/views/client/detail/components/ContactOutreachStatus.vue'
import CreateContactDialog from '@/views/client/detail/components/CreateContactDialog.vue'

export default {
  name: 'Contact',
  components: {
    CreateContactDialog,
    ComplianceAccountActions,
    Pagination,
    AddTaskDashboard,
    Location,
    OutreachMail,
    ContactOutreachStatus,
    ContactOutreachOpened,
    WhaleWisdomFilerSearch,
  },
  props: {
    client: Object,
    disabledEdit: Boolean,
  },

  data() {
    return {
      client_id: this.$route.params.client_id,
      need_roles_client_ids: [],
      portalAccessLoading: false,
      loading: false,
      legal_action_disabled: false,
      contactTableData: [],
      total: 0,
      query: {
        keyword: undefined,
        'office.name_contains': undefined,
        status: undefined,
        types_contains_any: [],
        location_ids: [],
        'whale_wisdom_filer_maps.whale_wisdom_filer_ids': [],
        page: 1,
        size: 10,
      },
      selectedContacts: [],
      extra: [
        'contact_infos',
        'projects_agg',
        'current_period_usage',
        'office.location',
        'location',
        'last_communication_time',
        'jobs',
        'communication_records.client_contact_tracking',
        'email_address_score',
        'whale_wisdom_filer_maps.whale_wisdom_filer',
      ],
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
    }),

    sendToClientThroughCapvisionPro() {
      const has_rule = this.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        this.client.preference?.tpa_client
    },

    needRoles() {
      return this.client?.preference?.preferred_workflow === 'BCG' || this.need_roles_client_ids.includes(this.client?.id)
    },
  },

  watch: {
    'sendToClientThroughCapvisionPro': {
      handler(newVal) {
        if (newVal) {
          this.legal_action_disabled = true
        }
      }, immediate: true,
    },
  },

  mounted() {
    this.getList()
    this.getNeedRolesClients()
  },

  methods: {
    getList() {
      this.loading = true
      const params = Object.assign({}, this.query,
        { extra: this.extra.join() })
      params.keyword = params.keyword || undefined
      params['office.name_contains'] = params['office.name_contains'] || undefined
      params['whale_wisdom_filer_maps.whale_wisdom_filer_ids'] = params['whale_wisdom_filer_maps.whale_wisdom_filer_ids'].map(
        item => item.whale_wisdom_filer_id).join() || undefined
      params.status = params.status || undefined
      params.types_contains_any = this.formatParams(params.types_contains_any)
      params.location_ids = params.location_ids.join() || undefined

      return ClientAPI.getContactList(this.client.id, params)
        .then(data => {
          this.contactTableData = data.list.map(item => {
            item.loading = false
            item.contact_infos = item.contact_infos.map(info => {
              return {
                id: info.id,
                short_type: info.type === 'PHONE' ? 'P' : info.type === 'EMAIL' ? 'E' : 'L',
                type: info.type,
                value: info.value,
                is_main: info.is_main,
              }
            })

            item.display_view_button = item.contact_infos.length > 0

            item.project_closed_count = item.projects_agg['CLOSED']
            item.project_active_count = Object.values(item.projects_agg).reduce((a, b) => {
              return a + b
            }) - item.project_closed_count

            item.office_id = item.office_id || undefined

            item.current_title = this.handleClientUserTitle(item)

            this.formatMostRecentEmail(item)
            return item
          })
          this.total = data.count
          this.loading = false
        })
    },

    formatMostRecentEmail(item) {
      item.contact_recent_email_status = 'info'
      if (item.email_address_score) {
        item.contact_recent_email_status = item.email_address_score.score
          ? 'success'
          : 'danger'
      }
    },

    sortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        if (val.prop === 'office.name') {
          this.query.sort = `${val.prop}${order},location.name${order}`
        } else {
          this.query.sort = val.prop + order
        }
        this.getList()
      }
    },

    handleSearch() {
      debounce(() => {
        this.query.page = 1
        this.getList()
      }, 500)
    },

    openToLink(key, value) {
      if (key === 'LINKEDIN_URL') {
        window.open(value, '_blank')
      }
      if (key === 'EMAIL') {
        const aTag = document.createElement('a')
        aTag.href = `mailto:${value}`
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    },

    getContactInfo(row) {
      row.display_view_button = false
      const params = {
        ids: row.contact_infos.map(item => (item.id))
          .join(),
      }
      return ToolAPI.getContactInfo(params)
        .then(data => {
          row.contact_infos.forEach(item => {
            const contact = data['list'].find(list => list.id === item.id)
            if (contact) item.value = contact.value
          })
        }, () => {
          row.display_view_button = true
        })
        .finally(() => {
          row.show_detail = true
        })
    },

    actionCreateContact() {
      this.$refs['createContactDialog'].openDialog()
    },

    actionEditContact(item) {
      item.loading = true
      this.getContactInfo(item)
        .then(() => {
          this.$refs['createContactDialog'].openDialog(item)
        })
        .finally(() => {
          item.loading = false
        })
    },

    deleteContact(data) {
      let delete_index = ''

      this.contactTableData.map((item, index) => {
        if (item.id === data.id) {
          delete_index = index
        }
      })

      return ClientAPI.deleteClientContact(this.client_id, data.id)
        .then(() => {
          this.contactTableData.splice(delete_index, 1)
        })
    },

    isLegalAccount(data) {
      return data.types.includes('LEGAL')
    },

    rowSetIndex({ row, rowIndex }) {
      row.table_index = rowIndex
      return 'hover'
    },

    handleSelectionChange(val) {
      this.selectedContacts = val
    },

    actionDownloadSelected() {
      const test_config = {
        fields: {
          'Name': 'name',
          'Contact Info': {
            format: row => row.contact_infos
              .map(item => `${item.short_type}: ${item.value}${item.is_main ? '(Main)' : ''}`)
              .join(' '),
          },
          'Client User Type': {
            format: row => {
              return this.$options.filters.getLabels(row.types, this.client_options.contact_person_type)
            },
          },
          'Office / Location': {
            format: row => {
              return `${row.office?.name || ''} / ${row.location?.name || ''}`
            },
          },
          'Status': {
            format: row => {
              return this.$options.filters.getLabel(row.status, this.client_options.contact_status)
            },
          },
          'Projects Active': 'project_closed_count',
          'Projects Closed': 'project_active_count',
          'Current Month Billable Hours': 'current_period_usage.current_month.billable_hour',
          'Quarterly Billable Hours': 'current_period_usage.current_quarter.billable_hour',
          'Annual Billable Hours': 'current_period_usage.current_year.billable_hour',
          'Create Date': {
            format: row => moment.tz(row.create_at, this.timeZone).format('MM/DD/YYYY'),
          },
        },
        data: this.selectedContacts,
        name: `Contacts - ${this.client.name}.csv`,
        header: ['This document and all information within is internal and confidential.\n'],
      }

      return DownloadExcel.download(test_config)
    },

    formatParams(list) {
      return (Array.isArray(list) && list || []).map(item => {
        return item
      })
        .join() || undefined
    },

    handleChangePortalAccess(item) {
      const params = {
        is_blocked: !item.is_blocked,
      }
      return ClientAPI.updateClientContact(this.client_id, item.id, params)
        .then(() => {
          item.is_blocked = !item.is_blocked
        })
    },
    handleClientUserTitle(item) {
      if (!item.jobs || item.jobs.length < 1) {
        return ''
      }
      // 取最近的工作经历的position 逻辑与后端保持一致
      const current_jobs = item.jobs.filter(i => i.is_current)
      if (current_jobs.length) {
        return current_jobs.length > 1 ? sortByStartOrPosition(current_jobs) : current_jobs[0].position
      } else {
        return ''
      }

      function sortByStartOrPosition(list) {
        const start_sort_list = _.orderBy(list, 'start', 'desc')
        if (start_sort_list[0].start === start_sort_list[1].start) {
          const same_start_list = current_jobs.filter(item => item.start === start_sort_list[0].start)
          return _.orderBy(same_start_list, 'position', 'asc')[0].position
        } else {
          return _.orderBy(list, 'start', 'desc')[0].position
        }
      }
    },
    async handleChangePortalAccessBatch(val) {
      this.portalAccessLoading = true
      try {
        if (this.selectedContacts.length) {
          const params = {
            batch: this.selectedContacts.map(item => ({
              id: item.id,
              is_blocked: !val,
            })),
          }
          await ClientAPI.updateClientContactBatch(params)
          this.selectedContacts.forEach(contact => {
            contact.is_blocked = !val
          })
        } else {
          const params = {
            client_id: this.client_id,
            is_blocked: !val,
          }
          await ClientAPI.updateClientContactSetBlockBatch(params)
          this.contactTableData.forEach(contact => {
            contact.is_blocked = !val
          })
        }
      } finally {
        this.portalAccessLoading = false
      }
    },

    getNeedRolesClients() {
      return ComplianceAPI.getAppConfigEntriesByKey('DUE_DILEGENCE_USER_ROLES')
        .then(data => {
          if (data) {
            this.need_roles_client_ids = data.value.split(',').map(item => +item)
          }
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.contact-content {
  margin: 0 2px;
  color: #20a0ff;
}

.contact-value-input {
  width: 400px;
  margin-right: 10px;
}

.icon-font {
  font-size: 1.1rem;
  margin: 0 5px;

  span {
    font-size: 14px;
  }
}

.vertical-inherit {
  vertical-align: inherit;
}

::v-deep .el-radio__label {
  font-size: 12px;
}
</style>
