<template>
  <div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="usage_list"
    >
      <el-table-column width="140" label="Type" prop="type" />
      <el-table-column label="M1" prop="m_1" />
      <el-table-column label="M2" prop="m_2" />
      <el-table-column label="M3" prop="m_3" />
      <el-table-column label="M4" prop="m_4" />
      <el-table-column label="M5" prop="m_5" />
      <el-table-column label="M6" prop="m_6" />
      <el-table-column label="M7" prop="m_7" />
      <el-table-column label="M8" prop="m_8" />
      <el-table-column label="M9" prop="m_9" />
      <el-table-column label="M10" prop="m_10" />
      <el-table-column label="M11" prop="m_11" />
      <el-table-column label="M12" prop="m_12" />
      <el-table-column width="80" label="M Total" prop="m_total" />
      <el-table-column width="80" label="YTD" prop="YTD" />
      <el-table-column width="80" label="Growth" prop="growth" />
    </el-table>
  </div>
</template>

<script>
import ContractAPI from '@/api/contract'
import moment from 'moment/moment'
export default {
  name: 'ClientUsage',
  props: {
    clientId: String,
  },
  data() {
    return {
      loading: false,
      usage_list: [],
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      this.loading = true
      const params = {
        today_date: moment().format('YYYY-MM-DD'),
        client_id: this.clientId,
      }
      return ContractAPI.getRevenueClientUsage(params)
        .then(data => {
          this.usage_list = formatData(data.current_year, data.past_year)
        })
        .finally(() => {
          this.loading = false
        })

      function formatData(current, past) {
        const year = moment(Object.keys(current)[0]).format('YYYY')
        const past_year = moment(Object.keys(past)[0]).format('YYYY')
        const label = [
          { label: 'H', key: 'total_billing_hours' },
          { label: 'consultation', key: 'consultation_revenue' },
          { label: 'conference', key: 'conference_revenue' },
          { label: 'research', key: 'research_revenue' },
          { label: 'total', key: 'total_revenue' },
        ]
        const result = []
        const format = {}
        format[year] = {}
        format[past_year] = {}

        // 原始数据处理成 [m_1:{a:x,b:x,c:x},m_2:{a:x,b:x,c:x}...YTD:{a:x,b:x,c:x]
        formatOriginData(format[year], current)
        formatOriginData(format[past_year], past)

        // 按照label类型，生成不同年份的12个月对应key的数组 以及YTD total growth
        // ex. [ {type:5023 H, m_1:1, m_2:1,...YTD:2,growth:50%,m_total:10}]
        label.forEach(i => {
          [past_year, year].forEach(item => {
            const origin_data = format[item]
            const target = {
              type: `${item} ${i.label}`,
              YTD: origin_data.YTD[i.key],
              growth: null,
            }
            const m_list = []
            for (let m = 1; m <= 12; m++) {
              target['m_' + m] = origin_data['m_' + m] ? origin_data['m_' + m][i.key] : 0
              m_list.push(target['m_' + m])
            }
            target.m_total = m_list.reduce((total, row) => total + row, 0).toFixed(2)

            /* 计算当前年份较去年的增长率*/
            if (item === year) {
              const past_tear_ytd = format[past_year].YTD[i.key]
              const num = target.YTD - past_tear_ytd
              target.growth = Math.round(num / past_tear_ytd * 10000) / 100.00 + '%'

              if (num === 0) target.growth = 0
              if (past_tear_ytd === 0) target.growth = 'N/A'
            }

            result.push(target)
          })
        })
        return result

        function formatOriginData(target, origin) {
          Object.keys(origin).forEach(key => {
            const label = moment(key).isValid() ? `m_${moment(key).format('M')}` : key
            target[label] = origin[key]
          })
        }
      }
    },
  },
}
</script>

<style scoped>

</style>
