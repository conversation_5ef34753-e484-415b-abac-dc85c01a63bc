<template>
  <div>
    <el-form v-if="!isEdit"
             label-position="left"
             label-width="200px"
             class="app-container">
      <el-form-item label="Name">{{ client.name }}</el-form-item>
      <el-form-item label="Type">{{ client.type | getLabel(client_options.type) }}</el-form-item>
      <el-form-item label="Client Status">
        {{ client.status | getLabel(client_options.status) }}
      </el-form-item>

      <div v-if="display.prospect || display.opportunity">

        <!-- Custom: Prospect -->
        <div v-if="display.prospect">
          <el-form-item label="Potential">
            {{ client.potential | getLabel(client_options.potential) }}
          </el-form-item>
          <el-form-item label="Potential Products">
            <div v-if="client.product_of_interests">
              <span
v-for="(item, index) in client.product_of_interests"
                    :key="item">{{ item | getLabel(client_options.product) }}
                <span v-if="index < client.product_of_interests.length - 1">, </span></span>
            </div>
            <div v-else>-</div>
          </el-form-item>
        </div>

        <!-- Custom: Opportunity -->
        <div v-if="display.opportunity">
          <el-form-item label="Deal Potential Size">
            <div v-if="client.deal_potential">{{ client.deal_potential }} Hours</div>
            <div v-else>-</div>
          </el-form-item>
          <el-form-item label="Close Date">
            {{ client.close_time | momentFormat }}
          </el-form-item>
        </div>
      </div>
      <el-form-item label="Compliance Status">
        {{ client.compliance_status | getLabel(client_options.compliance_status) }}
      </el-form-item>
      <el-form-item label="AM">{{ client.am }}</el-form-item>
      <el-form-item label="AM Team">{{ client.am_team }}</el-form-item>
      <el-form-item label="Headquarters">
        <div v-if="client.location">{{ client.location.name }}</div>
        <div v-else>-</div>
      </el-form-item>
      <el-form-item label="Offices">
        <div v-if="client.offices">{{ client.offices | commaSeparated }}</div>
        <div v-else>-</div>
      </el-form-item>
      <el-form-item label="Require case office at project level">
        <span v-if="client.preference">
        {{ client.preference.enable_client_office_in_project | yesOrNo }}
        </span>
      </el-form-item>
      <el-form-item label="Create Date">{{ client.create_at | momentFormat(' MM / DD / YYYY') }}</el-form-item>
      <el-form-item label="Require Project Code">
        {{ client.require_project_code | yesOrNo }}
      </el-form-item>
      <el-form-item label="Disallow Complete Task">
        {{ client.disallow_complete_task | yesOrNo }}
      </el-form-item>
      <el-form-item label="Require Project Subtype When Completing Call">
        <span v-if="client.preference">
          {{ client.preference.require_task_subtype | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item label="Blind expert names in To Client and portal">
        <span v-if="client.preference">
          {{ client.preference.blind_expert_names_in_to_client_and_portal | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item label="Display Usage in To Client">
          <span v-if="client.preference">
          {{ client.preference.display_usage_in_to_client | yesOrNo }}
        </span>
      </el-form-item>
      <el-form-item label="Display oversea multiplier">
        <span v-if="client.preference">
          {{ client.preference.display_oversea_when_to_client | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item v-if="!isSGDB" label="Require Call Recording">
        <span v-if="client.preference">
          {{ client.preference.require_call_recording | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item v-if="!isSGDB" label="Require Call Transcript">
        <span v-if="client.preference">
          {{ client.preference.require_call_transcript | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item v-if="!isSGDB" label="Enable Auto Call Out At Start Time">
        <span v-if="client.preference">
          {{ client.preference.enable_auto_call_out_at_start_time | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item v-if="!isSGDB" label="TPA Client">
        <span v-if="client.preference">
        {{ client.preference.tpa_client | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item v-if="!isSGDB" label="Outsourcing">
        <slot v-if="client.preference">
          <span v-if="!client.preference.is_outsource_client">{{ client.preference.is_outsource_client | yesOrNo }}</span>
          <span v-else>{{ client.preference.outsource_region || 'Yes' }}</span>
        </slot>
        <span v-else>No</span>
      </el-form-item>
      <el-form-item label="Include One-Click Dial in Client Invite (Zoom)">
        <span v-if="client.preference">
          {{ client.preference.include_one_click_dial_in_client_invite | yesOrNo }}
        </span>
        <span v-else>No</span>
      </el-form-item>
      <div v-if="display.usage_template">
        <el-form-item label="Usage download template">
          {{ client.preference ? getNames(client.preference.usage_excel_fields, all_usage_fields) : '' }}
        </el-form-item>
      </div>
      <div>
        <el-form-item label="Research Coverage">
          <div v-if="client.client_db_user_relation">
            <span v-for="(item, index) in client.client_db_user_relation" :key="item.user_id">
              {{ item.user.name }}<span v-if="index < client.client_db_user_relation.length - 1">, </span>
            </span>
          </div>
        </el-form-item>
      </div>
      <template v-if="isAdmin">
        <el-form-item label="Google Sheet Template ID">
          {{ client.preference ? client.preference.client_google_sheet_template_id : '' }}
        </el-form-item>
        <el-form-item label="Google Sheet Project Folder ID">
          {{ client.preference ? client.preference.client_google_sheet_folder_id : '' }}
        </el-form-item>
      </template>
      <el-form-item>
        <el-button
          v-if="!disabledEdit"
          size="mini"
          icon="el-icon-edit"
          @click="isEdit = true">
          Edit
        </el-button>
      </el-form-item>
    </el-form>
    <edit-client
v-else
                 :data="client"
                 is-edit
                 @save="isEdit = false; $emit('update')"
                 @cancel="isEdit = false" />
  </div>
</template>

<script>
import EditClient from '@/views/client/update'
import { mapGetters, mapState } from 'vuex'
import ContractAPI from '@/api/contract'

export default {
  name: 'ClientProfile',
  components: {
    EditClient,
  },
  props: {
    client: {
      type: Object,
      required: true,
    },
    disabledEdit: Boolean,
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      display: {
        prospect: false,
        opportunity: false,
        usage_template: false,
      },
      all_usage_fields: null,
    }
  },
  computed: {
    ...mapGetters([
      'client_options',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    checkClientApprovalStatus() {
      return this.client.status === 'EXECUTE' && this.client.compliance_status !== 'APPROVED'
    },
    isAdmin() {
      return this.roles.some(item => ['Admin'].includes(item.role.name))
    },
  },

  // 合同生效(effective)后，客户状态=>EXECUTE
  mounted() {
    this.getAvailableFields()
    this.calcDisplayRule(this.client.status)
  },
  methods: {
    getAvailableFields() {
      return ContractAPI.getUsageExcelTemplate()
        .then(data => {
          const client_workflow = this.client.preference?.preferred_workflow || 'NORMAL'
          this.all_usage_fields = data[client_workflow]
        }).finally(() => {
          this.display.usage_template = true
        })
    },

    calcDisplayRule(client_status) {
      let prospect = false
      let opportunity = false

      switch (client_status) {
        case 'PROSPECT':
          prospect = true
          break
        case 'ENGAGE':
          opportunity = true
          break
      }

      this.display.prospect = prospect
      this.display.opportunity = opportunity
    },

    getNames(list, option) {
      if (!list || !list.length) return '-'
      return list.map(item => {
        const result = option.filter(opt => opt.name === item)
        return result.length ? result[0].displayName : undefined
      }).join(', ')
    },

  },
}
</script>
