<template>
  <div class="mb-3">
    <el-card>
      <div slot="header" class="flex justify-content-between">
        <div><b>Cost Of Revenue</b></div>
        <refresh-button @action="getList" />
      </div>

      <el-table
        v-loading="cost_loading"
        :data="table_data"
        :default-sort="default_sort"
        style="width: 100%"
        @sort-change="sortData"
      >
        <el-table-column
          label="Project"
          width="120"
          sortable="custom"
          prop="project.name"
        >
          <template slot-scope="{row}">
            <project-search v-if="row.isEditing" v-model="row.project_id" :multiple="false" :client-id="clientId" />
            <router-link-project v-else :data="row.project" />
          </template>
        </el-table-column>
        <el-table-column
          label="Project Code"
          width="70"
        >
          <template slot-scope="{row}">
            {{row.project && row.project.code}}
          </template>
        </el-table-column>
        <el-table-column
          label="Project Type"
          width="110"
        >
          <template slot-scope="{row}">
            {{row.project && row.project.sub_type}}
          </template>
        </el-table-column>
        <el-table-column
          label="Task ID"
          width="80"
          prop="id"
        >
          <template slot-scope="{row}">
            <router-link
              v-if="!row.isEditing"
              class="text-truncate link"
              :to="getRouterInfo(row)"
            > {{ row.task_id }}
            </router-link>
            <task-id-search v-else v-model="row.task_id" :init-list="task_id_list" />
          </template>
        </el-table-column>

        <el-table-column
          label="Type"
          width="90"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <span v-if="row.isPayment">{{ row.type }}</span>
              <span v-else>{{ row.type | getLabel(options.typesForShow) }}</span>
            </span>
            <el-select v-else v-model="row.type">
              <el-option
                v-for="item in options.payableTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column
          label="Expert"
          width="130"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <router-link-advisor v-if="row.isPayment || row.advisor_id" :data="row.advisor" />
            </span>
            <span v-else>
              <task-advisor-search
                :project-id="row.project_id"
                :extra-option="[{id:0,label:'Not Applicable',value:'Not Applicable'}]"
                @search="query=>handleSelectedAdvisor(query, row)"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Vendor"
          width="90"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              {{row.vendor_name}}
            </span>
            <span v-else>
              <el-input
                v-model="row.vendor_name"
                placeholder="vendor name"
                class="mt-2px" />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Transaction Date"
          align="right"
          width="145"
          sortable="custom"
          prop="date"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              {{ row.date_show }}
            </span>
            <el-date-picker
              v-else
              v-model="row.date"
              type="date"
              size="mini"
              placeholder="Transaction Date"
              value-format="yyyy/MM/dd"
              format="MM/dd/yyyy"
              class="filter-item date-picker"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="Payable Amount(USD)"
          width="130"
        >
          <template #default="{row}">
            <amount :data="row" />
          </template>
        </el-table-column>
        <el-table-column
          label="Payment Amount (Non-USD)"
          width="120"
        >
          <template #default="{row}">
            <span v-if="row.currency !== 'USD'">
              {{ row.amount }}  {{row.currency}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Notes"
          width="200"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">{{ row.notes }}</span>
            <el-input v-else v-model="row.notes" :class="{'need-notes':needNotes(row)}" />
          </template>
        </el-table-column>

        <el-table-column
          label="Actions"
          class="flex align-items-center"
        >
          <template #default="{row}">
            <el-tooltip
              effect="dark"
              :enterable="false"
              content="Complete again"
              placement="top"
            >

              <el-link
                v-if="row.isPayment && !row.isEditing"
                type="primary"
                :underline="false"
                icon="el-icon-finished"
                @click="goToTaskComplete(row)"
              />
            </el-tooltip>
            <el-button
              v-if="showEditButtons(row)"
              type="primary"
              size="mini"
              @click="row.isEditing=true"
            >
              Edit
            </el-button>

            <el-popconfirm
              v-if="showEditButtons(row)"
              width="220"
              confirm-button-text="Yes, Delete"
              cancel-button-text="No, Cancel"
              title="Are you sure you want to delete this transaction?"
              @confirm="deletePayable(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="mini"
                >
                  Delete
                </el-button>
              </template>
            </el-popconfirm>

            <el-button
              v-if="row.isEditing"
              type="success"
              size="mini"
              :disabled="saveDisabled(row)"
              :loading="row.save_loading"
              @click="savePayableRow(row)"
            >
              Save
            </el-button>

            <el-button
              v-if="row.isEditing"
              type="info"
              size="mini"
              @click="cancelEditPayableRow(row)"
            >
              Cancel
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <div class="client-pl-total-box">
          <div class="pl-label">Subtotal</div>
          <div class="pl-amount">
            <amount :data="{'amount_us':total_payment_amount}" />
          </div>
          <el-button
            v-if="editPermission"
            type="success"
            class="float-right"
            icon="el-icon-plus"
            @click="addPayableTransaction()"
          >
            Add Payable Transaction
          </el-button>
        </div>
        <pagination
          :total="origin_list.length"
          :page.sync="page"
          :limit.sync="size"
          :page-sizes="customPageSize"
          @pagination="manuallyPage"
        />
      </div>
    </el-card>

  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import moment from 'moment/moment'
import PaymentAPI from '@/api/payment'
import mixin from '@/views/client/detail/PL/mixin'

export default {
  name: 'CostOfRevenue',
  mixins: [mixin],
  props: {
    cost: [Number, String],
    editPermission: {
      type: Boolean,
      default: false,
    },
    clientId: [Number, String],
  },
  data() {
    return {
      cost_loading: false,
      payments: [],
      payables: [],
      total_payment_amount: undefined,
      table_data: [],
      search_params: null,
      task_id_list: [],
    }
  },

  methods: {
    getList(params) {
      if (params) {
        this.page = 1
        this.search_params = params
      }
      this.cost_loading = true
      return Promise.all([
        this.fetchPayments(),
        this.fetchPayables(),
      ])
        .then(() => {
          this.calcPaymentTableData()
        })
        .finally(() => {
          this.cost_loading = false
        })
    },

    needNotes(row) {
      return row.type === 'OTHER_PAYABLES' && !row.notes
    },

    saveDisabled(row) {
      return this.needNotes(row) || !row.type
    },

    showEditButtons(row) {
      return !row.isPayment && this.editPermission && !row.isEditing
    },

    fetchPayments() {
      this.cost_loading = true
      const params = Object.assign({
        extra: 'task,advisor,project,amount_in_usd',
        page: 1,
        size: 99999,
        task_date_lte: this.search_params.date_lte,
        task_date_gte: this.search_params.date_gte,
      }, this.search_params)
      delete params.date_gte
      delete params.date_lte

      return PaymentAPI.getPaymentForPL(params)
        .then(data => {
          this.payments = data.list
        })
        .finally(() => {
          this.cost_loading = false
        })
    },

    fetchPayables() {
      const params = Object.assign({
        page: 1,
        size: 99999,
        extra: 'project,task,advisor',
        transaction_date_lte: moment(this.search_params.date_lte).format('YYYY-MM-DD'),
        transaction_date_gte: moment(this.search_params.date_gte).format('YYYY-MM-DD'),
      }, this.search_params)
      delete params.date_gte
      delete params.date_lte
      return ProjectAPI.getPayableTransactions(params)
        .then(data => {
          this.payables = data.list
        })
    },

    calcPaymentTableData() {
      const standardPayments = this.payments.map(it => {
        return {
          isPayment: true,
          id: it.id,
          isEditing: false,
          save_loading: false,
          task_id: it.task.id,
          type: it.project?.sub_type,
          advisor: it.advisor,
          vendor_name: it.vendor_name || '',
          advisor_id: it.advisor_id,
          date: moment(it.task_date).format('yyyy/MM/DD'),
          date_show: moment(it.task_date).format('MM/DD/yyyy'),
          amount: it.amount,
          amount_us: it.currency !== 'USD' ? it.amount_in_usd : it.amount,
          currency: it.currency,
          notes: it.remark1,
          payment_amount_non_usd: '',
          project: it.project,
          project_id: it.project_id,
          task: it.task,
        }
      },
      )
      const payables = this.payables.map(it => {
        return Object.assign({
          isPayment: false,
          isEditing: false,
          save_loading: false,
          delete_loading: false,
          elected_advisor_id: '',
          date: moment(it.transaction_date).format('yyyy/MM/DD'),
          date_show: moment(it.transaction_date).format('MM/DD/yyyy'),
          amount_us: it.amount,
        }, it)
      })
      this.origin_list = standardPayments.concat(payables)
      this.sorted_list = standardPayments.concat(payables)
      this.sortData()
      this.task_id_list = standardPayments
      this.calcPayment()
    },

    calcPayment() {
      this.total_payment_amount = this.origin_list.reduce((total, row) => total + row.amount_us, 0)
      this.$emit('update:cost', this.total_payment_amount)
      this.$emit('update:list', this.origin_list)
    },

    deletePayable(row) {
      row.delete_loading = true
      return ProjectAPI.deletePayableTransaction(row.id)
        .then(() => {
          const delete_index = this.payables.findIndex(item => item.id === row.id)
          this.payables.splice(delete_index, 1)
          this.calcPaymentTableData()
        })
        .finally(() => {
          row.delete_loading = false
        })
    },

    addPayableTransaction() {
      const row = {
        isPayment: false,
        isEditing: true,
        save_loading: false,
        delete_loading: false,
        task_id: 0,
        type: '',
        selected_advisor_id: '',
        date: moment().format('yyyy/MM/DD'),
        amount_us: 0,
        notes: '',
        vendor_name: '',
      }
      this.table_data.push(row)
    },

    savePayableRow(row) {
      row.save_loading = true
      const data = {
        id: row.id,
        project_id: parseInt(row.project_id),
        task_id: row.task_id,
        type: row.type,
        advisor_id: row.selected_advisor_id,
        transaction_date: row.date,
        amount: row.amount_us,
        notes: row.notes,
        vendor_name: row.vendor_name,
      }

      const promise = row.id > 0 ? ProjectAPI.updatePayableTransaction(row.id, data)
        : ProjectAPI.savePayableTransaction(data)

      promise.then((data) => {
        row.new_id = data.id
        this.fetchPayables()
          .then(() => {
            this.calcPaymentTableData()
          })
      })
        .finally(() => {
          row.save_loading = false
        })
    },
    cancelEditPayableRow(row) {
      const index = this.table_data.indexOf(row)

      if (row.id > 0) {
        const origin_row = this.payables.filter(item => item.id === row.id) || {}
        Object.assign(row, {
          isPayment: false,
          isEditing: false,
          save_loading: false,
          delete_loading: false,
          elected_advisor_id: '',
          date: moment(origin_row.transaction_date).format('yyyy/MM/DD'),
          amount_us: origin_row.amount,
        }, origin_row)
      } else {
        if (index !== -1) {
          this.table_data.splice(index, 1)
        }
      }
    },

    handleSelectedAdvisor(selected_id, row) {
      row.selected_advisor_id = selected_id === 0 ? 0 : selected_id.split('-')[2]
    },
  },
}
</script>
<style scoped lang="scss">
.mt-2px{
  margin-top:2px
}

.need-notes{
  display: flex;
  ::v-deep .el-input__inner {
    border: 1px solid #f56c6c;
  }
}

.need-notes:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.float-right{
  float: right;
}

.date-picker {
  width: 140px;
}

</style>
