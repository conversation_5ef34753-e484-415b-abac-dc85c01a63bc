<template>
  <div>
    <div class="filter-container flex flex-wrap align-items-center">
      <project-search v-model="searchQuery.project_ids" :client-id="clientId" class="w-200px filter-item" />
      <el-select
        v-model="searchQuery.sub_type_in"
        placeholder="Project Type"
        multiple
        class="w-200px filter-item"
        clearable>
        <el-option
          v-for="item in project_options.sub_type"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="searchQuery.date_range"
        type="daterange"
        class="filter-item"
        start-placeholder="Start Date"
        end-placeholder="End Date"
        :default-time="['00:00:00', '23:59:59']" />
      <el-button type="primary" class="filter-item" :disabled="loading" @click="searchList()">Search</el-button>
    </div>
    <revenue ref="revenue" :revenue.sync="revenue" :list.sync="revenue_list" :client-id="clientId" :edit-permission="permissionEdit" />
    <cost-of-revenue ref="cost" :cost.sync="cost" :list.sync="cost_list" :client-id="clientId" :edit-permission="permissionEdit" />
    <pl-summary :cost="cost" :revenue="revenue" :cost-list="cost_list" :revenue-list="revenue_list"/>
  </div>
</template>

<script>
import Revenue from './Revenue'
import CostOfRevenue from './CostOfRevenue'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import plSummary from '@/views/client/detail/PL/plSummary'
import { mapGetters } from 'vuex'
import moment from 'moment'
export default {
  name: 'Index',
  components: { Revenue, CostOfRevenue, ProjectSearch, plSummary },
  props: {
    clientId: [Number, String],
    client: Object,
    disabledEdit: Boolean,
  },
  data() {
    return {
      loading: false,
      revenue: 0,
      cost: 0,
      cost_list: [],
      revenue_list: [],
      searchQuery: {
        date_range: [moment().subtract('30', 'days').toISOString(), moment().toISOString()],
        project_ids: [],
        sub_type_in: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'project_options',
    ]),
    total() {
      return {
        pl: (this.revenue - this.cost).toFixed(2),
        percentage: (((this.revenue - this.cost) / this.revenue) * 100).toFixed(2),
      }
    },
    permissionEdit() {
      return this.roles.some(item => {
        return ['Admin', 'Edit_P_and_L'].includes(item.role.name)
      })
    },
  },
  mounted() {
    this.searchList()
  },
  methods: {
    searchList() {
      const date_range = this.searchQuery.date_range
      const params = {
        client_id: this.clientId,
        client_tsid: this.client.tsid,
        date_gte: date_range ? date_range[0] : undefined,
        date_lte: date_range ? date_range[1] : undefined,
        'project.client_id': this.clientId,
        'project.sub_type_in': this.searchQuery.sub_type_in.join(),
        'project.ids': this.searchQuery.project_ids.join(),
      }
      this.$refs.revenue.getList(params)
      this.$refs.cost.getList(params)
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-button{
  margin: 2px;
}

</style>
