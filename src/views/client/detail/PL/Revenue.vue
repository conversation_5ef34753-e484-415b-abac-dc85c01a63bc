<template>
  <div class="mb-3">
    <el-card>
      <div slot="header" class="flex justify-content-between">
        <div><b>Revenue</b></div>
        <div><refresh-button @action="getList" /></div>
      </div>
      <div v-if="selection.length" class="mb-3">
        <el-button
          type="primary"
          :loading="download_loading"
          icon="el-icon-download"
          @click="downloadUsageReport">
          Download
        </el-button>
        <template-download-usage :data-list.sync="download_tab" :option-list="available_fields" />
      </div>

      <el-table
        v-loading="revenue_loading"
        :data="table_data"
        style="width: 100%"
        :default-sort="default_sort"
        @sort-change="sortData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column v-if="has_download_permission" type="selection" width="40" />
        <el-table-column
          label="Project"
          width="120"
          sortable="custom"
          prop="project.name"
        >
          <template slot-scope="{row}">
            <project-search v-if="row.isEditing" v-model="row.project_id" :multiple="false" :client-id="clientId" />
            <router-link-project v-else :data="row.project" />
          </template>
        </el-table-column>
        <el-table-column
          label="Project Code"
          width="70"
        >
          <template slot-scope="{row}">
            {{row.project && row.project.code}}
          </template>
        </el-table-column>
        <el-table-column
          label="Project Type"
          width="110"
        >
          <template slot-scope="{row}">
            {{row.project && row.project.sub_type}}
          </template>
        </el-table-column>
        <el-table-column
          label="Task ID"
          width="90"
          prop="id"
        >
          <template slot-scope="{row}">
            <router-link
              v-if="!row.isEditing"
              class="text-truncate link"
              :to="getRouterInfo(row)"
            > {{ row.task_id }}
            </router-link>
            <task-id-search v-else v-model="row.task_id" :init-list="task_id_list" />
          </template>
        </el-table-column>

        <el-table-column
          label="Type"
          width="100"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <span v-if="row.isRevenue">{{ row.type }}</span>
              <span v-else>{{ row.type | getLabel(options.typesForShow) }}</span>
            </span>
            <el-select v-else v-model="row.type">
              <el-option
                v-for="item in options.receivableTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="Expert"
          width="200"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <router-link-advisor v-if="row.advisor" :data="row.advisor" />
              <span v-else>{{row.advisor_name}}</span>
            </span>
            <span v-else>
              <task-advisor-search
                :project-id="row.project_id"
                :extra-option="[{id:0,label:'Not Applicable',value:'Not Applicable'}]"
                @search="query=>handleSelectedAdvisor(query, row)"
              />
              <el-input
                v-if="row.edit_advisor_name"
                v-model="row.advisor_name"
                placeholder="advisor name"
                class="mt-2px" />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Transaction Date"
          align="right"
          width="145"
          sortable="custom"
          prop="date"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              {{ row.date_show }}
            </span>
            <el-date-picker
              v-else
              v-model="row.date"
              type="date"
              size="mini"
              placeholder="Transaction Date"
              value-format="yyyy/MM/dd"
              format="MM/dd/yyyy"
              class="filter-item date-picker"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="Receivable Amount(USD)"
          width="130"
        >
          <template #default="{row}">
            <amount :data="row" />
          </template>
        </el-table-column>
        <el-table-column
          label="Client Hours"
          width="80"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <span v-if="row.isRevenue">{{ row.client_hours }}</span>
            </span>
            <span v-else>Not Applicable</span>
          </template>
        </el-table-column>

        <el-table-column
          label="Overseas"
          width="80"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">{{ row.is_oversea ? 'Yes' : 'No' }}</span>
            <el-select
              v-else
              v-model="row.is_oversea"
              placeholder=""
            >
              <el-option label="Yes" :value="true" />
              <el-option label="No" :value="false" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="Notes"
          width="200"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">{{ row.notes }}</span>
            <el-input v-else v-model="row.notes" :class="{'need-notes':needNotes(row)}" />
          </template>
        </el-table-column>

        <el-table-column
          label="Actions"
          class="flex align-items-center"
        >
          <template #default="{row}">
            <el-tooltip
              effect="dark"
              :enterable="false"
              content="Complete again"
              placement="top"
            >

              <el-link
                v-if="row.isRevenue && !row.isEditing"
                type="primary"
                :underline="false"
                icon="el-icon-finished"
                @click="goToTaskComplete(row)"
              />
            </el-tooltip>
            <el-button
              v-if="showEditButtons(row)"
              type="primary"
              size="mini"
              @click="row.isEditing=true"
            >
              Edit
            </el-button>

            <el-popconfirm
              v-if="showEditButtons(row)"
              width="220"
              confirm-button-text="Yes, Delete"
              cancel-button-text="No, Cancel"
              title="Are you sure you want to delete this transaction?"
              @confirm="deleteReceivable(row)"
            >
              <el-button
                slot="reference"
                :loading="row.delete_loading"
                type="danger"
                size="mini"
              >
                Delete
              </el-button>
            </el-popconfirm>

            <el-button
              v-if="row.isEditing"
              type="success"
              size="mini"
              :disabled="saveDisabled(row)"
              :loading="row.save_loading"
              @click="saveReceivableRow(row)"
            >
              Save
            </el-button>

            <el-button
              v-if="row.isEditing"
              type="info"
              size="mini"
              @click="cancelEditReceivableRow(row)"
            >
              Cancel
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-loading="revenue_loading">
        <div class="client-pl-total-box">
          <div class="pl-label">Subtotal</div>
          <div class="pl-amount">
            <amount :data="{'amount_us':total_revenue_amount}" />
          </div>
          <b class="pl-5">{{client_hours_total}}</b>
          <el-button
            v-if="editPermission"
            type="success"
            class="float-right"
            icon="el-icon-plus"
            @click="addReceivableRow()"
          >
            Add Receivable Transaction
          </el-button>

        </div>
        <pagination
          :total="origin_list.length"
          :page.sync="page"
          :limit.sync="size"
          :page-sizes="customPageSize"
          @pagination="manuallyPage"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import ContractAPI from '@/api/contract'
import mixin from '@/views/client/detail/PL/mixin'
import ProjectAPI from '@/api/project'
import moment from 'moment/moment'
import TemplateDownloadUsage from '@/components/ClientDownloadUsage/index.vue'
export default {
  name: 'PL',
  components: { TemplateDownloadUsage },
  mixins: [mixin],
  props: {
    revenue: [Number, String],
    editPermission: {
      type: Boolean,
      default: false,
    },
    clientId: [Number, String],
  },
  data() {
    return {
      revenue_loading: false,
      download_loading: false,

      // revenues
      revenues: [],
      receivables: [],
      /*
      存放统一格式后的revenues, receivables
      对应tableData
       */
      table_data: [],
      total_revenue_amount: 0,
      client_hours_total: 0,
      task_id_list: [],
      search_params: null,
      selection: [],
      download_tab: [],
      available_fields: undefined,
    }
  },

  methods: {
    getList(params) {
      if (params) {
        this.page = 1
        this.search_params = params
      }
      this.revenue_loading = true
      return Promise.all([
        this.fetchRevenues(),
        this.fetchReceivables(),
      ])
        .then(() => {
          this.calcRevenueTableData()
        })
        .finally(() => {
          this.revenue_loading = false
        })
    },

    needNotes(row) {
      return row.type === 'OTHER' && !row.notes
    },

    showEditButtons(row) {
      return !row.isRevenue && this.editPermission && !row.isEditing
    },

    fetchRevenues() {
      const params = Object.assign({
        extra: 'task.advisor,project,task.p_l_profit',
        page: 1,
        size: 99999,
        revenue_time_lte: this.search_params.date_lte,
        revenue_time_gte: this.search_params.date_gte,
      }, this.search_params)
      delete params.date_gte
      delete params.date_lte

      return ContractAPI.getRevenueList(params)
        .then(data => {
          this.revenues = data.list
        })
    },

    fetchReceivables() {
      const params = Object.assign({
        page: 1,
        size: 99999,
        extra: 'project,task,advisor,task.p_l_profit',
        transaction_date_lte: moment(this.search_params.date_lte).format('YYYY-MM-DD'),
        transaction_date_gte: moment(this.search_params.date_gte).format('YYYY-MM-DD'),
      }, this.search_params)
      delete params.date_gte
      delete params.date_lte

      return ProjectAPI.getReceivableTransactions(params)
        .then(data => {
          this.receivables = data.list
        })
    },

    calcRevenueTableData() {
      const standardRevenues = this.revenues.map(it => {
        return {
          isRevenue: true,
          id: it.id,
          task_id: it.task_id,
          type: it.task?.project_sub_type,
          advisor: it.task?.advisor,
          date: moment(it.revenue_time).format('yyyy/MM/DD'),
          date_show: moment(it.revenue_time).format('MM/DD/yyyy'),
          amount_us: it.cash,
          is_oversea: it.task?.has_overseas,
          notes: it.billing_notes,
          client_hours: it.billing_hours,
          project: it.project,
          project_id: parseInt(it.project.id),
          task: it.task,
        }
      })
      const receivables = this.receivables.map(it => {
        return Object.assign({
          isRevenue: false,
          isEditing: false,
          save_loading: false,
          delete_loading: false,
          edit_advisor_name: it.advisor_id === 0,
          selected_advisor_id: +it.advisor_id,
          date: moment(it.transaction_date).format('yyyy/MM/DD'),
          date_show: moment(it.transaction_date).format('MM/DD/yyyy'),
          client_hours: 0,
          amount_us: it.amount,
          project_id: parseInt(it.project_id),
        }, it)
      })
      this.origin_list = standardRevenues.concat(receivables)
      this.sorted_list = standardRevenues.concat(receivables)
      this.sortData()
      this.task_id_list = standardRevenues
      this.calcTotal()
    },

    calcTotal() {
      this.total_revenue_amount = this.origin_list.reduce((total, row) => total + row.amount_us, 0)
      this.$emit('update:revenue', this.total_revenue_amount)
      this.$emit('update:list', this.origin_list)
      this.client_hours_total = this.origin_list.reduce((total, row) => total + row.client_hours, 0)
    },

    deleteReceivable(row) {
      row.delete_loading = true
      return ProjectAPI.deleteReceivableTransaction(row.id)
        .then(() => {
          const delete_index = this.receivables.findIndex(item => item.id === row.id)
          this.receivables.splice(delete_index, 1)
          this.calcRevenueTableData()
        })
        .finally(() => {
          row.delete_loading = false
        })
    },

    addReceivableRow() {
      const row = {
        isRevenue: false,
        isEditing: true,
        save_loading: false,
        delete_loading: false,
        task_id: 0,
        type: '',
        advisor: {},
        advisor_name: '',
        selected_advisor_id: '',
        edit_advisor_name: false,
        date: moment().format('yyyy/MM/DD'),
        amount_us: 0,
        is_oversea: false,
        notes: '',
      }
      this.table_data.push(row)
    },
    saveDisabled(row) {
      return this.needNotes(row) || typeof row.amount_us !== 'number' || !row.type
    },
    saveReceivableRow(row) {
      row.save_loading = true
      const data = {
        id: row.id,
        project_id: row.project_id,
        client_id: this.clientId,
        task_id: row.task_id,
        type: row.type,
        advisor_id: +row.selected_advisor_id,
        advisor_name: +row.selected_advisor_id === 0 ? row.advisor_name : undefined,
        transaction_date: row.date,
        amount: row.amount_us,
        is_oversea: row.is_oversea,
        notes: row.notes,
      }

      const promise = row.id > 0 ? ProjectAPI.updateReceivableTransaction(row.id, data)
        : ProjectAPI.saveReceivableTransaction(data)

      promise.then((data) => {
        row.new_id = data.id
        this.fetchReceivables()
          .then(() => {
            this.calcRevenueTableData()
          })
      })
        .finally(() => {
          row.save_loading = false
        })
    },
    cancelEditReceivableRow(row) {
      const index = this.table_data.indexOf(row)

      if (row.id > 0) {
        const origin_row = this.receivables.find(item => item.id === row.id) || {}
        Object.assign(row, {
          isRevenue: false,
          isEditing: false,
          save_loading: false,
          edit_advisor_name: origin_row.advisor_id === 0,
          selected_advisor_id: +origin_row.advisor_id,
          amount_us: origin_row.amount,
          date: moment(origin_row.transaction_date).format('yyyy/MM/DD'),
        }, origin_row)
      } else {
        if (index !== -1) {
          this.table_data.splice(index, 1)
        }
      }
    },

    handleSelectedAdvisor(selected_id, row) {
      if (selected_id === 0) {
        row.edit_advisor_name = true
        row.selected_advisor_id = selected_id
      } else {
        row.edit_advisor_name = false
        row.selected_advisor_id = selected_id.split('-')[2]
      }
    },

    async handleSelectionChange(val) {
      if (!this.available_fields) {
        await this.getAvailableFields()
      }
      this.selection = val
      if (val.length) {
        this.download_tab = this.available_fields.filter(item => item.isStandard).map(item => item.name)
      } else {
        this.download_tab = []
      }
    },

    downloadUsageReport() {
      const revenues = this.selection.filter(item => item.isRevenue)
      const receivables = this.selection.filter(item => !item.isRevenue)
      const params = {
        revenue_ids: revenues.map(item => item.id).join(','),
        zone_id: this.timeZone,
        columns: this.download_tab.length ? this.download_tab.join() : undefined,
        receivable_transaction_ids: receivables.map(item => item.id).join(','),
      }
      this.download_loading = true
      return ProjectAPI.downloadRevenue(params)
        .finally(() => {
          this.download_loading = false
        })
    },

    getAvailableFields() {
      return ProjectAPI.getAvailableFields()
        .then(data => {
          this.available_fields = data
        })
    },

  },
}
</script>

<style scoped lang="scss">
.mt-2px{
  margin-top:2px
}
.need-notes{
  display: flex;
  ::v-deep .el-input__inner {
    border: 1px solid #f56c6c;
  }
}

.need-notes:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.float-right{
  float: right;
 }

.date-picker {
  width: 140px;
}

</style>
