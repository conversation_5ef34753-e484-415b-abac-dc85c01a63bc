<template>
  <div class="app-container">
    <el-form
      ref="form"
      :disabled="isLoading"
      :model="form"
      :rules="rules"
      show-message
      label-width="200px"
      @submit.native.prevent="actionSubmit">
      <el-form-item label="Name" prop="name">
        <el-input
          ref="name"
          v-model="form.name"
          class="w-100" />
      </el-form-item>

      <el-form-item label="Type" prop="type">
        <el-select
          v-model="form.type"
          filterable
          placeholder="Search">
          <el-option
            v-for="item in client_options.type"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="Status" prop="status">
        <el-select
          v-model="form.status"
          filterable
          placeholder="Search"
          :disabled="!isEdit"
          @change="updateDisplayRule">
          <template v-for="item in client_options.status">
            <el-option
              v-if="filterStatus(item)"
              :key="item.value"
              :disabled="optionDisable(item.value)"
              :label="item.label"
              :value="item.value" />
          </template>
        </el-select>
      </el-form-item>

      <transition name="fade">
        <div v-if="display.prospect || display.opportunity">

          <!-- Custom: Prospect -->
          <div v-if="display.prospect">
            <el-form-item
              label="Potential"
              prop="potential">
              <el-radio-group
                v-model="form.potential"
                class="radio-group-vertical">
                <el-radio
                  v-for="item in client_options.potential"
                  :key="item.value"
                  :label="item.value">{{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="Potential Products"
              prop="product_of_interests">
              <el-checkbox-group
                v-model="form.product_of_interests"
                class="checkbox-group-vertical">
                <el-checkbox
                  v-for="item in client_options.product"
                  :key="item.value"
                  :label="item.value">{{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- Custom: Opportunity -->
          <div v-if="display.opportunity">
            <el-form-item
              label="Deal Potential Size"
              prop="deal_potential">
              <el-input
                v-model.number="form.deal_potential"
                type="number"
                step="any"
                class="w-172px">
                <template slot="append">Hours</template>
              </el-input>
            </el-form-item>
            <el-form-item
              label="Close Date"
              prop="close_time">
              <el-date-picker
                v-model="form.close_time"
                type="date" />
            </el-form-item>
          </div>
        </div>
      </transition>

      <el-form-item label="AM" prop="account_managers">
        <el-select
          v-model="form.account_managers"
          :loading="optionLoading"
          :remote-method="loadAmList"
          filterable
          multiple
          remote
          class="w-100"
          placeholder="AM">
          <el-option
            v-for="item in options.am_list"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="Client AM Team" prop="am_team_id">
        <el-select v-model="form.am_team_id" placeholder="select" @change="amTeamChange()">
          <el-option
            v-for="(item, index) in options.am_team_list"
            :key="index"
            :label="item.name"
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="Headquarters" prop="location">
        <location v-model="form.location_id" class="w-172px" />
      </el-form-item>
      <el-form-item label="Offices" prop="offices">
        <div
          v-for="(item, index) in form.offices"
          :key="index"
          class="flex"
          :class="{ 'mb-8': index < form.offices.length - 1 }">
          <el-input
            v-model="item.name"
            class="w-172px mr-8"
            placeholder="Office Name" />
          <location
            v-model="item.location_id"
            class="w-172px mr-8" />
          <el-input
            v-model="item.description"
            class="flex-1 mr-8"
            placeholder="Description" />
          <el-link
            :underline="false"
            type="danger"
            @click="actionRemoveOffice(index)">
            <el-icon name="minus" />
          </el-link>
        </div>
        <el-link
          :underline="false"
          type="primary"
          @click="actionAddOffice">
          <el-icon name="plus" />
        </el-link>
      </el-form-item>

      <el-divider />

      <el-form-item label="Preference">
        <el-checkbox v-model="form.require_project_code">Require Project Code</el-checkbox>
        <el-checkbox v-model="form.disallow_complete_task" :disabled="disallow_complete_task_disabled">Disallow Complete Task</el-checkbox>
        <el-checkbox v-model="form.preference.require_task_subtype">Require Project Subtype When Completing Call</el-checkbox>
        <el-checkbox v-model="form.preference.blind_expert_names_in_to_client_and_portal">Blind expert names in To Client and portal</el-checkbox>
        <el-checkbox v-model="form.preference.display_usage_in_to_client">Display Usage in To Client</el-checkbox>
        <el-checkbox v-if="!isSGDB" v-model="form.preference.require_call_recording">Require Call Recording</el-checkbox>
        <el-checkbox v-if="!isSGDB" v-model="form.preference.require_call_transcript" :disabled="!form.preference.require_call_recording">Require Call Transcript</el-checkbox>
        <el-checkbox v-if="!isSGDB" v-model="form.preference.enable_auto_call_out_at_start_time">Enable Auto Call Out At Start Time</el-checkbox>
        <el-checkbox v-model="form.preference.enable_client_office_in_project">Require case office at project level</el-checkbox>
        <el-checkbox v-model="form.preference.tpa_client">TPA Client</el-checkbox>
        <el-checkbox v-model="form.preference.is_outsource_client" :disabled="!isAdmin" @change="form.preference.outsource_region = $event ? (isSGDB ? 'CN' : 'SEA') : null">Outsourcing</el-checkbox>
        <el-select v-if="form.preference.is_outsource_client" v-model="form.preference.outsource_region" class="w-120px ml-8">
          <el-option v-if="isSGDB" label="CN" value="CN" />
          <el-option v-if="isSGDB" label="US" value="US" />
          <el-option v-else label="SEA" value="SEA" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="usageDownloadPermission" label="Usage download template">
        <template-download-usage
          v-if="form.preference.usage_excel_fields"
          :data-list.sync="form.preference.usage_excel_fields"
          :option-list="all_usage_fields" />
      </el-form-item>

      <el-form-item label="Research Coverage">
        <user-search
          v-model="form.client_db_user_relation"
          clearable
          multiple
          :extra-member="data ? data.client_db_user_relation : []"
          class="multiple-select"
          placeholder="Search" />
      </el-form-item>

      <template v-if="isAdmin">
        <el-form-item label="Google Sheet Template ID">
          <el-input
            v-model="form.preference.client_google_sheet_template_id"
            type="text"
            placeholder="Template ID"
          />
        </el-form-item>
        <el-form-item label="Google Sheet Project Folder ID">
          <el-input
            v-model="form.preference.client_google_sheet_folder_id"
            type="text"
            placeholder="Project Folder ID"
          />
        </el-form-item>
      </template>

      <el-form-item style="margin-top: 2em;">
        <el-button
          type="primary"
          icon="el-icon-check"
          :loading="isSubmitting"
          native-type="submit">
          {{ isEdit ? 'Update' : 'Create' }}
        </el-button>
        <el-button
          v-if="isEdit"
          type="text"
          @click="actionCancel">
          Cancel
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'
import moment from 'moment-timezone'
import TemplateDownloadUsage from '@/components/ClientDownloadUsage/index'
import ClientAPI from '@/api/client'
import ToolAPI from '@/api/tool'
import Mixin from '@/components/FormPage/mixin'
import { debounce } from '@/utils/tool'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import Location from '@/components/Location'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import UserAPI from '@/api/user'
import ContractAPI from '@/api/contract'

export default {
  name: 'CreateClient',
  components: { Location, TemplateDownloadUsage, UserSearch },
  mixins: [Mixin, SelectMixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('Name is required'))
      } else {
        const data = { module: 'client', params: { name: value } }
        debounce(() => {
          ToolAPI.checkName(data)
            .then((data) => {
              if (data && this.form.name !== this.data.name) {
                callback(new Error('The name already exists, please check it out or try another.'))
              } else {
                callback()
              }
            })
        }, 500)
      }
    }
    return {
      isLoading: false,
      optionLoading: false,
      isEditMode: false,
      isSubmitting: false,
      disallow_complete_task_disabled: false,
      form: this.initForm(),
      origin_account_managers: [],
      options: {
        am_list: [],
        am_team_list: [],
      },
      display: {
        prospect: false,
        opportunity: false,
      },
      all_usage_fields: undefined,

      rules: {
        name: [{ validator: validateName, required: true, trigger: 'change' }],
        type: [{ required: true, trigger: 'change' }],
        status: [{ required: true, trigger: 'change' }],
        am_team_id: [{ required: true, trigger: 'change' }],
        account_managers: [{ required: true, trigger: 'change' }],
      },
    }
  },
  computed: {
    ...mapGetters([
      'client_options',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    usageDownloadPermission() {
      return this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'Finance'].includes(item.role.name)
      })
    },
    isAdmin() {
      return this.roles.some(item => ['Admin'].includes(item.role.name))
    },
  },

  watch: {
    'form.preference.require_call_recording': {
      handler(newVal) {
        if (!newVal) {
          this.form.preference.require_call_transcript = false
        }
      }, immediate: true,
    },
  },

  mounted() {
    this.isLoading = true
    this.getOptions()
      .finally(() => {
        this.isLoading = false
        this.handleIsEdit()
      })
  },

  methods: {
    initForm() {
      return {
        id: undefined,
        name: '',
        type: 'HEDGE_FUND',
        status: 'PROSPECT',
        potential: undefined,
        product_of_interests: [],
        deal_potential: undefined,
        close_time: moment().add(1, 'week').toDate(), // 默认一周
        account_managers: [],
        am_team_id: undefined,
        location_id: undefined,
        offices: [],
        preference: {
          'usage_excel_fields': null,
          'require_task_subtype': false,
          require_call_recording: null,
          require_call_transcript: null,
          enable_auto_call_out_at_start_time: null,
          blind_expert_names_in_to_client_and_portal: false,
          display_oversea_when_to_client: false,
          is_outsource_client: false,
          outsource_region: null,
          display_usage_in_to_client: false,
          enable_client_office_in_project: false,
          tpa_client: false,
          client_google_sheet_template_id: null,
          client_google_sheet_folder_id: null,
        },
        require_project_code: false,
        disallow_complete_task: false,
        client_db_user_relation: [],
      }
    },

    optionDisable(value) {
      return ['EXECUTE', 'DORMANT', 'EXPIRED', 'CLOSED'].includes(value)
    },
    /**
     * 编辑页面定制
     */
    handleIsEdit() {
      if (this.isEdit && this.data) {
        this.isEditMode = true
        const data = _.cloneDeep(_.pick(this.data, Object.keys(this.form)))

        data.offices = data.offices ? data.offices.map(item => {
          return _.pick(item, ['id', 'name', 'location_id', 'description'])
        }) : []

        const ams = []

        data.account_managers = data.account_managers.map(item => {
          ams.push({ uid: item.uid, id: item.id })
          return item.uid
        })
        this.origin_account_managers = ams

        data.client_db_user_relation = data.client_db_user_relation.map(item => item.user_id)

        if (!data.preference) {
          data.preference = {
            usage_excel_fields: null,
            require_task_subtype: false,
            require_call_recording: null,
            require_call_transcript: null,
            enable_auto_call_out_at_start_time: null,
            blind_expert_names_in_to_client_and_portal: false,
            display_oversea_when_to_client: false,
            is_outsource_client: false,
            outsource_region: null,
            display_usage_in_to_client: false,
            enable_client_office_in_project: false,
            tpa_client: false,
          }
        }
        this.form = data
      }

      if (!this.form.preference.usage_excel_fields) {
        this.form.preference.usage_excel_fields = this.all_usage_fields.filter(item => item.is_standard)
          .map(item => item.name)
      }

      this.amTeamChange(true)

      this.updateDisplayRule(this.form.status)
    },

    filterStatus(item) {
      return this.data || item.value !== 'CLOSED'
    },

    handleSave(data) {
      let params = _.cloneDeep(data)
      params = this.preformatParams(params)
      // 默认值 设置
      if (!this.isSGDB) {
        const expire_duration = params.type === 'CONSULTING_FIRM' ? 43200 : 20160
        params.preference.enable_expire_delete_recordings_transcripts = true
        params.preference.recordings_expire_duration_minutes = expire_duration
        params.preference.transcripts_expire_duration_minutes = expire_duration
      }

      return ClientAPI.createClient(params).then(data => {
        this.$router.push({ name: 'ClientDetail', params: { client_id: data['id'] } })
      })
    },

    updateClient() {
      let params = _.cloneDeep(this.form)
      params = this.preformatParams(params)
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.isSubmitting = true

          return ClientAPI.updateClient(params)
            .then(() => {
              this.$emit('save')
            })
            .finally(() => {
              this.isSubmitting = false
            })
        } else {
          return false
        }
      })
    },
    /*
    * 创建客户时，非US TEAM disallow_complete_task：true 且不允许变更
    * 编辑客户时，非US |GUS TEAM ，disallow_complete_task：true 且不允许变更
    * 编辑客户时，初始化表单 US |GUS TEAM，disallow_complete_task 取原始值，其他team，复制false*/
    amTeamChange(init = false) {
      if (this.isSGDB) return
      const is_not_us_team = this.form.am_team_id !== 1
      const is_not_gus_team = this.form.am_team_id !== 2

      if (this.isEditMode) {
        if (is_not_us_team && is_not_gus_team) {
          this.form.disallow_complete_task = true
          this.disallow_complete_task_disabled = true
        } else {
          if (!init) this.form.disallow_complete_task = false
          this.disallow_complete_task_disabled = false
        }
      } else {
        this.disallow_complete_task_disabled = is_not_us_team
        this.form.disallow_complete_task = is_not_us_team
      }
    },
    preformatParams(params) {
      params.account_managers = this.formatAM(params.account_managers)
      params.offices = params.offices.filter(item => {
        const hasId = item.id
        const hasName = item.name
        const hasLocation = item.location_id
        const hasDescription = item.description

        return hasId || hasName || hasLocation || hasDescription
      })
      params.client_db_user_relation = params.client_db_user_relation.map(id => ({
        user_id: id,
        relation_type: 'RESEARCH',
      }))
      return params
    },

    getOptions() {
      return Promise.all([this.loadAmList(), getAmTeamList(), this.getAvailableFields()])
        .then(poly => {
          const am_team_list = poly[1]
          this.options.am_team_list = am_team_list

          if (am_team_list.length) {
            this.form.am_team_id = am_team_list[0].id
          }
        })

      function getAmTeamList() {
        return ClientAPI.getAmTeamList()
          .then(data => {
            const am_team_options = []

            data['list'].map(item => {
              am_team_options.push({
                name: item.name,
                id: item.id,
              })
            })

            return am_team_options
          })
      }
    },
    getAvailableFields() {
      return ContractAPI.getUsageExcelTemplate()
        .then(data => {
          const client_workflow = this.data?.preference?.preferred_workflow || 'NORMAL'
          this.all_usage_fields = data[client_workflow]
        })
    },

    loadAmList(name) {
      this.options.am_list = []

      const params = {
        'user_role.role_ids': '5',
        page: 1,
        size: 20,
        is_blocked: false,
        name_like: name || undefined,
      }
      return UserAPI.getUsersList(params).then(data => {
        const list = data['list'].filter(item => item.name)
        if (this.data && this.data.account_managers && this.data.account_managers.length) {
          const memberIds = list.map(item => item.id)
          this.data.account_managers.forEach(item => {
            if (!memberIds.includes(item.uid)) {
              list.unshift(item.user)
            }
          })
        }
        this.options.am_list = list
      })
    },

    formatAM(account_managers) {
      const result = []
      this.origin_account_managers.map(data => {
        const index = account_managers.indexOf(data.uid)
        if (index > -1) {
          result.push(data)
          account_managers.splice(index, 1)
        }
      })
      return account_managers.map(item => {
        return { uid: item }
      })
        .concat(result)
    },

    updateDisplayRule(client_status) {
      let prospect = false
      let opportunity = false

      switch (client_status) {
        case 'PROSPECT':
          prospect = true
          break
        case 'ENGAGE':
          opportunity = true
          break
      }

      this.display.prospect = prospect
      this.display.opportunity = opportunity
    },

    actionSubmit() {
      this.isEdit ? this.updateClient() : this.onSave()
    },

    actionCancel() {
      this.$emit('cancel')
    },

    actionAddOffice() {
      this.form.offices.push({
        name: undefined,
        location_id: undefined,
        description: undefined,
      })
    },

    actionRemoveOffice(index) {
      this.form.offices.splice(index, 1)
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .form-item {
  display: inline-block;
  margin-bottom: 0;

  > .el-form-item__content {
    margin-left: 0;
  }
}

.w-600px {
  width: 600px;
}

</style>
