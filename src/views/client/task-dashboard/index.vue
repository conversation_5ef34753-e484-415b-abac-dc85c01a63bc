<template>
  <div :class="{'app-container':!hideSearch}">
    <div v-if="!hideSearch" class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <user-search
        v-model="searchQuery.assignee_id"
        clearable
        role="am"
        :extra-member="[{user:{name:info.name,id:info.id},uid:info.id}]"
        placeholder="Assignee"
        class="w-200px filter-item"
        @change="handleSearch" />
      <client-search
        v-model="searchQuery.client_id"
        class="w-200px filter-item"
        @change="handleSearch" />
      <el-select
        v-model="searchQuery.client_contact_id"
        class="remote-select filter-item w-200px"
        filterable
        remote
        clearable
        reserve-keyword
        placeholder="Client User"
        :loading="selectLoading"
        :remote-method="getContact"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option v-for="item in contactList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="searchQuery.reminder_at"
        type="daterange"
        class="w-200px filter-item"
        start-placeholder="Reminder Date start"
        end-placeholder="Reminder Date end"
        :default-time="['00:00:00', '23:59:59']"
        @change="handleSearch"
      />
      <el-select
        v-model="searchQuery.priority"
        class="filter-item w-200px"
        clearable
        placeholder="Priority"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option label="Urgent" value="URGENT" />
        <el-option label="High" value="HIGH" />
        <el-option label="Medium" value="MEDIUM" />
        <el-option label="Low" value="LOW" />
      </el-select>
      <el-date-picker
        v-model="searchQuery.complete_at"
        type="daterange"
        class="w-200px filter-item"
        start-placeholder="Completion Date start"
        end-placeholder="Completion Date end"
        :default-time="['00:00:00', '23:59:59']"
        @change="handleSearch"
      />
      <el-select
        v-model="searchQuery.status"
        class="filter-item w-200px"
        clearable
        placeholder="Status"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option label="Initial" value="INITIAL" />
        <el-option label="On Hold" value="ON_HOLD" />
        <el-option label="In Progress" value="IN_PROGRESS" />
        <el-option label="Follow Up" value="FOLLOW_UP" />
        <el-option label="Completed" value="COMPLETED" />
      </el-select>
      <user-search
        v-model="searchQuery.create_by_id"
        clearable
        role="am"
        placeholder="Create By"
        class="w-200px filter-item"
        @change="handleSearch" />
    </div>
    <div>
      <add-task-dashboard
        v-if="hasCreateRight"
        :data-user="dataUser"
        :data-client="client"
        :show-clock="false"
        @hide-dialog="handleSearch" />

      <el-button
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="openNote">
        New Client Note
      </el-button>
    </div>
    <div class="flex">
      <div class="status-tags filter-item tabs-stackOverflow">
        <template v-for="(item, index) in filterOptions">
          <el-tag
            :key="index"
            :class="{active: (filterType === item.value)}"
            @click.native="searchFilter(item.value)"
          >{{ item.label }}
          </el-tag>
        </template>
      </div>
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="!recentTasks ? list : list.slice(0, 10)"
      @sort-change="handleSortData"
    >
      <el-table-column label="Create Date" sortable="custom" prop="create_at">
        <template v-if="scope.row.create_at != null" slot-scope="scope">
          {{ scope.row.create_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Assignee">
        <template slot-scope="scope">
          <el-dropdown class="task-assignee-dropdown" placement="bottom-end" @command="updateAssignee">
            <span>
              {{ scope.row.assignee?.name ?? '-' }}
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown" class="task-assignee-dropdown-items">
              <el-dropdown-item
                v-for="user in am"
                :key="user.id"
                :command="{
                  target: scope.row,
                  id: user.id
                }">
                {{ user.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column label="Create By">
        <template slot-scope="scope">
          {{ scope.row.create_by && scope.row.create_by.name }}
        </template>
      </el-table-column>
      <el-table-column label="Status">
        <template v-if="scope.row.status != null" slot-scope="scope">
          <el-dropdown class="task-status-dropdown" placement="bottom-end" @command="updateStatus">
            <span>
              <TaskStatus
                :task-status="scope.row.status"
                @status-options="onStatusOptionsReceived"
              />
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="option in statusOptions"
                :key="option.value"
                :command="{
                  target: scope.row,
                  status: option.value
                }"
              >
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column label="Priority">
        <template v-if="scope.row.priority != null" slot-scope="scope">
          <el-dropdown class="task-priority-dropdown" placement="bottom-end" @command="updatePriority">
            <span>
              <TaskPriority
                :task="scope.row.priority"
                @priority-options="onPriorityOptionsReceived"
              />
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="option in priorityOptions"
                :key="option.value"
                :command="{
                  target: scope.row,
                  priority: option.value
                }"
              >
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column label="Content">
        <template slot-scope="scope">
          <el-tooltip class="item" placement="left" :open-delay="200">
            <div slot="content" style="max-width:500px;">
              <span v-html="$sanitizeHtml(scope.row.content)" />
            </div>
            <div class="text-truncate cursor-pointer">
              {{ scope.row.content }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column v-if="!hideSearch" label="Client User" sortable="custom" prop="client_contact_id">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.client_contact_id"
            class="text-truncate link"
            :to="{ name:'ClientContactDetail', params: { contact_id: scope.row.client_contact_id }}">
            {{ scope.row.client_contact?.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column v-if="!hideSearch" label="Client" sortable="custom" prop="client_id">
        <template v-if="scope.row.client_id" slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column label="Reminder Date" sortable="custom" prop="reminder_at">
        <template v-if="scope.row.reminder_at != null" slot-scope="scope">
          {{ scope.row.reminder_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Completion Date" sortable="custom" prop="complete_at">
        <template v-if="scope.row.complete_at != null" slot-scope="scope">
          {{ scope.row.complete_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column v-if="!hideSearch" label="Type">
        <template slot-scope="scope">
          {{ scope.row.client_id ? 'Client' : 'Client User' }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="100">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#13ce66"
            title="Confirm to Complete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="success"
            @confirm="completeItem(scope.row)"
          >
            <el-link
              slot="reference"
              :disabled="scope.row.status === 'COMPLETED'"
              type="primary"
              :underline="false"
              icon="el-icon-finished"
              class="mr-8" />
          </el-popconfirm>
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteItem(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
          <add-task-dashboard :table-row-data="scope.row" show-clock is-edit @hide-dialog="handleSearch" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="!recentTasks"
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <el-dialog
      :visible.sync="isDialogVisible"
      title="Note"
      width="50%">
      <el-form
        ref="noteValidateForm"
        :model="form"
        label-width="110px">
        <el-form-item v-if="!hideSearch" label="Client" prop="client_contact_refs">
          <el-select
            v-model="form.client_id"
            placeholder="Client"
            clearable
            remote
            filterable
            :loading="searchClientLoading"
            :remote-method="getClientList"
            @change="handleId"
          >
            <el-option
              v-for="item in options.client_list"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!clientContactId" label="Client User" prop="client_contact_refs">
          <el-select
            v-model="form.client_contacts"
            placeholder="Client User"
            clearable
            multiple
            remote
            filterable
            class="multiple-select"
            :loading="searchContactLoading"
            :remote-method="getContactList"
          >
            <el-option
              v-for="item in options.client_contact_list"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-for="(item, index) in form.emails"
          v-show="!form.id"
          :key="index"
          :label="!index ? 'Notifications' : ''"
          :prop="'emails.' + index + '.value'"
          :rules="[{ validator: checkEmail, trigger: ['change','blur'] }]">
          <el-input
            v-model="item.value"
            class="w-200px"
          />
          <el-link
            v-if="index === form.emails.length-1"
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addEmailItem" />
          <el-link
            v-if="index"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeEmailItem(index)" />
        </el-form-item>
        <div class="flex">
          <el-form-item label="Communication Channel" prop="communication_method" required>
            <el-select
              v-model="form.communication_method"
              placeholder="Please select"
              clearable
              filterable
              class="w-200px"
            >
              <el-option
                v-for="item in client_options.note_communication_methods"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="form.communication_method === 'OTHER'"
            prop="communication_method_details"
            required
            label-width="0"
            class="flex-1 ml-8"
          >
            <el-input
              v-model="form.communication_method_details"
              type="text"
              placeholder="Please add Additional Detail"
              clearable
            />
          </el-form-item>
        </div>
        <el-form-item label="Tags" prop="tags">
          <note-tag-search v-model="form.tags" :tag-list="tag_list" class="multiple-select" />
        </el-form-item>
        <el-form-item label="Content" prop="content" required>
          <tinymce ref="note_content" v-model="form.content" :height="400" :toolbar="toolbar" />
        </el-form-item>
        <el-form-item label="Send Mail">
          <el-checkbox v-model="form.send_mail" :disabled="sendToClientThroughCapvisionPro || !hasEmailAddress" />
          <div v-if="sendToClientThroughCapvisionPro" class="warning line-height-normal">
            The legal rules have checked (Arrange/Emails and Invites from capvision-pro.com), it will not send relevant
            emails through the system.
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="!form.content.trim()"
          :loading="submitting"
          @click="actionSubmit">Save</el-button>
        <el-button type="text" @click="isDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import RouterLinkClient from '@/components/RouterLink/Client'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import TaskStatus from '@/views/client/task-dashboard/components/TaskStatus'
import TaskPriority from '@/views/client/task-dashboard/components/TaskPriority.vue'
import AddTaskDashboard from '@/views/client/detail/components/AddTaskDashboard'
import { mapGetters } from 'vuex'
import Tinymce from '@/components/Tinymce/index.vue'
import NoteTagSearch from '@/components/ClientNotes/NoteTagSearch'
import UserAPI from '@/api/user'

export default {
  name: 'ClientTaskDashboard',
  components: {
    NoteTagSearch,
    Tinymce,
    Pagination,
    RefreshButton,
    ClientSearch,
    UserSearch,
    RouterLinkClient,
    TaskStatus,
    TaskPriority,
    AddTaskDashboard,
  },
  props: {
    clientId: {
      type: [String, Number],
      default: 0,
    },
    clientContactId: {
      type: [String, Number],
      default: 0,
    },
    client: Object,
    dataUser: Object, // client contact 组件，指的是client contact
    recentTasks: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      })
    }
    return {
      checkEmail: validateEmail,
      toolbar: ['undo redo | bold italic link | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      searchClientLoading: false,
      searchContactLoading: false,
      list: [],
      selectLoading: false,
      contactList: [],
      total: 0,
      hideSearch: this.clientId || this.clientContactId,
      searchQuery: {
        assignee_id: undefined,
        client_id: this.clientId || undefined,
        client_contact_id: this.clientContactId || undefined,
        status: undefined,
        status_in: undefined,
        create_by_id: undefined,
        reminder_at: undefined,
        complete_at: undefined,
        page: 1,
        size: 20,
        sort: 'id desc',
        extra: 'client,client_contact.client,create_by,assignee,status_in',
        priority: undefined,
      },
      am: undefined,
      statusOptions: [],
      priorityOptions: [],
      isDialogVisible: false,
      form: this.initForm(),
      options: { client_list: [], client_contact_list: [] },
      tag_list: [],
      submitting: false,
      filterType: 'ACTIVE',
      filterOptions: [
        { 'value': 'ACTIVE', 'color': 'primary', 'label': 'Incomplete Assignments' },
        { 'value': 'ALL', 'color': 'primary', 'label': 'All' },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
      'roles',
      'info',
    ]),

    hasCreateRight() {
      return this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'AM / Sales'].includes(item.role.name)
      })
    },

    sendToClientThroughCapvisionPro() {
      if (this.form.client) {
        const has_rule = this.form.client.compliance_preference?.rule
        return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
          has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro
      }
      return false
    },
    hasEmailAddress() {
      return this.form.emails.some(item => item.value)
    },
  },

  mounted() {
    this.searchFilter('ACTIVE')
    this.getContact()
    this.getAmList()
  },

  methods: {
    initForm() {
      return {
        tags: [],
        content: '',
        client: null,
        client_id: this.clientId,
        client_contacts: [],
        emails: [{ value: '' }],
        communication_method: '',
        communication_method_details: '',
        send_mail: false,
      }
    },

    getList() {
      this.loading = true
      const params = Object.assign({}, this.searchQuery)
      if (params.reminder_at) {
        params.reminder_at_gte = params.reminder_at[0]
        params.reminder_at_lte = params.reminder_at[1]
        delete params.reminder_at
      }

      if (params.complete_at) {
        params.complete_at_gte = params.complete_at[0]
        params.complete_at_lte = params.complete_at[1]
        delete params.complete_at
      }
      params.status = params.status || undefined
      params.priority = params.priority || undefined

      return ClientAPI.getTaskDashboard(params).then(data => {
        this.list = data['list']
        this.total = data['count']
      }, () => {})
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },

    handleSortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.prop + order
        this.getList()
      }
    },

    completeItem(item) {
      return ClientAPI.updateTaskDashboard({ status: 'COMPLETED', id: item.id })
        .then(data => {
          this.$message({
            message: 'Complete successful',
            type: 'success',
          })
        }).finally(() => {
          this.getList()
        })
    },

    deleteItem(item) {
      return ClientAPI.deleteTaskDashboard(item.id)
        .then(data => {
          this.$message({
            message: 'Delete successful',
            type: 'success',
          })
        }).finally(() => {
          this.getList()
        })
    },

    getContact(val) {
      this.selectLoading = true
      const params = {
        name_like: val || undefined,
        page: 1,
        size: 15,
      }
      return ClientAPI.getFullContactList(params)
        .then(data => {
          this.contactList = data['list']
        })
        .finally(() => {
          this.selectLoading = false
        })
    },

    getTagList() {
      return ClientAPI.getClientNoteTags().then(data => {
        this.tag_list = data.list
      })
    },

    updateAssignee(val) {
      return ClientAPI.updateTaskDashboard({ assignee_id: val.id, id: val.target.id })
        .then(data => {
          this.$message({
            message: 'Assignee updated',
            type: 'success',
          })
        }).finally(() => {
          this.getList()
        })
    },

    onPriorityOptionsReceived(options) {
      this.priorityOptions = options
    },

    updatePriority(val) {
      return ClientAPI.updateTaskDashboard({ priority: val.priority, id: val.target.id })
        .then(data => {
          this.$message({
            message: 'Priority updated',
            type: 'success',
          })
        }).finally(() => {
          this.getList()
        })
    },

    onStatusOptionsReceived(options) {
      this.statusOptions = options
    },

    updateStatus(val) {
      return ClientAPI.updateTaskDashboard({ status: val.status, id: val.target.id })
        .then(data => {
          this.$message({
            message: 'Status updated',
            type: 'success',
          })
        }).finally(() => {
          this.getList()
        })
    },

    openNote() {
      this.form = this.initForm()
      this.isDialogVisible = true

      if (this.$refs['note_content']) this.$refs['note_content'].setContent(this.form.content)
      this.getTagList()
      this.getContactList()
      this.$nextTick(() => {
        this.$refs.noteValidateForm?.clearValidate()
      })
    },

    getClientList(val) {
      this.searchClientLoading = true
      const params = {
        name_like: val || undefined,
      }
      return ClientAPI.getClientList(params)
        .then((data) => {
          this.options.client_list = data.list
        })
        .finally(() => {
          this.searchClientLoading = false
        })
    },

    getContactList(val) {
      this.searchContactLoading = true
      this.form.client_contact_id = ''
      const params = {
        keyword: val || undefined,
      }

      if (this.clientId) {
        return ClientAPI.getContactList(this.clientId, params)
          .then((data) => {
            this.options.client_contact_list = data.list
          })
          .finally(() => {
            this.searchContactLoading = false
          })
      }
      return ClientAPI.getContactList(this.form.client_id, params)
        .then((data) => {
          this.options.client_contact_list = data.list
        })
        .finally(() => {
          this.searchContactLoading = false
        })
    },

    getAmList() {
      const params = {
        page: 1,
        size: 999,
        is_blocked: false,
        'user_role.role_ids': '5',
      }

      return UserAPI.getUsersList(params)
        .then(data => {
          this.am = data['list'].filter(item => item.name)
        })
    },

    handleId() {
      if (this.form.client_id) {
        this.getContactList()
        this.form.client = this.options.client_list.find(item => item.id === this.form.client_id)
        if (this.form.client.account_note_notification_emails.length) {
          this.form.client.account_note_notification_emails.map(item => {
            return { value: item }
          })
        }
      }
    },

    addEmailItem() {
      this.form.emails.push({ value: '' })
    },

    removeEmailItem(index) {
      this.form.emails.splice(index, 1)
    },

    actionSubmit() {
      this.$refs.noteValidateForm.validate((valid) => {
        if (valid) {
          const clientType = (this.clientContactId || this.form.client_contacts.length > 0)
            ? 'CLIENT_CONTACT'
            : 'CLIENT'
          const params = {
            client_id: this.form.client_id || undefined,
            type: clientType,
            client_contact_refs: clientType === 'CLIENT' ? this.form.client_contacts.map(item => {
              return { 'client_contact_id': item }
            }) : undefined,
            client_contact_id: this.clientId ? undefined : (this.clientContactId || this.form.client_contacts[0]),
            tag_refs: this.form.tags.map(item => {
              return { 'tag_id': item }
            }),
            notification_emails: this.form.send_mail ? this.form.emails.map(item => item.value) : [],
            content: this.form.content,
          }

          this.submitting = true
          return ClientAPI.addClientNote(params)
            .then(() => {
              this.$message({
                message: 'Save successful',
                type: 'success',
              })
            }).finally(() => {
              this.submitting = false
              this.isDialogVisible = false
            })
        } else {
          return false
        }
      })
    },

    searchFilter(filter) {
      if (filter === 'ACTIVE') {
        this.filterType = 'ACTIVE'
        this.searchQuery.assignee_id = this.info.id
        this.searchQuery.status_in = 'INITIAL,ON_HOLD,IN_PROGRESS,FOLLOW_UP'
      } else {
        this.filterType = 'ALL'
        this.searchQuery.assignee_id = undefined
        this.searchQuery.status_in = undefined
      }
      this.searchQuery.page = 1
      this.getList()
    },
  },
}
</script>

<style lang="scss" scoped>
.status-tags {
  margin-left: auto;
}

.task-assignee-dropdown {
  white-space: nowrap;
  font-size: 12px;
  cursor: pointer;
}

.task-status-dropdown {
  white-space: nowrap;
  font-size: 12px;
  cursor: pointer;
}

.task-priority-dropdown {
  white-space: nowrap;
  font-size: 12px;
  cursor: pointer;
}

.task-assignee-dropdown-items {
  height: 200px;
  overflow-y: scroll;
}

</style>
