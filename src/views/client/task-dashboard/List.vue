<template>
  <div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="list"
      @sort-change="handleSortData"
    >
      <el-table-column label="Type">
        <template slot-scope="scope">
          {{ scope.row.client_id ? 'Client' : 'Client User' }}
        </template>
      </el-table-column>
      <el-table-column label="Client" sortable="custom" prop="client_id">
        <template v-if="scope.row.client_id" slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column label="Client User" sortable="custom" prop="client_contact_id">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.client_contact_id"
            class="text-truncate link"
            :to="{ name:'ClientContactDetail', params: { contact_id: scope.row.client_contact_id }}">
            {{ scope.row.client_contact.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Reminder Date" sortable="custom" prop="reminder_at">
        <template slot-scope="scope">
          {{ scope.row.reminder_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Completion Date" sortable="custom" prop="complete_at">
        <template slot-scope="scope">
          {{ scope.row.complete_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Content">
        <template slot-scope="scope">
          <el-tooltip class="item" placement="left" :open-delay="200">
            <div slot="content" style="max-width:500px;">
              <span v-html="$sanitizeHtml(scope.row.content)" />
            </div>
            <div class="text-truncate cursor-pointer">
              {{ scope.row.content }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Create By">
        <template slot-scope="scope">
          {{ scope.row.create_by.name }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="70">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="deleteItem(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: 'List',
  props: {
    list: Array,
  },
}
</script>

<style scoped>

</style>
