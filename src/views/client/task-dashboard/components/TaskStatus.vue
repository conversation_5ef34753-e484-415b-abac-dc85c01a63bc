<template>
  <div :class="statusType" class="inline-block">
    <span>{{ taskStatus | getLabel(status_options) }}</span>
  </div>
</template>

<script>
export default {
  name: 'TaskStatus',
  props: { taskStatus: String },
  data() {
    return {
      status_options: [
        { label: 'Initial', value: 'INITIAL' },
        { label: 'On Hold', value: 'ON_HOLD' },
        { label: 'In Progress', value: 'IN_PROGRESS' },
        { label: 'Follow Up', value: 'FOLLOW_UP' },
        { label: 'Completed', value: 'COMPLETED' }],
    }
  },
  computed: {
    statusType() {
      switch (this.taskStatus) {
        case 'INITIAL':
          return 'orange-text'
        case 'ON_HOLD':
          return 'yellow-text'
        case 'IN_PROGRESS':
          return 'blue-text'
        case 'FOLLOW_UP':
          return 'red-text'
        case 'COMPLETED':
          return 'green-text'
      }
      return undefined
    },
  },
  mounted() {
    this.$emit('status-options',
      this.status_options)
  },
}
</script>

<style scoped>
.red-text {
  color: #fa2222;
}

.yellow-text {
  color: #c5a52c;
}

.blue-text {
  color: #40408d;
}

.green-text {
  color: #2cc550;
}

.orange-text {
  color: #df7a3c;
}
</style>
