<template>
  <div :class="priorityType">
    <span>{{ task|getLabel(priority_options) }}</span>
  </div>
</template>

<script>
export default {
  name: 'TaskPriority',
  props: { task: String },
  data() {
    return {
      priority_options: [
        { label: 'Urgent', value: 'URGENT' },
        { label: 'High', value: 'HIGH' },
        { label: 'Medium', value: 'MEDIUM' },
        { label: 'Low', value: 'LOW' }],
    }
  },
  computed: {
    priorityType() {
      switch (this.task) {
        case 'URGENT':
          return 'red-text'
        case 'HIGH':
          return 'orange-text'
        case 'MEDIUM':
          return 'yellow-text'
        case 'LOW':
          return 'gray-text'
      }
      return undefined
    },
  },
  mounted() {
    this.$emit('priority-options',
      this.priority_options)
  },
}
</script>

<style scoped>
.yellow-text {
  display: inline-block;
  color: #c5a52c;
}

.red-text {
  display: inline-block;
  color: #df3c3c;
}

.gray-text {
  display: inline-block;
  color: #3f3f3f;
}

.orange-text {
  display: inline-block;
  color: #df7a3c;
}
</style>
