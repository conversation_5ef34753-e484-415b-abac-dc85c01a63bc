<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-input
        v-model="searchQuery.name_like"
        class="w-200px filter-item"
        placeholder="Name"
        @input="handleSearch"
      />
      <el-select
        v-model="searchQuery.type_in"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Type"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.status_in"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Status"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.potential"
        clearable
        class="w-200px filter-item"
        placeholder="Potential"
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.potential"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.product_of_interests"
        clearable
        multiple
        class="w-200px filter-item"
        placeholder="Product"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.product"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <location
        v-model="searchQuery.location_ids"
        class="w-200px filter-item inline-block"
        placeholder="Headquarters"
        region
        @change="handleSearch"
      />
      <el-select
        v-model="searchQuery.pay_way_in"
        clearable
        collapse-tags
        multiple
        class="w-200px filter-item"
        placeholder="Pay Way"
        @change="handleSearch"
      >
        <el-option
          v-for="item in contract_options.payWay"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="searchQuery.date_range"
        type="daterange"
        range-separator="-"
        start-placeholder="Contract end date"
        end-placeholder="Contract end date"
        :default-time="['00:00:00', '23:59:59']"
        class="filter-item w-120px"
        @change="handleSearch"
      />
      <user-search
        v-model="searchQuery.am_id"
        class="w-200px filter-item"
        clearable
        role="am"
        placeholder="AM"
        @change="handleSearch" />
      <el-select
        v-model="searchQuery.am_team_id"
        clearable
        class="w-200px filter-item"
        placeholder="AM Team"
        @change="handleSearch"
      >
        <el-option
          v-for="item in am_team_options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        v-if="hasCreateRight"
        type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="actionGotoCreate()">New
      </el-button>
    </div>

    <el-table
v-loading="loading"
              :data="tableData"
              border
              stripe
              @sort-change="handleSortData">
      <el-table-column label="Name" sortable="custom" prop="name">
        <template slot-scope="scope">
          <router-link-client :data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column label="Type">
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(client_options.type) }}
        </template>
      </el-table-column>
      <el-table-column label="Status">
        <template slot-scope="scope">
          {{ scope.row.status | getLabel(client_options.status) }}
        </template>
      </el-table-column>
      <el-table-column label="Potential">
        <template slot-scope="scope">
          {{ scope.row.potential | getLabel(client_options.potential) }}
        </template>
      </el-table-column>
      <el-table-column label="Product">
        <template slot-scope="scope">
          {{ scope.row.product_of_interests | getLabels(client_options.product) }}
        </template>
      </el-table-column>
      <el-table-column label="Headquarters">
        <template v-if="scope.row.location" slot-scope="scope">
          {{ scope.row.location.name }}
        </template>
      </el-table-column>
      <el-table-column prop="contract_data.unit_price" label="Unit Price US / China" />
      <el-table-column prop="contract_data.remaining_hours" label="Remaining Hours">
        <template v-if="scope.row.contract_data" slot-scope="scope">
          <span v-if="scope.row.contract_data.pay_way === 'PAYGO'">-</span>
          <span v-else>{{ scope.row.contract_data.remaining_hours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="contract_data.pay_way" label="Pay Way">
        <template v-if="scope.row.contract_data" slot-scope="scope">
          {{ scope.row.contract_data.pay_way | getLabel(contract_options.payWay) }}
        </template>
      </el-table-column>

      <el-table-column prop="effective_contract.end_time" label="Contract End Date" sortable="custom">
        <template v-if="scope.row.contract_data" slot-scope="scope">
          {{ scope.row.contract_data.end_date |momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column prop="account_managers.user.name" label="AM" sortable="custom">
        <template slot-scope="scope">
          <div class="line-height-normal">{{ scope.row.ams }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="am_team" label="AM Team" />
      <el-table-column label="Action" width="60" align="center">
        <template slot-scope="scope">
          <add-task-dashboard :data-client="scope.row" :show-clock="true" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <router-view />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ClientAPI from '@/api/client'
import waves from '@/directive/waves'
import { debounce } from '@/utils/tool'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterTools from '@/utils/tool-router'
import Location from '@/components/Location'
import RefreshButton from '@/components/RefreshButton/index'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import AddTaskDashboard from '@/views/client/detail/components/AddTaskDashboard'

export default {
  name: 'Client',
  components: {
    RefreshButton,
    Pagination,
    RouterLinkClient,
    UserSearch,
    AddTaskDashboard,
    Location,
  },
  directives: { waves },
  data() {
    return {
      am_team_options: [{ name: '', id: 0 }],
      loading: false,
      tableData: [],
      total: NaN,
      searchQuery: {
        name_like: undefined,
        type_in: [],
        status_in: [],
        date_range: [],
        pay_way_in: undefined,
        am_id: undefined,
        am_team_id: undefined,
        potential: undefined,
        product_of_interests: undefined,
        location_ids: [],
        page: 1,
        size: 10,
        sort: undefined,
        extra: 'account_managers.user,contracts,contracts.used_hours,registered_address,effective_contract,location',
        aggregation: 'status,type',
      },

    }
  },
  computed: {
    ...mapGetters([
      'client_options',
      'contract_options',
      'roles',
    ]),

    hasCreateRight() {
      return this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'AM / Sales'].includes(item.role.name)
      })
    },
  },
  mounted() {
    RouterTools.resolveRouteQuery(this.$route, this.searchQuery, ['type_in', 'status_in', 'location_ids'])
    this.getAmTeamList().then(() => {
      this.getList()
    })
  },
  methods: {
    getList() {
      this.loading = true
      const {
        name_like,
        type_in,
        status_in,
        pay_way_in,
        am_id,
        am_team_id,
        page,
        size,
        extra,
        sort,
        aggregation,
        date_range,
        location_ids,
        potential,
        product_of_interests,
      } = this.searchQuery

      const params = {
        page, size, extra, sort, aggregation, am_team_id,
        location_ids: location_ids.join() || undefined,
        name_like: name_like || undefined,
        potential: potential || undefined,
        product_of_interests_contains_any: this.formatParams(product_of_interests),
        status_in: this.formatParams(status_in),
        type_in: this.formatParams(type_in),
        'effective_contract.payway_in': this.formatParams(pay_way_in),
        'account_managers.user.id': am_id || undefined,
      }

      if (date_range.length) {
        params['effective_contract.end_time_gte'] = date_range[0]
        params['effective_contract.end_time_lte'] = date_range[1]
      }

      RouterTools.saveQueryToRouter(params)

      return ClientAPI.getClientList(params)
        .then(data => {
          this.tableData = data.list.map(item => {
            item.contract_data = this.getContract(item.contracts)
            item.am_team = this.getAmTeamName(item.am_team_id)
            if (item.account_managers) {
              item.ams = item.account_managers.map(am => {
                if (am.user) {
                  return am.user.name
                }
              }).join(', ')
            }
            return item
          })

          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSortData({ prop, order }) {
      const dict = {
        'ascending': 'asc',
        'descending': 'desc',
      }
      if (!order) {
        this.searchQuery.sort = undefined
      } else {
        this.searchQuery.sort = `${prop} ${dict[order]}`
      }
      this.getList()
    },

    getContract(list) {
      let effective_list = []
      let result
      effective_list = list.filter(item => item.effective_status === 'EFFECTIVE')
      switch (effective_list.length) {
        case 0:
          result = null
          break
        case 1:
          result = formatContract(effective_list[0])
          break
        default:
          result = getNonBaseContract(effective_list)
          break
      }

      return result

      function getNonBaseContract(list) {
        const contract_list = list.filter(item => item.payway !== 'PROJECT_BASE')
        if (contract_list.length === 1) {
          return formatContract(contract_list[0])
        } else {
          console.log('There is a problem with the customer contract, please contact the management')
          return null
        }
      }

      function formatContract(contract) {
        let contract_size,
          remaining_hours,
          end_date

        const pay_way = contract.payway
        let unit_price = `${contract.unit_price_us} / ${contract.unit_price_china} ${contract.currency}`
        switch (pay_way) {
          case 'TRIAL':
            contract_size = null
            remaining_hours = null
            end_date = contract.end_time
            break
          case 'PAYGO':
            contract_size = `${contract.unit_price_china}  ${contract.currency} / H`
            remaining_hours = null
            end_date = contract.end_time
            break
          case 'PREPAY':
            contract_size = `${contract.charge_hours} H`
            remaining_hours = (contract.charge_hours - contract.used_hours).toFixed(4)
            end_date = contract.end_time
            break
          case 'PROJECT_BASE':
            unit_price = null
            contract_size = `${contract.contract_size} ${contract.currency}`
            remaining_hours = null
            end_date = null
            break
          default:
            break
        }
        return {
          pay_way,
          contract_size,
          remaining_hours,
          end_date,
          unit_price,
        }
      }
    },

    getAmTeamList() {
      return new Promise((resolve, reject) => {
        ClientAPI.getAmTeamList()
          .then(data => {
            this.am_team_options = data.list.map(item => {
              return { name: item.name, id: item.id }
            })
            return resolve(true)
          }, error => {
            return reject(error)
          })
      })
    },

    getAmTeamName(id) {
      let result = ''
      this.am_team_options.forEach(item => {
        if (item.id === id) {
          result = item.name
        }
      })
      return result
    },

    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },

    actionGotoCreate() {
      this.$router.push({ name: 'CreateClient' })
    },

    formatParams(list) {
      return (Array.isArray(list) && list || []).map(item => {
        return item
      })
        .join() || undefined
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
