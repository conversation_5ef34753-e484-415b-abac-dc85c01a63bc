<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <user-search
        v-model="searchQuery.user_id"
        :extra-member="extraMembers"
        role="am"
        placeholder="AM"
        class="w-200px filter-item"
        @change="handleSearch"
      />
      <div class="filter-item inline-block position-relative">
        <el-date-picker
          v-model="searchQuery.this_period"
          type="daterange"
          start-placeholder="This Period Start"
          end-placeholder="This Period End"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="thisPickerOptions"
          style="width: 250px;"
          @change="handleSearch"
        />
        <div class="filter-title">This Period</div>
      </div>
      <div class="filter-item inline-block position-relative">
        <el-date-picker
          v-model="searchQuery.compare_period"
          type="daterange"
          start-placeholder="Comparison Period Start"
          end-placeholder="Comparison Period End"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="comparisonPickerOptions"
          style="width: 250px;"
          @change="handleSearch"
        />
        <div class="filter-title">Comparison Period</div>
      </div>
      <el-select
        v-model="searchQuery.type"
        clearable
        placeholder="Client Type"
        class="filter-item w-200px"
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchQuery.status_in"
        clearable
        multiple
        placeholder="Client Status"
        class="filter-item w-200px"
        @change="handleSearch"
      >
        <el-option
          v-for="item in client_options.status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <el-descriptions
      v-if="aggregatedTable"
      v-loading="loading"
      :column="3"
      border
      class="mb-8"
      style="max-width: 800px;"
    >
      <el-descriptions-item label="This Period" label-style="text-align: center; font-weight: bold;">-</el-descriptions-item>
      <el-descriptions-item label="Comparison Period" label-style="text-align: center; font-weight: bold;">-</el-descriptions-item>
      <el-descriptions-item label="Revenue Comparison" label-style="text-align: center; font-weight: bold;">-</el-descriptions-item>
      <el-descriptions-item label="Total Revenue">
        {{ aggregatedTable.this_period_revenue_string }}
      </el-descriptions-item>
      <el-descriptions-item label="Total Revenue">
        {{ aggregatedTable.comparison_period_revenue_string }}
      </el-descriptions-item>
      <el-descriptions-item label="Revenue Change">
        {{ aggregatedTable.revenue_change_string }}
      </el-descriptions-item>
      <el-descriptions-item label="Consultation Revenue">
        {{ aggregatedTable.this_period_call_revenue_string }}
      </el-descriptions-item>
      <el-descriptions-item label="Consultation Revenue">
        {{ aggregatedTable.comparison_period_call_revenue_string }}
      </el-descriptions-item>
      <el-descriptions-item label="Revenue Percent Change">
        {{ aggregatedTable.revenue_percent_change }}
      </el-descriptions-item>
      <el-descriptions-item label="New Clients">
        {{ aggregatedTable.this_period_new_clients }}
      </el-descriptions-item>
      <el-descriptions-item label="New Clients">
        {{ aggregatedTable.comparison_period_new_clients }}
      </el-descriptions-item>
      <el-descriptions-item label="Consultation Change">
        {{ aggregatedTable.consultation_projects_change }}
      </el-descriptions-item>
      <el-descriptions-item label="New Consultation Projects">
        {{ aggregatedTable.this_period_new_consultation_projects }}
      </el-descriptions-item>
      <el-descriptions-item label="New Consultation Projects">
        {{ aggregatedTable.comparison_period_new_consultation_projects }}
      </el-descriptions-item>
      <el-descriptions-item label="Clients Change">
        {{ aggregatedTable.clients_change }}
      </el-descriptions-item>
      <el-descriptions-item label="" />
    </el-descriptions>

    <el-table
      v-loading="loading"
      :data="clientTable"
      :cell-class-name="handleCellClassName"
      border
      stripe
    >
      <el-table-column label="Client" min-width="150" class-name="column-border-right">
        <template v-slot="{row}">
          <router-link-client :data="{ id: row.client_id, name: row.account_name }" />
        </template>
      </el-table-column>
      <el-table-column label="Users" align="center" class-name="column-border-right column-border-bottom">
        <el-table-column label="Active" prop="active_users" />
        <el-table-column label="Total" prop="total_users" class-name="column-border-right" />
      </el-table-column>
      <el-table-column label="This Period" align="center" class-name="column-border-right column-border-bottom">
        <el-table-column label="Total Revenue" prop="this_period_revenue_string" />
        <el-table-column label="Cost (USD)" prop="this_period_cost" />
        <el-table-column label="Margin" prop="this_period_margin" />
        <el-table-column label="New Consultation Projects" prop="this_period_new_consultation_projects" />
        <el-table-column label="Client Hours" prop="this_period_hours" class-name="column-border-right" />
      </el-table-column>
      <el-table-column label="Comparison Period" align="center" class-name="column-border-right column-border-bottom">
        <el-table-column label="Total Revenue" prop="comparison_period_revenue_string" />
        <el-table-column label="Cost (USD)" prop="comparison_period_cost" />
        <el-table-column label="Margin" prop="comparison_period_margin" />
        <el-table-column label="New Consultation Projects" prop="comparison_period_new_consultation_projects" />
        <el-table-column label="Client Hours" prop="comparison_period_hours" class-name="column-border-right" />
      </el-table-column>
      <el-table-column label="Revenue Comparison" align="center" class-name="column-border-bottom">
        <el-table-column label="Revenue Change" prop="revenue_change_string" />
        <el-table-column label="Revenue Percent Change" prop="revenue_percent_change" />
        <el-table-column label="Client Hours Change" prop="call_client_hours_change" />
        <el-table-column label="Cost Percent Change" prop="cost_percent_change" />
        <el-table-column label="Margin Change" prop="margin_change" />
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import SalesKpiAPI from '@/api/sales-kpi'
import { mapGetters } from 'vuex'
import moment from 'moment-timezone'
import RouterLinkClient from '@/components/RouterLink/Client'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'

export default {
  name: 'ClientRevenueDashboard',
  components: {
    Pagination,
    RefreshButton,
    UserSearch,
    RouterLinkClient,
  },
  data() {
    return {
      extraMembers: [],
      thisPickerOptions: {
        shortcuts: this.initDatePickerOptions(),
      },
      comparisonPickerOptions: {
        shortcuts: this.initDatePickerOptions(true),
      },
      loading: false,
      searchQuery: {
        user_id: undefined,
        type: undefined,
        status_in: ['EXECUTE'],
        include_aggregated_table: true,
        this_period: [],
        compare_period: [],
        page: 1,
        size: 20,
      },
      aggregatedTable: null,
      clientTable: [],
      total: 0,
    }
  },

  computed: {
    ...mapGetters([
      'uid',
      'info',
      'client_options',
      'roles',
    ]),
  },

  mounted() {
    this.initSearchQuery()
    this.getList()
  },

  methods: {
    initSearchQuery() {
      this.searchQuery.user_id = this.uid
      this.extraMembers = [{ user: { name: this.info.name, id: this.uid } }]
      const last_month = moment().subtract(1, 'month')
      this.searchQuery.this_period = [
        moment().startOf('month').toISOString(),
        moment().endOf('month').toISOString(),
      ]
      this.searchQuery.compare_period = [
        last_month.startOf('month').toISOString(),
        last_month.endOf('month').toISOString(),
      ]
    },
    initDatePickerOptions(is_compare = false) {
      let shortcuts = [
        {
          text: 'This Month',
          onClick: (picker) => {
            const start = moment().startOf('month').toISOString()
            const end = moment().endOf('month').toISOString()
            picker.$emit('pick', [start, end])
            if (is_compare) return
            this.searchQuery.compare_period = [
              moment().subtract(1, 'month').startOf('month').toISOString(),
              moment().subtract(1, 'month').endOf('month').toISOString(),
            ]
          },
        },
        {
          text: 'Last Month',
          onClick: (picker) => {
            const start = moment().subtract(1, 'month').startOf('month').toISOString()
            const end = moment().subtract(1, 'month').endOf('month').toISOString()
            picker.$emit('pick', [start, end])
            if (is_compare) return
            this.searchQuery.compare_period = [
              moment().subtract(2, 'month').startOf('month').toISOString(),
              moment().subtract(2, 'month').endOf('month').toISOString(),
            ]
          },
        },
        {
          text: 'Quarter',
          onClick: (picker) => {
            const start = is_compare ? moment().subtract(1, 'quarter').startOf('quarter').toISOString() : moment().startOf('quarter').toISOString()
            const end = is_compare ? moment().subtract(1, 'quarter').endOf('quarter').toISOString() : moment().endOf('quarter').toISOString()
            picker.$emit('pick', [start, end])
            if (is_compare) return
            this.searchQuery.compare_period = [
              moment().subtract(1, 'quarter').startOf('quarter').toISOString(),
              moment().subtract(1, 'quarter').endOf('quarter').toISOString(),
            ]
          },
        },
        {
          text: 'This Year',
          onClick: (picker) => {
            const start = moment().startOf('year').toISOString()
            const end = moment().endOf('year').toISOString()
            picker.$emit('pick', [start, end])
            if (is_compare) return
            this.searchQuery.compare_period = [
              moment().subtract(1, 'year').startOf('year').toISOString(),
              moment().subtract(1, 'year').endOf('year').toISOString(),
            ]
          },
        },
        {
          text: 'Last Year',
          onClick: (picker) => {
            const start = moment().subtract(1, 'year').startOf('year').toISOString()
            const end = moment().subtract(1, 'year').endOf('year').toISOString()
            picker.$emit('pick', [start, end])
            if (is_compare) return
            this.searchQuery.compare_period = [
              moment().subtract(2, 'year').startOf('year').toISOString(),
              moment().subtract(2, 'year').endOf('year').toISOString(),
            ]
          },
        },
      ]
      if (is_compare) {
        shortcuts = [
          ...shortcuts,
          {
            text: 'YTD',
            onClick: (picker) => {
              const start = moment().subtract(1, 'year').startOf('year').toISOString()
              const end = moment().subtract(1, 'year').toISOString()
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Previous Period',
            onClick: (picker) => {
              const [this_start, this_end] = this.searchQuery.this_period
              const days = moment(this_end).diff(moment(this_start), 'days') + 1
              const start = moment(this_start).subtract(days, 'day').toISOString()
              const end = moment(this_end).subtract(days, 'days').toISOString()
              picker.$emit('pick', [start, end])
            },
          },
        ]
      }
      return shortcuts
    },
    getList() {
      this.loading = true
      const { page, size, type, status_in, include_aggregated_table, user_id, this_period, compare_period } = this.searchQuery
      const params = {
        page,
        size,
        include_aggregated_table,
        user_id: user_id || undefined,
        type: type || undefined,
        status_in: status_in?.join() || undefined,
        start_instant: this_period?.[0] || undefined,
        end_instant: this_period?.[1] || undefined,
        compare_start_instant: compare_period?.[0] || undefined,
        compare_end_instant: compare_period?.[1] || undefined,
      }
      return SalesKpiAPI.getSalesKpiDashboard(params)
        .then(data => {
          this.aggregatedTable = data.aggregated_table
          this.clientTable = data.client_table
          this.total = data.client_count
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },

    handleCellClassName({ row, column }) {
      if (column.label.includes('Change')) {
        const value = row[column.property]
        const type = typeof value
        const result = type === 'string' ? parseFloat(value?.replace(/%/g, '')?.replace('$', '')) : +value
        if (result > 0) return 'success'
        if (result < 0) return 'danger'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.filter-title {
  position: absolute;
  top: -50%;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #909399;
}

::v-deep .column-border-right {
  border-right: 2px solid #ccd1dc !important;
}

::v-deep .column-border-bottom {
  border-bottom: 2px solid #ccd1dc !important;
}
</style>
