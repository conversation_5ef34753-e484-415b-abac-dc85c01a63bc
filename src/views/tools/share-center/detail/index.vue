<template>
  <div v-loading="isLoading" class="components-container">
    <h2># {{ data.id }} {{ data.title }}</h2>

    <el-form
      v-if="!isEdit"
      label-position="right"
      label-width="150px"
    >
      <el-form-item label="Title">{{ data.title }}</el-form-item>
      <el-form-item label="Content">
        <div class="line-height-normal" v-html="$sanitizeHtml(data.content)" />
      </el-form-item>
      <el-form-item label="Create User">
        <slot v-if="data.create_user">{{ data.create_user.name}}</slot>
      </el-form-item>
      <el-form-item label="Update User">
        {{data.update_user ? data.update_user.name : null}}
      </el-form-item>
      <el-form-item label="Update Time">{{ data.update_at | momentFormat()}}</el-form-item>
      <el-form-item label="File">
        <div
          v-for="item in data.attachments"
          :key="item.file_url">
          <el-link
            :href="`${item.file_url}?rename=${item.file_name}`"
            type="primary"
            :underline="false">
            <i class="el-icon-download" />
            {{item.file_name}}
          </el-link>
        </div>
      </el-form-item>
      <el-form-item v-if="isRD">
        <el-button size="mini" icon="el-icon-edit" @click="isEdit = true">Edit</el-button>
      </el-form-item>
    </el-form>

    <edit-article v-else :data="data" is-edit @save="updateArticle" />
  </div>
</template>

<script>
import ArticleAPI from '@/api/article'
import EditArticle from '../update'
import { RouteTools } from '@/utils/tool'
import { mapGetters } from 'vuex'

export default {
  name: 'ArticleDetail',
  components: { EditArticle },
  data() {
    return {
      article_id: this.$route.params.id,
      data: {},
      isEdit: false,
      isLoading: false,
    }
  },
  computed: {
    ...mapGetters(['info']),
    isRD() {
      return this.info.roles.find(item => item.role.name === 'RD')
    },
  },
  created() {
    this.isLoading = true

    return ArticleAPI.getArticleDetail(this.article_id)
      .then(data => {
        this.data = data

        return RouteTools.setRouterMetaTitle(data.title, this.$route)
      })
      .finally(() => {
        this.isLoading = false
      })
  },
  methods: {
    updateArticle(data) {
      Object.assign(this.data, data)
      this.isEdit = false
    },
  },
}
</script>

<style scoped>
</style>
