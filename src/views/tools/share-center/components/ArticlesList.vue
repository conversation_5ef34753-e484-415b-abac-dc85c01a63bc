<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-button :disabled="!isRD" class="filter-item" type="success" icon="el-icon-plus" @click="actionGoToCreate">New</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe>
      <el-table-column label="ID" prop="id" width="50" />
      <el-table-column label="Title">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sticky" type="success">
            <svg-icon icon-class="top" />
            Top
          </el-tag>
          <router-link
            class="text-truncate link"
            :to="{ name: detailRouterName, params: { id: scope.row.id } }">
            {{ scope.row.title }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Create User">
        <template slot-scope="scope">{{ scope.row.create_user.name }}</template>
      </el-table-column>
      <el-table-column label="Update User">
        <template slot-scope="scope">
          {{ scope.row.update_user ? scope.row.update_user.name : null }}
        </template>
      </el-table-column>
      <el-table-column label="Update Date" width="130">
        <template slot-scope="scope">
          {{ scope.row.update_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="File">
        <template slot-scope="scope">
          <div @click.stop="">
            <el-link
              v-for="item in scope.row.attachments"
              :key="item.file_url"
              :href="`${item.file_url}?rename=${item.file_name}`"
              type="primary"
              :underline="false"
              class="mr-8">
              <i class="el-icon-download" />
              {{ item.file_name }}
            </el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="isRD" label="Action" width="80">
        <template slot-scope="scope">
          <div @click.stop="">
            <el-link type="primary" :underline="false" @click="actionSetTop(scope.row)">
              <svg-icon icon-class="top" />
            </el-link>

            <el-popconfirm
              icon="el-icon-info"
              icon-color="red"
              :title="`Are you sure to delete this article (id: ${scope.row.id})?`"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="danger"
              @confirm="actionDeleteArticle(scope.row.id)"
            >
              <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" />
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import ArticleAPI from '@/api/article'
import Pagination from '@/components/Pagination/index'
import RefreshButton from '@/components/RefreshButton/index'
import { mapGetters } from 'vuex'

export default {
  name: 'ArticlesList',
  components: { RefreshButton, Pagination },
  props: {
    type: String,
  },
  data() {
    return {
      loading: false,
      total: 0,
      tableData: [],
      searchQuery: {
        module: this.getModuleValue(),
        page: 1,
        size: 10,
      },
    }
  },
  computed: {
    ...mapGetters(['info']),
    detailRouterName() {
      return this.type === 'basic' ? 'BasicArticleDetail' : 'SalesArticleDetail'
    },
    isRD() {
      return this.info.roles.find(item => item.role.name === 'RD')
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      return ArticleAPI.getArticlesList(this.searchQuery)
        .then(data => {
          this.tableData = data['list']
          this.total = data['count']
        })
        .finally(() => {
          this.loading = false
        })
    },
    actionGoToCreate() {
      const router_name = this.type === 'basic' ? 'BasicCreateArticle' : 'SalesCreateArticle'
      this.$router.push({ name: router_name, query: { type: this.type } })
    },
    actionSetTop(val) {
      const params = {
        id: val.id,
        sticky: !val.sticky,
      }
      this.loading = true
      return ArticleAPI.updateArticle(params)
        .then(() => {
          this.getList()
        }, () => {
          this.loading = false
        })
    },
    actionDeleteArticle(id) {
      this.loading = true
      return ArticleAPI.deleteArticle(id)
        .then(() => {
          this.getList()
        }, () => {
          this.loading = false
        })
    },

    getModuleValue() {
      const module = this.type
      const module_params_dict = {
        basic: 'ALL_USER',
        sales: 'SALES',
      }
      return module_params_dict[module]
    },
  },
}
</script>

<style scoped>

</style>
