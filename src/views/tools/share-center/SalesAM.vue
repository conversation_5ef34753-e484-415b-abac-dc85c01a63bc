<template>
  <div className="app-container">
    <slot v-if="isCurrentRoute">
      <articles-list type="sales" />
    </slot>

    <router-view />
  </div>
</template>

<script>
import ArticlesList from '@/views/tools/share-center/components/ArticlesList'

export default {
  name: 'ShareCenterSalesAM',
  components: { ArticlesList },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'ShareCenterSalesAM'
    },
  },
}
</script>

<style scoped>

</style>
