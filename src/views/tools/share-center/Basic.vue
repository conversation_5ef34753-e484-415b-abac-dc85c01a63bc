<template>
  <div class="app-container">
    <slot v-if="isCurrentRoute">
      <articles-list type="basic" />
    </slot>

    <router-view />
  </div>
</template>

<script>
import ArticlesList from '@/views/tools/share-center/components/ArticlesList'

export default {
  name: 'ShareCenterBasic',
  components: { ArticlesList },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'ShareCenterAllUsers'
    },
  },
}
</script>

<style scoped>

</style>
