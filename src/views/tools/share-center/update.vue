<template>
  <div>
    <el-form ref="form" class="update-form" :model="form" :rules="rules" label-width="140px" :disabled="submitting">
      <el-form-item label="Title" prop="title">
        <el-input ref="name" v-model="form.title" />
      </el-form-item>
      <el-form-item label="Top">
        <el-switch v-model="form.sticky" />
      </el-form-item>
      <el-form-item label="Content">
        <tinymce v-model="form.content" />
      </el-form-item>
      <el-form-item label="File">

        <el-upload
          accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
          :action="upload_url"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :before-remove="beforeRemove"
          multiple
          :data="{'uid': uid}"
          :file-list="fileList">
          <div class="flex">
            <el-button size="small" type="success" icon="el-icon-upload">Upload</el-button>
            <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="!isEdit">
        <el-button type="primary" icon="el-icon-check" :disabled="!form.attachments.length" :loading="submitting" @click="onSave()">Save
        </el-button>
        <el-button type="text" :disabled="!form.attachments.length" @click="onSave()">Save & New</el-button>
      </el-form-item>
      <el-form-item v-else>
        <el-button type="primary" icon="el-icon-check" :loading="submitting" :disabled="!form.attachments.length" @click="onSave">Save</el-button>
        <el-button type="text" @click="$emit('save')">Cancel</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ArticleAPI from '@/api/article'
import Mixin from '@/components/FormPage/mixin'
import { mapGetters } from 'vuex'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'UpdateArticle',
  components: { Tinymce },
  mixins: [Mixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/articles',
      fileList: [],
      form: this.initForm(),
      rules: {
        title: [{ required: true, trigger: 'change' }],
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  mounted() {
    this.handleIsEdit()
  },
  methods: {
    initForm() {
      return {
        title: '',
        content: '',
        sticky: false,
        attachments: [],
        module: this.getModuleValue(),
      }
    },
    handleIsEdit() {
      if (this.isEdit && this.data) {
        for (const key in this.form) {
          this.form.id = this.data.id
          if (this.data[key]) {
            this.form[key] = this.data[key]
          }
        }
        this.getFileList(this.form.attachments)
      } else {
        this.form.name = this.$route.query.name || ''
      }
    },
    handleSuccess(response, file, fileList) {
      if (response.msg === 'ok') {
        const item = {
          file_name: response.data.file_name,
          file_url: response.data.url,
        }
        this.form.attachments.push(item)
      } else {
        this.$message(response.message)
      }
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    handleRemove(file, fileList) {
      const url = file.response ? file.response.data.url : file.url
      const index = this.form.attachments.findIndex(item => item.file_url === url)
      this.form.attachments.splice(index, 1)
    },
    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`, 'Notice', {
          type: 'warning',
        })
      }
    },
    getFileList(list) {
      if (list.length) {
        this.fileList = list.map(item => ({ name: item.file_name, url: item.file_url }))
      }
    },
    handleSave(val) {
      if (this.isEdit) {
        return ArticleAPI.updateArticle(val).then(() => {
          this.$emit('save', val)
        })
      } else {
        return ArticleAPI.createArticle(val).then(() => {
          this.$router.go(-1)
        })
      }
    },

    getModuleValue() {
      const module = this.$route.query.type
      const module_params_dict = {
        basic: 'ALL_USER',
        sales: 'SALES',
      }
      return module_params_dict[module]
    },
  },
}
</script>

<style scoped>

</style>
