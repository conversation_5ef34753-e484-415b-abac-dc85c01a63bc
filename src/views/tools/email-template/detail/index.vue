<template>
  <div v-loading="loading" class="components-container">
    <h2>#{{ data.id }} {{ data.name }}</h2>

    <el-form
      v-if="!isEdit && !loading"
      label-position="right"
      label-width="100px">
      <el-form-item label="Name">{{data.name}}</el-form-item>
      <!--      <el-form-item label="Type">{{data.type}}</el-form-item>-->
      <el-form-item label="Private">{{data.is_private}}</el-form-item>
      <el-form-item label="Subject">{{data.subject_template}}</el-form-item>
      <el-form-item label="Content" prop="content_template">
        <div class="email-content" v-html="data.content_template" />
      </el-form-item>
      <el-form-item label="">
        <el-button icon="el-icon-edit-outline" @click="isEdit = true">Edit</el-button>
      </el-form-item>
    </el-form>

    <edit-page v-if="isEdit && !loading" is-edit :data="data" @save="updateEmailTemplate" />
  </div>
</template>

<script>
import API from '@/api/email.js'
import EditPage from '../update'

export default {
  name: 'EmailTemplateDetail',
  components: { EditPage },
  data() {
    return {
      id: this.$route.params.id,
      loading: false,
      data: {},
      isEdit: true,
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.loading = true
      return API.getEmailDetail(this.id).then(data => {
        this.data = data
      }).finally(() => {
        this.loading = false
      })
    },
    updateEmailTemplate(data) {
      // this.isEdit = false
      Object.assign(this.data, data)
    },
  },
}
</script>

<style scoped>

</style>
