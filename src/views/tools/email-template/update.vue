<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" class="update-form" label-width="140px" :disabled="submitting">
      <el-form-item label="Name" prop="name">
        <el-input ref="name" v-model="form.name" />
      </el-form-item>
      <el-form-item label="Display Name" prop="display_name">
        <el-input v-model="form.display_name" />
      </el-form-item>
      <el-form-item label="Type" prop="content_type">
        <el-select
          v-model="form.content_type"
          filterable
          placeholder="Content Type"
          :disabled="!!projectId"
          style="width: 320px;"
          @change="actionLoadTags"
        >
          <el-option
            v-for="item in options.content_type_list"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="display.tag" label="Tag" prop="tags">
        <el-checkbox-group v-model="form.tags" size="mini">
          <el-checkbox
            v-for="item in options.email_tags"
            :key="item.name"
            :label="item.name"
            border>{{ item.description }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item v-if="display.is_private" label="Private" prop="is_private">
        <el-switch v-model="form.is_private" />
      </el-form-item>
      <el-form-item v-if="display.is_system_template" label="System Template" prop="is_system_template">
        <el-switch v-model="form.is_system_template" />
      </el-form-item>
      <el-form-item label="Draft" prop="is_draft">
        <el-switch v-model="form.is_draft" />
        <span class="info pl-5">If it is in draft status, the email will not be sent.</span>
      </el-form-item>
      <el-form-item
        v-if="display.should_apply_thymeleaf_engine_to_content"
        label="Apply Thymeleaf Engine To Content"
        prop="should_apply_thymeleaf_engine_to_content">
        <el-switch v-model="form.should_apply_thymeleaf_engine_to_content" />
      </el-form-item>
      <el-form-item v-if="isAdmin" label="Rank" prop="rank" class="w-200px">
        <el-input v-model="form.rank" type="number" />
      </el-form-item>
      <el-form-item label="Placeholders">
        <div slot="label">
          Placeholders<br>
          <el-button v-if="isAdmin" size="xs" @click="actionLoadPlaceholders">
            <span v-if="isPlaceholderAdvanceMode"><el-icon name="d-arrow-left" /> Lite</span>
            <span v-else>Advance <el-icon name="d-arrow-right" /></span>
          </el-button>
        </div>
        <div v-loading="loading_status.placeholder" class="container-placeholder">
          <el-table
            v-loading="submitting"
            stripe
            :show-header="false"
            class="table-list"
            :data="Object.entries(options.placeholder_list)"
          >
            <el-table-column prop="id" width="100">
              <template slot-scope="scope">
                <div class="mr-8">{{ placeholdersKeyFormat(scope.row[0]) }}</div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="flex flex-wrap">
                  <el-button
                    v-for="item in scope.row[1]"
                    :key="item.name"
                    type="primary"
                    size="xs"
                    plain
                    class="placeholder-item"
                    @click="insertPlaceholder(item)">
                    {{ placeholdersDescFormat(item.description, placeholdersKeyFormat(scope.row[0])) }}
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--          <div v-for="key in Object.keys(options.placeholder_list)" :key="key" class="mb-8">-->
          <!--            <div class="flex">-->
          <!--              <div class="mr-8">{{placeholdersKeyFormat(key)}}</div>-->
          <!--              <div class="flex flex-wrap">-->
          <!--                <el-button-->
          <!--                  v-for="item in options.placeholder_list[key]"-->
          <!--                  :key="item.name"-->
          <!--                  type="primary"-->
          <!--                  size="xs"-->
          <!--                  plain-->
          <!--                  class="placeholder-item"-->
          <!--                  @click="insertPlaceholder(item)">-->
          <!--                  {{placeholdersDescFormat(item.description,placeholdersKeyFormat(key))}}-->
          <!--                  &lt;!&ndash;                  {{item.hide_by_default}}&ndash;&gt;-->
          <!--                </el-button>-->
          <!--              </div>-->
          <!--            </div>-->

          <!--          </div>-->
        </div>
      </el-form-item>
      <el-form-item v-if="display.from_template" label="From Template" prop="from_template">
        <el-input v-model="form.from_template" style="width: 320px;" />
        <el-dropdown trigger="click" @command="actionSelectAddressTemplate($event, 'from_template')">
          <el-button icon="el-icon-arrow-down" size="xs" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in options.email_address_holder_list" :key="item.name" :command="item.name">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item v-if="display.to_template" label="To Template" prop="to_template">
        <el-input v-model="form.to_template" style="width: 320px;" />
        <el-dropdown trigger="click" @command="actionSelectAddressTemplate($event, 'to_template')">
          <el-button icon="el-icon-arrow-down" size="xs" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in options.email_address_holder_list" :key="item.name" :command="item.name">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item v-if="display.cc_template" label="Cc Template" prop="cc_template">
        <el-input v-model="form.cc_template" style="width: 320px;" clearable />
        <el-dropdown trigger="click" @command="actionSelectAddressTemplate($event, 'cc_template')">
          <el-button icon="el-icon-arrow-down" size="xs" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in options.email_address_holder_list" :key="item.name" :command="item.name">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item v-if="display.bcc_template" label="Bcc Template" prop="bcc_template">
        <el-input v-model="form.bcc_template" style="width: 320px;" clearable />
        <el-dropdown trigger="click" @command="actionSelectAddressTemplate($event, 'bcc_template')">
          <el-button icon="el-icon-arrow-down" size="xs" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in options.email_address_holder_list" :key="item.name" :command="item.name">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item v-if="display.reply_to_template" label="Reply To Template" prop="reply_to_template">
        <el-input v-model="form.reply_to_template" style="width: 320px;" clearable />
        <el-dropdown trigger="click" @command="actionSelectAddressTemplate($event, 'reply_to_template')">
          <el-button icon="el-icon-arrow-down" size="xs" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in options.email_address_holder_list" :key="item.name" :command="item.name">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
      <el-form-item label="Attachments" prop="attachments">
        <div v-for="(item, index) in form.attachments" :key="index">
          <div
            v-if="item.file_path"
            style="line-height: initial; margin: 5px 0;">
            <el-link
              type="primary"
              :href="item.file_path">{{ item.name }}
            </el-link>
            <el-link
              type="danger"
              style="margin-left: 1em;"
              @click="actionRemoveFile(index)">
              <el-icon name="delete" />
            </el-link>
          </div>
        </div>

        <el-upload
          class="inline-block"
          style="min-width: 200px;"
          accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
          :action="upload_url"
          :http-request="actionUpload"
          :before-upload="beforeUpload"
          :headers="{uid: uid}"
          :show-file-list="false"
        >
          <el-button
            size="mini"
            type="primary"
            :loading="uploadSubmitting">Upload...
          </el-button>
          <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="display.subject_template" label="Subject" prop="subject_template">
        <el-input id="subject-input" v-model="form.subject_template" @focus="subjectFocus = true" />
      </el-form-item>
      <el-form-item label="Content" prop="content_template">
        <el-tooltip
          placement="top"
          :disabled="!contentEditDisabled"
          content="No permission. Please contact IT if you need to make changes.">
          <div>
            <div :class="{'read-only':contentEditDisabled}">
              <tinymce
                v-if="!isLinkedinMessage"
                v-model="form.content_template"
                is-email
                p-origin-margin
                :readonly="contentEditDisabled"
                @focus="subjectFocus = false" />
              <el-input
                v-if="isLinkedinMessage"
                v-model="form.content_template"
                :disabled="contentEditDisabled"
                type="textarea"
                :rows="10" />
            </div>
          </div>
        </el-tooltip>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="onSave(routeName)">Save
        </el-button>
        <el-button v-if="!isEdit" type="text" @click="onSave()">Save & New</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Tinymce from '@/components/Tinymce'
import FormMixin from '@/components/FormPage/mixin'
import API from '@/api/email'
import { AppHttpService } from '@/utils/request'

export default {
  name: 'UpdateEmailTemplate',
  components: { Tinymce },
  mixins: [FormMixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/email_template_attachment',
      uploadSubmitting: false,
      form: this.initForm(),
      rules: {
        name: [
          {
            required: true,
            message: 'Please enter a name to distinguish it from other templates',
            trigger: 'change',
          }],
        subject_template: [
          {
            required: true,
            message: 'Please enter the subject of the email, you can use placeholders',
            trigger: 'change',
          }],
        content_template: [{ required: true, trigger: 'change' }],
      },
      subjectFocus: false,
      options: {
        email_tags: [],
        placeholder_list: [],
        content_type_list: [],
        email_address_holder_list: [],
      },
      config: {
        placeholders_display_all: false,
      },
      loading_status: {
        placeholder: false,
      },
    }
  },
  computed: {
    isPlaceholderAdvanceMode() {
      return this.config.placeholders_display_all
    },
    routeName() {
      const current = this.$route.name
      let route

      if (['CreateEmailTemplate', 'EmailTemplateDetail'].includes(current)) {
        route = 'EmailTemplate'
      }

      if (['ProjectCreateOutreach', 'ProjectOutreachDetail'].includes(current)) {
        route = 'ProjectOutreach'
      }

      if (['CommonProjectCreateOutreach', 'CommonProjectOutreachDetail'].includes(current)) {
        route = 'CommonProjectOutreach'
      }

      return route
    },
    ...mapGetters([
      'uid',
      'email_options',
      'roles',
    ]),

    isAdmin() {
      return this.roles.find(item => ['Admin', 'AdminExcludeCompliance'].includes(item.role.name))
    },
    isRD() {
      return this.roles.filter(item => item.role_id === 7).length > 0
    },

    isLinkedinMessage() {
      return ['LINKEDIN_MESSAGE', 'LINKEDIN_MESSAGE_CLIENT'].includes(this.form.content_type)
    },
    projectId() {
      return this.$route.params.project_id
    },
    contentType() {
      return this.$route.query.content_type
    },
    contentEditDisabled() {
      return this.form.should_apply_thymeleaf_engine_to_content && !this.isRD
    },
    display() {
      return {
        tag: this.isAdmin && !this.projectId,
        is_private: this.isAdmin && !this.projectId,
        is_system_template: this.isAdmin && !this.projectId,
        should_apply_thymeleaf_engine_to_content: this.isRD,
        rank: this.isAdmin,
        from_template: this.isAdmin && !this.projectId,
        to_template: this.isAdmin && !this.projectId,
        cc_template: !this.projectId,
        bcc_template: !this.projectId,
        reply_to_template: this.isAdmin,
        subject_template: this.contentType !== 'LINKEDIN_MESSAGE',
      }
    },
  },
  mounted() {
    if (this.contentType) this.form.content_type = this.contentType
    this.form.create_by_id = this.uid
    this.handleIsProject()
    this.handleIsEdit()
    this.handleDefaultContent()
    this.loadOptions()
  },
  methods: {
    initForm() {
      return {
        attachments: [],
        create_by_id: this.uid,
        name: '',
        display_name: '',
        rank: 0,
        content_type: 'PROJECT_OUTREACH',
        reference_id: 0,
        reference_type: 'DEFAULT',
        is_private: false,
        is_system_template: false,
        is_draft: false,
        should_apply_thymeleaf_engine_to_content: false,
        from_template: undefined,
        to_template: undefined,
        cc_template: undefined,
        bcc_template: undefined,
        reply_to_template: undefined,
        subject_template: '',
        content_template: '',
        tags: [],
        placeholder_categories: [],
      }
    },
    loadOptions() {
      const tasks = [
        this.getEmailPlaceholders(),
        this.getEmailTags({ content_type: this.form.content_type }),
        getEmailContentTypes(),
        this.getEmailAddressHolderList(),
      ]

      return Promise
        .all(tasks)
        .then(poly => {
          [
            this.options.placeholder_list,
            this.options.email_tags,
            this.options.content_type_list,
          ] = poly
        })

      function getEmailContentTypes() {
        return API.getEmailContentTypes()
      }
    },
    handleIsProject() {
      const project_id = this.$route.params.project_id
      if (project_id) {
        this.form.reference_type = 'PROJECT'
        this.form.reference_id = +project_id
      }
    },
    handleIsEdit() {
      if (this.isEdit && this.data) {
        Object.keys(this.form)
          .forEach(key => {
            this.form[key] = this.data[key]
          })
        if (!this.form.attachments) this.form.attachments = []
        if (!this.form.tags) this.form.tags = []
        this.form.id = +this.data.id
      }
    },

    /**
     * 根据页面需求自动加载样例模板
     */
    handleDefaultContent() {
      if (this.isEdit) return

      return this
        .getTemplatePlaceholder(this.contentType)
        .then(list => {
          if (Array.isArray(list) && list.length > 0) {
            this.form.subject_template = list[0].subject_template
            this.form.content_template = list[0].content_template
          }
        })
    },

    getTemplatePlaceholder(contentType) {
      const params = {
        tags_contains_all: 'NEW_TEMPLATE_PLACEHOLDER',
        content_type: contentType,
        is_system_template: true,
        pagination: false,
      }
      return API
        .getEmailList(params)
    },

    insertPlaceholder(item) {
      if (this.subjectFocus) {
        this.insertAtCursor(`{{${item.name}}}`)
      } else {
        window.tinymce.activeEditor.execCommand('mceInsertContent', false, `{{${item.name}}}`)
      }
    },
    async insertAtCursor(myValue) {
      const myField = document.querySelector('#subject-input')
      if (myField.selectionStart || myField.selectionStart === 0) {
        const startPos = myField.selectionStart
        const endPos = myField.selectionEnd
        this.form.subject_template = myField.value.substring(0, startPos) + myValue +
          myField.value.substring(endPos, myField.value.length)
        await this.$nextTick()
        myField.focus()
        myField.setSelectionRange(endPos + myValue.length, endPos + myValue.length)
      } else {
        this.form.subject_template += myValue
      }
    },
    handleSave(form) {
      if (this.isEdit) {
        return API.updateEmail(form)
          .then(() => {
            this.$emit('save', form)
          })
      } else {
        return API.createEmail(form)
      }
    },
    getEmailTags(params) {
      return API.getEmailTags(params)
    },
    actionLoadTags() {
      const params = { content_type: this.form.content_type }
      return this.getEmailTags(params)
        .then(data => {
          this.options.email_tags = data
        })
    },
    getEmailPlaceholders(is_show_all) {
      return API
        .getEmailPlaceholders(is_show_all)
        .then(data => {
          const placeholders = {}

          data.forEach(item => {
            if (Object.prototype.hasOwnProperty.call(placeholders, item.category)) {
              placeholders[item.category].push(item)
            } else {
              placeholders[item.category] = [item]
            }
          })

          return placeholders
        })
    },
    actionLoadPlaceholders() {
      this.config.placeholders_display_all = this.isPlaceholderAdvanceMode ? undefined : true
      this.loading_status.placeholder = true

      return this.getEmailPlaceholders(this.config.placeholders_display_all)
        .then(data => {
          this.options.placeholder_list = data
        })
        .finally(() => {
          this.loading_status.placeholder = false
        })
    },
    actionSelectAddressTemplate(value, key) {
      this.form[key] = `{{${value}}}`
    },

    getEmailAddressHolderList() {
      return API
        .getEmailPlaceholders(true)
        .then(data => {
          const hasEmail = new RegExp('_EMAIL$', 'g')
          const hsaEmails = new RegExp('_EMAILS$', 'g')
          this.options.email_address_holder_list = data.filter(
            item => item.name.match(hasEmail) || item.name.match(hsaEmails))
        })
    },

    placeholdersDescFormat(desc, type) {
      if (desc.indexOf(type) === 0) {
        return desc.substring(type.length + 1)
      } else {
        return desc
      }
    },
    placeholdersKeyFormat(value) {
      return value.toLowerCase()
        .replace(/_/g, ' ')
    },
    actionRemoveFile(index) {
      this.form.attachments.splice(index, 1)
    },
    actionUpload(ref) {
      const attachments = this.form.attachments
      const bodyFormData = new FormData()
      bodyFormData.append('file', ref.file)

      this.uploadSubmitting = true

      const config = {
        headers: ref.headers,
      }

      return AppHttpService
        .post('/email_templates/attachments', bodyFormData, config)
        .then(data => {
          const format = {
            name: ref.file.name,
            file_path: data['url'],
          }
          attachments.push(format)
        }, error => {
          return Promise.reject(error)
        })
        .finally(() => {
          this.uploadSubmitting = false
        })
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-checkbox {
  margin: 0 10px 4px 0;
}

::v-deep .el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__label {
  line-height: 22px;
}

::v-deep .el-table--mini td {
  padding: 1px;
}

.el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 0;
}

.container-placeholder {

  .el-button {
    margin: 2px;
  }
}

.read-only {
  pointer-events: none;
  opacity: 0.6;
}
</style>
