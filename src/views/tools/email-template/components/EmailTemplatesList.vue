<template>
  <div>
    <div class="filter-container">
      <el-button type="success" class="filter-item" icon="el-icon-plus" @click="actionGoToCreate">New</el-button>
    </div>
    <div v-if="!projectId" class="filter-container">
      <el-row type="flex" align="middle">
        <refresh-button class="filter-item" @action="getList" />
        <el-input
          v-model="searchQuery.keyword"
          type="text"
          placeholder="Search"
          class="search-input filter-item"
          suffix-icon="el-icon-search"
          clearable
          @input="handleSearch" />
        <el-select
          v-model="searchQuery.content_type_in"
          multiple
          filterable
          class="filter-item"
          collapse-tags
          placeholder="Content Type"
          style="width: 400px;"
          @change="handleSearch"
        >
          <el-option v-for="item in options.content_type_list" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
        <client-search
          v-model="searchQuery.reference_id"
          class="w-200px filter-item"
          @change="handleSearch" />
        <el-switch
          v-model="is_show_shared"
          active-text="All"
          inactive-text="Private"
          class="shared-switch filter-item"
          @change="handleFilter" />
      </el-row>
    </div>
    <el-row v-loading="loading" :gutter="16">
      <el-col v-for="item in tableData" :key="item.id" :md="12">
        <el-card class="box-card" shadow="hover">
          <el-row slot="header" type="flex" align="middle" justify="space-between">
            <span class="card-header text-truncate" :title="item.name">{{ item.name }}</span>
            <div class="no-wrap">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-edit"
                class="mr-8"
                @click="actionGoToDetail(item)" />
              <el-popconfirm
                icon="el-icon-info"
                icon-color="#F56C6C"
                title="Confirm to Delete？"
                cancel-button-text="Cancel"
                confirm-button-text="Confirm"
                confirm-button-type="danger"
                @confirm="deleteEmail(item)"
              >
                <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" />
              </el-popconfirm>
            </div>
          </el-row>
          <el-scrollbar style="height: 100%;">
            <div class="card-email">
              <h3 class="email-subject">{{ item.subject_template }}</h3>
              <div class="email-content" v-html="$sanitizeHtml(item.content_template)" />
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/tool'
import API from '@/api/email'
import Pagination from '@/components/Pagination'
import RefreshButton from '@/components/RefreshButton/index'
import RouterTools from '@/utils/tool-router'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'

export default {
  name: 'EmailTemplatesList',
  components: { RefreshButton, Pagination, ClientSearch },
  props: {
    projectId: [Number, String],
    linkedin: Boolean,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      is_show_shared: true,
      searchQuery: {
        uid: 0,
        keyword: null,
        reference_type: 'DEFAULT',
        content_type_in: [],
        reference_id: null,
        page: 1,
        size: 10,
        has_permission: true,
        extra: 'create_by',
      },
      options: {
        content_type_list: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'email_options',
    ]),
    content_type() {
      return this.linkedin ? 'LINKEDIN_MESSAGE' : 'PROJECT_OUTREACH'
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      RouterTools.resolveRouteQuery(this.$route, this.searchQuery, ['content_type_in'])
      this.searchQuery.uid = this.uid
      this.handleIsProject()
      this.getList()
      if (!this.projectId) {
        this.loadEmailContentTypes()
      }
    },
    getList() {
      this.loading = true
      this.searchQuery.reference_type = this.searchQuery.reference_id ? 'CLIENT' : 'DEFAULT'
      RouterTools.saveQueryToRouter(this.searchQuery)

      const params = JSON.parse(JSON.stringify(this.searchQuery))
      if (!this.is_show_shared) params.create_by_id = this.uid
      params.keyword = params.keyword ? params.keyword : undefined
      params.reference_id = params.reference_id ? params.reference_id : undefined
      params.types = params.content_type_in.join()
      params.is_draft = undefined
      delete params.content_type_in

      return API.getEmailList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        }).finally(() => {
          this.loading = false
        })
    },
    handleSearch() {
      debounce(() => {
        this.handleFilter()
      }, 500)
    },
    handleFilter() {
      this.searchQuery.page = 1
      this.getList()
    },
    handleIsProject() {
      if (this.projectId) {
        this.searchQuery.reference_id = this.$route.params.project_id
        this.searchQuery.reference_type = 'PROJECT'
        this.searchQuery.include_default_reference_type = true
        this.searchQuery.content_type = this.content_type
      }
    },
    actionGoToCreate() {
      let routeName
      const query = {}
      switch (this.$route.name) {
        case 'EmailTemplate':
          routeName = 'CreateEmailTemplate'
          break
        case 'ProjectOutreach':
          routeName = 'ProjectCreateOutreach'
          query.content_type = this.content_type
          break
        case 'CommonProjectOutreach':
          routeName = 'CommonProjectCreateOutreach'
          query.content_type = this.content_type
          break
      }
      this.$router.push({ name: routeName, query })
    },
    actionGoToDetail(item) {
      let routeName
      const query = {}
      switch (this.$route.name) {
        case 'EmailTemplate':
          routeName = 'EmailTemplateDetail'
          break
        case 'ProjectOutreach':
          routeName = 'ProjectOutreachDetail'
          query.content_type = this.content_type
          break
        case 'CommonProjectOutreach':
          routeName = 'CommonProjectOutreachDetail'
          query.content_type = this.content_type
          break
      }
      this.$router.push({ name: routeName, params: { id: item.id }, query })
    },
    deleteEmail(item) {
      return API.deleteEmail(item.id).then(() => {
        this.getList()
      })
    },
    loadEmailContentTypes() {
      return API.getEmailContentTypes()
        .then(data => {
          this.options.content_type_list = data
        })
    },
  },
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";

.el-radio {
  margin-right: 0;
}

.search-input {
  width: 250px;
  margin-right: $gap-md/2;
}

.shared-switch {
  margin-left: $gap-md/2;
}

.box-card {
  margin-bottom: $gap-sm;

  .card-header {
    font-size: 14px;
  }

  .card-email {
    height: 300px;
  }
}
</style>
