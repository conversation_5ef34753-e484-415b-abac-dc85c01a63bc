<template>
  <div class="app-container">
    <slot v-if="isCurrentRoute">
      <email-templates-list />
    </slot>

    <router-view />
  </div>
</template>

<script>
import EmailTemplatesList from './components/EmailTemplatesList'

export default {
  name: 'EmailTemplate',
  components: { EmailTemplatesList },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'EmailTemplate'
    },
  },
}
</script>

<style scoped>
</style>
