<template>
  <div>
    <div class="text-right">
      <el-button type="success" size="mini" icon="el-icon-plus" @click="actionCreateOrUpdate()">New</el-button>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      :row-class-name="tableRowClassName">
      <el-table-column
        prop="id"
        label="ID"
        width="40" />
      <el-table-column
        prop="name"
        label="Token Name"
        width="240" />
      <el-table-column
        prop="expire_at"
        label="Expiration"
        width="240">
        <template slot-scope="scope">
          <span v-if="scope.row.expire_at">{{ scope.row.expire_at | momentFormat('MM/DD/YYYY') }}</span>
          <span v-else>Never Expires</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="create_at"
        label="Create Date"
        width="240">
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat('MM/DD/YYYY') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="update_at"
        label="Update Date"
        width="240">
        <template slot-scope="scope">
          {{ scope.row.update_at | momentFormat('MM/DD/YYYY') }}
        </template>
      </el-table-column>
      <el-table-column
        label="">
        <template slot-scope="scope">
          <el-button type="text" class="el-icon-edit" @click="actionCreateOrUpdate(scope.row)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            :title="`Confirm do remove ${scope.row.name}?`"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="actionRemove(scope.row, scope.$index)"
          >
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              @click.stop />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="dialogVisible"
      :title="typeText+' Token'"
      width="40%"
      append-to-body
      class="standard-form"
      @closed="closeDialog()">
      <div v-if="form.id && !form.new_create_token" class="regenerate-tip">
        <i class="el-alert__icon el-icon-warning-outline" />
        Submitting this form will generate a new token. Be aware that any scripts or
        applications using this token will need to be updated.
      </div>
      <el-form v-if="form.new_create_token"
               ref="form"
               :model="form"
               class="show-form"
               label-width="150px"
               :disabled="isSubmitting">
        <el-form-item label="Token Name" prop="name">
          {{ form.name }}
        </el-form-item>
        <el-form-item label="Expiration" prop="expire_at">
          <span v-if="form.expire_date === 'custom'">{{ form.custom_date | momentFormat('MM/DD/YYYY') }}</span>
          <span v-else>{{ form.expire_date | getLabel(expire_options) }}</span>
        </el-form-item>
        <div class="token-box">
          <p>Make sure to copy your token now as you will not be able to see it again.</p>
          <el-input v-model="form.new_create_token" disabled>
            <el-button slot="append" icon="el-icon-copy-document" title="Copy" @click="copy(form.new_create_token)" />
          </el-input>
        </div>
      </el-form>
      <el-form v-else
               ref="form"
               :model="form"
               :rules="rules"
               class="mb-8"
               label-width="150px"
               :disabled="isSubmitting">
        <el-form-item label="Token Name" prop="name">
          <el-input v-model="form.name" class="w-200px" />
        </el-form-item>
        <el-form-item label="Expiration" prop="expire_at">
          <el-select v-model="form.expire_date" class="w-200px" clearable>
            <el-option
              v-for="(item, index) in expire_options"
              :key="index"
              :label="item.label"
              :value="item.value" />
          </el-select>
          <el-date-picker
            v-if="form.expire_date === 'custom'"
            v-model="form.custom_date"
            class="time-picker"
            type="date"
            placeholder="Date"
            value-format="yyyy-MM-dd"
          />
          <div v-if="form.expire_date">{{ expirationText }}</div>
        </el-form-item>
        <div class="text-center mt-normal">
          <el-button type="primary"
                     :disabled="!form.name"
                     :loading="isSubmitting"
                     @click="regenerateToken()">{{ typeText }}
          </el-button>
          <el-button type="text" @click="dialogVisible=false">Cancel</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import UserAPI from '@/api/user'
import moment from 'moment'
import { copyUtil } from '@/utils/tool'
import { mapGetters } from 'vuex'

export default {
  name: 'UserToken',
  data() {
    return {
      isSubmitting: false,
      dialogVisible: false,
      dialogTitle: 'Create Token',
      tableData: [],
      form: {
        name: '',
        expire_date: '',
        custom_date: null,
        id: 0,
      },
      rules: {
        name: [
          { required: true, message: 'Name is required', trigger: ['blur', 'change'] },
        ],
      },
      expire_options: [
        { value: 7, label: '7 Days' },
        { value: 30, label: '30 Days' },
        { value: 60, label: '60 Days' },
        { value: 90, label: '90 Days' },
        { value: 'never', label: 'Never Expires' },
        { value: 'custom', label: 'Custom...' },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'uid',
    ]),
    expirationText() {
      if (!this.form.expire_date || this.form.expire_date === 'never') return ''
      if (this.form.expire_date === 'custom') {
        return this.form.custom_date ? 'The token will expire on ' +
          moment.tz(this.form.custom_date, this.timeZone).format('MM/DD/YYYY') : ''
      } else {
        return 'The token will expire on ' +
          moment.tz(moment().add(this.form.expire_date, 'days'), this.timeZone).format('MM/DD/YYYY')
      }
    },
    typeText() {
      return this.form.id ? 'Regenerate' : 'Generate'
    },
  },
  mounted() {
    this.getTokenList()
  },
  methods: {
    getTokenList() {
      const params = { user_id: this.uid }
      return UserAPI.getUserToken(params)
        .then(data => {
          this.tableData = data.list
        })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.expire_at) {
        const is_expire = moment.tz(this.timeZone).isAfter(moment.tz(row.expire_at, this.timeZone))
        return is_expire ? 'expire-row' : ''
      }
      return ''
    },

    closeDialog() {
      this.form = {
        name: '',
        id: null,
        expire_date: 'never',
        custom_date: null,
      }
    },

    actionCreateOrUpdate(item) {
      if (item) {
        Object.assign(this.form, item)
      } else {
        this.form = {
          name: '',
          id: null,
          expire_date: 'never',
          custom_date: null,
        }
      }
      this.dialogVisible = true
    },

    regenerateToken() {
      this.isSubmitting = true
      const params = {
        name: this.form.name,
        expire_at: formatExpireDate(this.form.expire_date, this.form.custom_date),
      }
      if (this.form.id) {
        return UserAPI.updateUserToken(this.form.id, params)
          .then(data => {
            this.form.new_create_token = data.new_token
            this.tableData.forEach(item => {
              if (item.id === data.id) {
                item.name = data.name
                item.expire_at = data.expire_at
              }
            })
          })
          .finally(() => {
            this.isSubmitting = false
          })
      } else {
        return UserAPI.createUserToken(params)
          .then(data => {
            this.form.new_create_token = data.new_token
            this.tableData.push(data)
          })
          .finally(() => {
            this.isSubmitting = false
          })
      }

      function formatExpireDate(expire_date, custom_date) {
        if (!expire_date || expire_date === 'never') return null
        if (expire_date === 'custom') {
          return moment(custom_date).toISOString()
        } else {
          return moment().add(expire_date, 'days').toISOString()
        }
      }
    },

    copy(item) {
      copyUtil(item)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },

    actionRemove(item, index) {
      return UserAPI.deleteUserToken(item.id)
        .then(() => {
          this.tableData.splice(index, 1)
        })
    },
  }
  ,
}
</script>

<style scoped lang="scss">
.token-box {
  width: 80%;
  padding: 10px;
  border-radius: 5px;
  background-color: #C7EDCC;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: auto;
  margin-right: auto;

  p {
    margin: 5px;
  }
}

::v-deep .el-table .expire-row {
  background: #e6e5e3;
  color: #61676d;
}

.regenerate-tip {
  margin-bottom: 20px;
  color: #909399;
  font-size: 14px;
}

.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}
</style>
