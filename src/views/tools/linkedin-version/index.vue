<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getList" />
      <el-button :disabled="!isRD" class="filter-item" type="success" icon="el-icon-plus" @click="actionEdit()">New</el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe>
      <el-table-column label="ID" prop="id" width="50" />
      <el-table-column label="Type" prop="type">
        <template slot-scope="scope">
          {{ scope.row.type | getLabel(options_type) }}
        </template>
      </el-table-column>
      <el-table-column label="Version" prop="version">
        <template slot-scope="scope">
          {{ scope.row.version }}
          <el-tag v-if="scope.row.force_update" size="mini" type="primary" class="ml-3">Force Update</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Description" prop="description" />
      <el-table-column label="Create By">
        <template v-if="scope.row.create_by" slot-scope="scope">
          {{ scope.row.create_by.name }}
        </template>
      </el-table-column>
      <el-table-column label="Create Time">
        <template slot-scope="scope">
          {{ scope.row.create_at | momentFormat('') }}
        </template>
      </el-table-column>
      <el-table-column label="Page Url">
        <template slot-scope="scope">
          <el-link
            class="text-truncate link"
            @click="goToDetail(scope.row.app_page_url)">
            Detail
          </el-link>
        </template>
      </el-table-column>
      <el-table-column v-if="isRD" label="Action">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            class="mr-8"
            @click="actionEdit(scope.row)" />
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="dialogVisible"
      width="40%">
      <el-form ref="form" :model="form" label-width="125px">
        <el-form-item
          prop="type"
          label="Type">
          <el-select v-model="form.type">
            <el-option
              v-for="item in options_type"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="version"
          label="Version"
        >
          <el-input v-model="form.major" maxlength="1" :disabled="is_update" class="version-item">
            <template slot="append">major</template>
          </el-input>
          <el-input v-model="form.minor" maxlength="1" :disabled="is_update" class="version-item">
            <template slot="append">minor</template>
          </el-input>
          <el-input v-model="form.patch" maxlength="1" :disabled="is_update" class="version-item">
            <template slot="append">patch</template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="app_page_url"
          label="Page Url"
          :rules="[{ required: true, trigger: 'change' },]">
          <el-input
            v-model="form.app_page_url"
          />
        </el-form-item>
        <el-form-item
          label="Force Update">
          <el-checkbox v-model="form.force_update" />
        </el-form-item>
        <el-form-item
          label="Description">
          <el-input v-model="form.description"
                    type="textarea"
                    :rows="6"
                    autofocus
                    class="mt-8" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancel">Cancel</el-button>
        <el-button type="primary" :disabled="version_loading" @click="saveVersion">Save</el-button>
      </span>
    </el-dialog>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList" />
  </div>
</template>

<script>
import ArticleAPI from '@/api/article'
import Pagination from '@/components/Pagination/index'
import RefreshButton from '@/components/RefreshButton/index'
import { mapGetters } from 'vuex'

export default {
  name: 'LinkedinVersion',
  components: { RefreshButton, Pagination },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchQuery: {
        page: 1,
        size: 10,
        extra: 'create_by',
      },
      is_update: false,
      dialogVisible: false,
      version_loading: false,
      form: {
        type: 'LINKED_IN_CHROME_EXTENSION',
        force_update: false,
        app_page_url: undefined,
        major: 0,
        minor: 0,
        patch: 0,
        description: '',
      },
      options_type: [
        { value: 'LINKED_IN_CHROME_EXTENSION_FOR_CONTACT', label: 'Client User' },
        { value: 'LINKED_IN_CHROME_EXTENSION', label: 'Advisor' },
      ],
    }
  },
  computed: {
    ...mapGetters(['info']),

    isRD() {
      return this.info.roles.find(item => item.role.name === 'RD')
    },
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      return ArticleAPI.getLinkedinScraperVersion(this.searchQuery).then(data => {
        this.tableData = data.list
        this.total = data.count
      }).finally(() => {
        this.loading = false
      })
    },

    saveVersion() {
      this.version_loading = true
      const { force_update, app_page_url, description, type } = this.form
      const params = {
        force_update, app_page_url, description, type,
      }
      const save_request = this.is_update
        ? ArticleAPI.updateLinkedinScraperVersion(this.form.id, params)
        : ArticleAPI.addLinkedinScraperVersion(this.form)
      return save_request.then(data => {
        this.getList()
        this.$message({
          message: 'Save successful',
          type: 'success',
        })
      }).finally(() => {
        this.version_loading = false
        this.dialogVisible = false
      })
    },

    actionEdit(item) {
      this.dialogVisible = true
      this.is_update = !!item
      this.form = Object.assign(this.form, item)
    },

    cancel() {
      this.dialogVisible = false
      this.form = {
        type: 'LINKED_IN_CHROME_EXTENSION',
        force_update: false,
        app_page_url: undefined,
        major: 0,
        minor: 0,
        patch: 0,
        description: '',
      }
    },

    goToDetail(url) {
      window.open(url, '_blank')
    },
  },
}
</script>

<style scoped>
.version-item {
  width: 8rem;
  margin-left: 5px;
}
</style>
