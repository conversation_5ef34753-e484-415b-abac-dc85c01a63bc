<template>
  <div class="app-container">
    <div class="mb-8">
      <div class="mb-8">Upload a .CSV file if there are no special characters in the data, otherwise upload an Excel .xlsx file.</div>
      The uploaded files must be of fixed format. Make sure the columns are arranged as follows:
      <div class="mt-8 mb-8 info">
        A: linkedin url | B: name | C: position | D: Company | E: Location | F: date (job start date) | G: project
        id<br>
        H: sourced by (user full name in DB)<br>
        I: Email 1<br>
        J: Email 2 (keep blank column if no value)<br>
        K: Email 3 (keep blank column if no value)<br>
        L: Email 4 (keep blank column if no value)<br>
        M: Phone 1<br>
        N: Phone 2 (not necessary column)<br>
        O: Phone 3 (not necessary column)
      </div>
      Before clicking import, please check that the read data is correct.
    </div>
    <el-upload
      accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
      :action="upload_url"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      :show-file-list="false"
      :data="{'uid':uid}"
      :limit="1"
      :file-list="fileList"
    >
      <el-button size="small" type="primary">Upload</el-button>
      <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
    </el-upload>
    <el-link v-if="form.file_url" type="primary" :underline="false">
      <i class="el-icon-document" /> {{ form.file_name }}
    </el-link>

    <div v-if="XLS_list.length>0" class="mt-8">
      <div class="text-right mb-8">
        <el-button v-if="!advisorCreated" :loading="loading" type="primary" @click="saveAndImportXLS">Confirm & Import</el-button>
        <el-button v-if="imported" type="success" @click="DownloadImportXLS">Download Result</el-button>
      </div>
      <el-table
        v-loading="loading"
        border
        class="origin"
        stripe
        :data="current_XLS_list"
      >
        <el-table-column v-if="advisorCreated" label="ID" prop="id" width="70" />
        <el-table-column label="Linkedin Url" prop="linkedin_url" />
        <el-table-column label="Name" prop="name" />
        <el-table-column label="Position" prop="position" />
        <el-table-column label="Company" prop="company" />
        <el-table-column label="Location" prop="locations">
          <template slot-scope="{row}">
            <div v-for="(item,index) in row.locations" :key="index">{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column label="Date">
          <template slot-scope="{row}">{{ row.start_date | momentFormat }}</template>
        </el-table-column>
        <el-table-column label="Project ID" prop="target_project_id" />
        <el-table-column label="Sourced By" prop="sourced_by_name" />
        <el-table-column label="Email">
          <template slot-scope="{row}">
            <div v-for="(item,index) in row.emails" :key="index">{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column label="Phone">
          <template slot-scope="{row}">
            <div v-for="(item,index) in row.phones" :key="index">{{ item }}</div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="XLS_list.length"
        :page.sync="page"
        :limit.sync="size"
        @pagination="getCurrentList"
      />
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index'
import { mapGetters } from 'vuex'
import ProjectAPI from '@/api/project'

export default {
  name: 'AdvisorImportToProjectByXLS',
  components: { Pagination },
  data() {
    return {
      loading: false,
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      file_maxSize: 20,
      fileList: [],
      XLS_list: [],
      current_XLS_list: [],
      form: {
        file_name: undefined,
        file_url: undefined,
      },
      advisorCreated: false,
      imported: false,
      page: 1,
      size: 20,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  methods: {
    handleSuccess(response) {
      if (response.msg === 'ok') {
        this.form.file_name = response.data.file_name
        this.form.file_url = response.data.url
        this.readXLSData(response.data)
      } else {
        this.$message(response.message)
      }
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },

    readXLSData(data) {
      const params = {
        file_url: data.url,
        file_name: data.file_name,
      }
      return ProjectAPI.readAdvisorXlsData(params)
        .then(data => {
          this.XLS_list = data
          this.current_XLS_list = this.XLS_list.slice(0, this.size)
        })
    },
    saveAndImportXLS() {
      this.loading = true
      return ProjectAPI.saveAdvisorXlsData(this.XLS_list)
        .then(data => {
          this.$message({
            message: 'Save successful',
            type: 'success',
          })
          this.advisorCreated = true
          this.imported = true
          this.XLS_list = data
          this.getCurrentList()
        })
        .finally(() => {
          this.loading = false
        })
    },

    DownloadImportXLS() {
      const params = {
        batch: this.XLS_list[0].batch,
      }
      return ProjectAPI.importAdvisorXlsDownload(params)
    },

    getCurrentList() {
      const start_num = (this.page - 1) * this.size
      this.current_XLS_list = this.XLS_list.slice(start_num, start_num + this.size)
    },
  },
}
</script>

<style scoped>

</style>
