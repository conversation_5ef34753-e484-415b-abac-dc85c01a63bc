<template>
  <div class="app-container">
    <div class="mb-8">
      <advisor-search
        v-if="isAdvisorTab"
        v-model="form.target_ids"
        input-phone-email
        placeholder="Advisor Name / Phone / Email"
        class="w-300px"
      />
      <company-search
        v-if="isCompanyTab"
        v-model="form.target_ids"
        multiple
        show-link
        placeholder="Search Company"
        class="w-300px"
      />
      <location
        v-if="isLocationTab"
        v-model="form.target_ids"
        class="w-300px inline-block"
      />
      <el-button
        type="primary"
        :disabled="!form.target_ids.length"
        :loading="submitting"
        class="vertical-top"
        @click="addToBlackList"
      >
        Added To Global Black List
      </el-button>
    </div>
    <el-tabs v-model="currentTab" @tab-click="initList">
      <el-tab-pane label="Advisor" name="ADVISOR" />
      <el-tab-pane label="Company" name="CURRENT_JOB_COMPANY" />
      <el-tab-pane label="Location" name="LOCATION" />
    </el-tabs>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="list">
      <el-table-column v-if="isAdvisorTab" label="Advisor">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column v-if="isCompanyTab" label="Company">
        <template slot-scope="scope">
          <router-link-company :data="scope.row.company" />
        </template>
      </el-table-column>
      <el-table-column v-if="isLocationTab" label="Location">
        <template slot-scope="scope">
          {{scope.row.location.name}}
        </template>
      </el-table-column>
      <el-table-column label="Date" width="100">
        <template slot-scope="scope">
          {{ scope.row.update_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Action" width="80">
        <template slot-scope="scope">
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Remove from Black List？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            @confirm="removeAdvisorFromBlackList(scope.row)"
          >
            <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" class="mr-8" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getClientBlackList"
    />
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'
import Location from '@/components/Location'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RouterLinkCompany from '@/components/RouterLink/Company'
import Pagination from '@/components/Pagination'

export default {
  name: 'GlobalBlackList',
  components: { AdvisorSearch, CompanySearch, Location, RouterLinkAdvisor, RouterLinkCompany, Pagination },
  data() {
    return {
      loading: false,
      submitting: false,
      form: {
        target_ids: [],
      },
      searchQuery: {
        page: 1,
        size: 10,
      },
      total: 0,
      list: [],
      currentTab: 'ADVISOR',
    }
  },
  computed: {
    isAdvisorTab() {
      return this.currentTab === 'ADVISOR'
    },
    isCompanyTab() {
      return this.currentTab === 'CURRENT_JOB_COMPANY'
    },
    isLocationTab() {
      return this.currentTab === 'LOCATION'
    },
  },
  mounted() {
    this.initList()
  },
  methods: {
    initList() {
      this.searchQuery.page = 1
      this.total = 0
      this.list = []
      this.form.target_ids = []
      this.getClientBlackList()
    },
    getClientBlackList() {
      this.loading = true
      const params = {
        client_id: 0,
        type: this.currentTab,
        ...this.searchQuery,
        extra: 'advisor,company,location',
      }
      return ClientAPI.getClientBlackList(params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    addToBlackList() {
      this.submitting = true
      return ClientAPI.addToClientBlackList(0, this.currentTab, this.form)
        .then(() => {
          this.form.target_ids = []
          this.searchQuery.page = 1
          this.getClientBlackList()
        })
        .finally(() => {
          this.submitting = false
        })
    },
    removeAdvisorFromBlackList(item) {
      this.loading = true
      const params = { target_ids: [] }
      if (this.isAdvisorTab) params.target_ids = [item.advisor.id]
      if (this.isCompanyTab) params.target_ids = [item.company.id]
      if (this.isLocationTab) params.target_ids = [item.location.id]
      return ClientAPI.removeFromClientBlackList(0, this.currentTab, params)
        .then(() => {
          this.getClientBlackList()
        })
    },
  },
}
</script>

<style scoped>

</style>
