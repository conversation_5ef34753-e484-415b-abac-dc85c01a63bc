<template>
  <div class="app-container">
    <div class="filter-container">
      <form @submit.prevent="searchData">
        <fieldset :disabled="loading">
          <el-input
            v-model="searchQuery['third_party_company_maps.third_party_company.ticker_in']"
            class="filter-item w-200px"
            placeholder="Company Ticker"
            clearable />
          <el-button
            type="primary"
            class="filter-item"
            native-type="submit"
            icon="el-icon-search"
            :loading="loading"
          >
            Search
          </el-button>
        </fieldset>
      </form>
    </div>

    <el-table
      v-loading="loading"
      :data="list"
      border
      stripe
    >
      <el-table-column label="Company Ticker" width="200">
        <template v-slot="scope">
          <slot v-if="scope.row.third_party_company_maps.length">
            <span v-for="(m, index) in scope.row.third_party_company_maps" :key="m.id"><slot
              v-if="index">,</slot>{{ m.third_party_company.ticker }}</span>
          </slot>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="Name" />
      <el-table-column prop="aliases" label="Aliases">
        <template v-slot="scope">
          <el-tag
            v-for="item in scope.row.aliases"
            :key="item.id"
            :title="item.alias"
            class="mr-8"
          >
            {{ item.alias }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="company" label="Company">
        <template v-slot="scope">
          <slot v-for="item in scope.row.company_maps">
            <router-link
              v-if="item.company"
              :key="item.id"
              class="link"
              :to="{ name:'CompanyDetail', params: { company_id: item.company_id }}">
              {{ item.company.name }}
            </router-link>
          </slot>
        </template>
      </el-table-column>
      <el-table-column prop="third_party_company" label="Third Party Company">
        <template v-slot="scope">
          <span
            v-for="item in scope.row.third_party_company_maps"
            :key="item.id"
          >
            {{ item.third_party_company.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="Screened" align="center">
        <el-table-column label="Evaluate Start At" width="85">
          <template v-slot="scope">
            {{scope.row.project_sq_evaluate_start_at | momentFormat('YYYY-MM-DD HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column label="Evaluate End At" width="85">
          <template v-slot="scope">
            {{scope.row.project_sq_evaluate_end_at | momentFormat('YYYY-MM-DD HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column label="Status" width="90">
          <template v-slot="scope">
            <el-tag
              v-if="!scope.row.project_sq_evaluating && !scope.row.project_sq_stale"
              type="success"
            >
              Latest
            </el-tag>
            <el-tag
              v-if="!scope.row.project_sq_evaluating && scope.row.project_sq_stale"
              type="warning"
            >
              Stale
            </el-tag>
            <el-tag
              v-if="scope.row.project_sq_evaluating"
              type="primary"
            >
              Evaluating
            </el-tag>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="Former" align="center">
        <el-table-column label="Evaluate Start At" width="85">
          <template v-slot="scope">
            {{scope.row.job_exp_evaluate_start_at | momentFormat('YYYY-MM-DD HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column label="Evaluate End At" width="85">
          <template v-slot="scope">
            {{scope.row.job_exp_evaluate_end_at | momentFormat('YYYY-MM-DD HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column label="Status" width="90">
          <template v-slot="scope">
            <el-tag
              v-if="!scope.row.job_exp_evaluating && !scope.row.job_exp_stale"
              type="success"
            >
              Latest
            </el-tag>
            <el-tag
              v-if="!scope.row.job_exp_evaluating && scope.row.job_exp_stale"
              type="warning"
            >
              Stale
            </el-tag>
            <el-tag
              v-if="scope.row.job_exp_evaluating"
              type="primary"
            >
              Evaluating
            </el-tag>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :auto-scroll="false"
      @pagination="searchData"
    />
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import Pagination from '@/components/Pagination'

export default {
  name: 'AdvisorTag',
  components: { Pagination },
  data() {
    return {
      loading: false,
      searchQuery: {
        page: 1,
        size: 20,
        'third_party_company_maps.third_party_company.ticker_in': '',
        extra: 'aliases,company_maps.company,third_party_company_maps.third_party_company',
      },
      list: [],
      total: 0,
    }
  },
  mounted() {
    this.searchData()
  },
  methods: {
    searchData() {
      this.loading = true

      const params = {
        ...this.searchQuery,
        'third_party_company_maps.third_party_company.ticker_in': this.searchQuery['third_party_company_maps.third_party_company.ticker_in'] || undefined,
      }
      return ConsultantAPI
        .getAdvisorTagsByTicker(params)
        .then(data => {
          this.list = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
