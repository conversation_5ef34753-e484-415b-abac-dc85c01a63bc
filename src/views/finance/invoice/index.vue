<template>
  <div class="app-container">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getInvoiceList" />
      <client-search
        v-model="searchQuery.client_ids"
        multiple
        class="w-200px filter-item"
        @change="handleSearch" />
      <el-select
        v-model="searchQuery.contract_id"
        filterable
        clearable
        remote
        placeholder="Contract Name"
        class="remote-select filter-item"
        :loading="searchContractLoading"
        :remote-method="searchContract"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option
          v-for="item in contract_options"
          :key="item.id"
          :label="item.name"
          :value="item.id" />
      </el-select>
      <el-date-picker
        v-model="date_range"
        class="filter-item"
        type="daterange"
        range-separator="-"
        :default-time="['00:00:00', '23:59:59']"
        start-placeholder="Start date"
        end-placeholder="End date"
        @change="handleSearch"
      />
      <el-input v-model="search_ids"
                class="filter-item w-200px"
                placeholder="IDs (separated by a comma)"
                @input="searchByIds" />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="actionDownload"
      >
        Download
      </el-button>
    </div>
    <invoice-list v-loading="loading" :table-data="tableData" @delete="handleSearch" />

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getInvoiceList"
    />
  </div>
</template>

<script>
import API from '@/api/contract'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import InvoiceList from './components/InvoiceList'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'
import { debounce } from '@/utils/tool'

export default {
  name: 'Invoice',
  components: { RefreshButton, Pagination, InvoiceList, ClientSearch },
  data() {
    return {
      loading: false,
      searchContractLoading: false,
      total: 0,
      date_range: [],
      search_ids: undefined,
      searchQuery: {
        ids: undefined,
        client_ids: undefined,
        contract_id: undefined,
        page: 1,
        size: 10,
        extra: 'contract,client',
      },
      tableData: [],
      contract_options: [],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },

  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)
    this.getInvoiceList()
    this.searchContract()
  },

  methods: {
    getInvoiceList() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      this.loading = true
      const params = Object.assign({}, this.formatParams(this.searchQuery))
      params.client_ids = params.client_ids.join() || undefined

      return API.getInvoiceList(params).then(data => {
        this.tableData = data.list
        this.total = data.count
      }).finally(() => {
        this.loading = false
      })
    },
    searchByIds(val) {
      this.search_ids = val.replace(/[^\d,]/g, '')
      const no_change = this.search_ids === this.searchQuery.ids
      if (!this.search_ids || no_change) return

      debounce(() => {
        this.searchQuery.ids = this.search_ids
        this.handleSearch()
      }, 500)
    },
    handleSearch() {
      this.searchQuery.page = 1
      this.getInvoiceList()
    },

    formatParams(params) {
      const data = Object.assign({}, params)
      if (this.date_range && this.date_range.length) {
        data.invoice_time_gt = this.date_range[0]
        data.invoice_time_lt = this.date_range[1]
      }

      Object.keys(data)
        .forEach(function(key) {
          if (!data[key]) {
            delete data[key]
          }
        })
      return data
    },

    searchContract(val) {
      const params = {
        page: 1,
        size: 20,
        'effective_status': 'EFFECTIVE',
        name_contains: val || undefined,
      }
      this.searchContractLoading = true
      return API.getContractList(params)
        .then(data => {
          this.contract_options = data.list.map(item => {
            return { name: item.name, id: item.id }
          })
        }).finally(() => {
          this.searchContractLoading = false
        })
    },

    actionDownload() {
      const params = this.formatParams(this.searchQuery)
      params['client_ids'] = params.client_ids.join(',')
      return API.downloadInvoiceList(params)
    },
  },
}
</script>

<style scoped>

</style>
