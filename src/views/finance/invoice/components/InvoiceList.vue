<template>
  <el-table
    :border="border"
    stripe
    :data="tableData"
  >
    <el-table-column label="ID" width="80">
      <template slot-scope="scope">{{ scope.row.id }}</template>
    </el-table-column>
    <el-table-column label="Client">
      <template slot-scope="scope">
        <router-link-client :data="scope.row.client" />
      </template>
    </el-table-column>
    <el-table-column label="Contract">
      <template slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.contract.name"
          :to="{ name: 'ContractDetail', params: { id: scope.row.contract_id, is_update: false } }">
          {{ scope.row.contract.name }}
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Invoice Date">
      <template slot-scope="scope">{{ scope.row.create_at | momentFormat() }}</template>
    </el-table-column>
    <el-table-column label="Amount">
      <template slot-scope="scope">{{ scope.row.amount }}</template>
    </el-table-column>
    <el-table-column label="Currency">
      <template slot-scope="scope">{{ scope.row.currency }}</template>
    </el-table-column>
    <el-table-column label="File">
      <template slot-scope="scope">
        <el-link v-if="scope.row.contract.payway === 'PAYGO'"
                 type="primary"
                 :underline="false"
                 icon="el-icon-download"
                 class="mr-8"
                 @click="downloadInvoiceUsage(scope.row.id)">
          Usage
        </el-link>
        <el-link v-if="scope.row.file_path"
                 type="primary"
                 :underline="false"
                 icon="el-icon-download"
                 @click="downloadInvoiceDoc(scope.row.file_path)">Invoice
        </el-link>
      </template>
    </el-table-column>
    <el-table-column label="Action" width="80">
      <template slot-scope="scope">
        <el-popconfirm
          icon="el-icon-info"
          icon-color="red"
          :title="`Are you sure to delete this invoice (id: ${scope.row.id})?`"
          cancel-button-text="Cancel"
          confirm-button-text="Confirm"
          confirm-button-type="danger"
          @confirm="deleteInvoice(scope.row.id, scope.$index)"
        >
          <el-link slot="reference" type="danger" :underline="false" icon="el-icon-delete" />
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import API from '@/api/contract'
import RouterLinkClient from '@/components/RouterLink/Client'
import { downloadDocument } from '@/utils/tool'
import { mapState } from 'vuex'

export default {
  name: 'InvoiceList',
  components: { RouterLinkClient },
  props: {
    tableData: Array,
    border: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },

  methods: {
    downloadInvoiceUsage(id) {
      const params = { zone_id: this.timeZone }
      return API.downloadInvoiceUsage(id, params).then(data => {
        downloadDocument(data)
      })
    },
    downloadInvoiceDoc(path) {
      window.open(path, '_blank')
    },
    deleteInvoice(id, index) {
      return API.deleteInvoice(id).then(() => {
        this.$emit('delete', index)
      })
    },
  },
}
</script>

<style scoped>

</style>
