<template>
  <div>
    <el-button type="primary" @click="dialogVisible = true">Custom...</el-button>

    <el-dialog
      :title="`Custom - ${title}`"
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      class="standard-form"
      @open="openDialog">
      <el-form>
        <div v-for="(item,index) in form.steps" :key="index">
          <el-form-item label="Less than" class="inline-block" label-width="150px">
            <el-input v-model="item.offset" :min="0" type="number">
              <template slot="append">{{ unitText.unit }}</template>
            </el-input>
          </el-form-item>
          <el-form-item class="inline-block">
            <el-input v-model="item.value" :min="0" type="number">
              <template slot="append">{{ unitText.additional }}</template>
            </el-input>
          </el-form-item>
          <el-link type="primary" :underline="false" icon="el-icon-plus" @click="addRule(index)" />
          <el-link
            v-if="form.steps.length > 1"
            type="danger"
            :underline="false"
            icon="el-icon-minus"
            @click="removeRule(index)" />
        </div>
        <div>
          <el-form-item :label="every_label" class="inline-block" label-width="150px">
            <el-input v-model="form.extra_offset" :min="0" type="number">
              <template slot="append">{{ unitText.unit }}</template>
            </el-input>
          </el-form-item>
          <el-form-item label="add" label-width="40px" class="inline-block">
            <el-input v-model="form.extra_value" :min="0" type="number">
              <template slot="append">{{ unitText.additional }}</template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="disabledSave" @click="save">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StepsRuleCustom',
  props: {
    type: {
      type: String,
      default: 'unit_rule',
    },
    currency: String,
    value: String,
    permissionUpdate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      form: {},
      defaultStepData: {},
      unitText: {
        unit: 'min',
        additional: 'CapH',
      },
    }
  },
  computed: {
    every_label() {
      let value, length
      if (this.form.steps) {
        length = this.form.steps.length
        value = this.form.steps[length - 1].offset || this.form.steps[length - 2].offset
      } else {
        value = this.defaultStepData.extra_offset
      }
      return `More than ${value} every`
    },

    disabledSave() {
      if (!this.form.steps?.length) return false
      const steps_null = this.form.steps.filter(item => !item.value && item.value !== 0).length > 0
      return steps_null || !this.form.extra_offset || !this.form.extra_value
    },
  },
  methods: {
    openDialog() {
      this.initData()
      this.setInterfaceText()
      this.formatUnitRule4View(this.value)
    },
    initData() {
      let step_data
      switch (this.type) {
        case 'non_china_senior':
          step_data = {
            steps: [
              {
                value: 1,
                offset: 499,
              }, {
                value: 1.5,
                offset: 999,
              }, {
                value: 2,
                offset: 1499,
              }],
            extra_offset: 500,
            extra_value: 0.5,
          }
          break
        case 'china_senior':
          step_data = {
            steps: [
              {
                value: 1,
                offset: 1800,
              }, {
                value: 1.5,
                offset: 2500,
              }],
            extra_offset: 700,
            extra_value: 0.5,
          }
          break
        default:
          step_data = {
            steps: [
              {
                value: 1,
                offset: 60,
              }, {
                value: 1.5,
                offset: 90,
              }],
            extra_offset: 30,
            extra_value: 0.5,
          }
          break
      }
      this.defaultStepData = step_data
    },
    setInterfaceText() {
      switch (this.type) {
        case 'unit_rule':
          this.unitText.unit = 'min'
          this.unitText.additional = 'CapH'
          this.title = 'Client Hours'
          break
        case 'non_china_senior':
          this.unitText.unit = this.currency === 'USD' ? 'USD ($)' : (this.currency === 'SGD' ? 'SGD (S$)' : 'MYR (RM)')
          this.unitText.additional = 'CapH per H'
          this.title = 'Non China Senior Expert Fees'
          break
        case 'china_senior':
          this.unitText.unit = 'RMB'
          this.unitText.additional = 'CapH per H'
          this.title = 'China Senior Expert Fees'
          break
        case 'in_person':
          this.unitText.additional = 'CapH'
          break
        default:
          this.unitText.additional = 'CapH per hour'
          break
      }
    },
    formatUnitRule4View(input) {
      let output = {}
      if (['', undefined].indexOf(input) > -1) {
        output = this.defaultStepData
      } else {
        const text_steps = input.split(';')
        const text_extra = text_steps.pop()

        output.steps = text_steps.map((text_step) => {
          if (!text_step.includes('depends')) {
            const offset = text_step.match(/\(\S+]/)[0].split(',')[1].slice(0, -1)
            return {
              value: parseFloat(text_step.replace(text_step.match(/\(\S+]/)[0], '')),
              offset: parseInt(offset),
            }
          }
        })
        output.extra_value = parseFloat(text_extra.replace(text_extra.match(/<\S+>/)[0], ''))
        output.extra_offset = parseInt(text_extra.match(/<\S+>/)[0].split(',')[1].slice(0, -1))
      }
      this.form = output
    },
    addRule(index) {
      const currentOffset = (this.form.steps[index] || this.defaultStepData.steps[0]).offset
      let step_amount = 30

      switch (this.type) {
        case 'unit_rule':
          step_amount = 30
          break
        case 'non_china_senior':
          step_amount = this.form.extra_offset
          break
        case 'china_senior':
          step_amount = this.form.extra_offset
          break
      }

      this.form.steps.splice(index + 1, 0, Object.assign({}, {
        value: this.form.steps[index].value + 0.5,
        offset: Number.isInteger(currentOffset) ? currentOffset + step_amount : this.defaultStepData.steps[0].offset,
      }))
    },
    removeRule(index) {
      this.form.steps.splice(index, 1)
    },
    save() {
      const output = []
      this.form.steps.forEach((item, index) => {
        const value = item.value
        const offset_last = index === 0 ? 0 : this.form.steps[index - 1].offset
        const offset = item.offset
        output.push(value + '(' + offset_last + ',' + offset + ']')
      })
      output.push(
        this.form.extra_value + '<' + this.form.steps.slice(-1)[0].offset + ',' + this.form.extra_offset + '>')

      this.$emit('update:value', output.join(';'))
      if (this.permissionUpdate) this.$emit('update')
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.standard-form {
  ::v-deep .el-form-item__content .el-input-group {
    width: 170px;
  }

  ::v-deep .el-dialog__body {
    /*text-align: center;*/
  }
}
</style>
