<template>
  <div class="inline-block ml-8">
    <el-button type="primary" @click="openDialog">Edit</el-button>

    <el-dialog
      title="Update"
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item v-if="typeKey==='billing_contact_ids'" label="Billing Contact" prop="billing_contact_ids">
          <el-select v-model="form.billing_contact_ids" multiple>
            <el-option v-for="item in options.billing_contacts" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <create-client-user
            v-if="form.client_id"
            allow-change-status
            contact-type="BILLING"
            class="inline-block ml-5px"
            :client-id="form.client_id"
            @update="val => pushNewContact(val)" />
        </el-form-item>
        <el-form-item v-if="typeKey==='invoicing_instructions'" label="Invoicing" prop="invoicing_instructions">
          <tinymce v-if="dialogVisible" ref="invoicing" v-model="form.invoicing_instructions" :height="100" :toolbar="toolbar" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="save">Update</el-button>
        <el-button @click="closeDialog">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CreateClientUser from '@/views/project/components/CreateClientUser'
import Tinymce from '@/components/Tinymce'
import _ from 'lodash'
import ContractAPI from '@/api/contract'
import ClientAPI from '@/api/client'
import { mapState } from 'vuex'
export default {
  name: 'EditContractItem',
  components: { CreateClientUser, Tinymce },
  props: {
    typeKey: String,
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      loading: false,
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      dialogVisible: false,
      form: { invoicing_instructions: '' },
      options: { billing_contacts: [] },
      rules: {
        billing_contact_ids: [{ required: true, trigger: 'change', message: 'Billing Contact is required' }],
        invoicing_instructions: [{ required: true, trigger: 'change', message: 'Invoicing Instructions is required' }],
      },
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },

  methods: {
    getBillingContact() {
      if (this.form.client_id) {
        return ClientAPI.getContactList(this.form.client_id, { type: 'BILLING' }).then(data => {
          this.options.billing_contacts = data.list
        })
      }
    },

    async openDialog() {
      this.rules.billing_contact_ids = [{ required: !this.isSGDB, trigger: 'change', message: 'Billing Contact is required' }]

      this.form = _.cloneDeep(this.data)
      if (this.typeKey === 'billing_contact_ids') {
        await this.getBillingContact()
      }

      this.dialogVisible = true
      if (this.typeKey === 'invoicing_instructions') {
        this.$nextTick(() => {
          if (this.$refs.invoicing) this.$refs.invoicing.setContent(this.data.invoicing_instructions)
        })
      }
    },

    pushNewContact(val) {
      this.options.billing_contacts.push(val)
      this.form.billing_contact_ids.push(val.id)
    },

    closeDialog() {
      this.form.invoicing_instructions = null
      this.dialogVisible = false
    },

    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          const params = Object.assign({}, this.data)
          params[this.typeKey] = this.form[this.typeKey]
          return ContractAPI.updateContract(params)
            .then(data => {
              this.$message.success('Success')
              data.billing_contacts.forEach(item => {
                item.emailLoading = false
                item.email_obj = item.contact_infos?.find(item => item.type === 'EMAIL') || {}
              })
              this.$emit('update:data', Object.assign(params, data))
              this.closeDialog()
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>

</style>
