<template>
  <div class="inline-block">
    <el-link
      type="warning"
      :underline="false"
      :disabled="disable"
      icon="el-icon-time"
      @click="changeTimeDialogVisible=true" />

    <el-dialog
      title="Modify the contract effective time"
      :visible.sync="changeTimeDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="50%"
      class="change-time-dialog"
    >
      <el-form ref="form" v-loading="check_time_loading" label-position="left">
        <el-form-item>
          <el-date-picker
            v-model="current_time.start_time"
            type="date"
            align="right"
            :clearable="false"
            format="MM/dd/yyyy  HH:mm"
            :disabled="startTimeDisabled"
            :picker-options="pickerOptionsStart"
            placeholder="Start Date"
          />
          <span> -- </span>
          <el-date-picker
            v-model="current_time.end_time"
            type="date"
            align="right"
            format="MM/dd/yyyy HH:mm"
            :disabled="endTimeDisabled"
            :picker-options="pickerOptionsEnd"
            :clearable="false"
            placeholder="End Date"
          />
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateTime()">Confirm</el-button>
        <el-button type="text" @click="changeTimeDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import API from '@/api/contract'
import { mapState } from 'vuex'

export default {
  name: 'ChangeTime',
  props: {
    originTime: {
      type: Object,
      default: () => {
        return {}
      },
    },
    disable: {
      type: Boolean,
      default: false,
    },
    // 有意义的合同 ：即合同approval status为 APPLY || APPROVED
    isMeaningContract: {
      type: Boolean,
      default: false,
    },
    clientId: Number,
    contractId: Number,
  },

  data() {
    return {
      changeTimeDialogVisible: false,
      check_time_loading: false,
      right_max_time: '',
      left_min_time: '',
      current_moment: moment.tz(moment(), this.timeZone),
      current_time: {},
      endTimeDisabled: false,
      startTimeDisabled: false,
      pickerOptionsEnd: {},
      pickerOptionsStart: {},
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),

    endTimeMinLimit() {
      return this.isBefore(moment(this.originTime.end_time)
        .tz(this.timeZone), this.current_moment) ? moment(this.originTime.end_time)
          .tz(this.timeZone) : this.current_moment
    },
  },

  watch: {
    'changeTimeDialogVisible': {
      handler(newVal) {
        if (newVal) {
          this.checkTime()
          this.current_time = Object.assign({}, this.formatTime(this.originTime))
          this.right_max_time = ''
          this.left_min_time = ''
        }
      },
    },
  },

  methods: {
    formatTime(data) {
      return {
        start_time: moment(data.start_time).tz(this.timeZone),
        end_time: moment(data.end_time).tz(this.timeZone),
      }
    },

    checkTime() {
      this.check_time_loading = true
      const params = Object.assign({
        'extra': 'client_name,generator,approver',
      }, {
        client_id: this.clientId,
        approval_status_in: 'PENDING_APPROVAL,APPROVED',
      })

      return API.getContractList(params)
        .then(data => {
          if (data.count) {
            const other_contracts = data.list.filter(item => item.id !== this.contractId && item.payway !== 'PROJECT_BASE')
            if (other_contracts.length) this.checkTimeRange(other_contracts)
          }
        }).then(() => {
          this.checkShortcuts()
          this.checkTimeDisable()
        }).finally(() => {
          this.check_time_loading = false
        })
    },

    // 修改合同时间逻辑： (仅有意义合同设限)
    // 1. 正在申请中的合同不允许修改 （Request OR Apply）
    // 2.有意义合同的时间不允许发生重叠
    // 3.Approved 合同不能缩小已发生的时间范围

    checkTimeRange(list) {
      let end_time_limit = ''
      let start_time_limit = ''
      let has_next_contract = false
      let has_last_contract = false

      list.forEach(item => {
        // 找到下一份有意义合同的开始时间
        if (this.isAfter(moment(item.start_time).tz(this.timeZone).add(1, 'm'), this.current_time.end_time)) {
          if (end_time_limit) {
            this.isBefore(item.start_time, end_time_limit) ? end_time_limit = item.start_time : ''
          } else {
            end_time_limit = item.start_time
          }
          has_next_contract = true
        }

        // 找到当前合同 的上一份有意义合同的结束时间
        if (this.isBefore(moment(item.end_time).tz(this.timeZone), moment(this.current_time.start_time).add(1, 'm'))) {
          if (start_time_limit) {
            this.isAfter(item.end_time, start_time_limit) ? start_time_limit = item.end_time : ''
          } else {
            start_time_limit = item.end_time
          }
          has_last_contract = true
        }
      })

      has_last_contract ? this.left_min_time = moment.tz(start_time_limit, this.timeZone).format() : ''
      has_next_contract ? this.right_max_time = moment.tz(end_time_limit, this.timeZone).format() : ''
    },

    updateTime() {
      // apply 时 contract start_time保持到时分 / end_time 设置为当天0点
      const start_time = moment.tz(this.current_time.start_time, this.timeZone).toISOString()
      const end_time = moment.tz(this.current_time.end_time, this.timeZone).toISOString()

      const params = {
        id: this.contractId,
        start_time,
        end_time,
      }

      return API.updateContractItem(this.contractId, params)
        .then(data => {
          this.changeTimeDialogVisible = false
          this.$emit('update')
          this.$message.success('success')
        })
    },

    isBefore(a, b) {
      return moment(a).isBefore(moment(b))
    },

    isAfter(a, b) {
      return moment(a).isAfter(moment(b))
    },

    checkTimeDisable() {
      if (!this.isMeaningContract) return
      if (this.left_min_time) {
        this.startTimeDisabled = this.isAfter(moment.tz(this.left_min_time, this.timeZone)
          .add(1, 'm'), moment(this.originTime.start_time)
          .tz(this.timeZone)) && this.isBefore(moment(this.originTime.start_time)
          .tz(this.timeZone), this.current_moment)
      }
      if (this.right_max_time) {
        this.endTimeDisabled = this.isBefore(moment.tz(this.right_max_time, this.timeZone), moment(this.originTime.end_time)
          .tz(this.timeZone).add(1, 'm'))
      }
    },

    checkShortcuts() {
      const current_moment_format = moment.tz(moment(), this.timeZone).format()
      const originTime_start = moment(this.originTime.start_time).tz(this.timeZone)
      // 无意义合同 除结束时间大于开始时间外不设限
      if (!this.isMeaningContract) {
        this.pickerOptionsStart = {
          disabledDate: (time) => {
            return moment.tz(time, this.timeZone) > moment.tz(this.current_time.end_time, this.timeZone)
          },
          shortcuts: [{
            text: 'Current',
            onClick(picker) {
              picker.$emit('pick', current_moment_format)
            },
          }],
        }

        this.pickerOptionsEnd = {
          disabledDate: (time) => {
            return moment.tz(time, this.timeZone) < this.current_moment || moment.tz(time, this.timeZone) < moment.tz(this.current_time.start_time, this.timeZone)
          },
          shortcuts: [{
            text: 'Current',
            onClick(picker) {
              picker.$emit('pick', current_moment_format)
            },
          }],
        }
        return
      }

      // 合同结束时间不得超出下一份有意义合同生效时间
      this.pickerOptionsEnd = {
        disabledDate: (time) => {
          const select_time = moment.tz(time, this.timeZone)
          return select_time > moment.tz(this.right_max_time, this.timeZone) ||
            select_time < originTime_start ||
            select_time < this.endTimeMinLimit
        },
        shortcuts: null,
      }

      // 合同开始时间不得超过上一份有意义结束时间 && 已生效合同开始时间不得缩短
      if (originTime_start < this.current_moment) {
        this.pickerOptionsStart = {
          disabledDate: (time) => {
            const select_time = moment.tz(time, this.timeZone)
            return select_time < moment.tz(this.left_min_time, this.timeZone) || select_time > originTime_start
          },
          shortcuts: null,
        }
      } else {
        this.pickerOptionsStart = {
          disabledDate: (time) => {
            const select_time = moment.tz(time, this.timeZone)
            return select_time < moment.tz(this.left_min_time, this.timeZone) || select_time > moment(this.originTime.end_time)
              .tz(this.timeZone) || select_time > moment.tz(this.right_max_time, this.timeZone)
          },
          shortcuts: null,
        }
      }

      // 如果 合同开始时间 > now && 上一份合同的结束时间 < now => 可以设置当前合同的开始时间为current
      const start_time_in_gap = moment(this.current_time.start_time)
        .tz(this.timeZone) > this.current_moment && this.current_moment > moment(this.left_min_time)
        .tz(this.timeZone)
      // 没有上一份合同 && 开始时间 > now => 可以设置当前合同的开始时间为current
      const start_time_over = !this.left_min_time && moment(this.current_time.start_time)
        .tz(this.timeZone) > this.current_moment
      // 如果 合同开始时间 < now => 不允许设置当前合同的开始时间为current
      if (start_time_in_gap || start_time_over) {
        this.pickerOptionsStart.shortcuts = [{
          text: 'Current',
          onClick(picker) {
            picker.$emit('pick', current_moment_format)
          },
        }]
      }

      // (下一份合同的开始时间 > now  || 没有下一份合同 ) && 开始时间 < now => 可以设置当前合同的结束时间为 current
      const last_after_now = this.current_moment < moment(this.right_max_time)
        .tz(this.timeZone) || !this.right_max_time
      // 开始时间 < now
      if (last_after_now && moment(this.current_time.start_time)
        .tz(this.timeZone) < this.current_moment) {
        this.pickerOptionsEnd.shortcuts = [{
          text: 'Current',
          onClick(picker) {
            picker.$emit('pick', current_moment_format)
          },
        }]
      }
    },

  },
}
</script>

<style scoped lang="scss">
  .change-time-dialog {
    ::v-deep .el-picker-panel__sidebar.el-picker-panel__shortcut {
      width: 80px;
    }
  }
</style>
