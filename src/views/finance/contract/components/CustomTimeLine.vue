<template>
  <div class="container">
    <div class="last_time_line">
      <el-slider
        v-model="last_value"
        range
        disabled
        :show-tooltip="false"
        :marks="last_marks" />
    </div>
    <div class="current_time_line">
      <el-slider
        v-model="current_value"
        range
        disabled
        :show-tooltip="false"
        :marks="current_marks" />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'

export default {
  name: 'CustomTimeLine',
  props: {
    last: {
      type: Object,
      default: () => {
        return {}
      },
    },
    current: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },

  data() {
    return {
      last_value: [0, 100],
      last_marks: {},
      current_value: [0, 100],
      current_marks: {},
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },

  watch: {
    'current': {
      handler(val) {
        this.getTime()
      },
      immediate: true,
      deep: true,
    },
  },

  created() {
    this.getTime()
  },

  methods: {
    formatDate(val) {
      return moment.tz(val, this.timeZone).format('MM/DD/YYYY')
    },
    getTime() {
      this.last_marks = {
        0: {
          style: {
            width: '80px',
          },
          label: `last start\n${this.formatDate(this.last.start_time)}`,
        },
        100: {
          style: {
            width: '80px',
            transform: 'translateX(-50%)',
          },
          label: `last end\n${this.formatDate(this.last.end_time)}`,
        },
      }

      this.current_marks = {
        100: {
          style: {
            color: '#1989FA',
            width: '80px',
          },
          label: `current end\n${this.formatDate(this.current.end_time)}`,
        },
      }

      if (moment.tz(this.last.end_time, this.timeZone).add(1, 'minute')
        .isBefore(moment.tz(this.current.start_time, this.timeZone))) {
        this.current_value = [20, 100]
        this.current_marks[20] = {
          style: {
            color: '#1989FA',
            width: '80px',
          },
          label: `current start\n${this.formatDate(this.current.start_time)}`,
        }
      } else if (moment.tz(this.last.end_time, this.timeZone)
        .isAfter(moment.tz(this.current.start_time, this.timeZone))) {
        this.current_value = [20, 100]
        this.current_marks[20] = {
          style: {
            width: '80px',
          },
          label: `last end\n${this.formatDate(this.last.end_time)}`,
        }
        this.last_marks[100] = {
          style: {
            color: '#1989FA',
            width: '80px',
            transform: 'translateX(-50%)',
          },
          label: `current start\n${this.formatDate(this.current.start_time)}`,
        }
      } else {
        this.current_value = [0, 100]
        this.current_marks[0] = {
          style: {
            color: '#1989FA',
            width: '80px',
          },
          label: `current start\n${this.formatDate(this.current.start_time)}`,
        }
      }
    },
  },

}
</script>

<style scoped lang="scss">
  .container {
    width: 90%;
    margin: 40px auto;
    display: flex;
    justify-content: space-between;

    .last_time_line {
      flex: 1;
      position: relative;

      .contract-tip {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
      }

    }

    .current_time_line {
      flex: 1.2;
      position: relative;

      .contract-tip {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      ::v-deep .el-slider__runway.disabled .el-slider__bar {
        background-color: #66b1ff;
      }

      ::v-deep .el-slider__runway {
        background-color: red;
      }

      ::v-deep .el-slider__marks-text {
        top: -55px;
      }
    }
  }

</style>
