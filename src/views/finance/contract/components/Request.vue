<template>
  <el-dialog
    title="Request Notes"
    class="custom-dialog"
    :visible="requestVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="50%"
  >
    <el-form ref="dialogForm" :model="dialogForm" label-width="150px">
      <el-input v-model="dialogForm.request_note" type="textarea" :rows="3" />
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="requestContract('dialogForm')">Confirm</el-button>
      <el-button type="text" @click="$emit('update:requestVisible',false)">Cancel</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from '@/api/contract'

export default {
  name: 'Request',
  props: {
    contractId: Number,
    requestVisible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dialogForm: {
        request_note: '',
      },
    }
  },

  methods: {
    requestContract() {
      return API.requestContract(this.contractId, this.dialogForm)
        .then(data => {
          this.$emit('update:requestVisible', false)
          this.$emit('update')
        })
    },
  },

}
</script>

<style scoped>

</style>
