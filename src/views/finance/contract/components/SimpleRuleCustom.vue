<template>
  <div>
    <el-button type="primary" @click="dialogVisible = true">Custom...</el-button>

    <el-dialog
      :title="`Custom - ${title}`"
      :visible.sync="dialogVisible"
      width="40%"
      append-to-body
      @open="openDialog">
      <el-radio-group v-model="form.type">
        <el-row style="margin-bottom: 8px;">
          <el-radio :label="0">No extra fee</el-radio>
        </el-row>
        <el-row type="flex" align="middle" style="height:28px">
          <el-radio :label="1">Extra charge</el-radio>
          <el-input v-if="form.type === 1" v-model="form.value" class="w-200px">
            <template slot="append">{{unitText}}</template>
          </el-input>
        </el-row>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SimpleRuleCustom',
  props: {
    type: {
      type: String,
      default: 'in_person',
    },
    value: String,
  },

  data() {
    return {
      dialogVisible: false,
      title: '',
      unitText: 'CapH per hour',
      form: {
        type: 0,
        value: 0,
      },
    }
  },

  methods: {
    openDialog() {
      this.initUnitText()
      this.formatData4View(this.value)
    },
    initUnitText() {
      switch (this.type) {
        case 'in_person':
          this.unitText = 'CapH'
          this.title = 'In-Person Fee'
          break
        case 'transcription':
          this.unitText = 'CapH per hour'
          this.title = 'Transcription Fee'
          break
        case 'translation':
          this.unitText = 'CapH per hour'
          this.title = 'Translation Fee'
          break
        case 'overseas':
          this.unitText = 'CapH per hour'
          this.title = 'Overseas Fee'
          break
      }
    },
    formatData4View(input) {
      // **DO NOT** change the orders below!
      if (!input || input === 'none') {
        this.form.type = 0
      } else if (input.slice(-3)
        .toLowerCase() === 'h/h') {
        this.form.type = 1
        this.form.value = +input.slice(1, input.length - 3)
      } else if (input.slice(-1)
        .toLowerCase() === 'h') {
        this.form.type = 1
        this.form.value = +input.slice(1, input.length - 1)
      }
    },
    save() {
      let output = ''

      if ((this.form.type === 0 || +this.form.value === 0) && this.form.type !== 1) {
        output = ''
      } else if (this.form.type === 1) {
        if (this.type !== 'in_person') {
          output = '+' + (this.form.value * 10000) / 10000 + 'h/H'
        } else {
          output = '+' + this.form.value + 'h'
        }
      }

      if (this.type === 'overseas' && this.form.type) {
        this.$emit('update', this.form.value)
      }

      this.$emit('update:value', output)
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped>

</style>
