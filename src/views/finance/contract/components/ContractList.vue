<template>
  <div>
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div>
        <refresh-button class="filter-item" @action="getList" />
        <client-search
          v-model="searchQuery.client_id"
          class="w-200px filter-item"
          @change="handleSearch" />
        <el-select
          v-model="searchQuery.payway"
          filterable
          clearable
          class="filter-item"
          placeholder="Pay Way"
          @change="handleSearch">
          <el-option
            v-for="item in contract_options.payWay"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-input v-model="searchQuery.name_contains"
                  class="filter-item w-200px"
                  placeholder="Name"
                  @input="debounceSearch" />

        <el-select
          v-model="searchQuery.am_team_id"
          filterable
          clearable
          class="filter-item"
          placeholder="AM Team"
          @change="handleSearch">
          <el-option
            v-for="item in am_team_list"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <el-button class="filter-item" type="success" icon="el-icon-plus" @click="actionGoToCreate">New</el-button>
        <el-button class="filter-item" type="primary" icon="el-icon-download" @click="actionDownload">Download</el-button>
      </div>
      <div class="tabs-stackOverflow status-tags">
        <template v-for="(item, index) in contract_options.approval_status">
          <el-tag
            :key="index"
            :class="{active: (searchQuery.approval_status === item.value) || ((item.value === 'TOTAL') && !searchQuery.approval_status) }"
            @click.native="filterStatusSearch(item)">
            {{ item.label }}
            <span class="status-count">{{ getAggregationsCount('approval_status', item.value, aggregations) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      class="table-list"
      :data="tableData"
    >
      <el-table-column prop="id" label="ID" width="50" />
      <el-table-column label="Client">
        <template slot-scope="scope">
          <router-link-client :data="{name:scope.row.client_name,id:scope.row.client_id}" />
        </template>
      </el-table-column>

      <el-table-column label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name: 'ContractDetail', params: { id: scope.row.id, is_update: false } }">
            {{ scope.row.name }}
          </router-link>
          <el-tag v-if="scope.row.applicable_project_types === 'SURVEY'" size="mini" type="success">Survey</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Pay way">
        <template slot-scope="scope">
          <span>{{ scope.row.payway | getLabel(contract_options.payWay) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Unit Price US / China">
        <template slot-scope="scope">
          <span v-if="!scope.row.unit_price_us && !scope.row.unit_price_china">- -</span>
          <span v-else>{{ scope.row.unit_price_us }} / {{ scope.row.unit_price_china }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Currency" width="80">
        <template slot-scope="scope">
          {{ scope.row.currency }}
        </template>
      </el-table-column>

      <el-table-column label="Usage" width="140">
        <template slot-scope="scope">
          <div v-if="scope.row.payway === 'PROJECT_BASE'">
            <span v-if="scope.row.used_size">
              <el-link :underline="false" type="primary">{{ scope.row.used_size }}</el-link> /
            </span>
            {{ scope.row.contract_size }} {{ scope.row.currency }}
          </div>
          <div v-else>
            <usage-progress-bar
              v-if="scope.row.charge_hours > 0"
              :total="scope.row.charge_hours"
              :used="scope.row.used_hours" />
            <span v-else>{{ scope.row.used_hours || '- -' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="AM Team">
        <template slot-scope="scope">
          <span v-if="scope.row.client.am_team">{{ scope.row.client.am_team.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Start Date">
        <template slot-scope="scope">
          <span v-if="scope.row.payway === 'PROJECT_BASE'">- -</span>
          <span v-else>{{ scope.row.start_time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="End Date">
        <template slot-scope="scope">
          <span v-if="scope.row.payway === 'PROJECT_BASE'">- -</span>
          <span v-else>{{ scope.row.end_time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Approval Status" min-width="92">
        <template slot-scope="scope">
          <div class="flex justify-content-center  flex-wrap">
            <el-dropdown class="contract-status-dropdown" @command="actionUpdateApprovalStatus">
              <span :class="scope.row.approval_status | getProjectStausClass(contract_options.approval_status)">
                {{ scope.row.approval_status | getLabel(contract_options.approval_status) }}
                <i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  value="REQUEST"
                  :disabled="checkRequest(scope.row)"
                  :command="{item:scope.row,type:'REQUEST'}">
                  Draft Request
                </el-dropdown-item>
                <el-dropdown-item
                  value="APPLYING"
                  :disabled="checkApply(scope.row)"
                  :command="{item:scope.row,type:'APPLYING'}">
                  Apply
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-link
              v-if="checkHasFeedback(scope.row)"
              :underline="false"
              @click="feedbackModalShow(scope.row)">
              <i class="el-icon-chat-dot-round" /></el-link>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="Effective Status">
        <template slot-scope="scope">
          <span :class="scope.row.effective_status | getProjectStausClass(contract_options.effective_status)">
            {{ scope.row.effective_status | getLabel(contract_options.effective_status) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="Actions" width="90">
        <template slot-scope="scope">
          <change-time
            :origin-time="{start_time:scope.row.start_time,end_time:scope.row.end_time,}"
            :contract-id="scope.row.id"
            :is-meaning-contract="checkContractHasMean(scope.row.approval_status)"
            :disable="checkChangeTime(scope.row)"
            :client-id="scope.row.client_id"
            @update="getList" />
          <el-link
            type="primary"
            :underline="false"
            :disabled="checkEdit(scope.row)"
            icon="el-icon-edit"
            class="mr-8 ml-8"
            @click="actionGoToDetail(scope.row,true)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            :disabled="checkDelete(scope.row)"
            @confirm="deleteContract(scope.row.id)">
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              :disabled="checkDelete(scope.row)"
              icon="el-icon-delete" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <apply
      :contract-id="selected_id"
      :apply-dialog-visible.sync="is_apply"
      :client-id="selected_client_id"
      :draft-time="apply_check_time"
      :type="selected_type"
      :exited-file-list="selected_file_list"
      @update="updateList" />

    <request :contract-id="selected_id" :request-visible.sync="is_request" @update="updateList" />

    <el-dialog
      title="Feedback"
      :visible.sync="feedbackDialog"
      width="30%"
    >
      <div>
        <p>{{ feedbackData.content }}</p>
        <div class="feedback-creator">
          <span>By {{ feedbackData.creator }} --
            {{ feedbackData.create_at | momentFormat('MM/DD/YYYY HH:mm') }}
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <a
          class="link mr-8"
          :href="'mailto:'+feedbackData.creator_mail+'?body=Click%20for%20details:%20http%3A%2F%2Fqa-udb2.capvision.com/#/compliance/contractapproval?id='+feedbackData.contract_id">Send Mail</a>
        <el-button class="ml-8" type="primary" @click="feedbackDialog = false">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'
import { mapGetters } from 'vuex'
import ClientAPI from '@/api/client'
import ContractAPI from '@/api/contract'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import UsageProgressBar from '@/components/UsageProgressBar'
import Request from './Request'
import Apply from './Apply'
import ChangeTime from './ChangeTime'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import RefreshButton from '@/components/RefreshButton/index'
import RouteTools from '@/utils/tool-router'
import { debounce } from '@/utils/tool'

export default {
  name: 'ContractList',
  components: {
    RefreshButton,
    ClientSearch,
    Request,
    Apply,
    Pagination,
    ChangeTime,
    UsageProgressBar,
    RouterLinkClient,
  },
  props: {
    isCurrentRoute: Boolean,
    projectId: [Number, String],
  },

  data() {
    return {
      loading: false,
      feedbackDialog: false,
      feedbackData: {
        contract_id: '',
        content: '',
        creator: '',
        create_at: '',
        creator_mail: '',
      },
      initAggregation: true,
      tableData: [],
      client_options: [],
      aggregations: {},
      total: 0,
      searchQuery: {
        id: null,
        client_id: null,
        name_contains: undefined,
        approval_status: 'PENDING_APPROVAL',
        payway: null,
        page: 1,
        size: 10,
        extra: 'client_name,generator,approver,used_hours,materials,used_size,client.am_team',
        aggregation: 'approval_status',
        sort: 'id desc',
      },
      // request & apply
      selected_id: null,
      selected_type: null,
      selected_file_list: [],
      selected_client_id: null,
      apply_check_time: {},
      is_request: false,
      is_apply: false,
      am_team_list: [],
    }
  },

  computed: {
    ...mapGetters([
      'contract_options',
    ]),
  },

  mounted() {
    this.init()
  },

  methods: {
    init() {
      RouteTools.resolveRouteQuery(this.$route, this.searchQuery)

      this.getOptions()

      if (this.isCurrentRoute) {
        this.searchQuery.id = this.$route.query.id || ''
        this.getList()
      }
    },

    getList() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      this.loading = true
      this.tableData = []
      const params = this.formatParams(this.searchQuery)
      params['client.am_team_id'] = params.am_team_id
      delete params.am_team_id
      return ContractAPI.getContractList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count || 0
          if (this.initAggregation && data.aggregations) {
            this.aggregations = data.aggregations
            this.aggregations.total = data.count
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    updateList() {
      this.initAggregation = true
      this.getList()
    },

    formatParams(params) {
      const data = Object.assign({}, params)
      Object.keys(data)
        .forEach(function(key) {
          if (!data[key]) {
            delete data[key]
          }
        })
      return data
    },

    filterStatusSearch(item) {
      this.searchQuery.approval_status = item.value
      this.initAggregation = false
      this.getList()
    },

    getAggregationsCount(status, value, aggregations) {
      let result = 0
      if (value === null) result = aggregations.total
      if (!aggregations[status]) return result
      aggregations[status].forEach(item => {
        if (item.value === value) {
          result = item.count
        }
      })
      return result
    },

    debounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.searchQuery.id = null
      this.searchQuery.approval_status = 'PENDING_APPROVAL'
      this.initAggregation = true
      this.getList()
    },

    deleteContract(id) {
      return ContractAPI.deleteContract(id)
        .then(data => {
          this.getList()
        })
    },

    actionUpdateApprovalStatus(data) {
      const item = data.item
      this.selected_id = item.id
      if (data.type === 'APPLYING') {
        this.selected_type = item.payway
        this.selected_client_id = item.client_id
        this.selected_file_list = item.materials
        this.apply_check_time = { start_time: item.start_time, end_time: item.end_time }
        this.is_apply = true
      } else {
        this.is_request = true
      }
    },

    checkHasFeedback(item) {
      const apply_status = ['APPROVED', 'REJECTED'].includes(this.searchQuery.approval_status)
      if (apply_status) {
        return item.approve_note
      } else {
        return item.generate_note
      }
    },

    feedbackModalShow(data) {
      const result = { contract_id: data.id }

      if (['REJECTED', 'APPROVED'].includes(data.approval_status)) {
        result.content = data.approve_note
        result.create_at = data.approve_time
        result.creator = data.approver.name
        result.creator_mail = data.approver.email
      } else {
        result.content = data.generate_note
        result.create_at = data.generate_time
        result.creator = data.generator.name
        result.creator_mail = data.generator.email
      }

      this.feedbackData = result
      this.feedbackDialog = true
    },

    actionGoToCreate() {
      this.$router.push({ name: 'CreateContract' })
    },

    actionGoToDetail(item, update = false) {
      this.$router.push({ name: 'ContractDetail', params: { id: item.id, is_update: update } })
    },
    actionDownload() {
      const params = this.formatParams(this.searchQuery)
      params['client.am_team_id'] = params.am_team_id
      delete params.am_team_id
      return ContractAPI.downloadContractList(params)
    },
    getOptions() {
      const client_option = ClientAPI.getClientList()
        .then(data => {
          this.client_options = data.list.map(item => {
            return { name: item.name, id: item.id }
          })
          this.client_options = _.sortBy(this.client_options, ({ name }) => name.toLowerCase())
        })
      const am_team_option = ClientAPI.getAmTeamList()
        .then(data => {
          data['list'].map(item => {
            this.am_team_list.push({
              name: item.name,
              id: item.id,
            })
          })
        })
      return Promise.all([client_option, am_team_option])
    },

    // 正在in request状态 || 正在申请 || 申请已通过  =>不能请求拟合同
    checkRequest(item) {
      // return ['IN_REQUEST'].includes(item.request_status) || ['APPLYING', 'APPROVED'].includes(item.approval_status)
      return ['DRAFT_REQUESTED', 'DRAFT_CREATED', 'PENDING_APPROVAL', 'APPROVED'].includes(item.approval_status)
    },
    // 正在in request状态 || 正在申请 || 申请已通过 || 合同已生效或过期的 => 不能申请
    checkApply(item) {
      return ['EFFECTIVE', 'EXPIRED'].includes(item.effective_status) ||
        !['DRAFT_CREATED', 'REJECTED'].includes(item.approval_status)
      // return ['IN_REQUEST'].includes(item.request_status) || ['APPLYING', 'APPROVED'].includes(item.approval_status) || ['EFFECTIVE', 'EXPIRED'].includes(item.effective_status)
    },

    // 正在in request状态 || 正在申请 || 申请已通过 || 合同已生效或过期的 => 不能编辑
    // 05.06 美国要求申请时可以再编辑合同 =》申请已通过 不限制
    checkEdit(item) {
      return !['DRAFT', 'DRAFT_CREATED', 'REQUEST_REJECTED', 'REJECTED'].includes(item.approval_status)
    },

    checkChangeTime(item) {
      // return ['IN_REQUEST'].includes(item.request_status) || ['APPLYING'].includes(item.approval_status) ||
      //   item.payway === 'PROJECT_BASE'
      return ['DRAFT_REQUESTED', 'PENDING_APPROVAL'].includes(item.approval_status) ||
        item.payway === 'PROJECT_BASE'
    },

    checkContractHasMean(status) {
      // return ['APPLYING', 'APPROVED'].includes(status)
      return ['PENDING_APPROVAL', 'APPROVED'].includes(status)
    },

    checkDelete(item) {
      if (item.payway === 'TRIAL') {
        return false
      }
      return this.checkRequest(item) || ['EFFECTIVE'].includes(item.effective_status)
    },

  },
}
</script>

<style scoped lang="scss">

.table-list {
  user-select: none;
}

.contract-status-dropdown {
  font-size: 12px;
  cursor: pointer;
}

.action-icon {
  display: inline-block;
  margin-right: 5px;
}

.status-count {
  display: inline-block;
  opacity: 0.6;
  transform: scale(0.9);
}

.feedback-creator {
  text-align: right;
  font-size: 0.8rem;
  color: #806c6c;
}

</style>
