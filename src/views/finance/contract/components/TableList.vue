<template>
  <div>
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div>
        <el-button
          v-if="!disabledEdit"
          type="success"
          icon="el-icon-plus"
          class="filter-item"
          @click="actionGoToCreate">New
        </el-button>
      </div>
      <div class="tabs-stackOverflow filter-item">
        <template v-for="(item, index) in contract_options.approval_status">
          <el-tag
            :key="index"
            :class="{active: (searchQuery.approval_status === item.value) || ((item.value === 'TOTAL') && !searchQuery.approval_status) }"
            @click.native="filterStatusSearch(item)">
            {{ item.label }}
            <span class="status-count">{{ getAggregationsCount('approval_status', item.value, aggregations) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      class="table-list"
      :data="tableData"
    >
      <el-table-column prop="id" label="ID" width="50" />
      <el-table-column prop="name" label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name: 'ContractDetail', params: { id: scope.row.id, is_update: false } }">
            {{ scope.row.name }}
          </router-link>
          <el-tag v-if="scope.row.applicable_project_types === 'SURVEY'" size="mini" type="success">Survey</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Pay way">
        <template slot-scope="scope">
          <span>{{ scope.row.payway | getLabel(contract_options.payWay) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Unit Price US / China">
        <template slot-scope="scope">
          <span v-if="!scope.row.unit_price_us && !scope.row.unit_price_china">- -</span>
          <span v-else>{{ scope.row.unit_price_us }} / {{ scope.row.unit_price_china }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Usage" width="140">
        <template slot-scope="scope">
          <div v-if="scope.row.payway === 'PROJECT_BASE'">
            <span v-if="scope.row.used_size">
              <el-link :underline="false" type="primary">{{ scope.row.used_size }}</el-link> /
            </span>
            {{ scope.row.contract_size }} {{ scope.row.currency }}
          </div>
          <div v-else>
            <usage-progress-bar
              v-if="scope.row.charge_hours > 0"
              :total="scope.row.charge_hours"
              :used="scope.row.used_hours" />
            <span v-else>{{ scope.row.used_hours || '- -' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Start Date">
        <template slot-scope="scope">
          <span v-if="scope.row.payway === 'PROJECT_BASE'">- -</span>
          <span v-else>{{ scope.row.start_time | momentFormat() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="End Date">
        <template slot-scope="scope">
          <span v-if="scope.row.payway === 'PROJECT_BASE'">- -</span>
          <span v-else>{{ scope.row.end_time | momentFormat() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Approval Status">
        <template slot-scope="scope">
          <div class="flex justify-content-center">
            <el-dropdown class="contract-status-dropdown" @command="actionUpdateApprovalStatus">
              <span :class="scope.row.approval_status | getProjectStausClass(contract_options.approval_status)">
                {{ scope.row.approval_status | getLabel(contract_options.approval_status) }}
                <i class="el-icon-arrow-down el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  value="REQUEST"
                  :disabled="checkRequest(scope.row)"
                  :command="{item:scope.row,type:'REQUEST'}">
                  Draft Request
                </el-dropdown-item>
                <el-dropdown-item
                  value="APPLYING"
                  :disabled="checkApply(scope.row)"
                  :command="{item:scope.row,type:'APPLYING'}">
                  Apply
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-link
              v-if="checkHasFeedback(scope.row)"
              :underline="false"
              @click="feedbackModalShow(scope.row)">
              <i class="el-icon-chat-dot-round" /></el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Effective Status">
        <template slot-scope="scope">
          <span :class="scope.row.effective_status | getProjectStausClass(contract_options.effective_status)">
            {{ scope.row.effective_status | getLabel(contract_options.effective_status) }}
          </span>
          <el-popconfirm
            v-if="showExpired(scope.row)"
            title="Are you sure to change this contract to expired status?"
            @confirm="actionStatusToExpired(scope.row)"
          >
            <el-button
              slot="reference"
              type="danger"
              :loading="expiredLoading"
              class="el-button--xs ml-8">Expire
            </el-button>
          </el-popconfirm>
          <el-button
            v-if="showEffective(scope.row)"
            type="success"
            class="el-button--xs ml-8"
            @click="showAdjustEffectiveDateDialog(scope.row)">Effective
          </el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="!disabledEdit" label="Actions" width="90">
        <template slot-scope="scope">
          <change-time
            :origin-time="{start_time:scope.row.start_time,end_time:scope.row.end_time,}"
            :contract-id="scope.row.id"
            :effective-status="scope.row.effective_status"
            :is-meaning-contract="checkContractHasMean(scope.row.approval_status)"
            :disable="checkChangeTime(scope.row)"
            :client-id="scope.row.client_id"
            @update="getList" />
          <el-link
            type="primary"
            :underline="false"
            :disabled="checkEdit(scope.row)"
            icon="el-icon-edit"
            class="mr-8 ml-8"
            @click="actionGoToDetail(scope.row,true)" />
          <el-popconfirm
            icon="el-icon-info"
            icon-color="#F56C6C"
            title="Confirm to Delete？"
            cancel-button-text="Cancel"
            confirm-button-text="Confirm"
            confirm-button-type="danger"
            :disabled="checkDelete(scope.row)"
            @confirm="deleteContract(scope.row.id)">
            <el-link
              slot="reference"
              type="danger"
              :underline="false"
              :disabled="checkDelete(scope.row)"
              icon="el-icon-delete" />
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <apply
      :contract-id="selected_id"
      :apply-dialog-visible.sync="is_apply"
      :client-id="selected_client_id"
      :draft-time="apply_check_time"
      :type="selected_type"
      @update="updateList" />

    <request :contract-id="selected_id" :request-visible.sync="is_request" @update="updateList" />

    <el-dialog
      title="Feedback"
      :visible.sync="feedbackDialog"
      width="30%"
    >
      <div>
        <p>{{ feedbackData.content }}</p>
        <div class="feedback-creator">
          <span>By {{ feedbackData.creator }} -- {{ feedbackData.create_at | momentFormat('MM/DD/YYYY HH:mm') }}
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <a
          class="link mr-8"
          :href="'mailto:'+feedbackData.creator_mail+'?body=Click%20for%20details:%20http%3A%2F%2Fqa-udb2.capvision.com/#/compliance/contractapproval?id='+feedbackData.contract_id">Send Mail</a>
        <el-button class="ml-8" type="primary" @click="feedbackDialog = false">OK</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title=""
      :visible.sync="effectiveContractDialog"
      width="50%">
      <div>
        <div
          class="mb-normal">Effective time period of the contract:
          <div class="mt-3"><b>{{effective_contract.start_time}} -- {{effective_contract.end_time | momentFormat()}}</b></div>
        </div>
        <el-form ref="form">
          <el-form-item label="Start Date">
            <el-date-picker
              v-model="effective_contract.start_time"
              type="date"
              :clearable="false"
              format="MM/dd/yyyy"
              value-format="MM/dd/yyyy"
              :picker-options="pickerOptionsStart"
              placeholder="Start Date"
              class="w-200px"
            />
            <el-button type="success" class="ml-3" @click="adjustEffectiveDate">Adjust</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import ContractAPI from '@/api/contract'
import Pagination from '@/components/Pagination'
import UsageProgressBar from '@/components/UsageProgressBar'
import Request from './Request'
import Apply from './Apply'
import ChangeTime from './ChangeTime'
import moment from 'moment-timezone'

export default {
  name: 'TableList',
  components: { Request, Apply, Pagination, ChangeTime, UsageProgressBar },
  props: {
    clientId: {
      type: [Number, String],
      default: 0,
    },
    clientName: String,
    disabledEdit: Boolean,
    isClientAm: Boolean,
  },

  data() {
    return {
      loading: false,
      expiredLoading: false,
      feedbackDialog: false,
      effectiveContractDialog: false,
      feedbackData: {
        contract_id: '',
        content: '',
        creator: '',
        create_at: '',
        creator_mail: '',
      },
      tableData: [],
      total: 0,
      aggregations: {},

      searchQuery: {
        id: null,
        client_id: this.clientId,
        approval_status: null,
        page: 1,
        size: 20,
      },
      // request & apply
      selected_id: null,
      selected_type: null,
      selected_client_id: null,
      apply_check_time: {},
      is_apply: false,
      is_request: false,

      effective_contract: { start_time: '', end_time: '' },
      pickerOptionsStart: {
        disabledDate: (time) => {
          return time.getTime() > moment.tz(this.effective_contract.end_time, this.timeZone)
        },
      },
    }
  },

  computed: {
    ...mapGetters([
      'contract_options', 'roles',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),

    is_compliance() {
      return this.roles.filter(item => item.role_id === 4).length
    },
  },

  mounted() {
    this.getList()
  },

  methods: {

    getList() {
      this.loading = true
      this.tableData = []
      const params = Object.assign({
        'extra': 'client_name,generator,approver,used_hours',
        'aggregation': 'approval_status',
      }, this.searchQuery)

      return ContractAPI.getContractList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
          this.aggregations = data.aggregations
        })
        .finally(() => {
          this.loading = false
        })
    },

    updateList() {
      this.getList()
    },

    filterStatusSearch(item) {
      this.searchQuery.approval_status = item.value
      this.getList()
    },

    getAggregationsCount(status, value, aggregations) {
      let result = 0
      if (value === null) result = aggregations.total
      if (!aggregations[status]) return result
      aggregations[status].map(item => {
        if (item.value === value) {
          result = item.count
        }
      })
      return result
    },

    deleteContract(id) {
      return ContractAPI.deleteContract(id)
        .then(data => {
          this.getList()
        })
    },

    actionUpdateApprovalStatus(data) {
      const item = data.item
      this.selected_id = item.id
      if (data.type === 'APPLYING') {
        this.selected_type = item.payway
        this.selected_client_id = item.client_id
        this.selected_file_list = item.materials
        this.apply_check_time = { start_time: item.start_time, end_time: item.end_time }
        this.is_apply = true
      } else {
        this.is_request = true
      }
    },
    showExpired(item) {
      return item.effective_status === 'EFFECTIVE' && this.isClientAm
    },

    showEffective(item) {
      return item.effective_status === 'INEFFECTIVE' && item.approval_status === 'APPROVED' && this.isClientAm
    },

    showAdjustEffectiveDateDialog(item) {
      this.effective_contract = {
        start_time: moment.tz(item.start_time, this.timeZone).format('MM/DD/YYYY'),
        end_time: item.end_time,
        id: item.id,
      }
      this.effectiveContractDialog = true
    },

    adjustEffectiveDate() {
      const adjust_start_time = moment(this.effective_contract.start_time).format('YYYY-MM-DD')
      const params = {
        adjusted_start_time: moment.tz(`${adjust_start_time} 00:00:00`, this.timeZone).toISOString(),
        contract_id: this.effective_contract.id,
      }
      return ContractAPI.adjustContractStartTime(params)
        .then(data => {
          this.$message.success('Modified successfully.')
          this.tableData.forEach(item => {
            if (item.id === data.contract.id) {
              Object.assign(item, data.contract)
            }
          })
          this.effectiveContractDialog = false
        })
    },

    checkHasFeedback(item) {
      const apply_status = ['APPROVED', 'REJECTED'].includes(this.searchQuery.approval_status)
      if (apply_status) {
        return item.approve_note
      } else {
        return item.generate_note
      }
    },

    feedbackModalShow(data) {
      const result = { contract_id: data.id }
      if (['REJECTED', 'APPROVED'].includes(data.approval_status)) {
        result.content = data.approve_note
        result.create_at = data.approve_time
        result.creator = data.approver.name
        result.creator_mail = data.approver.email
      } else {
        result.content = data.generate_note
        result.create_at = data.generate_time
        result.creator = data.generator.name
        result.creator_mail = data.generator.email
      }
      this.feedbackData = result
      this.feedbackDialog = true
    },

    actionGoToCreate() {
      this.$router.push({ name: 'CreateContract', query: { client_id: +this.clientId, name: this.clientName } })
    },

    actionGoToDetail(item, update = false) {
      this.$router.push({ name: 'ContractDetail', params: { id: item.id, is_update: update } })
    },

    actionStatusToExpired(item) {
      this.expiredLoading = true
      return ContractAPI.expireContract(item.id)
        .then(() => {
          item.effective_status = 'EXPIRED'
        }).finally(() => {
          this.expiredLoading = false
        })
    },

    checkRequest(item) {
      return ['DRAFT_REQUESTED', 'DRAFT_CREATED', 'PENDING_APPROVAL', 'APPROVED'].includes(item.approval_status) ||
        this.disabledEdit
    },

    checkApply(item) {
      return ['EFFECTIVE', 'EXPIRED'].includes(item.effective_status) ||
        !['DRAFT_CREATED', 'REJECTED'].includes(item.approval_status) || this.disabledEdit
    },

    checkEdit(item) {
      // 05.06 美国要求申请时可以再编辑合同 =》申请已通过 不限制
      return !['DRAFT', 'DRAFT_CREATED', 'REQUEST_REJECTED', 'REJECTED'].includes(item.approval_status) ||
        this.disabledEdit
    },

    checkChangeTime(item) {
      if (['APPROVED'].includes(item.approval_status) && !this.is_compliance) {
        return true
      }
      return ['DRAFT_REQUESTED', 'PENDING_APPROVAL'].includes(item.approval_status) ||
        item.payway === 'PROJECT_BASE' || this.disabledEdit
    },

    checkContractHasMean(status) {
      return ['PENDING_APPROVAL', 'APPROVED'].includes(status)
    },

    checkDelete(item) {
      if (item.payway === 'TRIAL') {
        return false
      }
      return ['DRAFT_REQUESTED', 'PENDING_APPROVAL', 'APPROVED'].includes(item.approval_status) ||
        ['EFFECTIVE'].includes(item.effective_status) || this.disabledEdit
    },

  },
}
</script>

<style scoped lang="scss">

.table-list {
  user-select: none;
}

.contract-status-dropdown {
  font-size: 12px;
  cursor: pointer;
}

.action-icon {
  display: inline-block;
  margin-right: 5px;
}

.status-count {
  display: inline-block;
  opacity: 0.6;
  transform: scale(0.9);
}

.feedback-creator {
  text-align: right;
  font-size: 0.8rem;
  color: #806c6c;
}

::v-deep .el-date-editor.el-input {
  width: 180px;
}
</style>
