<template>
  <el-dialog
    title="Apply For Approval"
    :visible.sync="applyDialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="50%"
  >
    <div v-loading="contractLoading" style="min-height: 100px;">
      <contract-detail
        v-if="contractData.id"
        ref="contractDetail"
        :data="contractData"
        is-edit
        apply-dialog-edit />
    </div>
    <el-divider />

    <el-form ref="form" label-position="left">
      <div v-if="type !== 'PROJECT_BASE'" v-loading="check_time_loading" class="check-time-section">
        <el-form-item label="Current contract">
          <el-date-picker
            v-model="suggest_time.start_time"
            type="date"
            :clearable="false"
            format="MM/dd/yyyy HH:mm"
            :picker-options="pickerOptionsStart"
            placeholder="Start Date"
          />
          <span> -- </span>
          <el-date-picker
            v-model="suggest_time.end_time"
            type="date"
            format="MM/dd/yyyy  HH:mm"
            :picker-options="pickerOptionsEnd"
            :clearable="false"
            placeholder="End Date"
          />
        </el-form-item>

        <div v-if="tips" class="tip-section">
          <p class="ml-8" :class="{'warning':not_allowed}">{{ tips }}</p>
        </div>
        <div v-if="last_contract.end_time" class="mb-8 info">
          <span>Last {{all_contracts_expired ? 'expired':'effective'}}:</span>
          {{last_contract.start_time | momentFormat('MM/DD/YYYY')}} - {{last_contract.end_time | momentFormat('MM/DD/YYYY')}}
        </div>
      </div>

      <el-form-item>
        <el-upload
          accept=".pdf, .doc, .docx, .xls, .xlsx, .zip"
          :action="upload_url"
          :on-success="handleSuccess"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :disabled="not_allowed"
          multiple
          :data="{'uid':uid}"
          :file-list="fileList">
          <div class="flex">
            <el-button size="small" type="primary" :disabled="not_allowed">Upload Contract</el-button>
            <div class="inline-block tooltip-icon flex align-items-center" @click.stop>
              <span class="el-icon-question" />
              <span class="upload-tip">The maximum upload size is {{ file_maxSize }} MB. </span>
            </div>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="Apply Notes">
        <el-input v-model="params.apply_note" :disabled="not_allowed" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="success" :disabled="not_allowed" @click="applyContract()">Apply</el-button>
      <el-button type="text" @click="$emit('update:applyDialogVisible', false)">Cancel</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from '@/api/contract'
import { mapState, mapGetters } from 'vuex'
import moment from 'moment'
// import CustomTimeLine from './CustomTimeLine'
import ContractDetail from './../update/index'

export default {
  name: 'Apply',
  components: {
    // CustomTimeLine,
    ContractDetail,
  },
  props: {
    contractId: Number,
    clientId: Number,
    type: String,
    draftTime: {
      type: Object,
      default: () => {
        return {}
      },
    },
    applyDialogVisible: {
      type: Boolean,
      default: false,
    },
    exitedFileList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },

  data() {
    return {
      upload_url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      file_maxSize: 20,
      fileList: [],
      params: {
        apply_note: '',
        materials: [],
      },
      upload_file_list: [],
      // check time
      not_allowed: false,
      disabled_date: moment.tz(moment(), this.timeZone),
      suggest_time: {
        start_time: '',
        end_time: '',
      },
      last_contract: {
        start_time: '',
        end_time: '',
      },
      tips: '',
      contractData: {},
      contractLoading: false,
      check_time_loading: false, all_contracts_expired: false,
      pickerOptionsStart: {
        disabledDate: (time) => {
          return time.getTime() < moment.tz(this.disabled_date, this.timeZone)
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          if (this.all_contracts_expired) {
            return time.getTime() < moment.tz(moment(), this.timeZone)
          } else {
            return time.getTime() < moment.tz(this.suggest_time.start_time, this.timeZone)
          }
        },
      },
    }
  },

  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },

  watch: {
    'applyDialogVisible':
      {
        handler(newVal) {
          this.fileList = []
          if (newVal) {
            this.getContract()
            if (this.type !== 'PROJECT_BASE') {
              this.suggest_time = Object.assign({}, this.formatTime(this.draftTime))
              this.getClientContract()
            }
          } else {
            this.resetData()
          }
          this.getFileList(this.exitedFileList)
        },
      },
  },

  methods: {
    getContract() {
      const params = { 'extra': 'client_name,payment_term,materials,invoices,invoices.client,invoices.contract,used_hours,billing_contact' }
      this.contractLoading = true
      return API.getContractDetail(this.contractId, params)
        .then(data => {
          this.contractData = data
          this.contractLoading = false
          this.contractData.service_types = this.contractData.applicable_project_types.split(',')
        })
    },

    formatTime(data) {
      return {
        start_time: moment(data.start_time)
          .tz(this.timeZone),
        end_time: moment(data.end_time)
          .tz(this.timeZone),
      }
    },

    getClientContract() {
      const extra = { 'extra': 'client_name,generator,approver' }
      const params = Object.assign(extra, {
        client_id: this.clientId,
        approval_status_in: 'PENDING_APPROVAL,APPROVED',
      })

      return API.getContractList(params)
        .then(data => {
          if (data.count) {
            this.check_time_loading = true
            const filter_list = data['list'].filter(item => item.payway !== 'PROJECT_BASE')
            this.checkEffectTime(filter_list)
          }
        })
    },

    // 如果该客户已有合同，apply时避免当前合同与原有合同重叠或有缝隙，系统给出建议生效时间，用户仍然可以修改
    // 当客户有正在生效的合同时，取结束时间最大的合同（A），建议生效时间为A的结束时间，且生效时间和结束时间不得小于A的结束时间
    // 当客户的合同都已失效时，取结束时间最大的合同（B），建议生效时间为B的结束时间，且结束时间不得小于当前时间
    checkEffectTime(list) {
      const in_applying = list.filter(item => item.approval_status === 'PENDING_APPROVAL')
      const in_approved = list.filter(item => item.approval_status === 'APPROVED')

      if (in_applying.length) {
        this.not_allowed = true
        this.tips = 'The customer is currently applying for a contract, so it is not allowed to apply'
      } else if (in_approved.length) {
        let client_last_end_time = in_approved[0].end_time
        let client_last_start_time = in_approved[0].start_time

        const is_effective = in_approved.filter(item => item.effective_status === 'EFFECTIVE')

        if (is_effective.length > 0) {
          this.all_contracts_expired = false
          is_effective.forEach(item => {
            if (item.end_time > client_last_end_time) {
              client_last_end_time = item.end_time
              client_last_start_time = item.start_time
            }
          })
        } else {
          this.all_contracts_expired = true
          in_approved.forEach(item => {
            if (item.end_time > client_last_end_time) {
              client_last_end_time = item.end_time
              client_last_start_time = item.start_time
            }
          })
        }

        this.last_contract.end_time = moment.tz(client_last_end_time, this.timeZone)
        this.last_contract.start_time = moment.tz(client_last_start_time, this.timeZone)

        const rightTime = moment.tz(client_last_end_time, this.timeZone)

        if (this.timeIsSame(rightTime, this.draftTime.start_time)) {
          this.not_allowed = false
        } else {
          this.setSuggestedTime()
        }

        if (this.all_contracts_expired) {
          this.disabled_date = null
        } else {
          this.disabled_date = moment.tz(this.last_contract.end_time, this.timeZone) >
          moment.tz(moment(), this.timeZone) ? moment.tz(this.last_contract.end_time, this.timeZone) : null
        }
      }
      this.check_time_loading = false
    },

    setSuggestedTime() {
      this.suggest_time.start_time = moment.tz(this.last_contract.end_time, this.timeZone).add(1, 's')
        .format('YYYY-MM-DD HH:mm:ss')
    },

    async applyContract() {
      if (!this.upload_file_list.length) {
        return this.$message({
          message: 'Must upload signed contract',
          type: 'error',
        })
      }
      this.$refs['contractDetail'].form.start_time = moment(this.suggest_time.start_time).format('YYYY-MM-DD HH:mm:ss')
      this.$refs['contractDetail'].form.end_time = moment(this.suggest_time.end_time).format('YYYY-MM-DD HH:mm:ss')
      this.$refs['contractDetail'].asyncOnSave().then(() => {
        this.saveFile()
      }, () => {
        this.$message({
          message: 'The form was not entered correctly.',
          type: 'error',
        })
      })
      // await this.saveFile()
      // if (this.type === 'PROJECT_BASE') {
      // await this.saveFile()
      // } else {
      // await this.saveFileAndTime()
      // }
    },

    // saveFileAndTime() {
    //   const params = {
    //     id: this.contractId,
    //     // contract start_time保持到时分 / end_time 设置为当天0点
    //     start_time: moment.tz(this.suggest_time.start_time, this.timeZone).toISOString(),
    //     end_time: contractMomentFormat(moment(this.suggest_time.end_time).format('YYYY-MM-DD'), 'start'),
    //   }
    //   return API.updateContractItem(this.contractId, params)
    //     .then(data => {
    //       this.saveFile()
    //     })
    // },

    saveFile() {
      this.params.materials = this.upload_file_list
      return API.applyContract(this.contractId, this.params)
        .then(data => {
          this.$emit('update:applyDialogVisible', false)
          this.$emit('update')
          this.$message.success('success')
        })
    },

    timeIsSame(first, second) {
      return moment.tz(first, this.timeZone).isSame(moment.tz(second, this.timeZone))
    },

    resetData() {
      this.contractData = {}
      this.fileList = []
      this.params = {
        apply_note: '',
        materials: [],
      }
      this.upload_file_list = []
      this.not_allowed = false
      this.suggest_time = {
        start_time: '',
        end_time: '',
      }
      this.last_contract = {
        start_time: '',
        end_time: '',
      }
      this.tips = ''
    },

    getFileList(list) {
      const signed_contract_list = list.filter(item => item.type === 'SIGNED_CONTRACT_FILE')
      if (signed_contract_list.length) {
        this.upload_file_list = list
        this.fileList = signed_contract_list.map(item => {
          return { name: item.file_name, url: item.file_url }
        })
      }
    },

    handleSuccess(response) {
      if (response.msg === 'ok') {
        const item = {
          file_name: response.data.file_name,
          file_url: response.data.url,
          type: 'SIGNED_CONTRACT_FILE',
        }
        this.upload_file_list.push(item)
      } else {
        this.$message(response.message)
      }
    },

    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > this.file_maxSize * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },

    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`)
      }
    },

    handleRemove(file, fileList) {
      const index = this.upload_file_list.indexOf(item => item.file_url === file.url)
      this.upload_file_list.splice(index, 1)
      console.log(this.upload_file_list)
    },
  },

}
</script>

<style lang="scss" scoped>
.tooltip-icon {
  margin-left: 10px;
  font-size: 1.2rem;
  color: #606266;

  .upload-tip {
    display: none;
  }

  &:hover {
    .upload-tip {
      display: flex;
      padding-left: 10px;
      font-size: 0.8rem !important;
      line-height: 0.8rem;
      word-break: break-word;
    }
  }
}

.tip-section {
  font-size: 0.8rem;
  line-height: 1.1rem;
  margin-left: 180px;
  height: 20px;
  color: #aba6a6;

  .warning {
    color: red;
  }
}

.check-time-section {
  border-bottom: 1px solid #DCDFE6;
  margin-bottom: 10px;

  ::v-deep .el-form-item__label {
    width: 180px;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 10px;
  }

  ::v-deep .el-slider__marks-text {
    &:nth-child(2) {
      transform: translate(-100%);
    }

    &:nth-child(3) {
      transform: translate(0%);
    }
  }

  ::v-deep .el-slider__stop {
    width: 16px;
    height: 16px;
    top:-5px;
    border: 2px solid #409EFF;
    background-color: #FFF;
    border-radius: 50%;
    -webkit-transition: .2s;
    transition: .2s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

::v-deep .el-date-editor.el-input {
  width: 180px;
}
</style>
