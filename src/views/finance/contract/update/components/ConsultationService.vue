<template>
  <div>
    <el-form label-position="right" label-width="180px" :model="form" class="custom-form">
      <el-form-item label="Client Hour">
        <el-input v-model="form.term_client_hours" disabled />
        <step-rule v-if="!isShow" type="unit_rule" :value.sync="form.term_client_hours" class="inline-block" />
      </el-form-item>
      <el-form-item label="Non-China Senior Expert">
        <el-input v-model="form.term_non_china_senior" disabled />
        <step-rule
          v-if="!isShow || getPermission"
          type="non_china_senior"
          :permission-update="getPermission"
          :currency="form.currency"
          :value.sync="form.term_non_china_senior"
          class="inline-block"
          @update="updateSeniorExpert"
        />
      </el-form-item>
      <el-form-item label="China Senior Expert">
        <el-input v-model="form.term_china_senior" disabled />
        <step-rule
          v-if="!isShow || getPermission"
          :permission-update="getPermission"
          type="china_senior"
          :value.sync="form.term_china_senior"
          class="inline-block"
          @update="updateSeniorExpert" />
      </el-form-item>
      <el-form-item label="Overseas">
        <el-input v-model="form.term_overseas" disabled />
        <simple-rule v-if="!isShow"
                     type="overseas"
                     :value.sync="form.term_overseas"
                     class="inline-block"
                     @update="checkUsPrice" />
      </el-form-item>
      <el-form-item label="Transcription">
        <el-input v-model="form.term_transcription" disabled />
        <simple-rule v-if="!isShow" type="transcription" :value.sync="form.term_transcription" class="inline-block" />
      </el-form-item>
      <el-form-item label="Translation">
        <el-input v-model="form.term_translation" disabled />
        <simple-rule v-if="!isShow" type="translation" :value.sync="form.term_translation" class="inline-block" />
      </el-form-item>
      <el-form-item label="In Person">
        <el-input v-model="form.term_in_person" disabled />
        <simple-rule v-if="!isShow" type="in_person" :value.sync="form.term_in_person" class="inline-block" />
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import UpdateContractMixin from '../mixin'
import SimpleRule from '../../components/SimpleRuleCustom'
import StepRule from '../../components/StepsRuleCustom'
import { mapGetters } from 'vuex'
import API from '@/api/contract'

export default {
  name: 'ConsultationService',
  components: { SimpleRule, StepRule },
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    /*
    compliance & admin & AdminExcludeCompliance & AM/Sales 可以在合同生效后改动 senior expert*/
    getPermission() {
      return this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'Compliance', 'AM / Sales'].includes(item.role.name)
      })
    },
  },
  methods: {
    // Overseas = [Price(US)-Price(China)] / Price(China) => Price(US) > Price(China)
    checkUsPrice(value) {
      if (this.form.unit_price_china) {
        this.form.unit_price_us = (+value + 1) * this.form.unit_price_china
      }
    },

    updateSeniorExpert() {
      if (!this.form.id) return

      const params = {
        id: this.form.id,
        'term_non_china_senior': this.form.term_non_china_senior,
        'term_china_senior': this.form.term_china_senior,
      }
      return API.updateContractItem(this.form.id, params).then(data => {
        this.$message.success('success')
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.top-line {
  padding-top: 20px;
  border-top: 1px dashed #e1e4e8;
}

.custom-form {
  ::v-deep .el-input {
    width: 290px;
    margin-right: 10px;
  }
}

.other-form {
  .number-input {
    width: 75px;
  }

  ::v-deep .el-radio-group {
    margin-left: 20px;
  }

  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }
}

</style>
