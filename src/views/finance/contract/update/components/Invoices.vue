<template>
  <div>
    <el-form-item label="Invoices">
      <invoice-list :table-data="form.invoices" :border="false" @delete="deleteInvoice" />
    </el-form-item>
    <el-divider />
  </div>
</template>

<script>
import InvoiceList from '@/views/finance/invoice/components/InvoiceList'

export default {
  name: 'Invoices',
  components: { InvoiceList },
  props: {
    form: Object,
  },
  methods: {
    deleteInvoice(index) {
      this.form.invoices.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
