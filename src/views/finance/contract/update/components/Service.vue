<template>
  <div>
    <slot v-if="!isProjectBase && !isSurveyOnly">

      <div>
        <el-form-item :label="UnitPriceLabel" prop="unit_price_us">
          <el-input v-model.number="form.unit_price_us" type="number" class="w-200px" @change="checkOverseas()">
            <template slot="append">{{ form.currency }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="Unit Price (China)" prop="unit_price_china">
          <el-input v-model.number="form.unit_price_china" type="number" class="w-200px" @change="checkOverseas()">
            <template slot="append">{{ form.currency }}</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isPrepay" label="Charge Hours" prop="charge_hours">
          <el-input v-model="form.charge_hours" type="number" :min="0" class="w-200px">
            <template slot="append">CapH</template>
          </el-input>
        </el-form-item>
        <el-form-item label="Currency" prop="currency">
          <el-radio v-model="form.currency" label="USD">USD</el-radio>
          <el-radio v-if="isSGDB" v-model="form.currency" label="SGD">SGD</el-radio>
          <el-radio v-if="isSGDB" v-model="form.currency" label="MYR">MYR</el-radio>
        </el-form-item>
        <el-form-item label="Client Address" prop="client_address">
          <el-input v-model="form.client_address" type="textarea" rows="3" />
        </el-form-item>
      </div>

    </slot>
    <slot v-if="isProjectBase">
      <el-form-item label="Contract Size" prop="contract_size">
        <el-input v-model="form.contract_size" class="w-200px" type="number" :min="0" />
      </el-form-item>
      <el-form-item label="Currency" prop="currency">
        <el-radio v-model="form.currency" label="USD">USD</el-radio>
        <el-radio v-if="isSGDB" v-model="form.currency" label="SGD">SGD</el-radio>
        <el-radio v-if="isSGDB" v-model="form.currency" label="MYR">MYR</el-radio>
      </el-form-item>
    </slot>
    <slot v-if="isSurveyOnly">
      <el-form-item label="Currency" prop="currency">
        <el-radio v-model="form.currency" label="USD">USD</el-radio>
        <el-radio v-if="isSGDB" v-model="form.currency" label="SGD">SGD</el-radio>
        <el-radio v-if="isSGDB" v-model="form.currency" label="MYR">MYR</el-radio>
      </el-form-item>
      <el-form-item label="Client Address" prop="client_address">
        <el-input v-model="form.client_address" type="textarea" rows="3" />
      </el-form-item>
    </slot>
  </div>
</template>

<script>
import UpdateContractMixin from '../mixin'
import { mapState } from 'vuex'

export default {
  name: 'Service',
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
  },

  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    UnitPriceLabel() {
      return this.isSGDB ? 'Unit Price (SG)' : 'Unit Price (US)'
    },
  },
  methods: {
    // Overseas = [Price(US)-Price(China)] / Price(China) => Price(US) > Price(China)
    checkOverseas() {
      if (this.form.unit_price_china && this.form.unit_price_us) {
        if (this.form.unit_price_china < this.form.unit_price_us) {
          const overseas_value = (this.form.unit_price_us - this.form.unit_price_china) / this.form.unit_price_china
          this.form.term_overseas = `+${overseas_value.toFixed(4)}h/H`
        } else {
          this.form.term_overseas = ''
        }
      }
    },
  },
}
</script>

<style scoped>

</style>
