<template>
  <div v-if="!isProjectBase">
    <el-form-item label="Start Date" prop="start_time" :required="!isShow">
      <div v-if="isShow">{{ form.start_time | momentFormat() }}</div>
      <el-date-picker
        v-else
        v-model="form.start_time"
        type="date"
        :disabled="disabledEdit"
        format="MM/dd/yyyy"
        value-format="yyyy-MM-dd"
        placeholder="Start Date" />
    </el-form-item>
    <el-form-item label="End Date" prop="end_time" :required="!isShow">
      <div v-if="isShow">
        {{ form.end_time | momentFormat() }}
        <span v-if="form.is_good_until_usage_expired" class="ml-3 info">Good until usage expired</span>
      </div>
      <div v-else>
        <el-date-picker
          v-model="form.end_time"
          type="date"
          :disabled="disabledEdit || form.is_good_until_usage_expired"
          format="MM/dd/yyyy"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptionsEnd"
          placeholder="End Date" />
        <el-checkbox v-if="isPrepay" v-model="form.is_good_until_usage_expired" class="ml-3">Good until usage expired</el-checkbox>
      </div>
    </el-form-item>
  </div>
</template>

<script>
import UpdateContractMixin from '../mixin'
import moment from 'moment'

export default {
  name: 'ServicePeriod',
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
    isShow: {
      type: Boolean,
      default: false,
    },
    disabledEdit: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      pickerOptionsEnd: {
        disabledDate: (time) => {
          return time.getTime() < moment(this.form.start_time)
            .add(1, 'day')
        },
      },
    }
  },
}
</script>

<style scoped>
::v-deep .el-date-editor.el-input {
  width: 200px;
}
</style>
