<template>
  <el-card class="box-card">
    <div slot="header">Service Module 服务范围</div>

    <slot v-if="!isProjectBase">
      <el-card class="box-card" shadow="never" :class="{'no-card-body': !form.consultation_service}">
        <div slot="header">
          <el-checkbox v-model="form.consultation_service">Consultation Service 访谈服务</el-checkbox>
        </div>
        <consultation-service v-if="form.consultation_service" :form="form" />
      </el-card>
      <el-card class="box-card" shadow="never" :class="{'no-card-body': !form.conference_service}">
        <div slot="header">
          <el-checkbox v-model="form.conference_service">Conference Service 会议服务</el-checkbox>
        </div>
        <div v-if="form.conference_service">
          <div>
            <el-radio label="1">Standard service</el-radio>
            <p>On-line and Offline conference services are available with the charge rate in Capvision Hours to be
              listed on a per conference basis.</p>
          </div>
          <div>
            <el-radio label="2">Monthly service</el-radio>
            <div style="width:200px;margin-top: 10px;">
              <el-input>
                <template slot="append">CapH</template>
              </el-input>
            </div>
          </div>
        </div>
      </el-card>
    </slot>
    <slot v-if="isPrepay">
      <el-card class="box-card" shadow="never" :class="{'no-card-body': !form.non_customized_report}">
        <div slot="header">
          <el-checkbox v-model="form.non_customized_report">Non-Customized Report 凯盛自主研报服务</el-checkbox>
        </div>
        <div v-if="form.non_customized_report">Customized, ad hoc and subscription-based research reports are available
          from the Service Provider. Reports
          will be charged to the Client in Capvision Hours or in other fees on a case by case basis agreed to in writing
          by both Parties.
        </div>
      </el-card>
    </slot>
    <slot v-if="!isTrial">
      <el-card class="box-card mb-0" shadow="never" :class="{'no-card-body': !form.customized_service}">
        <div slot="header">
          <el-checkbox v-model="form.customized_service">Customized Service 定制化服务</el-checkbox>
        </div>
        <div v-if="form.customized_service">The Service Provider offers numerous other Services including data research,
          due diligence, industry
          research and consulting. If the customer is interested in these Services, the price, delivery cycle and format
          shall be confirmed and agreed to in writing by both Parties.
        </div>
      </el-card>
    </slot>
  </el-card>
</template>

<script>
import UpdateContractMixin from '../mixin'
import ConsultationService from './ConsultationService'

export default {
  name: 'ServiceModule',
  components: { ConsultationService },
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
  },
}
</script>

<style scoped>

</style>
