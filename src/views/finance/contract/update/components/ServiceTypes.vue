<template>
  <div>
    <consultation-service v-if="!isProjectBase && !isSurveyOnly" :form="form" :is-show="isShow" />
    <el-form-item label="Service Types" prop="service_types">
      <slot v-if="!isProjectBase">
        <el-checkbox-group v-model="form.service_types" :disabled="isShow">
          <el-checkbox label="CONSULTATION">Consultation</el-checkbox>
          <el-checkbox label="SURVEY">Survey</el-checkbox>
          <!--          <el-checkbox label="CONFERENCE">Conference</el-checkbox>-->
          <!--          <el-checkbox label="RESEARCH_SERVICE">Research Service</el-checkbox>-->
        </el-checkbox-group>
      </slot>
      <slot v-if="isProjectBase">
        <el-checkbox-group v-model="form.service_types" disabled>
          <el-checkbox label="RESEARCH_SERVICE_CUSTOMIZED">Customized Research Service</el-checkbox>
        </el-checkbox-group>
      </slot>
    </el-form-item>
  </div>
</template>

<script>
import UpdateContractMixin from '../mixin'
import ConsultationService from './ConsultationService'

export default {
  name: 'ServiceTypes',
  components: { ConsultationService },
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
    isShow: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style scoped>

</style>
