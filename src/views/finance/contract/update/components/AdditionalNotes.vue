<template>
  <div>
      <el-form-item label="- Invoicing -" />
      <el-form-item label-width="180px" label="Billing Contact" prop="billing_contact_ids">
        <el-select v-model="form.billing_contact_ids" multiple>
          <el-option v-for="item in options.billing_contacts" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <create-client-user
          v-if="form.client_id"
          allow-change-status
          contact-type="BILLING"
          class="inline-block ml-5px"
          :client-id="form.client_id"
          @update="val => pushNewContact(val)" />
        <span v-if="isShowNotice" class="warning">There is no client contact of billing type.</span>
      </el-form-item>
      <el-form-item label="Invoicing Instructions" label-width="180px" prop="invoicing_instructions">
        <tinymce v-model="form.invoicing_instructions" :height="100" :toolbar="toolbar" />
      </el-form-item>
    <el-divider />
    <el-form-item label="Custom Notes">
      <el-input v-model="form.custom_note" type="textarea" :rows="3" />
    </el-form-item>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import API from '@/api/client'
import UpdateContractMixin from '../mixin'
import CreateClientUser from '@/views/project/components/CreateClientUser'

export default {
  name: 'AdditionalNotes',
  components: { Tinymce, CreateClientUser },
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
  },
  data() {
    return {
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      options: {
        billing_contacts: [],
      },
    }
  },
  computed: {
    isShowNotice() {
      return this.form.client_id && !this.options.billing_contacts.length
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.getBillingContact()
    })
  },
  methods: {
    getBillingContact() {
      if (this.form.client_id) {
        return API.getContactList(this.form.client_id, { type: 'BILLING' }).then(data => {
          this.options.billing_contacts = data.list
        })
      }
    },
    handleClientChange() {
      this.form.billing_contact_id = ''
      if (this.form.client_id) {
        this.getBillingContact().then(() => {
          if (this.options.billing_contacts.length) {
            this.form.billing_contact_ids = [this.options.billing_contacts[0].id]
          }
        })
      }
    },

    pushNewContact(val) {
      this.options.billing_contacts.push(val)
      this.form.billing_contact_ids.push(val.id)
    },
  },
}
</script>

<style scoped>

</style>
