<template>
  <div>
    <el-form-item label="Payment Terms" prop="payment_term">
      <div v-if="form.payment_term">
        <el-table v-if="!isPayGo" :data="form.payment_term">
          <el-table-column
            type="index"
            width="80" />
          <el-table-column
            label="Amount"
          >
            <template slot-scope="scope">
              <el-input
                v-if="!isShow"
                v-model.number="scope.row.amount"
                :disabled="checkDisable(scope.$index)"
                class="w-200px"
                type="number"
                @input="computeCount()">
                <template slot="append">{{form.currency}}</template>
              </el-input>
              <span v-if="isShow">{{ scope.row.amount }} / {{form.currency}}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="isShow"
            label="Invoice ID">
            <template slot-scope="scope">
              {{ scope.row.invoice_id }}
            </template>
          </el-table-column>
          <el-table-column label="Invoice Date">
            <template slot-scope="scope">
              <div class="flex">
                <el-date-picker
                  v-if="!isShow"
                  v-model="scope.row.invoice_date"
                  format="dd-MM-yyyy"
                  placeholder="DD-MM-YYYY"
                  value-format="yyyy-MM-dd"
                />
              </div>
              <span v-if="isShow">{{ scope.row.invoice_date| momentFormat() }}</span>

            </template>
          </el-table-column>
          <el-table-column label="Payment Terms">
            <template slot-scope="scope">
              <el-input v-if="!isShow" v-model="scope.row.days" class="w-130px" type="number">
                <template slot="append">Day</template>
              </el-input>
              <div v-if="isShow">
                <el-popover
                  v-model="scope.row.popoverVisible"
                  placement="top"
                  width="100">
                  <div class="mt-8">
                    <el-input-number v-model="payment_term.days" :step="1" :min="0" />
                    <el-button type="text" size="mini" class="ml-3" :loading="loading" @click="updatePaymentTerm(scope.row)">Save</el-button>
                    <el-button type="text" size="mini" class="info" @click="scope.row.popoverVisible=false">Cancel</el-button>
                  </div>
                  <el-button slot="reference" type="text" :disabled="!editPermission" @click="initPaymentTerm(scope.row)">{{ scope.row.days }} Day</el-button>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="!isShow" label="">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" icon="el-icon-plus" class="mr-8" @click="addTerm()" />
              <el-link
                v-if="length > 1"
                :underline="false"
                type="danger"
                icon="el-icon-delete"
                @click="removeTerm(scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column v-if="isShow && form.approval_status === 'APPROVED'" label="Action">
            <template slot-scope="scope">
              <el-link
                v-if="scope.row.id === noInvoiceTermID && isPrepay"
                :underline="false"
                type="primary"
                icon="el-icon-plus"
                @click="createInvoice(scope.row)">
                create invoice
              </el-link>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-if="isPayGo" :data="form.payment_term">
          <el-table-column type="index" width="80" />
          <el-table-column label="Payment Rule">
            <template slot-scope="scope">
              <el-select v-if="!isShow" v-model="scope.row.period">
                <el-option
                  v-for="(item, index) in payment_period_options"
                  :key="index"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
              <span v-if="isShow">{{ scope.row.period | getLabel(payment_period_options) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Payment Term">
            <template slot-scope="scope">
              <el-input v-if="!isShow" v-model="scope.row.days" class="w-130px" type="number">
                <template slot="append">Day</template>
              </el-input>
              <div v-if="isShow">
                <el-popover
                  v-model="scope.row.popoverVisible"
                  placement="top"
                  width="100">
                  <div class="mt-8">
                    <el-input-number v-model="payment_term.days" :step="1" :min="0" />
                    <el-button type="text" size="mini" class="ml-3" :loading="loading" @click="updatePaymentTerm(scope.row)">Save</el-button>
                    <el-button type="text" size="mini" class="info" @click="scope.row.popoverVisible=false">Cancel</el-button>
                  </div>
                  <el-button slot="reference" type="text" :disabled="!editPermission" @click="initPaymentTerm(scope.row)">{{ scope.row.days }} Day</el-button>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="isShow && form.approval_status === 'APPROVED'" label="Action" />
          <el-table-column v-if="!isShow" label="">
            <template slot-scope="scope">
              <el-link :underline="false" type="primary" icon="el-icon-plus" class="mr-8" @click="addTerm()" />
              <el-link
                v-if="length > 1"
                :underline="false"
                type="danger"
                icon="el-icon-delete"
                @click="removeTerm(scope.$index)" />
            </template>
          </el-table-column>
        </el-table>
      </div>

    </el-form-item>
    <el-divider />
  </div>
</template>

<script>
import API from '@/api/contract'
import moment from 'moment'
import UpdateContractMixin from '../mixin'
import { mapState } from 'vuex'

export default {
  name: 'PaymentTerms',
  mixins: [UpdateContractMixin],
  props: {
    form: Object,
    isShow: {
      type: Boolean,
      default: false,
    },
    isSGDB: {
      type: Boolean,
      default: false,
    },
    editPermission: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,
      popoverVisible: false,
      payment_term: {},
      payment_period_options: [
        { value: 'MONTH', label: 'Month' },
        { value: 'QUARTER', label: 'Quarter' },
        { value: 'HALF_YEAR', label: 'Half Year' },
        { value: 'PROJECT', label: 'Project' },
      ],
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    total() {
      let result = null
      if (this.form.charge_hours > 0 && this.form.unit_price_china) {
        result = this.form.charge_hours * this.form.unit_price_china
      } else {
        result = this.current_count
      }
      return result
    },

    current_count() {
      let count = null
      this.form.payment_term.map(item => {
        count += +item.amount
      })
      return count
    },

    length() {
      return this.form.payment_term.length
    },

    noInvoiceTermID() {
      const term = this.form.payment_term.find(item => !item.invoice_id)
      return term ? term.id : undefined
    },
  },

  watch: {
    'form.charge_hours': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.form.payment_term && this.form.unit_price_china) this.computeCount()
      },
    },
    'form.unit_price_china': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.form.payment_term) this.computeCount()
      },
    },
    'form.contract_size': {
      handler(newVal, oldVal) {
        if (newVal && this.form.payway === 'PROJECT_BASE') {
          this.form.payment_term[0].amount = this.form.contract_size
        }
      },
    },

  },

  mounted() {
    if (this.isShow) return
    if (this.form.payment_term && this.form.payment_term.length) {
      this.form.payment_term[0].invoice_client_id = this.form.client_id
    } else {
      this.form.payment_term = [
        {
          order: 1,
          invoice_client_id: this.form.client_id,
          days: '30',
          amount: '',
          invoice_date: moment.tz()
            .format('YYYY-MM-DD'),
          period: 'MONTH',
        }]
    }
  },

  methods: {
    addTerm() {
      const item = {
        order: this.length + 1,
        invoice_client_id: this.form.client_id,
        days: '30',
        amount: '',
        invoice_date: '',
        period: 'MONTH',
      }
      if (this.length) {
        this.form.payment_term.splice(this.length - 1, 0, item)
      } else {
        item.amount = this.total
        this.form.payment_term.push(item)
      }
    },
    // payment 总额=contract_size 剩余默认金额 不允许<=0
    computeCount() {
      if (!this.length) return
      let current_count = 0
      const list = this.form.payment_term.filter((item, index) => (this.length - 1 !== index))
      list.forEach(item => {
        current_count += +item.amount
      })
      const remain_amount = this.total - current_count
      if (remain_amount < 1 && this.total) {
        this.form.payment_term.splice(this.length - 2, 1)
      } else {
        this.form.payment_term[this.length - 1].amount = remain_amount
      }
    },

    initPaymentTerm(row) {
      this.payment_term = Object.assign({}, row)
    },

    updatePaymentTerm(row) {
      this.loading = true
      const params = {
        payway: this.payment_term.payway,
        period: this.payment_term.period,
        days: this.payment_term.days,
      }
      API.updatePaymentTerm(this.payment_term.id, params)
        .then(() => {
          row.days = params.days
          this.$message({
            message: 'Save successful',
            type: 'success',
          })
          row.popoverVisible = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    removeTerm(index) {
      this.form.payment_term.splice(index, 1)
      this.form.payment_term.forEach((item, index) => {
        item.order = index + 1
      })
      this.computeCount()
    },

    checkDisable(index) {
      return this.form.charge_hours > 0 && index === this.length - 1 && this.length > 1
    },

    createInvoice(term) {
      const data = {
        client_id: this.form.client_id,
        contract_id: term.contract_id,
        contract_payment_item_id: term.id,
        zone_id: this.timeZone,
      }
      this.loading = true
      return API.createInvoice(data)
        .then(res => {
          term.invoice_id = res.id
          this.$emit('invoiced')
          this.$message({
            message: 'Create invoice successfully',
            type: 'success',
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.w-130px {
  width: 130px;
}

.w-50 {
  width: 50%;
}

.add-button {
  margin-top: 10px;
}

.custom-count {
  color: #5a5e66;
}
</style>
