<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" class="update-form" label-width="180px" :disabled="submitting">

      <el-form-item label="Contract Name" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="Client" prop="client_id">
        <client-search
          v-model="form.client_id"
          :client-name.sync="form.client_name"
          class="w-200px"
          @change="handleClientChange" />
      </el-form-item>
      <el-form-item label="Pay Way" prop="payway">
        <el-radio-group v-model="form.payway">
          <el-radio
            v-for="item in contract_options.payWay"
            :key="item.value"
            :label="item.value"
            @change="payWayChange()">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <service-period v-show="!isProjectBase" :form="form" :disabled-edit="applyDialogEdit" />
      <service :form="form" />
      <el-divider />
      <service-types :form.sync="form" />
      <el-divider />

      <payment-terms v-if="!isTrial" :form.sync="form" :is-s-g-d-b="isSGDB" />
      <additional-notes ref="additional-notes" :form="form" class="mb-8" />

      <div v-if="!applyDialogEdit">
        <el-form-item v-if="!isEdit">
          <el-button type="success" @click="saveRequest()">Save & Request</el-button>
        </el-form-item>
        <el-form-item v-if="isEdit">
          <el-button @click="$emit('save')">Cancel</el-button>
          <el-button v-if="data.approval_status === 'DRAFT_CREATED'" type="success" @click="onSave()">Save</el-button>
          <el-button v-else type="success" @click="saveRequest()">Save & Request</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
import { mapGetters, mapState } from 'vuex'
import API from '@/api/contract'
import Filters from '@/utils/filters'
import FormMixin from '@/components/FormPage/mixin'
import Service from './components/Service'
import ServicePeriod from './components/ServicePeriod'
import ServiceTypes from './components/ServiceTypes'
import AdditionalNotes from './components/AdditionalNotes'
import PaymentTerms from './components/PaymentTerms'
import UpdateContractMixin from './mixin'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'

export default {
  name: 'UpdateContract',
  components: {
    ClientSearch,
    Service,
    ServiceTypes,
    ServicePeriod,
    PaymentTerms,
    AdditionalNotes,
  },
  mixins: [FormMixin, UpdateContractMixin],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: null,
    },
    applyDialogEdit: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    const validateDate = (rule, value, callback) => {
      const t1 = moment(this.form.end_time)
      const t2 = moment(this.form.start_time)
      if (t1.diff(t2) < 0) {
        callback(new Error('End Date cannot be earlier than Start Date.'))
      } else {
        callback()
      }
    }
    const validateUsPrice = (rule, value, callback) => {
      if (value < 1) {
        callback(new Error('Please enter the correct unit price.'))
      } else {
        callback()
      }
    }
    const validateChinaPrice = (rule, value, callback) => {
      if (value < 1) {
        callback(new Error('Please enter the correct unit price.'))
      } else {
        callback()
      }
    }
    return {
      sent_request: false,
      file_maxSize: 20,
      form: this.initForm(),
      rules: {
        client_id: [{ required: true, trigger: 'change' }],
        name: [{ required: true, trigger: 'change' }],
        payway: [{ required: true, trigger: 'change' }],
        start_time: [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        end_time: [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        unit_price_us: [{ required: true, trigger: 'change' }, { validator: validateUsPrice, trigger: 'change' }],
        unit_price_china: [{ required: true, trigger: 'change' }, { validator: validateChinaPrice, trigger: 'change' }],
        charge_hours: [{ required: true, trigger: 'change' }],
        currency: [{ required: true, trigger: 'change' }],
        contract_size: [{ required: true, trigger: 'change' }],
        service_types: [{ required: true, trigger: 'change' }],
        billing_contact_ids: [{ required: true, trigger: 'change', message: 'Billing Contact is required' }],
        invoicing_instructions: [{ required: this.applyDialogEdit, trigger: 'change', message: 'Invoicing Instructions is required' }],
      },
    }
  },

  computed: {
    ...mapGetters([
      'uid',
      'contract_options',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
    }),
    autoContractName() {
      if (!this.form.payway) return false
      const { form, contract_options } = this
      const payWay = contract_options.payWay.find(item => item.value === form.payway).label
      const start_date = moment(form.start_time).format('MM/DD/YYYY')
      const last_world = form.payway === 'PREPAY' ? `- ${form.charge_hours}` : ''
      return `${form.client_name} - ${payWay} - ${start_date} ${last_world}`
    },
  },
  watch: {
    'autoContractName': {
      handler(newVal) {
        if (newVal) {
          this.form.name = newVal
        }
      },
    },
    'form.unit_price_us': {
      handler(newVal) {
        if (newVal) {
          this.form.term_non_china_senior = this.formatSenior(newVal)
        }
      },
    },
    'form.unit_price_china': {
      handler(newVal) {
        if (newVal) {
          this.form.term_china_senior = this.formatSenior(newVal)
        }
      },
    },
  },

  mounted() {
    this.handleIsEdit()
  },

  methods: {
    initForm() {
      return {
        creator_uid: this.uid,
        client_id: '',
        client_name: '',
        approver_id: '',
        name: '',
        payway: 'TRIAL',
        start_time: moment.tz()
          .format('YYYY-MM-DD'),
        end_time: moment.tz()
          .add(1, 'years')
          .subtract(1, 'day')
          .format('YYYY-MM-DD'),
        is_good_until_usage_expired: false,
        approval_status: 'DRAFT',
        unit_price_us: '',
        unit_price_china: '',
        charge_hours: 0,
        contract_size: 0,
        currency: 'USD',
        client_address: '',
        service_types: ['CONSULTATION', 'SURVEY'],
        term_client_hours: '1(0,60];1.5(60,90];2(90,120];0.5<120,30>',
        term_china_senior: '1(0,60];1.5(60,90];2(90,120];0.5<120,30>',
        term_non_china_senior: '1(0,60];1.5(60,90];2(90,120];0.5<120,30>',
        term_overseas: '+0.5h/H',
        term_transcription: '+0.5h/H',
        term_translation: '+0.5h/H',
        term_in_person: '+0.5h',
        invoicing_instructions: '',
        custom_note: '',
        billing_contact_ids: [],
        materials: [],
        payment_term: [
          {
            order: 1,
            invoice_client_id: '',
            days: '30',
            amount: '',
            invoice_date: moment.tz()
              .format('YYYY-MM-DD'),
            period: 'MONTH',
          },
        ],
      }
    },

    payWayChange() {
      this.form.start_time = moment()
        .format('YYYY-MM-DD')
      this.form.end_time = moment()
        .add(1, 'years')
        .subtract(1, 'day')
        .format('YYYY-MM-DD')
      this.form.service_types = ['CONSULTATION', 'SURVEY']
      switch (this.form.payway) {
        case 'TRIAL':
          this.form.charge_hours = 0
          this.form.payment_term = []
          break
        case 'PAYGO':
          this.form.charge_hours = 0
          break
        case 'PREPAY':
          this.form.charge_hours = 10
          break
        case 'PROJECT_BASE':
          this.form.unit_price_china = 0
          this.form.unit_price_us = 0
          this.form.charge_hours = 0
          this.form.start_time = new Date()
          this.form.end_time = new Date('2030-01-01')
          this.form.service_types = ['RESEARCH_SERVICE_CUSTOMIZED']
          break
        default:
          break
      }
    },

    handleIsEdit() {
      if (this.isEdit && this.data) {
        for (const key in this.form) {
          this.form[key] = this.data[key]
        }
        this.form.start_time = Filters.momentFormat(this.form.start_time, 'YYYY-MM-DD')
        this.form.end_time = Filters.momentFormat(this.form.end_time, 'YYYY-MM-DD')
        this.form.id = +this.data.id
      }
      if (this.$route.query.client_id) {
        this.form.client_id = +this.$route.query.client_id
        this.form.client_name = this.$route.query.name
      }
      this.rules.billing_contact_ids = [{ required: this.applyDialogEdit && !this.isSGDB, trigger: 'change', message: 'Billing Contact is required' }]
    },

    handleClientChange() {
      this.$refs['additional-notes'].handleClientChange()
    },

    saveRequest() {
      this.sent_request = true
      this.onSave()
    },

    saveContract(form) {
      const params = Object.assign({}, form)
      //  end_time 设置为当天0点
      if (form.payway === 'PROJECT_BASE') {
        params.start_time = new Date('1971-01-01')
        params.end_time = new Date('2030-01-01')
      } else {
        params.start_time = moment.tz(params.start_time, this.timeZone).toISOString()
        params.end_time = moment.tz(params.end_time, this.timeZone).toISOString()
        // params.start_time = contractMomentFormat(params.start_time, 'start')
        // params.end_time = contractMomentFormat(params.end_time, 'start')
      }

      params.applicable_project_types = params.service_types.join()

      if (this.isEdit) {
        return API.updateContract(params)
          .then((data) => {
            this.checkRequest(data.id)
            this.$message({
              message: 'success',
              type: 'success',
            })
            this.$emit('save', params)
          }, () => {
          })
      } else {
        return API.createContract(params)
          .then((data) => {
            this.checkRequest(data.id)
            this.$message({
              message: 'success',
              type: 'success',
            })
          }, () => {
          }).finally(
            () => {
              this.$route.query.client_id
                ? this.$router.push({
                  name: 'ClientDetail',
                  params: { client_id: this.$route.query.client_id },
                  query: { tab: 'contract' },
                }) : this.$router.back()
            },
          )
      }
    },

    handleSave(form) {
      if (form.service_types.join() === 'SURVEY') {
        return this.$confirm('This contract can only be used for \'Survey\' type projects', 'Notice', {
          type: 'warning',
        }).then(() => {
          form.term_client_hours = null
          form.term_china_senior = null
          form.term_non_china_senior = null
          form.term_overseas = null
          form.term_transcription = null
          form.term_translation = null
          form.term_in_person = null
          return this.saveContract(form)
        })
      } else {
        return this.saveContract(form)
      }
    },

    checkRequest(contract_id) {
      if (this.sent_request) {
        return API.requestContract(contract_id)
          .then(() => {
          }, () => {
          })
      }
      this.sent_request = false
    },

    formatSenior(unit_price) {
      const item = Math.round(unit_price * 0.35)
      return `1(0,${item}];1.5(${item},${item * 2}];2(${item * 2},${item * 3}];0.5<${item * 3},${item}>`
    },

  },
}
</script>

<style scoped lang="scss">
::v-deep .box-card {
  margin-bottom: 1em;

  &.mb-0 {
    margin-bottom: 0;
  }

  &.no-card-body .el-card__body {
    padding: 0;
  }
}
</style>
