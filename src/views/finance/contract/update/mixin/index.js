export default {
  computed: {
    isTrial() {
      return this.form.payway === 'TRIAL'
    },
    isPrepay() {
      return this.form.payway === 'PREPAY'
    },
    isPayGo() {
      return this.form.payway === 'PAYGO'
    },
    isProjectBase() {
      return this.form.payway === 'PROJECT_BASE'
    },
    // service type  只包含survey 时，不需要单价 和 详细收费规则
    isSurveyOnly() {
      return this.form.service_types && this.form.service_types.join() === 'SURVEY'
    },
  },
}
