<template>
  <div v-loading="loading" class="components-container">
    <h2 v-if="!applyContractId"># <span class="success">{{ data.id }}</span> {{ data.name }}</h2>
    <el-form v-if="!isEdit" ref="form" :model="data" class="update-form" label-width="180px" :disabled="loading">
      <el-form-item label="Contract Name" prop="name">
        {{ data.name }}
      </el-form-item>
      <el-form-item label="Client" prop="client_id">
        {{ data.client_name }}
      </el-form-item>
      <el-form-item label="Pay Way" prop="pay_way">
        {{ data['payway'] | getLabel(contract_options.payWay) }}
      </el-form-item>
      <service-period :form="data" is-show />
      <div v-if="data['payway'] !== 'PROJECT_BASE'">
        <el-form-item :label="UnitPriceLabel" prop="unit_price_us">
          {{ data['unit_price_us'] }}
        </el-form-item>
        <el-form-item label="Unit Price (China)" prop="unit_price_china">
          {{ data['unit_price_china'] }}
        </el-form-item>
      </div>
      <el-form-item v-else label="Contract Size" prop="contract_size">
        {{ data['contract_size'] }}
      </el-form-item>
      <el-form-item label="Currency" prop="currency">
        {{ data['currency'] }}
      </el-form-item>
      <el-form-item label="Client Address" prop="client_address">
        {{ data['client_address'] }}
      </el-form-item>
      <div v-if="data['charge_hours'] > 0">
        <el-form-item label="Charge Hours" prop="charge_hours">
          {{ data['charge_hours'] }}
        </el-form-item>
        <el-form-item v-if="data.used_hours" label="Used Hours" prop="used_hours">
          {{ data['used_hours'] }}
        </el-form-item>
      </div>

      <el-divider />
      <service-types :form="data" is-show />
      <div v-if="!applyContractId">
        <el-divider />
        <payment-terms v-if="data['payway'] !== 'TRIAL'" :form="data" :edit-permission="editPaymentTermPermission" is-show :is-s-g-d-b="isSGDB" @invoiced="getInvoices" />
        <invoices v-if="data['payway'] !== 'TRIAL'" :form="data" />
      </div>
      <el-form-item label="Billing Contact" prop="billing_contacts">
        <div v-for="item in data.billing_contacts" :key="item.id">
          {{ item.name }}
          <div v-if="item.email_obj && item.email_obj.id" class="inline-block ml-8">
            <span>{{item.email_obj.value}}</span>
          <el-link
            :disabled="item.emailLoading"
            :underline="false"
            type="primary"
            class="hover-visible ml-8"
            @click="getContactInfo(item)">
            <el-icon name="view" />
          </el-link>
          </div>
        </div>
        <edit-contract-item type-key="billing_contact_ids" :data.sync="data" />
      </el-form-item>
      <el-form-item label="Invoicing Instructions">
        <div class="flex">
        <div class="outermost-p-td-0" v-html="$sanitizeHtml(data.invoicing_instructions)" />
        <edit-contract-item type-key="invoicing_instructions" :data.sync="data" />
        </div>
      </el-form-item>
      <el-form-item label="Custom Notes">
        {{ data.custom_note }}
      </el-form-item>
      <el-form-item label="Related Files">
        <div v-for="(item, index) in data.materials" :key="index">
          <el-link :underline="false" :href="item.file_url" type="primary" target="_blank">{{ item.file_name }}
          </el-link>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button v-if="checkEdit()" icon="el-icon-edit-outline" @click="isEdit = true">Edit</el-button>
        <el-button v-if="!applyContractId" icon="el-icon-back" @click="goBack">Back</el-button>
      </el-form-item>
    </el-form>

    <edit-page v-else is-edit :data="data" @save="updateContract" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import ServicePeriod from '../update/components/ServicePeriod'
import ServiceTypes from '../update/components/ServiceTypes'
import PaymentTerms from '../update/components/PaymentTerms'
import Invoices from '../update/components/Invoices'
import EditPage from '../update/index'
import EditContractItem from '@/views/finance/contract/components/EditContractItem'
import API from '@/api/contract'
import { RouteTools } from '@/utils/tool'
import ToolAPI from '@/api/tool'

export default {
  name: 'ContractDetail',
  components: {
    ServicePeriod,
    ServiceTypes,
    EditPage,
    PaymentTerms,
    Invoices,
    EditContractItem,
  },
  props: {
    // 用于apply 显示详情
    applyContractId: [Number, String],
  },
  data() {
    return {
      id: this.$route.params.id || this.applyContractId,
      loading: false,
      emailLoading: false,
      data: {},
      isEdit: false,
      editPaymentTermPermission: false,
    }
  },

  computed: {
    ...mapGetters([
      'contract_options',
      'uid',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    UnitPriceLabel() {
      return this.isSGDB ? 'Unit Price (SG)' : 'Unit Price (US)'
    },

    isAdmin() {
      return this.roles.some(item => ['Admin'].includes(item.role.name))
    },
  },

  created() {
    this.getDetail()
  },

  methods: {
    getDetail() {
      this.loading = true
      const params = {
        'extra': [
          'client_name',
          'client.account_managers',
          'payment_term',
          'materials',
          'invoices.client',
          'invoices.contract',
          'used_hours',
          'billing_contacts.contact_infos'].join() }

      return API.getContractDetail(this.id, params)
        .then(data => {
          data.billing_contacts.forEach(item => {
            item.emailLoading = false
            item.email_obj = item.contact_infos?.find(item => item.type === 'EMAIL') || {}
          })
          this.data = data
          this.editPaymentTermPermission = data.client['account_managers'].findIndex(item => item.uid === this.uid) > -1 || this.isAdmin
          this.data.service_types = this.data.applicable_project_types.split(',')
          if (this.$route.params.is_update) {
            this.isEdit = true
          }

          return RouteTools.setRouterMetaTitle(data.name, this.$route)
        })
        .finally(() => {
          this.loading = false
        })
    },

    getInvoices() {
      this.loading = true
      const params = { 'extra': 'invoices,client,contract' }

      return API.getContractDetail(this.id, params)
        .then(data => {
          this.data.invoices = data.invoices
        })
        .finally(() => {
          this.loading = false
        })
    },

    updateContract(data) {
      this.isEdit = false
      Object.assign(this.data, data)
    },

    getContactInfo(contact) {
      contact.emailLoading = true
      return ToolAPI.getContactInfo({ ids: contact.email_obj.id })
        .then(data => {
          contact.email_obj.value = data.list[0].value
        })
        .finally(() => {
          contact.emailLoading = false
        })
    },

    checkEdit() {
      if (this.$route.params.from_comp) {
        return false
      }
      return !['DRAFT_REQUESTED', 'DRAFT_CREATED', 'PENDING_APPROVAL', 'APPROVED'].includes(this.data.approval_status)
    },

    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .box-card {
  margin-bottom: 1em;

  &.mb-0 {
    margin-bottom: 0;
  }

  &.no-card-body .el-card__body {
    padding: 0;
  }
}

</style>
