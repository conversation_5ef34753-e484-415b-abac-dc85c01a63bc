<template>
  <div class="app-container">
    <slot v-if="isCurrentRoute">
      <contract-list :is-current-route="isCurrentRoute" />
    </slot>

    <router-view />
  </div>
</template>

<script>
import ContractList from './components/ContractList'

export default {
  name: 'Contract',
  components: { ContractList },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'Contract'
    },
  },
}
</script>

<style scoped>
</style>
