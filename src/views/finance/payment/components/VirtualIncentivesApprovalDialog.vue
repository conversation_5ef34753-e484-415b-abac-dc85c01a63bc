<template>
  <el-dialog
    title="Virtual Incentives Payment Items Approval"
    :visible.sync="dialogVisible"
  >
    <el-table
      border
      stripe
      :data="tableData"
    >
      <el-table-column label="Task ID" prop="task_id" />
      <el-table-column label="Sku" prop="sku" />
      <el-table-column label="Brand Name" prop="brand_name" />
      <el-table-column label="Original Amount">
        <template v-slot="{row}">
          {{ row.original_amount }} {{ row.original_currency }}
        </template>
      </el-table-column>
      <el-table-column label="Amount">
        <template v-slot="{row}">
          {{ row.amount }} {{ row.currency }}
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button type="danger" icon="el-icon-close" :disabled="submitting" @click="actionUpdateApproval('REJECT')">Reject</el-button>
      <el-button type="primary" icon="el-icon-check" :disabled="submitting" @click="actionUpdateApproval('APPROVE')">Approve</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from '@/api/payment'

export default {
  name: 'VirtualIncentivesApprovalDialog',
  props: {
    items: Array,
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      tableData: [],
    }
  },
  methods: {
    openDialog(data) {
      this.tableData = data
      this.dialogVisible = true
    },
    actionUpdateApproval(event_type) {
      this.$confirm(`Confirm to ${event_type}?`, 'Notice', {
        confirmButtonText: event_type,
        confirmButtonClass: event_type === 'REJECT' ? 'el-button--danger' : 'el-button--primary',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }).then(() => {
        this.submitting = true
        return API.updateVirtualIncentivesApproval({
          payment_item_ids: this.items.map(item => item.id),
          event_type,
        }).then(() => {
          this.$message.success(event_type + ' Success')
          this.dialogVisible = false
          this.$emit('done')
        }).finally(() => {
          this.submitting = false
        })
      }, () => {})
    },
  },
}
</script>

<style scoped lang="scss">
</style>
