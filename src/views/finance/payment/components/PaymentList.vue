<template>
  <el-table
    ref="paymentTable"
    border
    stripe
    :data="tableData"
    :row-class-name="$options.filters.rowSetIndex"
    @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['paymentTable'], checkSelectable)"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="40" :selectable="checkSelectable" />
    <el-table-column label="ID" width="60">
      <template slot-scope="{row}">{{ row.id }}</template>
    </el-table-column>
    <el-table-column label="Task ID" width="70">
      <template slot-scope="{row}">{{ row.task_id }}</template>
    </el-table-column>
    <el-table-column label="Task Ts ID" width="80">
      <template slot-scope="{row}">
        <slot v-if="row.task">{{ row.task.tsid }}</slot>
      </template>
    </el-table-column>
    <el-table-column label="CN Task ID" width="70">
      <template slot-scope="{row}">{{ row.cn_task_id }}</template>
    </el-table-column>
    <el-table-column label="Project" width="100">
      <template slot-scope="scope">
        <router-link-project v-if="scope.row.task && !scope.row.is_cn_project" :data="scope.row.task.project" />
        <span v-if="['CN_TASK_US_ADVISOR'].includes(scope.row.collaboration_type)">
          <el-link
            type="primary text-truncate"
            :underline="false"
            @click="openToCNProject(scope.row)">
            <el-tag
              class="tag-lead"
              type="info"
              effect="dark"
              size="mini"
            >C</el-tag>
            {{ scope.row.task_info_snapshot.project_name }}
          </el-link>
        </span>
      </template>
    </el-table-column>
    <el-table-column label="Advisor" width="100">
      <template v-if="scope.row.advisor" slot-scope="scope">

        <router-link
          class="text-truncate link"
          :title="scope.row.advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.advisor.id,}}"
        >
          <el-tag
            v-if="['US_TASK_CN_ADVISOR'].includes(scope.row.collaboration_type)"
            class="tag-lead"
            type="info"
            effect="dark"
            size="mini"
          >C
          </el-tag>
          {{ scope.row.advisor.name_prefix || ''}} {{scope.row.advisor['full_name']}}
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Recommended Advisor" width="100">
      <template v-if="scope.row.referral" slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.referral.to_advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.referral.to_advisor.id,}}"
        >
          {{ scope.row.referral.to_advisor['name_prefix'] || ''}}
          {{ scope.row.referral.to_advisor['full_name'] }}
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Task Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.task_date | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="KM" width="100">
      <template slot-scope="scope">
        <span v-if="scope.row.task_info_snapshot">{{ scope.row.task_info_snapshot.task_manager_name }}</span>
      </template>
    </el-table-column>
    <el-table-column label="Minutes" width="100">
      <template slot-scope="scope">
        {{ (scope.row.hours * 60).toFixed(4) }}
      </template>
    </el-table-column>
    <el-table-column label="Rate" width="100">
      <template slot-scope="scope">
        {{ scope.row.rate }}
      </template>
    </el-table-column>
    <el-table-column label="Amount" width="100">
      <template slot-scope="scope">
        {{ scope.row.amount }}
        <el-tag v-if="scope.row.type==='REFERRAL'" size="mini">Referral</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="Currency" width="100">
      <template slot-scope="scope">
        {{ scope.row.currency }}
      </template>
    </el-table-column>
    <el-table-column label="Status">
      <template slot-scope="scope">
        {{ scope.row.status | getLabel(options) }}
      </template>
    </el-table-column>
    <el-table-column label="Paying Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.paying_at | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Paid Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.paid_at | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Pay Failed Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.pay_failed_at | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Operator" width="100">
      <template slot-scope="scope">
        {{ getOperator(scope.row) }}
      </template>
    </el-table-column>
    <el-table-column label="Physical Country" width="100">
      <template slot-scope="scope">
        <span v-if="scope.row.advisor && scope.row.advisor.location">{{ scope.row.advisor.location.name }}</span>
      </template>
    </el-table-column>
    <el-table-column label="Payment Method" width="100">
      <template slot-scope="scope">
        {{ transformValue(scope.row.pay_method_name) }}
      </template>
    </el-table-column>
    <el-table-column label="Email Address" width="100">
      <template slot-scope="scope">
        {{ scope.row.payment_detail && scope.row.payment_detail.email_address }}
      </template>
    </el-table-column>
    <el-table-column label="Account">
      <el-table-column label="Name">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.account_name }}
        </template>
      </el-table-column>
      <el-table-column label="Number" width="100">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.account_number }}
        </template>
      </el-table-column>
      <el-table-column label="Type" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.account && scope.row.account.account_type"
                class="capitalize">{{ scope.row.account.account_type.toLowerCase() }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Bank">
      <el-table-column label="Name">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.bank_name }}
        </template>
      </el-table-column>
      <el-table-column label="Area">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.bank_area }}
        </template>
      </el-table-column>
      <el-table-column label="Branch">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account['bank_branch'] }}
        </template>
      </el-table-column>
      <el-table-column label="Location">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account['bank_location'] }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Code">
      <el-table-column
        label="Cpf">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.cpf_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="IFSC">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.ifsc_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Sort">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.sort_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Swift">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.swift_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Routing Number">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.routing_number }}
        </template>
      </el-table-column>
      <el-table-column
        label="Transit Number">
        <template slot-scope="scope">
          {{ scope.row.account && scope.row.account.transit_number }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Physical Check">
      <el-table-column
        label="Check Payable To">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.check_payable_to }}
        </template>
      </el-table-column>
      <el-table-column
        label="Address 1">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.address1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="Address 2">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.address2 }}
        </template>
      </el-table-column>
      <el-table-column
        label="City">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.city }}
        </template>
      </el-table-column>
      <el-table-column
        label="State/Province/Territory">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.region }}
        </template>
      </el-table-column>
      <el-table-column
        label="Zip">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.zip }}
        </template>
      </el-table-column>
      <el-table-column
        label="Country">
        <template slot-scope="scope">
          {{ scope.row.payment_detail && scope.row.payment_detail.country }}
        </template>
      </el-table-column>
    </el-table-column>
  </el-table>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import Filters from '@/utils/filters'

export default {
  name: 'PaymentList',
  components: { RouterLinkProject },
  props: {
    tableData: Array,
    selection: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      options: [
        {
          value: 'INITIAL',
          label: 'Initial',
        }, {
          value: 'ADVISOR_CONFIRMED',
          label: 'Advisor Confirmed',
        }, {
          value: 'ADVISOR_CONFIRMING',
          label: 'Advisor Confirming',
        },
        {
          value: 'PAYING',
          label: 'Paying',
        }, {
          value: 'PAY_FAILED',
          label: 'Pay Failed',
        }, {
          value: 'PAID',
          label: 'Paid',
        }],
    }
  },

  methods: {
    checkSelectable(row) {
      // 中国专家在美国的项目 不允许改变状态 'US_TASK_CN_ADVISOR'
      return row.status !== 'PAID' && row.collaboration_type !== 'US_TASK_CN_ADVISOR'
    },

    getOperator(item) {
      let result = ''
      switch (item.status) {
        case 'PAYING':
          result = item.paying_by ? item.paying_by.name : ''
          break
        case 'PAID':
          result = item.paid_by ? item.paid_by.name : ''
          break
        case 'PAY_FAILED':
          result = item.pay_failed_by ? item.pay_failed_by.name : ''
          break
      }
      return result
    },

    handleSelectionChange(val) {
      this.$emit('update:selection', val)
    },

    openToCNProject(data) {
      if (data.us_payment && data.us_payment.cn_task_url_jit) {
        window.open(data.us_payment.cn_task_url_jit, '_blank')
      }
    },
    transformValue(i) {
      return Filters.transformKey(i.toLowerCase())
    },

  },
}
</script>

<style scoped lang="scss">
.tag-lead {
  height: auto;
  line-height: 12px;
  padding: 0 2px;
  margin-right: 2px;
  border-radius: 3px;

  &:hover:after {
    content: 'N'
  }
}
</style>
