<template>
  <el-table
    ref="paymentTable"
    border
    stripe
    :data="tableData"
    :row-class-name="$options.filters.rowSetIndex"
    @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['paymentTable'])"
    @selection-change="handleSelectionChange"
  >
    <el-table-column v-if="!selectDisabled" type="selection" width="40" fixed :selectable="checkSelectable" />
    <el-table-column label="ID" width="80">
      <template slot-scope="{row}">{{ row.id }}</template>
    </el-table-column>
    <el-table-column label="Task ID" width="75">
      <template slot-scope="{row}">{{ row.topic.task && row.topic.task.id }}</template>
    </el-table-column>
    <el-table-column label="Task Ts ID" width="80">
      <template slot-scope="{row}">{{ row.topic.task_tsid }}</template>
    </el-table-column>
    <el-table-column label="Project" width="120">
      <template slot-scope="scope">
        <router-link-project v-if="scope.row.topic && scope.row.topic.region !== 'CN'" :data="scope.row.topic.project" />
        <span v-if="scope.row.payment.region !== 'CN' && scope.row.topic.region === 'CN'">
          <el-link
            type="primary text-truncate"
            :underline="false"
            @click="openToCNProject(scope.row)">
            <el-tag
              class="tag-lead"
              type="info"
              effect="dark"
              size="mini"
            >C</el-tag>
            {{ scope.row.topic.detail_snapshot.project_name }}
          </el-link>
        </span>
      </template>
    </el-table-column>
    <el-table-column label="Advisor" width="120">
      <template v-if="scope.row.payment.advisor" slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.payment.advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.payment.advisor.id,}}"
        >
          <el-tag
            v-if="scope.row.payment.region === 'CN' && scope.row.topic.region !== 'CN'"
            class="tag-lead"
            type="info"
            effect="dark"
            size="mini"
          >C
          </el-tag>
          {{ scope.row.payment.advisor.name_prefix || ''}} {{scope.row.payment.advisor['full_name']}}
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Recommended Advisor" width="110">
      <template v-if="scope.row.topic.referral" slot-scope="scope">
        <router-link
          class="text-truncate link"
          :title="scope.row.topic.referral.to_advisor['full_name']"
          :to="{name: 'ConsultantDetail',params: {advisor_id: scope.row.topic.referral.to_advisor.id,}}"
        >
          {{ scope.row.topic.referral.to_advisor['name_prefix'] || ''}}
          {{ scope.row.topic.referral.to_advisor['full_name'] }}
        </router-link>
      </template>
    </el-table-column>
    <el-table-column label="Task Date" width="80">
      <template slot-scope="scope">
        {{ scope.row.topic.task_start_time | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="KM" width="135">
      <template slot-scope="scope">
        <span v-if="scope.row.topic && scope.row.topic.detail_snapshot">
          {{ scope.row.topic.detail_snapshot.task_manager_name }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="Minutes" width="70">
      <template slot-scope="scope">
        {{ scope.row.minutes }}
      </template>
    </el-table-column>
    <el-table-column label="Rate" width="70">
      <template slot-scope="scope">
        {{ scope.row.payment.rate }}
      </template>
    </el-table-column>
    <el-table-column label="Amount" width="70">
      <template slot-scope="scope">
        {{ scope.row.amount }}
        <el-tag v-if="scope.row.topic.type==='REFERRAL'" size="mini">Referral</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="Currency" width="70">
      <template slot-scope="scope">
        {{ scope.row.currency_name }}
      </template>
    </el-table-column>
    <el-table-column label="Status" width="130">
      <template slot-scope="scope">
        <el-tag :type="scope.row.status | getName(options, 'value', 'type')">
          {{ scope.row.status | getLabel(options) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="Paying Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.latest_pay_date | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Paid Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.latest_pay_succeeded_date | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Pay Failed Date" width="100">
      <template slot-scope="scope">
        {{ scope.row.latest_pay_failed_date | momentFormat() }}
      </template>
    </el-table-column>
    <el-table-column label="Type" width="80">
      <template slot-scope="scope">
        {{ scope.row.payment.subtopic_name | getLabel(payment_type_options) }}
      </template>
    </el-table-column>
    <el-table-column label="Payment Method" width="120">
      <template slot-scope="scope">
        <slot v-if="scope.row.form">{{ transformValue(scope.row.form.pay_method_name) }}</slot>
      </template>
    </el-table-column>
    <el-table-column label="Events" width="60">
      <template slot-scope="scope">
        <el-link type="primary" :underline="false" @click="openEventsDialog(scope.row)">
          {{ scope.row.events.length }}
        </el-link>
      </template>
    </el-table-column>
    <el-table-column label="Account">
      <el-table-column label="Name">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.account_name }}
        </template>
      </el-table-column>
      <el-table-column label="Number" width="100">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.account_number }}
        </template>
      </el-table-column>
      <el-table-column label="Type" width="100">
        <template v-if="scope.row.form" slot-scope="scope">
          <span
            v-if="scope.row.form.account && scope.row.form.account.account_type"
            class="capitalize">{{ scope.row.form.account.account_type.toLowerCase() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Country" width="100">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.country }}
        </template>
      </el-table-column>
      <el-table-column label="Address 1" width="150">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.address1 }}
        </template>
      </el-table-column>
      <el-table-column label="Address 2" width="150">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.address2 }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Bank">
      <el-table-column label="Name">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.bank_name }}
        </template>
      </el-table-column>
      <el-table-column label="Area">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.bank_area }}
        </template>
      </el-table-column>
      <el-table-column label="Branch">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account['bank_branch'] }}
        </template>
      </el-table-column>
      <el-table-column label="Location">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account['bank_location'] }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Code">
      <el-table-column
        label="Cpf">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.cpf_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="IFSC">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.ifsc_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Sort">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.sort_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Swift">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.swift_code }}
        </template>
      </el-table-column>
      <el-table-column
        label="Routing Number">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.routing_number }}
        </template>
      </el-table-column>
      <el-table-column
        label="Transit Number">
        <template v-if="scope.row.form" slot-scope="scope">
          {{ scope.row.form.account && scope.row.form.account.transit_number }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="Gift Card">
      <el-table-column
        label="Brand"
        min-width="170px"
      >
        <template v-if="scope.row.form && scope.row.form.virtual_incentives_payment_info" slot-scope="scope">
          {{ scope.row.form.virtual_incentives_payment_info.brand_name }}
        </template>
      </el-table-column>
      <el-table-column
        label="Email"
        min-width="170px"
      >
        <template v-if="scope.row.form && scope.row.form.virtual_incentives_payment_info" slot-scope="scope">
          {{ scope.row.form.virtual_incentives_payment_info.email }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column v-if="isSGDB" label="Project Ts ID" width="120">
      <template v-if="row.topic && row.topic.project" slot-scope="{row}">{{ row.topic.project.tsid }}</template>
    </el-table-column>
  </el-table>
</template>

<script>
import RouterLinkProject from '@/components/RouterLink/Project'
import Filters from '@/utils/filters'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'PaymentList',
  components: { RouterLinkProject },
  props: {
    tableData: Array,
    selection: {
      type: Array,
      default: () => {
        return []
      },
    },
    selectDisabled: Boolean,
  },
  data() {
    return {
      options: [
        {
          value: 'INITIAL',
          label: 'Initial',
          type: 'info',
        }, {
          value: 'ADVISOR_CONFIRMED',
          label: 'Advisor Confirmed',
          type: 'primary',
        }, {
          value: 'ADVISOR_CONFIRMING',
          label: 'Advisor Confirming',
          type: 'warning',
        }, {
          value: 'PAYING',
          label: 'Paying',
          type: 'warning',
        }, {
          value: 'PAY_FAILED',
          label: 'Pay Failed',
          type: 'danger',
        }, {
          value: 'PAID',
          label: 'Paid',
          type: 'success',
        }, {
          value: 'REJECTED',
          label: 'Rejected',
          type: 'danger',
        }, {
          value: 'CANCELLED',
          label: 'Cancelled',
          type: 'danger',
        },
      ],
      payment_type_options: [
        {
          value: 'DEFAULT',
          label: 'Default',
        }, {
          value: 'NETWORK_LOYALTY_FEE',
          label: 'Network Loyalty fee',
        },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  methods: {
    checkSelectable(row) {
      // 中国专家在美国的项目 不允许改变状态 'US_TASK_CN_ADVISOR'
      const hasFinancePaymentPermission = this.roles.some(item => ['Finance_Payment'].includes(item.role.name))
      return (hasFinancePaymentPermission || row.status !== 'PAID') && !(row.payment.region !== 'CN' && row.topic.region === 'CN')
    },

    getOperator(item) {
      let result = ''
      switch (item.status) {
        case 'PAYING':
          result = item.paying_by ? item.paying_by.name : ''
          break
        case 'PAID':
          result = item.paid_by ? item.paid_by.name : ''
          break
        case 'PAY_FAILED':
          result = item.pay_failed_by ? item.pay_failed_by.name : ''
          break
      }
      return result
    },

    handleSelectionChange(val) {
      this.$emit('update:selection', val)
    },

    openToCNProject(data) {
      const project_id = data.topic?.project_tsid?.split('-')?.[1]
      if (project_id) {
        const cn_db_url = import.meta.env.VITE_APP_URL_DB + '#/project/consultation/index/detail/tasklist?'
        const newWin = window.open('about:blank')
        newWin.location.href = cn_db_url + `id=${project_id}`
      }
    },

    openEventsDialog(row) {
      this.$emit('open-events', row)
    },

    transformValue(i) {
      return Filters.transformKey(i.toLowerCase())
    },
  },
}
</script>

<style scoped lang="scss">
.tag-lead {
  height: auto;
  line-height: 12px;
  padding: 0 2px;
  margin-right: 2px;
  border-radius: 3px;

  &:hover:after {
    content: 'N'
  }
}
</style>
