<template>
  <el-dialog
    :title="`Payment Events (${data.id})`"
    :visible.sync="paymentEventsDialogVisible"
  >
    <el-table
      border
      stripe
      :data="events"
    >
      <el-table-column label="Date">
        <template slot-scope="{row}">
          {{ row.create_at | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Type">
        <template slot-scope="{row}">
          {{ row.type }}
        </template>
      </el-table-column>
      <el-table-column label="Operator">
        <template slot-scope="{row}">
          {{ row.create_by.name }}
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button type="text" @click="paymentEventsDialogVisible = false">Close</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'PaymentEventsDialog',
  data() {
    return {
      paymentEventsDialogVisible: false,
      data: {},
      events: [],
    }
  },
  methods: {
    openDialog(row) {
      this.data = row
      this.events = row.events
      this.paymentEventsDialogVisible = true
    },
  },
}
</script>

<style scoped lang="scss">
</style>
