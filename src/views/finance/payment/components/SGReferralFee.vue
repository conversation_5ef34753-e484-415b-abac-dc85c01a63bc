<template>
  <div class="inline-block">
    <el-button type="success" icon="el-icon-plus" @click="initForm()"> Add Referral Payment</el-button>
    <el-dialog
      :visible.sync="dialogVisible"
      title="Add Referral Payment"
      width="50%"
    >
      <el-form ref="form" :model="form" :rules="rules" :disabled="loading" label-width="160px" class="form">
        <el-form-item label="Referrer" prop="from_advisor_id">
          <el-select
            v-model="form.from_advisor_id"
            filterable
            clearable
            remote
            placeholder="Referrer"
            class="remote-select"
            :loading="searchLoading"
            :remote-method="searchUSAdvisor"
            no-data-text="No matching data"
            @change="setFeeCurrency"
          >
            <el-option
              v-for="item in us_advisor_list"
              :key="item.source.id"
              :label="item.source.name"
              :value="item.source.id">
              {{ item.source.name }} <span class="info pl-5">{{ item.source.id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Recommended Advisor" prop="to_advisor_id">
          <el-select
            v-model="form.to_advisor_id"
            filterable
            clearable
            remote
            placeholder="Recommended Advisor"
            class="remote-select"
            :loading="searchLoading"
            :remote-method="searchAdvisor"
            no-data-text="No matching data"
            @change="setFeeCurrency"
          >
            <el-option
              v-for="item in advisor_list"
              :key="item.source.id"
              :label="item.source.name"
              :value="item.source.id">
              {{ item.source.name }} <span class="info pl-5">{{ item.source.id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Task ID" prop="first_task_id">
          <el-input v-model="form.first_task_id" class="w-200px mr-8" />
          <el-checkbox v-model="form.is_cn_task">CN Task</el-checkbox>
        </el-form-item>
        <el-form-item label="Fee" prop="fee">
          <el-input
            v-model="form.fee"
            type="number"
            :step="150"
            :min="0"
            class="w-200px" />
          <el-select v-model="form.currency" class="w-120px">
            <el-option
              v-for="item in options.currency"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>

        <el-collapse class="box-card">
          <el-collapse-item name="consultant">
            <template slot="title">
              Payment Confirmation Email
            </template>
            <div v-loading="emailLoading">
              <to-cc-editor :data="email" class="mb-8" />
              <el-input v-model="email.subject" class="mb-8" placeholder="Subject" />
              <tinymce ref="tinymce_editor" v-model="email.content" :height="300" inline is-email />
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="actionCancel">Cancel</el-button>
        <el-button type="primary" :loading="loading" @click="actionSubmit">Submit</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import Tinymce from '@/components/Tinymce'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import EmailAPI from '@/api/email'

export default {
  name: 'SGReferralFee',
  components: { Tinymce, ToCcEditor },
  data() {
    return {
      loading: false,
      emailLoading: false,
      searchLoading: false,
      dialogVisible: false,
      form: {
        from_advisor_id: null,
        to_advisor_id: null,
        first_task_id: null,
        is_cn_task: false,
        fee: null,
        currency: 'USD',
      },
      rules: {
        from_advisor_id: [{ required: true, trigger: 'blur', message: 'Referrer is required' }],
        to_advisor_id: [{ required: true, trigger: 'blur', message: 'Recommended Advisor is required' }],
        first_task_id: [{ required: true, trigger: 'blur', message: 'Task ID is required' }],
        fee: [{ required: true, trigger: 'blur' }],
      },
      advisor_list: [],
      us_advisor_list: [],
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'GBP', 'JPY'],
      },
      email_template_id: null,
      email: {},
    }
  },
  watch: {
    'form.from_advisor_id': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          this.generateEmails()
        }
      }, immediate: true,
    },
    'form.fee': {
      handler(newVal) {
        if (newVal) {
          this.generateEmails()
        }
      }, immediate: true,
    },
  },
  methods: {
    initForm() {
      this.resetForm()
      this.getEmailTemplates()
      this.searchAdvisor()
      this.dialogVisible = true
    },

    getEmailTemplates() {
      this.emailLoading = true
      const params = {
        content_type: 'ADVISOR_REFERRAL_PAYMENT_CONFIRM',
        page: 1,
        size: 10,
        include_default_reference_type: true,
        uid: this.$store.state.user.uid,
      }
      return EmailAPI.getEmailList(params)
        .then((data) => {
          this.email_template_id = data.list[0]?.id
        })
        .finally(() => {
          this.emailLoading = false
        })
    },

    generateEmails() {
      const params = [
        {
          advisor_id: this.form.from_advisor_id,
          context_params: {
            payment: {
              PAYMENT_AMOUNT: `${this.form.fee} ${this.form.currency}`,
            },
          },
        }]
      this.emailLoading = true
      return EmailAPI.getEmailsByTemplate(this.email_template_id, params).then(data => {
        this.email = data[0]
        this.$nextTick(() => {
          if (this.$refs['tinymce_editor']) {
            this.$refs['tinymce_editor'].setContent(this.email.content)
          }
        })
      }).finally(() => {
        this.emailLoading = false
      })
    },

    actionCancel() {
      this.resetForm()
      this.dialogVisible = false
    },
    actionSubmit() {
      if (this.email.to_list.length < 1) {
        this.$message({
          message: 'The mail recipient cannot be empty.',
          type: 'error',
        })
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const { from_advisor_id, to_advisor_id, fee, currency, first_task_id, is_cn_task } = this.form
          const params = {
            from_advisor_id,
            to_advisor_id,
            fee,
            currency,
            first_task_id: is_cn_task ? null : first_task_id,
            cn_first_task_id: is_cn_task ? first_task_id : null,
            payment_confirm_email: this.email,
          }
          this.loading = true
          return ConsultantAPI.advisorReferrals(params)
            .then((data) => {
              this.$message({
                message: 'Save successful',
                type: 'success',
              })
              this.$emit('update')
              this.dialogVisible = false
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },

    resetForm() {
      this.form.from_advisor_id = null
      this.form.to_advisor_id = null
      this.form.first_task_id = null
      this.form.is_cn_task = false
      this.form.fee = null
      this.form.currency = 'USD'
    },

    setFeeCurrency(val) {
      const select_advisor = this.advisor_list.find(item => item.source.id === val)
      this.form.currency = select_advisor?.rate_currency || 'USD'
    },

    searchUSAdvisor(query = '') {
      this.searchAdvisor(query, true)
    },

    searchAdvisor(query = '', no_cn = false) {
      const params = {
        is_cn: no_cn ? false : undefined,
        name: query || undefined,
      }

      this.searchLoading = true
      return ConsultantAPI.searchConsultant(params).then(data => {
        no_cn ? this.us_advisor_list = data.list : this.advisor_list = data.list
      }).finally(() => {
        this.searchLoading = false
      })
    },
  },
}

</script>

<style scoped>
.box-card {
  margin: 10px;
}
</style>
