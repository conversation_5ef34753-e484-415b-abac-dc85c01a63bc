<template>
  <div class="app-container">
    <div class="position-relative filter-container tool-switch">
      <transition name="fade">
        <div
          v-if="!selection_list.length"
          :key="1"
          class="tool-switch-page1 flex justify-content-center align-items-center flex-wrap"
          style="width:100%;">
          <div>
            <refresh-button class="filter-item" @action="getList" />
            <advisor-search
              v-model="searchQuery.advisor_ids"
              class="filter-item"
              collapse-tags
              advisor-type="ADVISOR"
              @change="handleSearch" />
            <el-select
              v-model="searchQuery.collaboration_type_in"
              multiple
              placeholder="Type"
              class="filter-item"
              clearable
              @change="handleSearch">
              <el-option
                v-for="lang in collaboration_option"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value" />
            </el-select>
            <project-search
              v-model="searchQuery.project_ids"
              class="filter-item"
              collapse-tags
              @change="handleSearch" />
            <el-select
              v-model="searchQuery.project_sub_type"
              placeholder="Project Type"
              class="filter-item"
              clearable
              @change="handleSearch">
              <el-option
                v-for="lang in project_options.sub_type"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value" />
            </el-select>
            <el-date-picker
              v-model="task_date_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Task Date"
              end-placeholder="Task Date"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-input
              v-model="searchQuery.ids"
              class="filter-item w-200px"
              clearable
              placeholder="IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-model="searchQuery.task_ids"
              class="filter-item w-200px"
              clearable
              placeholder="Task IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-model="searchQuery['task.tsid_in']"
              class="filter-item w-200px"
              clearable
              placeholder="Task Ts IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-model="searchQuery.cn_task_ids"
              class="filter-item w-200px"
              placeholder="CN Task IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-date-picker
              v-model="paying_at_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Paying Date"
              end-placeholder="Paying Date"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-date-picker
              v-model="paid_at_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Paid Date"
              end-placeholder="Paid Date"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-button class="filter-item" type="primary" icon="el-icon-download" @click="downloadPayment">Download
            </el-button>
            <el-button
              class="filter-item"
              type="success"
              icon="el-icon-upload"
              @click="paymentUploadDialogVisible=true">Upload
            </el-button>
            <SGReferralFee v-if="isSGDB" class="filter-item" @update="getList" />
          </div>
          <div class="status-tags tabs-stackOverflow">
            <template v-for="(item, index) in status_options">
              <el-tag
                :key="index"
                :class="{active: (searchQuery.status === item.value)}"
                @click.native="filterStatusSearch(item)">
                {{ item.label }}
                <span class="status-count">{{ getAggregationsCount(item.value) }}</span>
              </el-tag>
            </template>
          </div>
        </div>
        <div v-else :key="2" class="tool-switch-page2">
          <el-select v-model="change_status" class="filter-item" placeholder="Status">
            <el-option
              label="Paying"
              value="PAYING"
            />
            <el-option
              label="Paid"
              value="PAID" />
            <el-option
              label="Pay Failed"
              value="PAY_FAILED" />
          </el-select>
          <el-button type="primary" @click="changeStatus">
            Change Status ({{ selection_list.length }})
          </el-button>
        </div>
      </transition>
    </div>
    <payment-list v-loading="loading"
                  :table-data="tableData"
                  :selection.sync="selection_list" />

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <el-dialog
      title="Payment Upload"
      :visible.sync="paymentUploadDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="30%"
    >
      <el-form label-width="100px" label-position="left">
        <el-form-item label="Status">
          <el-select v-model="change_status" class="filter-item" placeholder="Status">
            <el-option
              label="Paying"
              value="PAYING"
            />
            <el-option
              label="Paid"
              value="PAID" />
            <el-option
              label="Pay Failed"
              value="PAY_FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item label="File">
          <el-upload accept=".xlsx"
                     :before-upload="beforeUpload"
                     :http-request="actionUpload"
                     :limit="1"
                     :file-list="fileList"
                     action="">
            <el-button size="mini" type="primary">Upload...
            </el-button>
            <div slot="tip" class="el-upload__tip">The file format can only be in xlsx format.<br>The size of the
              uploaded file must not exceed 20MB.
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="!upload_file" @click="uploadPayment">Submit</el-button>
        <el-button type="text" :loading="loading" @click="closeUploadDialog()">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/payment'
import Pagination from '@/components/Pagination/index'
import PaymentList from '@/views/finance/payment/components/PaymentList'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import RouteTools from '@/utils/tool-router'
import RefreshButton from '@/components/RefreshButton/index'
import SGReferralFee from '@/views/finance/payment/components/SGReferralFee'
import { debounce } from '@/utils/tool'
// import { AppHttpService } from '@/utils/request'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'Payment',
  components: { RefreshButton, Pagination, PaymentList, AdvisorSearch, ProjectSearch, SGReferralFee },
  data() {
    return {
      loading: false,
      status_options: [
        {
          value: 'INITIAL',
          label: 'Initial',
        }, {
          value: 'ADVISOR_CONFIRMING',
          label: 'Advisor Confirming',
        }, {
          value: 'ADVISOR_CONFIRMED',
          label: 'Advisor Confirmed',
        }, {
          value: 'PAYING',
          label: 'Paying',
        }, {
          value: 'PAID',
          label: 'Paid',
        }, {
          value: 'PAY_FAILED',
          label: 'Pay Failed',
        }, {
          value: null,
          label: 'Total',
        }],
      collaboration_option: [
        {
          value: 'US_TASK_US_ADVISOR',
          label: 'US Task / US Advisor',
        }, {
          value: 'US_TASK_CN_ADVISOR',
          label: 'US Task / CN Advisor',
        }, {
          value: 'CN_TASK_US_ADVISOR',
          label: 'CN Task / US Advisor',
        },
      ],
      total: 0,
      task_date_range: [],
      paying_at_range: [],
      paid_at_range: [],
      searchQuery: {
        page: 1,
        size: 20,
        ids: undefined,
        task_ids: undefined,
        'task.tsid_in': undefined,
        cn_task_ids: undefined,
        project_sub_type: undefined,
        extra: [
          'task',
          'advisor.location',
          'advisor.bank_account',
          'task.project',
          'us_payment.cn_task_url_jit',
          'paying_by',
          'paid_by',
          'pay_failed_by',
          'referral.to_advisor',
          'task.lead',
        ].join(),
        aggregation: 'status',
        status: null,
        advisor_ids: [],
        project_ids: [],
        collaboration_type_in: [],
      },
      tableData: [],
      initAggregation: true,
      aggregations: { total: 0 },
      selection_list: [],
      change_status: 'PAYING',

      paymentUploadDialogVisible: false,
      fileList: [],
      upload_file: undefined,
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    ...mapGetters([
      'project_options',
    ]),
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery, ['collaboration_type_in', 'project_ids', 'advisor_ids'])
    this.getList()
  },
  methods: {
    getList() {
      RouteTools.saveQueryToRouter(this.searchQuery)

      const params = Object.assign({}, this.searchQuery)
      this.formatDateParams(params, this.task_date_range, 'task_date')
      this.formatDateParams(params, this.paying_at_range, 'paying_at')
      this.formatDateParams(params, this.paid_at_range, 'paid_at')
      params.advisor_ids = this.formatListParams(params.advisor_ids)
      params.project_ids = this.formatListParams(params.project_ids)
      params.collaboration_type_in = this.formatListParams(params.collaboration_type_in)
      params['project.sub_type'] = params.project_sub_type || undefined
      delete params.project_sub_type

      this.loading = true
      return API.getPaymentList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
          if (this.initAggregation && data.aggregations) {
            this.aggregations.total = 0
            this.aggregations.status = data.aggregations.status
            data.aggregations.status.forEach(item => {
              this.aggregations.total += item.count
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    debounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },

    filterStatusSearch(item) {
      this.searchQuery.page = 1
      this.searchQuery.status = item.value
      this.initAggregation = false
      this.getList()
    },

    getAggregationsCount(value) {
      let result = 0
      if (value === null) result = this.aggregations.total
      if (!this.aggregations.status) return result
      this.aggregations.status.forEach(item => {
        if (item.value === value) {
          result = item.count
        }
      })
      return result
    },

    downloadPayment() {
      const { ids, task_ids, status, advisor_ids, project_ids, collaboration_type_in, project_sub_type } = this.searchQuery
      const params = {
        ids, task_ids, status,
        advisor_ids: this.formatListParams(advisor_ids),
        project_ids: this.formatListParams(project_ids),
        collaboration_type_in: this.formatListParams(collaboration_type_in),
        'project.sub_type': project_sub_type || undefined,
      }
      this.formatDateParams(params, this.task_date_range, 'task_date')
      this.formatDateParams(params, this.paying_at_range, 'paying_at')
      this.formatDateParams(params, this.paid_at_range, 'paid_at')

      return API.downloadPayment(params)
    },

    closeUploadDialog() {
      this.paymentUploadDialogVisible = false
      this.fileList = []
      this.upload_file = undefined
    },

    actionUpload(ref) {
      const bodyFormData = new FormData()
      bodyFormData.append('file', ref.file)
      this.upload_file = bodyFormData
    },

    beforeUpload(file) {
      const file_type = file.name.substring(file.name.lastIndexOf('.') + 1)

      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else if (file_type !== 'xlsx') {
          this.$message({
            message: 'The file format can only be in xlsx format.',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },

    uploadPayment() {
      return API.uploadPayment(this.change_status, this.upload_file)
        .then(data => {
          const list_ids = data.map(item => item.id).join()
          this.$message({
            message: list_ids + ' change status successful',
            type: 'success',
          })
        })
        .finally(() => {
          this.closeUploadDialog()
        })
    },

    changeStatus() {
      const params = { ids: this.selection_list.map(item => (item.id)) }
      return new Promise((resolve, reject) => {
        switch (this.change_status) {
          case 'PAID':
            return API.changeToPaid(params).then(() => {
              resolve(true)
            }, () => {
              reject()
            })
          case 'PAYING':
            return API.changeToPaying(params).then(() => {
              resolve(true)
            }, () => {
              reject()
            })
          case 'PAY_FAILED':
            return API.changeToPayFailed(params).then(() => {
              resolve(true)
            }, () => {
              reject()
            })
        }
      }).then(() => {
        this.$message({
          message: 'Change status successfully',
          type: 'success',
        }, () => {
        })
      }).finally(() => {
        this.selection_list = []
        this.searchQuery.page = 1
        this.searchQuery.status = null
        this.initAggregation = true
        this.getList()
      })
    },

    formatListParams(value) {
      return value.length ? value.join(',') : undefined
    },

    formatDateParams(params, dateRange, type) {
      params[type + '_gte'] = dateRange && dateRange.length ? dateRange[0] : undefined
      params[type + '_lte'] = dateRange && dateRange.length ? dateRange[1] : undefined
    },
  },
}
</script>

<style scoped lang="scss">
.tool-switch {
  min-height: 45px;
}

::v-deep .el-select input {
  min-height: 28px;
}
</style>
