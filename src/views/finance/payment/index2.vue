<template>
  <div class="app-container">
    <div class="position-relative filter-container tool-switch">
      <transition name="fade">
        <div
          v-if="!selection_list.length"
          :key="1"
          class="tool-switch-page1 flex justify-content-center align-items-center flex-wrap"
          style="width:100%;">
          <div>
            <refresh-button class="filter-item" @action="getList" />
            <advisor-search
              v-model="form.advisor_ids"
              class="filter-item"
              collapse-tags
              advisor-type="ADVISOR"
              @change="handleSearch" />
            <el-select
              v-model="form.payment_region"
              placeholder="Advisor Region"
              class="filter-item"
              clearable
              @change="handleSearch">
              <el-option
                v-for="lang in options.advisor_topic_region"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value" />
            </el-select>
            <el-select
              v-model="form.topic_region"
              placeholder="Project Region"
              class="filter-item"
              clearable
              @change="handleSearch">
              <el-option
                v-for="lang in options.advisor_topic_region"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value" />
            </el-select>
            <project-search
              v-model="form.project_ids"
              class="filter-item"
              collapse-tags
              @change="handleSearch" />
            <el-select
              v-model="form.project_sub_type"
              placeholder="Project Type"
              class="filter-item"
              clearable
              @change="handleSearch">
              <el-option
                v-for="lang in project_options.sub_type"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value" />
            </el-select>
            <el-select
              v-model="form.pay_method_in"
              clearable
              multiple
              collapse-tags
              placeholder="Payment Method"
              class="filter-item"
              style="width: 210px;"
              @change="handleSearch"
            >
              <el-option
                v-for="lang in options.pay_method"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value"
              />
            </el-select>
            <el-input
              v-model="form.ids"
              clearable
              class="filter-item w-200px"
              title="IDs (separated by a comma)"
              placeholder="IDs (separated by a comma)"
              @input="handleDebounceSearch" />
            <el-input
              v-model="form.task_ids"
              clearable
              class="filter-item w-200px"
              title="Task Ts IDs (separated by a comma)"
              placeholder="Task IDs (separated by a comma)"
              @input="handleDebounceSearch" />
            <el-input
              v-model="form.task_ts_ids"
              clearable
              class="filter-item w-200px"
              title="Task Ts IDs (separated by a comma)"
              placeholder="Task Ts IDs (separated by a comma)"
              @input="handleDebounceSearch" />
            <el-input
              v-if="isSGDB"
              v-model="form.project_ts_ids"
              clearable
              class="filter-item w-200px"
              title="Project Ts IDs (separated by a comma)"
              placeholder="Project Ts IDs (separated by a comma)"
              @input="handleDebounceSearch" />
            <el-date-picker
              v-model="task_date_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Task Date"
              end-placeholder="Task Date"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch"
            />
            <el-date-picker
              v-model="paid_at_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Paid Date"
              end-placeholder="Paid Date"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-button
              class="filter-item"
              type="primary"
              icon="el-icon-download"
              :loading="downloading"
              @click="downloadPayment">
              Download
            </el-button>
            <el-button
              class="filter-item"
              type="success"
              icon="el-icon-upload"
              @click="paymentUploadDialogVisible=true">Upload
            </el-button>
            <SGReferralFee v-if="isSGDB" class="filter-item" @update="getList" />
          </div>
          <div class="status-tags tabs-stackOverflow">
            <template v-for="(item, index) in options.status">
              <el-tag
                :key="index"
                :class="{active: (form.status === item.value)}"
                @click.native="filterStatusSearch(item)">
                {{ item.label }}
                <span class="status-count">{{ getAggregationsCount(item.value) }}</span>
              </el-tag>
            </template>
          </div>
        </div>
        <div v-else :key="2" class="tool-switch-page2">
          <template v-if="notOnlySelectVirtualIncentivesItems">
            <el-select
              v-model="change_status"
              :disabled="!!selectedVirtualIncentivesItemsLength"
              class="filter-item"
              placeholder="Status"
            >
              <el-option
                label="Paying"
                value="PAY"
              />
              <el-option
                label="Paid"
                value="PAY_SUCCESS" />
              <el-option
                label="Pay Failed"
                value="PAY_FAIL" />
            </el-select>
            <el-button
              type="primary"
              :disabled="!!selectedVirtualIncentivesItemsLength"
              @click="changeStatus"
            >
              Change Status ({{ selection_list.length }})
            </el-button>
          </template>
          <el-button
            v-if="selectedVirtualIncentivesItemsLength"
            type="primary"
            :disabled="notOnlySelectVirtualIncentivesItems"
            :loading="virtualIncentivesLoading"
            @click="openVirtualIncentivesApprovalDialog"
          >
            Virtual Incentives Approval Preview ({{ selectedVirtualIncentivesItemsLength }})
          </el-button>
          <el-tooltip
            v-if="notOnlySelectVirtualIncentivesItems && selectedVirtualIncentivesItemsLength"
            effect="dark"
            content="Virtual Incentives payment items cannot be selected with other items."
            placement="bottom"
            class="ml-8"
          >
            <i class="el-icon-info info" />
          </el-tooltip>
        </div>
      </transition>
    </div>
    <payment-list
      v-loading="loading"
      :table-data="tableData"
      :selection.sync="selection_list"
      @open-events="openPaymentEventsDialog" />

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList"
    />

    <el-dialog
      title="Payment Upload"
      :visible.sync="paymentUploadDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="30%"
    >
      <el-form label-width="100px" label-position="left">
        <el-form-item label="Status">
          <el-select v-model="change_status" class="filter-item" placeholder="Status">
            <el-option
              label="Paying"
              value="PAY"
            />
            <el-option
              label="Paid"
              value="PAY_SUCCESS" />
            <el-option
              label="Pay Failed"
              value="PAY_FAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="File">
          <el-upload
            accept=".xlsx"
            :before-upload="beforeUpload"
            :http-request="actionUpload"
            :limit="1"
            :file-list="fileList"
            action="">
            <el-button size="mini" type="primary">Upload...
            </el-button>
            <div slot="tip" class="el-upload__tip">The file format can only be in xlsx format.<br>The size of the
              uploaded file must not exceed 20MB.
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="!upload_file" @click="uploadPayment">Submit</el-button>
        <el-button type="text" :loading="loading" @click="closeUploadDialog()">Cancel</el-button>
      </span>
    </el-dialog>

    <payment-events-dialog ref="PaymentEventsDialog" />
    <virtual-incentives-approval-dialog
      ref="VirtualIncentivesApprovalDialog"
      :items="selection_list"
      @done="getList"
    />
  </div>
</template>

<script>
import API from '@/api/payment'
import Pagination from '@/components/Pagination/index'
import PaymentList from '@/views/finance/payment/components/PaymentList2'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import RouteTools from '@/utils/tool-router'
import RefreshButton from '@/components/RefreshButton/index'
import SGReferralFee from '@/views/finance/payment/components/SGReferralFee'
import PaymentEventsDialog from '@/views/finance/payment/components/PaymentEventsDialog'
import VirtualIncentivesApprovalDialog from '@/views/finance/payment/components/VirtualIncentivesApprovalDialog.vue'
import { debounce } from '@/utils/tool'
import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'

export default {
  name: 'Payment',
  components: {
    RefreshButton,
    Pagination,
    PaymentList,
    AdvisorSearch,
    ProjectSearch,
    SGReferralFee,
    PaymentEventsDialog,
    VirtualIncentivesApprovalDialog,
  },
  data() {
    return {
      loading: false,
      downloading: false,
      options: {
        status: [
          {
            value: 'INITIAL',
            label: 'Initial',
          }, {
            value: 'ADVISOR_CONFIRMING',
            label: 'Advisor Confirming',
          }, {
            value: 'ADVISOR_CONFIRMED',
            label: 'Advisor Confirmed',
          }, {
            value: 'PAYING',
            label: 'Paying',
          }, {
            value: 'PAID',
            label: 'Paid',
          }, {
            value: 'PAY_FAILED',
            label: 'Pay Failed',
          }, {
            value: 'CANCELLED',
            label: 'Cancelled',
          }, {
            value: null,
            label: 'Total',
          },
        ],
        advisor_topic_region: [
          {
            value: 'US',
            label: 'US',
          },
          {
            value: 'CN',
            label: 'CN',
          },
          {
            value: 'SEA',
            label: 'SEA',
          },
        ],
        pay_method: [
          {
            value: 'NO_PAYMENT_METHOD',
            label: 'No Payment Method',
          }, {
            value: 'DIRECT_DEPOSIT',
            label: 'Direct Deposit',
          }, {
            value: 'PHYSICAL_CHECK',
            label: 'Physical Check',
          }, {
            value: 'VIRTUAL_INCENTIVES',
            label: 'Virtual Incentives',
          },
        ],
      },
      total: 0,
      task_date_range: [],
      paid_at_range: [],
      searchQuery: {
        page: 1,
        size: 20,
        extra: [
          'form.account',
          'form.account2_no_mosaic_jit',
          'form.virtual_incentives_payment_info',
          'topic.referral.to_advisor',
          'topic.project',
          'topic.task',
          'payment.advisor',
          'events.create_by',
          'event',
        ].join(),
      },
      form: {
        aggregation: ['status_name'],
        ids: undefined,
        task_ids: undefined,
        task_ts_ids: undefined,
        project_ts_ids: undefined,
        project_sub_type: undefined,
        status: null,
        advisor_ids: [],
        project_ids: [],
        payment_region: undefined,
        topic_region: undefined,
        pay_method_in: [],
      },
      tableData: [],
      initAggregation: true,
      aggregations: { total: 0 },
      selection_list: [],
      change_status: 'PAY',

      paymentUploadDialogVisible: false,
      fileList: [],
      upload_file: undefined,

      virtualIncentivesLoading: false,
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    ...mapGetters([
      'project_options',
    ]),
    selectedVirtualIncentivesItemsLength() {
      return this.selection_list.filter(item => item.form?.pay_method_name === 'VIRTUAL_INCENTIVES')?.length
    },
    notOnlySelectVirtualIncentivesItems() {
      return this.selection_list.length !== this.selectedVirtualIncentivesItemsLength
    },
  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.form, ['project_ids', 'advisor_ids', 'pay_method_in'], ['ids', 'task_ids', 'task_ts_ids'])
    this.getList()
  },
  methods: {
    getList() {
      const saveQuery = { ...this.form }
      delete saveQuery.aggregation
      RouteTools.saveQueryToRouter(saveQuery)

      const query = { ...this.searchQuery }
      if (this.form.pay_method_in.length) query.pay_method_in = this.form.pay_method_in.join()

      const params = this.formatPaymentParams(true)

      this.formatDateParams(params.topic, this.task_date_range, 'task_start_time')
      // this.formatDateParams(params, this.paying_at_range, 'paying_at')
      this.formatDateParams(params, this.paid_at_range, 'paid_at')

      this.loading = true
      return API.getPaymentItemsList(params, query)
        .then(data => {
          this.tableData = data.list.map(item => {
            if (item.form) item.form.account = item.form.account2_no_mosaic_jit || item.form.account
            item.latest_pay_date = getPaymentStatusTime(item.events, 'PAY')
            item.latest_pay_succeeded_date = getPaymentStatusTime(item.events, 'PAY_SUCCESS')
            item.latest_pay_failed_date = getPaymentStatusTime(item.events, 'PAY_FAIL')
            return item
          })
          this.total = data.count
          if (this.initAggregation && data.aggregations) {
            this.aggregations.total = 0
            this.aggregations.status_name = data.aggregations.status_name
            data.aggregations.status_name.forEach(item => {
              this.aggregations.total += item.count
            })
          }
        })
        .finally(() => {
          this.loading = false
        })

      function getPaymentStatusTime(list, type) {
        const target_list = _.sortBy(list.filter(item => item.type === type), 'create_at').reverse()
        return target_list.length ? target_list[0].create_at : null
      }
    },

    formatPaymentParams(needAggregation) {
      const params = {
        ids: this.form.ids ? this.form.ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
        status_name: this.form.status || undefined,
        payment: {
          advisor: {
            ids: this.form.advisor_ids.length ? this.form.advisor_ids : undefined,
          },
          region: this.form.payment_region || undefined,
        },
        topic: {
          region: this.form.topic_region || undefined,
          project: {
            ids: this.form.project_ids.length ? this.form.project_ids : undefined,
            sub_type: this.form.project_sub_type || undefined,
            tsid_in: this.form.project_ts_ids ? this.form.project_ts_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
          },
          task: {
            ids: this.form.task_ids ? this.form.task_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
            tsid_in: this.form.task_ts_ids ? this.form.task_ts_ids.split(/,|，|\s+/).map(item => item.trim()) : undefined,
          },
        },
        // events: {
        // },
      }
      if (needAggregation) params.aggregation = this.form.aggregation
      return params
    },

    handleDebounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.getList()
    },

    filterStatusSearch(item) {
      this.searchQuery.page = 1
      this.form.status = item.value
      this.initAggregation = false
      this.getList()
    },

    getAggregationsCount(value) {
      let result = 0
      if (value === null) result = this.aggregations.total
      if (!this.aggregations.status_name) return result
      this.aggregations.status_name.forEach(item => {
        if (item.value === value) {
          result = item.count
        }
      })
      return result
    },

    downloadPayment() {
      this.downloading = true
      const params = this.formatPaymentParams()
      this.formatDateParams(params.topic, this.task_date_range, 'task_start_time')
      this.formatDateParams(params, this.paid_at_range, 'paid_at')

      const query = {}
      if (this.form.pay_method_in.length) query.pay_method_in = this.form.pay_method_in.join()

      return API.downloadPaymentExcel(params, query).finally(() => {
        this.downloading = false
      })
    },

    closeUploadDialog() {
      this.paymentUploadDialogVisible = false
      this.fileList = []
      this.upload_file = undefined
    },

    actionUpload(ref) {
      const bodyFormData = new FormData()
      bodyFormData.append('file', ref.file)
      this.upload_file = bodyFormData
    },

    beforeUpload(file) {
      const file_type = file.name.substring(file.name.lastIndexOf('.') + 1)

      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else if (file_type !== 'xlsx') {
          this.$message({
            message: 'The file format can only be in xlsx format.',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },

    uploadPayment() {
      return API.uploadPaymentExcel(this.change_status, this.upload_file)
        .then(data => {
          const list_ids = data.map(item => item.id).join()
          this.$message({
            message: list_ids + ' change status successful',
            type: 'success',
          })
        })
        .finally(() => {
          this.closeUploadDialog()
        })
    },

    changeStatus() {
      const params = {
        ts_ids: this.selection_list.map(item => (item.id)),
        event_type: this.change_status,
      }
      return API.changePaymentEvent(params).then(() => {
        this.$message({
          message: 'Change status successfully',
          type: 'success',
        }, () => {})
      }).finally(() => {
        this.selection_list = []
        this.searchQuery.page = 1
        this.form.status = null
        this.initAggregation = true
        this.getList()
      })
    },

    formatListParams(value) {
      return value.length ? value.join(',') : undefined
    },

    formatDateParams(params, dateRange, type) {
      params[type + '_gte'] = dateRange && dateRange.length ? dateRange[0] : undefined
      params[type + '_lte'] = dateRange && dateRange.length ? dateRange[1] : undefined
    },

    openPaymentEventsDialog(row) {
      this.$refs['PaymentEventsDialog'].openDialog(row)
    },

    openVirtualIncentivesApprovalDialog() {
      const params = {
        payment_item_ids: this.selection_list.map(item => item.id),
      }
      this.virtualIncentivesLoading = true
      return API.getVirtualIncentivesApprovalPreview(params)
        .then(data => {
          this.$refs['VirtualIncentivesApprovalDialog'].openDialog(data)
        })
        .finally(() => {
          this.virtualIncentivesLoading = false
        })
    },
  },
}
</script>

<style scoped lang="scss">
.tool-switch {
  min-height: 45px;
}

::v-deep .el-select input {
  min-height: 28px;
}
</style>
