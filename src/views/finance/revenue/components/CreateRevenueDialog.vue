<template>
  <el-dialog
    title="Create Revenue"
    :visible.sync="dialogVisible"
  >
    <el-form
      ref="form"
      :model="form"
      :disabled="submitting"
      label-width="110px"
    >
      <el-form-item label="Client" prop="client_id" required>
        <client-search
          v-model="form.client_id"
          class="revenue-form-item"
          @change="handleChangeClient"
        />
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Client Contact" prop="client_contact_id">
        <el-select
          v-model="form.client_contact_id"
          filterable
          placeholder="Client Contact Name"
          clearable
          class="revenue-form-item"
        >
          <el-option
            v-for="item in options.client_contacts"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-tooltip
          v-if="!options.client_contacts.length"
          content="The client has no contact."
          effect="dark"
          placement="top">
          <el-link type="info" :underline="false" icon="el-icon-info" />
        </el-tooltip>
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Project" prop="project_id">
        <project-search
          ref="projectSearch"
          v-model="form.project_id"
          :multiple="false"
          :client-id="form.client_id"
          clearable
          class="revenue-form-item"
        />
        <el-tooltip
          v-if="noProject"
          content="The client has no project."
          effect="dark"
          placement="top">
          <el-link type="info" :underline="false" icon="el-icon-info" />
        </el-tooltip>
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Task ID" prop="task_id">
        <el-input
          v-model.number="form.task_id"
          type="number"
          placeholder="Task ID"
          class="revenue-form-item" />
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Contract" prop="contract_id" required>
        <contract-search
          ref="contractSearch"
          v-model="form.contract_id"
          :client-id="form.client_id"
          class="revenue-form-item"
        />
        <el-tooltip
          v-if="noContract"
          content="The client has no effective and approved contract."
          effect="dark"
          placement="top">
          <el-link type="info" :underline="false" icon="el-icon-info" />
        </el-tooltip>
      </el-form-item>
      <el-divider />
      <el-form-item label="Date" prop="revenue_time" required>
        <el-date-picker
          v-model="form.revenue_time"
          type="date"
          placeholder="Date"
          format="MM/dd/yyyy"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="Client Hours" prop="client_hours" required>
        <el-input-number v-model="form.client_hours" class="w-120px" :step="0.25" />
      </el-form-item>
      <el-form-item label="Billable Hours" prop="billing_hours" required>
        <el-input-number v-model="form.billing_hours" class="w-120px" :step="0.25" />
      </el-form-item>
      <el-form-item label="Revenue" prop="cash" required>
        <el-input-number v-model="form.cash" class="w-120px" />
        <el-select
          v-model="form.cash_currency"
          placeholder="Currency"
          class="w-120px ml-3">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="Remarks" prop="billing_notes">
        <el-input v-model="form.billing_notes" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button type="primary" :loading="submitting" @click="actionCreateRevenue">Confirm</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ContractAPI from '@/api/contract'
import ClientAPI from '@/api/client'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import ContractSearch from '@/components/SelectRemoteSearch/ContractSearch'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import moment from 'moment'

export default {
  name: 'CreateRevenueDialog',
  components: { ClientSearch, ContractSearch, ProjectSearch },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      form: {
        client_id: undefined,
        revenue_time: undefined,
        contract_id: undefined,
        client_hours: 0,
        billing_hours: 0,
        cash: 0,
        cash_currency: 'USD',
        billing_notes: '',
        project_id: undefined,
        client_contact_id: undefined,
        task_id: '',
      },
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        client_contacts: [],
      },
      noProject: false,
      noContract: false,
    }
  },
  methods: {
    show() {
      this.dialogVisible = true
    },
    actionCreateRevenue() {
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.submitting = true
            const params = {
              project_id: 0,
              task_id: 0,
              ...this.form,
            }
            params.revenue_time = moment(this.form.revenue_time).toISOString()
            await ContractAPI.createRevenue(params)
            this.$emit('done')
            this.dialogVisible = false
          } finally {
            this.submitting = false
          }
        } else {
          return false
        }
      })
    },
    handleChangeClient(val) {
      this.form.client_contact_id = undefined
      this.form.project_id = undefined
      this.form.task_id = ''
      this.form.contract_id = undefined
      if (val) {
        this.getClientContacts()
        if (!this.$refs['projectSearch'] || !this.$refs['contractSearch']) return
        this.$nextTick(async() => {
          await this.$refs['projectSearch']?.searchProject()
          await this.$refs['contractSearch']?.searchContract()
          this.noProject = !this.$refs['projectSearch']?.list.length
          this.noContract = !this.$refs['contractSearch']?.list.length
          this.$refs.form.clearValidate()
        })
      }
    },
    getClientContacts() {
      return ClientAPI.getClient(this.form.client_id, 'client_contacts')
        .then(data => {
          this.options.client_contacts = data['client_contacts']
        })
    },
  },
}
</script>

<style scoped>
.revenue-form-item {
  max-width: 300px;
  width: 100%;
}
</style>
