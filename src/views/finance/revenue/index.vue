<template>
  <div class="app-container">
    <div class="position-relative filter-container tool-switch filter-container">
      <transition name="fade">
        <div
          v-if="!selection.length"
          :key="1"
          class="tool-switch-page1 flex justify-content-center align-items-center flex-wrap"
          style="width:100%;">
          <div>
            <refresh-button class="filter-item" @action="getList" />
            <client-search
              v-model="searchQuery.client_id"
              class="w-200px filter-item"
              @change="handleSearch" />
            <project-search
              v-model="searchQuery.project_id"
              class="filter-item"
              :multiple="false"
              @change="handleSearch" />
            <ProjectCodeSearch
              v-model="searchQuery.project_code"
              class="filter-item"
              @change="handleSearch" />
            <el-select
              v-model="searchQuery.contract_id"
              filterable
              clearable
              remote
              placeholder="Contract Name"
              class="remote-select filter-item"
              :loading="searchContractLoading"
              :remote-method="searchContract"
              no-data-text="No matching data"
              @change="handleSearch"
            >
              <el-option
                v-for="item in contract_list"
                :key="item.id"
                :label="item.name"
                :value="item.id" />
            </el-select>
            <el-select
              v-model="searchQuery.payways"
              multiple
              clearable
              class="filter-item"
              collapse-tags
              placeholder="Pay Way"
              @change="handleSearch">
              <el-option
                v-for="item in contract_options.payWay"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-select
              v-model="searchQuery['task.sub_type_in']"
              multiple
              clearable
              class="filter-item"
              collapse-tags
              placeholder="Task Sub Type"
              @change="handleSearch"
            >
              <el-option
                v-for="item in task_sub_type_options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-date-picker
              v-model="task_date_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Task Date"
              end-placeholder="Task Date"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-date-picker
              v-model="revenue_time_range"
              type="daterange"
              class="filter-item"
              start-placeholder="Revenue Date"
              end-placeholder="Revenue Date"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleSearch" />
            <el-input
              v-model="searchQuery.ids"
              class="filter-item w-200px"
              clearable
              title="IDs (separated by a comma)"
              placeholder="IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-model="searchQuery.task_ids"
              class="filter-item w-200px"
              clearable
              title="Task IDs (separated by a comma)"
              placeholder="Task IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-model="searchQuery['task.tsid_in']"
              class="filter-item w-200px"
              clearable
              title="Task Ts IDs (separated by a comma)"
              placeholder="Task Ts IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-if="isSGDB"
              v-model="searchQuery['project.tsid_in']"
              class="filter-item w-200px"
              clearable
              title="Project Ts IDs (separated by a comma)"
              placeholder="Project Ts IDs (separated by a comma)"
              @input="debounceSearch" />
            <el-input
              v-if="isSGDB"
              v-model="searchQuery['project.outsource_client_name_contains']"
              placeholder="CN Client"
              clearable
              class="filter-item w-200px"
              @input="debounceSearch" />
            <el-button
              type="primary"
              class="filter-item"
              icon="el-icon-download"
              @click="downloadRevenueList">
              Download
            </el-button>
            <el-button
              v-if="createRevenueBtnVisible"
              type="success"
              class="filter-item"
              icon="el-icon-plus"
              @click="openCreateRevenueDialog">
              Create
            </el-button>
          </div>
          <div class="tabs-stackOverflow status-tags">
            <template v-for="(item, index) in aggregation_status">
              <el-tag
                :key="index"
                :class="{active: (searchQuery.status_in === item.value || (!item.value && !searchQuery.status_in))}"
                @click.native="filterStatusSearch(item)">
                {{ item.label }}
                <span class="status-count">{{ item.count }}</span>
              </el-tag>
            </template>
          </div>
        </div>
        <div v-else :key="2" class="tool-switch-page2">
          <el-tooltip
class="item"
                      :disabled="!createInvoiceDisabled"
                      effect="dark"
                      content="Contracts of the prepay type cannot create invoices here.(Only Admin and Finance have permission to edit.)"
                      placement="top">
            <div class="inline-block">
              <el-button
                class="filter-item"
                type="success"
                icon="el-icon-plus"
                :disabled="createInvoiceDisabled"
                :loading="invoice_loading"
                @click="createInvoice">Create Invoice
              </el-button>
            </div>
          </el-tooltip>
          <el-button
            type="primary"
            class="filter-item"
            icon="el-icon-download"
            :loading="download_loading"
            @click="downloadUsageReport">Download Usage
          </el-button>
          <el-tooltip
effect="dark"
                      placement="top"
                      content="At present you can only download REVENUE based on the same contract at the same time.">
            <el-link :underline="false" icon="el-icon-question" />
          </el-tooltip>
          <template-download-usage :data-list.sync="download_tab" :option-list="client_usage_fields" />
        </div>
      </transition>
    </div>

    <create-revenue-dialog
      ref="createRevenueDialog"
      @done="getList"
    />

    <el-table
      ref="revenueTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      @select="(select_list,row) => $options.filters.tableShiftMultiple(select_list, row, tableData, $refs.revenueTable, checkSelectable)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" :selectable="checkSelectable" />
      <el-table-column prop="id" label="ID" width="50" />
      <el-table-column label="Task ID" width="70">
        <template slot-scope="{row}">{{ row.task_id || '' }}</template>
      </el-table-column>
      <el-table-column label="Task Ts ID" width="80">
        <template slot-scope="{row}">{{ row.task ? row.task.tsid : '' }}</template>
      </el-table-column>
      <el-table-column label="Project">
        <template slot-scope="scope">
          <router-link-project v-if="scope.row.project" :data="scope.row.project" />
        </template>
      </el-table-column>
      <el-table-column label="Project Code">
        <template slot-scope="scope">
          {{ scope.row.project && scope.row.project.code }}
        </template>
      </el-table-column>
      <el-table-column label="Project Status" width="120">
        <template v-if="scope.row.project" slot-scope="scope">
          {{ scope.row.project.status | getLabel(project_options.status) }}
        </template>
      </el-table-column>
      <el-table-column label="Client">
        <template slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column label="Contract">
        <template slot-scope="scope">
          <router-link
            v-if="scope.row.contract"
            class="text-truncate link"
            :title="scope.row.contract.name"
            :to="{ name: 'ContractDetail', params: { id: scope.row.contract_id } }">
            {{ scope.row.contract.name || '' }}
          </router-link>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isSGDB"
        label="CN Client"
        min-width="100"
      >
        <template v-slot="{row}">
          <span v-if="row.project && row.project.exported_to === 'CN'">
            {{ row.project.outsource_client_name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="Task Date">
        <template slot-scope="scope">
          {{ scope.row.task && scope.row.task.start_time | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Revenue Date">
        <template slot-scope="scope">
          {{ scope.row.revenue_time | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Task Sub Type">
        <template slot-scope="scope">
          {{ scope.row.task && scope.row.task.sub_type | getLabel(task_sub_type_options) }}
        </template>
      </el-table-column>
      <el-table-column label="KM">
        <template slot-scope="scope">
          <span v-if="scope.row.task && scope.row.task.lead">{{ scope.row.task.lead.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="User">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :to="{ name: 'ClientDetail', params: { client_id: scope.row.client_id }, query: { tab: 'contact' } }">
            {{ scope.row.contact_name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="Advisor">
        <template v-if="scope.row.task" slot-scope="scope">
          <router-link-advisor v-if="scope.row.task.advisor" :data="scope.row.task.advisor" />
        </template>
      </el-table-column>
      <el-table-column prop="client_hours" label="Client Hours" />
      <el-table-column prop="billing_hours" label="Billing Hours" width="100" />
      <el-table-column label="Revenue">
        <template slot-scope="scope">
          {{ scope.row.cash }} / {{ scope.row.cash_currency }}
        </template>
      </el-table-column>
      <el-table-column label="Notes" width="100">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.billing_notes" :content="scope.row.billing_notes" placement="top">
            <i class="primary el-icon-chat-line-square" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Invoice ID" width="100">
        <template slot-scope="scope">
          {{ scope.row.invoice_id }}
        </template>
      </el-table-column>
      <el-table-column v-if="isSGDB" label="Project TS ID" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.project && scope.row.project.tsid }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :page-sizes="customPageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'
import ProjectAPI from '@/api/project'
import ContractAPI from '@/api/contract'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RouterLinkProject from '@/components/RouterLink/Project'
import { debounce, downloadDocument } from '@/utils/tool'
import RefreshButton from '@/components/RefreshButton/index'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import TemplateDownloadUsage from '@/components/ClientDownloadUsage/index'
import RouteTools from '@/utils/tool-router'
import ProjectCodeSearch from '@/components/SelectRemoteSearch/ProjectCodeSearch'
import ProjectSearch from '@/components/SelectRemoteSearch/ProjectSearch'
import moment from 'moment-timezone'
import CreateRevenueDialog from './components/CreateRevenueDialog'

export default {
  name: 'Revenue',
  components: {
    RefreshButton,
    ClientSearch,
    Pagination,
    RouterLinkProject,
    RouterLinkClient,
    RouterLinkAdvisor,
    TemplateDownloadUsage,
    ProjectCodeSearch,
    ProjectSearch,
    CreateRevenueDialog,
  },

  data() {
    return {
      loading: false,
      invoice_loading: false,
      download_loading: false,
      searchProjectLoading: false,
      searchContractLoading: false,
      customPageSize: [20, 30, 50, 100, 150, 200, 500, 700, 800, 1000],
      tableData: [],
      total: NaN,
      contract_list: [],
      project_list: [],
      aggregation_status: this.initAggregationStatus(),
      initAggregationFlag: false,
      task_date_range: [],
      revenue_time_range: [],
      searchQuery: {
        ids: undefined,
        task_ids: undefined,
        'task.tsid_in': undefined,
        'project.tsid_in': undefined,
        'project.outsource_client_name_contains': undefined,
        client_id: null,
        project_id: null,
        project_code: null,
        contract_id: null,
        status_in: null,
        payways: [],
        'task.sub_type_in': [],
        page: 1,
        size: 20,
        sort: 'revenue_time desc,id asc',
        extra: 'task.lead,task.client_contact,task.advisor,contract,client.preference,project',
        aggregation: 'status',
      },
      selection: [],
      select_contract_id: 0,
      download_tab: [],
      client_usage_fields: undefined,
      all_usage_fields: undefined,
      default_usage_fields: undefined,
      shiftPress: false,
      last_select_index: null,
      task_sub_type_options: [
        { label: 'Consultation', value: 'CONSULTATION' },
        { label: 'Investor Call', value: 'INVESTOR_CALL' },
        { label: 'Regular Phone Consultation', value: 'REGULAR_PHONE_CONSULTATION' },
        { label: 'Follow Up Phone Consultation', value: 'FOLLOW_UP_PHONE_CONSULTATION' },
        { label: 'Written Follow Up', value: 'WRITTEN_FOLLOW_UP' },
        { label: 'Phone', value: 'PHONE' },
        { label: 'Survey', value: 'SURVEY' },
        { label: 'BD Call', value: 'BD_CALL' },
        { label: 'Consultation-Survey', value: 'CONSULTATION_SURVEY' },
        { label: 'Consultation-Conference', value: 'CONSULTATION_CONFERENCE' }
      ],
    }
  },

  computed: {
    ...mapGetters([
      'contract_options',
      'project_options',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
    }),

    createInvoiceDisabled() {
      const hasPermission = this.roles.some(item => ['Admin', 'Finance'].includes(item.role?.name))
      return this.selection.filter(item => item.contract?.payway === 'PREPAY').length > 0 || !hasPermission
    },
    createRevenueBtnVisible() {
      return this.roles.some(item => ['Admin', 'AllowAddRevenue'].includes(item.role?.name))
    },
  },

  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)

    this.getOptionsList()
    this.initAggregationFlag = true
    this.getList()
  },
  methods: {
    getList() {
      const url_params = _.cloneDeep(this.searchQuery)
      delete url_params.status_in
      RouteTools.saveQueryToRouter(url_params)

      this.loading = true
      const params = Object.assign({}, this.formatParams(this.searchQuery))
      params['project.code'] = params.project_code
      params['contract.payway_in'] = params.payways.join()
      params['task.sub_type_in'] = params['task.sub_type_in'].join()
      delete params.payways
      this.formatDateParams(params, this.task_date_range, 'task.start_time')
      this.formatDateParams(params, this.revenue_time_range, 'revenue_time')

      return ContractAPI.getRevenueList(params)
        .then(data => {
          this.total = data.count
          this.tableData = data.list.map(item => {
            if (item.task) {
              item.contact_name = item.task.client_contact_id > 0 && item.task.client_contact ? item.task.client_contact.name : ''
            }
            return item
          })
          if (data.aggregations && this.initAggregationFlag) {
            this.aggregation_status = this.initAggregationStatus()

            data.aggregations.status.forEach(item => {
              if (item.value === 'CREATED') {
                this.aggregation_status = [
                  { value: 'BILLED,CONFIRMED', label: 'Billed', count: this.total - item.count },
                  { value: 'CREATED', label: 'Unbilled', count: item.count },
                  { value: null, label: 'Total', count: this.total }]
              }
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    downloadRevenueList() {
      this.loading = true
      const params = Object.assign({}, this.formatParams(this.searchQuery))
      params['project.code'] = params.project_code
      params['contract.payway_in'] = params.payways.join()
      params['task.sub_type_in'] = params['task.sub_type_in'].join()
      delete params.payways
      this.formatDateParams(params, this.task_date_range, 'task.start_time')
      this.formatDateParams(params, this.revenue_time_range, 'revenue_time')
      delete params.page
      delete params.size
      delete params.aggregation

      return ContractAPI.downloadRevenueList(params)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Success!',
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    getOptionsList() {
      return Promise.all([this.searchProject(), this.searchContract(), this.getAvailableFields()])
    },

    searchContract(val) {
      const params = {
        page: 1,
        size: 20,
        'approval_status': 'APPROVED',
        name_contains: val || undefined,
      }
      this.searchContractLoading = true
      return ContractAPI.getContractList(params)
        .then(data => {
          this.contract_list = data.list.map(item => {
            return { name: item.name, id: item.id }
          })
        }).finally(() => {
          this.searchContractLoading = false
        })
    },

    searchProject(val) {
      const params = {
        page: 1,
        size: 20,
        name_like: val || undefined,
      }
      this.searchProjectLoading = true
      return ProjectAPI.getProjectList(params)
        .then(data => {
          this.project_list = data.list.map(item => {
            return { name: item.name, id: item.id }
          })
        }).finally(() => {
          this.searchProjectLoading = false
        })
    },

    filterStatusSearch(item) {
      this.searchQuery.page = 1
      this.initAggregationFlag = false
      this.searchQuery.status_in = item.value
      this.getList()
    },

    debounceSearch() {
      debounce(() => {
        this.handleSearch()
      }, 500)
    },

    handleSearch() {
      this.searchQuery.page = 1
      this.searchQuery.status_in = null
      this.initAggregationFlag = true
      this.getList()
    },

    handleSelectionChange(val) {
      this.selection = val
      if (val.length) {
        this.select_contract_id = val[0].contract_id
        const client_workflow = val[0].client.preference?.preferred_workflow || 'NORMAL'
        this.client_usage_fields = this.all_usage_fields[client_workflow]
        val.forEach(item => {
          if (item.contract_id !== this.select_contract_id) {
            this.$refs.revenueTable.toggleRowSelection(item, false)
          }
        })

        if (val[0].client.preference?.usage_excel_fields) {
          this.download_tab = val[0].client.preference.usage_excel_fields
        } else {
          const default_usage_fields = this.client_usage_fields.filter(item => item.is_standard)
          this.download_tab = default_usage_fields.map(item => item.name)
        }
      } else {
        this.select_contract_id = 0
        this.download_tab = []
      }
    },

    getAvailableFields() {
      return ContractAPI.getUsageExcelTemplate()
        .then(data => {
          this.all_usage_fields = data
        })
    },

    checkSelectable(row) {
      // 目前后端限制只能 download 同一个合同的 usage
      return !this.select_contract_id || row.contract_id === this.select_contract_id
    },

    createInvoice() {
      if (this.validateSelection(true)) {
        const data = {
          client_id: this.selection[0].client_id,
          contract_id: this.selection[0].contract_id,
          revenues: this.selection.map(item => ({ id: item.id })),
          zone_id: this.timeZone,
        }
        this.invoice_loading = true
        return ContractAPI.createInvoice(data)
          .then(res => {
            this.selection.forEach(item => {
              item.invoice_id = res.id
            })
            this.$message({
              message: 'Create invoice successfully',
              type: 'success',
            })
          })
          .finally(() => {
            this.invoice_loading = false
          })
      }
    },

    downloadUsageReport() {
      if (this.validateSelection()) {
        const params = {
          ids: this.selection.map(item => item.id)
            .join(','),
          zone_id: this.timeZone,
          columns: this.download_tab.length ? this.download_tab.join() : undefined,
        }
        this.download_loading = true
        return ContractAPI.downloadUsageReport(params)
          .then(data => {
            downloadDocument(data)
          })
          .finally(() => {
            this.download_loading = false
          })
      }
    },

    validateSelection(checkInvoice = false) {
      let message = ''
      const validateContract = this.selection.find(item => item.contract_id !== this.selection[0].contract_id)
      if (validateContract) message = 'Contracts of the selected revenues must be the same'
      if (checkInvoice) {
        const validateInvoice = this.selection.find(item => item.invoice_id)
        if (validateInvoice) message = `Revenue id:${validateInvoice.id} has been invoiced`
      }
      if (message) this.$message({ message, type: 'warning' })
      return !message
    },

    formatParams(data) {
      Object.keys(data)
        .forEach(function(key) {
          if (!data[key]) {
            delete data[key]
          }
        })
      return data
    },

    formatDateParams(params, dateRange, type) {
      const toTimezoneUTC = (val) => { return moment.tz(val, this.timeZone).toISOString() }

      params[type + '_gte'] = dateRange && dateRange.length ? toTimezoneUTC(dateRange[0]) : undefined
      params[type + '_lte'] = dateRange && dateRange.length ? toTimezoneUTC(dateRange[1]) : undefined
    },

    initAggregationStatus() {
      return [
        { value: 'BILLED,CONFIRMED', label: 'Billed', count: 0 },
        { value: 'CREATED', label: 'Unbilled', count: 0 },
        { value: null, label: 'Total', count: 0 },
      ]
    },

    openCreateRevenueDialog() {
      this.$refs['createRevenueDialog']?.show()
    },
  },
}
</script>

<style scoped>
.tool-switch {
  min-height: 45px;
}
</style>
