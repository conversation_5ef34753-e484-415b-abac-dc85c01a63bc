<template>
  <div class="components-container">
    <Calendar v-model="events" :selectable="false" :editable="false" scheduler />
  </div>
</template>

<script>
import API from '@/api/scheduler'
import Calendar from '@/components/Calendar'
import moment from 'moment-timezone'
import { mapState } from 'vuex'

export default {
  name: 'Scheduler',
  components: { Calendar },
  data() {
    return {
      events: [],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },
  mounted() {
    this.getSchedule()
  },
  methods: {
    getSchedule() {
      return API.getSchedule().then(data => {
        this.events = data.map(obj => {
          return Object.assign({
            start: moment(obj.start_time).tz(this.timeZone).format(),
            end: moment(obj.end_time).tz(this.timeZone).format(),
            editable: false,
            title: `${obj.advisor.name_prefix || ''} ${obj.advisor.full_name}`,
          }, obj)
        })
      })
    },
  },
}
</script>

<style scoped>

</style>
