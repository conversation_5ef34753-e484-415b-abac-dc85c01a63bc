<template>
  <div class="login-container">
    <login-form
      v-show="['LOGIN_FORM', 'EXPIRED_2FA'].includes(loginStatus)"
      :user-id.sync="userId"
      @auth="actionAuth"
      @after-login="routeToRedirect"
    />
    <login-verification
      v-show="['NEED_2FA_AUTH', 'FAILED_2FA_AUTH'].includes(loginStatus)"
      :user-id="userId"
      :login-form="loginForm"
      @auth="actionAuth"
      @after-login="routeToRedirect"
    />
    <social-login-verification
      v-if="loginStatus === 'NEED_2FA_VERIFY'"
      @auth="actionAuth"
    />
    <reset-password
      v-if="loginStatus === 'NEED_RESET_PASSWORD'"
      @auth="actionAuth"
    />
  </div>
</template>

<script>
import LoginForm from '@/views/login/components/LoginForm'
import LoginVerification from '@/views/login/components/LoginVerification'
import SocialLoginVerification from '@/views/login/components/SocialLoginVerification.vue'
import ResetPassword from '@/views/login/components/ResetPassword'

export default {
  name: 'Login',
  components: { LoginForm, LoginVerification, SocialLoginVerification, ResetPassword },
  data() {
    return {
      loginForm: {},
      userId: 0,
      redirect: undefined,
      loginStatus: 'LOGIN_FORM',
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
  },
  methods: {
    actionAuth(login_status, login_form) {
      const login_status_map = {
        'NEED_RESET_PASSWORD': {
          message: 'You have failed to log in five times in a row, please try to reset your password.',
          type: 'warning',
        },
        'EXPIRED_2FA': {
          message: 'Verification code has expired, please log in again.',
          type: 'warning',
        },
        'FAILED_2FA_AUTH': {
          message: 'Verification failed.',
          type: 'error',
        },
      }
      if (login_status_map[login_status]) {
        this.$message({
          message: login_status_map[login_status].message,
          type: login_status_map[login_status].type,
          duration: 5000,
        })
      }
      const status_list = [
        'LOGIN_FORM',
        'EXPIRED_2FA',
        'NEED_2FA_AUTH',
        'FAILED_2FA_AUTH',
        'NEED_2FA_VERIFY',
        'NEED_RESET_PASSWORD',
      ]
      this.loginStatus = status_list.includes(login_status) ? login_status : 'LOGIN_FORM'
      this.loginForm = login_form
    },
    routeToRedirect() {
      this.$router.push({ path: this.redirect || '/' })
    },
  },
}
</script>

<style lang="scss" scoped>
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$dark_gray: #889aa4;
$light_gray: #eee;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  ::v-deep .login-container .el-input .el-input__inner {
    color: $cursor;
  }
}

.login-container {
  display: flex;
  align-items: center;
  min-height: 100%;
  width: 100%;
  background-color: #2d3a4b;
  overflow: hidden;

  /* reset element-ui css */
  ::v-deep .el-input {
    .el-input__inner {
      background: transparent;
      -webkit-appearance: none;
      color: $cursor;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px #283443 inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  ::v-deep .el-button.el-button--primary {
    border: 1px solid transparent;
    background-color: #0960bd;
    box-shadow: 0 2px 0 rgba(0, 47, 113, 0.15);
    transition: all .2s;

    &:hover {
      background-color: #2873b7;
    }
  }

  ::v-deep .el-divider__text {
    color: white;
    background-color: #2d3a4b;
  }

  /* custom css */
  ::v-deep .login-form {
    position: relative;
    width: 100%;
    padding: 30px;
    margin: 0 auto;
    overflow: hidden;

    opacity: 0;
    animation: enter-x-animation .4s ease-in-out .3s;
    animation-fill-mode: forwards;
    animation-delay: .1s;
    transform: translate(50px);

    .tips {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
    }

    .title-container {
      position: relative;

      .title {
        font-size: 26px;
        color: $light_gray;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
      }
    }
  }
}

@keyframes enter-x-animation {
  to {
    opacity: 1;
    transform: translate(0)
  }
}
</style>
