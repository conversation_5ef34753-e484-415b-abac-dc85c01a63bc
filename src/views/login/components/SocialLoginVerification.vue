<template>
  <el-form
    ref="loginForm"
    :model="form"
    :disabled="submitting"
    class="login-form"
  >

    <div class="title-container">
      <h3 class="title">Login Verification</h3>
    </div>

    <el-form-item prop="email" required>
      <el-input
        v-model="form.email"
        type="email"
        placeholder="Please Input Email"
        clearable
        size="medium"
      />
    </el-form-item>
    <el-form-item prop="code" required>
      <div class="flex">
        <el-input
          v-model="form.code"
          placeholder="Please Input Verification Code"
          clearable
          size="medium"
          class="flex-1 mr-8"
        />
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!!intervalNumber"
          @click="sendVerificationCode"
        >
          {{ intervalNumber ? `${intervalNumber}s` : 'Send Code' }}
        </el-button>
      </div>
    </el-form-item>
    <div class="login-verification-btn">
      <el-button
        type="primary"
        size="medium"
        :loading="submitting"
        @click="confirmLoginCode"
      >
        Confirm To Verify
      </el-button>
      <el-button
        size="medium"
        @click="$emit('auth', 'LOGIN_FORM')"
      >
        Back
      </el-button>
    </div>
  </el-form>
</template>

<script>
import AuthAPI from '@/api/auth'

export default {
  name: 'SocialLoginVerification',
  data() {
    return {
      loading: false,
      submitting: false,
      intervalNumber: 0,
      form: {
        email: '',
        auth_option: 'EMAIL',
        code: '',
      },
    }
  },
  methods: {
    sendVerificationCode() {
      if (!this.form.email) return

      this.loading = true
      return AuthAPI.getTwoFactorAuth({
        email: this.form.email,
        auth_option: this.form.auth_option,
      }).then(() => {
        this.intervalNumber = 60
        const interval = setInterval(() => {
          this.intervalNumber -= 1
          if (this.intervalNumber <= 0) clearInterval(interval)
        }, 1000)
      }).finally(() => {
        this.loading = false
      })
    },
    confirmLoginCode() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.submitting = true
          return AuthAPI.verifyTwoFactorAuth({ ...this.form })
            .then(data => {
              if (data.verify_result) {
                this.$message({
                  type: 'success',
                  message: 'Success to verify, please login again.',
                })
                this.$emit('auth', 'LOGIN_FORM')
              } else {
                this.$message({
                  type: 'error',
                  message: 'Failed to verify.',
                })
              }
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          console.log('error submit!!')
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

.login-form {
  max-width: 400px;

  ::v-deep .el-radio .el-radio__label {
    color: $cursor;
  }

  ::v-deep .el-input .el-input__inner {
    border: 1px solid $light_gray;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  .login-verification-btn {
    .el-button {
      display: block;
      width: 100%;
      margin: 10px 0;
    }
  }
}
</style>
