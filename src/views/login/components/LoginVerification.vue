<template>
  <el-form
    ref="loginForm"
    :model="form"
    :disabled="submitting"
    class="login-form"
  >

    <div class="title-container">
      <h3 class="title">Login Verification</h3>
    </div>

    <el-form-item>
      <el-radio-group v-model="form.auth_option" size="medium">
        <el-radio
          v-for="item in options.auth"
          :key="item.value"
          :label="item.value"
          border
          class="mr-8"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item prop="code" required>
      <div class="flex">
        <el-input
          v-model="form.code"
          placeholder="Please Input Verification Code"
          clearable
          size="medium"
          class="flex-1 mr-8"
        />
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!!intervalNumber"
          @click="sendVerificationCode"
        >
          {{ intervalNumber ? `${intervalNumber}s` : 'Send Code' }}
        </el-button>
      </div>
    </el-form-item>
    <div class="login-verification-btn">
      <el-button
        type="primary"
        size="medium"
        :loading="submitting"
        @click="confirmLoginCode"
      >
        Confirm To Login
      </el-button>
      <el-button
        size="medium"
        @click="$emit('auth', 'LOGIN_FORM')"
      >
        Back
      </el-button>
    </div>
  </el-form>
</template>

<script>
import AuthAPI from '@/api/auth'

export default {
  name: 'LoginVerification',
  props: {
    userId: Number,
    loginForm: Object,
  },
  data() {
    return {
      loading: false,
      submitting: false,
      intervalNumber: 0,
      options: {
        auth: [
          {
            label: 'Email',
            value: 'EMAIL',
          }, {
            label: 'SMS',
            value: 'SMS',
          },
        ],
      },
      form: {
        auth_option: 'EMAIL',
        code: '',
      },
    }
  },
  methods: {
    sendVerificationCode() {
      this.loading = true
      return AuthAPI.getTwoFactorAuth({
        id: this.userId,
        auth_option: this.form.auth_option,
      }).then(() => {
        this.intervalNumber = 60
        const interval = setInterval(() => {
          this.intervalNumber -= 1
          if (this.intervalNumber <= 0) clearInterval(interval)
        }, 1000)
      }).finally(() => {
        this.loading = false
      })
    },
    confirmLoginCode() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          const loginParams = {
            ...this.loginForm,
            ...this.form,
          }
          this.submitting = true
          this.$store.dispatch('user/loginBy2FA', loginParams).then(res => {
            if (res && res.login_status !== 'SUCCESS') {
              return this.$emit('auth', res.login_status, this.loginForm)
            }
            this.$emit('after-login')
          }).catch((data) => {
            console.log('login failed', data)
          }).finally(() => {
            this.submitting = false
          })
        } else {
          console.log('error submit!!')
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

.login-form {
  max-width: 400px;

  ::v-deep .el-radio .el-radio__label {
    color: $cursor;
  }

  ::v-deep .el-input .el-input__inner {
    border: 1px solid $light_gray;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  .login-verification-btn {
    .el-button {
      display: block;
      width: 100%;
      margin: 10px 0;
    }
  }
}
</style>
