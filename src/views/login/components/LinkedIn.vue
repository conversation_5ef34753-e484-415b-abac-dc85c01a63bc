<template>
<div class="box">
  <div class="text-center">
  <div class="connect-icon primary"><el-icon name="connection" /></div>
  <div class="mb-8">Authorize DB personal information to the <PERSON>rap<PERSON> tool.</div>
  <el-button type="primary" size="medium" class="btn" @click="sendMessageToTool()">Confirm</el-button>
  </div>
</div>
</template>

<script>
import store from '@/store'
import router from '@/router'

export default {
  name: 'LinkedIn',
  data() {
    return {
      user_info: null,
    }
  },
  mounted() {
    store.dispatch('user/getInfo')
      .then((data) => {
        this.user_info = {
          idToken: store.getters.token,
          uid: data.id,
          email: data.email,
          name: data.name }
      }, () => {
        return router.push({
          name: 'Login',
          search: {
            redirect: this.$route.path,
          },
        })
      })
  },
  methods: {
    sendMessageToTool() {
      window.postMessage({
        type: 'FROM_AUTH_PAGE',
        payload: JSON.stringify(this.user_info),
      }, '*')
      window.addEventListener('message', function(event) {
        // 插件获取身份信息后 关闭窗口
        if (event.data.type === 'FINISHED') {
          window.close()
        }
      })
    },
  },
}
</script>

<style scoped>
.box{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.connect-icon{
  font-size: 38px;
  margin-bottom: 5px;
}

.btn{
  padding-left:32px;
  padding-right:32px;
  margin-top:16px;
  margin-bottom:80px;
}
</style>
