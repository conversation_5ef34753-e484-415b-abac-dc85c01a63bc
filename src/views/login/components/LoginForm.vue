<template>
  <el-form
    ref="loginForm"
    :model="loginForm"
    :rules="loginRules"
    :disabled="formDisabled"
    class="login-form"
    auto-complete="on"
    label-position="left"
  >

    <div class="title-container">
      <h3 class="title">Login</h3>
    </div>

    <el-form-item prop="username">
      <span class="svg-container">
        <svg-icon icon-class="user" />
      </span>
      <el-input
        ref="username"
        v-model="loginForm.username"
        placeholder="Username"
        name="username"
        type="text"
        tabindex="1"
        auto-complete="on"
      />
    </el-form-item>

    <el-form-item prop="password">
      <span class="svg-container">
        <svg-icon icon-class="password" />
      </span>
      <el-input
        :key="passwordType"
        ref="password"
        v-model="loginForm.password"
        :type="passwordType"
        placeholder="Password"
        name="password"
        tabindex="2"
        auto-complete="on"
        @keyup.enter.native="handleLogin"
      />
      <span class="show-pwd" @click="showPwd">
        <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
      </span>
    </el-form-item>

    <div class="text-right mb-3">
      <el-link type="primary" @click="actionForgetPassword">Forget Password?</el-link>
    </div>

    <div class="social-login">
      <el-button
        :loading="loading"
        type="primary"
        size="medium"
        @click.native.prevent="handleLogin"
      >
        Login
      </el-button>
      <el-divider>Login With</el-divider>
      <el-button
        v-for="item in socialLogin"
        :key="item.provider"
        :loading="item.loading"
        type="primary"
        size="medium"
        @click.native.prevent="authenticate(item)"
      >
        <svg-icon :icon-class="item.icon" class="mr-8" />
        {{ item.name }}
      </el-button>
    </div>

  </el-form>
</template>

<script>
import AuthAPI from '@/api/auth'

export default {
  name: 'LoginForm',
  props: {
    userId: Number,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('The password can not be less than 6 digits'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      passwordType: 'password',
      loginForm: {
        username: '',
        password: '',
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
      },
      socialLogin: [
        {
          provider: 'linkedin',
          name: 'Linkedin',
          icon: 'linkedin-social',
          loading: false,
        }, {
          provider: 'google',
          name: 'Google',
          icon: 'google',
          loading: false,
        }, {
          provider: 'facebook',
          name: 'Facebook',
          icon: 'facebook',
          loading: false,
        },
      ],
    }
  },
  computed: {
    formDisabled() {
      return this.loading || this.socialLogin.some(item => item.loading)
    },
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    async handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          try {
            this.loading = true
            const data = await AuthAPI.loginBy2FA(this.loginForm)
            if (data.user) {
              this.$emit('update:userId', data.user.id)
            }
            if (data.login_status !== 'SUCCESS') {
              this.$emit('auth', data.login_status, this.loginForm)
            }
            this.loading = false
          } catch (e) {
            console.log('login failed', e)
            this.loading = false
          }
        } else {
          console.log('error submit!!')
        }
      })
    },
    authenticate(item) {
      item.loading = true
      this.$store.dispatch('user/authenticate', item.provider).then(() => {
        this.$emit('after-login')
        item.loading = false
      }).catch(message => {
        console.log('login failed', message)
        if (message.includes('2FA required')) {
          this.$emit('auth', 'NEED_2FA_VERIFY')
        }
      }).finally(() => {
        item.loading = false
      })
    },
    actionForgetPassword() {
      this.$emit('auth', 'NEED_RESET_PASSWORD')
    },
  },
}
</script>

<style lang="scss" scoped>
$dark_gray: #889aa4;

.login-form {
  max-width: 520px;

  /* reset element-ui css */
  ::v-deep .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    .el-input__inner {
      padding: 12px 5px 12px 15px;
      height: 47px;
      border: 0;
      border-radius: 0;
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  /* custom css */
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .social-login {
    .el-button {
      display: block;
      width: 100%;
      margin: 10px 0;
    }

    ::v-deep .el-divider__text {
      color: white;
      background-color: #2d3a4b;
    }
  }
}
</style>
