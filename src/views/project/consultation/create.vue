<template>
  <div class="app-container">
    <client-warning :client-id="form.client_id" type="projectCreate" />
    <el-form ref="form" class="form-data" :model="form" :rules="rules" label-width="160px" :disabled="submitting">
      <el-form-item label="Name" prop="name">
        <el-input ref="name" v-model="form.name" placeholder="Name" />
      </el-form-item>
      <el-form-item label="Client" prop="client_id">
        <el-select
          v-model="form.client_id"
          filterable
          remote
          reserve-keyword
          placeholder="Search"
          class="remote-select w-300px"
          :class="{'is-reverse':is_visible}"
          :loading="selectLoading"
          :remote-method="getClient"
          no-data-text="No matching data"
          @change="changeClient"
        >
          <el-option v-for="item in clientList.data" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <span v-if="survey_only_client" class="warning pl-5">This Client is Survey Only.</span>
      </el-form-item>
      <el-form-item label="Industry" prop="industry_id">
        <el-select v-model="form.industry_id" filterable placeholder="Search">
          <el-option
            v-for="item in project_options.industry"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Project Type" prop="sub_type">
        <el-select v-model="form.sub_type" placeholder="Type" @change="subTypeSelect">
          <el-option
            v-for="item in project_options.sub_type"
            :key="item.value"
            :label="item.label"
            :disabled="item.value==='Consultation' && survey_only_client"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="needCaseType" key="case_type" label="Case Type" prop="case_type">
        <el-select key="case_type" v-model="form.case_type" placeholder="Case Type" @change="handleChangeCaseType">
          <el-option
            v-for="item in project_options.case_type"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item
                    v-if="isSurveySubType"
                    key="third_party_survey_url"
                    label="Survey URL"
                    prop="third_party_survey_url">
        <el-input v-model="form.third_party_survey_url" />
      </el-form-item>
      <el-form-item v-if="isSurveySubType" key="rate" label="Survey Honorarium" prop="rate">
        <el-input-number v-model="form.rate" :min="0" class="w-120px" />
        <el-select
          v-model="form.rate_currency"
          placeholder="Currency"
          class="w-200px ml-3">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="isSurveySubType" label="Client Charge per Complete" prop="client_charge_per_complete">
        <el-input-number v-model="form.client_charge_per_complete" :min="0" class="w-120px" />
        <el-select
          v-model="form.client_charge_per_complete_currency"
          placeholder="Currency"
          class="w-200px ml-3">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item
      label="Investment Target"
      prop="investment_target_company_ids"
      >
        <div class="multiple-select flex">
          <company-search
                          v-model="form.investment_target_company_ids"
                          multiple
                          stock-label
                          :origin-list="create_company_list"
                          show-link
                          class="multiple-select"
                          placeholder="Investment Target"
                          clearable />
          <el-button
                     v-if="isSSTOrConsultationSubType"
                     type="success"
                     class="ml-3"
                     icon="el-icon-plus"
                     @click="dialogVisible=true">New
          </el-button>
        </div>
        <el-checkbox
                     v-show="isSSTOrConsultationSubType"
                     v-model="investment_target_not_confirmed">
          Investment Target is not confirmed or is not applicable.
        </el-checkbox>
      </el-form-item>
      <el-form-item label="Blind expert profiles" prop="blind_expert_profiles">
        <el-checkbox v-model="form.blind_expert_profiles" :disabled="client_blind_advisor_name" />
      </el-form-item>
      <el-form-item v-if="need_client_office" label="Client Office" prop="client_office_id" required>
        <client-office-search
          v-model="form.client_office_id"
          :client-id="form.client_id"
        />
      </el-form-item>
      <el-form-item v-if="isOutsourcingClient" label="Outsource Region" prop="tsid" required>
        <el-input
          v-model="form.tsid"
          placeholder="This is an outsourcing client. Please enter Project Ts ID."
          class="input-with-select">
          <el-select slot="prepend" v-model="form.exported_to" disabled>
            <el-option v-if="isSGDB" label="CN" value="CN" />
            <el-option v-if="isSGDB" label="US" value="US" />
            <el-option v-else label="SEA" value="SEA" />
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="Project Code" prop="code">
        <el-input
          v-model="form.code"
          placeholder="Project Code"
          size="mini" />
        <el-checkbox
           v-show="need_code_project_types.includes(form.sub_type)"
           v-model="project_code_to_be_provided_later">
          Project code to be provided later
        </el-checkbox>
      </el-form-item>
      <el-form-item label="Start Date" prop="start_date">
        <el-date-picker
          v-model="form.start_date"
          type="date"
          placeholder="Start Date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item key="end_date" label="End Date" prop="end_date">
        <el-date-picker
          v-model="form.end_date"
          type="date"
          placeholder="End Date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="CN DB Project ID" prop="ndb_id">
        <el-input v-model="form.ndb_id" type="number" placeholder="CN DB Project ID" />
      </el-form-item>
      <el-form-item label="Time Zone" prop="zone_id_string">
        <time-zone-select v-model="form.zone_id_string" />
      </el-form-item>
      <client-custom-fields
        v-if="client_custom_fields.length > 0"
        :custom-fields.sync="client_custom_fields"
        :form-data.sync="form.client_custom_fields" />
      <template v-if="need_roles_client">
        <copy-paste-contacts
          ref="copyPasteContacts"
          :client-id="form.client_id"
          :client-contacts="client_contacts_option"
          @quick-add="quickAddClientContact"
          @refresh="getClientProfile"
        />
        <el-form-item label="Project Leader">
          <div class="multiple-select flex">
            <el-select
              v-model="form.project_leader"
              filterable
              multiple
              clearable
              placeholder="Project Leader"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="form.client_id"
              :bcg="need_roles_client"
              bcg-role="PROJECT_LEADER"
              class="ml-3"
              @update="updateClientUser($event, 'project_leader')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Principal">
          <div class="multiple-select flex">
            <el-select
              v-model="form.principal"
              filterable
              multiple
              clearable
              placeholder="Principal"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="form.client_id"
              :bcg="need_roles_client"
              bcg-role="PRINCIPAL"
              class="ml-3"
              @update="updateClientUser($event, 'principal')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Partner">
          <div class="multiple-select flex">
            <el-select
              v-model="form.partner"
              filterable
              multiple
              clearable
              placeholder="Partner"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="form.client_id"
              :bcg="need_roles_client"
              bcg-role="PARTNER"
              class="ml-3"
              @update="updateClientUser($event, 'partner')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Manager Director / Partner (MDP)">
          <div class="multiple-select flex">
            <el-select
              v-model="form.mdp"
              filterable
              multiple
              clearable
              placeholder="Manager Director / Partner (MDP)"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="form.client_id"
              :bcg="need_roles_client"
              bcg-role="MANAGING_DIRECTOR_AND_PARTNER_MDP"
              class="ml-3"
              @update="updateClientUser($event, 'mdp')"
            />
          </div>
        </el-form-item>
      </template>
      <el-form-item label="Client Contacts" prop="contacts">
        <div class="multiple-select flex">
          <el-select
            v-model="form.contacts"
            filterable
            reserve-keyword
            placeholder="This first is the primary contact"
            multiple
            clearable
            :loading="selectLoading"
            class="flex-1"
            no-data-text="No matching data"
          >
            <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <create-client-user
            v-if="form.client_id"
            class="ml-3"
            :client-id="form.client_id"
            :bcg="need_roles_client"
            @update="updateClientUser" />
        </div>
      </el-form-item>
      <el-form-item
v-if="!isSurveySubType && allowEditExternalRecordingContacts"
                    key="external_contacts_for_recording_list"
                    label="External Contacts for Recording"
                    prop="external_contacts_for_recording_list">
        <email-editor :origin-list.sync="form.external_contacts_for_recording_list" />
      </el-form-item>
      <el-form-item label="Client Provided Bridge Link" prop="client_provided_bridge_link">
        <el-input
          v-model="form.client_provided_bridge_link"
          type="textarea" />
      </el-form-item>
      <el-form-item label="Relationship Manager">
        <el-tag v-for="item in client_am_list" :key="item.id" type="info" class="mr-8">{{ item.user.name }}</el-tag>
      </el-form-item>
      <el-form-item label="Project Manager" prop="pm_id">
        <user-search
          v-model="form.pm_id"
          :extra-member="extra_member.project_manager"
          placeholder="Search" />
      </el-form-item>
      <el-form-item label="Support Members" prop="supporter">
        <user-search
          v-model="form.supporter"
          clearable
          multiple
          allow-select-team
          :extra-member="extra_member.support_members"
          role="create-project"
          class="multiple-select"
          placeholder="Search"
          @select="(selected)=>{form.supporter = selected}" />
      </el-form-item>
      <el-form-item label="Tickers">
        <ticker-search v-model="form.tickers" class="multiple-select" multiple />
      </el-form-item>
      <el-form-item label="Proactive Project">
        <el-checkbox v-model="form.is_proactive">Yes</el-checkbox>
      </el-form-item>
      <el-form-item v-if="tpa_client" label="TPA Project" prop="tpa_project">
        <el-select v-model="form.tpa_project">
          <el-option label="Yes" :value="true" />
          <el-option label="No" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="Slack Channel" prop="slack_channel_name">
        <el-checkbox v-model="create_slack_channel" @change="formatSlackChannel()" />
        <div v-if="create_slack_channel">
          <el-input v-model="form.slack_channel_name" placeholder="Slack Channel" />
          <div class="slack-channel-tip">
            Channel name can only contain lowercase letters, numbers, hyphens and underscores.
            (e.g. flexible_packaging_id1873. Once the project is created, we will splice in the project ID after the
            fields you fill in.)
          </div>
        </div>
      </el-form-item>
      <!--      <el-form-item label="Tags">-->
      <!--        <tag-search v-model="form.tags" class="multiple-select" multiple />-->
      <!--      </el-form-item>-->
      <el-form-item label="Request Content" prop="request_content">
        <el-input v-model="form.request_content" placeholder="Request Content" type="textarea" :rows="6" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitting" @click="onSave()">Create</el-button>
        <span v-if="clone_types.length" class="info ml-3">
          <i class="el-icon-success" />
          <i>{{ clone_types.join(', ') }}</i> from
          <router-link
            v-if="clone_data"
            class="link"
            :to="{ name: 'ProjectDetail', params: { project_id: +clone_data.project_id } }">
            {{ clone_data.project_name }}
          </router-link>
          will be cloned.
        </span>
        <span v-if="am_dashboard_advisors.length" class="info ml-3">
          <i class="el-icon-success" />
          <b>{{ am_dashboard_advisors.length }}</b> experts will be attached.
        </span>
      </el-form-item>
    </el-form>

    <el-dialog
      title="Create Company"
      width="600px"
      append-to-body
      :visible.sync="dialogVisible"
      @open="openCompanyDialog">
      <create-company
        ref="create-company"
        is-dialog
        @create="afterCreateCompany" />
    </el-dialog>
  </div>
</template>

<script>

import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import ComplianceAPI from '@/api/compliance'
// import TagSearch from '@/components/SelectRemoteSearch/TagSearch/'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'
import TickerSearch from '@/components/SelectRemoteSearch/TickerSearch'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import clientWarning from '@/components/ComplianceWarning/Index'
import clientCustomFields from '@/components/ClientCustomFields/index'
import { mapGetters, mapState } from 'vuex'
import Mixin from '@/components/FormPage/mixin'
import moment from 'moment-timezone'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
import CreateClientUser from '../components/CreateClientUser'
import createCompany from '@/views/company/update'
import Tool from '@/utils/tool'
import EmailEditor from '@/components/EmailEditor/ToCC'
import ClientOfficeSearch from '@/components/SelectRemoteSearch/ClientOfficeSearch/index.vue'
import CopyPasteContacts from '../components/CopyPasteContacts.vue'

export default {
  name: 'CreateProject',
  components: {
    UserSearch,
    TickerSearch,
    clientWarning,
    clientCustomFields,
    CompanySearch,
    TimeZoneSelect,
    CreateClientUser,
    createCompany,
    EmailEditor,
    ClientOfficeSearch,
    CopyPasteContacts,
  },
  mixins: [Mixin, SelectMixin],
  data() {
    const validateSlackChannel = (rule, value, callback) => {
      const mailReg = /^[a-z0-9_-]*$/g
      setTimeout(() => {
        if (mailReg.test(value)) {
          callback()
        } else {
          callback(new Error('Please enter the correct slack channel'))
        }
      }, 100)
    }
    return {
      form: this.initForm(),
      selectLoading: false,
      clientList: {
        data: [],
        count: 0,
      },
      contactsList: {
        data: [],
        count: 0,
      },
      client_contacts_option: [],
      client_am_list: [],
      survey_only_client: false,
      client_custom_fields: [],
      create_slack_channel: false,
      extra_member: {
        project_manager: [],
        support_members: [],
      },
      clone_data: null,
      clone_types: [],
      rules: {
        name: [{ required: true, validator: this.uniqueValidator, trigger: 'change' }],
        industry_id: [{ required: true, message: 'industry is required', trigger: 'change' }],
        sub_type: [{ required: true, message: 'project type is required', trigger: 'change' }],
        case_type: [{ required: true, message: 'case type is required', trigger: 'change' }],
        third_party_survey_url: [{ required: true, message: 'Survey URL is required', trigger: 'change' }],
        client_id: [{ required: true, message: 'client is required', trigger: 'change' }],
        contacts: [{ required: true, message: 'client contacts is required', trigger: 'change' }],
        pm_id: [{ required: true, message: 'project manager is required', trigger: 'change' }],
        request_content: [{ required: true, message: 'request content is required', trigger: 'change' }],
        slack_channel_name: [{ required: false, validator: validateSlackChannel, trigger: 'change' }],
        client_provided_bridge_link: [{ required: false, trigger: 'blur' }],
        rate: [{ required: false, validator: this.validateRate, trigger: 'change' }],
        code: [{ required: false, validator: this.validateCode, trigger: 'change' }],
        investment_target_company_ids: [
          {
            required: false,
            validator: this.validateInvestmentTarget,
            trigger: 'change',
          }],
        tpa_project: [{ required: true, message: 'TPA Project is required', trigger: 'change' }],
      },
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
      },
      need_code_project_types: [],
      isConsultingFirmClient: false,
      isOutsourcingClient: false,
      project_code_to_be_provided_later: false,
      investment_target_not_confirmed: false,
      client_blind_advisor_name: false,
      need_client_office: false,
      tpa_client: false,
      need_roles_client_ids: [],
      need_roles_client: false,
      dialogVisible: false,
      create_company_list: [],
      am_dashboard_advisors: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'name',
      'project_options',
      'client_options',
      'roles',
    ]),

    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),

    isSSTOrConsultationSubType() {
      return ['Investor Call', 'Consultation'].includes(this.form.sub_type)
    },
    isSurveySubType() {
      return this.form.sub_type === 'Survey'
    },

    needCaseType() {
      return this.form.sub_type === 'Consultation' && this.isConsultingFirmClient
    },

    allowEditExternalRecordingContacts() {
      return this.roles.some(item => {
        return ['Admin', 'ExternalRecordingContacts'].includes(item.role.name)
      })
    },
  },
  created() {
    this.initByRouteQuery()
    this.getNeedRolesClients()
  },
  deactivated() {
    this.resetData()
  },
  methods: {
    initForm() {
      return {
        name: '',
        industry_id: '',
        investment_target_company_ids: [],
        sub_type: 'Consultation',
        case_type: null,
        third_party_survey_url: undefined,
        rate: undefined,
        rate_currency: null,
        client_charge_per_complete: undefined,
        client_charge_per_complete_currency: null,
        code: '',
        client_id: '',
        project_leader: [],
        partner: [],
        principal: [],
        mdp: [],
        contacts: [],
        external_contacts_for_recording_list: [],
        status: '',
        ndb_id: '',
        zone_id_string: Tool.timeZone,
        pm_id: this.$store.state.user.uid,
        supporter: [],
        // tags: [],
        is_proactive: false,
        tpa_project: undefined,
        tickers: [],
        slack_channel_name: '',
        start_date: moment.tz(this.$store.state.app.timeZone)
          .format('YYYY-MM-DD'),
        end_date: undefined,
        request_content: '',
        client_custom_fields: {},
        blind_expert_profiles: false,
        client_office_id: undefined,
        exported_to: '',
        tsid: undefined,
      }
    },
    async initByRouteQuery() {
      this.clone_types = []
      const init_users = [{ user: { name: this.name, id: this.uid }, uid: this.uid }]
      if (this.$route.query.clone) {
        const cloneData = JSON.parse(this.$route.query.clone)
        this.clone_data = cloneData
        this.form.industry_id = cloneData['industry']
        for (const key in cloneData) {
          if (typeof cloneData[key] !== 'boolean') {
            switch (key) {
              case 'sub_type':
                this.form.sub_type = cloneData[key]
                break
              case 'client':
                await this.getClient(cloneData[key].name)
                this.form.client_id = cloneData[key].id
                this.changeClient(this.form.client_id)
                break
              case 'client_contacts':
                this.form.contacts = cloneData[key]
                break
              case 'client_provided_bridge_link':
                this.form.client_provided_bridge_link = cloneData[key]
                break
              case 'project_manager':
                this.form.pm_id = cloneData[key][0].uid
                this.extra_member.project_manager = cloneData[key]
                break
              case 'support_members':
                this.form.supporter = cloneData[key].map(item => item.uid)
                this.extra_member.support_members = cloneData[key]
                break
              case 'code':
                this.form.code = cloneData[key]
                break
            }
            this.subTypeSelect()
          } else {
            const obj = {
              attached_leads: 'Attached Leads',
              attached_advisors: 'Attached Advisors',
              task_angles: 'Task Angles',
              screening_questions: 'Screening Questions',
            }
            this.clone_types.push(obj[key])
          }
        }
      } else if (this.$route.query.am_dashboard) {
        const queryData = JSON.parse(this.$route.query.am_dashboard)
        this.am_dashboard_advisors = queryData.advisors
        this.extra_member.project_manager = [...init_users]
        await this.getClient(queryData.client.name)
        this.form.client_id = queryData.client.id
        this.changeClient(this.form.client_id)
      } else {
        this.extra_member.project_manager = [...init_users]
        await this.getClient()
      }
    },
    formatSlackChannel() {
      this.form.slack_channel_name = this.create_slack_channel ? this.form.name : null
    },

    subTypeSelect() {
      if (this.isSSTOrConsultationSubType) {
        this.rules.investment_target_company_ids = [
          {
            required: true,
            validator: this.validateInvestmentTarget,
            trigger: 'change',
          }]
      } else {
        this.rules.investment_target_company_ids = [
          { required: false },
        ]
      }
      setTimeout(() => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            return
          } else {
            return false
          }
        })
      }, 500)
    },
    async handleSave(val) {
      const params = {
        name: val.name,
        client_id: val.client_id,
        industry: val.industry_id.toUpperCase(),
        investment_target_company_ids: val.investment_target_company_ids,
        sub_type: val.sub_type,
        case_type: this.needCaseType ? val.case_type : null,
        third_party_survey_url: val.third_party_survey_url,
        rate: val.rate,
        rate_currency: val.rate_currency,
        client_charge_per_complete: val.client_charge_per_complete,
        client_charge_per_complete_currency: val.client_charge_per_complete_currency,
        request_content: val.request_content,
        start_date: val.start_date,
        end_date: val.end_date,
        code: val.code,
        is_proactive: val.is_proactive,
        tpa_project: this.tpa_client ? val.tpa_project : undefined,
        ndb_id: val.ndb_id,
        zone_id_string: val.zone_id_string,
        members: [],
        client_custom_fields: val.client_custom_fields,
        external_contacts_for_recording_list: val.external_contacts_for_recording_list,
        client_provided_bridge_link: val.client_provided_bridge_link,
        blind_expert_profiles: val.blind_expert_profiles,
        client_office_id: val.client_office_id,
      }
      if (val.pm_id !== '') {
        params.members.push({
          uid: val.pm_id,
          role: 'PROJECT_MANAGER',
        })
      }

      if (typeof params.rate !== 'number' || params.sub_type !== 'Survey') {
        params.rate = null
        params.rate_currency = null
      }

      val.supporter.forEach(item => {
        params.members.push({ 'uid': item, 'role': 'SUPPORT_MEMBER' })
      })

      if (val.contacts.length) {
        params.project_client_contacts = val.contacts.map(item => {
          return {
            client_contact_id: item,
            client_contact_type: 'GENERAL',
          }
        })
        params.project_client_contacts[0].client_contact_type = 'PRIMARY'
      }

      const bcg_roles = [
        { name: 'project_leader', value: 'PROJECT_LEADER' },
        { name: 'principal', value: 'PRINCIPAL' },
        { name: 'partner', value: 'PARTNER' },
        { name: 'mdp', value: 'MANAGING_DIRECTOR_AND_PARTNER_MDP' },
      ]
      bcg_roles.forEach(role => {
        val[role.name].forEach(item => {
          params.project_client_contacts.push({ client_contact_id: item, client_contact_type: role.value })
        })
      })

      if (val.tickers.length) {
        params.tickers = val.tickers.map(item => {
          return {
            company_id: item,
          }
        })
      }
      if (this.am_dashboard_advisors.length) {
        params.angles = [{
          name: 'Relevant',
          topic: 'Relevant',
          members: [{ user_id: this.uid }],
          tasks: this.am_dashboard_advisors.map(id => ({ advisor_id: id })),
        }]
      }

      if (this.isOutsourcingClient) {
        params.exported_to = val.exported_to
        params.tsid = val.tsid
      }

      if (this.clone_data) {
        await this.cloneProject(params)
      } else {
        await this.createProject(params)
      }
    },

    async cloneProject(params) {
      const extra = 'sq.branches.questions,angles.tasks,tasks,leads'
      const originProject = await ProjectAPI.getProject(this.clone_data.project_id, extra)

      if (this.clone_data['attached_leads']) {
        params.leads = originProject.leads
      }
      if (this.clone_data['task_angles']) {
        params.angles = originProject.angles.map(angle => ({
          id: angle.id,
          rank: angle.rank,
          inquiry_branch_id: angle.inquiry_branch_id,
          members: angle.members,
          name: angle.name,
          project_id: angle.project_id,
          tasks: this.clone_data['attached_advisors'] ? angle.tasks : undefined,
        }))
      }
      if (!this.clone_data['task_angles'] && this.clone_data['attached_advisors']) {
        const advisors = originProject.angles.reduce((sum, angle) => {
          sum = [...sum, ...angle.tasks]
          return sum
        }, [])
        params.leads = params.leads ? [...advisors, ...params.leads] : advisors
      }
      if (this.clone_data['screening_questions']) {
        params.sq_id = originProject.sq_id
        params.sq = originProject.sq
      }
      const cloneForm = {
        from_project: Object.assign({
          id: this.clone_data.project_id,
        }, params),
      }
      await ProjectAPI.cloneProject(this.clone_data.project_id, cloneForm)
        .then(data => {
          this.$router.push({ name: 'ProjectInformation', params: { project_id: data.id } })
        })
    },

    async createProject(params) {
      await this.handleAutoAngleCreate(params)
      await ProjectAPI.createProject(params)
        .then(data => {
          if (this.create_slack_channel) {
            this.updateSlackChannel(data)
          } else {
            this.$router.push({ name: 'ProjectInformation', params: { project_id: data.id } })
          }
        })
    },

    async handleAutoAngleCreate(params) {
      if (params.sub_type === 'Survey') {
        let survey_angle_test_members
        await ComplianceAPI.getAppConfigEntriesByKey('SURVEY_PROJECT_TEST_ANGLE_DEFAULT_MEMBERS')
          .then(data => {
            survey_angle_test_members = data.value.split(',').map(item => {
              return { 'user_id': item }
            })
          })
        params.angles = [
          {
            'name': 'complete',
          },
          {
            'name': 'test',
            'members': survey_angle_test_members,
          },
        ]
      }

      if (params.case_type === 'DUE_DILIGENCE') {
        const support_members = params.members.filter(item => item.role === 'PROJECT_MANAGER')
          .map(item => ({ 'user_id': item.uid }))
        params.angles = [
          {
            'name': 'Competitors/Formers',
            'members': support_members,
          },
          {
            'name': 'Customers',
            'members': support_members,
          },
        ]
      }
    },
    updateSlackChannel(val) {
      const params = {
        slack_channel_name: `${this.form.slack_channel_name}_id${val.id}`,
      }
      return ProjectAPI.updateProjectSlackChannel(val.id, params).then((data) => {
        this.$router.push({ name: 'ProjectInformation', params: { project_id: data.id } })
      })
    },

    getClient(val) {
      const params = {

        name_like: val || undefined,
        // status: 'EXECUTE',
        extra: 'client_contacts.contact_infos_without_mosaic,account_managers.user,contracts,compliance_preference,preference',
        // 不对客户状态做限制
        // 'contracts.approval_status_in': 'PENDING_APPROVAL,APPROVED',
        // compliance_status: 'APPROVED',
        page: 1,
        size: 20,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList.data = data.list
          this.clientList.count = data.count
        })
    },

    getClientProfile() {
      const extra = 'client_contacts.contact_infos_without_mosaic,account_managers.user,contracts,compliance_preference,preference'
      this.$refs['copyPasteContacts'].loading = true
      return ClientAPI.getClient(this.form.client_id, extra)
        .then(data => {
          this.client_contacts_option = data.client_contacts || []
          this.$nextTick(() => {
            this.$refs['copyPasteContacts'].handleParseEmails()
          })
        })
        .finally(() => {
          this.$refs['copyPasteContacts'].loading = false
        })
    },

    changeClient(val) {
      this.client_contacts_option = []
      this.form.contacts = []
      this.form.client_office_id = undefined
      this.customSupport()
      this.getClientCustom(val)
      this.clientList.data.forEach(item => {
        if (item.id === val) {
          this.client_contacts_option = item.client_contacts || []
          this.client_am_list = item.account_managers || []
          this.survey_only_client = onlySurveyContract(item.contracts)
          this.survey_only_client ? this.form.sub_type = 'Survey' : ''
          this.need_code_project_types = needCodeTypes(item)
          this.isConsultingFirmClient = item.type === 'CONSULTING_FIRM'
          this.client_blind_advisor_name = item.preference?.blind_expert_names_in_to_client_and_portal
          this.form.blind_expert_profiles = this.client_blind_advisor_name
          this.isOutsourcingClient = item.preference?.is_outsource_client
          this.form.exported_to = item.preference?.outsource_region
          this.need_client_office = item.preference?.enable_client_office_in_project
          this.tpa_client = item.preference?.tpa_client
          this.need_roles_client = item.preference?.preferred_workflow === 'BCG' || this.need_roles_client_ids.includes(val)
        }
      })

      // client 有且仅有 生效的survey合同时，只允许创建survey项目
      function onlySurveyContract(contracts) {
        if (!contracts) return false
        const effective_contracts = contracts.filter(item => item.effective_status === 'EFFECTIVE').length
        const effective_survey_contract = contracts.filter(
          item => item.effective_status === 'EFFECTIVE' && item.applicable_project_types === 'SURVEY').length
        return !!effective_contracts && effective_contracts === effective_survey_contract
      }

      // client 有且仅有 生效的survey合同时，只允许创建survey项目
      function needCodeTypes(data) {
        if (!data.compliance_preference?.rule.compliance_rules?.project_creation_rules?.remind_user_to_add_a_project_case_code) return []
        return data.compliance_preference.rule.compliance_rules.project_creation_rules.remind_user_to_add_a_project_case_code_project_types
      }
    },

    updateClientUser(val, field) {
      const client = this.clientList.data.find(item => item.id === this.form.client_id)
      if (client) {
        client.client_contacts.push(val)
        this.client_contacts_option = client.client_contacts || []
        this.client_am_list = client.account_managers || []
        this.form.contacts.push(val.id)
        if (field) this.form[field].push(val.id)
      }
    },

    getClientCustom(client_id) {
      const params = {
        type: 'PROJECT',
        client_id: client_id,
      }

      return ClientAPI.getCustomFields(params).then(data => {
        this.client_custom_fields = data.list.map(item => {
          this.form.client_custom_fields[item.field_name] = null
          return item
        })
      })
    },

    handleChangeCaseType(val) {
      this.customSupport()
      if (val === 'PROPOSAL_OR_LOP') {
        this.form.is_proactive = true
      }
    },

    // https://www.notion.so/capvision/Create-Edit-Project-Add-user-to-Support-f8ad835a1d16498285cfec38df2f5b7a?pvs=4
    // 临时修改 后续通过规则完善
    customSupport() {
      if (this.isSGDB) return
      const need_push_member = [115, 98].includes(this.form.client_id) && this.needCaseType && this.form.case_type === 'DUE_DILIGENCE'
      if (need_push_member && !this.form.supporter.includes(427)) {
        this.form.supporter.push(427)
        const member = {
          uid: 427,
          user: {
            id: 427,
            name: 'Jasper Grothues',
          },
        }
        this.extra_member.support_members.push(member)
      }
    },

    afterCreateCompany(val) {
      this.create_company_list.push(val)
      this.form.investment_target_company_ids.push(val.id)
      this.dialogVisible = false
    },

    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },

    validateRate(rule, value, callback) {
      setTimeout(() => {
        if (typeof value !== 'number') {
          value = null
          this.form.rate_currency = null
          callback()
        } else {
          if (!this.form.rate_currency) {
            callback(new Error('Please enter the rate currency'))
          } else {
            callback()
          }
        }
      }, 100)
    },

    // 客户 compliance rules 勾选对应需要user填写code时，必填；如果用户勾选project_code_to_be_provided_later，可以不必填
    validateCode(rule, value, callback) {
      setTimeout(() => {
        if (value || this.need_code_project_types.length < 1 || this.project_code_to_be_provided_later) {
          callback()
        } else {
          if (this.need_code_project_types.includes(this.form.sub_type) && !value) {
            callback(new Error('Please provide a project/case code'))
          } else {
            callback()
          }
        }
      }, 100)
    },

    // 如果用户勾选investment_target_not_confirmed，可以不必填
    validateInvestmentTarget(rule, value, callback) {
      setTimeout(() => {
        if (value.length > 0 || this.investment_target_not_confirmed) {
          callback()
        } else {
          callback(new Error('Investment Target is required'))
        }
      }, 100)
    },

    uniqueValidator(rule, value, callback) {
      if (value === '') {
        callback(new Error('name is required'))
      } else {
        callback()
        // 要求不验证名字重复
        // const data = { module: 'project', params: { name: value } }
        // debounce(() => {
        //   ToolAPI.checkName(data)
        //     .then((data) => {
        //       if (data) {
        //         callback(new Error('The name change already exists, please fill it in again'))
        //       } else {
        //         callback()
        //       }
        //     })
        // }, 500)
      }
    },

    quickAddClientContact(val) {
      const keys = this.client_options.contact_bcg_roles.map(bcg_role => bcg_role.key)
      keys.forEach(key => {
        if (this.form[key]) this.form[key] = []
      })

      val.forEach(item => {
        item.contact.roles?.forEach(role => {
          const bcg_role = this.client_options.contact_bcg_roles.find(bcg_role => bcg_role.value === role)
          if (bcg_role?.key) {
            this.form[bcg_role.key].push(item.contact.id)
            this.form[bcg_role.key] = Array.from(new Set(this.form[bcg_role.key]))
          }
        })
      })
      this.form['contacts'] = [...this.form['contacts'], ...val.map(item => item.contact.id)]
      this.form['contacts'] = Array.from(new Set(this.form['contacts']))
    },

    getNeedRolesClients() {
      return ComplianceAPI.getAppConfigEntriesByKey('DUE_DILEGENCE_USER_ROLES')
        .then(data => {
          if (data) {
            this.need_roles_client_ids = data.value.split(',').map(item => +item)
          }
        })
    },
  },
}
</script>
<style scoped>
.tooltip-icon {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
  vertical-align: middle;
}

.slack-channel-tip {
  color: #909399;
  line-height: 14px;
  margin-top: 5px;
}

.input-with-select {
  display: inline-table;

  .el-input-group__prepend {
    width: 95px;
    background-color: #fff;
  }
}
</style>
