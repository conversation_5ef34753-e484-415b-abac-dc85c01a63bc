<template>
  <div class="app-container">
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div ref="tool_switch_box" class="position-relative tool-switch">
        <transition-group name="fade">
          <div
            v-show="!selectedLeads.length"
            ref="tool_switch_page1"
            :key="1"
            class="tool-switch-page1">
            <refresh-button class="filter-item" @action="loadData" />
            <user-search
              v-model="searchQuery.member_ids_format"
              multiple
              clearable
              allow-select-team
              collapse-tags
              :disabled="isGKM"
              :extra-member="[{user:{name:name,id:uid},uid:uid}]"
              placeholder="Project Manager"
              class="w-200px filter-item"
              @init-completed="userSearchInitCompleted"
              @change="actionSearch"
            />
            <el-input
              v-model="searchQuery.name_like"
              placeholder="Project Name"
              class="w-200px filter-item"
              clearable
              @input="actionSearch" />
            <ProjectCodeSearch v-model="searchQuery.code" class="filter-item w-200px" @change="actionSearch" />
            <el-input
              v-model="searchQuery.tsid"
              placeholder="Project Ts ID"
              class="w-200px filter-item"
              clearable
              @input="actionSearch" />
            <el-select
              v-model="searchQuery.sub_type"
              placeholder="Type"
              class="w-200px filter-item"
              clearable
              @change="actionSearch">
              <el-option
                v-for="item in project_options.sub_type"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-select
              v-model="searchQuery.case_type"
              placeholder="Case Type"
              class="w-200px filter-item"
              clearable
              @change="actionSearch">
              <el-option
                v-for="item in project_options.case_type"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-select
              v-model="searchQuery.industry_in"
              class="filter-item w-200px"
              placeholder="Industry"
              clearable
              multiple
              @change="actionSearch"
            >
              <el-option
v-for="item in project_options.industry"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
            <client-search
v-model="searchQuery.client_id"
                           placeholder="Client"
                           class="w-200px filter-item"
                           @change="actionSearch" />
            <el-select
              v-model="searchQuery.client_type"
              placeholder="Client Type"
              class="w-200px filter-item"
              clearable
              @change="actionSearch">
              <el-option
                v-for="item in client_options.type"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
            <el-input
              v-if="isSGDB"
              v-model="searchQuery.outsource_client_name_contains"
              placeholder="CN Client"
              class="w-200px filter-item"
              @input="actionSearch" />
            <el-button type="success" icon="el-icon-plus" class="filter-item" @click="actionGotoCreate()">New
            </el-button>
          </div>
          <div
            v-show="selectedLeads.length"
            ref="tool_switch_page2"
            :key="2"
            class="tool-switch-page2">
            <el-select
              v-model="batchChangeStatus"
              class="w-200px filter-item"
            >
              <el-option
                v-for="item in project_options.status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.value==='CLOSED'"
              />
            </el-select>
            <el-button
              :disabled="!batchChangeStatus.length"
              :loading="statusLoading"
              type="primary"
              class="filter-item"
              @click="actionBatchUpdateStatus">
              Change Status{{ ` (${selectedLeads.length}) ` }}
            </el-button>
          </div>
        </transition-group>
      </div>
      <div class="status-tags filter-item tabs-stackOverflow">
        <template v-for="(item, index) in filterStatusOptions">
          <el-tag
            :key="index"
            :class="{active: (searchQuery.status === item.value) }"
            @click.native="actionSearchByStatus(item.value)"
          >{{ item.label }}
            <span class="status-count">{{ getAggregationsCount(item.value, 'status', aggregations) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      ref="consultationTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['consultationTable'])"
      @selection-change="handleSelectionChange">
      >
      <el-table-column
        type="selection"
        width="45" />
      <el-table-column
        label="ID"
        prop="id"
        width="70" />
      <el-table-column
        label="Name"
      >
        <template slot-scope="scope">
          <router-link-project :data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        width="85"
      >
        <template slot-scope="scope">
          <el-dropdown class="project-status-dropdown" @command="actionUpdateStatus">
            <span :class="scope.row.status | getProjectStausClass(project_options.status)">
              {{ scope.row.status | getLabel(project_options.status) }}<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in project_options.status"
                :key="index"
                :command="{
                  id:scope.row.id,
                  status:item.value,
                  name:scope.row.name,
                  sent_close_email_advisor_ids:scope.row.sent_close_email_advisor_ids}"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
      >
        <template slot-scope="scope">
          {{ scope.row.members | getMembers('PROJECT_MANAGER') }}
        </template>
      </el-table-column>
      <el-table-column
        label="Client"
      >
        <template v-if="scope.row.client != null" slot-scope="scope">
          <router-link-client :data="scope.row.client" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="isSGDB"
        label="CN Client"
        prop="outsource_client_name"
      >
        <template v-slot="{row}">
          <span v-if="row.exported_to === 'CN'">
            {{ row.outsource_client_name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Primary Contact"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.display_data.project_client_contacts }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
      >
        <template slot-scope="scope">
          {{ scope.row.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Code"
      >
        <template slot-scope="scope">
          {{ scope.row.code }}
        </template>
      </el-table-column>
      <el-table-column label="Project Start Time">
        <template slot-scope="scope">
          {{ scope.row['create_at'] | momentFormat('MM/DD/YYYY hh:mm A') }}
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
        prop="sub_type"
        width="110">
        <template slot-scope="scope">
          {{ scope.row['sub_type'] | getLabel(project_options.sub_type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Case Type"
        prop="case_type"
        width="110">
        <template slot-scope="scope">
          {{ scope.row['case_type'] | getLabel(project_options.case_type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Ts ID"
        prop="tsid"
        width="90" />
    </el-table>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="loadData" />

    <close-project ref="closeProject" @update="loadData" />
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkProject from '@/components/RouterLink/Project'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import { mapGetters, mapState } from 'vuex'
import { debounce } from '@/utils/tool'
import _ from 'lodash'
import RouterTools from '@/utils/tool-router'
import RefreshButton from '@/components/RefreshButton/index'
import CloseProject from '@/views/project/components/CloseProject'
import ProjectCodeSearch from '@/components/SelectRemoteSearch/ProjectCodeSearch'

export default {
  name: 'ProjectList',
  components: {
    CloseProject,
    RefreshButton,
    UserSearch,
    Pagination,
    RouterLinkClient,
    RouterLinkProject,
    ClientSearch,
    ProjectCodeSearch,
  },
  mixins: [SelectMixin],
  data() {
    return {
      firstVisit: true,
      routerName: this.$route.name,
      loading: false,
      selectLoading: false,
      statusLoading: false,
      needAggregationHandle: true,
      total: 0,
      tableData: [],
      itemData: null,
      searchQuery: {
        page: 1,
        size: 10,
        client_id: undefined,
        name_like: undefined,
        tsid: undefined,
        code: undefined,
        sub_type: undefined,
        outsource_client_name_contains: undefined,
        industry_in: [],
        member_ids: [],
        member_ids_format: [],
        // member_role: undefined,
        status: 'ACTIVE',
        status_in: undefined,
        extra: 'members,members.user,client,project_client_contacts,project_client_contacts.client_contact,project_options',
        aggregation: 'status',
        type: 'CONSULTATION',
        sort: 'id desc',
      },
      aggregations: {
        status: [],
      },
      selectedLeads: [],
      batchChangeStatus: 'ACTIVE',
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    ...mapGetters([
      'project_options',
      'client_options',
      'uid',
      'name',
      'roles',
    ]),
    isGKM() {
      return this.roles.some(item => {
        return ['GKM'].includes(item.role.name)
      })
    },
    filterStatusOptions() {
      const result = _.cloneDeep(this.project_options.status)
      result.push({ value: 'TOTAL', color: 'primary', label: 'Total' })
      return result
    },
  },
  mounted() {
    const params = RouterTools.resolveRouteQuery(this.$route, this.searchQuery, ['member_ids_format'])

    this.firstVisit = Object.keys(params).length === 0

    if (!this.firstVisit) {
      this.loadData()
    }
  },
  activated() {
    this.mounted()
  },
  methods: {
    loadData() {
      this.loading = true
      const url_params = _.cloneDeep(this.searchQuery)
      delete url_params.status_in
      delete url_params.status
      delete url_params.member_ids

      RouterTools.saveQueryToRouter(url_params)

      const searchQuery = this.formatSearchParams(Object.assign({}, this.searchQuery))
      delete searchQuery.member_ids_format
      return ProjectAPI.getProjectList(searchQuery)
        .then(data => {
          formatProjectList(data['list'])

          this.tableData = data['list']
          this.total = data['count']

          if (this.needAggregationHandle && data['aggregations']) {
            this.calAggregationsCount(data['aggregations'].status)
            this.aggregations.status = data['aggregations'].status
          }
        })
        .finally(() => {
          this.loading = false
        })

      function formatProjectList(list) {
        list.forEach(item => {
          item.display_data = {
            project_client_contacts: getClientContacts(item),
          }
        })

        function getClientContacts(data) {
          if (!data.project_client_contacts) {
            return
          }

          return data
            .project_client_contacts
            .map(item => {
              return item.client_contact_type === 'PRIMARY' &&
                item.client_contact &&
                item.client_contact.name || ''
            })
            .join('')
        }
      }
    },
    formatSearchParams(data) {
      if (data.status === 'TOTAL') {
        data.status = null
      }

      data.name_like = data.name_like || undefined
      data.client_id = data.client_id || undefined
      data.sub_type = data.sub_type || undefined
      data.case_type = data.case_type || undefined
      data.industry_in = data.industry_in.join() || undefined
      data['client.type'] = data.client_type || undefined
      data.outsource_client_name_contains = data.outsource_client_name_contains || undefined
      data.tsid = data.tsid || undefined

      // data.member_role = data.member_ids.length ? 'PROJECT_MANAGER' : undefined

      // 可同时筛选user和team,处理参数
      if (data.member_ids_format.length) {
        let ids = []
        data.member_ids_format.forEach(item => {
          if (typeof item === 'string' && item.includes('team')) {
            ids = ids.concat(item.split('-').slice(1).map(item => +item))
          } else {
            ids.push(item)
          }
        })
        data.member_ids = ids.filter(item => item).join()
      } else {
        data.member_ids = undefined
      }

      return data
    },
    actionGotoCreate() {
      this.$router.push({ name: 'CreateProject' })
    },

    actionUpdateStatus(params) {
      if (params.status === 'CLOSED') {
        this.$refs.closeProject.openDialog(params)
        return
      }
      return ProjectAPI.updateProject(params).then(data => {
        this.needAggregationHandle = true
        this.tableData.forEach(item => {
          if (item.id === data.id) {
            item.status = data.status
          }
        })
        this.loadData()
        this.$message({
          message: 'success',
          type: 'success',
        })
      })
    },

    actionBatchUpdateStatus() {
      const params = this.selectedLeads.map(item => {
        return { id: item.id, status: this.batchChangeStatus }
      })
      if (this.batchChangeStatus === 'CLOSED') return this.actionBatchCloseProject(this.selectedLeads)
      this.statusLoading = true
      return ProjectAPI.updateBatchProject(params)
        .then(() => {
          this.selectedLeads = []
          this.loadData()
        })
        .finally(() => {
          this.statusLoading = false
        })
    },
    actionBatchCloseProject(selectedIds) {
      const params = selectedIds.map(item => item.id).join()
      this.$confirm(
        'After closing the project, the experts involved in the project will be notified by email.', 'Notice', {
          confirmButtonText: 'Confirm',
          type: 'warning',
        }).then(() => {
        this.statusLoading = true
        return ProjectAPI.closeProjects(params)
          .then(() => {
            this.selectedLeads = []
            this.loadData()
          })
          .finally(() => {
            this.statusLoading = false
          })
      })
    },

    getAggregationsCount(val, type, aggregations) {
      let result = 0
      if (!aggregations) return result
      aggregations[type].forEach(item => {
        if (item.value === val) {
          result = item.count
        }
      })
      return result
    },
    calAggregationsCount(val) {
      let total_count = 0
      val.forEach(item => {
        total_count += item.count
      })

      val.push({
        value: 'TOTAL',
        count: total_count,
      })
    },
    actionSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.searchQuery.status = 'TOTAL'
        this.needAggregationHandle = true
        this.loadData()
      }, 500)
    },
    actionSearchByStatus(status) {
      this.searchQuery.status = status
      this.searchQuery.page = 1
      this.needAggregationHandle = false
      this.loadData()
    },
    handleSelectionChange(val) {
      this.selectedLeads = val
    },
    userSearchInitCompleted() {
      if (this.firstVisit) {
        this.searchQuery.member_ids_format = (this.uid ? [this.uid] : [])
        this.loadData()
          .then(data => {
            this.needAggregationHandle = false
          })
        this.firstVisit = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>

@media screen and (max-width: 1366px) {
  .w-200px {
    width: 180px;
  }
}

.status-tags {
  margin-left: auto;
}

.icon-size {
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
}

.project-status-dropdown {
  font-size: 12px;
  cursor: pointer;
}

.tool-switch {
  min-width: 537px;
  @media screen and (max-width: 1366px) {
    min-width: 500px;
  }
}

.tool-switch-page1, .tool-switch-page2 {
  // min-height: 38px;
}

.status-count {
  display: inline-block;
  opacity: 0.6;
  transform: scale(0.9);
}
</style>
