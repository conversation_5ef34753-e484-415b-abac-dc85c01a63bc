<template>
  <div class="flex">
    <div class="mr-8">Alternative Hosts:</div>
    <el-select
      :loading="loading"
      multiple
      filterable
      value-key="id"
      placeholder="Search"
      no-data-text="No matching data"
      class="flex-1"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in email_list"
        :key="item.email"
        :label="item.name"
        :value="item.email">
        {{ item.name }}
      </el-option>
    </el-select>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
export default {
  name: 'AlternativeHosts',
  props: {
    arrangement: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      email_list: [],
      loading: false,
    }
  },
  mounted() {
    this.loading = true
    ProjectAPI.getAlternativeHostsEmailList()
      .then(data => {
        this.email_list = data
      })
      .finally(() => {
        this.loading = false
      })
  },
}
</script>

<style scoped>

</style>
