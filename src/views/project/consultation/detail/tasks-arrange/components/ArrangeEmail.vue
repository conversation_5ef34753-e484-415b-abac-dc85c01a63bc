<template>
  <el-tabs v-loading="loading" value="advisor" type="card" class="mt-8">
    <el-tab-pane label="Advisor" name="advisor">
      <div class="mb-8 flex align-items-center">
        <el-checkbox
          v-model="item.send_mail_to_advisor"
          class="mr-normal"
        >
          Send Mail
        </el-checkbox>
        <el-checkbox
          v-model="item.send_calendar_to_advisor"
          class="mr-8"
          @change="changeEmailTemplate(item.advisor_email.template, item, true, item.send_calendar_to_advisor)">
          Send Calendar
        </el-checkbox>
        <slot v-if="isLoopup">
          <span class="pr-8">Advisor Location:</span>
          <el-select
            v-model="item.loopUp_location_advisor"
            value-key="id"
            filterable
            @change="changeEmailTemplate(item.advisor_email.template, item, true, item.send_calendar_to_advisor)">
            <el-option
              v-for="location in arrangeOptions.loopUp_location_list"
              :key="location.id"
              :value="location"
              :label="location['area_name']" />
          </el-select>
          <span class="text-info text-small ml-8"><el-icon name="warning-outline" /> LoopUp "Dial-In Code" is base on this location.</span>
        </slot>
        <slot v-if="!isLoopup && arrangedItem">
          <span>Calendar Invite Location:</span>
          <el-input
            v-model="item.task.arrange_advisor_calendar_location"
            class="mr-8 inline-block w-200px" />
        </slot>

        <div
          v-if="hasTCTemplate"
          class="inline-block ml-normal">
          <span class="info">TC:</span>
          <el-input
            v-model="item.tc_rate"
            type="number"
            :step="150"
            :min="0"
            placeholder="TC Rate"
            class="w-120px" />
          <el-select
            v-model="item.tc_rate_currency"
            placeholder="Currency"
            class="w-120px">
            <el-option
              v-for="i in options.currency"
              :key="i"
              :label="i"
              :value="i" />
          </el-select>
          <el-checkbox
            v-if="latestDefaultTcHasAttachments"
            v-model="item.tc_with_attachments"
            class="ml-8"
          >
            Attachments:
            <el-link
              type="primary"
              :href="latestDefaultTc.templates[0].file_url">
              {{ latestDefaultTc.templates[0].file_name }}
            </el-link>
          </el-checkbox>
        </div>
      </div>
      <div v-if="arrangedItem && item.task.arrange_bridge_type && !isLoopup" class="flex">
        <span>Conference Bridge Information:</span>
        <tinymce
          v-model="item.task.arrange_advisor_bridge_info_str"
          :height="100"
          p-origin-margin
          :toolbar="toolbar"
          class="bridge-detail flex-1" />
      </div>
      <email-editor
        v-if="showAdvisorTemplates(item)"
        ref="email-editor-advisor"
        only-to
        :to-cc-options="item.task.advisor_contact_email_list"
        :options="advisorTemplates"
        :template="item.advisor_email.template"
        :email="item.advisor_email.email"
        @change="(val) => {changeEmailTemplate(val, item, true,true)}" />
      <div class="display-none">
        <sq-copy
          v-if="item.task.latest_submitted_sq"
          :id="`sq-copy-advisor-${item.task.id}`"
          :data="item.task.latest_submitted_sq" />
      </div>
    </el-tab-pane>
    <el-tab-pane label="Client" name="client">
      <el-alert
        v-if="sendToClientThroughCapvisionPro"
        title="The system will not send emails and calendar to this client. Please copy the email content yourself and send the email through the 'capvision-pro.com' mailbox."
        type="warning"
        show-icon
        :closable="false"
        class="mb-8 mt-8" />
      <div class="mb-8 flex align-items-center">
        <el-checkbox
          v-if="isMcKinsey"
          v-model="item.send_mail_to_client"
          @change="changeEmailTemplate(item.client_email.template, item, false, item.send_calendar_to_client)"
        >
          Send Mail
        </el-checkbox>
        <el-checkbox
          v-model="item.send_calendar_to_client"
          :disabled="item.task.project.tpa_project"
          class="pr-8"
          @change="changeEmailTemplate(item.client_email.template, item, false, item.send_calendar_to_client)"
        >
          Send Calendar
        </el-checkbox>
        <slot v-if="isLoopup">
          <span class="pr-8">Client Location:</span>
          <el-select
            v-model="item.loopUp_location_client"
            value-key="id"
            multiple
            filterable
            @change="changeEmailTemplate(item.client_email.template, item, false, item.send_calendar_to_client)">
            <el-option
              v-for="location in arrangeOptions.loopUp_location_list"
              :key="location.id"
              :value="location"
              :label="location['area_name']" />
          </el-select>
          <span class="text-info text-small ml-8"><el-icon name="warning-outline" /> LoopUp "Dial-In Code" is base on this location.</span>
        </slot>
        <slot v-if="!isLoopup && arrangedItem">
          <span class="pr-8">Calendar Invite Location:</span>
          <el-input
            v-if="!isLoopup"
            v-model="item.task.arrange_contact_calendar_location"
            class="inline-block w-200px" />
        </slot>
      </div>
      <div v-if="arrangedItem && item.task.arrange_bridge_type && !isLoopup" class="flex">
        <span>Conference Bridge Information:</span>
        <tinymce
          v-model="item.task.arrange_detail_str"
          :height="100"
          :toolbar="toolbar"
          p-origin-margin
          class="bridge-detail flex-1" />
      </div>
      <email-editor
        v-if="showClientTemplates(item)"
        ref="email-editor-client"
        :client-id="item.task.client_id"
        :options="customClientTemplates"
        :to-cc-options="item.task.project_client_contact_email_list"
        :template="item.client_email.template"
        :email="item.client_email.email"
        :readonly="item.task.project.tpa_project"
        @change="(val)=> {changeEmailTemplate(val, item, false,true)}" />
      <div class="display-none">
        <profile-format-custom
          :id="`profile-format-${item.task.id}`"
          no-questions
          no-availability
          :data="item.task"
          :custom-time-zone="selectTimeZone" />
        <sq-copy
          v-if="item.task.latest_submitted_sq"
          :id="`sq-copy-client-${item.task.id}`"
          show-answer
          :short-profile="item.client_short_profile || isMcKinsey"
          :data="item.task.latest_submitted_sq" />
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import EmailEditor from '@/components/EmailEditor'
import ProfileFormatCustom from '@/components/FormatProfile/ProfileIndex'
import SqCopy from '@/components/Question/SqQuestionCopy'
import mixin from './mixin'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'ArrangeEmail',
  components: { EmailEditor, SqCopy, ProfileFormatCustom, Tinymce },
  mixins: [mixin],
  data() {
    return {
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      loading: false,
      send_calendar_to_client: false,
      send_calendar_to_advisor: false,
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
      },
    }
  },

  computed: {
    isLoopup() {
      return this.item.task.arrange_bridge_type === 'LOOPUP'
    },
  },

  mounted() {
    this.getEmailTemplateList()
  },

}
</script>

<style lang="scss" scoped>
.bridge-detail {
  //width: 99%;
  display: inline-block;
  position: relative;
  margin-left: 8px;
  margin-right:10px;
}

.bridge-detail:before {
  position: absolute;
  top: -4px;
  left: -8px;
  content: "*";
  color: #f56c6c;
}

.pr-8 {
  padding-right: 8px;
}

.mr-8 {
  margin-right: 8px;
}

::v-deep .mce-menubar {
  border: 0;
}

::v-deep .mce-statusbar .mce-container-body {
  display: none;
}
</style>
