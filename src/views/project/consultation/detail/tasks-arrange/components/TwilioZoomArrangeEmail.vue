<template>
  <div>
    <div v-if="isZoom" class="zoom-setting">
      <el-checkbox v-model="item.conference_arrangement.enable_waiting_room">
        Enable Waiting room
      </el-checkbox>
      <div>
        <el-checkbox
          v-model="item.conference_arrangement.enable_as_co_host"
          class="mr-8"
        >
          Enable as Co-Host
        </el-checkbox>
        <el-select
          v-if="item.conference_arrangement.enable_as_co_host"
          v-model="item.conference_arrangement.co_host_roles"
          :loading="loading"
          multiple
          filterable
          default-first-option
          value-key="id"
          placeholder="Search"
          class="mr-8"
          no-data-text="No matching data"
        >
          <el-option
            v-for="item in options.conference_role"
            :key="item.value"
            :label="item.label"
            :disabled="item.value === 'EXPERT'"
            :value="item.value">
            {{ item.label }}
          </el-option>
        </el-select>
      </div>
    </div>
    <el-tabs v-loading="loading" value="advisor" type="card" class="mt-8">
      <el-tab-pane label="Advisor" name="advisor">
        <div class="mb-8 flex align-items-center">
          <el-checkbox
            v-model="item.send_mail_to_advisor"
            class="mr-normal"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.send_calendar_to_advisor"
            class="mr-8"
            @change="changeEmailTemplateForTwilioZoom(item.advisor_email.template, item, 'advisor_email', item.send_calendar_to_advisor)">
            Send Calendar
          </el-checkbox>
          <slot v-if="arrangedItem">
            <span>Calendar Invite Location:</span>
            <el-input
              v-model="item.task.arrange_advisor_calendar_location"
              class="mr-8 inline-block w-200px" />
          </slot>
          <el-checkbox
            v-model="item.conference_arrangement.enable_auto_dial_expert"
            :disabled="!item.conference_arrangement.arrange_expert_phone"
            class="mr-8">
            Auto dial out to expert at start time
          </el-checkbox>

          <span v-if="item.conference_arrangement.enable_auto_dial_expert">
            <el-input v-if="item.conference_arrangement.arrange_expert_location && isVettingCallPage" v-model="item.conference_arrangement.arrange_expert_location" class="w-200px" disabled />
            <el-input v-if="item.conference_arrangement.arrange_expert_phone" v-model="item.conference_arrangement.arrange_expert_phone" class="w-200px ml-8" disabled />
          </span>
          <div
            v-if="hasTCTemplate"
            class="inline-block ml-normal">
            <span class="info">TC:</span>
            <el-input
              v-model="item.tc_rate"
              type="number"
              :step="150"
              :min="0"
              placeholder="TC Rate"
              class="w-120px" />
            <el-select
              v-model="item.tc_rate_currency"
              placeholder="Currency"
              class="w-120px">
              <el-option
                v-for="i in options.currency"
                :key="i"
                :label="i"
                :value="i" />
            </el-select>
            <el-checkbox
              v-if="latestDefaultTcHasAttachments"
              v-model="item.tc_with_attachments"
              class="ml-8"
            >
              Attachments:
              <el-link
                type="primary"
                :href="latestDefaultTc.templates[0].file_url">
                {{ latestDefaultTc.templates[0].file_name }}
              </el-link>
            </el-checkbox>
          </div>
        </div>
        <email-editor
          v-if="showAdvisorTemplates(item)"
          ref="advisor_email"
          :options="advisorTemplates"
          :to-cc-options="item.task.advisor_contact_email_list"
          :template="item.advisor_email.template"
          :email="item.advisor_email.email"
          @change="(val) => {changeEmailTemplateForTwilioZoom(val, item, 'advisor_email',true)}" />
        <div class="display-none">
          <sq-copy
            v-if="item.task.latest_submitted_sq"
            :id="`sq-copy-advisor-${item.task.id}`"
            :data="item.task.latest_submitted_sq" />
          <ca-copy
            v-if="item.task.latest_submitted_pre_ca && item.conference_arrangement.conference_invitees.includes('CLIENT_COMPLIANCE')"
            :id="`ca-copy-advisor-${item.task.id}`"
            :data="item.task.latest_submitted_pre_ca.inquiry_instance"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="Client" name="client">
        <CapvisionProAlert v-if="sendToClientThroughCapvisionPro" />
        <div v-if="arrangedItem">
          <span>Calendar Invite Location:</span>
          <el-input
            v-model="item.task.arrange_contact_calendar_location"
            class="inline-block w-200px" />
        </div>
        <div class="mb-8">
          <el-checkbox
            v-model="item.conference_arrangement.enable_record"
            :disabled="!item.record_and_transcript_can_check"
          >
            Requires a Call Recording
            <el-tooltip
              v-if="item.compliance_rules_disable_transcription_record"
              effect="dark"
              placement="top">
              <div slot="content" class="w-300px">The client’s legal rules prohibit transcription and recording.(Transcript/Notetaking Rules / Disable Transcripts and Recordings)</div>
              <el-link type="primary" class="pl-5" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </el-checkbox>

          <el-checkbox
            v-model="item.conference_arrangement.enable_transcription"
            :disabled="!item.conference_arrangement.enable_record || !item.record_and_transcript_can_check"
          >
            Requires a Transcript
          </el-checkbox>
          <el-tooltip
            v-if="!item.record_and_transcript_can_check"
            effect="dark"
            placement="top">
            <div slot="content" class="w-300px">To enable recording, please get written consent from the expert and toggle Consent to Record in the Client Task progress bar to YES. Note: Only effective if the recording setting is turned on before the meeting starts.</div>
            <el-link type="primary" class="pl-5" :underline="false" icon="el-icon-question" />
          </el-tooltip>
          <div v-show="item.conference_arrangement.enable_transcription && !isSGDB" class="inline-block ml-3 mr-8">
           <span>Transcription channels:</span>
           <el-select
             v-model="item.conference_arrangement.transcription_type"
             class="w-200px">
               <el-option
                 v-for="option in options.transcription_type"
                 :key="option.value"
                 :label="option.label"
                 :value="option.value" />
           </el-select>
          </div>
        </div>
        <div class="mb-8 flex align-items-center">
          <el-checkbox
            v-if="isMcKinsey"
            v-model="item.send_mail_to_client"
            @change="changeEmailTemplateForTwilioZoom(item.client_email.template, item, 'client_email', item.send_calendar_to_client)"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.send_calendar_to_client"
            :disabled="item.task.project.tpa_project"
            class="pr-8"
            @change="changeEmailTemplateForTwilioZoom(item.client_email.template, item, 'client_email', item.send_calendar_to_client)"
          >
            Send Calendar
          </el-checkbox>
          <el-input v-if="isVettingCallPage" v-model="item.conference_arrangement.arrange_client_location" class="w-200px" disabled />
          <el-input v-if="item.conference_arrangement.arrange_client_phone" v-model="item.conference_arrangement.arrange_client_phone" class="w-200px ml-8" disabled />
        </div>
        <alternative-hosts v-model="item.conference_arrangement.alternative_host_email_list" />
        <email-editor
          v-if="showClientTemplates(item)"
          ref="client_email"
          :options="customClientTemplates"
          :template="item.client_email.template"
          :email="item.client_email.email"
          :client-id="item.task.client_id"
          :to-cc-options="item.task.project_client_contact_email_list"
          :readonly="item.task.project.tpa_project"
          @change="(val)=> {changeEmailTemplateForTwilioZoom(val, item, 'client_email',true)}" />
        <div class="display-none">
          <profile-format-custom
            :id="`profile-format-${item.task.id}`"
            no-questions
            no-availability
            :data="item.task"
            :custom-time-zone="selectTimeZone" />
          <sq-copy
            v-if="item.task.latest_submitted_sq"
            :id="`sq-copy-client-${item.task.id}`"
            show-answer
            :short-profile="item.client_short_profile || isMcKinsey"
            :data="item.task.latest_submitted_sq" />
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="item.conference_arrangement.conference_invitees.includes('CLIENT_COMPLIANCE')" label="Client Compliance" name="client_compliance">
        <CapvisionProAlert v-if="sendToClientThroughCapvisionPro" />
        <div v-if="!sendToClientThroughCapvisionPro" class="mb-8 flex align-items-center">
          <el-checkbox
            v-model="item.conference_arrangement.send_mail_to_client_compliance"
            class="mr-normal"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.conference_arrangement.send_calendar_to_client_compliance"
            :disabled="item.task.project.tpa_project"
            class="mr-8"
          >
            Send Calendar
          </el-checkbox>
        </div>
        <email-editor
          v-if="showOtherTemplates('client_compliance')"
          ref="client_compliance_email"
          no-options
          :to-cc-options="item.task.client_compliance_email_list"
          :email="item.client_compliance_email.email"
          :readonly="item.task.project.tpa_project"
        />
      </el-tab-pane>
      <el-tab-pane v-if="item.conference_arrangement.conference_invitees.includes('CLIENT_SPONSOR')" label="Client Sponsor" name="client_sponsor">
        <CapvisionProAlert v-if="sendToClientThroughCapvisionPro" />
        <div v-if="!sendToClientThroughCapvisionPro" class="mb-8 flex align-items-center">
          <el-checkbox
            v-model="item.conference_arrangement.send_mail_to_client_sponsor"
            class="mr-normal"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.conference_arrangement.send_calendar_to_client_sponsor"
            class="mr-8"
          >
            Send Calendar
          </el-checkbox>
        </div>

        <email-editor
          v-if="showOtherTemplates('client_sponsor')"
          ref="client_sponsor_email"
          no-options
          allow-create-address
          :email="item.client_sponsor_email.email" />
      </el-tab-pane>
      <el-tab-pane v-if="item.conference_arrangement.conference_invitees.includes('MODERATOR')" label="Moderator" name="moderator">
        <div class="mb-8 flex align-items-center">
          <el-checkbox
            v-model="item.conference_arrangement.send_mail_to_moderator"
            class="mr-normal"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.conference_arrangement.send_calendar_to_moderator"
            class="mr-8"
          >
            Send Calendar
          </el-checkbox>
        </div>
        <email-editor
          v-if="showOtherTemplates('moderator')"
          ref="moderator_email"
          no-options
          allow-create-address
          :email="item.moderator_email.email" />
      </el-tab-pane>
      <el-tab-pane v-if="item.conference_arrangement.conference_invitees.includes('TRANSLATOR')" label="Translator" name="translator">
        <div class="mb-8 flex align-items-center">
          <el-checkbox
            v-model="item.conference_arrangement.send_mail_to_translator"
            class="mr-normal"
          >
            Send Mail
          </el-checkbox>
          <el-checkbox
            v-model="item.conference_arrangement.send_calendar_to_translator"
            class="mr-8"
          >
            Send Calendar
          </el-checkbox>
        </div>
        <email-editor
          v-if="showOtherTemplates('translator')"
          ref="translator_email"
          no-options
          allow-create-address
          :email="item.translator_email.email" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EmailAPI from '@/api/email'
import EmailEditor from '@/components/EmailEditor'
import ProfileFormatCustom from '@/components/FormatProfile/ProfileIndex'
import SqCopy from '@/components/Question/SqQuestionCopy'
import CaCopy from '@/components/Question/CACopy'
import CapvisionProAlert from '@/views/project/consultation/detail/tasks-arrange/components/CapvisionProAlert'
import mixin from './mixin'
import AlternativeHosts from '@/views/project/consultation/detail/tasks-arrange/components/AlternativeHosts'

export default {
  name: 'TwilioZoomArrangeEmail',
  components: { EmailEditor, SqCopy, CaCopy, ProfileFormatCustom, CapvisionProAlert, AlternativeHosts },
  mixins: [mixin],
  data() {
    return {
      loading: false,
      other_email_templates: {
        'CLIENT_COMPLIANCE': [],
        'CLIENT_SPONSOR': [],
        'MODERATOR': [],
        'TRANSLATOR': [],
      },
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        conference_role: [
          { label: 'Expert', value: 'EXPERT', email_ref: 'advisor_email' },
          { label: 'Client', value: 'CLIENT', email_ref: 'client_email' },
          { label: 'Client Compliance', value: 'CLIENT_COMPLIANCE', email_ref: 'client_compliance_email' },
          { label: 'Client Sponsor', value: 'CLIENT_SPONSOR', email_ref: 'client_sponsor_email' },
          { label: 'Moderator', value: 'MODERATOR', email_ref: 'moderator_email' },
          { label: 'Translator', value: 'TRANSLATOR', email_ref: 'translator_email' },
        ],
        transcription_type: [
          { label: 'Default', value: 'DEFAULT' },
          // { label: 'Cadence', value: 'CADENCE' },
          { label: 'Deepgram (Whisper)', value: 'DEEPGRAM' },
        ],
      },
    }
  },

  computed: {
    taskNeedApprove() {
      return this.item.task.compliance_approve_policy_jit !== 'NO_NEED' &&
      !this.item.task.compliance_passed_jit || !this.item.task.advisor.tc_term_valid['CONSULTATION']
    },

    isZoom() {
      return this.item.task.arrange_bridge_type === 'ZOOM'
    },
  },

  watch: {
    'item.conference_arrangement.conference_invitees': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getOtherEmailList()
            .then(() => {
              this.getEmailTemplateListForOtherRoles()
            })
        }
      },
    },

    'item.conference_arrangement.enable_record': {
      handler(newVal) {
        if (!newVal) {
          this.item.conference_arrangement.enable_transcription = false
        }
      }, immediate: true,
    },
    'item.task.arrange_bridge_type': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.init()
        }
      },
    },
  },

  mounted() {
    this.init()
  },

  methods: {
    init() {
      this.loading = true
      this.getEmailTemplateForClientAdvisor()
        .then(() => {
          return this.getOtherEmailList()
        })
        .then(() => {
          this.getEmailTemplateListForOtherRoles(true)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 监听 conference_arrangement.conference_invitees，添加对应的tab
    async getEmailTemplateListForOtherRoles(initial = false) {
      const new_email_templates = this.item.conference_arrangement.conference_invitees.filter(
        item => !['EXPERT', 'CLIENT'].includes(item))
      if (new_email_templates.length > 0) {
        new_email_templates.forEach(temp => {
          const target = this.options.conference_role.find(i => i.value === temp)
          if (!this.item[target.email_ref] || initial) {
            const email_default = {
              template: this.other_email_templates[temp].filter(i => i.tags.includes(this.item.task.arrange_bridge_type))[0],
              email: {
                subject: '',
                content: '',
              },
            }

            this.$set(this.item, target.email_ref, email_default)
            this.$set(this.item.conference_arrangement, `send_mail_to_${temp.toLowerCase()}`, true)
            this.getEmailContentForRole(this.item, this.item[target.email_ref], target.email_ref)
          }
        })
      }
    },

    async getEmailTemplateForClientAdvisor() {
      // 获取专家邮件内容
      this.changeEmailTemplateForTwilioZoom(this.advisorTemplates[0], this.item, 'advisor_email', true)
      // 获取客户邮件内容
      const tag = this.item.task.compliance_passed_jit ? 'LEGAL_APPROVED' : 'LEGAL_TO_BE_APPROVED'
      const template = this.clientTemplates.find(item => this.skipEmailTag || (!item.tags || item.tags.includes(tag)))
      this.changeEmailTemplateForTwilioZoom(template || this.clientTemplates[0], this.item, 'client_email', true)
    },

    changeEmailTemplateForTwilioZoom(val, item, template_type, send_mail) {
      if (send_mail) {
        item[template_type].template = val
        this.getEmailContentForRole(item, item[template_type], template_type)
      }
    },

    async getEmailContentForRole(item, item_email, template_type) {
      const isAdvisor = template_type === 'advisor_email'
      const params = {
        client_id: item.task.client_id,
        contact_id: item.client_contact.id,
        user_id: null,
        project_id: item.task.project.id,
        task_id: item.task.id,
        advisor_id: item.task.advisor_id,
        context_params: {
          angle_suffix: true,
          should_provide_bridge_info: !this.taskNeedApprove,
        },
        no_replace_placeholders: [
          '{{ADVISOR_BRIDGE_INFO}}',
          '{{BRIDGE_INFO}}',
          '{{TASK_START_TIME}}',
          '{{ADVISOR_FULL_NAME}}',
          '{{CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_CONTACT}}',
          '{{CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_ADVISOR}}',
        ],
      }

      if (template_type === 'client_email' && this.isZoom) {
        params.no_replace_placeholders.push('{{ZOOM_MEETING_ONE_CLICK_DIAL}}')
      }

      this.loading = true
      const role = this.options.conference_role.find(i => i.email_ref === template_type).value

      item_email.email.attachments = []
      item_email.email = await EmailAPI.getEmailByTemplateForTaskArrange(item_email.template.id, params)

      item_email.email.content = this.handleMailContent(item_email.email.content, item_email.template.id, item, isAdvisor, role)
      item_email.email.subject = this.handleMailSubject(item_email.email.subject, item_email.template.id, item, role)

      this.handleMailToCcList(item_email.email, item, role)
      if (this.$refs[template_type]) {
        this.$refs[template_type].setContent()
      }

      this.loading = false
    },

    // 除客户和专家外的角色 task 审核通过状态以及 未通过 公用一个template
    getOtherEmailList() {
      const need_get_other_email_templates = this.item.conference_arrangement.conference_invitees.filter(
        item => !['EXPERT', 'CLIENT'].includes(item) && !this.other_email_templates[item].length)

      if (need_get_other_email_templates.length < 1) return Promise.resolve()

      return Promise.all(need_get_other_email_templates.map(item => (getEmailTemplates(`CALENDAR_TASK_ARRANGE_TO_${item}`, this.other_email_templates, item))))

      function getEmailTemplates(content_type, target_role_email, item) {
        const email_params = {
          content_type: content_type,
        }

        return EmailAPI.getEmailList(email_params)
          .then(data => {
            target_role_email[item] = data.list
          })
      }
    },

    showOtherTemplates(type) {
      return this.item.conference_arrangement[`send_calendar_to_${type}`] || this.item.conference_arrangement[`send_mail_to_${type}`]
    },
  },
}
</script>

<style lang="scss" scoped>
.bridge-detail {
  width: 99%;
  display: inline-block;
  position: relative;
  margin-left: 8px;
}

.phone-number {
  width: 300px;
  display: inline-block;
  position: relative;
  margin-left: 20px;
}

.phone-number:before, .bridge-detail:before {
  position: absolute;
  top: -4px;
  left: -8px;
  content: "*";
  color: #f56c6c;
}

.pr-8 {
  padding-right: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.zoom-setting {
  background-color: rgb(242, 246, 252);
  padding: 15px 10px;
  margin-top: 1em;
  margin-bottom: 1em;
}
</style>
