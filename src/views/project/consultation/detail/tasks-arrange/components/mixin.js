import { mapState } from 'vuex'
import EmailAPI from '@/api/email'
import _ from 'lodash'

export default {
  props: {
    item: Object,
    arrangeOptions: Object,
    advisorTemplates: Array,
    clientTemplates: Array,
    selectTimeZone: String,
    latestDefaultTc: Object,
    sendToClientThroughCapvisionPro: { type: Boolean, default: false },
    skipEmailTag: { type: Boolean, default: false },
    calendarCopyClientEmailList: {
      type: Array,
      default() {
        return []
      },
    },
    arrangeCopyClientComplianceEmailList: {
      type: Array,
      default() {
        return []
      },
    },
  },
  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
      TengYueId: state => state.app.TengYueId,
      isSGDB: state => state.app.isSGDB,
      VikingGlobalInvestorsLPId: state => state.app.VikingGlobalInvestorsLPId,
    }),
    isMc<PERSON>insey() {
      return +this.item.task.client_id === this.McKinseyId
    },
    isVikingClient() {
      return +this.item.task.client_id === this.VikingGlobalInvestorsLPId
    },
    isGenerationClient() {
      return [this.GenerationIMId, this.GenerationGrowthEquityId].includes(+this.item.task.client_id)
    },
    isVettingCallPage() {
      return this.$route.name === 'TasksVettingCall'
    },
    customClientTemplates() {
      return this.item.task.compliance_passed_jit ? this.clientTemplates : this.clientTemplates.filter(
        item => !item.tags.includes('LEGAL_APPROVED'))
    },

    hasTCTemplate() {
      return this.item.advisor_email.template?.tags?.includes('TC')
    },

    latestDefaultTcHasAttachments() {
      return this.latestDefaultTc?.templates[0]?.file_name
    },

    showReferenceNumber() {
      return !this.isSGDB && parseInt(this.item.task.client_id) === parseInt(this.TengYueId) && this.item.task.project_sub_type === 'Consultation'
    },

    arrangedItem() {
      return this.item.task.compliance_passed_jit && !this.item.advisor_email?.template?.tags?.includes('TC')
    },
  },

  watch: {
    'selectTimeZone': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getEmailTemplateList()
        }
      },
    },
    'item.bridge_status': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getEmailTemplateList()
        }
      },
    },

    'item.tc_with_attachments': {
      handler(val) {
        if (val && this.item.advisor_email.email) {
          this.item.advisor_email.email.attachments.push({
            name: this.latestDefaultTc.templates[0].file_name,
            file_path: this.latestDefaultTc.templates[0].file_url,
          })
        } else {
          this.item.advisor_email.email.attachments = []
        }
      },
    },

    'latestDefaultTc': {
      handler(val) {
        if (val && this.latestDefaultTcHasAttachments) {
          this.item.tc_with_attachments = true
        }
      },
    },
  },

  methods: {
    showClientTemplates(item) {
      return item.task.project.tpa_project ? true : (item.send_calendar_to_client || item.send_mail_to_client)
    },
    showAdvisorTemplates(item) {
      return item.send_calendar_to_advisor || item.send_mail_to_advisor
    },

    async getEmailTemplateList() {
      // 获取专家邮件内容
      this.changeEmailTemplate(this.advisorTemplates[0], this.item, true, true)
      // 获取客户邮件内容
      const tag = this.item.task.compliance_passed_jit ? 'LEGAL_APPROVED' : 'LEGAL_TO_BE_APPROVED'
      const template = this.clientTemplates.find(item => this.skipEmailTag || (!item.tags || item.tags.includes(tag)))
      this.changeEmailTemplate(template || this.clientTemplates[0], this.item, false, true)
    },

    changeEmailTemplate(val, item, isAdvisor = true, send_mail) {
      if (send_mail) {
        const key = isAdvisor ? 'advisor_email' : 'client_email'
        item[key].template = val
        this.getEmailContent(item, item[key], isAdvisor)
      }
    },
    async getEmailContent(item, item_email, isAdvisor = true) {
      const us_loopup_area_id = isAdvisor ? item.loopUp_location_advisor?.id : {}
      const params = {
        client_id: item.task.client_id,
        contact_id: item.client_contact.id,
        user_id: null,
        project_id: item.task.project.id,
        task_id: item.task.id,
        advisor_id: item.task.advisor_id,
        context_params: {
          loopup_area_id: this.isSGDB ? null : us_loopup_area_id,
        },
        no_replace_placeholders: [
          '{{ADVISOR_BRIDGE_INFO}}',
          '{{BRIDGE_INFO}}',
          '{{TASK_START_TIME}}',
          '{{ADVISOR_FULL_NAME}}',
          '{{CONFERENCE_LINE_DAIL_IN_ACCESS}}',
          '{{CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_CONTACT}}',
          '{{CONFERENCE_LINE_ONE_CLICK_DIAL_IN_LINK_ADVISOR}}',
        ],
      }
      this.loading = true
      item_email.email.attachments = []
      item_email.email = await EmailAPI.getEmailByTemplateForTaskArrange(item_email.template.id, params)
      item_email.email.content = this.handleMailContent(item_email.email.content, item_email.template.id, item, isAdvisor, isAdvisor ? 'EXPERT' : 'CLIENT')
      item_email.email.subject = this.handleMailSubject(item_email.email.subject, item_email.template.id, item, isAdvisor ? 'EXPERT' : 'CLIENT')

      this.handleMailToCcList(item_email.email, item, isAdvisor ? 'EXPERT' : 'CLIENT')
      const key = isAdvisor ? 'email-editor-advisor' : 'email-editor-client'
      if (this.$refs[key]) {
        this.$refs[key].setContent()
      }
      this.loading = false
    },

    /* 优先取上次发送的to list, 没有上次发送就取angle的calendar_invite_to_emails+ calendarCopyClientEmailList + 邮件模板的值*/
    handleMailToCcList(email, item, role) {
      if (item.arrangement?.mail_info[role]?.to_list) {
        email.to_list = Array.from(new Set(item.arrangement.mail_info[role]?.to_list)) || []
        email.cc_list = Array.from(new Set(item.arrangement.mail_info[role]?.cc_list)) || []
        email.to_addresses = []
        email.cc_addresses = []
      } else if (role === 'CLIENT') {
        const angle_invite_emails = item.task.angle?.calendar_invite_to_emails?.split(',') || []
        const client_compliance_calendar_copy_emails = this.calendarCopyClientEmailList.map(i => i.email)
        email.to_list = _.uniq([...email.to_list, ...angle_invite_emails, ...client_compliance_calendar_copy_emails])
        email.to_addresses = []
      }

      if (role === 'CLIENT_COMPLIANCE') {
        const client_compliance_emails = this.arrangeCopyClientComplianceEmailList.map(i => i.email)
        const client_chaperons = item.task.task_client_chaperones?.map(i => ({ email: i.chaperone?.email, name: i.chaperone?.name })) || []
        const client_chaperons_emails = client_chaperons.map(i => i.email)
        email.to_list = _.uniq([...email.to_list, ...client_compliance_emails, ...client_chaperons_emails])
        email.to_addresses = [...email.to_addresses, ...this.arrangeCopyClientComplianceEmailList, ...client_chaperons]
      }
    },

    handleMailSubject(email_subject, template_id, item, role) {
      let reference_number = ''
      if (this.showReferenceNumber && role === 'CLIENT' && item.task?.client_custom_fields?.reference_number) {
        reference_number = ` (${item.task?.client_custom_fields?.reference_number})`
      }
      const same_template = parseInt(item.arrangement?.mail_info[role]?.template_id) === parseInt(template_id)
      const same_bridge = item.origin_bridge_type === item.task.arrange_bridge_type
      const is_client_or_advisor = ['EXPERT', 'CLIENT'].includes(role)
      const saved_subject = item.arrangement?.mail_info[role]?.subject

      if (saved_subject && is_client_or_advisor && same_bridge && same_template) {
        return saved_subject
      }

      const is_client_or_compliance = ['CLIENT', 'CLIENT_COMPLIANCE'].includes(role)
      // 客户 id 62 临时定制 后续替换定制模板(for us)
      if (is_client_or_compliance && parseInt(item.task.client_id) === 62 && !this.isSGDB) {
        const project_name = item.task.project?.name || ''
        const project_code = item.task.project?.code || ''
        if (!item.task.compliance_passed_jit) {
          return `Pending Compliance Approval - Capvision Consultation – ${project_name} (project code: ${project_code}) with ${this.replaceAdvisorBios('{{ADVISOR_FULL_NAME}}')} ${reference_number}`
        } else {
          return `Capvision Consultation – ${project_name} (project code: ${project_code}) with ${this.replaceAdvisorBios('{{ADVISOR_FULL_NAME}}')} ${reference_number}`
        }
      } else {
        return this.replaceAdvisorBios(email_subject) + reference_number
      }
    },

    // 优先取上次发送的邮件内容
    handleMailContent(email, template_id, item, isAdvisor, role) {
      const is_client_or_advisor = ['EXPERT', 'CLIENT'].includes(role)
      const same_template = parseInt(item.arrangement?.mail_info[role]?.template_id) === parseInt(template_id)
      const same_bridge = item.origin_bridge_type === item.task.arrange_bridge_type
      const saved_content = item.arrangement?.mail_info[role]?.content

      if (saved_content && is_client_or_advisor && same_bridge && same_template) {
        return saved_content
      }

      const data = this.replaceAdvisorBios(email)
      let result = this.replaceSQ(data, item, isAdvisor, role)

      if (role === 'CLIENT_COMPLIANCE') {
        let ca_content = ''
        const client_compliance_rules = item.task.client.compliance_preference?.rule?.compliance_rules
        const is_include_client_agreement = client_compliance_rules?.calendar_invite?.master_switch && client_compliance_rules?.calendar_invite?.is_include_client_agreement_in_compliance_invite_if_auto_approved
        const is_based_on_ca = client_compliance_rules?.approval?.client_conditional_approve_type === 'Based on CA' && client_compliance_rules?.approval?.master_switch
        const auto_passed = item.task.latest_submitted_pre_ca?.inquiry_instance?.status === 'AUTO_PASSED'
        if (is_include_client_agreement && is_based_on_ca && auto_passed) {
          const content_id = `ca-copy-advisor-${item.task.id}`
          ca_content = `<br><div><span style="font-family: Calibri;"><strong>Client Agreement Questions/Comments</strong></span></div>` +
            '<div contenteditable="false">' + document.getElementById(content_id).innerHTML + '</div>'
        }
        result = result.replace('{CA_CONTENT_IF_ECOM_AUTO_APPROVE}', ca_content)
      }

      return result
    },
    replaceAdvisorBios(email) {
      let html = document.getElementById(`profile-format-${this.item.task.id}`).innerHTML

      /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
      const client_status_is_not_EXECUTE = this.item.task.client.status !== 'EXECUTE'
      const client_compliance_status_no_pass = this.item.task.client.compliance_status !== 'APPROVED'
      const client_blind_expert_name = this.item.task.client.preference?.blind_expert_names_in_to_client_and_portal
      const project_blind_expert_name = this.item.task.project?.blind_expert_profiles

      if (client_status_is_not_EXECUTE || client_compliance_status_no_pass || client_blind_expert_name || project_blind_expert_name) {
        html = html.replace(this.item.task.advisor.full_name, 'Advisor')
        email = email.replace('{{ADVISOR_FULL_NAME}}', `Advisor ${this.item.task.display_id}`)
      } else {
        const format_name = `${this.item.task.advisor.name_prefix || ''} ${this.item.task.advisor.full_name}`
        email = email.replace('{{ADVISOR_FULL_NAME}}', format_name)
      }
      return email.replace('{TASK_ADVISOR_BIOS}', html)
    },

    replaceSQ(email, item, isAdvisor, role) {
      const content_id = isAdvisor ? `sq-copy-advisor-${item.task.id}` : `sq-copy-client-${item.task.id}`
      let html = ''
      if (item.sq) {
        html = '<div style="font-size: 11pt; font-family: Calibri;">' + '<div contenteditable="false">' + document.getElementById(content_id).innerHTML + '</div>' + '</div>'
      } else {
        email = email.replace('<div class="sq-box"', '<div class="sq-box" style="display:none"')
      }

      if (this.isGenerationClient && !isAdvisor && item.task.client_custom_fields) {
        html += '<div style="margin-top: 5px;"><b>Expert Code: ' +
          item.task.client_custom_fields['task_expert_code'] + '</b></div>'
      }

      // 单个客户的样式要求，如果后期有更多客户需要，可添加相应占位符
      if (role === 'CLIENT' && this.isVikingClient) {
        html += '<div><br/><br/></div><div>' +
          '<span style="font-family: Calibri"><strong>Compliance Statement: </strong>' +
          '</span>I need to read you the following statement under my Investment Firm’s compliance procedures: our Investment Firm invests in publicly traded securities. We use consultants such as yourself to help make more informed investment decisions. You have confirmed that you are not prohibited by your employer or any other party from doing this consultation. Please be careful not to disclose any information that you believe may be material and non-public, or that you have any duty to keep confidential. If you are unsure of your obligations or are asked a question you may not be able to answer given those obligations, please err on the side of caution and do not answer that question. You will be paid in full for this consultation regardless of how many questions you decline to answer.</div>'
      }

      return email.replace('{SQ_CONTENT}', html)
    },

    calendarDisabled(item) {
      return [this.McKinseyId].includes(item.task.client_id)
    },
  },
}
