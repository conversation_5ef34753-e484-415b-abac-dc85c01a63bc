<template>
  <div>
    <el-page-header content="Tasks Arrange" @back="actionGotoClientTask" />

    <div v-loading="loading" class="collapse-wrapper">
      <arrange-warning
        :client-id="$route.query.client_id"
        type="arrange" />
      <div v-if="noCommonContact || noValidContract">
        <el-alert
          v-if="noCommonContact"
          title="This project is missing the common type of client contact, please add it first"
          show-icon
          type="error"
          class="mb-8" />
        <el-alert
          v-if="noValidContract"
          title="This client does not have a valid contract and cannot be arranged"
          show-icon
          type="error"
          class="mb-8" />
      </div>
      <div v-else>
        <el-alert
          v-if="!roomCountLoading && !isSGDB"
          :title="`These is currently ${options.loopUp_rooms_list.length} free meeting rooms.`"
          :type="options.loopUp_rooms_list.length ? 'success' : 'error'"
          show-icon
          class="mb-8" />
        <el-alert
          v-if="filterTasks.length"
          title="These tasks will be filtered, due they have been completed, or not sent to client, or experts need to sign TC, or their compliance approval is rejected or not in process:"
          type="warning"
          show-icon
          class="mb-8">
          <div v-if="filterTasks.length" class="flex align-items-center">
            <router-link
              v-for="(item, index) in filterTasks"
              :key="index"
              class="text-truncate link"
              :to="{ name: 'ConsultantDetail', params: { advisor_id: item.advisor_id } }">
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.full_name }}
              <slot v-if="index !== filterTasks.length - 1">,</slot>&nbsp;
            </router-link>
          </div>
        </el-alert>
        <el-alert
          v-if="data.length && sendToClientThroughCapvisionPro(data[0].task)"
          title="The client's legal rules have checked to send emails and calendar to this client through 'capvision-pro.com', the system will not send emails and calendar to this client. Please copy the email content yourself and send the email through the 'capvision-pro.com' mailbox."
          type="warning"
          show-icon
          :closable="false"
          class="mb-8 mt-8" />
      </div>

      <el-collapse v-model="activeCollapse" accordion>
        <el-collapse-item v-for="item in data" :key="item.id" :name="item.id">
          <template slot="title">
            <div class="collapse-title">
              <router-link
                class="text-truncate link"
                :to="{ name: 'ConsultantDetail', params: { advisor_id: item.task.advisor_id } }">
                {{ item.task.advisor.name_prefix || ''}}
                {{ item.task.advisor.full_name }}
              </router-link>
              <el-tooltip v-if="!item.task.compliance_passed_jit" effect="dark" content="Need Approval" placement="top">
                <el-link type="warning" :underline="false" icon="el-icon-warning" />
              </el-tooltip>
              <el-tooltip v-if="advisorNoEmail(item.task)" effect="dark" content="No email" placement="top">
                <el-link type="warning" :underline="false" icon="el-icon-warning" />
              </el-tooltip>
            </div>
            <el-select v-model="item.client_contact" value-key="id" class="flex-shrink-0" @change="getLocationPhoneForTwilio(item)">
              <el-option
                v-for="option in item.client_contacts_option"
                :key="option.id"
                :label="option.client_contact.name"
                :value="option.client_contact" />
            </el-select>
            <time-zone-select
              v-model="item.selectTimeZone"
              class="ml-8 inline-block flex-shrink-0"
              @change="formatSchedule(item)" />

            <el-dropdown
              trigger="click"
              @command="item.selectTimeZone = $event; formatSchedule(item);">
              <el-tooltip content="Quick Select Time Zone" placement="right">
                <el-button type="primary" plain circle icon="el-icon-caret-bottom" class="ml-8" @click.stop />
              </el-tooltip>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="!item.client_contact.zone_id_string"
                  :command="item.client_contact.zone_id_string">
                  Client’s Time Zone
                  <i v-if="item.client_contact.zone_id_string">--
                    {{ item.client_contact.zone_id_string }}</i>
                  <i v-else>(Not available, please update the client record)</i>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!item.task.advisor.zone_id_string"
                  :command="item.task.advisor.zone_id_string">
                  Advisor’s Time Zone
                  <i v-if="item.task.advisor.zone_id_string">--
                    {{ item.task.advisor.zone_id_string }}</i>
                  <i v-else>(Not available, please update the advisor record)</i>
                </el-dropdown-item>
                <el-dropdown-item :command="timeZone">My Time Zone <i>-- {{ timeZone }}</i></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <div v-if="item.selectFormatTime" class="format-str">
              <i v-if="scheduleLoading" class="el-icon-loading" />
              <slot v-if="!scheduleLoading && item.selectFormatTime">
                {{ item.selectFormatTime.start | momentFormatZone('ddd MM/DD: h:mma',item.selectTimeZone) }} -
                {{ item.selectFormatTime.end | momentFormatZone('h:mma',item.selectTimeZone) }}
              </slot>
            </div>
            <el-select
              v-model="item.task.arrange_bridge_type"
              value-key="id"
              placeholder="Arrange Bridge"
              class="ml-8 mr-8 w-200px flex-shrink-0"
              @change="setEmailTemplates(item, item.task, true)">
              <el-option
                v-for="room in options.arrange_bridge_types"
                :key="room.value"
                :label="room.label"
                :value="room.value" />
            </el-select>
            <a v-if="item.task.arrange_bridge_type === 'LOOPUP' && item.task.loopup_room"
               class="text-truncate link ml-8"
               :href="'//' + item.task.loopup_room.url"
               target="_blank">Loopup
              #{{ item.task.loopup_room.id }}</a>
            <el-select
              v-if="['TWILIO','ZOOM'].includes(item.task.arrange_bridge_type)"
              v-model="item.conference_arrangement.conference_invitees"
              placeholder="Conference Invitees"
              multiple
              class="twilio-select">
              <el-option
                v-for="role in options.conference_roles"
                :key="role.value"
                :disabled="['CLIENT','EXPERT'].includes(role.value)"
                :label="role.label"
                :value="role.value" />
            </el-select>
          </template>
          <calendar
            v-model="item.schedule"
            single-select
            :select-time-zone="item.selectTimeZone"
            :task-id="item.id"
            :outsource="!!item.task.task_outsource_info"
            :class="{'collapse-item-wrapper': activeCollapse !== item.id}"
            @new="() => {getSelectedSchedule(item)}"
            @delete="() => {deleteScheduleData(item)}"
            @resize="(val) => {resizeScheduleData(val, item)}" />

          <div v-if="item.task">
            <twilio-zoom-arrange-email
              v-if="['TWILIO','ZOOM'].includes(item.task.arrange_bridge_type)"
              ref="twilioArrangeEmail"
              :item="item"
              :select-time-zone="item.selectTimeZone"
              :arrange-options="options"
              :latest-default-tc="latest_default_tc"
              :advisor-templates="item.advisor_emailTemplates"
              :client-templates="item.client_emailTemplates"
              :calendar-copy-client-email-list="calendarCopyClientEmailList(item.task.client)"
              :arrange-copy-client-compliance-email-list="approvalCopyClientComplianceEmailList(item.task.client)"
              :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro(item.task)" />
            <arrange-email
              v-else
              :item="item"
              :select-time-zone="item.selectTimeZone"
              :arrange-options="options"
              :latest-default-tc="latest_default_tc"
              :advisor-templates="item.advisor_emailTemplates"
              :client-templates="item.client_emailTemplates"
              :calendar-copy-client-email-list="calendarCopyClientEmailList(item.task.client)"
              :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro(item.task)" />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-button type="success" :disabled="!data.length" :loading="submitLoading" @click="submit">
      {{ data.length && data[0].task.project.tpa_project ? 'Submit and Copy Client Invite' : 'Submit' }}
    </el-button>
    <el-button type="text" @click="actionGotoClientTask">Cancel</el-button>

    <el-dialog
      :visible.sync="recordingRepetitionConfirmationVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="Recording confirmation"
      width="45%"
    >
      <div>
        Recording is set to NO.  Are you sure you don’t want to record this call?
        <div v-if="unopened_recording_tasks.length>1">[{{unopened_recording_tasks.map(item=>item.advisor_name).join()}}]</div>
    </div>
      <span slot="footer" class="dialog-footer">
          <el-tooltip
            v-if="recording_change_setting_disabled"
            effect="dark"
            placement="top">
            <div slot="content" class="w-300px">To enable recording, please get written consent from the expert and toggle Consent to Record in the Client Task progress bar to YES. Note: Only effective if the recording setting is turned on before the meeting starts.</div>
            <el-link type="info" class="pl-5" :underline="false" icon="el-icon-question" />
          </el-tooltip>
        <el-button type="primary" :disabled="recording_change_setting_disabled" @click="recordingRepetitionConfirmationVisible=false">Change Settings</el-button>
        <el-button type="primary" @click="arrange()">Do Not Record</el-button>
        <div class="mt-3">
          <el-checkbox v-model="stop_prompting_for_recording_settings">Do not show this popup again</el-checkbox>
         </div>
      </span>
    </el-dialog>
</div>
</template>

<script>
import { mapState } from 'vuex'
import API from '@/api/project'
import Calendar from '@/components/Calendar'
import moment from 'moment-timezone'
import { copyUtil, RouteTools } from '@/utils/tool'
import detailMixins from './../mixins/mixins'
import ArrangeWarning from '@/components/ComplianceWarning/Index'
import TwilioZoomArrangeEmail from '@/views/project/consultation/detail/tasks-arrange/components/TwilioZoomArrangeEmail'
import ArrangeEmail from './components/ArrangeEmail'
import { AppHttpService } from '@/utils/request'
import EmailAPI from '@/api/email'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
import TcAPI from '@/api/tc'
import ConsultantAPI from '@/api/consultant'

export default {
  name: 'TaskArrange',
  components: { Calendar, ArrangeEmail, ArrangeWarning, TimeZoneSelect, TwilioZoomArrangeEmail },
  mixins: [detailMixins],
  data() {
    return {
      loading: false,
      roomCountLoading: true,
      scheduleLoading: false,
      submitLoading: false,
      noCommonContact: false,
      noValidContract: false,
      recordingRepetitionConfirmationVisible: false,
      recording_change_setting_disabled: false,
      stop_prompting_for_recording_settings: false,
      unopened_recording_tasks: [],
      activeCollapse: '',
      typeColor: {
        advisor: '#67C23A',
        client: '#E6A23C',
        arranged: '#891ae4',
        other_arranged: '#a7a5a8',
        pm: '#3788D8',
        outsource: '#fdb4b4',
      },
      filterTasks: [],
      data: [],
      options: {
        loopUp_location_list: [],
        loopUp_rooms_list: [],
        arrange_bridge_types: [
          { label: 'Client Provided Bridge', value: 'CLIENT_PROVIDED' },
          { label: 'Internal Zoom', value: 'INTERNAL_ZOOM' },
          { label: 'ZipDX', value: 'ZIPDX' },
          { label: 'Other', value: 'OTHER' },
        ],
        conference_roles: [
          { label: 'Expert', value: 'EXPERT' },
          { label: 'Client', value: 'CLIENT' },
          { label: 'Client Compliance', value: 'CLIENT_COMPLIANCE' },
          { label: 'Client Sponsor', value: 'CLIENT_SPONSOR' },
          { label: 'Moderator', value: 'MODERATOR' },
          { label: 'Translator', value: 'TRANSLATOR' },
        ],
      },
      latest_default_tc: null,
      emailTemplates: {
        advisor_emailTemplates: [],
        client_emailTemplates: [],
        other_emailTemplates: [],
      },
      task_detail_extra: [
        'angle',
        'arrangement',
        'advisor_profile',
        'advisor.location',
        'advisor.contact_infos',
        'advisor.contact_infos_without_mosaic',
        'advisor.jobs.company',
        'loopup_room',
        'advisor_schedules',
        'advisor_record_consent',
        'project.project_client_contacts.client_contact.location',
        'project.project_client_contacts.client_contact.office.location',
        'project.contract',
        'latest_submitted_sq.questions',
        'latest_submitted_sq.answers',
        'latest_submitted_sq.branch',
        'latest_submitted_pre_ca.inquiry_instance',
        'compliance_approve_policy_jit',
        'compliance_passed_jit',
        'client_contact.contact_infos',
        'client_contact.contact_infos_without_mosaic',
        'client.contracts',
        'client.compliance_preference',
        'client.preference',
        'client_custom_fields',
        'default_client_rate_usd',
        'default_client_rate_usd_considering_unsigned_tc',
        'standard_rate_multiplier_display_to_client',
        'given_senior_rate_multiplier',
        'arrangement.conference_invitees',
        'task_outsource_info.task_outsource_arrangement',
        'task_client_chaperones.chaperone',
      ],
    }
  },
  computed: {
    ...mapState({
      tasks: state => state.tasks.list,
      timeZone: state => state.app.timeZone,
      McKinseyId: state => state.app.McKinseyId,
      projectInfo: state => state.project.data,
    }),
    isMcKinsey() {
      return +this.$route.query.client_id === this.McKinseyId
    },

    isSGDB() {
      return import.meta.env.VITE_APP_DB_NAME && import.meta.env.VITE_APP_DB_NAME === 'SG'
    },
  },
  mounted() {
    this.loading = true

    if (!this.isSGDB) {
      this.options.arrange_bridge_types.unshift({ label: 'Loopup', value: 'LOOPUP' })
      this.options.arrange_bridge_types.unshift({ label: 'Twilio', value: 'TWILIO' })
      this.options.arrange_bridge_types.unshift({ label: 'Zoom BETA', value: 'ZOOM' })
    }

    Promise.all([this.getTasksDetail(), this.getLoopUpRoomsList(), this.getEmailTemplateList()])
      .then(this.getOptions)
      .then(this.getFilterTasks)
      .then(this.getTCList)
      .then(this.getScheduleData)
      .finally(() => {
        this.loading = false
      })
  },
  methods: {
    formatSchedule(data) {
      data.schedule.forEach(item => {
        if (item.creator_type !== 'PM') {
          item.start = moment(item.start_time).tz(data.selectTimeZone).format()
          item.end = moment(item.end_time).tz(data.selectTimeZone).format()
        }
      })
    },
    getTasksDetail() {
      const params = {
        project_id: this.$route.params.project_id,
        params: {
          ids: this.$route.query.id,
          extra: this.task_detail_extra.join(),
        },
      }
      return API.getTasksDetail(params)
        .then(data => {
          this.$store.dispatch('tasks/SET_LIST', data['list'])
        })
    },
    getOptions() {
      if (this.isSGDB) return Promise.resolve()

      return AppHttpService
        .get('/common/loopup/numbers')
        .then(list => {
          this.options.loopUp_location_list = list
        })
    },
    async getEmailTemplateList() {
      const content_types = ['CALENDAR_TASK_ARRANGE_ADVISOR', 'CALENDAR_TASK_ARRANGE_CONTACT']
      const client_email_params = {
        has_permission: true,
        content_type: content_types[1],
        reference_ids: [0, this.$route.query.client_id].join(),
        reference_type: 'CLIENT',
        sort: 'reference_id desc',
        include_default_reference_type: true,
      }
      const request_advisor = EmailAPI.getEmailList({ has_permission: true, content_type: content_types[0] })
      const request_contact = EmailAPI.getEmailList(client_email_params)
      const data = await Promise.all([request_advisor, request_contact])
      this.emailTemplates['advisor_emailTemplates'] = data[0].list
      this.emailTemplates['client_emailTemplates'] = data[1].list
    },
    async getLoopUpRoomsList() {
      if (!this.isSGDB) {
        this.options.loopUp_rooms_list = await API.getLoopUpRoomsList()
      }
      this.roomCountLoading = false
    },
    getFilterTasks() {
      // 清理数据
      this.filterTasks = []
      this.data = []

      const filterTasks = []
      const data = []

      this.tasks.forEach((item, index) => {
        // const advisor_has_email = item.advisor.contact_infos.find(item => item.type === 'EMAIL')
        const task_not_completed = item.general_status !== 'COMPLETED'
        const client_compliance_can_arrange = item.client_compliance_status !== 'REJECTED'
        const client_contact_agree = item.client_contact_status === 'APPROVED'
        // item.compliance_approve_policy_jit:null 视为客户不需要rules
        const compliance_approve_no_need = item.compliance_approve_policy_jit === 'NO_NEED' ||
          !item.compliance_approve_policy_jit
        const compliance_need_approve_capvision_get = item.compliance_approve_policy_jit &&
          item.compliance_approve_policy_jit !== 'NO_NEED' &&
          ['SENT', 'APPROVED', 'DIRECT_APPROVED'].includes(item.capvision_compliance_status)
        const is_advisor = item.advisor.tc_term_valid['CONSULTATION']

        const task_compliance_status_can_arrange = !item.compliance_approve_policy_jit || compliance_approve_no_need ||
          compliance_need_approve_capvision_get

        // 客户规则 未签TC OR 未 to compliance/approval
        const client_rules_can_arrange_before_sign_tc_or_approval = clientRulesCanArrangeBeforeSignTcOrApproval(item)

        // 若专家是导出到Outsource DB使用的，且在Outsource DB中已经Arrange了，才可以Arrange（无需client_contact_agree）
        const is_exported_to_outsource_can_arrange = item.task_outsource_status === 'EXPORTED' &&
          item.task_outsource_info.task_outsource_general_status === 'ARRANGED'
        // 若为Outsource协作专家，且不为Survey项目，且在Outsource DB中已经Compliance Approved了，才可以Arrange（无需client_rules_can_arrange_before_sign_tc_or_approval）
        const is_outsource_can_arrange = item.task_outsource_info && item.project_sub_type !== 'Survey'
          ? item.task_outsource_info.task_outsource_compliance_status === 'APPROVED'
          : true
        // 若Task的Angle为Physician Angle，专家需验证通过NPI才可以 arrange
        const npi_validated_can_arrange = item.angle.tags?.includes('PHYSICIAN')
          ? item.advisor.npi_status === 'VALID'
          : true
        // 若专家在Outsource已Arrange，则自动选择Client Provided Bridge
        if (item.task_outsource_info?.task_outsource_arrangement) item.arrange_bridge_type = 'CLIENT_PROVIDED'

        // 满足以下情况之一，不可以arrange
        // 专家没有email -- 2022.11.11 邮件通知移除
        // task 已经 complete
        // 客户法务 rejected task
        // 客户 没有选择该专家
        // 客户审核规则：需要审核 && task未发送给凯盛法务 || 客户rules->call scheduling->Can send placeholder calendar invite before approval or TC signature? 未勾选 && (专家未签TC || task 审核未通过)

        if (task_not_completed &&
          client_compliance_can_arrange &&
          (client_contact_agree || is_exported_to_outsource_can_arrange) &&
          (client_rules_can_arrange_before_sign_tc_or_approval || (is_advisor && task_compliance_status_can_arrange)) &&
          is_outsource_can_arrange &&
          npi_validated_can_arrange
        ) {
          const client_contacts_option = item.project.project_client_contacts.filter(
            item => item.client_contact.types.includes('COMMON'))
          if (!client_contacts_option.length) {
            this.noCommonContact = true
            return
          }

          const valid_none_project_base_contracts = item.client.contracts.filter(
            item => (item.payway !== 'PROJECT_BASE' && item.effective_status === 'EFFECTIVE'))
          const project_bound_contract = item.project.contract?.effective_status === 'EFFECTIVE'

          if (!valid_none_project_base_contracts.length && !project_bound_contract) {
            this.noValidContract = true
            return
          }

          // 法务审核通过，专家不需要签TC，第一次arrange 时， 获取client_provided_bridge_link数据
          const not_need_sign_tc = is_advisor || item.task_outsource_status === 'IMPORTED'
          if (item.project.client_provided_bridge_link && !item.arrangement?.bridge_type) {
            item.arrange_bridge_type = 'CLIENT_PROVIDED'
            if (not_need_sign_tc && item.compliance_passed_jit) {
              item.arrange_detail_str = item.project.client_provided_bridge_link.replace(/\n/gi, '<br>')
              item.arrange_advisor_bridge_info_str = item.project.client_provided_bridge_link.replace(/\n/gi, '<br>')
              item.arrange_contact_calendar_location = item.project.client_provided_bridge_link.replace(/\n/gi, ' ')
              item.arrange_advisor_calendar_location = item.project.client_provided_bridge_link.replace(/\n/gi, ' ')
            }
          }

          let loopup_room = null
          if (!item.loopup_room && data.length < this.options.loopUp_rooms_list.length) {
            loopup_room = this.options.loopUp_rooms_list[data.length]
          }

          // 如果已经schedule，取上次选择且仍旧存在于项目的客户联系人;否则 取项目的 project_client_contacts type为common 的第一个客户联系人
          let client_contact_obj = client_contacts_option[0].client_contact
          if (item.client_contact_id) {
            const last_selected_client_contact = client_contacts_option.find(i => i.client_contact.id === item.client_contact_id)
            if (last_selected_client_contact?.client_contact) {
              client_contact_obj = last_selected_client_contact.client_contact
            }
          }

          // 客户联系人 作为客户收件人选项
          item.project_client_contact_email_list = []
          if (this.projectInfo.project_client_contacts.length) {
            this.projectInfo.project_client_contacts.forEach(temp => {
              const list = temp.client_contact.contact_infos_without_mosaic.filter(i => i.type === 'EMAIL' && i.value)
              list.forEach(i => {
                item.project_client_contact_email_list.push({ email: i.value, name: temp.client_contact.name })
              })
            })
          }

          // 客户法务 作为client compliance收件人选项
          item.client_compliance_email_list = []
          if (this.projectInfo.client?.client_contacts?.length) {
            const compliance_list = this.projectInfo.client?.client_contacts?.filter(item => item.types.some(type => ['LEGAL', 'COMPLIANCE'].includes(type)))

            compliance_list.forEach(temp => {
              const email_list = temp.contact_infos_without_mosaic.filter(i => i.type === 'EMAIL' && i.value)
              email_list.forEach(i => {
                item.client_compliance_email_list.push({ email: i.value, name: temp.name })
              })
            })
          }

          // 专家的所有邮箱地址 作为专家收件人选项
          item.advisor_contact_email_list = []
          item.advisor.contact_infos_without_mosaic.filter(i => i.type === 'EMAIL' && i.value)
            .forEach(i => {
              item.advisor_contact_email_list.push({ email: i.value, name: item.advisor.name })
            })

          // twilio||zoom 接桥信息 初始化 || 获取上次操作的数据
          const consulting_client = item.client.type === 'CONSULTING_FIRM'
          item.compliance_rules_disable_transcription_record = clientRulesDisableTranscriptAndRecord(item.client)
          // 咨询类客户 或者 专家已经同意开启录音时，录音和转录都可选
          item.record_and_transcript_can_check = !item.compliance_rules_disable_transcription_record && (consulting_client || item.advisor_record_consent?.is_consent)

          const alternative_host_list = item.arrangement?.conference_invitees?.filter(item => item.role === 'ALTERNATIVE_HOST').map(item => item.email)

          item.conference_arrangement = {
            conference_invitees: this.initConferenceInvitees(item),
            enable_auto_dial_expert: false,
            enable_record: item.record_and_transcript_can_check && item.client.preference?.require_call_recording,
            enable_transcription: item.record_and_transcript_can_check && item.client.preference?.require_call_transcript,
            transcription_type: 'DEEPGRAM',
            arrange_expert_location: item.advisor.location?.name,
            arrange_expert_phone: this.getMainPhone(item.advisor.contact_infos_without_mosaic),
            arrange_client_location: client_contact_obj?.location?.name || null,
            arrange_client_phone: this.getMainPhone(client_contact_obj?.contact_infos_without_mosaic),
            // zoom
            include_one_click_dial: item.client.preference?.include_one_click_dial_in_client_invite || false,
            enable_waiting_room: item.arrangement?.enable_waiting_room || false,
            enable_as_co_host: !!item.arrangement?.co_host_roles || true,
            co_host_roles: (item.arrangement?.co_host_roles && item.arrangement?.co_host_roles.split(',')) || ['CLIENT'],
            alternative_host_email_list: alternative_host_list || [],
          }

          if (['TWILIO', 'ZOOM'].includes(item.arrange_bridge_type) && item.arrangement) {
            item.conference_arrangement.enable_record = item.record_and_transcript_can_check && item.arrangement.enable_record
            item.conference_arrangement.enable_transcription = item.record_and_transcript_can_check && item.arrangement.enable_transcription

            if (item.arrangement.conference_invitees.length > 0) {
              item.conference_arrangement.conference_invitees = []
              item.arrangement.conference_invitees.forEach(i => {
                if (!item.conference_arrangement.conference_invitees.includes(i.role) && i.role !== 'ALTERNATIVE_HOST') {
                  item.conference_arrangement.conference_invitees.push(i.role)
                }
              })
            }
          }

          // 根据 专家是否签署TC/task是否approved 分配email template
          this.setEmailTemplates(item, item, false)

          if (!is_advisor) {
            item.tc_rate = item.advisor.rate || item.advisor.default_rate
            item.tc_rate_currency = item.advisor.rate_currency
          }
          if (this.isMcKinsey) {
            item.client_emailTemplates = this.emailTemplates['client_emailTemplates'].filter(t => {
              return t.reference_id === this.McKinseyId
            })
          }
          // const client_short_profile = item.client.preference && item.client.preference.short_form_profile
          const client_short_profile = item.client.preference?.to_client_default_profiles_format === 'SHORT_FORM_PROFILES'

          const client_need_calendar = !this.checkClientNoNeedCalendar(item.client)
          const client_calendar_subject_include_angle = this.checkClientCalendarSubjectIncludeAngle(item.client)
          const send_mail_to_client = this.isMcKinsey
          const advisor_need_calendar = true
          data.push({
            id: item.id,
            task: item,
            sq: handleSQ(item['latest_submitted_sq']),
            client_contacts_option,
            client_contact: client_contact_obj,
            schedule: [],
            tc_rate: item.tc_rate || undefined,
            tc_rate_currency: item.tc_rate_currency || undefined,
            selectTimeZone: item.project.zone_id_string || this.timeZone,
            send_calendar_to_advisor: advisor_need_calendar,
            send_calendar_to_client: item.project.tpa_project ? false : client_need_calendar,
            send_mail_to_client: send_mail_to_client,
            send_mail_to_advisor: true,
            tc_with_attachments: false,
            loopup_room,
            arrangement: item.arrangement,
            conference_arrangement: item.conference_arrangement,
            record_and_transcript_can_check: item.record_and_transcript_can_check,
            compliance_rules_disable_transcription_record: item.compliance_rules_disable_transcription_record,
            bridge_status: 0,
            origin_bridge_type: item.arrange_bridge_type,
            advisor_email: {
              template: null,
              email: {
                subject: '',
                content: '',
              },
            },
            client_email: {
              template: null,
              email: {
                subject: '',
                content: '',
              },
            },
            client_emailTemplates: item.client_emailTemplates,
            advisor_emailTemplates: item.advisor_emailTemplates,
            client_short_profile,
            client_calendar_subject_include_angle,
          })
        } else {
          filterTasks.push(item)
        }
      })

      this.loading = false

      if (this.isSGDB) {
        this.filterTasks = this.filterTasks.concat(filterTasks)
        this.data = this.data.concat(data)

        return Promise.resolve()
      } else {
        // 获取每个 task 对应的 loopUp_location
        const tasks = data.map(item => {
          return getLoopUpLocation(item)
        })

        return Promise
          .all(tasks)
          .then(poly => {
            const loopUp_location_us = this.options.loopUp_location_list.find(item => item['area_name'] === 'USA')

            data.forEach((item, index) => {
              const location = poly[index] || loopUp_location_us
              item.loopUp_location_advisor = location

              // client location 可多选
              const client_country_id = item.client_contact.location?.id_path.split(' ')[1]
              const client_location = this.options.loopUp_location_list.find(
                item => item.location_id === +client_country_id)
              item.loopUp_location_client = [client_location || loopUp_location_us]
            })

            this.filterTasks = this.filterTasks.concat(filterTasks)
            this.data = this.data.concat(data)
          })
      }

      function clientRulesCanArrangeBeforeSignTcOrApproval(row) {
        const client_compliance_rules = row.client.compliance_preference?.rule?.compliance_rules
        const client_has_call_scheduling_rules = client_compliance_rules &&
          client_compliance_rules.call_scheduling_emails?.master_switch &&
          client_compliance_rules.call_scheduling_emails?.can_send_placeholder_calendar_invite_before_approval
        return client_has_call_scheduling_rules &&
          client_compliance_rules.call_scheduling_emails?.send_placeholder_calendar_invite_before_approval_type ===
          'Can send'
      }

      // 需求：https://www.notion.so/capvision/Compliance-Disable-Transcripts-and-Recordings-setting-18423199f41a803693dbd6b16af8f905?pvs=4
      function clientRulesDisableTranscriptAndRecord(client) {
        const transcript_notetaking_rules = client.compliance_preference?.rule?.compliance_rules?.transcript_notetaking_rules
        return transcript_notetaking_rules?.master_switch && transcript_notetaking_rules?.disable_transcripts_and_recordings
      }

      function getLoopUpLocation(data) {
        return AppHttpService
          .get('/common/loopup/number', { params: { location_id: data.task.advisor.location_id } })
      }

      function handleSQ(inquiry) {
        if (!inquiry) return null
        return {
          title: inquiry.branch_snapshot.title,
          description: inquiry.branch_snapshot.description,
          questions: inquiry.qa_list_snapshot.map(item => {
            if (item.sub_questions) {
              item.sub_questions.forEach(sub => {
                sub.answer = item.answer ? formatAnswer(item.answer) : ''
              })
            } else {
              item.answer = item.answer ? formatAnswer(item.answer) : ''
            }
            return item
          }),
        }
      }

      function formatAnswer(answer) {
        return Object.assign({}, answer, {
          id: answer.id,
          km_comment: answer.km_comment,
        })
      }
    },

    // 根据 专家是否签署TC/task是否approved/arrange bridge  分配email template
    // target_item.bridge_status 用于 arrange email 模板替换监听
    // 麦肯锡的客户模板 用定制的 不用根据 bridge 切换
    setEmailTemplates(target_item, filter_item, bridge_change) {
      const task_need_approval = filter_item.compliance_approve_policy_jit !== 'NO_NEED' &&
        !filter_item.compliance_passed_jit

      // arrange bridge 改变时 && task 未审核通过  =>模板list 不需要改变
      if (bridge_change) this.activeCollapse = target_item.id
      if (bridge_change && task_need_approval) return

      // 其他DB导入来的专家 不需要考虑TC（在其DB中已签署TC）
      if (!filter_item.advisor.tc_term_valid['CONSULTATION'] && filter_item.task_outsource_status !== 'IMPORTED') {
        target_item.advisor_emailTemplates = this.emailTemplates['advisor_emailTemplates'].filter(
          t => t.tags && t.tags.includes('TC'))
        !this.isMcKinsey && (target_item.client_emailTemplates = this.emailTemplates['client_emailTemplates'].filter(
          t => t.tags && t.tags.includes('LEGAL_TO_BE_APPROVED')))
        return
      }

      if (task_need_approval) {
        filterTemplate(this.emailTemplates, 'LEGAL_TO_BE_APPROVED', target_item, this.isMcKinsey)
      } else {
        switch (filter_item.arrange_bridge_type) {
          case null:
            target_item.bridge_status = 1
            filterTemplate(this.emailTemplates, 'LEGAL_APPROVED', target_item, this.isMcKinsey)
            break
          case 'LOOPUP':
            target_item.bridge_status = 2
            filterTemplate(this.emailTemplates, 'LOOPUP', target_item, this.isMcKinsey)
            break
          case 'TWILIO':
            target_item.bridge_status = 3
            this.getLocationPhoneForTwilio(target_item)
            filterTemplate(this.emailTemplates, 'TWILIO', target_item, this.isMcKinsey)
            break
          case 'ZOOM':
            target_item.bridge_status = 4
            filterTemplate(this.emailTemplates, 'ZOOM', target_item, this.isMcKinsey)
            break
          default:
            target_item.bridge_status = 5
            filterTemplate(this.emailTemplates, 'NOT_LOOPUP', target_item, this.isMcKinsey)
        }
      }

      function filterTemplate(list, tag, target, isMcKinsey) {
        target.advisor_emailTemplates = list['advisor_emailTemplates'].filter(t => t.tags && t.tags.includes(tag))
        !isMcKinsey ? target.client_emailTemplates = list['client_emailTemplates'].filter(
          t => t.tags && t.tags.includes(tag)) : ''
      }
    },

    getTCList() {
      const need_tc = this.data.filter(item => item.advisor_emailTemplates &&
        item.advisor_emailTemplates.find(i => i.tags && i.tags.includes('TC'))).length > 0
      if (need_tc) {
        const params = {
          is_current: true,
          type: 'DEFAULT',
        }
        return TcAPI.getTCList(params)
          .then(data => {
            this.$set(this, 'latest_default_tc', data[0])
          })
      } else {
        return Promise.resolve()
      }
    },

    getScheduleData() {
      const promise = []
      // 获取每条task的schedule，并处理添加arranged的时间段
      this.data.forEach(item => {
        promise.push(this.getSchedule(item))
      })
      this.scheduleLoading = true
      return Promise.all(promise).then(() => {
        this.data.forEach(item => {
          item.schedule.forEach(e => {
            // if (e.creator_type === 'PM') {
            //   // this.getNewScheduleData(e, item)
            //   if (Number(e.task_id) !== Number(item.task.id)) {
            //     e.editable = false
            //     const is_current_project = Number(e.project_id) === Number(this.$route.params.project_id)
            //     e.creator_type = is_current_project ? 'PROJECT_ARRANGED' : 'OTHER_ARRANGED'
            //     e.backgroundColor = is_current_project ? this.typeColor.arranged : this.typeColor.other_arranged
            //     e.borderColor = is_current_project ? this.typeColor.arranged : this.typeColor.other_arranged
            //     e.title = is_current_project ? item.task.advisor.full_name : 'other'
            //   }
            // }
          })

          const task_outsource_arrangement = item.task.task_outsource_info?.task_outsource_arrangement
          if (task_outsource_arrangement) {
            const interview_info = task_outsource_arrangement.communication_tool_props?.interview_info.replace(/\n/g, '<br>').replace(/\t/g, '&emsp;')
            item.task.arrange_advisor_bridge_info_str = interview_info
            item.task.arrange_detail_str = interview_info
            item.schedule.unshift({
              start: moment(task_outsource_arrangement.start).tz(item.selectTimeZone).format(),
              end: moment(task_outsource_arrangement.end).tz(item.selectTimeZone).format(),
              task_id: item.id,
              editable: false,
              title: `Outsource Arranged`,
              creator_type: 'OUTSOURCE_ARRANGED',
              backgroundColor: this.typeColor.outsource,
              borderColor: this.typeColor.outsource,
            })
          }
        })
      }).finally(() => {
        this.scheduleLoading = false
      })
    },
    getSchedule(item) {
      const params = {
        param_preset: 'ARRANGE',
        advisor_id: item.task.advisor_id,
        task_id: item.task.id,
        client_id: item.task.client_id,
        extra: 'advisor',
      }
      return API.getArrangeSchedule(params).then(data => {
        item.schedule = data.map(obj => {
          let color
          let title = ''
          let editable = false
          switch (obj.creator_type) {
            case 'ADVISOR':
              color = this.typeColor.advisor
              title = 'Advisor'
              break
            case 'CLIENT_CONTACT':
              color = this.typeColor.client
              title = 'Client Contact'
              break
            case 'PM':
              if (Number(obj.task_id) !== Number(item.task.id)) {
                const is_current_project = Number(obj.project_id) === Number(this.$route.params.project_id)
                obj.creator_type = is_current_project ? 'PROJECT_ARRANGED' : 'OTHER_ARRANGED'
                color = is_current_project ? this.typeColor.arranged : this.typeColor.other_arranged
                title = is_current_project ? obj.advisor.full_name : 'other'
              }
              break
            default:
              editable = true
              break
          }
          return Object.assign({
            start: moment(obj.start_time).tz(item.selectTimeZone).format(),
            end: moment(obj.end_time).tz(item.selectTimeZone).format(),
            editable,
            title: title,
            backgroundColor: color,
            borderColor: color,
          }, obj)
        })

        this.getSelectedSchedule(item)
      })
    },
    // 新建时间段或修改arrange安排时为其他scheduler添加arranged时间段
    getNewScheduleData(obj, task) {
      // const event = JSON.parse(JSON.stringify(obj))
      // Object.assign(event, {
      //   task_id: task.task.id,
      //   editable: false,
      //   title: `${task.task.advisor.name_prefix || ''} ${task.task.advisor.full_name} arranged`,
      //   creator_type: 'OTHER_ARRANGED',
      //   backgroundColor: this.typeColor.other_arranged,
      //   borderColor: this.typeColor.other_arranged,
      // })
      // this.data.forEach(item => {
      //   if (item.id !== task.task.id) {
      //     item.schedule.push(event)
      //   }
      // })
      // this.getSelectedSchedule(Object.assign(event, task))
      this.getSelectedSchedule(task)
    },
    deleteScheduleData(task) {
      this.data.forEach(item => {
        if (item.id !== task.task.id) {
          const index = item.schedule.findIndex(e => e.task_id === task.task.id)
          item.schedule.splice(index, 1)
        }
      })
      task.selectFormatTime = ''
    },
    resizeScheduleData(obj, task) {
      const event = JSON.parse(JSON.stringify(obj))
      this.data.forEach(item => {
        if (item.id !== task.task.id) {
          item.schedule.forEach(e => {
            if (e.id === event.id) {
              e.start = event.start
              e.end = event.end
            }
          })
        }
      })
      this.getSelectedSchedule(task)
    },
    getSelectedSchedule(task) {
      const time = task.schedule.find(e => e.creator_type === 'PM' && parseInt(e.task_id) === parseInt(task.id))
      task.selectFormatTime = time
    },
    actionGotoClientTask() {
      let target_router_name = ''
      const item = this.tasks[0]
      if (item.project.sub_type === 'Investor Call') {
        target_router_name = 'ProjectClientTasksSST'
      } else if (item.client_id === this.McKinseyId) {
        target_router_name = 'ProjectClientTasksMcK'
      } else {
        target_router_name = 'ProjectClientTasks'
      }
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.push({
            name: target_router_name,
          })
        })
    },
    /**
     * 保存 Arrange 安排及发送邮件
     */
    submit() {
      this.recordingRepetitionConfirmationVisible = false
      // 检查未选择访谈时间
      const isNotArranged = this.data.find(item => {
        const flag = item.schedule.find(event => event.creator_type === 'PM')
        if (!flag) return item
      })

      const noClientEmail = this.data.find(item => item.client_email.email.to_list.length < 1)
      const noAdvisorEmail = this.data.find(
        item => item.task.compliance_passed_jit && item.advisor_email.email.to_list.length < 1)

      const other_templates_need_to_list = this.checkConferenceInviteesToList()

      const noArrangeBridge = this.data.find(item => !item.task.arrange_bridge_type)

      const needBridgeStr = this.data.find(
        item => item.task.arrange_bridge_type &&
          item.task.compliance_passed_jit && !item.advisor_email?.template?.tags?.includes('TC') &&
          !['LOOPUP', 'TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type) &&
          (!item.task.arrange_detail_str || !item.task.arrange_advisor_bridge_info_str))

      const tc_need_rate = this.data.filter(
        item => item.advisor_email.template?.tags?.includes('TC') &&
          (!item.tc_rate_currency || !/^\d+(?=\.{0,1}\d+$|$)/.test(item.tc_rate)))

      const enable_recording_prompt = this.data.filter(
        item => ['TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type) && !item.conference_arrangement.enable_record)

      if (tc_need_rate.length > 0) {
        const advisor_list = tc_need_rate.map(item => (`${item.task.advisor.name_prefix || ''} ${item.task.advisor.full_name}`)).join()
        this.$alert(`The expert's rate must be greater than or equal to 0 and have a currency.(${advisor_list})`,
          'Notice', {
            type: 'warning',
          })
        return
      }

      if (noClientEmail) {
        this.$alert(`The recipient of a client's email cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (other_templates_need_to_list.length > 0) {
        this.$alert(`Recipient cannot be empty (${other_templates_need_to_list.join()}).`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (noAdvisorEmail) {
        this.$alert(`The recipient of a advisor's email cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (noArrangeBridge) {
        this.$alert(`The arrangement bridge of the task cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (needBridgeStr) {
        this.$alert(`The details of the arrangement bridge of the task cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (isNotArranged) {
        this.$alert(`There is no arranged time in ${isNotArranged.task.advisor.name_prefix || ''} ${isNotArranged.task.advisor.full_name}`, 'Notice', {
          type: 'warning',
        })
        return
      }

      const storage_data = localStorage.getItem('Stop-prompting-for-recording-settings')
      const stop_prompting = JSON.parse(storage_data) && JSON.parse(storage_data).expired_at > moment().valueOf()

      if (enable_recording_prompt.length > 0 && !stop_prompting) {
        this.recordingRepetitionConfirmationVisible = true
        this.stop_prompting_for_recording_settings = false
        this.recording_change_setting_disabled = enable_recording_prompt.length === 1 && !enable_recording_prompt[0].record_and_transcript_can_check
        this.unopened_recording_tasks = enable_recording_prompt.map(item => {
          return { advisor_name: item.task.advisor.name, task_id: item.task.id }
        })
      } else {
        this.arrange()
      }
    },

    async arrange() {
      this.recordingRepetitionConfirmationVisible = false
      const allPromise = []
      for (const item of this.data) {
        // 发给专家的邮件 密送给当前db user
        item.advisor_email.email.bcc_list.push(this.$store.state.user.info.email)

        if (this.sendToClientThroughCapvisionPro(item.task)) {
          item.send_calendar_to_client = false
          item.send_mail_to_client = false
        }

        const schedule = item.schedule.find(event => event.creator_type === 'PM')
        const bridge_is_loopup = item.task.arrange_bridge_type === 'LOOPUP'
        const data = {
          task_id: item.task.id,
          data: {
            task: {
              client_contact_id: item.client_contact.id,
              interview_type: item.task.project.sub_type === 'Investor Call' ? 'INVESTOR_CALL' : null,
              loopup_room: (item.loopup_room && bridge_is_loopup) ? {
                id: item.loopup_room.id,
              } : undefined,
              arrange_bridge_type: item.task.arrange_bridge_type,
              arrange_detail_str: !bridge_is_loopup
                ? item.task.arrange_detail_str || null
                : null,
              arrange_advisor_bridge_info_str: !bridge_is_loopup
                ? item.task.arrange_advisor_bridge_info_str || null
                : null,
              arrange_contact_calendar_location: !bridge_is_loopup
                ? item.task.arrange_contact_calendar_location
                : null,
              arrange_advisor_calendar_location: !bridge_is_loopup
                ? item.task.arrange_advisor_calendar_location
                : null,
              advisor_loopup_area_id: this.isSGDB ? null : item.loopUp_location_advisor.id,
              contact_loopup_area_ids: this.isSGDB ? null : item.loopUp_location_client.map(item => item.id).join(),
            },
            schedule: {
              start_time: moment.tz(schedule.start, this.timeZone).toISOString(),
              end_time: moment.tz(schedule.end, this.timeZone).toISOString(),
              id: +schedule.id >= 1 ? schedule.id : undefined,
            },
            mail_to_contact: {
              email: item.client_email.email,
              is_send_email: item.send_mail_to_client,
              is_send_calendar: item.send_calendar_to_client,
            },
            mail_to_advisor: {
              email: item.advisor_email.email,
              is_send_email: item.send_mail_to_advisor,
              is_send_calendar: item.send_calendar_to_advisor },
            send_task_client_scheduled_email_to_advisor: false,
            loopup_area_id: this.isSGDB ? null : item.loopUp_location_advisor.id,
            ...formatConferenceParams(item),
          },
        }

        // 客户要求通过'capvision-pro'邮箱发送，系统将不会发送邮件给客户
        if (this.sendToClientThroughCapvisionPro(item.task)) {
          data.data.mail_to_contact.is_send_email = false
          data.data.mail_to_contact.is_send_calendar = false
          if (['TWILIO', 'ZOOM'].includes(data.data.task.arrange_bridge_type) && data.data.mails_to_others) {
            data.data.mails_to_others.mail_to_client_compliance.is_send_email = false
            data.data.mails_to_others.mail_to_client_compliance.is_send_calendar = false
            data.data.mails_to_others.mail_to_client_sponsor.is_send_email = false
            data.data.mails_to_others.mail_to_client_sponsor.is_send_calendar = false
          }
        }

        const has_tc_template = item.advisor_email.template.tags && item.advisor_email.template.tags.includes('TC')
        if (has_tc_template) {
          const params = {
            portal: {
              type: 'ADVISOR_PRE_CALL',
              advisor_id: item.task.advisor_id,
              task_id: item.task.id,
              project_id: item.task.project_id,
              advisor_tc: {
                tc_id: this.latest_default_tc.id,
                tc_template_id: this.latest_default_tc.templates[0].id,
                rate: item.tc_rate,
                rate_currency: item.tc_rate_currency,
              },
            },
            email_template_tags: item.advisor_email.template.tags,
          }
          await ConsultantAPI.sendConsultantPortal(params).then(response => {
            data.data.portal_advisor_id = response.id
          }).then(() => {
            // tc 没签 只能schedule
            allPromise.push(API.scheduleTask(data))
          })
        } else {
          item.task.compliance_passed_jit ? allPromise.push(API.arrangeTask(data)) : allPromise.push(API.scheduleTask(data))
        }
      }

      this.submitLoading = true
      await Promise.all(allPromise).then(res => {
        if (this.stop_prompting_for_recording_settings && this.unopened_recording_tasks.length) {
          const storage_data = {
            value: true,
            expired_at: moment().add(1, 'year').valueOf(),
          }
          localStorage.setItem('Stop-prompting-for-recording-settings', JSON.stringify(storage_data))
        }
        if (res[0]?.sent_calendars) {
          const content = res[0]?.sent_calendars.find(item => item.schedule_role === 'CLIENT_CONTACT')?.content
          if (content) {
            copyUtil(content, true).then(() => {
              this.$message.success('Copy successful')
            }).catch(() => {
              this.$message.error('Copy failed')
            })
          } else {
            this.$message.error('Copy failed')
          }
        }
        this.$notify({
          title: 'Success',
          message: 'Arrange Tasks successfully!',
          type: 'success',
        })
        this.actionGotoClientTask()
      }).finally(() => {
        this.submitLoading = false
      })

      function formatConferenceParams(item) {
        if (!['TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type)) {
          return undefined
        } else {
          const initial_params = {
            email: null,
            is_send_email: false,
            is_send_calendar: false }
          const mails_to_others = {
            'mail_to_client_compliance': Object.assign({}, initial_params),
            'mail_to_client_sponsor': Object.assign({}, initial_params),
            'mail_to_moderator': Object.assign({}, initial_params),
            'mail_to_translator': Object.assign({}, initial_params),
          }

          const alternative_host_list = item.conference_arrangement.alternative_host_email_list?.map(item => {
            return {
              role: 'ALTERNATIVE_HOST',
              email: item,
            }
          })
          let conference_invitees = alternative_host_list || []
          item.conference_arrangement.conference_invitees.map(temp => {
            const transform = temp.toLowerCase()

            if (['client', 'expert'].includes(transform)) {
              const phone = item.conference_arrangement[`arrange_${transform}_phone`]
              const location = item.conference_arrangement[`arrange_${transform}_location`]
              if (transform === 'client') {
                item.client_email.email.to_list.forEach(i => {
                  conference_invitees.push({
                    role: temp,
                    'country': '',
                    'phone_number': '',
                    email: i,
                  })
                })
              } else {
                conference_invitees.push({
                  role: temp,
                  'country': location || '',
                  'phone_number': phone || '',
                })
              }
            } else {
              mails_to_others[`mail_to_${transform}`].email = item[`${transform}_email`].email
              mails_to_others[`mail_to_${transform}`].is_send_email = !!item.conference_arrangement[`send_mail_to_${transform}`]
              mails_to_others[`mail_to_${transform}`].is_send_calendar = !!item.conference_arrangement[`send_calendar_to_${transform}`]

              const list = item[`${transform}_email`].email.to_list.map(i => {
                return {
                  role: temp,
                  'country': '',
                  'phone_number': '',
                  email: i,
                }
              })
              conference_invitees = conference_invitees.concat(list)
            }
          })

          const arrangement = {
            'enable_auto_dial_expert': item.conference_arrangement.enable_auto_dial_expert,
            'enable_record': item.conference_arrangement.enable_record,
            'enable_transcription': item.conference_arrangement.enable_transcription,
            'transcription_type': item.conference_arrangement.transcription_type,
            enable_waiting_room: item.conference_arrangement?.enable_waiting_room,
            co_host_roles: '',
            conference_invitees: conference_invitees,
          }

          if (item.conference_arrangement.enable_as_co_host && item.conference_arrangement.co_host_roles.length) {
            arrangement.co_host_roles = item.conference_arrangement.co_host_roles.join()
          }

          return { mails_to_others, arrangement }
        }
      }
    },
    checkClientNoNeedCalendar(item) {
      // McKinsey 定制 不发日历
      // if (this.isMcKinsey) return true
      const has_rule = item.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        has_rule.compliance_rules.calendar_invite.is_do_not_send
    },

    checkClientCalendarSubjectIncludeAngle(item) {
      const has_rule = item.compliance_preference?.rule
      if (has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        has_rule.compliance_rules.calendar_invite.is_include_angle_name_in_subject) {
        return has_rule.compliance_rules.calendar_invite.include_angle_name_position
      }
    },

    sendToClientThroughCapvisionPro(item) {
      // 客户 法务规则勾选 Arrange/Emails and Invites from capvision-pro.com时，用户通过复制模板内容，自己从Outlook发送邮件，不在系统中发送给客户
      const has_rule = item.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        item.project.tpa_project
    },

    calendarCopyClientEmailList(item) {
      // 客户 法务规则勾选 Calendar Invite copy,抄送填写的邮箱地址
      const has_rule = item.compliance_preference?.rule
      const need_copy_client_user = has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        !has_rule.compliance_rules.calendar_invite.is_do_not_send
      return need_copy_client_user ? has_rule.compliance_rules.calendar_invite.copy_others_list : []
    },
    approvalCopyClientComplianceEmailList(item) {
      // 客户 法务规则Approval 抄送填写的邮箱地址
      const has_rule = item.compliance_preference?.rule
      const need_copy_client_compliance_user = has_rule && has_rule.compliance_rules?.approval.master_switch
      return need_copy_client_compliance_user ? has_rule.compliance_rules.approval.names_and_emails_to_send_approval_to : []
    },

    advisorNoEmail(task) {
      return task.advisor.contact_infos.filter(item => item.type === 'EMAIL').length < 1
    },

    checkConferenceInviteesToList() {
      const result_list = []
      this.data.forEach(item => {
        if (['TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type)) {
          const include_others = item.conference_arrangement.conference_invitees.filter(i => !['EXPERT', 'CLIENT'].includes(i))

          if (include_others.length > 0) {
            include_others.forEach(temp => {
              const transform = temp.toLowerCase()
              const target = item[`${transform}_email`].email
              if (!!item.conference_arrangement[`send_mail_to_${transform}`] && target.to_list.length < 1) {
                result_list.push(transform.replace('_', ' '))
              }
            })
          }
        }
      })
      return result_list
    },

    getMainPhone(contact_infos) {
      if (!contact_infos?.length) return null
      const phone_list = contact_infos.filter(item => item.type === 'PHONE')
      if (phone_list.length < 1) return null
      return phone_list.find(item => item.is_main)?.value || phone_list[0].value
    },

    initConferenceInvitees(task) {
      const has_rule = task.client.compliance_preference?.rule
      const need_add_client_compliance = has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_automatically_add_client_compliance_role
      const has_chaperones = task.client?.compliance_preference?.require_chaperone && task.task_client_chaperones?.length
      return need_add_client_compliance || has_chaperones ? ['CLIENT', 'EXPERT', 'CLIENT_COMPLIANCE'] : ['CLIENT', 'EXPERT']
    },
    getLocationPhoneForTwilio(item) {
      this.$set(item.conference_arrangement, 'arrange_client_location', item.client_contact?.location?.name || null)
      this.$set(item.conference_arrangement, 'arrange_client_phone', this.getMainPhone(
        item.client_contact?.contact_infos_without_mosaic))
    },
  },
}
</script>

<style scoped lang="scss">
.collapse-wrapper {
  margin: 16px 0;
}

.collapse-title {
  min-width: 200px;
}

.collapse-item-wrapper {
  height: 660px;
  overflow: hidden;
}

.format-str {
  padding: 0 8px;
  font-size: 11pt;
  font-family: Calibri;
  flex-shrink: 0;
}

::v-deep .el-collapse-item__header {
  height:100%;
  line-height: 100%;
  min-height: 54px;
}

.twilio-select {
  margin-left:8px;
  margin-right: 8px;
  width:200px;
  flex-shrink:0;

  ::v-deep .el-tag__close.el-icon-close{
    display: none;
  }
}

</style>

