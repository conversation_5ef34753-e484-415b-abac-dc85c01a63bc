<template>
  <div>
    <el-page-header content="Tasks Arrange" @back="actionGotoClientTask" />

    <div v-loading="loading" class="collapse-wrapper">
      <arrange-warning
        :client-id="$route.query.client_id"
        type="arrange" />
      <div v-if="noCommonContact || noValidContract">
        <el-alert
          v-if="noCommonContact"
          title="This project is missing the common type of client contact, please add it first"
          show-icon
          type="error"
          class="mb-8" />
        <el-alert
          v-if="noValidContract"
          title="This client does not have a valid contract and cannot be arranged"
          show-icon
          type="error"
          class="mb-8" />
      </div>
      <div v-else>
        <el-alert
          v-if="!roomCountLoading && !isSGDB"
          :title="`These is currently ${options.loopUp_rooms_list.length} free meeting rooms.`"
          :type="options.loopUp_rooms_list.length ? 'success' : 'error'"
          show-icon
          class="mb-8" />
        <el-alert
          v-if="data.length && sendToClientThroughCapvisionPro(data[0].task)"
          title="The client's legal rules have checked to send emails and calendar to this client through 'capvision-pro.com', the system will not send emails and calendar to this client. Please copy the email content yourself and send the email through the 'capvision-pro.com' mailbox."
          type="warning"
          show-icon
          :closable="false"
          class="mb-8 mt-8" />
      </div>

      <el-collapse v-model="activeCollapse" accordion>
        <el-collapse-item v-for="item in data" :key="item.id" :name="item.id">
          <template slot="title">
            <div class="collapse-title">
              <router-link
                class="text-truncate link"
                :to="{ name: 'ConsultantDetail', params: { advisor_id: item.task.advisor_id } }">
                {{ item.task.advisor.name_prefix || ''}}
                {{ item.task.advisor.full_name }}
              </router-link>
              <el-tooltip v-if="advisorNoEmail(item.task)" effect="dark" content="No email" placement="top">
                <el-link type="warning" :underline="false" icon="el-icon-warning" />
              </el-tooltip>
            </div>
            <el-select v-model="item.client_contact" value-key="id" class="flex-shrink-0" @change="getLocationPhoneForTwilio(item)">
              <el-option
                v-for="option in item.client_contacts_option"
                :key="option.id"
                :label="option.client_contact.name"
                :value="option.client_contact" />
            </el-select>
            <time-zone-select
              v-model="item.selectTimeZone"
              class="ml-8 inline-block flex-shrink-0"
              @change="formatSchedule(item)" />

            <el-dropdown
              trigger="click"
              @command="item.selectTimeZone = $event; formatSchedule(item);">
              <el-tooltip content="Quick Select Time Zone" placement="right">
                <el-button type="primary" plain circle icon="el-icon-caret-bottom" class="ml-8" @click.stop />
              </el-tooltip>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="!item.client_contact.zone_id_string"
                  :command="item.client_contact.zone_id_string">
                  Client’s Time Zone
                  <i v-if="item.client_contact.zone_id_string">--
                    {{ item.client_contact.zone_id_string }}</i>
                  <i v-else>(Not available, please update the client record)</i>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!item.task.advisor.zone_id_string"
                  :command="item.task.advisor.zone_id_string">
                  Advisor’s Time Zone
                  <i v-if="item.task.advisor.zone_id_string">--
                    {{ item.task.advisor.zone_id_string }}</i>
                  <i v-else>(Not available, please update the advisor record)</i>
                </el-dropdown-item>
                <el-dropdown-item :command="timeZone">My Time Zone <i>-- {{ timeZone }}</i></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <div v-if="item.selectFormatTime" class="format-str">
              <i v-if="scheduleLoading" class="el-icon-loading" />
              <slot v-if="!scheduleLoading && item.selectFormatTime">
                {{ item.selectFormatTime.start | momentFormatZone('ddd MM/DD: h:mma',item.selectTimeZone) }} -
                {{ item.selectFormatTime.end | momentFormatZone('h:mma',item.selectTimeZone) }}
              </slot>
            </div>

            <el-select
              v-model="item.task.arrange_bridge_type"
              value-key="id"
              placeholder="Arrange Bridge"
              class="ml-8 mr-8 w-200px flex-shrink-0"
              @change="setEmailTemplates(item,item.task.arrange_bridge_type)">
              <el-option
                v-for="room in options.arrange_bridge_types"
                :key="room.value"
                :label="room.label"
                :value="room.value" />
            </el-select>
            <a
              v-if="item.task.arrange_bridge_type === 'LOOPUP' && item.loopup_room"
              class="text-truncate link ml-8"
              :href="'//' + item.loopup_room.url"
              target="_blank">Loopup
              #{{ item.loopup_room.id }}</a>
          </template>
          <calendar
            v-model="item.schedule"
            single-select
            :select-time-zone="item.selectTimeZone"
            :task-id="item.id"
            :outsource="!!item.task.task_outsource_info"
            :class="{'collapse-item-wrapper': activeCollapse !== item.id}"
            @new="() => {getSelectedSchedule(item)}"
            @delete="() => {deleteScheduleData(item)}"
            @resize="(val) => {resizeScheduleData(val, item)}" />

          <div v-if="item.task">
            <twilio-arrange-email
              v-if="['TWILIO','ZOOM'].includes(item.task.arrange_bridge_type)"
              ref="twilioArrangeEmail"
              :item="item"
              :select-time-zone="item.selectTimeZone"
              :skip-email-tag="true"
              :arrange-options="options"
              :latest-default-tc="latest_default_tc"
              :advisor-templates="item.advisor_emailTemplates"
              :client-templates="item.client_emailTemplates"
              :calendar-copy-client-email-list="calendarCopyClientEmailList(item.task.client)"
              :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro(item.task)" />

            <arrange-email
              v-else
              :item="item"
              :skip-email-tag="true"
              :select-time-zone="item.selectTimeZone"
              :arrange-options="options"
              :latest-default-tc="latest_default_tc"
              :advisor-templates="item.advisor_emailTemplates"
              :client-templates="item.client_emailTemplates"
              :calendar-copy-client-email-list="calendarCopyClientEmailList(item.task.client)"
              :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro(item.task)" />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-button type="success" :disabled="!data.length" :loading="submitLoading" @click="submit">Submit</el-button>
    <el-button type="text" @click="actionGotoClientTask">Cancel</el-button>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import API from '@/api/project'
import Calendar from '@/components/Calendar'
import moment from 'moment-timezone'
import { RouteTools } from '@/utils/tool'
import detailMixins from './../../mixins/mixins'
import ArrangeWarning from '@/components/ComplianceWarning/Index'
import TwilioArrangeEmail from '@/views/project/consultation/detail/tasks-arrange/components/TwilioZoomArrangeEmail'
import ArrangeEmail from './../components/ArrangeEmail'
import { AppHttpService } from '@/utils/request'
import EmailAPI from '@/api/email'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
export default {
  name: 'TasksVettingCall',
  components: { Calendar, ArrangeEmail, ArrangeWarning, TimeZoneSelect, TwilioArrangeEmail },
  mixins: [detailMixins],
  data() {
    return {
      loading: false,
      roomCountLoading: true,
      scheduleLoading: false,
      submitLoading: false,
      noCommonContact: false,
      noValidContract: false,
      activeCollapse: '',
      typeColor: {
        advisor: '#67C23A',
        client: '#E6A23C',
        arranged: '#891ae4',
        other_arranged: '#a7a5a8',
        pm: '#3788D8',
        outsource: '#fdb4b4',
      },
      filterTasks: [],
      data: [],
      options: {
        loopUp_location_list: [],
        loopUp_rooms_list: [],
        arrange_bridge_types: [
          { label: 'Client Provided Bridge', value: 'CLIENT_PROVIDED' },
          { label: 'Internal Zoom', value: 'INTERNAL_ZOOM' },
          { label: 'ZipDX', value: 'ZIPDX' },
          { label: 'Other', value: 'OTHER' },
        ],
      },
      latest_default_tc: null,
      emailTemplates: {
        advisor_emailTemplates: [],
        client_emailTemplates: [],
      },
      task_detail_extra: [
        'advisor_profile',
        'advisor.location',
        'advisor.contact_infos',
        'advisor.contact_infos_without_mosaic',
        'advisor.jobs.company',
        'loopup_room',
        'advisor_schedules',
        'advisor_record_consent',
        'project.project_client_contacts.client_contact.location',
        'project.project_client_contacts.client_contact.contact_infos_without_mosaic',
        'project.project_client_contacts.client_contact.office.location',
        'latest_submitted_sq.questions',
        'latest_submitted_sq.answers',
        'latest_submitted_sq.branch',
        'client_contact.contact_infos',
        'client_contact.contact_infos_without_mosaic',
        'client.contracts',
        'client.compliance_preference',
        'client.preference',
        'client_custom_fields',
        'default_client_rate_usd',
        'default_client_rate_usd_considering_unsigned_tc',
        'standard_rate_multiplier_display_to_client',
        'given_senior_rate_multiplier',
        'vetting_call_arrangement.conference_invitees',
        'task_outsource_info.task_outsource_arrangement',
      ],
    }
  },
  computed: {
    ...mapState({
      tasks: state => state.tasks.list,
      timeZone: state => state.app.timeZone,
      McKinseyId: state => state.app.McKinseyId,
    }),

    isMcKinsey() {
      return +this.$route.query.client_id === this.McKinseyId
    },

    isSGDB() {
      return import.meta.env.VITE_APP_DB_NAME && import.meta.env.VITE_APP_DB_NAME === 'SG'
    },
  },
  mounted() {
    this.loading = true

    if (!this.isSGDB) {
      this.options.arrange_bridge_types.unshift({ label: 'Loopup', value: 'LOOPUP' })
      this.options.arrange_bridge_types.unshift({ label: 'Twilio', value: 'TWILIO' })
      this.options.arrange_bridge_types.unshift({ label: 'Zoom BETA', value: 'ZOOM' })
    }

    Promise.all([this.getTasksDetail(), this.getLoopUpRoomsList(), this.getEmailTemplateList()])
      .then(this.getOptions)
      .then(this.handleTasks)
      .then(this.getScheduleData)
      .finally(() => {
        this.loading = false
      })
  },
  methods: {
    formatSchedule(data) {
      data.schedule.forEach(item => {
        if (item.creator_type !== 'PM') {
          item.start = moment(item.start_time).tz(data.selectTimeZone).format()
          item.end = moment(item.end_time).tz(data.selectTimeZone).format()
        }
      })
    },
    getTasksDetail() {
      const params = {
        project_id: this.$route.params.project_id,
        params: {
          ids: this.$route.query.id,
          extra: this.task_detail_extra.join(),
        },
      }
      return API.getTasksDetail(params)
        .then(data => {
          this.$store.dispatch('tasks/SET_LIST', data['list'])
        })
    },
    getOptions() {
      if (this.isSGDB) return Promise.resolve()

      return AppHttpService
        .get('/common/loopup/numbers')
        .then(list => {
          this.options.loopUp_location_list = list
        })
    },
    async getEmailTemplateList() {
      const content_types = ['ARRANGE_VETTING_CALL_TO_ADVISOR', 'ARRANGE_VETTING_CALL_TO_CONTACT']
      const client_email_params = {
        has_permission: true,
        content_type: content_types[1],
        reference_ids: [0, this.$route.query.client_id].join(),
        reference_type: 'CLIENT',
        sort: 'reference_id desc',
        include_default_reference_type: true,
      }
      const request_advisor = EmailAPI.getEmailList({ has_permission: true, content_type: content_types[0] })
      const request_contact = EmailAPI.getEmailList(client_email_params)
      const data = await Promise.all([request_advisor, request_contact])
      this.emailTemplates['advisor_emailTemplates'] = data[0].list
      this.emailTemplates['client_emailTemplates'] = data[1].list
    },
    async getLoopUpRoomsList() {
      if (!this.isSGDB) {
        this.options.loopUp_rooms_list = await API.getLoopUpRoomsList()
      }
      this.roomCountLoading = false
    },
    handleTasks() {
      this.data = []

      const data = []

      this.tasks.forEach(item => {
        const client_contacts_option = item.project.project_client_contacts.filter(
          item => item.client_contact.types.includes('COMMON'))
        if (!client_contacts_option.length) {
          this.noCommonContact = true
          return
        }

        const client_has_valid_contract = item.client.contracts.filter(
          item => (item.payway !== 'PROJECT_BASE' && item.effective_status === 'EFFECTIVE'))
        if (!client_has_valid_contract.length) {
          this.noValidContract = true
          return
        }

        if (item.project.client_provided_bridge_link && !item.vetting_call_arrangement?.bridge_type) {
          item.arrange_bridge_type = 'CLIENT_PROVIDED'
          item.arrange_detail_str = item.project.client_provided_bridge_link.replace(/\n/gi, '<br>')
          item.arrange_advisor_bridge_info_str = item.project.client_provided_bridge_link.replace(/\n/gi, '<br>')
          item.arrange_contact_calendar_location = item.project.client_provided_bridge_link.replace(/\n/gi, ' ')
          item.arrange_advisor_calendar_location = item.project.client_provided_bridge_link.replace(/\n/gi, ' ')
        } else {
          item.arrange_detail_str = item.vetting_call_arrangement?.arrange_detail_str
          item.arrange_advisor_bridge_info_str = item.vetting_call_arrangement?.arrange_advisor_bridge_info_str
          item.arrange_advisor_calendar_location = item.vetting_call_arrangement?.arrange_advisor_calendar_location || ''
          item.arrange_contact_calendar_location = item.vetting_call_arrangement?.arrange_contact_calendar_location || ''
        }

        let loopup_room = null
        if (!item.loopup_room && data.length < this.options.loopUp_rooms_list.length) {
          loopup_room = this.options.loopUp_rooms_list[data.length]
        }

        // 如果已经schedule，取上次选择且仍旧存在于项目的客户联系人;否则 取项目的 project_client_contacts type为common 的第一个客户联系人
        let client_contact_obj = client_contacts_option[0].client_contact
        if (item.client_contact_id) {
          const last_selected_client_contact = client_contacts_option.find(i => i.client_contact.id === item.client_contact_id)
          if (last_selected_client_contact?.client_contact) {
            client_contact_obj = last_selected_client_contact.client_contact
          }
        }
        item.arrange_bridge_type = item.vetting_call_arrangement?.bridge_type || 'LOOPUP'

        // twilio 接桥信息 初始化 || 获取上次操作的数据
        const consulting_client = item.client.type === 'CONSULTING_FIRM'
        // 咨询类客户 或者 专家已经同意开启录音时，录音和转录都可选
        item.record_and_transcript_can_check = consulting_client || item.advisor_record_consent?.is_consent
        const alternative_host_list = item.arrangement?.conference_invitees?.filter(item => item.role === 'ALTERNATIVE_HOST').map(item => item.email)

        item.conference_arrangement = {
          conference_invitees: ['CLIENT', 'EXPERT'],
          enable_auto_dial_expert: false,
          enable_record: item.record_and_transcript_can_check && item.client.preference?.require_call_recording,
          enable_transcription: item.record_and_transcript_can_check && item.client.preference?.require_call_transcript,
          transcription_type: 'DEFAULT',
          arrange_expert_location: item.advisor.location?.name,
          arrange_expert_phone: this.getMainPhone(item.advisor.contact_infos_without_mosaic),
          arrange_client_location: client_contact_obj?.location?.name || null,
          arrange_client_phone: this.getMainPhone(client_contact_obj?.contact_infos_without_mosaic),
          // zoom
          enable_waiting_room: item.arrangement?.enable_waiting_room || false,
          enable_as_co_host: !!item.arrangement?.co_host_roles || true,
          co_host_roles: (item.arrangement?.co_host_roles && item.arrangement?.co_host_roles.split(',')) || ['CLIENT'],
          alternative_host_email_list: alternative_host_list || [],
        }
        if (['TWILIO', 'ZOOM'].includes(item.arrange_bridge_type) && item.vetting_call_arrangement) {
          item.conference_arrangement.enable_record = item.record_and_transcript_can_check && item.vetting_call_arrangement.enable_record
          item.conference_arrangement.enable_transcription = item.record_and_transcript_can_check && item.vetting_call_arrangement.enable_transcription

          if (item.vetting_call_arrangement.conference_invitees.length > 0) {
            item.conference_arrangement.conference_invitees = []
            item.vetting_call_arrangement.conference_invitees.forEach(i => {
              if (!item.conference_arrangement.conference_invitees.includes(i.role)) {
                item.conference_arrangement.conference_invitees.push(i.role)
              }
            })
          }
        }

        if (this.isMcKinsey) {
          item.client_emailTemplates = this.emailTemplates['client_emailTemplates'].filter(t => {
            return t.reference_id === this.McKinseyId
          })
        }

        this.setEmailTemplates(item, item.arrange_bridge_type)
        const client_short_profile = item.client.preference && item.client.preference.short_form_profile

        const client_need_calendar = !this.checkClientNoNeedCalendar(item.client)
        const client_calendar_subject_include_angle = this.checkClientCalendarSubjectIncludeAngle(item.client)
        const send_mail_to_client = this.isMcKinsey
        const advisor_need_calendar = true
        data.push({
          id: item.id,
          task: item,
          sq: handleSQ(item['latest_submitted_sq']),
          client_contacts_option,
          client_contact: client_contact_obj,
          schedule: [],
          selectTimeZone: item.project.zone_id_string || this.timeZone,
          send_calendar_to_advisor: advisor_need_calendar,
          send_calendar_to_client: client_need_calendar,
          send_mail_to_client: send_mail_to_client,
          send_mail_to_advisor: true,
          tc_with_attachments: false,
          loopup_room,
          conference_arrangement: item.conference_arrangement,
          vetting_call_arrangement: item.vetting_call_arrangement,
          record_and_transcript_can_check: item.record_and_transcript_can_check,
          bridge_status: 0,
          advisor_email: {
            template: null,
            email: {
              subject: '',
              content: '',idea
            },
          },
          client_email: {
            template: null,
            email: {
              subject: '',
              content: '',
            },
          },
          client_emailTemplates: item.client_emailTemplates,
          advisor_emailTemplates: item.advisor_emailTemplates,
          client_short_profile,
          client_calendar_subject_include_angle,
        })
      })

      this.loading = false

      if (this.isSGDB) {
        this.data = this.data.concat(data)

        return Promise.resolve()
      } else {
        // 获取每个 task 对应的 loopUp_location
        const tasks = data.map(item => {
          return getLoopUpLocation(item)
        })

        return Promise
          .all(tasks)
          .then(poly => {
            const loopUp_location_us = this.options.loopUp_location_list.find(item => item['area_name'] === 'USA')

            data.forEach((item, index) => {
              const location = poly[index] || loopUp_location_us
              item.loopUp_location_advisor = location

              // client location 可多选
              const client_country_id = item.client_contact.location?.id_path.split(' ')[1]
              const client_location = this.options.loopUp_location_list.find(
                item => item.location_id === +client_country_id)
              item.loopUp_location_client = [client_location || loopUp_location_us]
            })

            this.data = this.data.concat(data)
          })
      }

      function getLoopUpLocation(data) {
        return AppHttpService
          .get('/common/loopup/number', { params: { location_id: data.task.advisor.location_id } })
      }

      function handleSQ(inquiry) {
        if (!inquiry) return null
        return {
          title: inquiry.branch_snapshot.title,

          description: inquiry.branch_snapshot.description,
          questions: inquiry.qa_list_snapshot.map(item => {
            if (item.sub_questions) {
              item.sub_questions.forEach(sub => {
                sub.answer = item.answer ? formatAnswer(item.answer) : ''
              })
            } else {
              item.answer = item.answer ? formatAnswer(item.answer) : ''
            }
            return item
          }),
        }
      }

      function formatAnswer(answer) {
        return Object.assign({}, answer, {
          id: answer.id,
          km_comment: answer.km_comment,
        })
      }
    },

    // target_item.bridge_status 用于 arrange email 模板替换监听
    // 麦肯锡的客户模板 用定制的 不用根据 bridge 切换
    setEmailTemplates(item, arrange_bridge_type) {
      this.activeCollapse = item.id
      switch ((arrange_bridge_type)) {
        case 'LOOPUP':
          item.bridge_status = 2
          this.filterTemplate(this.emailTemplates, 'LOOPUP', item, this.isMcKinsey)
          break
        case 'TWILIO':
          item.bridge_status = 3
          this.getLocationPhoneForTwilio(item)
          this.filterTemplate(this.emailTemplates, 'TWILIO', item, this.isMcKinsey)
          break
        case 'ZOOM':
          item.bridge_status = 4
          this.filterTemplate(this.emailTemplates, 'ZOOM', item, this.isMcKinsey)
          break
        default:
          item.bridge_status = 5
          this.filterTemplate(this.emailTemplates, 'OTHER_BRIDGE', item, this.isMcKinsey)
      }
    },

    filterTemplate(list, tag, target, isMcKinsey) {
      target.advisor_emailTemplates = list['advisor_emailTemplates'].filter(t => t.tags && t.tags.includes(tag))
      !isMcKinsey && (target.client_emailTemplates = list['client_emailTemplates'].filter(t => t.tags && t.tags.includes(tag)))
    },

    getScheduleData() {
      const promise = []
      // 获取每条task的schedule，并处理添加arranged的时间段
      this.data.forEach(item => {
        promise.push(this.getSchedule(item))
      })
      this.scheduleLoading = true
      return Promise.all(promise).then(() => {
        this.data.forEach(item => {
          const task_outsource_arrangement = item.task.task_outsource_info?.task_outsource_arrangement
          if (task_outsource_arrangement) {
            item.task.arrange_bridge_type = 'CLIENT_PROVIDED'
            item.schedule.unshift({
              start: moment(task_outsource_arrangement.start).tz(item.selectTimeZone).format(),
              end: moment(task_outsource_arrangement.end).tz(item.selectTimeZone).format(),
              task_id: item.id,
              editable: false,
              title: `Outsource Arranged`,
              creator_type: 'OUTSOURCE_ARRANGED',
              backgroundColor: this.typeColor.outsource,
              borderColor: this.typeColor.outsource,
            })
          }
        })
      }).finally(() => {
        this.scheduleLoading = false
      })
    },
    getSchedule(item) {
      const params = {
        param_preset: 'ARRANGE',
        advisor_id: item.task.advisor_id,
        task_id: item.task.id,
        client_id: item.task.client_id,
        extra: 'advisor',
      }
      const vetting_call = item.task.vetting_call_arrangement

      return API.getArrangeSchedule(params).then(data => {
        // Exclude the schedule that is arranged for the current task
        const filter_task_arrange_schedule = data.filter(temp => !(temp.creator_type === 'PM' && temp.task_id === item.task.id))
        item.schedule = filter_task_arrange_schedule.map(obj => {
          let color
          let title = ''
          let editable = false
          switch (obj.creator_type) {
            case 'ADVISOR':
              color = this.typeColor.advisor
              title = 'Advisor'
              break
            case 'CLIENT_CONTACT':
              color = this.typeColor.client
              title = 'Client Contact'
              break
            case 'PM':
              if (Number(obj.task_id) !== Number(item.task.id)) {
                const is_current_project = Number(obj.project_id) === Number(this.$route.params.project_id)
                obj.creator_type = is_current_project ? 'PROJECT_ARRANGED' : 'OTHER_ARRANGED'
                color = is_current_project ? this.typeColor.arranged : this.typeColor.other_arranged
                title = is_current_project ? obj.advisor.full_name : 'other'
              }
              break
            default:
              editable = true
              break
          }
          return Object.assign({
            start: moment(obj.start_time).tz(item.selectTimeZone).format(),
            end: moment(obj.end_time).tz(item.selectTimeZone).format(),
            editable,
            title: title,
            backgroundColor: color,
            borderColor: color,
          }, obj)
        })

        if (vetting_call) {
          item.schedule.push(
            {
              task_id: item.task.id,
              start: moment(vetting_call.start_time).tz(item.selectTimeZone).format(),
              end: moment(vetting_call.end_time).tz(item.selectTimeZone).format(),
              editable: true,
              title: '',
              creator_type: 'PM',
              backgroundColor: '',
              borderColor: '',
            })
        }
        this.getSelectedSchedule(item)
      })
    },

    deleteScheduleData(task) {
      this.data.forEach(item => {
        if (item.id !== task.task.id) {
          const index = item.schedule.findIndex(e => e.task_id === task.task.id)
          item.schedule.splice(index, 1)
        }
      })
      task.selectFormatTime = ''
    },
    resizeScheduleData(obj, task) {
      const event = JSON.parse(JSON.stringify(obj))
      this.data.forEach(item => {
        if (item.id !== task.task.id) {
          item.schedule.forEach(e => {
            if (e.id === event.id) {
              e.start = event.start
              e.end = event.end
            }
          })
        }
      })
      this.getSelectedSchedule(task)
    },
    getSelectedSchedule(task) {
      const time = task.schedule.find(e => e.creator_type === 'PM' && parseInt(e.task_id) === parseInt(task.id))
      task.selectFormatTime = time
    },
    actionGotoClientTask() {
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.push({
            name: 'ProjectClientTasksSST',
          })
        })
    },
    /**
     * 保存 Arrange 安排及发送邮件
     */
    async submit() {
      // 检查未选择访谈时间
      const isNotArranged = this.data.find(item => {
        const flag = item.schedule.find(event => event.creator_type === 'PM')
        if (!flag) return item
      })

      const noClientEmail = this.data.find(item => item.client_email.email.to_list.length < 1)
      const noAdvisorEmail = this.data.find(item => item.advisor_email.email.to_list.length < 1)

      const noArrangeBridge = this.data.find(item => !item.task.arrange_bridge_type)
      const needBridgeStr = this.data.find(
        item => item.task.arrange_bridge_type && !['LOOPUP', 'TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type) &&
          (!item.task.arrange_detail_str || !item.task.arrange_advisor_bridge_info_str))

      if (noClientEmail) {
        this.$alert(`The recipient of a client's email cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (noAdvisorEmail) {
        this.$alert(`The recipient of a advisor's email cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (noArrangeBridge) {
        this.$alert(`The arrangement bridge of the task cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (needBridgeStr) {
        this.$alert(`The details of the arrangement bridge of the task cannot be empty`, 'Notice', {
          type: 'warning',
        })
        return
      }

      if (isNotArranged) {
        this.$alert(`There is no arranged time in ${isNotArranged.task.advisor.name_prefix || ''} ${isNotArranged.task.advisor.full_name}`, 'Notice', {
          type: 'warning',
        })
      } else {
        const allPromise = []
        for (const item of this.data) {
          // 发给专家的邮件 密送给当前db user
          item.advisor_email.email.bcc_list.push(this.$store.state.user.info.email)

          if (this.sendToClientThroughCapvisionPro(item.task)) {
            item.send_calendar_to_client = false
            item.send_mail_to_client = false
          }

          const schedule = item.schedule.find(event => event.creator_type === 'PM')
          const bridge_is_loopup = item.task.arrange_bridge_type === 'LOOPUP'
          const params_data = {
            task_id: item.task.id,
            data: {
              arrangement: {
                bridge_type: item.task.arrange_bridge_type,
                start_time: moment.tz(schedule.start, this.timeZone).toISOString(),
                end_time: moment.tz(schedule.end, this.timeZone).toISOString(),
                id: +schedule.id >= 1 ? schedule.id : undefined,
                loopup_area_id: this.isSGDB ? null : item.loopUp_location_advisor.id,
                arrange_detail_str: !bridge_is_loopup
                  ? item.task.arrange_detail_str || null
                  : null,
                arrange_advisor_bridge_info_str: !bridge_is_loopup
                  ? item.task.arrange_advisor_bridge_info_str || null
                  : null,
                arrange_contact_calendar_location: !bridge_is_loopup
                  ? item.task.arrange_contact_calendar_location
                  : null,
                arrange_advisor_calendar_location: !bridge_is_loopup
                  ? item.task.arrange_advisor_calendar_location
                  : null,
                advisor_loopup_area_id: this.isSGDB ? null : item.loopUp_location_advisor.id,
                contact_loopup_area_ids: this.isSGDB ? null : item.loopUp_location_client.map(item => item.id).join(),
                ...formatConferenceParams(item),
                loopup_room: (item.loopup_room && bridge_is_loopup) ? {
                  id: item.loopup_room.id,
                } : undefined,

              },
              mail_to_contact: {
                email: item.client_email.email,
                is_send_email: item.send_mail_to_client,
                is_send_calendar: item.send_calendar_to_client },
              mail_to_advisor: {
                email: item.advisor_email.email,
                is_send_email: item.send_mail_to_advisor,
                is_send_calendar: item.send_calendar_to_advisor },
            },
          }

          // 客户要求通过'capvision-pro'邮箱发送，系统将不会发送邮件给客户
          if (this.sendToClientThroughCapvisionPro(item.task)) {
            params_data.params.mail_to_contact.is_send_email = false
            params_data.params.mail_to_contact.is_send_calendar = false
          }
          allPromise.push(API.vettingCallTask(params_data))
        }

        this.submitLoading = true
        await Promise.all(allPromise).then(() => {
          this.$notify({
            title: 'Success',
            message: 'Successfully arranged(Vetting Call)!',
            type: 'success',
          })
          this.actionGotoClientTask()
        }).finally(() => {
          this.submitLoading = false
        })
      }

      function formatConferenceParams(item) {
        if (!['TWILIO', 'ZOOM'].includes(item.task.arrange_bridge_type)) {
          return undefined
        } else {
          const conference_invitees = []
          item.conference_arrangement.conference_invitees.map(temp => {
            const transform = temp.toLowerCase()

            if (['client', 'expert'].includes(transform)) {
              const phone = item.conference_arrangement[`arrange_${transform}_phone`]
              const location = item.conference_arrangement[`arrange_${transform}_location`]
              if (transform === 'client') {
                item.client_email.email.to_list.forEach(i => {
                  conference_invitees.push({
                    role: temp,
                    'country': '',
                    'phone_number': '',
                    email: i,
                  })
                })
              } else {
                conference_invitees.push({
                  role: temp,
                  'country': location || '',
                  'phone_number': phone || '',
                })
              }
            }
          })

          const arrangement = {
            'enable_auto_dial_expert': item.conference_arrangement.enable_auto_dial_expert,
            'enable_record': item.conference_arrangement.enable_record,
            'enable_transcription': item.conference_arrangement.enable_transcription,
            conference_invitees: conference_invitees,
          }
          return arrangement
        }
      }
    },

    checkClientNoNeedCalendar(item) {
      const has_rule = item.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        has_rule.compliance_rules.calendar_invite.is_do_not_send
    },

    checkClientCalendarSubjectIncludeAngle(item) {
      const has_rule = item.compliance_preference?.rule
      if (has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        has_rule.compliance_rules.calendar_invite.is_include_angle_name_in_subject) {
        return has_rule.compliance_rules.calendar_invite.include_angle_name_position
      }
    },

    sendToClientThroughCapvisionPro(item) {
      // 客户 法务规则勾选 Arrange/Emails and Invites from capvision-pro.com时，用户通过复制模板内容，自己从Outlook发送邮件，不在系统中发送给客户
      const has_rule = item.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules.arrange_email.master_switch &&
        has_rule.compliance_rules.arrange_email.is_emails_and_invites_from_capvision_pro ||
        item.project.tpa_project
    },

    calendarCopyClientEmailList(item) {
      // 客户 法务规则勾选 Calendar Invite copy,抄送填写的邮箱地址
      const has_rule = item.compliance_preference?.rule
      const need_copy_client_user = has_rule && has_rule.compliance_rules.calendar_invite.master_switch &&
        !has_rule.compliance_rules.calendar_invite.is_do_not_send
      return need_copy_client_user ? has_rule.compliance_rules.calendar_invite.copy_others_list : []
    },

    advisorNoEmail(task) {
      return task.advisor.contact_infos.filter(item => item.type === 'EMAIL').length < 1
    },

    getMainPhone(contact_infos) {
      if (!contact_infos?.length) return null
      const phone_list = contact_infos.filter(item => item.type === 'PHONE')
      if (phone_list.length < 1) return null
      return phone_list.find(item => item.is_main)?.value || phone_list[0].value
    },

    getLocationPhoneForTwilio(item) {
      this.$set(item.conference_arrangement, 'arrange_client_location', item.client_contact?.location?.name || null)
      this.$set(item.conference_arrangement, 'arrange_client_phone', this.getMainPhone(
        item.client_contact?.contact_infos_without_mosaic))
    },
  },
}
</script>

<style scoped lang="scss">
.collapse-wrapper {
  margin: 16px 0;
}

.collapse-title {
  min-width: 200px;
}

.collapse-item-wrapper {
  height: 660px;
  overflow: hidden;
}

.format-str {
  padding: 0 8px;
  font-size: 11pt;
  font-family: Calibri;
  flex-shrink: 0;
}

::v-deep .el-collapse-item__header {
  height:100%;
  line-height: 100%;
  min-height: 54px;
}

</style>
