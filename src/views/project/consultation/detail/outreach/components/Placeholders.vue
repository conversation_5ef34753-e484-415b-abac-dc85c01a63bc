<template>
  <div class="inline-block mr-8">
    <el-popover
      v-model="visible"
      placement="top"
      width="300">
      <div>
        <div class="info text-left">Here are some common placeholders, click the corresponding button to copy the placeholder to the clipboard.</div>
        <div v-for="(value,key) in placeholder_list" :key="key" class="text-left">
          <div class="text-left"><b>{{key}}</b></div>
          <el-button v-for="(temp,index) in value"
                     :key="index"
                     type="primary"
                     size="xs"
                     plain
                     class="ma-3px"
                     @click="copyItem(temp)">{{ temp.label }}</el-button>
        </div>
      </div>
      <el-button slot="reference" size="xs" class="inline-block">Placeholders</el-button>
    </el-popover>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { copyUtil } from '@/utils/tool'
export default {
  name: 'Placeholders',
  data() {
    return {
      visible: false,
      // 暂定固定list，因为接口数据不支持特定outreach过滤，且在outreach阶段，有些占位符不会取到值
      common_list: {
        user: [
          { label: 'recruiter signature', value: 'RECRUITER_SIGNATURE' },
          { label: 'recruiter name', value: 'RECRUITER_NAME' },
          { label: 'recruiter given name', value: 'RECRUITER_GIVEN_NAME' },
          { label: 'recruiter title', value: 'RECRUITER_TITLE' },
          { label: 'recruiter phone', value: 'RECRUITER_PHONE' },
          { label: 'recruiter email', value: 'RECRUITER_EMAIL' },
        ],
        advisor: [
          { label: 'full name', value: 'ADVISOR_FULL_NAME' },
          { label: 'first name', value: 'ADVISOR_FIRST_NAME' },
          { label: 'last name', value: 'ADVISOR_LAST_NAME' },
        ],
        project: [
          { label: 'name', value: 'PROJECT_NAME' },
          { label: 'manager name', value: 'PROJECT_MANAGER_NAME' },
          { label: 'manager email', value: 'PROJECT_MANAGER_EMAIL' },
          { label: 'support member emails', value: 'PROJECT_SUPPORT_MEMBER_EMAILS' },
        ],
      },
      list_for_survey: {
        angle: [
          { label: 'name', value: 'ANGLE_NAME' },
          { label: 'topic', value: 'ANGLE_TOPIC' },
          { label: 'discussion topic', value: 'ANGLE_DISCUSSION_TOPIC' },
        ],
        task: [
          { label: 'start time', value: 'TASK_START_TIME' },
          { label: 'end time', value: 'TASK_END_TIME' },
          { label: 'rate with currency', value: 'TASK_RATE_WITH_CURRENCY' },
          { label: 'portal advisor 3rd party survey link', value: 'PORTAL_ADVISOR_3RD_PARTY_SURVEY_LINK' },
          { label: 'portal advisor one click decline link', value: 'PORTAL_ADVISOR_DECLINE_LINK' },
        ],
      },
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),

    placeholder_list() {
      let result_list = { ...this.common_list }
      if (this.projectInfo.sub_type === 'Survey') {
        result_list = Object.assign(result_list, this.list_for_survey)
      }
      return result_list
    },
  },

  methods: {
    copyItem(item) {
      copyUtil(`{{${item.value}}}`, false)
        .then(() => {
          this.$message.success('Copy successful')
        })
    },
  },
}
</script>

<style scoped>
.ma-3px{
  margin:3px !important;
}
</style>
