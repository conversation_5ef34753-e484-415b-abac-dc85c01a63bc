<template>
  <div v-loading="loading || contract_loading">
    <el-page-header content="Task Complete" @back="actionGotoDetail" />
    <post-call-rule :client-id="client_id" type="postCall" />
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="160px">
      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Basic Information</span>
        </div>
        <el-form-item v-if="!!options.sub_type.length" label="Subtype" prop="sub_type">
          <el-select
            v-model="form.sub_type"
            :disabled="(isSpeciallyForInvestmentBankClient || isOutsourceSurveyProject || isOutsourceConferenceProject) ? true : hasBilled"
          >
            <el-option
              v-for="item in options.sub_type"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Client Duration" prop="client_duration">
          <el-input-number
            v-model="form.client_duration"
            :min="0"
            :disabled="hasBilled"
            :step="1"
            :precision="0"
            @change="calculateEndTime"
          />
          <span class="ml-8">Minutes</span>
        </el-form-item>
        <el-form-item label="Start Time" required style="margin-bottom: 0;">
          <el-form-item prop="time_info.start_date" class="inline-block">
            <el-date-picker
              v-model="form.time_info.start_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              value-format="yyyy-MM-dd"
              :disabled="hasBilled"
              @change="handleStartDateChange" />
          </el-form-item>
          -
          <el-form-item prop="time_info.start_time" class="inline-block">
            <el-time-picker
              v-model="form.time_info.start_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm"
              :disabled="hasBilled"
              @change="handleStartTimeChange" />
          </el-form-item>

          <el-tooltip
            v-if="isStartOverNow"
            effect="dark"
            content="Start Time cannot be later than now."
            placement="top">
            <el-link type="warning" :underline="false" icon="el-icon-warning tooltip-icon" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="End Time" required>
          <el-form-item prop="time_info.end_date" class="inline-block" style="margin-bottom: 0;">
            <el-date-picker
              v-model="form.time_info.end_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              :disabled="hasBilled"
              value-format="yyyy-MM-dd" />
          </el-form-item>
          -
          <el-form-item prop="time_info.end_time" class="inline-block" style="margin-bottom: 0;">
            <el-time-picker
              v-model="form.time_info.end_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm"
              disabled
              @change="calculateHour" />
          </el-form-item>

          <el-tooltip
            v-if="isEndOverNow"
            effect="dark"
            content="End Time cannot be later than now."
            placement="top">
            <el-link type="warning" :underline="false" icon="el-icon-warning tooltip-icon" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="Lead" prop="lead_uid">
          <el-select v-model="form.lead_uid">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
        </el-form-item>
        <el-form-item label="Support" prop="support_uid" style="margin-bottom: 0;">
          <el-select v-model="form.support_uid" :disabled="supportDisabled">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
          <el-tooltip
            v-if="supportDisabled"
            effect="dark"
            placement="top"
            content="The specialist's TC is signed within one month and cannot be changed by the supporter.">
            <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
          </el-tooltip>
        </el-form-item>
      </el-card>

      <el-card v-if="isKoraCapital" class="box-card" shadow="none">
        <div slot="header">
          Client Compliance Information
        </div>
        <el-form-item label="Approval Date" prop="compliance_approval_date" style="margin-bottom: 0;">
          <el-date-picker
            v-model="form.compliance_approval_date"
            class="time-picker"
            type="date"
            placeholder="Select"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-card>

      <el-card class="box-card" shadow="none">
        <div slot="header">
          Client Charge Information
          <span v-show="!bound_contract && task" class="warning ml-8"><i class="el-icon-warning" /> There is currently no valid contract!</span>
        </div>
        <el-form-item label="Client Hour" prop="client_hours">
          <div class="flex align-items-center">
            <el-input-number
              v-model="form.client_hours"
              :disabled="hasBilled || isNoCharge"
              :min="client_hour_min"
              :step="0.25"
              type="number"
              class="client-hour-input">
              <el-button
                slot="prepend"
                icon="el-icon-minus"
                :disabled="hasBilled"
                @click="form.client_hours >= 1 ? form.client_hours -= 1 : form.client_hours = 0" />
              <el-button slot="append" icon="el-icon-plus" :disabled="hasBilled" @click="form.client_hours += 1" />
            </el-input-number>
            <span v-if="hasTimeMinimumCharge" class="info ml-3">
              {{ task.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
            </span>
            <el-tooltip v-if="bound_contract" effect="dark" placement="top">
              <div v-if="viewData.client_hours" slot="content">
                <table>
                  <thead>
                    <th align="left" width="100">Minutes</th>
                    <th align="left">CapH</th>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in viewData.client_hours.rules" :key="index">
                      <td v-for="rule in item" :key="rule">{{ rule }}</td>
                    </tr>
                  </tbody>
                </table>
                <span>{{ viewData.client_hours.addition }}</span>
              </div>
              <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
            </el-tooltip>
          </div>
        </el-form-item>

        <billing-notes-custom
          ref="billing-notes-custom"
          :form.sync="form"
          :is-b-c-g="isBCG"
          :bound-contract="bound_contract"
          :view-data="viewData"
          :has-billed="hasBilled"
          @calculateHour="form.client_hours = task.client_hours ?? undefined" />
      </el-card>

      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Payment Information</span>
        </div>
        <div class="flex justify-content-between">
          <div>
            <el-form-item label="Minutes" prop="payment.minutes">
              <el-input-number v-model="form.payment.minutes" :min="advisor_min_minutes" @change="calculatePaymentAmount" />
              <span v-if="hasTimeMinimumCharge" class="info ml-3">
                {{ task.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
              </span>
            </el-form-item>
            <el-form-item label="Amount" prop="payment.amount">
              <el-input-number
                v-model="form.payment.amount"
                :min="advisor_min_amount"
                class="mr-8"
                @change="checkW9Status()" />
              {{ form.payment.currency }}
              <el-tooltip v-if="task" effect="dark" placement="top">
                <div slot="content">
                  <span>Rate: {{ task.advisor.rate }} {{ task.advisor.rate_currency }}/H</span>
                </div>
                <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
              </el-tooltip>
              <el-checkbox v-model="form.should_network_loyalty_fee" class="ml-8">Network Loyalty Fee</el-checkbox>
              <el-alert
                v-if="isRateChanged"
                type="warning"
                show-icon
                :closable="false"
                class="mt-8">
                <template #title>
                  Rate has changed.
                  <el-link
                    type="primary"
                    :underline="false"
                    style="vertical-align: baseline;"
                    @click="calculatePaymentAmountByNewRate()">
                    Click to calculate amount with current rate.
                  </el-link>
                </template>
              </el-alert>
            </el-form-item>
            <el-form-item
              v-if="form.should_network_loyalty_fee"
              label="Network Loyalty Fee"
              prop="payment.network_loyalty_fee">
              <el-input-number
                v-model="form.payment.network_loyalty_fee"
                :min="0"
                class="mr-8" />
              {{ form.payment.currency }}
            </el-form-item>
            <el-form-item v-if="form.w9_status!==0" label="Form W-9">
              <div v-if="[1,3,4].includes(form.w9_status)">
                <div v-if="form.w9_status===3">Under signing...</div>
                <div v-if="form.w9_status===4">Signed</div>
                <div class="info">Experts have received ${{ calculate_advisor_paid_this_year }} this year.</div>
              </div>
              <div v-if="form.w9_status===2">
                <el-checkbox v-model="form.should_send_w9_form">Send "Form W-9"
                </el-checkbox>
                <div class="info">Expert's payment has over ${{ calculate_advisor_paid_this_year }} this
                  year,need to send w9 forms to experts to fill in tax
                  information.
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="form.enable_physical_check_payment_method">Enable Physical Check Payment Method</el-checkbox>
            </el-form-item>
          </div>
          <rate-board
            v-if="task"
            :task="task"
          />
        </div>

        <!-- <el-form-item style="margin-bottom: 0;">
          <payment-info :task="task" />
        </el-form-item> -->
      </el-card>

      <slot v-if="!loading">
        <!-- is_cn / Outsource导入专家在美国项目complete 不用发送 payment information confirm -->
        <consultant-mail
          v-if="!isImportedFromCNDB && !isImportedFromOutsource"
          ref="consultant-mail1"
          :form="form"
          :task="task"
          :portal="advisor_portal1"
          :type="1" />

        <consultant-mail
          v-if="!isImportedFromOutsource"
          ref="consultant-mail2"
          :form="form"
          :task="task"
          :portal="advisor_portal2"
          :type="2" />
        <!-- <client-mail ref="client-mail" :form="form" :task="task" :portal="contact_portal" :contract="bound_contract" /> -->
        <twilio-recording-transcript
          v-if="isTwilioOrZoomTask"
          :task="task"
          :form="form"
          :designated-recipient="specialRecipientForGA"
          :to-cc-options="projectClientContactEmailList"
          :send-to-client-through-capvision-pro="sendToClientThroughCapvisionPro"
          :client-compliance-review-first="needClientComplianceReviewFirst"
          :portal="asset_client_portal" />
      </slot>
    </el-form>
    <el-button type="success" :loading="submit_loading" :disabled="checkDisabled" @click="submit">Submit</el-button>
    <el-button type="text" @click="actionGotoDetail">Cancel</el-button>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import API from '@/api/project'
import moment from 'moment-timezone'
import BillingNotesCustom from './components/BillingNotesCustom'
// import PaymentInfo from './components/PaymentInfo'
import ConsultantMail from './components/ConsultantMail'
import TwilioRecordingTranscript from '@/views/project/consultation/detail/task-complete/components/TwilioRecordingTranscript'
// import ClientMail from './components/ClientMail'
import RateBoard from './components/RateBoard'
import PostCallRule from '@/components/ComplianceWarning/Index'
import { RouteTools } from '@/utils/tool'
import _ from 'lodash'

export default {
  name: 'TaskComplete',
  components: { BillingNotesCustom, ConsultantMail, PostCallRule, TwilioRecordingTranscript, RateBoard },
  data() {
    const validateDate = (rule, value, callback) => {
      const obj = this.form.time_info
      for (const key in obj) {
        if (!obj[key]) {
          return
        }
      }
      const t1 = moment(`${obj.end_date} ${obj.end_time}`)
      const t2 = moment(`${obj.start_date} ${obj.start_time}`)
      if (t1.diff(t2) < 0) {
        callback(new Error('End Time cannot be earlier than Start Time.'))
      } else {
        callback()
      }
    }
    const validateNumber = (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error('Input value must be greater than 0.'))
      } else {
        callback()
      }
    }
    return {
      loading: true,
      contract_loading: false,
      submit_loading: false,
      isRateChanged: false,
      calculate_advisor_paid_this_year: undefined,
      client_hour_min: 0,
      advisor_min_minutes: 0,
      advisor_min_amount: 0,
      form: {
        time_info: {
          start_date: '',
          start_time: '',
          end_date: '',
          end_time: '',
        },
        client_duration: undefined,
        lead_uid: undefined,
        sub_type: undefined,
        support_uid: undefined,
        members_list: [],
        client_hours: undefined,
        billingNotes: {
          has_discount: false,
          discount_hours: 1,
          has_senior: false,
          senior: 1.5,
          has_in_person: false,
          in_person: NaN,
          has_overseas: false,
          overseas: NaN,
          has_transcription: false,
          transcription: NaN,
          has_translation: false,
          translation: NaN,
          has_charge_cash: true,
          specified_charge_cash: undefined,
          specified_charge_cash_currency: 'USD',
          not_charged: false,
          not_charged_reason: '',
          not_charged_reason_input: '',
          remarks: '',
        },
        billing_hours: 0,
        billing_notes_str: '',
        w9_status: 0,
        should_send_w9_form: false,
        should_network_loyalty_fee: false,
        enable_physical_check_payment_method: false,
        payment: {
          minutes: 0,
          amount: 0,
          currency: '',
          rate: 0,
          network_loyalty_fee: 0,
        },
        compliance_approval_date: undefined,
      },
      options: {
        sub_type: [],
      },
      advisor_portal1: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      advisor_portal2: {
        template: null,
        is_send: true,
        task_ca: {
          inquiry: null,
          inquiry_branch: null,
        },
        email: {
          subject: '',
          content: '',
        },
      },
      contact_portal: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      asset_client_portal: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      rules: {
        'sub_type': [{ required: true, trigger: 'change', message: 'Subtype is required' }],
        'client_duration': [{ required: true, trigger: 'change', message: 'Client Duration is required' }],
        'lead_uid': [{ required: true, trigger: 'change', message: 'Lead is required' }],
        'support_uid': [{ required: true, trigger: 'change', message: 'Support is required' }],
        'time_info.start_date': [{ required: true, trigger: 'change' }],
        'time_info.start_time': [{ required: true, trigger: 'change' }],
        'time_info.end_date': [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        'time_info.end_time': [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        'client_hours': [{ required: true, trigger: 'change', message: 'Client Hour is required' }],
        'billingNotes.discount_hours': [
          { required: true, trigger: 'change' }, {
            validator: validateNumber,
            trigger: 'change',
          }],
        'billingNotes.senior': [
          { required: true, trigger: 'change' }, {
            validator: validateNumber,
            trigger: 'change',
          }],
        'billingNotes.specified_charge_cash': [{ required: true, trigger: 'change', message: 'Charge Total is required' }],
        'billingNotes.not_charged_reason': [{ required: true, trigger: 'change', message: 'No Charge Reason is required' }],
        'billingNotes.not_charged_reason_input': [{ required: true, trigger: 'change', message: 'No Charge Reason is required' }],
        'payment.minutes': [{ required: true, trigger: 'change' }],
        'payment.amount': [{ required: true, trigger: 'change' }],
        'compliance_approval_date': [{ required: true, trigger: 'change', message: 'Client Compliance Approval Date is required' }],
      },
      task: null,
      bound_contract: null,
      viewData: {},
      task_detail_extra: [
        'schedule',
        'advisor.location_id_path',
        'advisor.paid_usd_in_this_year_jit',
        // 'advisor.w9_form',
        'advisor.sourced_by',
        'has_billed_jit',
        'advisor.bank_account',
        'advisor_payments',
        'advisor_record_consent',
        'advisor_normal_payment.task_amount_hours',
        'payment_topic.payments.items.form',
        'project.members.user',
        'lead',
        'client.preference',
        'client.compliance_preference',
        'arrangement',
        'client_rate_usd',
        'default_client_rate_usd',
        'default_client_rate_usd_considering_unsigned_tc',
        'specified_client_rate_currency_after_multiple',
        'project',
        'task_outsource_info',
        'events',
      ].join(),
    }
  },
  computed: {
    ...mapState({
      client_id: state => state.project.data.client_id,
      timeZone: state => state.app.timeZone,
      isSGDB: state => state.app.isSGDB,
      userInfo: state => state.user.info,
      projectInfo: state => state.project.data,
    }),

    ...mapGetters([
      'advisor_options',
    ]),

    projectClientContactEmailList() {
      if (this.projectInfo?.project_client_contacts?.length) {
        const result_list = []
        this.projectInfo.project_client_contacts.forEach(item => {
          const list = item.client_contact?.contact_infos_without_mosaic?.filter(temp => temp.type === 'EMAIL' && temp.value) || []
          list.forEach(i => {
            result_list.push({ email: i.value, name: item.name })
          })
        })
        return result_list
      } else {
        return []
      }
    },

    isBCG() {
      return this.task?.client.preference?.preferred_workflow === 'BCG'
    },

    isKoraCapital() {
      return this.task?.client.preference?.preferred_workflow === 'KORA_CAPITAL'
    },

    isGeneralAtlantic() {
      return this.task?.client.preference?.preferred_workflow === 'GA'
    },

    specialRecipientForGA() {
      if (!this.isSGDB && this.isGeneralAtlantic && this.clientApproveBaseOnCA) {
        const test_env = ['test', 'dev'].includes(import.meta.env.VITE_APP_ENV)
        return test_env ? ['<EMAIL>'] : ['<EMAIL>']
      }
      return []
    },

    clientApproveBaseOnCA() {
      const client_rule = this.task?.client.compliance_preference?.rule
      return client_rule && client_rule.compliance_rules?.approval?.client_conditional_approve_type === 'Based on CA'
    },
    // 客户法务规则 需要用户通过capvision-pro邮箱发送邮件给客户
    sendToClientThroughCapvisionPro() {
      const has_rule = this.task?.client.compliance_preference?.rule
      return has_rule && has_rule.compliance_rules?.arrange_email.master_switch &&
        has_rule.compliance_rules?.arrange_email.is_emails_and_invites_from_capvision_pro ||
        this.projectInfo?.tpa_project
    },

    // 客户法务规则 需要法务预先审核 会议录音及速记
    needClientComplianceReviewFirst() {
      if (this.task.asset_portal_link_approved) {
        return false
      }
      const has_rule = this.task?.client.compliance_preference?.rule
      const need_client_compliance_review = has_rule && has_rule.compliance_rules.transcript_notetaking_rules.master_switch &&
        has_rule.compliance_rules.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first
      if (need_client_compliance_review) {
        return `Please send all transcript and recording links to (${has_rule.compliance_rules.transcript_notetaking_rules.require_compliance_review_email_list.join()}) for review and approval before sending to the client analyst.  To access the link, copy from the Scheduled Calls tab for this project via Actions.`
      } else {
        return false
      }
    },

    if_use_time() {
      if (this.form.time_info.start_date && this.form.time_info.start_time) {
        return moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time, this.timeZone)
          .toISOString()
      } else {
        return undefined
      }
    },
    isStartOverNow() {
      if (!this.form.time_info.start_date || !this.form.time_info.start_time) return false
      const start = moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time, this.timeZone)
      return moment().isBefore(start)
    },
    isEndOverNow() {
      if (!this.form.time_info.end_date || !this.form.time_info.end_time) return false
      const end = moment.tz(this.form.time_info.end_date + ' ' + this.form.time_info.end_time, this.timeZone)
      return moment().isBefore(end)
    },
    hasBilled() {
      return this.task && this.task.has_billed_jit
    },
    hasCA() {
      return !!this.advisor_portal2.task_ca.inquiry
    },
    isTwilioOrZoomTask() {
      return ['TWILIO', 'ZOOM'].includes(this.task.arrange_bridge_type) && (this.task.arrangement?.enable_record || this.task.arrangement?.enable_transcription)
    },
    isUSAdvisor() {
      return this.task && this.task.advisor && !this.task.advisor.is_cn
    },
    isImportedFromCNDB() {
      return this.task && this.task.advisor.is_cn
    },
    isImportedFromOutsource() {
      return this.task?.task_outsource_status === 'IMPORTED'
    },
    isNoCharge() {
      return this.form.billingNotes.not_charged && this.form.billingNotes.not_charged_reason !== 'Proposal'
    },
    isSpeciallyForInvestmentBankClient() {
      return this.task?.client.type === 'INVESTMENT_BANK' && ['Consultation', 'Investor Call'].includes(this.task?.project_sub_type)
    },
    isOutsourceSurveyProject() {
      return this.projectInfo?.outsource_project_type_enum === 'Survey'
    },
    isOutsourceConferenceProject() {
      return this.projectInfo?.outsource_project_type_enum === 'Conference'
    },
    clientRequiredPostCallCA() {
      return this.task && this.task.client.require_post_call_ca && !this.isImportedFromOutsource
    },
    checkDisabled() {
      return (this.clientRequiredPostCallCA && !this.hasCA) || !this.bound_contract || isNaN(this.form.client_hours)
    },
    sourceByWithinOneMonth() {
      let within_one_month = false
      const has_sourced = this.task && this.task.advisor && !!this.task.advisor.sourced_by_id
      if (has_sourced) {
        const task_start_time = moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
          this.timeZone).utc()
        const diff = task_start_time.diff(moment(this.task.advisor.sourced_at), 'days')
        within_one_month = diff >= 0 && diff <= 30
      }
      return within_one_month
    },
    // 专家签署TC后30天之内，support默认为tc的发送者
    // 新加坡DB不做限制
    // 有改source_by权限的，也可以改support，即使在30天之内
    supportDisabled() {
      const permissionAllowModifyExpertSourceBy = this.userInfo.roles.find(
        item => item.role.name === 'AllowModifyExpertSourceBy')
      return this.sourceByWithinOneMonth && !this.isSGDB && !permissionAllowModifyExpertSourceBy
    },
    hasTimeMinimumCharge() {
      return this.task && this.task.advisor?.time_minimum !== 'NO_MINIMUM'
    },
    timeMinimumFormat() {
      if (!this.task) return {}
      const time_minimum = this.task?.advisor?.time_minimum
      const time_minimum_options = this.advisor_options.time_minimum_charge
      const minimum_minutes = this.$options.filters.getName(time_minimum, time_minimum_options, 'value', 'minutes')
      const minimum_hour = this.$options.filters.getName(time_minimum, time_minimum_options, 'value', 'hour')
      return { minimum_minutes, minimum_hour }
    },
  },
  watch: {
    'timeZone': {
      handler(newVal, oldVal) {
        const _this = this
        handleTimeChange('start_date', 'start_time')
        handleTimeChange('end_date', 'end_time')

        function handleTimeChange(date, time) {
          const time_info = _this.form.time_info
          if (time_info[date] && time_info[time]) {
            const new_time = moment.tz(time_info[date] + ' ' + time_info[time], oldVal).tz(newVal)
            time_info[date] = new_time.format('YYYY-MM-DD')
            time_info[time] = new_time.format('HH:mm')
          }
        }
      },
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      return this.getTaskDetail()
        .then(() => {
          this.advisor_portal2.is_send = !this.isImportedFromOutsource
          // Consultant Mail, Client Mail 在二次 Complete 时默认不勾选，初次 Complete 时勾选
          if (this.task['general_status'] !== 'COMPLETED' && !this.task['complete_time']) {
            // is_cn / Outsource导入的专家 不发payment info
            this.advisor_portal1.is_send = !this.isImportedFromCNDB && !this.isImportedFromOutsource
            this.contact_portal.is_send = !!this.bound_contract && !this.isImportedFromOutsource
          }

          const advisor_consent_to_recording = this.task.advisor_record_consent?.is_consent
          this.asset_client_portal.is_send = this.isTwilioOrZoomTask && advisor_consent_to_recording && !this.sendToClientThroughCapvisionPro && !this.needClientComplianceReviewFirst
          // 如果complete过则取上次 completed的 client hour和 payment minutes/amount
          // 如果未complete过，则自己根据合同规则计算
          // 如果专家 is_one_hour_minimum:true,未completed=>amount= 按hours>=1 * 专家当前rate；amount输入框最小值当前专家的费率
          //                                 已经completed过=> 专家费率取上次的费率，amount输入框最小值上次的费率.提示费率变更，点击后以当前费率计算，amount最小值为当前费率
          /**
           * 上条注释中的is_one_hour_minimum已弃用，改为使用time_minimum判断，具体可见下方的checkTimeMinimumCharge方法
           */

          this.form.payment.rate = this.task.advisor.rate
          if (this.task.client_hours) {
            const is_payment_v2 = !!this.task.payment_topic_id
            const recent_payment = is_payment_v2
              ? this.task.payment_topic?.payments?.find(item => item.subtopic_name === 'DEFAULT')
              : _.orderBy(this.task.advisor_payments, 'id', 'desc')[0]
            const recent_payment_currency = recent_payment.currency_name || recent_payment.currency
            const network_loyalty_fee = this.task.payment_topic?.payments?.find(item => item.subtopic_name === 'NETWORK_LOYALTY_FEE')

            if (recent_payment &&
              (this.task.advisor.rate_currency !== recent_payment_currency ||
                this.task.advisor.rate !== recent_payment.rate)) {
              this.form.payment.rate = recent_payment.rate
              this.isRateChanged = true
            }

            if (is_payment_v2) {
              this.form.payment.minutes = recent_payment?.minutes
              this.form.payment.amount = recent_payment?.amount
              this.form.payment.currency = recent_payment?.currency_name
            } else {
              this.form.payment.minutes = this.task.advisor_normal_payment?.task_amount_hours.hours * 60
              this.form.payment.amount = this.task.advisor_normal_payment?.task_amount_hours.amount
              this.form.payment.currency = this.task.advisor_normal_payment?.currency
            }

            this.form.client_hours = this.task.client_hours
            this.form.should_network_loyalty_fee = !!network_loyalty_fee?.amount
            this.form.payment.network_loyalty_fee = network_loyalty_fee?.amount || 0
            this.advisor_min_amount = 0
            this.advisor_min_minutes = 0
            this.client_hour_min = 0

            if (this.hasTimeMinimumCharge) {
              const { minimum_minutes, minimum_hour } = this.timeMinimumFormat
              this.advisor_min_amount = +recent_payment.rate * minimum_hour
              this.advisor_min_minutes = minimum_minutes
              this.client_hour_min = minimum_hour
            }
          } else {
            this.calculateHour()
            this.form.client_hours = this.task.client_hours ?? undefined
          }
        })
        .finally(() => { this.loading = false })
    },
    getTaskDetail() {
      const params = {
        project_id: this.$route.params.project_id,
        id: this.$route.query.id,
        params: {
          extra: this.task_detail_extra,
        },
      }
      return API.getTaskDetail(params).then(data => {
        this.task = data
        // 处理显示访谈安排的时间
        const start = data.start_time
        const end = data.end_time
        this.form.time_info = {
          start_date: moment(start).tz(this.timeZone).format('YYYY-MM-DD'),
          start_time: moment(start).tz(this.timeZone).format('HH:mm'),
          end_date: moment(end).tz(this.timeZone).format('YYYY-MM-DD'),
          end_time: moment(end).tz(this.timeZone).format('HH:mm'),
        }
        // 获取task开始时间后 校验合同
        return this.getBoundContract()
          .then(() => {
            return data
          })
      }).then(data => {
        // 获取RM RA
        // this.form.lead_uid = data.lead_uid
        this.initSubType(data)

        // 处理 members_list 开始
        const hash = {}
        this.form.members_list = data.project.members.reduce((arr, next) => {
          if (!hash[next.uid]) {
            hash[next.uid] = true && arr.push(next)
          }
          return arr
        }, [])

        const user_exist = this.form.members_list.find(item => item.uid === this.userInfo.id)
        if (!user_exist) {
          const item = { uid: this.userInfo.id, user: { name: this.userInfo.name } }
          this.form.members_list.push(item)
        }

        if (this.sourceByWithinOneMonth) {
          const source_exist = this.form.members_list.find(item => item.uid === this.task.advisor.sourced_by_id)
          if (!source_exist) {
            const item = { uid: this.task.advisor.sourced_by_id, user: { name: this.task.advisor.sourced_by.name } }
            this.form.members_list.push(item)
          }
        }
        const contract = this.bound_contract || {}
        // 处理 members_list 结束

        if (data.general_status === 'COMPLETED') {
          // 获取RM RA
          this.form.lead_uid = data.lead_uid
          this.form.support_uid = data.support_uid
          this.form.sub_type = data.sub_type
          this.form.client_duration = data.client_duration
          this.form.billingNotes.has_charge_cash = Boolean(data.specified_charge_cash || data.specified_charge_cash === 0)
          this.form.compliance_approval_date = this.isKoraCapital && data.compliance_approval_date
            ? moment(data.compliance_approval_date).tz(this.timeZone).format('YYYY-MM-DD') : undefined
        } else {
          this.form.support_uid = this.sourceByWithinOneMonth ? this.task.advisor.sourced_by_id : undefined
          this.form.billingNotes.has_charge_cash = Boolean(contract)
        }

        /* 载入 Recommend 步骤勾选的加收项 */
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(this.form.billingNotes, key)) {
            this.form.billingNotes[key] = data[key]
          }
        }

        // 非BCG客户, 初次Complete时 如果不是中国地区的专家 且合同中设置了overseas 则自动勾上overseas
        if (!this.isBCG && data.general_status !== 'COMPLETED' && data.advisor.location_id_path &&
          !data.advisor.location_id_path.split(' ').includes('108')) {
          data.has_overseas = !!this.form.billingNotes.overseas
        }

        // 因 No Charge Reason 有Other选项，显示时需处理该数据
        const not_charged_reason = this.$refs['billing-notes-custom'].options.not_charged_reasons.find(
          reason => reason === data.not_charged_reason)

        Object.assign(this.form.billingNotes, {
          discount_hours: data.discount_hours ? data.discount_hours : 1,
          not_charged: data.not_charged,
          not_charged_reason: data.not_charged ? (not_charged_reason || 'Other') : null,
          not_charged_reason_input: data.not_charged ? (not_charged_reason || data.not_charged_reason) : null,
          senior: data.senior_rate ? data.senior_rate : 1.5,
          remarks: data.billing_remarks,
          has_discount: !!data.discount_hours,
          has_senior: data.senior_rate > 1,
          has_in_person: !!data.has_in_person,
          has_overseas: !!data.has_overseas,
          has_transcription: !!data.has_transcription,
          has_translation: !!data.has_translationn,
          specified_charge_cash: data.specified_charge_cash || data.specified_charge_cash === 0 ? data.specified_charge_cash : undefined,
          specified_charge_cash_currency: data.specified_charge_cash_currency || this.bound_contract?.currency,
        })

        // Complete时 如果是美国专家 校验 W9 状态值:
        // 1:未达标 => 当年累计达到 $600
        // 2:达标 未发送W9
        // 3:达标 已发送 专家未签
        // 4:达标 专家已签 无需再发送（一旦签过，后续无需再签）
        if (data.advisor.location_id_path && data.advisor.location_id_path.split(' ').includes('174')) {
          this.calculate_advisor_paid_this_year = data.advisor.paid_usd_in_this_year_jit
          if (data.advisor.w9_form) {
            switch (data.advisor.w9_form.status) {
              case 'INIT':
                this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
                this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
                break
              case 'REQUESTED':
                this.form.w9_status = 3
                break
              case 'SIGNED':
                this.form.w9_status = 4
            }
          } else {
            this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
            this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
          }
        }

        // 处理bank account
        this.form.payment.rate = data.advisor.rate
        this.form.payment.currency = data.advisor.rate_currency

        const advisor_bank_data = data.advisor.bank_account
        if (advisor_bank_data && advisor_bank_data.bank_account) {
          for (const key in this.form.payment) {
            if (advisor_bank_data.bank_account[key]) this.form.payment[key] = advisor_bank_data.bank_account[key]
          }
        }

        // 非初次complete时,去上次complete的值 初始化 false
        if (data.general_status === 'COMPLETED') {
          let disabled_pay_methods = data.advisor_normal_payment?.disabled_pay_methods
          if (data.payment_topic_id) {
            const recent_payment = data.payment_topic?.payments?.find(item => item.subtopic_name === 'DEFAULT')
            disabled_pay_methods = recent_payment?.items[recent_payment.items.length - 1]?.form?.disabled_pay_methods
          }

          if (!disabled_pay_methods?.includes('PHYSICAL_CHECK')) {
            this.form.enable_physical_check_payment_method = true
          }
        }
      })
    },
    initSubType(data) {
      // project_sub_type 是 'Consultation' 且 客户profile (勾选了require_task_subtype || BCG客户)
      const needTaskSubtype = this.task?.project_sub_type === 'Consultation' && (this.task.client.preference?.require_task_subtype || this.isBCG)
      const isSpecifiedSubtype = this.task?.client.preference?.specified_subtype_when_complete_task

      if (!this.isSpeciallyForInvestmentBankClient) {
        if (needTaskSubtype) {
          this.options.sub_type = (isSpecifiedSubtype || this.isBCG)
            ? [
              { label: 'Written Follow Up', value: 'WRITTEN_FOLLOW_UP' },
              { label: 'Phone', value: 'PHONE' },
              { label: 'Survey', value: 'SURVEY' },
              { label: 'BD Call', value: 'BD_CALL' },
            ]
            : [
              { label: 'Regular Phone Consultation', value: 'REGULAR_PHONE_CONSULTATION' },
              { label: 'Follow Up Phone Consultation', value: 'FOLLOW_UP_PHONE_CONSULTATION' },
              { label: 'Written Follow Up', value: 'WRITTEN_FOLLOW_UP' },
            ]
        }
        this.form.sub_type = data.sub_type
      } else {
        this.options.sub_type = [
          { label: 'Consultation', value: 'CONSULTATION' },
          { label: 'Investor Call', value: 'INVESTOR_CALL' },
        ]
        this.form.sub_type = data.project_sub_type.toUpperCase().replace(/\s+/g, '_')
      }

      if (this.isOutsourceSurveyProject) {
        this.options.sub_type.push({ label: 'Consultation-Survey', value: 'CONSULTATION_SURVEY' })
        this.form.sub_type = 'CONSULTATION_SURVEY'
      }
      if (this.isOutsourceConferenceProject) {
        this.options.sub_type.push({ label: 'Consultation-Conference', value: 'CONSULTATION_CONFERENCE' })
        this.form.sub_type = 'CONSULTATION_CONFERENCE'
      }
    },
    getBoundContract(val = true) {
      if (!val) return

      this.contract_loading = true
      const params = { if_use_time: this.if_use_time }
      return API.getTaskBoundContract(this.$route.query.id, params).then(data => {
        this.bound_contract = data

        // 处理加收规则的数据和显示
        if (data) {
          Object.assign(this.form.billingNotes, {
            in_person: data.in_person_term?.value,
            overseas: data.overseas_term?.extra,
            transcription: data.transcription_term?.extra,
            translation: data.translation_term?.extra,
            has_charge_cash: true,
            specified_charge_cash: undefined,
            specified_charge_cash_currency: data.currency,
          })

          this.viewData = {
            client_hours: this.ruleAccessibility(data, 'client_hours_term'),
            china_senior: this.ruleAccessibility(data, 'china_senior_term'),
            non_china_senior: this.ruleAccessibility(data, 'non_china_senior_term'),
          }
        } else {
          this.viewData = null
        }
      }).finally(() => {
        this.contract_loading = false
      })
    },
    ruleAccessibility(data, type) {
      const input = data[type]
      const viewData = {
        rules: [],
        addition: '',
      }
      const unit_text = type === 'client_hours_term' ? '' : 'x'

      viewData.rules = input.intervals.map((item, index) => {
        return [`(${index ? input.intervals[index - 1].first : 0}, ${item.first}]`, `${item.second}${unit_text}`]
      })

      const Yuan = type === 'china_senior_term' ? 'RMB' : ''
      const Dollar = type === 'non_china_senior_term' ? '$' : ''
      const text_1 = Dollar + input.step + Yuan
      const text_2 = Dollar + input.intervals[input.intervals.length - 1].first + Yuan

      viewData.addition = [
        'for every ',
        text_1,
        ' above ',
        text_2,
        ', additional ',
        input.inc,
        unit_text,
        ' charge'].join('')

      return viewData
    },

    /* 根据time_minimum字段，专家访谈不到1小时或30分钟，按1小时或30分钟算*/
    checkTimeMinimumCharge(hours) {
      const { minimum_minutes, minimum_hour } = this.timeMinimumFormat

      this.advisor_min_amount = +this.task.advisor.rate * minimum_hour
      this.advisor_min_minutes = minimum_minutes
      this.client_hour_min = minimum_hour

      const start_time = this.form.time_info.start_time
      const end_time = this.form.time_info.end_time
      const minutes = moment(end_time, 'HH:mm').diff(moment(start_time, 'HH:mm'), 'minutes')

      if (minutes <= minimum_minutes && minutes > 0) {
        this.form.client_hours = minimum_hour
        this.form.payment.minutes = minimum_minutes
      } else {
        this.form.client_hours = hours
        this.form.payment.minutes = minutes
      }
    },

    calculateHour() {
      const start_time = this.form.time_info.start_time
      const end_time = this.form.time_info.end_time
      if (start_time && end_time) {
        const minutes = moment(end_time, 'HH:mm').diff(moment(start_time, 'HH:mm'), 'minutes')
        let hours

        if (this.bound_contract) {
          const term = this.bound_contract.client_hours_term
          const interval = term.intervals.find(item => minutes <= item.first)
          if (interval) {
            hours = interval.second
          } else {
            const last_interval = term.intervals[term.intervals.length - 1]
            hours = last_interval.second + (parseInt((minutes - last_interval.first) / term.step) +
              ((minutes - last_interval.first) % term.step ? 1 : 0)) * term.inc
          }
        } else {
          hours = (parseInt(minutes / 30) + (minutes % 30 ? 1 : 0)) * 0.5
        }

        if (this.hasTimeMinimumCharge) {
          this.checkTimeMinimumCharge(hours)
        } else {
          // this.form.client_hours = hours
          this.form.payment.minutes = minutes
          this.advisor_min_amount = 0
          this.advisor_min_minutes = 0
          this.client_hour_min = 0
        }
        this.calculatePaymentAmount()
        this.calculateEndTime()
      }
    },
    calculateEndTime(val = this.form.client_duration) {
      this.form.time_info.end_time = moment(this.form.time_info.start_time, 'HH:mm').add(val, 'm').format('HH:mm')
      this.$refs['ruleForm'].validateField('client_duration')
    },
    calculatePaymentAmountByNewRate() {
      this.form.payment.rate = this.task.advisor.rate
      const { minimum_hour } = this.timeMinimumFormat
      this.advisor_min_amount = this.hasTimeMinimumCharge ? this.task.advisor.rate * minimum_hour : 0
      this.isRateChanged = false
      this.calculatePaymentAmount()
    },
    calculatePaymentAmount() {
      this.form.payment.amount = (this.form.payment.minutes / 60 * this.form.payment.rate).toFixed(2)
      this.form.payment.currency = this.task.advisor.rate_currency
      this.checkW9Status()
    },

    checkW9Status() {
      if (this.$refs['consultant-mail1']) this.$refs['consultant-mail1'].handleTemplateChange()
      if (this.$refs['consultant-mail2']) this.$refs['consultant-mail2'].handleTemplateChange()
      // 计算本次付款后是否达到 $600
      if (this.isUSAdvisor) {
        const sum = this.form.payment.amount + this.task.advisor.paid_usd_in_this_year_jit
        this.calculate_advisor_paid_this_year = sum
        if ([3, 4].includes(this.form.w9_status)) return

        if (sum > 600) {
          this.form.w9_status = 2
          this.form.should_send_w9_form = true
        } else {
          this.form.w9_status = 1
          this.form.should_send_w9_form = false
        }
      }
      if (this.form.payment.currency !== 'USD') {
        this.form.w9_status = 0
        this.form.should_send_w9_form = false
      }
    },

    handleStartDateChange(val) {
      this.form.time_info.end_date = val
      this.handleStartTimeChange(val)
    },
    handleStartTimeChange(val) {
      this.getBoundContract(val)
      this.calculateHour()
    },
    actionGotoDetail() {
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.push({
            name: 'ProjectDetail',
          })
        })
    },
    submit() {
      if (this.advisor_portal1.is_send && !this.advisor_portal1.email?.to_list?.length > 0) {
        this.$message({
          message: 'The mail (Payment Confirmation Email) recipient cannot be empty.',
          type: 'error',
        })
        return
      }
      if (this.advisor_portal2.is_send && !this.advisor_portal2.email?.to_list?.length > 0) {
        this.$message({
          message: 'The mail (Post Call CA) recipient cannot be empty.',
          type: 'error',
        })
        return
      }
      if (this.asset_client_portal.is_send && !this.asset_client_portal.email?.to_list?.length > 0) {
        this.$message({
          message: 'The mail (Twilio Recording/Transcript Email) recipient cannot be empty.',
          type: 'error',
        })
        return
      }

      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submit_loading = true
          const rate = this.task.payment_topic_id
            ? this.task.payment_topic?.payments?.find(item => item.subtopic_name === 'DEFAULT')?.rate
            : this.task.advisor_normal_payment?.rate
          const compliance_approval_date = this.form.compliance_approval_date
            ? moment.tz(this.form.compliance_approval_date, this.timeZone).toISOString() : undefined

          const data = {
            should_send_w9_form: this.form.should_send_w9_form,
            payment: {
              hours: (this.form.payment.minutes / 60).toFixed(4),
              minutes: this.form.payment.minutes,
              amount: this.form.payment.amount,
              currency: this.form.payment.currency,
              disabled_pay_methods: this.form.enable_physical_check_payment_method ? [] : ['PHYSICAL_CHECK'],
              rate: this.isRateChanged ? rate : this.form.payment.rate,
              network_loyalty_fee: this.form.should_network_loyalty_fee ? this.form.payment.network_loyalty_fee : undefined,
              use_legacy_payment_api: false,
            },
            advisor_portal1: this.advisor_portal1.is_send ? {
              portal: {
                type: 'ADVISOR_POST_CALL',
              },
              email: this.advisor_portal1.email,
              email_template_tags: this.advisor_portal1.template?.tags,
            } : undefined,

            advisor_portal2: this.advisor_portal2.is_send ? {
              portal: {
                type: 'ADVISOR_POST_CALL',
                task_ca: this.advisor_portal2.task_ca.inquiry ? {
                  inquiry_id: this.advisor_portal2.task_ca.inquiry.id,
                  inquiry_branch_id: this.advisor_portal2.task_ca.inquiry_branch.id,
                } : undefined,
              },
              email: this.advisor_portal2.email,
              email_template_tags: this.advisor_portal2.template.tags,
            } : undefined,
            // contact_portal: this.contact_portal.is_send ? {
            //   portal: {
            //     type: 'CONTACT_POST_CALL',
            //     project_id: this.$route.params.project_id,
            //     task_id_set: [this.$route.query.id],
            //   },
            //   email: this.contact_portal.email,
            //   email_template_tags: this.contact_portal.template.tags,
            // } : undefined,
            twilio_asset_email: this.asset_client_portal.is_send ? this.asset_client_portal.email : undefined,
            task: {
              lead_uid: this.form.lead_uid,
              support_uid: this.form.support_uid,
              compliance_approval_date,
            },
          }

          if (!this.hasBilled) {
            data.task = {
              start_time: moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
                this.timeZone)
                .toISOString(),
              end_time: moment.tz(this.form.time_info.end_date + ' ' + this.form.time_info.end_time, this.timeZone)
                .toISOString(),
              sub_type: this.form.sub_type,
              client_duration: this.form.client_duration,
              lead_uid: this.form.lead_uid,
              support_uid: this.form.support_uid,
              compliance_approval_date,
              client_hours: this.form.client_hours,
              discount_hours: this.form.billingNotes.has_discount ? this.form.billingNotes.discount_hours : 0,
              not_charged: this.form.billingNotes.not_charged,
              not_charged_reason: this.form.billingNotes.not_charged
                ? (this.form.billingNotes.not_charged_reason === 'Other'
                  ? this.form.billingNotes.not_charged_reason_input
                  : this.form.billingNotes.not_charged_reason) : null,
              senior_rate: this.form.billingNotes.has_senior ? this.form.billingNotes.senior : 1,
              has_overseas: this.form.billingNotes.has_overseas,
              has_transcription: this.form.billingNotes.has_transcription,
              has_translation: this.form.billingNotes.has_translation,
              has_in_person: this.form.billingNotes.has_in_person,
              specified_charge_cash: this.form.billingNotes.has_charge_cash
                ? this.form.billingNotes.specified_charge_cash
                : null,
              specified_charge_cash_currency: this.form.billingNotes.has_charge_cash
                ? this.form.billingNotes.specified_charge_cash_currency
                : null,
              billing_remarks: this.form.billingNotes.remarks,
              billing_notes: this.viewData ? this.form.billing_notes_str : undefined,
              billing_hours: this.viewData ? this.form.billing_hours : undefined,
            }
          }

          return API.completeTask(this.$route.query.id, data).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Complete Task successfully!',
              type: 'success',
            })
            this.actionGotoDetail()
          })
            .finally(() => {
              this.submit_loading = false
            })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.box-card {
  margin: 8px 0;
}

.time-picker {
  width: 165px;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}

::v-deep .tooltip-icon {
  font-size: 1.25em;
  margin-left: 0.5em;
  vertical-align: text-top;
}

::v-deep .client-hour-input {
  width: 150px;

  input {
    text-align: center;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 14px;
  }
}

::v-deep .el-collapse-item__header .el-checkbox__label {
  font-size: 15px;
}

::v-deep .el-collapse {
  border: 1px solid #EBEEF5;
  padding: 0 16px;
}

::v-deep .mce-tinymce {
  width: auto !important;
}
</style>
