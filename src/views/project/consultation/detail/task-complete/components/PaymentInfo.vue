<template>
  <div>
    <el-button type="primary" @click="dialogVisible = true">Edit</el-button>

    <el-dialog
      title="Update"
      :visible.sync="dialogVisible"
      width="700px"
      @open="formatInfo">
      <el-form ref="bankInfoForm" v-loading="loading" :model="data" :rules="rules" label-width="160px">
        <div v-if="paymentType==='DIRECT_DEPOSIT'">
          <el-form-item label="Bank Country" prop="bank_area">
            <el-select v-model="data.bank_area" filterable>
              <el-option
                v-for="item in options.country_list"
                :key="item.value"
                :value="item.value"
                :label="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="Payee Type">
            <el-select v-model="data.receiver_type">
              <el-option
                v-for="item in options.payment_receiver_type_list"
                :key="item.id"
                :value="item.id"
                :label="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="Account Holder Name" prop="account_name">
            <el-input v-model="data.account_name" />
          </el-form-item>
          <el-form-item label="Bank Name" prop="bank_name">
            <el-input v-model="data.bank_name" />
          </el-form-item>
          <el-form-item v-if="display.bank_location" label="Bank Location" prop="bank_location">
            <el-cascader
              v-model="data.bank_location"
              :options="options.location_list"
              filterable
              :props="options.cascader_props" />
          </el-form-item>
          <el-form-item v-if="display.bank_branch" label="Bank Branch">
            <el-input v-model="data.bank_branch" />
          </el-form-item>
          <el-form-item
            v-if="display.account_number"
            key="account_number"
            :label="displayNameOfAccountNumber"
            prop="account_number">
            <el-input v-model="data.account_number" maxlength="50" :placeholder="placeholderOfAccountNumber" />
          </el-form-item>
          <el-form-item v-if="display.routing_number" key="routing_number" label="Routing Number" prop="routing_number">
            <el-input v-model="data.routing_number" placeholder="9 numbers" />
          </el-form-item>
          <el-form-item v-if="display.account_type" key="account_type" label="Account Type" prop="account_type">
            <el-select v-model="data.account_type">
              <el-option
                v-for="item in options.account_type"
                :key="item.id"
                :value="item.id"
                :label="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="display.transit_number" key="transit_number" label="Transit Number" prop="transit_number">
            <el-input v-model="data.transit_number" placeholder="8 or 9 digits" />
          </el-form-item>
          <el-form-item v-if="display.swift_code" key="swift_code" label="SWIFT Code" prop="swift_code">
            <el-input v-model="data.swift_code" placeholder="8 or 11 characters" />
          </el-form-item>
          <el-form-item v-if="display.sort_code" key="sort_code" label="Sort Code" prop="sort_code">
            <el-input v-model="data.sort_code" placeholder="6 numbers" />
          </el-form-item>
          <el-form-item v-if="display.cpf_code" key="cpf_code" label="CPF Code" prop="cpf_code">
            <el-input v-model="data.cpf_code" placeholder="11 numbers" />
          </el-form-item>
          <el-form-item v-if="display.ifsc_code" key="ifsc_code" label="IFSC Code" prop="ifsc_code">
            <el-input v-model="data.ifsc_code" placeholder="11 characters (letters and numbers)" />
          </el-form-item>
          <el-form-item v-if="display.cmnd_code" key="cmnd_code" label="CMND Code" prop="cmnd_code">
            <el-input v-model="data.cmnd_code" />
          </el-form-item>
          <el-form-item v-if="display.tax_id_code" key="tax_id_code" :label="displayNameOfTaxIDCode" prop="tax_id_code">
            <el-input v-model="data.tax_id_code" :placeholder="placeholderOfTaxIDCode" />
          </el-form-item>
          <el-form-item v-if="display.birthday" key="birthday" :label="displayNameOfTaxIDCode" prop="tax_id_code">
            <el-date-picker
              v-model="data.tax_id_code"
              type="date"
              format="MM/dd/yyyy"
              value-format="dd/MM/yyyy"
              :picker-options="options.birthdayPickerOptions" />
          </el-form-item>
        </div>
        <div v-if="paymentType==='AMAZON_GIFT_CARD'">
          <el-form-item label="Email Address" prop="email_address">
            <el-input v-model="data.email_address" />
          </el-form-item>
        </div>
        <div v-if="paymentType==='PHYSICAL_CHECK'">
          <el-form-item label="Check Payable To" prop="check_payable_to">
            <el-input v-model="data.check_payable_to" />
          </el-form-item>
          <el-form-item label="Address 1" prop="address1">
            <el-input v-model="data.address1" />
          </el-form-item>
          <el-form-item label="Address 2" prop="address2">
            <el-input v-model="data.address2" />
          </el-form-item>
          <el-form-item label="City" prop="city">
            <el-input v-model="data.city" />
          </el-form-item>
          <el-form-item label="State/Province/Territory" prop="region">
            <el-select v-model="data.region" filterable>
              <el-option
                v-for="item in options.us_region_list"
                :key="item.id"
                :value="item.name"
                :label="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="Zip" prop="zip">
            <el-input v-model="data.zip" />
          </el-form-item>
          <el-form-item label="Country" prop="country">
            <el-input v-model="data.country" disabled />
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submit_loading" @click="updatePaymentInfo">Submit</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/consultant'
import { mapState } from 'vuex'

const LOCATION_ARGENTINA = 'ARGENTINA'
const LOCATION_AUSTRALIA = 'AUSTRALIA'
const LOCATION_BRAZIL = 'BRAZIL'
const LOCATION_CANADA = 'CANADA'
const LOCATION_CHINA = 'CHINA'
const LOCATION_INDIA = 'INDIA'
const LOCATION_MEXICO = 'MEXICO'
const LOCATION_PERU = 'PERU'
const LOCATION_RUSSIA = 'RUSSIA'
const LOCATION_SINGAPORE = 'SINGAPORE'
// KOREA,DPR =>NORTH KOREA
const LOCATION_SOUTH_KOREA = 'KOREA'
const LOCATION_UK = 'UK'
const LOCATION_US = 'US'
const LOCATION_VIETNAM = 'VIETNAM'
const LOCATION_CHILE = 'CHILE'
const LOCATION_IRELAND = 'IRELAND'
const LOCATION_REST_OF_WORLD = 'REST_OF_WORLD'

export default {
  name: 'ConsultantPayment',
  props: {
    task: Object,
    paymentType: String,
    paymentInfo: Object,
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      })
    }
    const validNumberLetters = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9 ]+$/
      setTimeout(() => {
        if (!value.trim()) {
          callback(new Error('Check Payable To is required.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Check Payable To can only be numbers or letters.'))
        } else {
          callback()
        }
      })
    }
    return {
      loading: false,
      submit_loading: false,
      dialogVisible: false,
      options: {
        identifier_type: [
          {
            id: 1,
            name: 'SWIFT BIC (International)',
          }, {
            id: 2,
            name: 'CHIPS ABA (International)',
          }, {
            id: 3,
            name: 'ROUTING NUMBER (US Accounts Only)',
          }, {
            id: 4,
            name: 'SORT CODE (International)',
          },
        ],
        payment_receiver_type_list: [
          { id: 'ONESELF', name: 'Consultant (Self)' }, // 专家本人
          { id: 'ANOTHER_PERSON', name: 'Others (Third Party)' }, // 他人
          { id: 'CORPORATION', name: 'Company (Public Accounts)' }, // 机构, 公司
        ],
        country_list: [
          { id: LOCATION_ARGENTINA, name: 'Argentina' },
          { id: LOCATION_AUSTRALIA, name: 'Australia' },
          { id: LOCATION_BRAZIL, name: 'Brazil' },
          { id: LOCATION_CANADA, name: 'Canada' },
          { id: LOCATION_CHINA, name: 'China' },
          { id: LOCATION_INDIA, name: 'India' },
          { id: LOCATION_MEXICO, name: 'Mexico' },
          { id: LOCATION_PERU, name: 'Peru' },
          { id: LOCATION_RUSSIA, name: 'Russia' },
          { id: LOCATION_SINGAPORE, name: 'Singapore' },
          { id: LOCATION_SOUTH_KOREA, name: 'South Korea' },
          { id: LOCATION_UK, name: 'UK' },
          { id: LOCATION_US, name: 'United States' },
          { id: LOCATION_VIETNAM, name: 'Vietnam' },
          { id: LOCATION_REST_OF_WORLD, name: 'Rest of world' },
        ],
        location_list: [],
        us_region_list: [],
        cascader_props: {
          value: 'cn_name',
          label: 'cn_name',
        },
        account_type: [
          { id: 'SAVING', name: 'SAVING' },
          { id: 'CHECKING', name: 'CHECKING' },
        ],
        birthdayPickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now()
          },
        },
      },
      data: {
        receiver_type: 'ONESELF',
        account_name: undefined,
        bank_name: undefined,
        bank_area: LOCATION_US,
        /* --- dynamic --- */
        bank_branch: undefined,
        bank_location: undefined,
        account_number: undefined,
        account_type: 'SAVING',
        routing_number: undefined,
        transit_number: undefined,
        swift_code: undefined,
        sort_code: undefined,
        cpf_code: undefined,
        ifsc_code: undefined,
        cmnd_code: undefined,
        tax_id_code_type: undefined,
        tax_id_code: undefined,
        /* --- PHYSICAL_CHECK --- */
        check_payable_to: undefined,
        address1: undefined,
        address2: undefined,
        city: undefined,
        region: undefined,
        zip: undefined,
        country: undefined,
        /* --- AMAZON_GIFT_CARD --- */
        email_address: undefined,
      },
      display: {
        bank_branch: false,
        bank_location: false,
        account_number: false,
        account_type: false,
        routing_number: false,
        transit_number: false,
        swift_code: false,
        sort_code: false,
        cpf_code: false,
        ifsc_code: false,
        cmnd_code: false,
        tax_id_code: false,
        birthday: false,
        payee_mobile: false,
      },
      rules: {
        'account_name': [{ required: true, trigger: 'change' }],
        'bank_name': [{ required: true, trigger: 'change' }],
        'bank_area': [{ required: true, trigger: 'change' }],
        /* --- dynamic --- */
        'bank_location': [{ required: true, trigger: 'change' }],
        'account_number': [
          {
            validator: (rule, value, callback) => {
              if (!value.trim()) {
                callback(new Error(this.displayNameOfAccountNumber + ' is required.'))
              } else {
                const regex_digits = new RegExp('^\\d+$')
                switch (this.data.bank_area) {
                  case LOCATION_US:
                    callback()
                    break
                  case LOCATION_SINGAPORE:
                    if (!regex_digits.test(value)) {
                      callback(new Error('Account Number must be digits.'))
                    } else {
                      callback()
                    }
                    break
                  case LOCATION_MEXICO:
                    if (!regex_digits.test(value) || value.trim().length !== 18) {
                      callback(new Error('CLABE must be numeric and exactly contain 18 digits.'))
                    } else {
                      callback()
                    }
                    break
                  case LOCATION_ARGENTINA:
                    if (!regex_digits.test(value) || value.trim().length !== 22) {
                      callback(new Error('CBU must be numeric and exactly contain 22 digits.'))
                    } else {
                      callback()
                    }
                    break
                  case LOCATION_PERU:
                    if (!regex_digits.test(value) || value.trim().length !== 20) {
                      callback(new Error('CCI must be numeric and exactly contain 20 digits.'))
                    } else {
                      callback()
                    }
                    break
                  default:
                    if (value.trim().length > 34) {
                      callback(new Error('IBAN is up to 34 characters.'))
                    } else {
                      callback()
                    }
                    break
                }
              }
            }, trigger: 'change',
          }, { required: true, trigger: 'change' }],
        'routing_number': [{ required: true, trigger: 'change' }, { len: 9, trigger: 'blur' }],
        'transit_number': [
          {
            validator: (rule, value, callback) => {
              if (!/^[\d]+$/.test(value)) {
                callback(new Error('Transit Number must be digits.'))
              } else if (![8, 9].includes(value.length)) {
                callback(new Error('Transit Number is 8 or 9 digits'))
              } else {
                callback()
              }
            }, trigger: 'change',
          },
          { required: true, trigger: 'change' },
        ],
        'swift_code': [
          { required: true, trigger: 'change' }, {
            validator: (rule, value, callback) => {
              if (!value.trim()) {
                callback(new Error('SWIFT Code is required.'))
              } else {
                if (![8, 11].includes(value.trim().length)) {
                  callback(new Error('SWIFT Code must be 8 or 11 characters'))
                } else {
                  callback()
                }
              }
            },
          }],
        'sort_code': [{ required: true, trigger: 'change' }, { len: 6, trigger: 'blur' }],
        'cpf_code': [{ required: true, trigger: 'change' }, { len: 11, trigger: 'blur' }],
        'ifsc_code': [{ required: true, trigger: 'change' }, { len: 11, trigger: 'blur' }],
        'cmnd_code': [{ required: true, trigger: 'change' }],
        'tax_id_code': [
          {
            validator: (rule, value, callback) => {
              if (!value.trim()) {
                callback(new Error(this.displayNameOfTaxIDCode + ' is required.'))
              } else {
                if (this.data.bank_area === LOCATION_ARGENTINA) {
                  if (!/^\d{11}$/.test(value)) {
                    callback(new Error('Identificación Nacional del Beneficiario is 11 digits.'))
                  } else {
                    callback()
                  }
                }
                if (this.data.bank_area === LOCATION_AUSTRALIA) {
                  if (!/^\d{6}$/.test(value)) {
                    callback(new Error('BSB Code is 6 digits.'))
                  } else {
                    callback()
                  }
                }
                if (this.data.bank_area === LOCATION_PERU) {
                  if (!/^\d{8}$/.test(value)) {
                    callback(new Error('DNI Number is 8 digits.'))
                  } else {
                    callback()
                  }
                }
                if (this.data.bank_area === LOCATION_CHILE) {
                  if (!/^(\d{8}|\d{9})$/.test(value)) {
                    callback(new Error('RUT Number is 8 or 9 digits.'))
                  } else {
                    callback()
                  }
                }
                if (this.data.bank_area === LOCATION_SOUTH_KOREA) {
                  if (!/^\d{13}$/.test(value)) {
                    callback(new Error('RRN is 13 digits.'))
                  } else {
                    callback()
                  }
                }
                if (this.data.bank_area === LOCATION_RUSSIA) {
                  const regex = new RegExp('^\\d{10,12}$')

                  if (!regex.test(String(this.data.tax_id_code).trim())) {
                    callback(new Error('INN Number is ranges from 10 to 12 numbers.'))
                  } else {
                    callback()
                  }
                }
              }
            }, trigger: 'change',
          }, { required: true, trigger: 'change' }],
        'check_payable_to': [{ required: true, validator: validNumberLetters, trigger: 'change' }],
        'address1': [{ required: true, message: 'Address is required', trigger: 'change' }],
        'city': [{ required: true, message: 'City is required', trigger: 'change' }],
        'region': [{ required: true, message: 'Region is required', trigger: 'change' }],
        'zip': [{ required: true, message: 'Zip is required', trigger: 'change' }],
        'country': [{ required: true, message: 'Country is required', trigger: 'change' }],
        'email_address': [{ required: true, validator: validateEmail, trigger: 'change' }],
      },
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    // Receiver Type 为 "Others" 时，需要填写手机号
    isPayeeMobileDisplay() {
      return this.data.receiver_type === 'ANOTHER_PERSON'
    },

    isDirectDeposit() {
      return this.paymentType === 'DIRECT_DEPOSIT'
    },
    /*
     * Account Number for US & SINGAPORE
     * IBAN for Europe
     * Account Number / IBAN for others
     */
    displayNameOfAccountNumber() {
      let output

      switch (this.data.bank_area) {
        case LOCATION_US:
        case LOCATION_SINGAPORE:
        case LOCATION_CHINA:
          output = 'Account Number'
          break
        case LOCATION_UK:
        case LOCATION_REST_OF_WORLD:
          output = 'IBAN'
          break
        case LOCATION_MEXICO:
          output = 'CLABE'
          break
        case LOCATION_ARGENTINA:
          output = 'CBU'
          break
        case LOCATION_PERU:
          output = 'CCI'
          break
        default:
          output = 'Account Number / IBAN'
      }

      return output
    },
    placeholderOfAccountNumber() {
      let output

      switch (this.data.bank_area) {
        case LOCATION_US:
          output = '10-12 digits'
          break
        case LOCATION_RUSSIA:
          output = '10 to 12 numbers'
          break
        case LOCATION_SINGAPORE:
          output = 'digits only'
          break
        case LOCATION_MEXICO:
          output = '18 numbers'
          break
        case LOCATION_ARGENTINA:
          output = '22 numbers'
          break
        case LOCATION_PERU:
          output = '20 numbers'
          break
        default:
          output = ''
      }

      return output
    },
    displayNameOfTaxIDCode() {
      let output = 'Tax ID'

      switch (this.data.bank_area) {
        case LOCATION_AUSTRALIA:
          output = 'BSB Code'
          break
        case LOCATION_RUSSIA:
          output = 'INN Number'
          break
        case LOCATION_VIETNAM:
          output = 'Date Of Birth'
          break
        case LOCATION_ARGENTINA:
          output = 'Identificación Nacional del Beneficiario'
          break
        case LOCATION_PERU:
          output = 'DNI Number'
          break
        case LOCATION_SOUTH_KOREA:
          output = 'RRN'
          break
        case LOCATION_CHILE:
          output = 'RUT'
          break
      }

      return output
    },
    placeholderOfTaxIDCode() {
      let output

      switch (this.data.bank_area) {
        case LOCATION_AUSTRALIA:
          output = '6 digits'
          break
        case LOCATION_RUSSIA:
          output = '10-12 digits'
          break
        case LOCATION_ARGENTINA:
          output = '11 digits'
          break
        case LOCATION_PERU:
          output = '8 digits'
          break
        case LOCATION_SOUTH_KOREA:
          output = '13 digits'
          break
      }

      return output
    },
  },
  watch: {
    /**
     * 依据 Bank 所属地区调整填写项目
     */
    'data.bank_area': {
      handler(bank_area) {
        const data = {
          bank_branch: false,
          bank_location: false,
          account_number: false,
          account_type: false,
          routing_number: false,
          transit_number: false,
          swift_code: false,
          sort_code: false,
          cpf_code: false,
          ifsc_code: false,
          cmnd_code: false,
          tax_id_code: false,
          birthday: false,
        }

        switch (bank_area) {
          case LOCATION_AUSTRALIA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_BRAZIL:
            data.account_number = true
            data.swift_code = true
            data.cpf_code = true
            break
          case LOCATION_CANADA:
            data.account_number = true
            data.transit_number = true
            data.swift_code = true
            break
          case LOCATION_CHINA:
            data.account_number = true
            data.bank_branch = true
            data.bank_location = true
            data.swift_code = true
            break
          case LOCATION_INDIA:
            data.account_number = true
            data.swift_code = true
            data.ifsc_code = true
            break
          case LOCATION_RUSSIA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_UK:
            data.account_number = true
            data.swift_code = true
            data.sort_code = true
            break
          case LOCATION_IRELAND:
            data.account_number = true
            data.swift_code = true
            data.sort_code = true
            break
          case LOCATION_US:
            data.account_number = true
            data.account_type = true
            data.swift_code = this.isSGDB
            data.routing_number = true
            break
          case LOCATION_VIETNAM:
            data.account_number = true
            data.swift_code = true
            data.cmnd_code = true
            data.birthday = true
            break
          case LOCATION_ARGENTINA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_PERU:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_SOUTH_KOREA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_CHILE:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          default:
            data.account_number = true
            data.swift_code = true
            break
        }

        Object.assign(this.display, data)

        /**
         * tax_id_code_type 值根据所选地区设置
         */
        let tax_id_code_type

        switch (bank_area) {
          case LOCATION_AUSTRALIA:
            tax_id_code_type = '2' // BSB Code - '6 digits'
            break
          case LOCATION_RUSSIA:
            tax_id_code_type = '1' // INN Number - '10-12 digits'
            break
          case LOCATION_VIETNAM:
            tax_id_code_type = '3' // Date Of Birth
            break
          case LOCATION_ARGENTINA:
            tax_id_code_type = '4' // Identificación Nacional del Beneficiario
            break
          case LOCATION_PERU:
            tax_id_code_type = '5' // DNI Number
            break
          case LOCATION_SOUTH_KOREA:
            tax_id_code_type = '6' // RRN
            break
          case LOCATION_CHILE:
            tax_id_code_type = '7' // RUT
            break
          default:
            tax_id_code_type = undefined
            break
        }

        this.data.tax_id_code_type = tax_id_code_type

        /**
         * account_type 除 US 外都为 null
         */
        this.data.account_type = data.account_type ? 'SAVING' : null
      },
      immediate: true,
    },
    'data.receiver_type': {
      handler() {
        this.display.payee_mobile = this.isPayeeMobileDisplay
      },
      immediate: true,
    },
  },
  mounted() {
    this.getLocationList()
  },
  methods: {
    getLocationList() {
      this.$store
        .dispatch('options/getAllCountryList')
        .then(data => {
          this.options.country_list = this.formatCountry(data)
          // 319 Taiwan
          this.options.location_list = data.filter(item => (item.parent_id === 108 && item.id !== 319)).map(item => {
            // 直辖市不添加子地区
            if (![225, 228, 313, 324].includes(item.id)) {
              item.children = data.filter(location => location.parent_id === item.id)
            }
            return item
          })
          // format for US State/Province/Territory
          const other_region = [
            { name: 'District of Columbia', id: 'District of Columbia' },
            { name: 'Puerto Rico', id: 'Puerto Rico' },
          ]
          this.options.us_region_list = data.filter(item => (item.parent_id === 108 && item.id !== 319)).concat(other_region)
        })
    },

    formatCountry(list) {
      /*
      *  78 澳门
      *  82 香港
       */
      const country_list = list.filter(item => item.level === 2)
      const special_administrative_region_of_china = list.filter(item => [78, 82].includes(item.id))
      const _list = country_list.concat(special_administrative_region_of_china)
      return _list.map(item => {
        if (item.id === 155) {
          return { name: 'UK', value: 'UK' }
        } else if (item.id === 174) {
          return { name: 'United States', value: 'US' }
        } else if (item.id === 78) {
          return { name: 'Hong Kong (China)', value: 'Hong Kong' }
        } else if (item.id === 107) {
          return { name: 'Taiwan (China)', value: 'Taiwan' }
        } else if (item.id === 82) {
          return { name: 'Macau (China)', value: 'Macau' }
        } else {
          const format_value = item.name.trim().toUpperCase().replace(/\s+/g, '_')
          return { name: item.name, value: format_value }
        }
      })
    },

    formatInfo() {
      // const info = this.isDirectDeposit ? this.paymentInfo.bank_account?.account2_no_mosaic_jit : this.paymentInfo.payment_detail
      // this.data = Object.assign(this.data, info)

      // this.loading = true
      // return API.getBankAccount(this.task.advisor_id).then(data => {
      //   const bank_account = data ? data.account : ''
      //   if (data && bank_account) {
      //     bank_account.bank_location = bank_account.bank_location ? bank_account.bank_location.split('-') : []
      //   }
      //   Object.assign(this.data, bank_account)
      // }).finally(() => {
      //   this.loading = false
      // })
    },
    updatePaymentInfo() {
      this.$refs['bankInfoForm'].validate((valid) => {
        if (valid) {
          this.submit_loading = true
          const params = {}
          switch (this.paymentType) {
            case 'AMAZON_GIFT_CARD':
              params.email_address = this.data.email_address
              break
            case 'PHYSICAL_CHECK':
              params.check_payable_to = this.data.check_payable_to
              params.address1 = this.data.address1
              params.address2 = this.data.address2
              params.city = this.data.city
              params.region = this.data.region
              params.zip = this.data.zip
              params.country = this.data.country
              break
            case 'DIRECT_DEPOSIT':
              params.receiver_type = this.data.receiver_type
              params.bank_name = this.data.bank_name
              params.bank_area = this.data.bank_area === 'UK_ENGLAND' ? 'UK' : this.data.bank_area
              params.bank_branch = this.data.bank_branch
              params.bank_location = this.data.bank_location ? this.data.bank_location.join('-') : ''
              params.account_name = this.data.account_name
              params.account_number = this.data.account_number
              params.account_type = this.data.account_type
              params.routing_number = this.data.routing_number ? this.data.routing_number.replace(/\s+/g, '') : ''
              params.transit_number = this.data.transit_number
              params.swift_code = this.data.swift_code
              params.sort_code = this.data.sort_code
              params.cpf_code = this.data.cpf_code
              params.ifsc_code = this.data.ifsc_code
              params.cmnd_code = this.data.cmnd_code
              params.tax_id_code_type = this.data.tax_id_code_type
              params.tax_id_code = this.data.tax_id_code

              if (params.bank_area !== LOCATION_US) {
                params.account_type = null
              }

              if (params.tax_id_code && params.tax_id_code_type === '3') {
                const [year, month, day] = params.tax_id_code.split('-')
                params.tax_id_code = `${day}/${month}/${year}`
              }
              break
          }
          const paramsData = {
            'project_sub_type': this.paymentInfo.project_sub_type,
            'pay_method_name': this.paymentType,
            bank_account: this.isDirectDeposit ? { account2: params } : undefined,
            payment_detail: this.isDirectDeposit ? undefined : params,
          }
          return API.updateConsultantPaymentInfo(this.paymentInfo.advisor_id, paramsData)
            .then((data) => {
              this.dialogVisible = false
              this.$emit('update')
              this.$message({
                message: 'success',
                type: 'success',
              })
            }).finally(() => {
              this.submit_loading = false
            })
        }
      })
    },
  },
}
</script>
