<template>
  <el-collapse class="box-card">
    <el-collapse-item name="client">
      <template slot="title">
        <el-checkbox
          v-model="portal.is_send"
          :disabled="!contract"
          @change="handleIsSend">
          Client Mail
        </el-checkbox>
        <el-select
          v-if="portal.is_send"
          v-model="portal.template"
          value-key="id"
          class="w-200px ml-8"
          @change="handleTemplateChange">
          <el-option
            v-for="template in options"
            :key="template.id"
            :label="template.name"
            :value="template" />
        </el-select>
        <span v-show="!contract" class="warning ml-8"><i class="el-icon-warning" /> Need a valid contract.</span>
        <span v-show="noticeVisible" class="warning ml-8"><i class="el-icon-warning" /> You need to regenerate the email because the info above has changed.</span>
      </template>
      <div v-if="portal.is_send" v-loading="loading">
        <to-cc-editor :data="portal.email" class="mb-8" />
        <el-input v-model="portal.email.subject" class="mb-8" placeholder="Subject" />
        <tinymce ref="tinymce_editor" v-model="portal.email.content" :toolbar="toolbar" />
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import Mixin from './mixins/generateMail'
import EmailAPI from '@/api/email'

export default {
  name: 'ClientMail',
  mixins: [Mixin],
  data() {
    return {
      noticeVisible: false,
    }
  },
  watch: {
    'form': {
      handler() {
        if (this.portal.is_send) {
          this.portal.is_send = false
          this.noticeVisible = true
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getEmailTemplateList().then(() => {
      this.handleIsSend(this.portal.is_send)
    })
  },
  methods: {
    handleIsSend(val) {
      this.noticeVisible = false
      if (val) {
        this.getEmailTemplateList().then(() => {
          this.handleTemplateChange()
        })
      }
    },
    async getEmailTemplateList() {
      if (this.options.length) return

      const params = { has_permission: true, content_type: 'PORTAL_CONTACT_POST_CALL' }
      const data = await EmailAPI.getEmailList(params)
      this.options = data.list
      if (!this.portal.template) {
        this.portal.template = data.list[0]
      }
    },
    async handleTemplateChange() {
      const data = await this.generateMail(this.portal.template.id, true)
      this.portal.email = data
      this.portal.email.content = '<div style="font-size: 11pt; font-family: Calibri;">' + this.portal.email.content + '</div>'

      if (!this.$refs.tinymce_editor) return
      this.$refs.tinymce_editor.confirmLoaded()
        .then(() => {
          this.$nextTick(() => {
            this.$refs.tinymce_editor.setContent(this.portal.email.content)
          })
        })
    },
  },
}
</script>

<style scoped>

</style>
