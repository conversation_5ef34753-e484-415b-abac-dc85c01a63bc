<template>
  <el-collapse class="box-card">
    <el-collapse-item name="consultant">
      <template slot="title">
        <el-checkbox
          v-model="portal.is_send"
          :class="{'only-checkbox-disabled': type === 2 && !no_ca}"
          :disabled="type === 2"
          @change="handleIsSend">
          <slot v-if="type === 1">Payment Confirmation Email</slot>
          <slot v-if="type === 2">Post Call CA</slot>
        </el-checkbox>
        <el-select
          v-if="portal.is_send && !no_ca"
          v-model="portal.template"
          value-key="id"
          class="w-200px ml-8 mr-8"
          style="height: 48px;"
          @change="handleTemplateChange">
          <el-option
            v-for="template in options"
            :key="template.id"
            :label="template.name"
            :value="template" />
        </el-select>
        <slot v-if="portal.is_send && isCaTemplate && type === 2 && !no_ca">
          Post CA
          <el-select v-model="portal.task_ca.inquiry" value-key="id" class="ml-8" @change="handleCaChange">
            <el-option
              v-for="item in ca_list"
              :key="item.id"
              :label="item.name"
              :value="item" />
          </el-select>
          <el-select v-model="portal.task_ca.inquiry_branch" value-key="id" @change="handleCaBranchChange">
            <el-option
              v-for="item in ca_branches_list"
              :key="item.id"
              :label="item.locale"
              :value="item" />
          </el-select>
        </slot>
        <slot v-if="displayNeedCAWarning">
          <span class="warning ml-8"><i class="el-icon-warning" /> Need a Post Call CA.</span>
        </slot>
      </template>
      <div v-if="portal.is_send && !no_ca" v-loading="loading">
        <to-cc-editor :data="portal.email" class="mb-8" />
        <el-input v-model="portal.email.subject" class="mb-8" placeholder="Subject" />
        <tinymce ref="tinymce_editor" v-model="portal.email.content" :toolbar="toolbar" />
        <div v-if="type === 2" class="display-none">
          <ca-copy v-if="post_ca" id="ca-copy" :data="post_ca" />
        </div>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import Mixin from './mixins/generateMail'
import EmailAPI from '@/api/email'
import InquiryAPI from '@/api/inquiry'
import CaCopy from '@/components/Question/CACopy'

export default {
  name: 'ConsultantMail',
  components: { CaCopy },
  mixins: [Mixin],
  props: {
    type: Number,
  },
  data() {
    return {
      no_ca: false,
      ca_list: [],
      ca_branches_list: [],
      post_ca: null,
    }
  },
  computed: {
    isCaTemplate() {
      const template = this.portal.template
      return template && template.tags.includes('CA')
    },
    clientRequiredPostCallCA() {
      return this.task && this.task.client.require_post_call_ca
    },
    displayNeedCAWarning() {
      return this.clientRequiredPostCallCA && this.no_ca
    },
  },
  mounted() {
    Promise.all([this.getEmailTemplateList(), this.getPostCallCA()]).then(() => {
      this.handleIsSend(this.portal.is_send)
    })
  },
  methods: {
    handleIsSend(val) {
      // POST CA 是否勾选 依据客户的 post ca required 判断，禁止勾选
      if (this.type === 2 && !this.clientRequiredPostCallCA) {
        this.portal.is_send = false
        return
      }
      if (val && !this.no_ca) {
        this.getEmailTemplateList().then(() => {
          this.handleTemplateChange()
        })
      }
    },
    async getEmailTemplateList() {
      if (this.options.length) return

      const params = { has_permission: true, content_type: 'PORTAL_ADVISOR_POST_CALL' }
      const data = await EmailAPI.getEmailList(params)
      const tags = this.type === 1 ? ['PAYMENT_CONFIRM', 'ADVISOR_FEEDBACK'] : ['CA']

      /* survey 与 consultation 项目模板区分*/
      const project_type_list = this.task.project.sub_type === 'Survey' ? data.list.filter(
        item => item.tags.includes('SURVEY')) : data.list.filter(item => !item.tags.includes('SURVEY'))
      this.options = project_type_list.filter(item => tags.every(e => item.tags.includes(e)))

      if (!this.portal.template) {
        this.portal.template = this.options.find(item => item.tags.includes('CA_LINK')) || this.options[0]
      }
    },
    async handleTemplateChange() {
      if (!this.portal.is_send) return
      const data = await this.generateMail(this.portal.template.id, false)

      if (this.type === 2) {
        const html = '<div contenteditable="false">' + document.getElementById('ca-copy').innerHTML + '</div>'
        data.content = data.content.replace('{CA_CONTENT}', html)
      }

      this.portal.email = data
      this.portal.email.content = '<div style="font-size: 11pt; font-family: Calibri;">' + this.portal.email.content +
        '</div>'

      if (!this.$refs.tinymce_editor) return
      this.$refs.tinymce_editor.confirmLoaded()
        .then(() => {
          this.$nextTick(() => {
            this.$refs.tinymce_editor.setContent(this.portal.email.content)
          })
        })
    },
    handleCaChange(val) {
      this.ca_branches_list = val.branches.filter(item => item.is_default_using)
      const branch = this.ca_branches_list.find(item => item.locale === this.task.advisor.locale)
      this.portal.task_ca.inquiry_branch = branch || this.ca_branches_list[0]
      this.handleCaBranchChange()
    },
    handleCaBranchChange() {
      this.handleCa()
      this.handleTemplateChange()
    },
    handleCa() {
      const inquiry = Object.assign({}, this.portal.task_ca.inquiry_branch)
      inquiry.questions = inquiry.questions.map(item => {
        if (item.sub_questions) {
          item.sub_questions.forEach(sub => {
            sub.answer = ''
          })
        } else {
          item.answer = ''
        }
        return item
      })
      this.post_ca = inquiry
    },
    async getPostCallCA() {
      if (this.isCommonProject || this.type === 1) return

      const params = {
        types: 'POST_CALL_CA',
        client_id: this.task.client_id,
        extra: 'branches,branches.questions,branches.name,branches.description',
      }
      const data = await InquiryAPI.getInquiryList(params)

      if (!data.list.length) {
        // this.portal.is_send = false
        this.no_ca = true
        return
      }
      this.ca_list = data.list

      // 初始化 POST CA
      if (data.list.length) {
        this.portal.task_ca.inquiry = this.ca_list[0]
        this.ca_branches_list = this.ca_list[0].branches.filter(item => item.is_default_using)
        const branch = this.ca_branches_list.find(item => item.locale === this.task.advisor.locale)
        this.portal.task_ca.inquiry_branch = branch || this.ca_branches_list[0]
        this.handleCa()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.only-checkbox-disabled {
  ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #409EFF;
  }
}
</style>
