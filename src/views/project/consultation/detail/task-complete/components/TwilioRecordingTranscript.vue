<template>
  <el-collapse class="box-card">
    <el-collapse-item name="twilio" :disabled="btnDisabled">
      <template slot="title">
        <el-checkbox
          v-model="portal.is_send"
          :disabled="btnDisabled"
          @change="handleIsSend">
          Recording/Transcript Email
        </el-checkbox>
        <span
          v-if="!!clientComplianceReviewFirst"
          class="warning email-tips"> {{ clientComplianceReviewFirst }}</span>
        <span
          v-if="sendToClientThroughCapvisionPro"
          class="warning email-tips">The client's legal rules require sending emails through 'capvision-pro', you need to copy the email content and send emails through 'capvision-pro'.</span>
        <span
          v-if="!advisorConsentToRecording"
          class="warning email-tips">The expert did not consent to recording.</span>
      </template>
      <div v-loading="loading">
        <to-cc-editor :data="portal.email" :options="toCcOptions" class="mb-8" />
        <el-input v-model="portal.email.subject" class="mb-8" placeholder="Subject" />
        <tinymce ref="tinymce_editor" v-model="portal.email.content" :toolbar="toolbar" />
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import Mixin from './mixins/generateMail'
import EmailAPI from '@/api/email'
import API from '@/api/email'
import _ from 'lodash'

export default {
  name: 'TwilioRecordingTranscript',
  mixins: [Mixin],

  computed: {
    btnDisabled() {
      return this.sendToClientThroughCapvisionPro || this.clientComplianceReviewFirst || !this.advisorConsentToRecording
    },

    advisorConsentToRecording() {
      return this.task.advisor_record_consent?.is_consent
    },
  },
  mounted() {
    this.getEmailTemplateList()
      .then(() => {
        this.handleIsSend(this.portal.is_send)
      })
  },
  methods: {
    handleIsSend(val) {
      if (val || this.sendToClientThroughCapvisionPro) {
        this.getEmailTemplateList().then(() => {
          this.handleTemplateChange()
        })
      }
    },

    async getEmailTemplateList() {
      const params = {
        content_type: 'CONFERENCE_ASSET_NEW_RECORDING_AVAILABLE',
      }
      const data = await EmailAPI.getEmailList(params)
      if (!this.portal.template) {
        this.portal.template = data.list[0]
      }
    },

    async handleTemplateChange() {
      const params = {
        advisor_id: this.task.advisor_id,
        contact_id: this.task.client_contact_id,
        project_id: this.task.project_id,
        task_id: this.task.id,
        no_replace_placeholders: [
          '{{ADVISOR_NAME}}',
        ],
      }
      const data = await API.getEmailByTemplate(this.portal.template.id, params)
      this.portal.email = data
      this.portal.email.content = '<div style="font-size: 11pt; font-family: Calibri;">' +
        this.replaceAdvisorName(this.portal.email.content) + '</div>'
      this.portal.email.subject = this.replaceAdvisorName(this.portal.email.subject)
      this.handleToCC(data)

      if (!this.$refs.tinymce_editor) return
      this.$refs.tinymce_editor.confirmLoaded()
        .then(() => {
          this.$nextTick(() => {
            this.$refs.tinymce_editor.setContent(this.portal.email.content)
          })
        })
    },

    handleToCC(data) {
      // 2025/1/9 0:24 outlook subject:Please change logic for Addresses to receive transcripts/recordings for General Atlantic
      if (this.designatedRecipient.length) {
        const client_approve_list = this.task.events.filter(
          item => ['CLIENT_COMPLIANCE_AUTO_APPROVE', 'CLIENT_COMPLIANCE_APPROVE'].includes(item.type))
        const last_approve_type = _.sortBy(client_approve_list, 'create_at')[0]?.type || null

        if (last_approve_type !== 'CLIENT_COMPLIANCE_AUTO_APPROVE') {
          this.portal.email.to_addresses = []
          this.portal.email.to_list = this.designatedRecipient
        }
      }

      const has_rule = this.task?.client.compliance_preference?.rule
      const compliance_require_cc = has_rule &&
        has_rule.compliance_rules?.transcript_notetaking_rules?.is_copy_client_compliance_when_sending

      if (compliance_require_cc) {
        const cc_list = has_rule.compliance_rules?.transcript_notetaking_rules?.cc_on_all_transcript_recording_emails_to_analysts ||
          []

        if (cc_list.length) {
          this.portal.email.cc_addresses = []
          this.portal.email.cc_list = [...new Set(data.cc_list.concat(cc_list))]
        }
      }
    },

    replaceAdvisorName(email) {
      /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
      const client_status_is_not_EXECUTE = this.task.client.status !== 'EXECUTE'
      const client_compliance_status_no_pass = this.task.client.compliance_status !== 'APPROVED'
      const client_blind_expert_name = this.task.client.preference?.blind_expert_names_in_to_client_and_portal
      const project_blind_expert_name = this.task.project?.blind_expert_profiles
      if (client_status_is_not_EXECUTE || client_compliance_status_no_pass || !!client_blind_expert_name ||
        project_blind_expert_name) {
        return email.replace('{{ADVISOR_NAME}}', `Advisor ${this.task.display_id}`)
      } else {
        const format_name = `${this.task.advisor.name_prefix ||
        ''} ${this.task.advisor.firstname} ${this.task.advisor.lastname}`
        return email.replace('{{ADVISOR_NAME}}', format_name)
      }
    },
  },
}
</script>

<style scoped>
.email-tips {
  line-height: 1em;
  padding-left: 20px;
}
</style>
