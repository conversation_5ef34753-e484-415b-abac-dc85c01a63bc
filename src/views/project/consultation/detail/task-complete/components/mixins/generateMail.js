import API from '@/api/email'
import Tinymce from '@/components/Tinymce'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import moment from 'moment-timezone'
import { mapState } from 'vuex'

export default {
  components: { Tinymce, ToCcEditor },
  props: {
    form: Object,
    isCommonProject: {
      type: Boolean,
      default: false,
    },
    task: Object,
    portal: Object,
    contract: Object,
    sendToClientThroughCapvisionPro: {
      type: Boolean,
      default: false,
    },
    clientComplianceReviewFirst: {
      type: [String, Boolean],
      default: false,
    },
    toCcOptions: {
      type: Array,
      default() {
        return []
      },
    },
    designatedRecipient: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      toolbar: ['fontselect fontsizeselect | undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      options: [],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),

  },
  methods: {
    generateMail(id, isClient) {
      const data = {
        advisor_id: this.task.advisor_id,
      }
      if (isClient) {
        const _data = {
          client_id: this.task.client_id,
          contact_id: this.task.client_contact_id,
          user_id: null,
          project_id: this.task.project_id,
          task_id: this.task.id,
        }
        Object.assign(data, _data)
        const info = this.form.billingNotes
        Object.assign(data, {
          context_params: {
            revenue_preview: {
              basic_hour: this.form.client_hours + '',
              overseas: info.has_overseas ? info.overseas + '' : null,
              senior_expert: info.has_senior ? info.senior + '' : null,
              in_person: info.has_in_person ? info.in_person + '' : null,
              transcription: info.has_transcription ? info.transcription *
                this.form.client_hours + '' : null,
              translation: info.has_translation ? info.translation *
                this.form.client_hours + '' : null,
              discount: info.has_discount ? info.discount_hours + '' : null,
              total: this.form.billing_hours * this.contract.unit_price_china +
                ' ' + this.contract.currency,
              billing_notes: this.form.billing_notes_str,
            },
            TASK_START_TIME: moment.tz(this.form.time_info.start_date + ' ' +
              this.form.time_info.start_time, this.timeZone).toISOString(),
            TASK_END_TIME: moment.tz(
              this.form.time_info.end_date + ' ' + this.form.time_info.end_time,
              this.timeZone).toISOString(),
          },
        })
      } else {
        const payment = this.form.payment
        // const lead = this.task.lead
        const project = this.task.project
        Object.assign(data, {
          context_params: {
            payment: {
              PAYMENT_HOURLY_RATE: `${payment.rate} ${payment.currency}/H`,
              PAYMENT_TIME: `${payment.minutes} minutes`,
              PAYMENT_AMOUNT: `${payment.amount} ${payment.currency}`,
              PAYMENT_PROJECT_NAME: project.name,
              // PAYMENT_TASK_MANAGER_NAME: lead ? lead.name : null,
              // PAYMENT_TASK_MANAGER_SIGNATURE: lead ? lead.signature : null,
              // PAYMENT_TASK_MANAGER_EMAIL: lead ? lead.email : null,
            },
          },
        })
      }
      this.loading = true
      return API.getEmailByTemplate(id, data)
        .finally(() => {
          this.loading = false
        })
    },
  },
}
