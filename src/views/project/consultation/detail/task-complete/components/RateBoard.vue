<template>
  <div class="rate-board">
    <div>
      <b>Expert Rate :</b>
      <slot v-if="task.rate">
        {{ task.rate }} {{ task.rate_currency }}/H
      </slot>
      <slot v-if="!task.rate && task.advisor.rate">
        {{ task.advisor.rate }} {{ task.advisor.rate_currency }}/H
      </slot>
    </div>
    <div>
      <b>Client Rate :</b>
      {{
        task.client_rate_usd || task.specified_client_rate_currency_after_multiple || task.default_client_rate_usd ||
          task.default_client_rate_usd_considering_unsigned_tc || '-'
      }}
    </div>
    <div v-if="task.advisor.time_minimum !== 'NO_MINIMUM'">
      {{ task.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'RateBoard',
  props: {
    'task': Object,
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
  },
}
</script>

<style scoped lang="scss">
.rate-board {
  font-size: 12px;
  line-height: 1.6;
  padding: 8px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 2px 12px 0;
  height: fit-content;

  b {
    margin-right: .5rem;
  }
}
</style>
