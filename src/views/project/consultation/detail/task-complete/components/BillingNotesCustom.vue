<template>
  <el-form-item label="Billing Notes" style="margin-bottom: 0;">
    <el-row>
      <el-col :md="4">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_discount" :disabled="hasBilled">Discount</el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :md="4">
        <el-form-item v-if="form.billingNotes.has_discount" prop="billingNotes.discount_hours">
          <el-input-number v-model="form.billingNotes.discount_hours" :min="0" :step="0.5" :disabled="hasBilled" />
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_discount" :md="16">
        Discount Amount {{ form.billingNotes.discount_hours }}H
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox
            v-model="form.billingNotes.not_charged"
            :disabled="hasBilled"
            @change="handleChangeNoCharged"
          >
            Client will not be charged for this call
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col v-if="form.billingNotes.not_charged" :md="16" class="flex">
        <el-form-item prop="billingNotes.not_charged_reason">
          <el-select
            v-model="form.billingNotes.not_charged_reason"
            clearable
            placeholder="No Charge Reason"
            class="w-200px"
            @change="handleChangeNoCharged"
          >
            <el-option
              v-for="item in options.not_charged_reasons"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.billingNotes.not_charged_reason === 'Other'"
          prop="billingNotes.not_charged_reason_input"
          class="flex-1 ml-8"
        >
          <el-input
            v-model="form.billingNotes.not_charged_reason_input"
            type="text"
            placeholder="Please enter an explanation."
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="4">
        <el-form-item>
          <div class="flex align-items-center">
            <el-checkbox v-model="form.billingNotes.has_senior" :disabled="hasBilled">Senior Expert</el-checkbox>
            <el-tooltip v-if="viewData && viewData.china_senior" effect="dark" placement="top">
              <div slot="content">
                <table>
                  <thead>
                    <th align="left" width="100">Rate</th>
                    <th align="left">Additional Charge</th>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in viewData.china_senior.rules" :key="index">
                      <td v-for="rule in item" :key="rule">{{ rule }}</td>
                    </tr>
                  </tbody>
                </table>
                <span>{{ viewData.china_senior.addition }}</span>
                <table>
                  <thead>
                    <th align="left" width="100">Rate</th>
                    <th align="left">Additional Charge</th>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in viewData.non_china_senior.rules" :key="index">
                      <td v-for="rule in item" :key="rule">{{ rule }}</td>
                    </tr>
                  </tbody>
                </table>
                <span>{{ viewData.non_china_senior.addition }}</span>
              </div>
              <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
            </el-tooltip>
          </div>
        </el-form-item>
      </el-col>
      <el-col :md="4">
        <el-form-item v-if="form.billingNotes.has_senior" prop="billingNotes.senior">
          <el-input-number v-model="form.billingNotes.senior" :min="0" :step="0.5" :disabled="hasBilled" />
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_senior" :md="16">
        Charge {{ form.billingNotes.senior }}X Fee
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_in_person" :disabled="hasBilled || isNotSetUp('term_in_person')">In Person</el-checkbox>
          <span v-if="isNotSetUp('term_in_person')" class="info">This item is not set in the contract.</span>
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_in_person && viewData" :md="16">
        <span v-if="form.billingNotes.in_person">Extra charge: +{{ form.billingNotes.in_person }}H per meeting</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_overseas" :disabled="hasBilled || isNotSetUp('term_overseas')">Oversea</el-checkbox>
          <span v-if="isNotSetUp('term_overseas')" class="info">This item is not set in the contract.</span>
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_overseas && viewData" :md="16">
        <span v-if="form.billingNotes.overseas">Charge {{ form.billingNotes.overseas }}X base contract price</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_transcription" :disabled="hasBilled || isNotSetUp('term_transcription')">Transcription</el-checkbox>
          <span v-if="isNotSetUp('term_transcription')" class="info">This item is not set in the contract.</span>
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_transcription && viewData" :md="16">
        <span
          v-if="form.billingNotes.transcription">Extra charge {{ form.billingNotes.transcription }} H per hour</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_translation" :disabled="hasBilled || isNotSetUp('term_translation')">Translation</el-checkbox>
          <span v-if="isNotSetUp('term_translation')" class="info ml-8">This item is not set in the contract.</span>
        </el-form-item>
      </el-col>
      <el-col v-show="form.billingNotes.has_translation && viewData" :md="16">
        <span v-if="form.billingNotes.translation"> Extra charge {{ form.billingNotes.translation }} H per hour</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="8">
        <el-form-item>
          <el-checkbox v-model="form.billingNotes.has_charge_cash" :disabled="hasBilled || isNoCharge">Charge Total</el-checkbox>
        </el-form-item>
      </el-col>
      <el-col v-if="form.billingNotes.has_charge_cash && viewData" :md="16">
        <el-form-item prop="billingNotes.specified_charge_cash">
          <el-input-number v-model="form.billingNotes.specified_charge_cash" :disabled="hasBilled || isNoCharge" class="w-120px" :min="0" />
          <el-select
            v-model="form.billingNotes.specified_charge_cash_currency"
            placeholder="Currency"
            disabled
            class="w-200px ml-3">
            <el-option
              v-for="item in options.currency"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-form-item>
      <el-card v-show="boundContract" shadow="none">
        <div class="flex">
          <b style="margin-right: 8px;">
            Billing Hour
          </b>
          <div>
            <div>= Client Hour * Extra Charge + In-Person - Discount Hour</div>
            <div>= Client Hour * (Additional Expert Charge + Translation Fee + Transcription Fee) + In-Person - Discount Hour</div>
            <div>= Client Hour * [(Senior Rate * Oversea Rate) + Translation Fee + Transcription Fee] + In-Person - Discount Hour</div>
            <div>= {{form.client_hours}} *
              ({{billingNotes_handled.senior_fee ? billingNotes_handled.senior_fee: 1}} * {{1 + billingNotes_handled.oversea_fee}} +
              {{billingNotes_handled.translation_fee}} + {{billingNotes_handled.transcription_fee}})
              + {{billingNotes_handled.in_person_fee}} - {{billingNotes_handled.discount_hour}}
            </div>
            <div>= {{form.billing_hours}} CapH</div>
          </div>
        </div>
      </el-card>
    </el-form-item> -->
    <el-form-item>
      <el-input
        v-model="form.billingNotes.remarks"
        type="textarea"
        placeholder="Remarks"
        :autosize="{ minRows: 2, maxRows: 5}"
        :disabled="hasBilled" />
    </el-form-item>
    <div
      v-if="boundContract"
      class="pre-wrap">{{ form.billing_notes_str }}
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'BillingNotesCustom',
  props: {
    isBCG: Boolean,
    form: Object,
    viewData: Object,
    boundContract: Object,
    hasBilled: Boolean,
  },
  data() {
    return {
      billingNotes_handled: {},
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        not_charged_reasons: [
          'Expert was not a good fit',
          'Technical issues',
          'Poor quality',
          'Proposal',
          'Client or prospect special discount',
          'Other',
        ],
      },
      bcg_sub_type_options: [
        { label: 'Written Follow Up', value: 'WRITTEN_FOLLOW_UP' },
        { label: 'Phone', value: 'PHONE' },
        { label: 'Survey', value: 'SURVEY' },
        { label: 'BD Call', value: 'BD_CALL' },
      ],
    }
  },
  computed: {
    isNoCharge() {
      return this.form.billingNotes.not_charged
    },
  },
  watch: {
    'form.client_hours': {
      handler() {
        this.form.billing_hours = this.calculateBillingHours()
        this.form.billing_notes_str = this.handleUSClientBillingNotes(this.billingNotes_handled)
      },
    },
    'form.sub_type': {
      handler() {
        this.form.billing_notes_str = this.handleUSClientBillingNotes(this.billingNotes_handled)
      },
    },
    'form.billingNotes': {
      handler(newVal) {
        if (newVal) {
          this.billingNotes_handled = {
            discount_hour: newVal.has_discount ? newVal.discount_hours : 0,
            // not_charged: newVal.not_charged,
            // not_charged_reason: newVal.not_charged ? (newVal.not_charged_reason === 'Other'
            //   ? newVal.not_charged_reason_input
            //   : newVal.not_charged_reason) : null,
            senior_fee: newVal.has_senior ? newVal.senior : 0,
            oversea_fee: newVal.has_overseas ? newVal.overseas : 0,
            transcription_fee: newVal.has_transcription ? newVal.transcription : 0,
            translation_fee: newVal.has_translation ? newVal.translation : 0,
            in_person_fee: newVal.has_in_person ? newVal.in_person : 0,
            has_charge_cash: newVal.has_charge_cash,
            specified_charge_cash: newVal.has_charge_cash ? newVal.specified_charge_cash : null,
            specified_charge_cash_currency: newVal.has_charge_cash ? newVal.specified_charge_cash_currency : null,
            remarks: newVal.remarks,
          }
          this.form.billing_hours = this.calculateBillingHours()
          this.form.billing_notes_str = this.handleUSClientBillingNotes(this.billingNotes_handled)
        }
      },
      deep: true,
    },
  },
  methods: {
    handleUSClientBillingNotes(data) {
      if (this.isBCG && data.has_charge_cash) {
        return this.generateBCGNote()
      }
      const notes_strings = []

      if (data.discount_hour) {
        notes_strings.push([data.discount_hour, 'h discount'].join(''))
      }

      if (data.senior_fee) {
        notes_strings.push(['x', data.senior_fee, ' accounted for senior expert fee'].join(''))
      }

      if (data.in_person_fee) {
        notes_strings.push(['extra ', data.in_person_fee, 'hr accounted for in-person fee'].join(''))
      }

      if (data.oversea_fee) {
        notes_strings.push(['extra x', data.oversea_fee, ' accounted for oversea expert fee'].join(''))
      }

      if (data.transcription_fee) {
        notes_strings.push(['extra ', data.transcription_fee, 'hr/hr accounted for transcription fee'].join(''))
      }

      if (data.translation_fee) {
        notes_strings.push(['extra ', data.translation_fee, 'hr/hr accounted for interpretation fee'].join(''))
      }

      if (data.has_charge_cash) {
        notes_strings.push([data.specified_charge_cash, data.specified_charge_cash_currency, 'charge total'].join(' '))
      }

      let remarks_string = ''

      if (data.remarks && !this.isBCG) {
        remarks_string = ['Remarks: ', data.remarks].join('')
      }

      if (this.isBCG) {
        const call_duration = (this.form.billing_hours * 60).toFixed(2)
        const type = this.form.sub_type ? this.bcg_sub_type_options.find(item => item.value === this.form.sub_type).label : ''
        remarks_string = [`${call_duration} minutes, ${type}`].join('')
      }

      notes_strings.push(remarks_string)

      return notes_strings.join('; ')
    },
    calculateBillingHours() {
      return this.form.client_hours *
        ((this.billingNotes_handled.senior_fee ? this.billingNotes_handled.senior_fee : 1) *
          (1 + this.billingNotes_handled.oversea_fee) +
          this.billingNotes_handled.translation_fee + this.billingNotes_handled.transcription_fee) +
        this.billingNotes_handled.in_person_fee - this.billingNotes_handled.discount_hour
    },

    generateBCGNote() {
      if (!this.boundContract || !this.billingNotes_handled.has_charge_cash) return
      const charge_total = `${this.billingNotes_handled.specified_charge_cash} ${this.billingNotes_handled.specified_charge_cash_currency}`
      const call_duration = (this.form.billing_hours * 60).toFixed(2)
      const type = this.form.sub_type ? this.bcg_sub_type_options.find(item => item.value === this.form.sub_type).label : ''
      return `${charge_total} charge total; ${call_duration} minutes, ${type}`
    },

    isNotSetUp(key) {
      return !this.hasBilled && this.boundContract && !this.boundContract[key]
    },

    handleChangeNoCharged() {
      const form = { ...this.form }
      if (form.billingNotes.not_charged && form.billingNotes.not_charged_reason !== 'Proposal') {
        form.client_hours = 0
        form.billingNotes.has_charge_cash = true
        form.billingNotes.specified_charge_cash = 0
        this.$emit('update:form', form)
      } else {
        this.$emit('calculateHour')
      }
    },
  },
}
</script>

<style scoped lang="scss">
.el-form-item .el-form-item--mini {
  margin-bottom: 12px;
}
</style>
