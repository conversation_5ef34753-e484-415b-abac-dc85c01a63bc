<template>
  <div v-loading="loading || contract_loading">
    <el-page-header content="Task Complete" @back="actionGotoDetail" />
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="160px">
      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Basic Information</span>
        </div>
        <el-form-item label="Start Time" required style="margin-bottom: 0;">
          <el-form-item prop="time_info.start_date" class="inline-block">
            <el-date-picker
              v-model="form.time_info.start_date"
              type="date"
              placeholder="Date"
              value-format="yyyy-MM-dd"
              @change="handleStartDateChange" />
          </el-form-item>
        </el-form-item>
        <el-form-item label="Lead" prop="lead_uid">
          <el-select v-model="form.lead_uid" class="w-220px">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
        </el-form-item>
        <el-form-item label="Support" prop="support_uid" style="margin-bottom: 0;">
          <el-select v-model="form.support_uid" class="w-220px">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
        </el-form-item>
      </el-card>

      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Client Charge Information</span>
        </div>
        <el-form-item label="Charge" prop="survey_receivable.amount">
          <el-input-number
            v-model="form.survey_receivable.amount"
            :min="0"
            class="mr-8" />
          <el-select
            v-model="form.survey_receivable.currency"
            placeholder="Currency"
            class="w-120px">
            <el-option
              v-for="i in advisor_options.currency"
              :key="i"
              :label="i"
              :value="i" />
          </el-select>
        </el-form-item>
      </el-card>

      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Payment Information</span>
        </div>
        <div class="flex justify-content-between">
          <div>
            <el-form-item label="Amount" prop="payment.amount">
              <el-input-number
                v-model="form.payment.amount"
                :min="0"
                class="mr-8"
                @input="checkW9Status" />
              {{ form.payment.currency }}
              <el-tooltip v-if="task" effect="dark" placement="top">
                <div slot="content">
                  <span>Rate: {{ task.advisor.rate }} {{ task.advisor.rate_currency }}/H</span>
                </div>
                <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="form.w9_status!==0" label="Form W-9">
              <div v-if="[1,3,4].includes(form.w9_status)">
                <div v-if="form.w9_status===3">Under signing...</div>
                <div v-if="form.w9_status===4">Signed</div>
                <div class="info">Experts have received ${{ calculate_advisor_paid_this_year }} this year.</div>
              </div>
              <div v-if="form.w9_status===2">
                <el-checkbox v-model="form.should_send_w9_form">Send "Form W-9"
                </el-checkbox>
                <div class="info">Expert's payment has over ${{ calculate_advisor_paid_this_year }} this
                  year,need to send w9 forms to experts to fill in tax
                  information.
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="form.enable_physical_check_payment_method">Enable Physical Check Payment Method</el-checkbox>
            </el-form-item>
          </div>
          <rate-board
            v-if="task"
            :task="task"
          />
        </div>
      </el-card>

      <slot v-if="!loading">
        <!-- is_cn / Outsource导入专家在美国项目complete 不用发送 payment information confirm -->
        <consultant-mail
          v-if="!isImportedFromCNDB && !isImportedFromOutsource"
          ref="consultant-mail1"
          :form="form"
          :task="task"
          :portal="advisor_portal1"
          :type="1" />

      </slot>
    </el-form>

    <el-tooltip
      placement="top"
      :disabled="!checkDisabled"
      content="The client has no active contracts in the selected time period.">
      <el-button type="success" :loading="submit_loading" :disabled="checkDisabled" @click="submit">Submit</el-button>
    </el-tooltip>
    <el-button type="text" @click="actionGotoDetail">Cancel</el-button>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import API from '@/api/project'
import moment from 'moment-timezone'
import ConsultantMail from './components/ConsultantMail'
import RateBoard from '@/views/project/consultation/detail/task-complete/components/RateBoard'
import { RouteTools } from '@/utils/tool'

export default {
  name: 'SurveyComplete',
  components: { ConsultantMail, RateBoard },
  data() {
    const validateDate = (rule, value, callback) => {
      const obj = this.form.time_info
      const start = moment.tz(`${obj.start_date} ${obj.start_time}`, this.timeZone)
      if (!obj.start_date) {
        callback(new Error('Start Date is required.'))
      } else if (moment().isBefore(start)) {
        callback(new Error('Start Time cannot be later than now.'))
      } else {
        callback()
      }
    }
    return {
      loading: true,
      contract_loading: false,
      submit_loading: false,
      calculate_advisor_paid_this_year: undefined,
      form: {
        time_info: {
          start_date: moment().format('YYYY-MM-DD'),
          start_time: '00:00',
        },
        lead_uid: undefined,
        support_uid: undefined,
        members_list: [],
        w9_status: 0,
        should_send_w9_form: false,
        enable_physical_check_payment_method: false,
        payment: {
          amount: 0,
          currency: '',
          rate: 0,
        },
        survey_receivable: {
          amount: 0,
          currency: 'USD',
        },
      },
      advisor_portal1: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      rules: {
        'lead_uid': [{ required: true, trigger: 'change', message: 'Lead is required' }],
        'support_uid': [{ required: true, trigger: 'change', message: 'Support is required' }],
        'time_info.start_date': [{ required: true, validator: validateDate, trigger: 'change' }],
        'payment.amount': [{ required: true, trigger: 'change' }],
      },
      task: null,
      bound_contract: null,
      viewData: {},
      task_detail_extra: [
        'advisor.location_id_path',
        'advisor.paid_usd_in_this_year_jit',
        // 'advisor.w9_form',
        'advisor.sourced_by',
        'advisor.bank_account',
        'lead',
        'client',
        'angle',
        'project.members.user',
        'survey_receivable',
        'client_rate_usd',
        'default_client_rate_usd',
        'default_client_rate_usd_considering_unsigned_tc',
        'specified_client_rate_currency_after_multiple',
        'task_outsource_info',
      ].join(),
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
    ...mapState({
      client_id: state => state.project.data.client_id,
      timeZone: state => state.app.timeZone,
      isSGDB: state => state.app.isSGDB,
      userInfo: state => state.user.info,
    }),
    if_use_time() {
      if (this.form.time_info.start_date && this.form.time_info.start_time) {
        return moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time, this.timeZone)
          .toISOString()
      } else {
        return undefined
      }
    },

    isUSAdvisor() {
      return this.task && this.task.advisor && !this.task.advisor.is_cn
    },
    isImportedFromCNDB() {
      return this.task && this.task.advisor.is_cn
    },
    isImportedFromOutsource() {
      return this.task?.task_outsource_status === 'IMPORTED'
    },
    checkDisabled() {
      return !this.bound_contract
    },
  },
  watch: {
    'timeZone': {
      handler(newVal, oldVal) {
        const _this = this
        handleTimeChange('start_date', 'start_time')

        function handleTimeChange(date, time) {
          const time_info = _this.form.time_info
          if (time_info[date]) {
            const new_time = moment.tz(time_info[date], oldVal).tz(newVal)
            time_info[date] = new_time.format('YYYY-MM-DD')
            time_info[time] = '00:00'
          }
        }
      },
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      return this.getTaskDetail()
        .then(() => {
          // Consultant Mail, Client Mail 在二次 Complete 时默认不勾选，初次 Complete 时勾选
          if (this.task['general_status'] !== 'COMPLETED' && !this.task['complete_time']) {
            // is_cn / Outsource导入的专家 不发payment info
            this.advisor_portal1.is_send = !this.isImportedFromCNDB && !this.isImportedFromOutsource
          }
          this.getTaskRate()
        })
        .finally(() => { this.loading = false })
    },

    /* task rate 优先取自己的值，其次是angle绑定的rate，再其次是project rate*/
    getTaskRate() {
      if (this.task.rate !== null) {
        this.form.payment.amount = this.task.rate
        this.form.payment.currency = this.task.rate_currency
      } else if (this.task.angle && this.task.angle.rate !== null) {
        this.form.payment.amount = this.task.angle.rate
        this.form.payment.currency = this.task.angle.rate_currency
      } else {
        this.form.payment.amount = this.task.project.rate
        this.form.payment.currency = this.task.project.rate_currency
      }
    },

    getTaskDetail() {
      const params = {
        project_id: this.$route.params.project_id,
        id: this.$route.query.id,
        params: {
          extra: this.task_detail_extra,
        },
      }
      return API.getTaskDetail(params).then(data => {
        this.task = data

        if (data.start_time) {
          // 处理显示时间
          const start = data.start_time
          this.form.time_info = {
            start_date: moment(start).tz(this.timeZone).format('YYYY-MM-DD'),
            start_time: '00:00',
          }
        }
        // 获取task开始时间后 校验合同
        return this.getBoundContract()
          .then(() => {
            return data
          })
      }).then(data => {
        // 处理 members_list 开始
        const hash = {}
        this.form.members_list = data.project.members.reduce((arr, next) => {
          if (!hash[next.uid]) {
            hash[next.uid] = true && arr.push(next)
          }
          return arr
        }, [])

        const user_exist = this.form.members_list.find(item => item.uid === this.userInfo.id)
        if (!user_exist) {
          const item = { uid: this.userInfo.id, user: { name: this.userInfo.name } }
          this.form.members_list.push(item)
        }
        // 处理 members_list 结束

        if (data.general_status === 'COMPLETED') {
          // 获取RM RA
          this.form.lead_uid = data.lead_uid
          this.form.support_uid = data.support_uid
        }

        // Complete时 如果是美国专家 校验 W9 状态值:
        // 1:未达标 => 当年累计达到 $600
        // 2:达标 未发送W9
        // 3:达标 已发送 专家未签
        // 4:达标 专家已签 无需再发送（一旦签过，后续无需再签）
        if (data.advisor.location_id_path && data.advisor.location_id_path.split(' ').includes('174')) {
          this.calculate_advisor_paid_this_year = data.advisor.paid_usd_in_this_year_jit
          if (data.advisor.w9_form) {
            switch (data.advisor.w9_form.status) {
              case 'INIT':
                this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
                this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
                break
              case 'REQUESTED':
                this.form.w9_status = 3
                break
              case 'SIGNED':
                this.form.w9_status = 4
            }
          } else {
            this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
            this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
          }
        }

        // 处理bank account
        this.form.payment.rate = data.advisor.rate
        this.form.payment.currency = data.advisor.rate_currency

        // https://www.notion.so/capvision/Surveys-Charge-Per-Complete-data-point-90b8ef1442f841e5a2f5ae189652cb5e?pvs=4
        this.form.survey_receivable.amount = data.survey_receivable?.amount || this.task.project.client_charge_per_complete
        this.form.survey_receivable.currency = data.survey_receivable?.currency || this.task.project.client_charge_per_complete_currency

        const advisor_bank_data = data.advisor.bank_account
        if (advisor_bank_data && advisor_bank_data.bank_account) {
          for (const key in this.form.payment) {
            if (advisor_bank_data.bank_account[key]) this.form.payment[key] = advisor_bank_data.bank_account[key]
          }
        }

        // 非初次complete时,去上次complete的值 初始化 false
        if (data.general_status === 'COMPLETED' && !data.advisor_normal_payment?.disabled_pay_methods.includes('PHYSICAL_CHECK')) {
          this.form.enable_physical_check_payment_method = true
        }
      })
    },

    // 获取客户有效合同
    getBoundContract(val = true) {
      if (!val) return

      this.contract_loading = true
      const params = { if_use_time: this.if_use_time }
      return API.getTaskBoundContract(this.$route.query.id, params).then(data => {
        this.bound_contract = data
      }).finally(() => {
        this.contract_loading = false
      })
    },

    checkW9Status() {
      // 计算本次付款后是否达到 $600
      if (this.isUSAdvisor) {
        const sum = this.form.payment.amount + this.task.advisor.paid_usd_in_this_year_jit
        this.calculate_advisor_paid_this_year = sum
        if ([3, 4].includes(this.form.w9_status)) return

        if (sum > 600) {
          this.form.w9_status = 2
          this.form.should_send_w9_form = true
        } else {
          this.form.w9_status = 1
          this.form.should_send_w9_form = false
        }
      }
      if (this.form.payment.currency !== 'USD') {
        this.form.w9_status = 0
        this.form.should_send_w9_form = false
      }
    },

    handleStartDateChange(val) {
      this.form.time_info.end_date = val
      this.getBoundContract(val)
    },

    actionGotoDetail() {
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.push({
            name: 'ProjectDetail',
          })
        })
    },
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submit_loading = true
          const data = {
            should_send_w9_form: this.form.should_send_w9_form,
            payment: {
              amount: this.form.payment.amount,
              currency: this.form.payment.currency,
              rate: this.form.payment.rate,
              disabled_pay_methods: this.form.enable_physical_check_payment_method ? [] : ['PHYSICAL_CHECK'],
              use_legacy_payment_api: false,
            },
            survey_receivable: {
              amount: this.form.survey_receivable.amount,
              currency: this.form.survey_receivable.currency,
            },
            advisor_portal1: this.advisor_portal1.is_send ? {
              portal: {
                type: 'ADVISOR_POST_CALL',
              },
              email: this.advisor_portal1.email,
              email_template_tags: this.advisor_portal1.template?.tags,
            } : undefined,
          }

          data.task = {
            start_time: moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
              this.timeZone)
              .toISOString(),
            end_time: null,
            lead_uid: this.form.lead_uid,
            support_uid: this.form.support_uid,
          }
          return API.completeTask(this.$route.query.id, data).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Complete Task successfully!',
              type: 'success',
            })
            this.actionGotoDetail()
          })
            .finally(() => {
              this.submit_loading = false
            })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.box-card {
  margin: 8px 0;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}

::v-deep .tooltip-icon {
  font-size: 1.25em;
  margin-left: 0.5em;
  vertical-align: text-top;
}

.w-220px {
  width: 220px;
}

::v-deep .client-hour-input {
  width: 150px;

  input {
    text-align: center;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 14px;
  }
}

::v-deep .el-collapse-item__header .el-checkbox__label {
  font-size: 15px;
}

::v-deep .el-collapse {
  border: 1px solid #EBEEF5;
  padding: 0 16px;
}

::v-deep .mce-tinymce {
  width: auto !important;
}
</style>
