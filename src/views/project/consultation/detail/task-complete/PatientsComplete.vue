<template>
  <div v-loading="loading || contract_loading">
    <el-page-header content="Task Complete" @back="actionGotoDetail" />
    <post-call-rule :client-id="client_id" type="postCall" />
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="160px">
      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Basic Information</span>
        </div>
        <el-form-item label="Client Duration" prop="client_duration">
          <el-input-number
            v-model="form.client_duration"
            :min="0"
            :step="1"
            :precision="0"
            @change="calculateEndTime"
          />
          <span class="ml-8">Minutes</span>
        </el-form-item>
        <el-form-item label="Start Time" required style="margin-bottom: 0;">
          <el-form-item prop="time_info.start_date" class="inline-block">
            <el-date-picker
              v-model="form.time_info.start_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              value-format="yyyy-MM-dd"
              :disabled="hasBilled"
              @change="handleStartDateChange" />
          </el-form-item>
          -
          <el-form-item prop="time_info.start_time" class="inline-block">
            <el-time-picker
              v-model="form.time_info.start_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm"
              :disabled="hasBilled"
              @change="handleStartTimeChange" />
          </el-form-item>

          <el-tooltip
            v-if="isStartOverNow"
            effect="dark"
            content="Start Time cannot be later than now."
            placement="top">
            <el-link type="warning" :underline="false" icon="el-icon-warning tooltip-icon" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="End Time" required>
          <el-form-item prop="time_info.end_date" class="inline-block" style="margin-bottom: 0;">
            <el-date-picker
              v-model="form.time_info.end_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              :disabled="hasBilled"
              value-format="yyyy-MM-dd" />
          </el-form-item>
          -
          <el-form-item prop="time_info.end_time" class="inline-block" style="margin-bottom: 0;">
            <el-time-picker
              v-model="form.time_info.end_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm"
              disabled
              @change="calculateHour" />
          </el-form-item>

          <el-tooltip
            v-if="isEndOverNow"
            effect="dark"
            content="End Time cannot be later than now."
            placement="top">
            <el-link type="warning" :underline="false" icon="el-icon-warning tooltip-icon" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="Lead" prop="lead_uid">
          <el-select v-model="form.lead_uid">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
        </el-form-item>
        <el-form-item label="Support" prop="support_uid" style="margin-bottom: 0;">
          <el-select v-model="form.support_uid" :disabled="supportDisabled">
            <slot v-if="task">
              <el-option
                v-for="item in form.members_list"
                :key="item.uid"
                :label="item.user.name"
                :value="item.uid" />
            </slot>
          </el-select>
          <el-tooltip
            v-if="supportDisabled"
            effect="dark"
            placement="top"
            content="The specialist's TC is signed within one month and cannot be changed by the supporter.">
            <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
          </el-tooltip>
        </el-form-item>
      </el-card>

      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Payment Information</span>
        </div>
        <div class="flex justify-content-between">
          <div>
            <el-form-item label="Minutes" prop="payment.minutes">
              <el-input-number
                v-model="form.payment.minutes"
                :min="advisor_min_minutes"
                @change="calculatePaymentAmount" />
              <span v-if="hasTimeMinimumCharge" class="info ml-3">
                {{ task.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
              </span>
            </el-form-item>
            <el-form-item label="Amount" prop="payment.amount">
              <el-input-number
                v-model="form.payment.amount"
                :min="advisor_min_amount"
                class="mr-8"
                @change="checkW9Status()" />
              {{ form.payment.currency }}
              <el-tooltip v-if="task" effect="dark" placement="top">
                <div slot="content">
                  <span>Rate: {{ task.advisor.rate }} {{ task.advisor.rate_currency }}/H</span>
                </div>
                <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
              </el-tooltip>
              <el-checkbox v-model="form.should_network_loyalty_fee" class="ml-8">Network Loyalty Fee</el-checkbox>
              <el-alert
                v-if="isRateChanged"
                type="warning"
                show-icon
                :closable="false"
                class="mt-8">
                <template #title>
                  Rate has changed.
                  <el-link
                    type="primary"
                    :underline="false"
                    style="vertical-align: baseline;"
                    @click="calculatePaymentAmountByNewRate()">
                    Click to calculate amount with current rate.
                  </el-link>
                </template>
              </el-alert>
            </el-form-item>
            <el-form-item
              v-if="form.should_network_loyalty_fee"
              label="Network Loyalty Fee"
              prop="payment.network_loyalty_fee">
              <el-input-number
                v-model="form.payment.network_loyalty_fee"
                :min="0"
                class="mr-8" />
              {{ form.payment.currency }}
            </el-form-item>
            <el-form-item v-if="form.w9_status!==0" label="Form W-9">
              <div v-if="[1,3,4].includes(form.w9_status)">
                <div v-if="form.w9_status===3">Under signing...</div>
                <div v-if="form.w9_status===4">Signed</div>
                <div class="info">Experts have received ${{ calculate_advisor_paid_this_year }} this year.</div>
              </div>
              <div v-if="form.w9_status===2">
                <el-checkbox v-model="form.should_send_w9_form">Send "Form W-9"
                </el-checkbox>
                <div class="info">Expert's payment has over ${{ calculate_advisor_paid_this_year }} this
                  year,need to send w9 forms to experts to fill in tax
                  information.
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="form.enable_physical_check_payment_method">Enable Physical Check Payment Method
              </el-checkbox>
            </el-form-item>
          </div>
          <rate-board
            v-if="task"
            :task="task"
          />
        </div>
      </el-card>

      <slot v-if="!loading">
        <!-- is_cn / Outsource导入专家在美国项目complete 不用发送 payment information confirm -->
        <consultant-mail
          v-if="!isImportedFromCNDB && !isImportedFromOutsource"
          ref="consultant-mail1"
          :form="form"
          :task="task"
          :portal="advisor_portal1"
          :type="1" />

        <consultant-mail
          v-if="!isImportedFromOutsource"
          ref="consultant-mail2"
          :form="form"
          :task="task"
          :portal="advisor_portal2"
          :type="2" />

        <!-- <client-mail ref="client-mail" :form="form" :task="task" :portal="contact_portal" :contract="bound_contract" /> -->
      </slot>
    </el-form>
    <el-button type="success" :loading="submit_loading" :disabled="checkDisabled" @click="submit">Submit</el-button>
    <el-button type="text" @click="actionGotoDetail">Cancel</el-button>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import API from '@/api/project'
import moment from 'moment-timezone'
import ConsultantMail from './components/ConsultantMail'
import PostCallRule from '@/components/ComplianceWarning/Index'
import RateBoard from '@/views/project/consultation/detail/task-complete/components/RateBoard'
import { RouteTools } from '@/utils/tool'

export default {
  name: 'PatientsTaskComplete',
  components: { ConsultantMail, PostCallRule, RateBoard },
  data() {
    const validateDate = (rule, value, callback) => {
      const obj = this.form.time_info
      for (const key in obj) {
        if (!obj[key]) {
          return
        }
      }
      const t1 = moment(`${obj.end_date} ${obj.end_time}`)
      const t2 = moment(`${obj.start_date} ${obj.start_time}`)
      if (t1.diff(t2) < 0) {
        callback(new Error('End Time cannot be earlier than Start Time.'))
      } else {
        callback()
      }
    }
    return {
      loading: true,
      contract_loading: false,
      submit_loading: false,
      isRateChanged: false,
      calculate_advisor_paid_this_year: undefined,
      advisor_min_minutes: 0,
      advisor_min_amount: 0,
      form: {
        time_info: {
          start_date: '',
          start_time: '',
          end_date: '',
          end_time: '',
        },
        client_duration: undefined,
        lead_uid: undefined,
        support_uid: undefined,
        members_list: [],
        w9_status: 0,
        should_send_w9_form: false,
        should_network_loyalty_fee: false,
        enable_physical_check_payment_method: false,
        payment: {
          minutes: 0,
          amount: 0,
          currency: '',
          rate: 0,
          network_loyalty_fee: 0,
        },
      },
      advisor_portal1: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      advisor_portal2: {
        template: null,
        is_send: true,
        task_ca: {
          inquiry: null,
          inquiry_branch: null,
        },
        email: {
          subject: '',
          content: '',
        },
      },
      contact_portal: {
        template: null,
        is_send: false,
        email: {
          subject: '',
          content: '',
        },
      },
      rules: {
        'client_duration': [{ required: true, trigger: 'change', message: 'Client Duration is required' }],
        'lead_uid': [{ required: true, trigger: 'change', message: 'Lead is required' }],
        'support_uid': [{ required: true, trigger: 'change', message: 'Support is required' }],
        'time_info.start_date': [{ required: true, trigger: 'change' }],
        'time_info.start_time': [{ required: true, trigger: 'change' }],
        'time_info.end_date': [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        'time_info.end_time': [{ required: true, trigger: 'change' }, { validator: validateDate, trigger: 'change' }],
        'payment.minutes': [{ required: true, trigger: 'change' }],
        'payment.amount': [{ required: true, trigger: 'change' }],
      },
      task: null,
      bound_contract: null,
      viewData: {},
      task_detail_extra: [
        'schedule',
        'advisor.location_id_path',
        'advisor.paid_usd_in_this_year_jit',
        // 'advisor.w9_form',
        'advisor.sourced_by',
        'advisor_record_consent',
        'has_billed_jit',
        'advisor.bank_account',
        'payment_topic.payments.items.form',
        'project.members.user',
        'lead',
        'client',
        'client_rate_usd',
        'default_client_rate_usd',
        'default_client_rate_usd_considering_unsigned_tc',
        'specified_client_rate_currency_after_multiple',
        'task_outsource_info',
      ].join(),
    }
  },
  computed: {
    ...mapState({
      client_id: state => state.project.data.client_id,
      timeZone: state => state.app.timeZone,
      isSGDB: state => state.app.isSGDB,
      userInfo: state => state.user.info,
    }),
    ...mapGetters([
      'advisor_options',
    ]),
    if_use_time() {
      if (this.form.time_info.start_date && this.form.time_info.start_time) {
        return moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time, this.timeZone)
          .toISOString()
      } else {
        return undefined
      }
    },
    isStartOverNow() {
      if (!this.form.time_info.start_date || !this.form.time_info.start_time) return false
      const start = moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time, this.timeZone)
      return moment().isBefore(start)
    },
    isEndOverNow() {
      if (!this.form.time_info.end_date || !this.form.time_info.end_time) return false
      const end = moment.tz(this.form.time_info.end_date + ' ' + this.form.time_info.end_time, this.timeZone)
      return moment().isBefore(end)
    },
    hasBilled() {
      return this.task && this.task.has_billed_jit
    },
    hasCA() {
      return !!this.advisor_portal2.task_ca.inquiry
    },
    isUSAdvisor() {
      return this.task && this.task.advisor && !this.task.advisor.is_cn
    },
    isImportedFromCNDB() {
      return this.task && this.task.advisor.is_cn
    },
    isImportedFromOutsource() {
      return this.task?.task_outsource_status === 'IMPORTED'
    },
    clientRequiredPostCallCA() {
      return this.task && this.task.client.require_post_call_ca
    },
    checkDisabled() {
      return (this.clientRequiredPostCallCA && !this.hasCA) || !this.bound_contract
    },
    sourceByWithinOneMonth() {
      let within_one_month = false
      const has_sourced = this.task && this.task.advisor && !!this.task.advisor.sourced_by_id
      if (has_sourced) {
        const task_start_time = moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
          this.timeZone).utc()
        const diff = task_start_time.diff(moment(this.task.advisor.sourced_at), 'days')
        within_one_month = diff >= 0 && diff <= 30
      }
      return within_one_month
    },
    // 专家签署TC后30天之内，support默认为tc的发送者
    // 新加坡DB不做限制
    // 有改source_by权限的，也可以改support，即使在30天之内
    supportDisabled() {
      const permissionAllowModifyExpertSourceBy = this.userInfo.roles.find(
        item => item.role.name === 'AllowModifyExpertSourceBy')
      return this.sourceByWithinOneMonth && !this.isSGDB && !permissionAllowModifyExpertSourceBy
    },
    hasTimeMinimumCharge() {
      return this.task && this.task.advisor?.time_minimum !== 'NO_MINIMUM'
    },
    timeMinimumFormat() {
      if (!this.task) return {}
      const time_minimum = this.task.advisor.time_minimum
      const time_minimum_options = this.advisor_options.time_minimum_charge
      const minimum_minutes = this.$options.filters.getName(time_minimum, time_minimum_options, 'value', 'minutes')
      const minimum_hour = this.$options.filters.getName(time_minimum, time_minimum_options, 'value', 'hour')
      return { minimum_minutes, minimum_hour }
    },
  },
  watch: {
    'timeZone': {
      handler(newVal, oldVal) {
        const _this = this
        handleTimeChange('start_date', 'start_time')
        handleTimeChange('end_date', 'end_time')

        function handleTimeChange(date, time) {
          const time_info = _this.form.time_info
          if (time_info[date] && time_info[time]) {
            const new_time = moment.tz(time_info[date] + ' ' + time_info[time], oldVal).tz(newVal)
            time_info[date] = new_time.format('YYYY-MM-DD')
            time_info[time] = new_time.format('HH:mm')
          }
        }
      },
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      return this.getTaskDetail()
        .then(() => {
          this.advisor_portal2.is_send = !this.isImportedFromOutsource
          // Consultant Mail, Client Mail 在二次 Complete 时默认不勾选，初次 Complete 时勾选
          if (this.task['general_status'] !== 'COMPLETED' && !this.task['complete_time']) {
            // is_cn / Outsource导入的专家 不发payment info
            this.advisor_portal1.is_send = !this.isImportedFromCNDB && !this.isImportedFromOutsource
            // this.contact_portal.is_send = !!this.bound_contract
            this.contact_portal.is_send = !this.isImportedFromOutsource
          }
          // 如果complete过则取上次 completed的 client hour和 payment minutes/amount
          // 如果未complete过，则自己根据合同规则计算
          // 如果专家 is_one_hour_minimum:true,未completed=>amount= 按hours>=1 * 专家当前rate；amount输入框最小值当前专家的费率
          //                                 已经completed过=> 专家费率取上次的费率，amount输入框最小值上次的费率.提示费率变更，点击后以当前费率计算，amount最小值为当前费率
          /**
           * 上条注释中的is_one_hour_minimum已弃用，改为使用time_minimum判断，具体可见下方的checkTimeMinimumCharge方法
           */

          this.form.payment.rate = this.task.advisor.rate
          this.calculateHour()
        })
        .finally(() => { this.loading = false })
    },
    getTaskDetail() {
      const params = {
        project_id: this.$route.params.project_id,
        id: this.$route.query.id,
        params: {
          extra: this.task_detail_extra,
        },
      }
      return API.getTaskDetail(params).then(data => {
        this.task = data
        // 处理显示访谈安排的时间
        const start = data.start_time
        const end = data.end_time
        this.form.time_info = {
          start_date: moment(start).tz(this.timeZone).format('YYYY-MM-DD'),
          start_time: moment(start).tz(this.timeZone).format('HH:mm'),
          end_date: moment(end).tz(this.timeZone).format('YYYY-MM-DD'),
          end_time: moment(end).tz(this.timeZone).format('HH:mm'),
        }
        // 获取task开始时间后 校验合同
        return this.getBoundContract()
          .then(() => {
            return data
          })
      }).then(data => {
        // 处理 members_list 结束
        const hash = {}
        this.form.members_list = data.project.members.reduce((arr, next) => {
          if (!hash[next.uid]) {
            hash[next.uid] = true && arr.push(next)
          }
          return arr
        }, [])

        const user_exist = this.form.members_list.find(item => item.uid === this.userInfo.id)
        if (!user_exist) {
          const item = { uid: this.userInfo.id, user: { name: this.userInfo.name } }
          this.form.members_list.push(item)
        }

        if (this.sourceByWithinOneMonth) {
          const source_exist = this.form.members_list.find(item => item.uid === this.task.advisor.sourced_by_id)
          if (!source_exist) {
            const item = { uid: this.task.advisor.sourced_by_id, user: { name: this.task.advisor.sourced_by.name } }
            this.form.members_list.push(item)
          }
        }
        // 处理 members_list 结束

        if (data.general_status === 'COMPLETED') {
          // 获取RM RA
          this.form.lead_uid = data.lead_uid
          this.form.support_uid = data.support_uid
          this.form.client_duration = data.client_duration
        } else {
          this.form.support_uid = this.sourceByWithinOneMonth ? this.task.advisor.sourced_by_id : undefined
        }

        // 初次Complete时 如果不是中国专家 则自动勾上overseas
        if (data.general_status !== 'COMPLETED' && data.advisor.location_id_path &&
          !data.advisor.location_id_path.split(' ').includes('108')) {
          data.has_overseas = true
        }

        // Complete时 如果是美国专家 校验 W9 状态值:
        // 1:未达标 => 当年累计达到 $600
        // 2:达标 未发送W9
        // 3:达标 已发送 专家未签
        // 4:达标 专家已签 无需再发送（一旦签过，后续无需再签）
        if (data.advisor.location_id_path && data.advisor.location_id_path.split(' ').includes('174')) {
          this.calculate_advisor_paid_this_year = data.advisor.paid_usd_in_this_year_jit
          if (data.advisor.w9_form) {
            switch (data.advisor.w9_form.status) {
              case 'INIT':
                this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
                this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
                break
              case 'REQUESTED':
                this.form.w9_status = 3
                break
              case 'SIGNED':
                this.form.w9_status = 4
            }
          } else {
            this.form.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
            this.form.should_send_w9_form = data.advisor.paid_usd_in_this_year_jit > 600
          }
        }

        // 处理bank account
        this.form.payment.rate = data.advisor.rate
        this.form.payment.currency = data.advisor.rate_currency

        const advisor_bank_data = data.advisor.bank_account
        if (advisor_bank_data && advisor_bank_data.bank_account) {
          for (const key in this.form.payment) {
            if (advisor_bank_data.bank_account[key]) this.form.payment[key] = advisor_bank_data.bank_account[key]
          }
        }

        // 非初次complete时,去上次complete的值 初始化 false
        if (data.general_status === 'COMPLETED') {
          let disabled_pay_methods = data.advisor_normal_payment?.disabled_pay_methods
          if (data.payment_topic_id) {
            const recent_payment = data.payment_topic?.payments?.find(item => item.subtopic_name === 'DEFAULT')
            disabled_pay_methods = recent_payment?.items[recent_payment.items.length - 1]?.form?.disabled_pay_methods
          }

          if (!disabled_pay_methods?.includes('PHYSICAL_CHECK')) {
            this.form.enable_physical_check_payment_method = true
          }
        }
      })
    },
    getBoundContract(val = true) {
      if (!val) return

      this.contract_loading = true
      const params = { if_use_time: this.if_use_time }
      return API.getTaskBoundContract(this.$route.query.id, params).then(data => {
        this.bound_contract = data
      }).finally(() => {
        this.contract_loading = false
      })
    },

    /* 根据time_minimum字段，专家访谈不到1小时或30分钟，按1小时或30分钟算*/
    checkTimeMinimumCharge(hours) {
      const { minimum_minutes, minimum_hour } = this.timeMinimumFormat

      this.advisor_min_amount = +this.task.advisor.rate * minimum_hour
      this.advisor_min_minutes = minimum_minutes

      const start_time = this.form.time_info.start_time
      const end_time = this.form.time_info.end_time
      const minutes = moment(end_time, 'HH:mm').diff(moment(start_time, 'HH:mm'), 'minutes')

      if (minutes <= minimum_minutes && minutes > 0) {
        this.form.payment.minutes = minimum_minutes
      } else {
        this.form.payment.minutes = minutes
      }
    },

    calculateHour() {
      const start_time = this.form.time_info.start_time
      const end_time = this.form.time_info.end_time
      if (start_time && end_time) {
        const minutes = moment(end_time, 'HH:mm').diff(moment(start_time, 'HH:mm'), 'minutes')
        let hours

        if (this.bound_contract) {
          const term = this.bound_contract.client_hours_term
          const interval = term.intervals.find(item => minutes <= item.first)
          if (interval) {
            hours = interval.second
          } else {
            const last_interval = term.intervals[term.intervals.length - 1]
            hours = last_interval.second + (parseInt((minutes - last_interval.first) / term.step) +
              ((minutes - last_interval.first) % term.step ? 1 : 0)) * term.inc
          }
        } else {
          hours = (parseInt(minutes / 30) + (minutes % 30 ? 1 : 0)) * 0.5
        }

        if (this.hasTimeMinimumCharge) {
          this.checkTimeMinimumCharge(hours)
        } else {
          this.form.payment.minutes = minutes
          this.advisor_min_amount = 0
          this.advisor_min_minutes = 0
        }
        this.calculatePaymentAmount()
        this.calculateEndTime()
      }
    },
    calculateEndTime(val = this.form.client_duration) {
      this.form.time_info.end_time = moment(this.form.time_info.start_time, 'HH:mm').add(val, 'm').format('HH:mm')
      this.$refs['ruleForm'].validateField('client_duration')
    },
    calculatePaymentAmountByNewRate() {
      this.form.payment.rate = this.task.advisor.rate
      const { minimum_hour } = this.timeMinimumFormat
      this.advisor_min_amount = this.hasTimeMinimumCharge ? this.task.advisor.rate * minimum_hour : 0
      this.isRateChanged = false
      this.calculatePaymentAmount()
    },
    calculatePaymentAmount() {
      this.form.payment.amount = (this.form.payment.minutes / 60 * this.form.payment.rate).toFixed(2)
      this.form.payment.currency = this.task.advisor.rate_currency
      this.checkW9Status()
    },

    checkW9Status() {
      if (this.$refs['consultant-mail1']) this.$refs['consultant-mail1'].handleTemplateChange()
      if (this.$refs['consultant-mail2']) this.$refs['consultant-mail2'].handleTemplateChange()
      // 计算本次付款后是否达到 $600
      if (this.isUSAdvisor) {
        const sum = this.form.payment.amount + this.task.advisor.paid_usd_in_this_year_jit
        this.calculate_advisor_paid_this_year = sum
        if ([3, 4].includes(this.form.w9_status)) return

        if (sum > 600) {
          this.form.w9_status = 2
          this.form.should_send_w9_form = true
        } else {
          this.form.w9_status = 1
          this.form.should_send_w9_form = false
        }
      }
      if (this.form.payment.currency !== 'USD') {
        this.form.w9_status = 0
        this.form.should_send_w9_form = false
      }
    },

    handleStartDateChange(val) {
      this.form.time_info.end_date = val
      this.handleStartTimeChange(val)
    },
    handleStartTimeChange(val) {
      this.getBoundContract(val)
      this.calculateHour()
    },
    actionGotoDetail() {
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.push({
            name: 'ProjectDetail',
          })
        })
    },
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.submit_loading = true
          const rate = this.task.payment_topic_id
            ? this.task.payment_topic?.payments?.find(item => item.subtopic_name === 'DEFAULT')?.rate
            : this.task.advisor_normal_payment?.rate
          const data = {
            should_send_w9_form: this.form.should_send_w9_form,
            payment: {
              hours: (this.form.payment.minutes / 60).toFixed(4),
              minutes: this.form.payment.minutes,
              amount: this.form.payment.amount,
              currency: this.form.payment.currency,
              disabled_pay_methods: this.form.enable_physical_check_payment_method ? [] : ['PHYSICAL_CHECK'],
              rate: this.isRateChanged ? rate : this.form.payment.rate,
              network_loyalty_fee: this.form.should_network_loyalty_fee ? this.form.payment.network_loyalty_fee : undefined,
              use_legacy_payment_api: false,
            },
            advisor_portal1: this.advisor_portal1.is_send ? {
              portal: {
                type: 'ADVISOR_POST_CALL',
              },
              email: this.advisor_portal1.email,
              email_template_tags: this.advisor_portal1.template?.tags,
            } : undefined,

            advisor_portal2: this.advisor_portal2.is_send ? {
              portal: {
                type: 'ADVISOR_POST_CALL',
                task_ca: this.advisor_portal2.task_ca.inquiry ? {
                  inquiry_id: this.advisor_portal2.task_ca.inquiry.id,
                  inquiry_branch_id: this.advisor_portal2.task_ca.inquiry_branch.id,
                } : undefined,
              },
              email: this.advisor_portal2.email,
              email_template_tags: this.advisor_portal2.template.tags,
            } : undefined,
          }

          if (!this.hasBilled) {
            data.task = {
              start_time: moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
                this.timeZone)
                .toISOString(),
              end_time: moment.tz(this.form.time_info.end_date + ' ' + this.form.time_info.end_time, this.timeZone)
                .toISOString(),
              client_duration: this.form.client_duration,
              lead_uid: this.form.lead_uid,
              support_uid: this.form.support_uid,
            }
          }

          return API.completeTask(this.$route.query.id, data).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Complete Task successfully!',
              type: 'success',
            })
            this.actionGotoDetail()
          })
            .finally(() => {
              this.submit_loading = false
            })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.box-card {
  margin: 8px 0;
}

.time-picker {
  width: 165px;
}

::v-deep .el-checkbox__label {
  font-size: 12px;
}

::v-deep .tooltip-icon {
  font-size: 1.25em;
  margin-left: 0.5em;
  vertical-align: text-top;
}

::v-deep .client-hour-input {
  width: 150px;

  input {
    text-align: center;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 14px;
  }
}

::v-deep .el-collapse-item__header .el-checkbox__label {
  font-size: 15px;
}

::v-deep .el-collapse {
  border: 1px solid #EBEEF5;
  padding: 0 16px;
}

::v-deep .mce-tinymce {
  width: auto !important;
}
</style>
