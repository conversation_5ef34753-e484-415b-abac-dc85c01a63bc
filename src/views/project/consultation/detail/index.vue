<template>
  <div v-loading="isLoading">
    <el-empty v-if="permission_error_text" :description="permission_error_text" class="no-permission-container" />

    <div
      v-if="isProjectLoaded"
      class="app-container">
      <ProjectWebSocket v-if="enableWebsocket" />
      <!-- Warning Bar  -->
      <div
        v-if="!client_is_execute"
        class="client-status-warning flex align-items-center">
        <svg-icon
          sicon-class="warning"
          style="font-size: 26px; margin-right: 8px;" />
        <div>
          <div style="font-weight: bold;"> Scheduling and Invites are not available</div>
          <div style="margin-top: 2px;">
            The client's status is not “Execute”.
          </div>
        </div>
      </div>

      <el-alert
        v-if="requireChaperone"
        title="All calls for this project require a Chaperone unless otherwise instructed by Client Compliance."
        show-icon
        type="warning"
        class="mb-8"
      />

      <el-alert
        v-if="outsource_approval_is_not_approved"
        title="The latest outsource approval for this project has not yet been approved."
        show-icon
        type="error"
        class="mb-8"
      />

      <el-alert
        v-if="client_contract_expired_in_days >= 0"
        show-icon
        type="error"
        class="mb-8">
        <template #title>
          <router-link
            class="text-truncate link"
            :to="{ name: 'ClientContracts', params: { client_id: client_id }}">
            Contract
          </router-link>
          Expires in <b>{{ client_contract_expired_in_days }}</b> Days
        </template>
      </el-alert>

      <!-- Tool - Quick View -->
      <key-quick-view :project="projectInfo" />

      <!-- Project -->
      <el-row
        type="flex"
        justify="space-between"
        align="top"
        :gutter="10"
        class="flex-wrap">
        <el-col class="project-base-info width-auto">
          <div class="flex justify-content-between">
            <div class="project-title">
              {{ projectInfo.name }}
              <el-tag type="info"># {{ projectInfo.id }}</el-tag>
              <el-tag :type="projectInfo.status | getProjectStausClass(project_options.status)">
                {{ projectInfo.status |getLabel(project_options.status) }}
              </el-tag>
              <el-tag
                v-if="projectData.call_opportunity_sum_jit"
                type="success">
                Call Opportunity #: {{ projectData.call_opportunity_sum_jit }}
              </el-tag>
              <el-tag v-if="needProjectCode" type="danger">Project Code Required</el-tag>
            </div>

            <div class="flex align-items-center">
              <to-bcg-template
                v-if="projectInfo.bcg_hub_project"
                class="mr-8"
              />

              <div v-if="!isSurveyProject">
                <el-dropdown
                  v-if="projectInfo.google_sheet_url || projectInfo.client_google_sheet_url"
                  trigger="click"
                  class="mr-8"
                  @command="openGoogleSheetLink"
                >
                  <el-button type="primary">
                    View Google Sheets <i class="el-icon-arrow-down el-icon--right" />
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-if="projectInfo.google_sheet_url"
                      :command="projectInfo.google_sheet_url"
                    >
                      <i class="el-icon-link" />Internal
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="projectInfo.client_google_sheet_url"
                      :command="projectInfo.client_google_sheet_url"
                    >
                      <i class="el-icon-link" />External
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <el-dropdown
                v-if="!isSurveyProject && allowCopyCompliancePortalLink && clientComplianceID"
                trigger="click"
                class="mr-8"
                @command="getCompliancePortalLink">
                <el-button type="success">
                  View Compliance Portal <i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="true">Internal</el-dropdown-item>
                  <el-dropdown-item :command="false">
                    Compliance View
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-dropdown
                v-if="!isSurveyProject"
                trigger="click"
                class="mr-8"
                @command="getClientViewLink">
                <el-button type="primary">
                  View Client Portal <i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="true">Internal</el-dropdown-item>
                  <el-dropdown-item :command="false">Client View</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <clone-project />
            </div>
          </div>

          <div v-if="projectInfo.tags.length > 0" class="project-tags">
            <svg-icon icon-class="tag" />&nbsp;
            <span v-for="(item, index) in projectInfo.tags" :key="index">
              {{ item.tag.content }}<span v-if="index !== projectInfo.tags.length - 1">, </span>
            </span>
          </div>
        </el-col>
        <el-col class="flex width-auto">
          <project-alert v-if="!client_is_approved || latestOutsourceApprovalMessage">
            <div v-if="!client_is_approved">
              This client is not compliance approved. Advisors sent to clients may not proceed with consulting until all
              rules
              are approved.
            </div>
            <div v-if="latestOutsourceApprovalMessage">
              {{ latestOutsourceApprovalMessage }}
            </div>
          </project-alert>
          <client-contract-usage :client-id="client_id" />
          <tips v-if="!isSurveyProject" />
        </el-col>
      </el-row>
      <el-tabs v-if="isTabsRoute" v-model="active_tab_label" type="card" class="mt-3" @tab-click="tabClick">
        <template v-for="item in tabs">
          <el-tab-pane
            v-if="item.exclude_sub_type !== projectInfo.sub_type && (item.allowClientAMDashboard ? allowClientAMDashboard : true)"
            :key="item.router_name"
            :name="item.router_name"
            lazy
          >
            <template v-slot:label>
              <span v-if="hasNewCompliance(item)" value="!"> {{ item.label }}</span>
              <el-badge v-else>{{ item.label }}</el-badge>
              <UnreadBadge v-if="clientTaskTab(item) && clientTaskUnreadCount" :count="clientTaskUnreadCount" />
              <UnreadBadge v-if="item.label==='Leads' && leadsUnreadCount" :count="leadsUnreadCount" />
            </template>
          </el-tab-pane>
        </template>
      </el-tabs>

      <router-view :project-info="projectInfo" />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { copyUtil, RouteTools } from '@/utils/tool'
import InquiryAPI from '@/api/inquiry'
import ProjectAPI from '@/api/project'
import UserAPI from '@/api/user'
import ToolAPI from '@/api/tool'
import KeyQuickView from '@/views/project/consultation/detail/components/KeyQuickView/index'
import Tips from '@/views/project/consultation/detail/components/KeyQuickView/Tips'
import ClientContractUsage from '@/views/project/consultation/detail/components/ClientAlerts/ContractUsage'
import ProjectAlert from '@/views/project/consultation/detail/components/ClientAlerts/ProjectAlert'
import CloneProject from '@/views/project/consultation/detail/components/CloneProject'
import ToBcgTemplate from '@/views/project/consultation/detail/components/ToBcgTemplate'
import moment from 'moment/moment'
import ProjectWebSocket from '@/components/WebSocket/history'
import UnreadBadge from '@/views/project/consultation/detail/components/UnreadBadge'

export default {
  name: 'ProjectDetail',
  components: {
    Tips,
    KeyQuickView,
    ClientContractUsage,
    ProjectAlert,
    CloneProject,
    ToBcgTemplate,
    ProjectWebSocket,
    UnreadBadge,
  },
  data() {
    return {
      isLoading: true,
      permission_error_text: null,
      project_id: this.$route.params.project_id,
      client_id: null,
      client_is_execute: true,
      client_is_approved: true,
      client_contract_expired_in_days: -1,
      need_code_types: [],
      projectInfo: undefined,
      active_tab_label: undefined,
      need_read_client_legal_rules: false,
      outsource_approval_is_not_approved: false,
      task_type_for_screened: 'normal',
      clientComplianceID: undefined,
      tabs: [
        { label: 'Project Info', router_name: 'ProjectInformation' },
        { label: 'Client Tasks', router_name: 'ProjectClientTasks' },
        { label: 'Screened Experts', router_name: 'ProjectScreenedExperts', exclude_sub_type: 'Survey' },
        { label: 'Leads', router_name: 'ProjectLeads' },
        { label: 'Screening Questions', router_name: 'ProjectScreeningQuestion', exclude_sub_type: 'Survey' },
        { label: 'Outreach', router_name: 'ProjectOutreach' },
        { label: 'Compliance', router_name: 'ProjectCompliance' },
        { label: 'Scheduled Calls', router_name: 'ProjectScheduledCalls', exclude_sub_type: 'Survey' },
        { label: 'P&L', router_name: 'PL' },
        {
          label: 'Recommendations',
          router_name: 'ProjectRecommendations',
          exclude_sub_type: 'Survey',
          allowClientAMDashboard: true,
        },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'uid',
      'roles',
    ]),

    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
      projectData: state => state.project.data,
      userInfo: state => state.user.info,
      unreadEvents: state => state.project.unread_subscription_events,
    }),

    hideTasks() {
      return (
        this.$route.name === 'TasksArrange' ||
        this.$route.name === 'TaskComplete' ||
        this.$route.name === 'SurveyTaskComplete' ||
        this.$route.name === 'PatientsTaskComplete'
      )
    },

    isProjectLoaded() {
      return this.projectInfo && Object.prototype.hasOwnProperty.call(this.projectInfo, 'id') && !this.permission_error_text
    },
    currentRouterLabel() {
      return this.$route.name
    },
    isTabsRoute() {
      return ![
        'TasksArrange',
        'TasksVettingCall',
        'TaskComplete',
        'SurveyTaskComplete',
        'PatientsTaskComplete'].includes(this.$route.name)
    },

    needProjectCode() {
      return this.need_code_types.includes(this.projectData.sub_type) && !this.projectData.code
    },
    isSurveyProject() {
      return this.projectInfo?.sub_type === 'Survey'
    },
    allowCopyCompliancePortalLink() {
      return this.roles.some(role => role.role?.name === 'CopyCompliancePortalLink')
    },
    allowClientAMDashboard() {
      return this.roles.some(role => role.role?.name === 'AllowClientAMDashboard')
    },
    latestOutsourceApprovalMessage() {
      const outsource_approvals = this.projectInfo.outsource_approvals
      if (!outsource_approvals?.length) return ''

      let from_region = ''
      if (this.projectInfo.exported_to === 'CN') from_region = 'CN'
      if (this.projectInfo.exported_to === 'US') from_region = 'SEA'
      return from_region ? outsource_approvals.find(item => item.is_latest_approval)
        ?.events.sort((a, b) => b.id - a.id).find(item => item.from_region === from_region)?.message : ''
    },
    requireChaperone() {
      return this.projectInfo?.client?.compliance_preference?.require_chaperone
    },

    enableWebsocket() {
      const user_enable_notification = this.userInfo.preference?.enable_in_app_notifications
      return user_enable_notification && !this.isSurveyProject && !this.hideTasks
    },

    clientTaskUnreadCount() {
      return this.enableWebsocket ? this.unreadEvents?.client_task_ids.length : 0
    },

    leadsUnreadCount() {
      return this.enableWebsocket ? this.unreadEvents?.leads_task_ids.length : 0
    },
  },
  watch: {
    $route(to) {
      this.setActiveTab(to.name)
    },

    'client_id': {
      handler(newVal) {
        if (newVal === this.McKinseyId) {
          this.task_type_for_screened = 'MCK'
          this.tabs.splice(1, 1, { label: 'Client Tasks McK', router_name: 'ProjectClientTasksMcK' })
        }
      }, immediate: true,
    },
    'projectInfo.sub_type': {
      handler(newVal) {
        if (newVal === 'Investor Call') {
          this.task_type_for_screened = 'SST'
          this.tabs.splice(1, 1, { label: 'Client Tasks SST', router_name: 'ProjectClientTasksSST' })
        }
      }, immediate: true,
    },
    'projectInfo.exported_to': {
      handler(newVal) {
        if (newVal === 'CN' && !this.tabs.find(item => item.label === 'CA')) {
          this.tabs.push({ label: 'CA', router_name: 'CAForProject' })
        }
      },
    },
  },
  created() {
    this.loadData()
  },
  activated() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.isLoading = true
      this.getUserTaskTableColsWidth()

      this.$store.dispatch('project/getInfo', this.project_id)
        .then(async (data) => {
          await this.checkForGKMRole(data)
          this.client_id = data.client_id

          const client = data.client

          if (client) {
            this.client_is_execute = client.status === 'EXECUTE'
            this.client_is_approved = client.compliance_status === 'APPROVED'

            if (client.compliance_preference?.rule?.compliance_rules?.project_creation_rules?.remind_user_to_add_a_project_case_code) {
              this.need_code_types = client.compliance_preference?.rule?.compliance_rules?.project_creation_rules?.remind_user_to_add_a_project_case_code_project_types
            }
            if (client.contracts?.length) {
              client.contracts.filter(item => item.effective_status === 'EFFECTIVE')
                .forEach(item => {
                  const daysDifference = moment(item.end_time).diff(moment(), 'day')
                  this.client_contract_expired_in_days = daysDifference <= 30 ? daysDifference : -1
                })
            }

            this.clientComplianceID = client.client_contacts.find(
              item => item.types.some(type => ['LEGAL', 'COMPLIANCE'].includes(type)))?.id
          }

          return await this.getClientCAData(data)
        })
        .then(data => {
          this.projectInfo = data
          this.need_read_client_legal_rules = data.members.find(
            item => item.uid === this.uid && !item.has_read_client_legal_rules)

          if (data.outsource_approvals?.length) {
            const approval = data.outsource_approvals.find(item => item.is_latest_approval)
            this.outsource_approval_is_not_approved = approval.local_approval !== 'APPROVED' ||
              approval.outsource_approval !== 'APPROVED'
          }
          document.title = data.name
          return RouteTools.setRouterMetaTitle(data.name, this.$route)
        })
        .then(this.getBcgApprovedTemplate)
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.isLoading = false
          this.setActiveTab(this.currentRouterLabel)
        })
    },

    hasNewCompliance(item) {
      return item.label === 'Compliance' && this.need_read_client_legal_rules
    },
    clientTaskTab(item) {
      return ['ProjectClientTasksMcK', 'ProjectClientTasksSST', 'ProjectClientTasks'].includes(item.router_name)
    },
    getClientCAData(project) {
      const params = {
        type: 'PRE_CALL_CA',
        client_id: project.client_id,
      }
      return InquiryAPI
        .getInquiryList(params)
        .then(data => {
          project.ca_list = data['list']
          return project
        })
    },
    tabClick(tab) {
      const compliance_router = tab.name === 'ProjectCompliance'
      if (compliance_router && this.need_read_client_legal_rules) {
        ProjectAPI.readProjectComplianceRules(this.project_id)
          .then(() => {
            this.need_read_client_legal_rules = false
          })
      }

      const filteredTasks = ['ProjectScreenedExperts'].includes(tab.name)

      this.$router.push({
        name: tab.name,
        query: compliance_router ? { client_id: this.client_id } : undefined,
        params: filteredTasks ? { type: this.task_type_for_screened } : undefined,
      })
      document.title = this.projectInfo.name
    },
    setActiveTab(route_name) {
      if (route_name === 'ProjectClientTasks') {
        if (this.projectInfo?.sub_type === 'Investor Call') {
          this.active_tab_label = 'ProjectClientTasksSST'
        } else if (this.client_id === this.McKinseyId) {
          this.active_tab_label = 'ProjectClientTasksMcK'
        } else {
          this.active_tab_label = route_name
        }
      } else {
        this.active_tab_label = route_name
      }
    },
    openGoogleSheetLink(href) {
      window.open(href, '_blank')
    },
    getClientViewLink(is_internal = true) {
      const params = {
        project_id: this.project_id,
        is_internal,
        trigger_recommend_event: false,
      }
      return ProjectAPI.getClientPortalLink(params)
        .then(async data => {
          await copyUtil(data.link_jit)
          this.$message({
            message: `Copy ${is_internal ? 'internal' : 'client view'} link successful`,
            type: 'success',
          })
        })
    },

    getCompliancePortalLink(is_internal = false) {
      const params = {
        client_id: this.client_id,
        is_internal,
        client_contact_id: !is_internal ? this.clientComplianceID : undefined,
      }
      return ProjectAPI.getCompliancePortalLink(params)
        .then(async data => {
          await copyUtil(data.link)
          this.$message({
            message: `Copy ${is_internal ? 'internal' : 'compliance portal'} link successful`,
            type: 'success',
          })
        })
    },

    getUserTaskTableColsWidth() {
      UserAPI.getUserRemoteStorage(`project_task_cols_width_${this.project_id}`)
        .then(data => {
          this.$store.dispatch('project/setTaskColsWidth', data['value'])
        })
    },

    getBcgApprovedTemplate() {
      if (this.projectInfo.bcg_hub_project) {
        const params = {
          project_id: this.projectInfo.id,
          status: 'APPROVED',
          extra: 'original_template',
          sort: 'create_at desc',
        }
        return ToolAPI.getToBcgTemplates(params)
          .then(data => {
            if (data.list.length) {
              this.$store.dispatch('project/updateInfo', { ...this.projectInfo, approved_bcg_templates: data.list })
            }
          })
      }
    },

    // GKM 禁止查看和自己无关的项目
    checkForGKMRole(project) {
      const isGKM = this.roles.some(role => role.role?.name === 'GKM')
      const is_project_member = project.members.some(item => item.uid === this.uid)
      if (isGKM && !is_project_member) {
        this.permission_error_text = 'You have no permission for this project.'
        return false
      } else {
        this.permission_error_text = ''
        return true
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.project-base-info {
  /*margin-bottom: 0.5em;*/
  flex-grow: 1;
  overflow: auto;

  .project-title {
    font-size: 1.6em;
    font-weight: bold;
  }

  .project-status,
  .project-tags {
    font-size: 12px;
    margin: .5em 0;
  }

  /*.project-status {*/
  /*  color: #999;*/
  /*}*/

  .project-tags {
    color: #1458d4;
  }
}

.client-status-warning {
  color: #df3c3c;
  background-color: #fafafa;
  font-size: 12px;
  padding: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
}

::v-deep .el-badge__content.is-fixed {
  top: 10px;
  right: 1px;
}

.no-permission-container{
  min-height: calc(100vh - 66px);
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
