<template>
  <div class="inline-block">
    <ico-button
      tooltip="Preview Screening"
      no-border
      @action="showDialog">
      <el-icon slot="ico" class="el-icon-document" />
    </ico-button>

    <questionnaire-show
      :inquiry-id="project.sq_id"
      :branch-id="showInquiryBranchId"
      @close="hideDialog" />

  </div>
</template>

<script>
import IcoButton from '@/components/IcoButton'
import QuestionnaireShow from '@/components/Question/QuestionnaireShowV2'

export default {
  name: 'TaskQuestionnaireShow',
  components: {
    IcoButton,
    QuestionnaireShow,
  },
  props: {
    angle: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      project: this.$store.state.project.data,
      showInquiryBranchId: null,
    }
  },
  methods: {
    showDialog() {
      this.showInquiryBranchId = this.angle.inquiry_branch_id
    },
    hideDialog() {
      this.showInquiryBranchId = null
    },
  },
}
</script>
