import OutreachToAdvisor from '@/views/project/consultation/detail/components/SendEmail/OutreachToAdvisor'
import { debounce } from '@/utils/tool'
import { mapState } from 'vuex'
import ProjectAPI from '@/api/project'
import moment from 'moment-timezone'
import EditTaskAngle from '@/views/project/consultation/detail/client-tasks/components/EditTaskAngle'
import ShowAngleTopic from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/ShowAngleTopic.vue'
import QuestionnaireShow from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/PreviewScreening'
import GenerationImTaskCustom from '@/components/ClientCustomFields/GenerationImCustom/TaskExpertCode'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import AddToProject from '@/views/consultant/components/addToProject'
import Pagination from '@/components/Pagination'
import HonorariumEdit from '../../../components/TaskHonorariumEdit'
import ClientRate from './components/ClientRateEdit'
import ClientPortalPublish from './components/ClientPortalPublish'
import AdvisorProfileTips from '@/views/project/consultation/detail/components/AdvisorProfileTips'
import WorkflowLink from '@/views/project/consultation/detail/components/WorkflowLink'
import USGovernmentMark from '@/views/project/consultation/detail/components/USGovernmentMark'
import RecentOutreachStatus from '@/components/RecentEmailStatus/RecentOutreachStatus'
import CNAdvisorCAOverride from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/CNAdvisorCAOverride'
import NewAdvisorTag from '@/components/NewAdvisorTag'
import ClientPortalSelected
  from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/ClientPortalSelected'

export default {
  components: {
    OutreachToAdvisor,
    EditTaskAngle,
    ShowAngleTopic,
    QuestionnaireShow,
    GenerationImTaskCustom,
    RouterLinkAdvisor,
    AddToProject,
    Pagination,
    HonorariumEdit,
    ClientRate,
    ClientPortalPublish,
    AdvisorProfileTips,
    WorkflowLink,
    USGovernmentMark,
    RecentOutreachStatus,
    CNAdvisorCAOverride,
    NewAdvisorTag,
    ClientPortalSelected,
  },

  data() {
    return {
      medianRateLoading: false,
      customPageSize: [10, 20, 50, 100, 200, 300],
      extra_for_survey: [
        'email_records.advisor_tracking',
        'advisor.email_address_score',
      ],
    }
  },
  computed: {
    ...mapState({
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
      TengYueId: state => state.app.TengYueId,
      userInfo: state => state.user.info,
      isSGDB: state => state.app.isSGDB,
    }),

    isGeneration() {
      return [this.GenerationIMId, this.GenerationGrowthEquityId].includes(this.project.client_id)
    },

    isSurveyProject() {
      return this.project.sub_type === 'Survey'
    },

    allSelectedAdvisor() {
      return this.$store.state.tasks.selectionList
    },

    showReferenceNumber() {
      return !this.isSGDB && this.project?.client_id === this.TengYueId && this.project.sub_type === 'Consultation'
    },
  },

  created() {
    this.init()
  },

  beforeDestroy() {
    this.lazy && this.removeLazyLoadListener()
  },

  mounted() {
    if (this.lazy) {
      this.addLazyLoadListener()
    } else {
      this.propsFroList()
        .then(() => {
          this.$nextTick(() => {
            this.loadSortable()
          })
        })
    }
  },

  methods: {
    init() {
      const query = this.$route.query
      if (+query.angle_id === this.angle.id && query.task_id) {
        this.searchQuery.id = +query.task_id
      }

      this.showAllColumns = !this.isSurveyProject

      const { preference } = this.userInfo
      if (!preference) return
      if (preference.enable_client_task_page_pagination) {
        this.searchQuery.size = preference.client_task_page_default_size
      } else {
        this.searchQuery.size = 9999
        this.customPageSize = []
      }
    },

    getTaskOutreachNum(email_records) {
      return email_records.filter(item => item.email_content_type === 'PROJECT_OUTREACH').length
    },

    propsFroList() {
      return new Promise((resolve, reject) => {
        this.tableData.data = this.angle.tasks.sort((a, b) => (b.rank - a.rank)) || []
        this.tableData.count = this.angle.tasks ? this.angle.tasks.length : 0

        resolve()
      })
    },

    getExpertRate(row) {
      if (typeof row.rate === 'number') return row.rate + '/' + row.rate_currency
      if (typeof row.advisor.rate === 'number') return row.advisor.rate + '/' + row.advisor.rate_currency
      return ''
    },

    downloadAngleTask() {
      this.$emit('downloadAngleTask')
    },

    outreachToAdvisor() {
      this.$refs.outreachToAdvisor.handleCommand(this.angle.related_email_template)
    },

    gotoAddAdvisor() {
      const query = {
        project_id: this.project.id,
        client_id: this.project.client_id,
        type: 'ADVISOR',
        origin: this.$route.name,
        origin_id: this.angle.id,
      }

      this.$router.push({
        name: 'AddConsultant',
        query,
      })
    },

    completeDisabled(row) {
      // survey 类型项目可以直接complete；其他类型项目 客户要求project_code但是没有设置值||客户不允许||task状态 'SCHEDULED'，不可以complete
      if (this.isSurveyProject) {
        return false
      } else {
        const client_require_project_code = row.client.require_project_code
        const project_no_code = this.project.code.length < 1
        const client_disallow_complete = row.client.disallow_complete_task
        const task_status_scheduled = row.general_status === 'SCHEDULED'
        return (client_require_project_code && project_no_code) || client_disallow_complete || task_status_scheduled
      }
    },

    async actionRemoveAdvisor(row, index) {
      try {
        await this.$confirm(`Confirm to Remove ${row.advisor.full_name}?`, 'Notice', { type: 'warning' })
        const params = [row.id]
        await ProjectAPI.deleteTask(params)
        this.tableData.data.splice(index, 1)
        this.$message({
          message: 'Remove successful',
          type: 'success',
        })
      } catch (e) {
        return e
      }
    },

    deleteAngle() {
      this.$emit('delete', this.angle.id, this.sequence - 2)
    },

    checkSourced(row) {
      const permissionAllowModifyExpertSourceBy = this.userInfo.roles.find(
        item => item.role.name === 'AllowModifyExpertSourceBy')
      let isAfterOneMonth = false
      const has_completed = row.general_status === 'COMPLETED'
      if (has_completed && row.advisor.sourced_by_id > 0) {
        const last_month = moment(row.consult_date).subtract(1, 'months').utc()
        isAfterOneMonth = moment(row.advisor.sourced_at).isAfter(last_month)
      }
      return isAfterOneMonth && !permissionAllowModifyExpertSourceBy && !this.isSurveyProject
    },

    handleLazyLoad() {
      if (this.isInContainer(this.$el, this.scrollContainer) && !this.hideOnPage) {
        this.removeLazyLoadListener()
        if (this.tableData.data.length) return
        this.getList()
          .then(() => {
            this.$nextTick(() => {
              this.loadSortable()
            })
          })
      }
    },

    debounceSearch() {
      debounce(() => {
        this.$emit('clearSearchByTaskId')
        this.getList()
      }, 500)
    },
    sortData(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        if (val.prop === 'outreach_opened') {
          const outreach_sort = 'outreach_opened desc,outreach_opened_at desc,outreach_tracking_status,outreach_tracking_update_at desc'
          this.searchQuery.sort = val.order === 'descending' ? outreach_sort : undefined
        } else {
          this.searchQuery.sort = val.order ? val.prop + order : this.orderBy
        }
        this.getList()
      }
    },

    addLazyLoadListener() {
      this.scrollContainer = window
      this.scrollContainer.addEventListener('scroll', this.handleLazyLoad)
      this.handleLazyLoad()
    },
    removeLazyLoadListener() {
      if (!this.scrollContainer) return
      this.scrollContainer.removeEventListener('scroll', this.handleLazyLoad)
      this.scrollContainer = null
    },
    isInContainer(el, container) {
      const elRect = el.getBoundingClientRect()
      const {
        innerHeight,
        innerWidth,
      } = container

      return elRect.top >= 0 &&
        elRect.left >= 0 &&
        elRect.bottom <= innerHeight + 150 &&
        elRect.right <= innerWidth
    },

    tableRowClassName({ row, rowIndex }) {
      row.table_index = rowIndex
      if (row.client_portal_status === 'APPROVED') {
        return 'success-row'
      }
      return ''
    },

    async updateMedianRate() {
      try {
        this.medianRateLoading = true
        await ProjectAPI.updateAngleMedianRate(this.angle.id)
        this.$emit('updateMedianRate')
      } finally {
        this.medianRateLoading = false
      }
    },
  },
}
