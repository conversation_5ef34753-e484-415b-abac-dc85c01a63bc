<template>
  <div v-if="task.task_outsource_info" class="flex align-items-center justify-content-between">
    <el-tooltip placement="top" effect="light">
      <slot v-if="task.task_outsource_status === 'READY_TO_EXPORT'">
        <div slot="content">Ready To Export</div>
        <span class="warning">
          <i class="el-icon-s-promotion" /> Ready
        </span>
      </slot>
      <slot v-if="task.task_outsource_status === 'IMPORTED'">
        <div slot="content">Imported From {{ task.task_outsource_info.outsource_advisor_from }}</div>
        <span class="primary">
          <svg-icon icon-class="import" /> Imported
        </span>
      </slot>
      <slot v-if="task.task_outsource_status === 'EXPORTED'">
        <div slot="content">Exported To {{ task.task_outsource_info.outsource_advisor_to }}</div>
        <span class="success">
          <svg-icon icon-class="export" /> Exported
        </span>
      </slot>
    </el-tooltip>
    <div>
      <el-tooltip placement="top" effect="light">
        <div slot="content">
          <div class="mb-5px"><b>Compliance Status</b></div>
          <div :class="complianceStatusTagType.type">{{ complianceStatusTagType.label }}</div>
        </div>
        <el-tag
          size="mini"
          :type="complianceStatusTagType.type"
        >
          C
        </el-tag>
      </el-tooltip>
      <el-tooltip placement="top" effect="light">
        <div slot="content">
          <div class="mb-5px"><b>Arrangement Status</b></div>
          <div :class="arrangementStatusTagType.type">{{ arrangementStatusTagType.label }}</div>
          <div v-if="arrangementStatusTagType.value === 'ARRANGED'" class="mt-8">
            <div>
              {{ task.task_outsource_info.task_outsource_arrangement.start | momentFormat('MM/DD/YYYY hh:mm A') }} -
              {{ task.task_outsource_info.task_outsource_arrangement.end | momentFormat('hh:mm A') }}
            </div>
            <div v-if="task.task_outsource_info.communication_tool" class="mt-8">
              {{ task.task_outsource_info.communication_tool }}
            </div>
          </div>
        </div>
        <el-tag
          size="mini"
          :type="arrangementStatusTagType.type"
        >
          A
        </el-tag>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OutsourceStatusBtn',
  props: {
    task: Object,
  },
  data() {
    return {
      status_options: {
        compliance: [
          { value: 'INITIAL', label: 'Initial', type: 'info' },
          { value: 'SENT', label: 'Sent', type: 'primary' },
          { value: 'APPROVED', label: 'Approved', type: 'success' },
          { value: 'REJECTED', label: 'Rejected', type: 'danger' },
          { value: 'HOLD', label: 'Hold', type: 'primary' },
          { value: 'REQUEST_MORE_INFO', label: 'Request More Info', type: 'primary' },
        ],
        arrange: [
          { value: 'INITIAL', label: 'Initial', type: 'info' },
          { value: 'SELECTED', label: 'Selected', type: 'primary' },
          { value: 'ARRANGED', label: 'Arranged', type: 'success' },
        ],
      },
    }
  },
  computed: {
    complianceStatusTagType() {
      const status = this.task?.task_outsource_info?.task_outsource_compliance_status
      return this.status_options.compliance.find(item => item.value === status)
    },
    arrangementStatusTagType() {
      const status = this.task?.task_outsource_info?.task_outsource_general_status
      return this.status_options.arrange.find(item => item.value === status)
    },
  },
  mounted() {
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
</style>
