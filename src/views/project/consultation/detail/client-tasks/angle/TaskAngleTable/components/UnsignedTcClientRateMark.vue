<template>
  <div v-if="showMark" class="inline-block">
    <el-tooltip
      effect="dark"
      content="The expert has not yet signed their T&C so the Client Rate is subject to change."
      placement="top">
      <img class="mark-img" src="@/icons/yellow-warning.png">
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'UnsignedTcClientRateMark',
  props: {
    task: Object,
  },

  computed: {
    showMark() {
      return !this.task.default_client_rate_usd && this.task.default_client_rate_usd_considering_unsigned_tc
    },
  },
}
</script>

<style scoped>
.mark-img {
  width: 10px;
  height: 10px;
}
</style>
