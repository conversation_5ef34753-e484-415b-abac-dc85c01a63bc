<template>
  <div>
    <el-popover
      v-model="popoverVisible"
      placement="top"
      trigger="click"
      width="300">
      <div v-if="visible" class="mt-8">
        <el-form
          ref="form"
          label-width="70px"
          inline
        >
          <el-form-item
            label="Selected">
            <el-radio-group v-model="client_portal_select">
              <el-radio v-if="task.general_status === 'RECOMMENDED'" label="INITIAL">--</el-radio>
              <el-radio label="SELECTED">Yes</el-radio>
              <el-radio label="NOT_INTERESTED">No</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="client_portal_select==='NOT_INTERESTED'"
            label="Reason"
          >
            <el-select
              v-model="not_interested_reason"
              placeholder="Reason"
            >
              <el-option v-for="item in reason_list" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="not_interested_reason==='Other'"
            label="Note"
          >
            <el-input
              v-model="not_interested_reason_note"
              placeholder="Note" />
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button type="text" size="mini" class="ml-3" @click="updateClientPortalSelected">Save</el-button>
          <el-button type="text" size="mini" class="info" @click="cancelEdit">Cancel</el-button>
        </div>
      </div>
      <div slot="reference">
        <el-tooltip class="item" :disabled="tooltipDisabled" effect="light" placement="top-start" :open-delay="500">
          <div slot="content">

            <el-table
              v-if="task.client_portal_status==='NOT_INTERESTED'"
              border
              stripe
              :data="client_decline"
            >
              <el-table-column label="Reason" width="200">
                <template slot-scope="scope">
                  {{ scope.row.reason }}
                </template>
              </el-table-column>
              <el-table-column label="Note">
                <template slot-scope="scope">
                  {{ scope.row.note }}
                </template>
              </el-table-column>
              <el-table-column label="User">
                <template slot-scope="scope">
                  <span v-if="scope.row.create_by_id">{{ scope.row.create_by.name }}</span>
                  <span v-else>Client</span>
                </template>
              </el-table-column>
              <el-table-column label="Time" width="150">
                <template slot-scope="scope">
                  {{ scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-link
                   v-if="editable"
                   :type="textColor"
                   :underline="false"
                   @click="initClientPortalSelectStatus()">
            {{ task.client_portal_status | getLabel(client_select_status_options) }}
          </el-link>
        </el-tooltip>
      </div>
    </el-popover>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'ClientPortalSelected',
  props: {
    task: Object,
  },
  data() {
    return {
      loading: false,
      logLoading: false,
      popoverVisible: false,
      visible: false,
      publish: false,
      client_decline: [],
      not_interested_reason: '',
      not_interested_reason_note: '',
      client_portal_select: null,
      client_select_status_options: [
        { value: 'INITIAL', label: '---' },
        { value: 'SELECTED', label: 'Yes' },
        { value: 'NOT_INTERESTED', label: 'No' },
      ],
      reason_list: [
        {
          value: 'I’m in contact with this expert via another network',
          label: 'I’m in contact with this expert via another network',
        },
        {
          value: 'Expert is not compliant or is a conflict of interest',
          label: 'Expert is not compliant or is a conflict of interest',
        },
        {
          value: 'Expert is not relevant or is out of scope',
          label: 'Expert is not relevant or is out of scope',
        },
        {
          value: 'Quota is filled for this target company or angle/workstream',
          label: 'Quota is filled for this target company or angle/workstream',
        },
        { value: 'Other', label: 'Other' },
      ],
    }
  },
  computed: {
    tooltipDisabled() {
      return this.client_decline.length < 1 || this.visible || this.task.client_portal_status !== 'NOT_INTERESTED'
    },

    editable() {
      const compliance_initial = this.task.capvision_compliance_status === 'INITIAL' && this.task.client_compliance_status === 'INITIAL'
      return ['RECOMMENDED', 'SELECTED'].includes(this.task.general_status) && compliance_initial
    },
    textColor() {
      if (this.task.client_portal_status === 'NOT_INTERESTED') {
        return 'danger'
      } else {
        return this.task.client_portal_status === 'SELECTED' ? 'success' : 'info'
      }
    },
  },
  mounted() {
    this.client_decline = this.task.client_decline ? [this.task.client_decline] : []
  },

  methods: {
    getLogs() {
      this.logLoading = true
      const params = {
        project_id: this.task.project_id,
        'id': this.task.id,
        params: {
          'extra': 'client_decline.client_contact,client_decline.create_by',
        },
      }
      return ProjectAPI.getTaskDetail(params).then(data => {
        this.client_decline = data.client_publish_log
      }).finally(() => {
        this.logLoading = false
      })
    },

    initClientPortalSelectStatus() {
      this.client_portal_select = this.task.client_portal_status
      this.visible = true
    },

    updateClientPortalSelected() {
      const params = {
        id: this.task.id,
        client_portal_status: this.client_portal_select,
      }
      if (this.client_portal_select === 'NOT_INTERESTED') {
        params.client_decline = {
          'reason': this.not_interested_reason || undefined,
          'note': this.not_interested_reason_note || undefined,
        }
      }

      return ProjectAPI.updateClientPortalStatus(params).then(data => {
        this.client_decline = [data.client_decline]
        this.task.client_portal_status = params.client_portal_status
        this.visible = false
        this.popoverVisible = false
      }).finally(() => {
        this.loading = false
      })
    },

    cancelEdit() {
      this.visible = false
      this.popoverVisible = false
    },
  },
}
</script>

<style scoped>

</style>
