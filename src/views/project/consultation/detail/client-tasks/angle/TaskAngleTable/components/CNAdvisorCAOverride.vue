<template>
  <el-tooltip :disabled="!isOverridden" effect="dark" :content="overrideRecord" placement="top">
    <el-popconfirm
      :title="actionTitle"
      cancel-button-text="Cancel"
      confirm-button-text="Confirm & Submit"
      @confirm="actionOverride()"
    >
      <el-link
        slot="reference"
        :type="isOverridden?'success':'primary'"
        :underline="false"
        @click.stop>
        {{ actionName }}
      </el-link>
    </el-popconfirm>
  </el-tooltip>

</template>

<script>
import ProjectAPI from '@/api/project'
import moment from 'moment'

export default {
  name: 'CNAdvisorCAOverride',
  props: { item: Object, project: Object },
  data() {
    return {}
  },
  computed: {
    overrideRecord() {
      if (this.isOverridden) {
        const record = this.item.latest_submitted_pre_ca
        return record.create_by?.name + ' override at ' + moment(record.create_at).format('MM/DD/YYYY hh:mm A')
      } else {
        return ''
      }
    },
    isOverridden() {
      return this.item.ca_status === 'RESPONDED' && this.item.latest_submitted_pre_ca &&
        this.item.latest_submitted_pre_ca.method === 'OVERRIDDEN'
    },
    actionName() {
      return this.isOverridden ? 'Overridden' : 'Override'
    },
    actionTitle() {
      return this.isOverridden
        ? 'Confirmation of revocation of override?'
        : 'By submitting this override you confirm that the expert has signed the Client Agreement via the China DB.  Please note this override will be stored for compliance review'
    },
  },
  methods: {
    actionOverride() {
      if (this.isOverridden) {
        return ProjectAPI.CNAdvisorTaskCARevoke(this.item.id).then(data => {
          this.$message({
            type: 'success',
            message: 'Revoke CA Successfully',
          })
          this.item.ca_status = 'INITIAL'
        })
      } else {
        return ProjectAPI.CNAdvisorTaskCAOverride(this.item.id).then(data => {
          this.$message({
            type: 'success',
            message: 'Override CA Successfully',
          })
          this.item.ca_status = 'RESPONDED'
          this.item.latest_submitted_pre_ca = data
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
