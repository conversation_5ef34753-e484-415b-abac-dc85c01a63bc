<template>
  <div>
    <el-popover
      v-model="popoverVisible"
      placement="top"
      width="100">
      <div v-if="visible" class="mt-8">
        <el-input-number v-model="client_rate_new" :precision="2" :step="0.1" :min="0" />
        <el-button type="text" size="mini" class="ml-3" @click="updateClientRate">Save</el-button>
        <el-button type="text" size="mini" class="info" @click="cancelEdit">Cancel</el-button>
      </div>
      <el-button slot="reference" type="text" @click="initClientRate()">{{ clientRate }}</el-button>
    </el-popover>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'ClientRateEdit',
  props: {
    task: {
      type: Object,
    },
  },
  data() {
    return {
      popoverVisible: false,
      visible: false,
      client_rate_new: 0,
    }
  },
  computed: {
    clientRate() {
      return this.task.client_rate_usd || this.task.specified_client_rate_currency_after_multiple || this.task.default_client_rate_usd || this.task.default_client_rate_usd_considering_unsigned_tc || '---'
    },
  },
  methods: {
    initClientRate() {
      this.client_rate_new = this.task.client_rate_usd || this.task.specified_client_rate_currency_after_multiple || this.task.default_client_rate_usd || this.task.default_client_rate_usd_considering_unsigned_tc
      this.visible = true
    },

    cancelEdit() {
      this.popoverVisible = false
    },

    updateClientRate() {
      const params = {
        project_id: this.task.project_id,
        id: this.task.id,
        client_rate_usd: this.client_rate_new,
      }
      return ProjectAPI.updateTask(params)
        .then(() => {
          this.$set(this.task, 'client_rate_usd', this.client_rate_new)
          this.$emit('update')
          this.$message.success('success')
        }).finally(() => {
          this.popoverVisible = false
        })
    },
  },
}
</script>

<style scoped>

</style>
