<template>
  <div class="tasks angle">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="flex align-items-center clearfix">
        <i class="cursor-move el-icon-rank" />
        <span class="angle-title">{{ angleData.name }}</span>
        <div class="inline-block angle-tool">
          <ico-button
            class="filter-item"
            tooltip="Collapse"
            no-border
            is-rotate
            :deg="90"
            @action="handleCollapseClick">
            <el-icon slot="ico" class="el-icon-arrow-right" />
          </ico-button>
          <show-angle-topic :angle="angleData" />
          <show-angle-median-rate v-if="isSurveyProject" :angle="angle" />
          <edit-task-angle
            class="filter-item"
            type="edit"
            :projec="project"
            :angle.sync="angleData"
            @delete="deleteAngle"
            @Done="reloadList()"
          />
          <ico-button
            v-if="!screened"
            class="filter-item"
            tooltip="Add Advisor"
            no-border
            @action="gotoAddAdvisor">
            <svg-icon slot="ico" icon-class="consultant" />
          </ico-button>
          <questionnaire-show
            v-if="angleData.inquiry_branch_id"
            class="filter-item"
            :angle="angleData"
          />
          <add-to-project
            v-if="angle_advisors.length > 0"
            :task-angle="angleData.name"
            :advisors="angle_advisors" />
          <el-tooltip
            content="Refresh"
            placement="bottom"
            :hide-after="1500"
            effect="dark">
            <refresh-button no-border class="filter-item" @action="reloadList" />
          </el-tooltip>
          <ico-button
            class="filter-item"
            tooltip="Download"
            no-border
            @action="downloadAngleTask()">
            <el-icon slot="ico" class="el-icon-download" />
          </ico-button>
          <ico-button
            v-if="angleData.related_email_template_id"
            tooltip="Outreach"
            no-border
            @action="outreachToAdvisor()">
            <el-icon slot="ico" class="el-icon-message" />
          </ico-button>
          <ico-button
            v-if="isSurveyProject"
            tooltip="Update Median Rate"
            no-border
            @action="updateMedianRate()">
            <el-icon slot="ico" :class="medianRateLoading ? 'el-icon-loading' : 'el-icon-magic-stick'" />
          </ico-button>
        </div>
        <el-input
          v-model.number="searchQuery.id"
          type="number"
          clearable
          placeholder="Task ID"
          class="filter-item w-120px ml-3"
          @input="debounceSearch"
        />
        <el-checkbox
          v-model="showAllColumns"
          border
          class="filter-item"
          style="margin-left: auto;"
        >
          Show All Columns
        </el-checkbox>
      </div>

      <el-collapse-transition>
        <div v-show="isCollapse">
          <el-table
            ref="advisorMultipleTable"
            key="project_advisor_table"
            v-loading="loading"
            row-key="id"
            class="stripe-table"
            :data="tableData.data"
            stripe
            fit
            border
            highlight-current-row
            :span-method="arraySpanMethod"
            :row-class-name="tableRowClassName"
            :header-cell-class-name="tableHeaderCellClassName"
            @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData.data, $refs.advisorMultipleTable, checkSelectable)"
            @sort-change="sortData"
            @selection-change="handleSelectionChange"
            @toggleRowExpansion="toggleRowExpansion"
            @header-dragend="handleHeaderDragend"
          >
            <el-table-column width="35">
              <template slot-scope="scope">
                <el-dropdown
                  trigger="click"
                  placement="bottom"
                  :disabled="!!scope.row.complete_time"
                  @command="handleLockTask(scope.row, $event)"
                >
                  <i
                    :class="{
                      'el-icon-lock cursor-pointer danger': scope.row.display_id_locked,
                      'el-icon-unlock cursor-move success': hadSentToClient(scope.row) && !scope.row.display_id_locked,
                      'el-icon-rank cursor-move': !hadSentToClient(scope.row) && !scope.row.display_id_locked
                    }"
                    style="font-size: 12px;"
                  />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-if="scope.row.display_id_locked"
                      icon="el-icon-unlock"
                      :command="false">
                      Unlock
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-else
                      icon="el-icon-lock"
                      :command="true">
                      Lock
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column type="selection" min-width="45" :selectable="checkSelectable" />
            <el-table-column prop="display_id" label="ID" width="55" />
            <el-table-column label="Unique ID" width="90">
              <template slot-scope="scope">
                GL{{ scope.row.advisor_id }}
              </template>
            </el-table-column>
            <el-table-column v-if="showAllColumns" key="task_id" prop="id" label="Task ID" min-width="65" />
            <el-table-column prop="advisor_id" label="Advisor ID" min-width="65" />
            <el-table-column type="expand" label="View" min-width="45">
              <template slot-scope="scope">
                <div v-if="!scope.row.advisor.gdpr_delete_by_id" class="table-expand">
                  <edit-task
                    :type="scope.row.advisor.type"
                    class="flex flex-wrap edit-task justify-content-center"
                    :task.sync="scope.row"
                    @update="reloadList"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Client Portal">
              <el-table-column label="Publish" min-width="70">
                <template slot-scope="scope">
                  <client-portal-publish :task="scope.row" />
                </template>
              </el-table-column>
              <el-table-column label="Selected" min-width="70">
                <template slot-scope="scope">
                  <client-portal-selected :task="scope.row" />
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Mark" width="50">
              <template slot-scope="scope">
                <mark-btn :data="scope.row" />
              </template>
            </el-table-column>
            <el-table-column label="Progress" width="120" class-name="overflow-visible">
              <template slot-scope="scope">
                <task-progress-v2
                  task-type="tasks"
                  :data="scope.row"
                  :project="project"
                  @update="getSelectedTasksDetail(selectedAdvisor)"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="name"
              label="Name"
              min-width="138"
              column-key="collapse-start"
            >
              <template slot-scope="scope">
                <el-tooltip
                  :enterable="false"
                  :open-delay="1000"
                  content="Copy Task URL"
                  effect="dark"
                  placement="top"
                >
                  <el-link
                    :underline="false"
                    class="copy-task-link"
                    @click="copyTaskUrl(scope.row.id)">
                    <svg-icon icon-class="location-arrow-solid" />
                  </el-link>
                </el-tooltip>
                <USGovernmentMark :advisor="scope.row.advisor" />
                <span v-if="scope.row.advisor && !scope.row.advisor.gdpr_delete_by_id">
                  <el-tooltip
                    effect="dark"
                    placement="top"
                    :disabled="!scope.row.advisor_blocked_status.length"
                  >
                    <div slot="content">
                      <span v-for="(status, index) in scope.row.advisor_blocked_status" :key="status">
                        <slot v-if="index">,</slot>
                        <slot v-if="status === 'ADVISOR'">Expert</slot><slot
                          v-if="status === 'CURRENT_JOB_COMPANY'">Current Company</slot><slot
                          v-if="status === 'LOCATION'">Location</slot>
                      </span>
                      is in this Client’s blacklist.
                      <div>
                        <el-link type="primary" @click="actionRemoveAdvisor(scope.row, scope.$index)">
                          Click To Remove
                        </el-link>
                      </div>
                    </div>
                    <router-link-advisor
                      :title="scope.row.advisor.full_name"
                      :data="scope.row.advisor"
                      :class="{'is-blocked': scope.row.advisor_blocked_status.length}" />
                  </el-tooltip>
                  <NewAdvisorTag :advisor="scope.row.advisor" />
                </span>
                <GenerationImTaskCustom v-if="isGeneration" :task-data.sync="scope.row" @update="reloadList" />
                <span v-if="scope.row.advisor.gdpr_delete_by_id" class="warning pl-5">
                  Expert Deleted by {{ scope.row.advisor.gdpr_delete_by.name }} at {{
                    scope.row.advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A')
                  }}.
                </span>
              </template>
            </el-table-column>
            <el-table-column label="Location" min-width="120" column-key="advisor-info">
              <template slot-scope="scope">
                <span v-if="scope.row.advisor.location_id">
                  {{scope.row.advisor.location && scope.row.advisor.location.name}}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="Company" min-width="150" column-key="advisor-info">
              <template slot-scope="scope">
                <el-dropdown
                  placement="bottom-start"
                  trigger="click"
                  @command="(val) => changeTaskJob(val, scope.row)"
                >
                  <span class="dropdown-link">
                    <slot
                      v-if="scope.row['advisor_profile'] && scope.row['advisor_profile'].company"
                      :title="scope.row['advisor_profile'].company.name">
                      <advisor-profile-tips
                        :advisor-profile="scope.row['advisor_profile']"
                        :advisor-jobs="scope.row.advisor.jobs" />
                      {{ scope.row['advisor_profile'].company.name }}
                    </slot>
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="job in scope.row.advisor.jobs"
                      :key="job.id"
                      :command="job"
                    >
                      <slot v-if="job.company">
                        {{ job.company.name }} | {{ job.position }} |
                        {{ job.start_date }} ~
                        {{ job.is_current ? 'Current' : job.end_date }}
                      </slot>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column label="Title" min-width="140" column-key="advisor-info">
              <template slot-scope="scope">
                <el-popover
                  :ref="`popover-${sequence}-${scope.$index}`"
                  :append-to-body="true"
                  placement="top"
                  width="200">
                  <el-form
                    ref="form"
                    :model="advisorProfileForm"
                    inline
                    size="mini">
                    <el-form-item
                      style="margin-right: 0;margin-bottom: 17px;"
                      prop="position"
                      :rules="{required: true, message: 'position is required', trigger: 'change'}">
                      <el-input v-model="advisorProfileForm.position" />
                    </el-form-item>
                    <el-form-item
                      style="margin-bottom: 0px;width: 100%;text-align: center;">
                      <el-button
                        type="primary"
                        @click="changeTaskAdvisorProfilePosition(scope._self.$refs[`popover-${sequence}-${scope.$index}`], scope.row, advisorProfileForm)"
                      >Save
                      </el-button>
                      <el-button
                        type="text"
                        @click="scope._self.$refs[`popover-${sequence}-${scope.$index}`].doClose()">Cancel
                      </el-button>
                    </el-form-item>
                  </el-form>

                  <span
                    v-if="scope.row['advisor_profile']"
                    slot="reference"
                    class="cursor-pointer text-truncate"
                    :title="scope.row['advisor_profile'].position"
                    @click="advisorProfileForm.position = scope.row['advisor_profile'].position"
                  >{{ scope.row['advisor_profile'].position }}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Date" min-width="120" column-key="advisor-info">
              <template slot-scope="scope">
                <span v-if="scope.row['advisor_profile'] && scope.row['advisor_profile'].start_date">
                  {{jobDateFormat(scope.row['advisor_profile'].start_date) }}~{{scope.row['advisor_profile'].is_current ? 'Current' : jobDateFormat(scope.row['advisor_profile'].end_date) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="US Government Employee" min-width="90" column-key="advisor-info">
              <template slot-scope="scope">
                <el-dropdown
                  trigger="click"
                  placement="bottom-start"
                  @command="updateTaskAdvisorUsGovernmentEmployee($event, scope.row)">
                  <span class="dropdown-link">
                    <span
                      v-if="!scope.row.client_custom_fields">
                      ---
                    </span>
                    <span
                      v-if="scope.row.client_custom_fields && !scope.row.client_custom_fields.is_us_government_employee"
                      class="danger">
                      No
                    </span>
                    <span
                      v-if="scope.row.client_custom_fields && scope.row.client_custom_fields.is_us_government_employee"
                      class="success">
                      Yes
                    </span>
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="false">No</el-dropdown-item>
                    <el-dropdown-item :command="true">Yes</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column v-if="isSurveyProject" label="Survey Honorarium" min-width="80" column-key="advisor-info">
              <template slot-scope="scope">
                <honorarium-edit :task="scope.row" />
              </template>
            </el-table-column>
            <el-table-column label="Contact" min-width="80" column-key="advisor-info">
              <template slot-scope="scope">
                <contact-tags :data="scope.row.advisor" />
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isSurveyProject"
              label="Expert Rate"
              min-width="80"
              prop="rate_usd_approximate"
              sortable="column">
              <template slot-scope="scope">
                {{ getExpertRate(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Client Rate" min-width="70">
              <template slot-scope="scope">
                <client-rate :task.sync="scope.row" />
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="One Hour Minimum" min-width="90">
              <template slot-scope="scope">
                <el-tooltip
                  v-if="scope.row.advisor.time_minimum !== 'NO_MINIMUM'"
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    This expert will consult for a minimum of
                    {{ scope.row.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }}
                  </template>
                  <el-tag type="warning" size="mini">
                    {{ scope.row.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }}
                  </el-tag>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Consult Date" min-width="125">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.consult_date">{{ scope.row.consult_date | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                <span v-if="!scope.row.consult_date">---</span>
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Client Hour" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.task_hours ? scope.row.task_hours.client_hours : '---' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Billable Hour" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.task_hours ? scope.row.task_hours.billing_hours : '---' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Support" min-width="100" sortable prop="support.name">
              <template slot-scope="scope">
                <el-dropdown
                  v-if="scope.row['support']"
                  placement="bottom-start"
                  trigger="click"
                  @command="(val) => changeTaskSupport(val, scope.row)"
                >
                  <span class="dropdown-link">
                    <slot :title="scope.row['support'].name">
                      {{ scope.row['support'].name }}
                    </slot>
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="member in supportMembers"
                      :key="member.id"
                      :disabled="checkSourced(scope.row)"
                      :command="member"
                    >
                      <span>{{ member.name }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column label="Last Outreach Time" min-width="150">
              <template slot-scope="scope">
                <span v-if="scope.row.latest_outreach_record">
                  {{ scope.row.latest_outreach_record.create_at| momentFormat('MM/DD/YYYY hh:mm A') }}
                  ({{ getTaskOutreachNum(scope.row.email_records) }})
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isSGDB && showAllColumns"
              key="verified"
              label="Verified"
              min-width="70">
              <template slot-scope="scope">
                <recent-outreach-status
                  api-type="ListWise"
                  :email-records="scope.row.advisor_email_list_wise_records"
                  :email-status="scope.row.list_wise_email_status" />
              </template>
            </el-table-column>
            <el-table-column
              v-if="isSurveyProject && !isSGDB && showAllColumns"
              key="recent_email_status"
              label="Recent Email Status"
              min-width="80">
              <template slot-scope="scope">
                <recent-outreach-status
                  api-type="SendGrid"
                  :item="scope.row"
                  :email-status="scope.row.most_recent_email_status" />
              </template>
            </el-table-column>
            <el-table-column
              v-if="isSurveyProject && !isSGDB && showAllColumns"
              key="opened"
              label="Opened"
              prop="outreach_opened"
              sortable="column"
              :sort-orders="['descending', null]"
              min-width="90">
              <template slot-scope="scope">
                <outreach-opened :item="scope.row" />
              </template>
            </el-table-column>
            <el-table-column v-if="!isSurveyProject" label="Override CA" min-width="80">
              <template slot-scope="scope">
                <CNAdvisorCAOverride
                  v-if="scope.row.client.require_pre_call_ca"
                  :item.sync="scope.row"
                  :project="project" />
              </template>
            </el-table-column>
            <el-table-column v-if="isSurveyProject" label="Survey Link" min-width="80">
              <template slot-scope="scope">
                <survey-link-copy :task-data="scope.row" />
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isSurveyProject"
              label="Links"
              fixed="right"
              width="55"
            >
              <template slot-scope="scope">
                <workflow-link
                  v-if="!scope.row.advisor_blocked_status.length"
                  :task-item="scope.row"
                  :project="project"
                  expert-type="Advisor" />
              </template>
            </el-table-column>
            <el-table-column label="Action" fixed="right" width="100">
              <template slot-scope="scope">
                <div
                  v-if="!scope.row.advisor.gdpr_delete_by_id && !scope.row.advisor_blocked_status.length"
                  class="flex align-items-center">
                  <upload-ca-email
                    v-if="isShowActionBtn(scope.row, 'uploadCAEmail')"
                    class="mr-8"
                    :task="scope.row"
                    ca-type="PRE_CALL"
                    @done="reloadList"
                  />
                  <upload-ca-email
                    v-if="isShowActionBtn(scope.row, 'uploadPostCAEmail')"
                    class="mr-8"
                    :task="scope.row"
                    ca-type="POST_CALL"
                    @done="reloadList"
                  />
                  <el-tooltip
                    effect="dark"
                    :enterable="false"
                    placement="top"
                  >
                    <div slot="content">
                      <span v-if="checkClientExecute(scope.row)">The client is not in the execution state and cannot schedule a call.</span>
                      <span v-if="checkGenerationIMCode(scope.row)">The client requires an Expert code.</span>
                      <span v-if="checkAdvisorInBlacklist(scope.row)">The expert is on the blacklist and cannot be Arrange.</span>
                      <span v-if="scope.row.arrange_need_tc_signed">The client asked the expert to sign the TC.</span>
                      <span
                        v-if="scope.row.arrange_need_task_approved">The client requires a legal review and approval.</span>
                      <span
                        v-if="scope.row.arrange_need_outsource_approved">The client requires outsource compliance approval.</span>
                      <span v-if="scope.row.arrange_need_validated_npi">The advisor's NPI is not validated.</span>
                      <span v-if="!scope.row.arrange_disabled">
                        {{ `Arrange${!scope.row['compliance_passed_jit'] ? ' (Need Approval)' : ''}` }}</span>
                    </div>
                    <el-link
                      v-if="isShowActionBtn(scope.row, 'arrange')"
                      type="success"
                      :disabled="scope.row.arrange_disabled"
                      :underline="false"
                      class="mr-8"
                      icon="el-icon-date"
                      @click="goToTaskArrange(scope.row)"
                    />
                  </el-tooltip>
                  <el-tooltip
                    effect="dark"
                    :enterable="false"
                    content="Cancel Arrange"
                    placement="top"
                  >
                    <el-link
                      v-if="isShowActionBtn(scope.row, 'cancel')"
                      type="danger"
                      :underline="false"
                      class="mr-8"
                      icon="el-icon-document-delete"
                      @click="cancelArrangedSchedule(scope.row)"
                    />
                  </el-tooltip>
                  <el-tooltip
                    effect="dark"
                    :enterable="false"
                    :content="completeTip(scope.row)"
                    placement="top"
                  >
                    <el-link
                      v-if="isShowActionBtn(scope.row, 'complete')"
                      type="primary"
                      :underline="false"
                      icon="el-icon-finished"
                      :disabled="completeDisabled(scope.row)"
                      @click="goToTaskComplete(scope.row.id)"
                    />
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-if="customPageSize.length"
            :total="tableData.count"
            :page.sync="searchQuery.page"
            :limit.sync="searchQuery.size"
            :auto-scroll="false"
            :page-sizes="customPageSize"
            @pagination="getList"
          />
        </div>
      </el-collapse-transition>
    </el-card>

    <outreach-to-advisor
      ref="outreachToAdvisor"
      :tasks.sync="selectedAdvisor"
      :project="project"
      style="display: none !important;" />
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import TaskMixins from '@/views/project/consultation/detail/mixins/task'
import SurveyLinkCopy from '@/views/project/consultation/detail/components/SurveyLinkCopy'
import { copyUtil } from '@/utils/tool'
import OutreachOpened from '@/views/project/consultation/detail/components/Outreach/OutreachOpened'
import CommonMixin from './common'
import ShowAngleMedianRate from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/ShowAngleMedianRate.vue'

export default {
  name: 'McKTaskAngle',
  components: {
    ShowAngleMedianRate,
    SurveyLinkCopy,
    OutreachOpened,
  },
  mixins: [TaskMixins, CommonMixin],
  props: {
    lazy: {
      type: Boolean,
      default: false,
    },
    orderBy: String,
    screened: Boolean,
    selectLoading: Boolean,
    searchAngleTaskId: [String, Number],
    searchAngleAdvisorId: [String, Number],
    searchCompanyNameContains: String,
    searchCompanyIsContainsMode: Boolean,
    searchCompanyIsCurrent: Boolean,
    searchSupportIds: Array,
    searchTaskIds: String,
    blacklistFilter: String,
    hideOnPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      angleData: Object.assign({}, this.angle),
      angle_advisors: [],
      showAllColumns: false,
      searchQuery: {
        project_id: this.$route.params.project_id,
        angle_id: this.angle.id,
        support_ids: undefined,
        company_name_contains: undefined,
        company_is_contains_mode: undefined,
        company_is_current: undefined,
        sq_status: this.screened ? 'RESPONDED' : undefined,
        id: undefined,
        ids: this.searchTaskIds,
        page: 1,
        size: 20,
        'advisor.or-0.status_not_in': undefined,
        'advisor.or-1.status_is_null': undefined,
        'advisor_decline.or-0.id_is_null': undefined,
        'advisor_decline.or-1.is_valid': undefined,
        extra: [
          'arrangement',
          'advisor.jobs.company',
          'advisor.current_job',
          'advisor.sourced_at',
          'advisor_profile.company',
          'latest_outreach_record',
          'email_records.advisor_tracking',
          'advisor.email_validation_records',
          'advisor.contact_infos',
          'advisor.gdpr_delete_by',
          'advisor.sourced_at',
          'advisor.location',
          'advisor_decline',
          'advisor_blocked_status',
          'support',
          'revenues',
          'schedule',
          'post_ca_list',
          'latest_submitted_pre_ca.files',
          'latest_submitted_pre_ca.inquiry_instance',
          'latest_submitted_post_ca.files',
          'latest_submitted_post_ca.inquiry_instance',
          'latest_pre_ca',
          'client',
          'compliance_approve_policy_jit',
          'compliance_passed_jit',
          'default_client_rate_usd',
          'default_client_rate_usd_considering_unsigned_tc',
          'specified_client_rate_currency_after_multiple',
          'client_decline.create_by',
          'advisor.location_id_path',
          'client.compliance_preference',
          'client.am_team',
          'advisor.latest_signed_tc',
          'task_outsource_info.task_outsource_arrangement',
        ].join(),
        sort: 'rank asc',
      },
      scrollContainer: undefined,
      companyNameContainsTimeout: null,
    }
  },

  watch: {
    angle(val) {
      if (val) {
        this.$forceUpdate()
      }
    },
    'angleData.inquiry_branch_id': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.reloadList()
        }
      }, immediate: false,
    },
    'orderBy': {
      handler(newVal) {
        if (newVal) {
          this.searchQuery.sort = newVal
        }
      },
      immediate: true,
    },
    'blacklistFilter': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && (newVal || oldVal)) {
          this.initBlacklistFilter()
          if (newVal === 'BLACKLIST' || newVal === 'B&D') {
            this.searchQuery['advisor.or-0.status_not_in'] = 'BLACKLIST'
            this.searchQuery['advisor.or-1.status_is_null'] = true
          }
          if (newVal === 'DECLINED' || newVal === 'B&D') {
            this.searchQuery['advisor_decline.or-0.id_is_null'] = true
            this.searchQuery['advisor_decline.or-1.is_valid'] = false
          }
          this.getList()
        }
      },
      immediate: true,
    },
    'searchAngleTaskId': {
      handler(newVal, oldVal) {
        if (!newVal) {
          this.searchQuery.id = null
          this.getList()
          return
        }
        if (newVal !== oldVal && this.angle.id === +newVal.split('-')[0]) {
          this.searchQuery.id = +newVal.split('-')[1]
          this.getList()
        }
      }, immediate: false,
    },
    'searchAngleAdvisorId': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && (newVal || oldVal)) {
          this.searchQuery.advisor_id = +newVal || undefined
          this.getList()
        }
      }, immediate: true,
    },
    'searchTaskIds': {
      handler(newVal, oldVal) {
        if (!newVal) {
          this.searchQuery.ids = null
          this.getList()
          return
        }
        if (newVal !== oldVal) {
          this.searchQuery.ids = newVal
          this.getList()
        }
      }, immediate: false,
    },
    'searchSupportIds': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && (newVal?.length || oldVal?.length)) {
          this.searchQuery.support_ids = newVal.join() || undefined
          this.getList()
        }
      }, immediate: true,
    },
    'searchCompanyNameContains': {
      handler(newVal) {
        if (this.companyNameContainsTimeout) clearTimeout(this.companyNameContainsTimeout)
        this.companyNameContainsTimeout = setTimeout(() => {
          this.searchQuery.company_name_contains = newVal.trim() || undefined
          this.getList()
        }, 500)
      },
      immediate: false,
    },
    'searchCompanyIsContainsMode': {
      handler(newVal) {
        if (this.searchCompanyNameContains.trim()) {
          this.searchQuery.company_is_contains_mode = newVal
          this.getList()
        }
      },
      immediate: false,
    },
    'searchCompanyIsCurrent': {
      handler(newVal) {
        if (this.searchCompanyNameContains.trim()) {
          this.searchQuery.company_is_current = newVal
          this.getList()
        }
      },
      immediate: false,
    },
    'allSelectedAdvisor': {
      handler(val) {
        this.selectable = true
        if (val.length) {
          const firstSelectedTask = val[0]
          if (firstSelectedTask.angle_id === this.angle.id) return
          if (firstSelectedTask.task_outsource_status === 'IMPORTED') {
            this.selectable = false
          }
          if (firstSelectedTask.task_outsource_status !== 'IMPORTED' && this.angle.angle_for_outsource_import) {
            this.selectable = false
          }
        }
      },
    },
  },

  methods: {
    getList() {
      if (this.hideOnPage) return Promise.resolve()
      this.loading = true
      const params = this.searchQuery
      if (this.isSurveyProject) {
        params.extra += `,${this.extra_for_survey.join(',')}`
      }
      return ProjectAPI.getProjectAdvisorList(params)
        .then(data => {
          this.tableData.data = this.formatList(data)['list']
          this.tableData.count = data['count']
          this.angle_advisors = this.tableData.data.map(item => item.advisor)
          this.$nextTick(() => {
            this.clone_select.length ? this.selectForUser() : ''
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    completeTip(row) {
      if (this.completeDisabled(row)) {
        return row.client.disallow_complete_task && !this.isSurveyProject
          ? 'The client is not allowed to complete.'
          : 'The client asked for a project code, but there was none.'
      } else {
        return 'Complete'
      }
    },

    copyTaskUrl(task_id) {
      const url = window.location.origin +
        `/#/project/consultation/${this.projectId}/client-tasks-mck?angle_id=${this.angle.id}&task_id=${task_id}`
      return copyUtil(url)
        .then(() => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
    },

    updateTaskClientPortalStatus(status, item) {
      const params = {
        project_id: this.project.id,
        id: item.id,
        client_portal_status: status,
      }
      return ProjectAPI.updateTask(params)
        .then(() => {
          item.client_portal_status = status
        })
    },

    updateTaskAdvisorUsGovernmentEmployee(val, item) {
      const new_client_custom_fields = Object.assign({}, item.client_custom_fields, {
        is_us_government_employee: val,
      })
      const params = {
        project_id: this.project.id,
        id: item.id,
        client_custom_fields: new_client_custom_fields,
      }
      return ProjectAPI.updateTask(params)
        .then(() => {
          item.client_custom_fields = new_client_custom_fields
        })
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.advisor?.gdpr_delete_by_id) {
        if (column.columnKey === 'collapse-start') {
          return [1, 7]
        } else if (column.columnKey === 'advisor-info') {
          return [0, 0]
        }
      }
    },

    async handleLockTask(row, val) {
      await ProjectAPI.updateTaskDisplayLocked(row.id, val)
      row.display_id_locked = val
    },

    initBlacklistFilter() {
      this.searchQuery['advisor.or-0.status_not_in'] = undefined
      this.searchQuery['advisor.or-1.status_is_null'] = undefined
      this.searchQuery['advisor_decline.or-0.id_is_null'] = undefined
      this.searchQuery['advisor_decline.or-1.is_valid'] = undefined
    },
  },
}
</script>

<style lang="scss">
.angle {
  & + & {
    margin-top: 15px;
  }

  .angle-title {
    max-width: 200px;
    line-height: 1.7;
    margin: 0 10px 0 20px;
    word-spacing: normal;
  }

  .stripe-table {
    border: 1px solid #ebeef5;
    border-bottom: 0;

    thead .gutter {
      display: table-cell !important;
    }

    thead th:nth-of-type(2) label {
      margin-left: 4px;
    }

    tr.blue-background-class {
      background: #c8ebfb;
    }

    .success-row td {
      background-color: #f0f9eb !important;
    }

    .copy-task-link {
      color: #dadce0;

      :hover {
        color: #868686;
      }
    }
  }

  .el-table__row:hover {
    .dropdown-link .el-icon--right {
      display: inline-block;
    }
  }

  .dropdown-link {
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;

    .el-icon--right {
      display: none;
    }
  }

  .is-blocked {
    color: #F56C6C;

    &:hover {
      color: #f78989;
    }
  }
::v-deep .el-button+.el-button{
  margin-left: 0;
}
}

</style>
