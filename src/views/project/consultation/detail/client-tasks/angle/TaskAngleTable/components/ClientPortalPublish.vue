<template>
  <div>
    <el-popover
      v-model="popoverVisible"
      placement="top"
      trigger="click"
      width="100">
      <div v-if="visible" class="mt-8">
        <el-switch
          v-model="publish"
          :loading="loading"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="Yes"
          inactive-text="No"
          class="mb-8" />
        <el-button type="text" size="mini" class="ml-3" @click="updateClientPublishStatus">Save</el-button>
        <el-button type="text" size="mini" class="info" @click="cancelEdit">Cancel</el-button>
      </div>
      <div slot="reference">
        <el-tooltip class="item" :disabled="tooltipDisabled" effect="light" placement="top-start" :open-delay="500">
          <div slot="content">
            <el-table
              border
              stripe
              :data="status_log"
            >
              <el-table-column label="Publish" width="80">
                <template slot-scope="scope">
                  {{ scope.row.json.client_publish_status | getLabel(client_publish_status_options) }}
                </template>
              </el-table-column>
              <el-table-column label="User">
                <template slot-scope="scope">
                  <span v-if="scope.row.create_by">{{ scope.row.create_by.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Time" width="150">
                <template slot-scope="scope">
                  {{ scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-link type="primary" :underline="false" @click="initClientPublishStatus()">
            {{ task.client_publish_status | getLabel(client_publish_status_options) }}
          </el-link>
        </el-tooltip>
      </div>
    </el-popover>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'ClientPortalPublish',
  props: {
    task: Object,
  },
  data() {
    return {
      loading: false,
      popoverVisible: false,
      visible: false,
      publish: false,
      status_log: [],
      client_publish_status_options: [
        { value: 'INITIAL', label: '---' },
        { value: 'PUBLISHED', label: 'Yes' },
        { value: 'UNPUBLISHED', label: 'No' },
      ],
    }
  },
  computed: {
    tooltipDisabled() {
      return this.status_log.length < 1 || this.visible
    },
  },
  mounted() {
    this.status_log = this.task.client_publish_log || []
  },
  methods: {
    initClientPublishStatus() {
      this.publish = this.task.client_publish_status === 'PUBLISHED'
      this.visible = true
    },

    updateClientPublishStatus() {
      this.loading = true
      const params = {
        'id': this.task.id,
        'client_publish_status': this.publish ? 'PUBLISHED' : 'UNPUBLISHED',
      }
      return ProjectAPI.updateTaskClientPublish(params).then(data => {
        this.task.client_publish_status = data.client_publish_status
        this.status_log = data.client_publish_log
      }).finally(() => {
        this.loading = false
        this.visible = false
        this.popoverVisible = false
      })
    },

    cancelEdit() {
      this.visible = false
      this.popoverVisible = false
    },
  },
}
</script>

<style scoped>

</style>
