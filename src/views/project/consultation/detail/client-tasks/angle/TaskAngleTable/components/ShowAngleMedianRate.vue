<template>
  <div v-if="!!rateCurrencyKeys.length" class="inline-block">
    <el-tooltip effect="light" placement="top-start">
      <div slot="content">
        <el-descriptions direction="vertical" :column="rateCurrencyKeys.length + 1" border>
          <el-descriptions-item>
            Rate Median
          </el-descriptions-item>
          <el-descriptions-item v-for="item in rateCurrencyKeys" :key="item" :label="item">
            {{ angle.median_advisor_rates_map[item] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-button class="ico-btn no-border">
        <el-icon class="el-icon-money primary" />
      </el-button>
    </el-tooltip>
    <span class="primary fs-12">{{ angle.median_advisor_rates_map.USD }} USD</span>
  </div>
</template>

<script>

export default {
  name: 'ShowAngleMedianRate',
  props: {
    angle: Object,
  },
  computed: {
    rateCurrencyKeys() {
      return Object.keys(this.angle?.median_advisor_rates_map || {})
    },
  },
}
</script>

<style scoped>
.ico-btn {
  padding: 0;
  width: 28px;
  height: 28px;
  box-sizing: border-box;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.no-border {
  border: none;
  margin: 0;

  &:hover {
    border: 1px solid #DCDFE6;
  }
}
</style>
