<template>
  <el-tooltip effect="dark" placement="top-start">
    <div slot="content">
      <div>Angle Topic: {{ angle.topic }}</div>
      <div v-if="angle.discussion_topic" class="flex">
        <div>Discussion Topic: </div>
        <div class="pre-wrap">{{ angle.discussion_topic }}</div>
      </div>
    </div>
    <el-button class="ico-btn no-border">
      <el-icon class="el-icon-chat-line-round primary" />
    </el-button>
  </el-tooltip>
</template>

<script>

export default {
  name: 'ShowAngleTopic',
  props: {
    angle: Object,
  },
}
</script>

<style scoped>
.ico-btn {
  padding: 0;
  width: 28px;
  height: 28px;
  box-sizing: border-box;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.no-border {
  border: none;
  margin: 0;

  &:hover {
    border: 1px solid #DCDFE6;
  }
}
</style>
