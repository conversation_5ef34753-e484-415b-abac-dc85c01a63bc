<template>
  <div>
    <el-button type="text" icon="el-icon-upload2" @click="actionDialogShow" />
    <el-dialog :visible.sync="dialogVisible" :title="content" append-to-body>
      <el-upload
        accept=".pdf, .doc, .docx, .xls, .xlsx, .zip, .eml, .msg"
        :action="upload_url"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        multiple
        :http-request="uploadFullStep"
        :file-list="fileList">
        <el-button v-if="!loading" type="primary">Upload</el-button>
        <el-button v-else type="text" icon="el-icon-loading" disabled />
        <div slot="tip" class="el-upload__tip">The size of the uploaded file must not exceed 20MB.</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :disabled="loading" @click="handleSubmit">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { AppHttpService } from '@/utils/request'

export default {
  name: 'UploadCaEmail',
  props: {
    task: Object,
    caType: String,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      upload_url: `/files`,
      fileList: [],
      upload_file_list: [],
    }
  },
  computed: {
    content() {
      let type = ''
      switch (this.caType) {
        case 'PRE_CALL':
          type = 'Pre'
          break
        case 'POST_CALL':
          type = 'Post'
          break
      }
      return `Upload ${type} CA Email`
    },
  },
  methods: {
    actionDialogShow() {
      this.fileList = []
      this.dialogVisible = true
    },
    uploadFullStep(data) {
      this.loading = true
      const form = new FormData()
      form.append('file', data.file)
      form.append('content_type', 'TASK_CA')

      return AppHttpService
        .patch(data.action, form)
        .then(resp => {
          /* (old) Example 1
          create_time: "2020-05-28T03:17:45Z"
          file_url: "https://qa-udb2.capvision.com/files/ca_respond/2020-06/TASK_CA_75"
          id: 75
          inquiry_branch_id: 87
          inquiry_id: 66
          inquiry_instance: null
          inquiry_instance_id: 2459
          submit_time: "2020-06-01T08:41:46Z"
          task_id: 53
          type: "PRE_CALL"
          update_time: "2020-06-01T08:41:45Z"
          */

          /* Example 2
          create_time: "2020-06-22T10:12:07Z"
          file_name: "测试下载时文件名正常显示.txt"
          file_url: "https://qa-udb2.capvision.com/files/ca_respond/2020-06/TASK_CA_3TFDSW4C"
          id: 182
          inquiry: null
          inquiry_branch: null
          inquiry_branch_id: 144
          inquiry_id: 94
          inquiry_instance: null
          inquiry_instance_id: 2610
          method: "EMAIL"
          submit_time: "2020-06-28T08:10:41Z"
          task_id: 93
          type: "PRE_CALL"
          update_time: "2020-06-28T08:10:40Z"
          */
          this.loading = false
          return resp
        })
    },
    handleSubmit() {
      const params = {
        file_ids: this.upload_file_list.map(item => item.id),
        id: this.task.latest_pre_ca.id,
      }
      return AppHttpService
        .patch(`task_ca/${params.id}/respond`, params)
        .then(data => {
          this.$notify({
            title: 'Success',
            message: 'CA Email Upload success!',
            type: 'success',
          })
          this.$emit('done', data)
        })
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        if (file.size > 20 * 1024 * 1024) {
          this.$message({
            message: 'The file cannot be larger than 20MB',
            type: 'error',
          })
          return reject()
        } else {
          return resolve(true)
        }
      })
    },
    handleSuccess(_res, _file, fileList) {
      if (_file.status === 'success') {
        this.upload_file_list = fileList.map(item => {
          return {
            name: item.response.name,
            url: item.response.path,
            id: item.response.id,
          }
        })
      } else {
        this.$message({
          message: 'Upload failed',
          type: 'warning',
        })
      }
    },
    beforeRemove(file, fileList) {
      if (file && file.status === 'success') {
        return this.$confirm(`Confirm to remove ${file.name}？`, 'Notice', {
          type: 'warning',
        })
      }
    },

    handleRemove(file, fileList) {
      this.upload_file_list = fileList.map(item => {
        return {
          name: item.response.name,
          url: item.response.path,
          id: item.response.id,
        }
      })
    },
  },
}
</script>

<style scoped>

</style>
