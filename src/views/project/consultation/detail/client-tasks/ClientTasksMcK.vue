<template>
  <div class="tasks">
    <slot v-if="!hideTasks">
      <div
        ref="tool_switch_box"
        class="position-relative tool-switch filter-container"
        :style="{
          minHeight: `${ceilingToolSwitchheight}px`,
          zIndex: toolSwitchZIndex
        }"
      >
        <transition name="fade">
          <div
            v-if="!selectedAdvisor.length"
            :key="1"
            ref="tool_switch_page1"
            class="inline-block tool-switch-page1"
          >
            <div>
              <edit-task-angle
                class="filter-item"
                :project="project"
                :angle.sync="angle"
                @Done="saveDone"
              />
              <outsource-team-support
                v-if="!project.exported_to || project.outsource_approvals.length"
                class="filter-item"
                :project="project"
              />
              <el-select
                v-if="angleList.length && angle_view_type==='stacked'"
                v-model="selectedAngleIds"
                multiple
                clearable
                filterable
                placeholder="Select Angle"
                class="filter-item"
                @change="handleSelectAngle">
                <el-option
                  v-for="item in angleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-input
                v-if="angle_view_type === 'stacked'"
                v-model="searchTaskIds"
                placeholder="Task ID"
                class="inline-block filter-item w-150px"
                clearable
              />
              <task-advisor-search ref="taskAdvisorSearch" :project-id="projectId" @search="searchByTaskId" />
              <advisor-search
                v-model="searchAngleAdvisorId"
                class="filter-item w-150px ml-3"
                :multiple="false"
                :initial-id="$route.query.advisor_id"
              />
              <el-input
                v-model="searchCompanyNameContains"
                :placeholder="searchCompanyIsContainsMode ? 'Keyword Contains' : 'Employer'"
                class="filter-item w-200px keyword-custom-input"
                clearable
              >
                <el-checkbox
                  slot="append"
                  v-model="searchCompanyIsContainsMode"
                >
                  <el-icon name="bottom" />
                </el-checkbox>
              </el-input>
              <el-select
                v-model="searchCompanyIsCurrent"
                class="inline-block filter-item w-150px">
                <el-option label="Currents" :value="true" />
                <el-option label="Formers" :value="false" />
                <el-option label="Currents and Formers" :value="undefined" />
              </el-select>
              <el-select
                v-model="selectedSupportIds"
                multiple
                clearable
                placeholder="Select Support"
                class="filter-item w-150px">
                <el-option
                  v-for="item in supportMembers"
                  :key="item.user_id"
                  :label="item.name"
                  :value="item.user_id"
                />
              </el-select>
              <el-select
                v-model="orderBy"
                filterable
                class="filter-item w-172px"
                @change="handleChangeOrder">
                <el-option
                  v-for="item in project_options.task.order_by"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="blacklistFilter"
                class="filter-item w-200px">
                <el-option value="BLACKLIST" label="Exclude blacklisted advisors" />
                <el-option value="DECLINED" label="Exclude declined advisors" />
                <el-option value="B&D" label="Exclude blacklisted & declined advisors" />
                <el-option value="" label="All Advisors" />
              </el-select>
              <el-button
                class="filter-item"
                type="primary"
                :loading="downloadLoading"
                icon="el-icon-download"
                @click="downloadAllTasks()"
              >Download All
              </el-button>
            </div>
          </div>
          <div
            v-else
            :key="2"
            ref="tool_switch_page2"
            v-loading="select_loading"
            :class="{
              'fixed': isFixedToolSwitch
            }"
            :style="{
              width: `${pageWidth + 4}px`,
              top: '0px'
            }"
            class="inline-block tool-switch-page2"
          >
            <refresh-button class="filter-item" @action="reloadList(true)" />
            <outreach-to-advisor
              v-if="isSurveyProject"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
              @update="clearSelectedAndReload" />
            <email-to-advisor
              v-if="!isSurveyProject && !isImportedTasks"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
              type="Advisors"
              @update="clearSelectedAndReload"
            />
            <email-to-client
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
              @update="reloadList"
            />
            <simulate-client-select-advisor
              v-if="!isSurveyProject"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
              @update="reloadList" />
            <email-to-compliance
              v-if="!isSurveyProject"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
              @update="reloadList"
            />
            <copy-portal-link
              v-if="!isSurveyProject"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
            />
            <copy-availability
              v-if="!isSurveyProject"
              class="filter-item"
              :tasks.sync="selectedAdvisor"
              :project="project"
            />
            <attach-to-project
              class="filter-item"
              :selected-advisors="selectedAdvisor"
              :project-id="project.id"
              type="clientTask"
            />
            <move-to-angle
              class="filter-item"
              :project-id="project.id"
              :angle-list="angleList"
              :selected-advisors="selectedAdvisor"
              @done="moveTasksDone"
            />
            <remove-task
              class="filter-item"
              :selected-advisors="selectedAdvisor"
              @done="removeTasksDone"
            />
            <el-button
              class="filter-item"
              type="primary"
              :loading="downloadLoading"
              icon="el-icon-download"
              @click="downloadTask()"
            >Download
            </el-button>
            <el-tooltip
              v-if="!isSurveyProject"
              :disabled="!arrange_disabled"
              :content="arrange_disabled_text"
              placement="top"
              class="filter-item inline-block">
              <div>
                <el-button
                  type="success"
                  :disabled="arrange_disabled"
                  icon="el-icon-date"
                  @click="goToTasksArrange"
                >Arrange
                </el-button>
              </div>
            </el-tooltip>
            <copy-task-links
              v-if="!isSurveyProject"
              :tasks="selectedAdvisor"
              class="filter-item inline-block"
            />
            <advisor-consent-to-recording
              v-if="!isSurveyProject && !isSGDB"
              :disabled="selectedAdvisor.length > 1"
              class="filter-item"
              :task="selectedAdvisor[0]"
            />
            <add-to-cart :advisors="selectedAdvisor" class="filter-item" />
            <export-to-outsource v-if="project.exported_to" :tasks="selectedAdvisor" :project="project" class="filter-item" @update="reloadList" />
            <complete-c-a v-if="selectedAdvisor.length===1" :task-data="selectedAdvisor[0]" class="filter-item" @update="reloadList" />
            <generate-ca-link v-if="selectedAdvisor.length===1" :task-data="selectedAdvisor[0]" class="filter-item" @update="reloadList" />
          </div>
        </transition>
      </div>

      <!--监听项目event并提示未读数据-->
      <ProjectWebSocketAlert tab-key="client_task_ids" />

      <!-- <tasks :angle="{name: 'Default Angle', id: 0}" /> -->
      <loading-status v-loading="loading" :status="loading" :data="angleList" />

      <div v-if="angleList.length">
        <el-tabs v-model="angle_view_type" class="angle-view-type" @tab-click="viewTabClick">
          <el-tab-pane name="stacked" lazy class="angles">
            <span slot="label"><i class="el-icon-s-data" /> Stacked View</span>
            <template v-for="(data, index) in angleList">
              <task-angle-table
                v-show="!selectedAngleIds.length || selectedAngleIds.includes(data.id)"
                :ref="`taskAngleTable${data.id}`"
                :key="data.id"
                v-loading="loading"
                :lazy="true"
                :angle="data"
                :blacklist-filter="blacklistFilter"
                :sequence="index + 1"
                :order-by="orderBy"
                :hide-on-page="!!selectedAngleIds.length && !selectedAngleIds.includes(data.id)"
                :select-loading.sync="select_loading"
                :search-angle-task-id="searchAngleTaskId"
                :search-task-ids="searchTaskIds"
                :search-angle-advisor-id="searchAngleAdvisorId"
                :search-company-name-contains="searchCompanyNameContains"
                :search-company-is-contains-mode="searchCompanyIsContainsMode"
                :search-company-is-current="searchCompanyIsCurrent"
                :search-support-ids="selectedSupportIds"
                :screened="screened"
                @downloadAngleTask="downloadTask(data.id)"
                @delete="deleteAngle"
                @clearSearchByTaskId="clearSearchByTaskId"
                @updateTableColsWidth="updateTasksTableColsWidth"
                @updateMedianRate="getList"
              />
            </template>
          </el-tab-pane>
          <el-tab-pane name="tabbed" lazy>
            <span slot="label"><i class="el-icon-folder" /> Tabbed View</span>
            <el-tabs v-model="active_angle_id" type="card" class="angle-tabs">
              <el-tab-pane v-for="(data,index) in angleList" :key="data.id" :name="data.id_string" :label="data.name" lazy>
                <span slot="label" class="overflow-three">{{data.name}}</span>
                <task-angle-table
                  :ref="`taskAngleTable${data.id}`"
                  :key="data.id"
                  :lazy="true"
                  :angle="data"
                  :blacklist-filter="blacklistFilter"
                  :sequence="index + 1"
                  :order-by="orderBy"
                  :select-loading.sync="select_loading"
                  :search-angle-task-id="searchAngleTaskId"
                  :search-task-ids="searchTaskIds"
                  :search-support-ids="selectedSupportIds"
                  :search-angle-advisor-id="searchAngleAdvisorId"
                  :search-company-name-contains="searchCompanyNameContains"
                  :search-company-is-contains-mode="searchCompanyIsContainsMode"
                  :search-company-is-current="searchCompanyIsCurrent"
                  :screened="screened"
                  @clearSearchByTaskId="clearSearchByTaskId"
                  @downloadAngleTask="downloadTask(data.id)"
                  @delete="deleteAngle"
                  @updateTableColsWidth="updateTasksTableColsWidth"
                  @updateMedianRate="getList"
                />
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
        </el-tabs>
      </div>
      <download-task ref="taskDownload" @update="downloadLoading=false" />
    </slot>
    <router-view />
  </div>
</template>

<script>
import PopupManager from 'element-ui/lib/utils/popup/popup-manager'
import ProjectAPI from '@/api/project'
import detailMixins from '../mixins/mixins'
import TaskAngleTable from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/McKTaskAngle'
import EditTaskAngle from '@/views/project/consultation/detail/client-tasks/components/EditTaskAngle'
import RefreshButton from '@/components/RefreshButton'
import EmailToAdvisor from '@/views/project/consultation/detail/components/SendEmail/EmailToAdvisor_v2'
import EmailToClient from '@/views/project/consultation/detail/components/SendEmail/EmailToClient_v2'
import EmailToCompliance from '@/views/project/consultation/detail/components/SendEmail/EmailToCompliance_v2'
import CopyPortalLink from '@/views/project/consultation/detail/components/CopyPortalLink'
import CopyAvailability from '@/views/project/consultation/detail/components/CopyAvailability'
import AttachToProject from '@/views/project/consultation/detail/components/AttachToProject'
import MoveToAngle from '@/views/project/consultation/detail/client-tasks/components/MoveToAngle'
import RemoveTask from '@/views/project/consultation/detail/components/RemoveTask'
import LoadingStatus from '@/components/LoadingStatus'
import OutreachToAdvisor from '@/views/project/consultation/detail/components/SendEmail/OutreachToAdvisor'
import SimulateClientSelectAdvisor from '@/views/project/consultation/detail/client-tasks/components/SimulateClientSelectAdvisor'
import CopyTaskLinks from '@/views/project/consultation/detail/client-tasks/components/CopyTaskLinks'
import TaskAdvisorSearch from '@/views/project/consultation/detail/components/TaskAdvisorSearch'
import { mapGetters, mapState } from 'vuex'
import Sortable from 'sortablejs'
import { insertNodeAt, removeNode } from '@/utils/tool'
import DownloadTask from '@/views/project/consultation/detail/components/DownloadTask'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import AdvisorConsentToRecording from '@/views/project/consultation/detail/client-tasks/components/AdvisorConsentToRecording'
import AddToCart from '@/components/ShoppingCart/AddToCart'
import ExportToOutsource from '@/views/project/consultation/detail/client-tasks/components/ExportToOutsource.vue'
import OutsourceTeamSupport from '@/views/project/consultation/detail/client-tasks/components/OutsourceTeamSupport.vue'
import CompleteCA from '@/views/project/consultation/detail/client-tasks/components/Outsource/CompleteCA'
import GenerateCaLink from '@/views/project/consultation/detail/client-tasks/components/Outsource/GenerateCaLink'

export default {
  name: 'ClientTasksMcK',
  components: {
    SimulateClientSelectAdvisor,
    LoadingStatus,
    TaskAngleTable,
    EditTaskAngle,
    RefreshButton,
    EmailToAdvisor,
    EmailToClient,
    EmailToCompliance,
    CopyPortalLink,
    CopyAvailability,
    AttachToProject,
    MoveToAngle,
    RemoveTask,
    OutreachToAdvisor,
    CopyTaskLinks,
    TaskAdvisorSearch,
    DownloadTask,
    AdvisorSearch,
    AdvisorConsentToRecording,
    AddToCart,
    ExportToOutsource,
    OutsourceTeamSupport,
    CompleteCA,
    GenerateCaLink,
  },
  mixins: [detailMixins],
  data() {
    return {
      select_loading: false,
      downloadLoading: false,
      project: this.$store.state.project.data,
      projectId: this.$route.params.project_id,
      client_is_execute: true,
      arrange_disabled: true,
      arrange_disabled_text: 'The client is not in the execution state and cannot schedule a call.',
      loading: false,
      searchQuery: {
        project_id: this.$route.params.project_id,
        page: 1,
        size: 100,
        extra: [
          'members.user',
          'related_email_template',
        ].join(),
      },
      btnText: 'New Task Angle',
      editDialogShow: false,
      angle: {
        name: '',
        inquiry_branch_id: '',
        supporter: [],
      },
      angleList: [],
      toolSwitchZIndex: 0,
      isFixedToolSwitch: false,
      pageWidth: 0,
      ceilingToolSwitchTop: 0,
      ceilingToolSwitchheight: 0,
      selectedAngleIds: [],
      active_angle_id: null,
      angle_view_type: null,
      selectedSupportIds: [],
      searchTaskIds: null,
      searchAngleTaskId: null,
      searchAngleAdvisorId: null,
      searchCompanyNameContains: '',
      searchCompanyIsContainsMode: false,
      searchCompanyIsCurrent: undefined,
      orderBy: 'rank asc',
      blacklistFilter: '',
    }
  },
  computed: {
    ...mapState({
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
      GenerationIMId: state => state.app.GenerationIMId,
      userInfo: state => state.user.info,
      isSGDB: state => state.app.isSGDB,
      projectInfo: state => state.project.data,
    }),
    ...mapGetters([
      'project_options',
    ]),
    selectedAdvisor: {
      get() {
        return this.$store.state.tasks.selectionList
      },
      set() {},
    },
    hideTasks() {
      return (
        this.$route.name === 'TasksArrange' ||
        this.$route.name === 'TaskComplete' ||
        this.$route.name === 'SurveyTaskComplete' ||
        this.$route.name === 'PatientsTaskComplete'
      )
    },
    isSurveyProject() {
      return this.project.sub_type === 'Survey'
    },
    isImportedTasks() {
      return this.selectedAdvisor.some(item => item.task_outsource_status === 'IMPORTED')
    },
  },
  watch: {
    'selectedAdvisor': {
      handler(val) {
        if (val.length) {
          this.$nextTick(() => {
            this.ceilingToolSwitch()
            this.checkArrangeDisabled()
          })
        }
      },
    },
  },
  created() {
    this.init()
    window.addEventListener('beforeunload', this.saveDataToSessionStorage)
  },
  mounted() {
    if (this.hideTasks) {
      return
    }
    this.loading = true
    this.getList()
      .then(() => {
        this.$nextTick(() => {
          this.loadSortable()
        })
      })
    this.addCeilingToolSwitch()
  },
  beforeDestroy() {
    this.$store.dispatch('tasks/CLEAR_SELECTION_LIST')
    this.removeCeilingToolSwitch()
    this.saveDataToSessionStorage()
    window.removeEventListener('beforeunload', this.saveDataToSessionStorage)
  },
  methods: {
    async init() {
      const query = this.$route.query
      if (query.task_id && !query.angle_id) {
        const data = await ProjectAPI.getTaskDetail({ project_id: this.projectId, id: query.task_id })
        if (!data.angle_id) {
          await this.$router.replace({
            name: 'ProjectLeads',
            query: {
              task_id: query.task_id,
            },
          })
        } else {
          this.selectedAngleIds = [parseInt(query.angle_id)]
          this.active_angle_id = data.angle_id
        }
      }

      if (query.advisor_id) {
        this.searchAngleAdvisorId = parseInt(query.advisor_id)
      }

      // selectedAngleIds/angle_view_type 以路由携带参数优先，其次取浏览器session storage中存储的值 最后取初始默认值
      if (query.angle_id) {
        this.selectedAngleIds = [parseInt(query.angle_id)]
        this.active_angle_id = query.angle_id
      }

      if (query.angle_ids) {
        this.selectedAngleIds = query.angle_ids.split(',').map(item => parseInt(item))
        // angle 多选只适用于 'stacked'
        this.angle_view_type = 'stacked'
      }

      const session_storage = JSON.parse(sessionStorage.getItem(this.session_key))

      if (!this.angle_view_type || parseInt(this.angle_view_type) === 0) {
        this.angle_view_type = session_storage?.view_type || (this.isSurveyProject ? 'tabbed' : 'stacked')
      }

      if (!this.searchTaskIds) {
        this.searchTaskIds = session_storage?.selected_task_ids || null
      }

      if (!this.selectedAngleIds.length) {
        this.selectedAngleIds = session_storage?.selected_angle_ids || []
      }

      if (!this.active_angle_id || parseInt(this.active_angle_id) === 0) {
        this.active_angle_id = session_storage?.active_angle_id || null
      }

      const { preference } = this.userInfo
      if (!preference) return
      this.orderBy = preference.client_task_page_order_by
    },

    getList() {
      this.loading = true
      return ProjectAPI.getAngleList(this.searchQuery)
        .then(data => {
          this.angleList = data.list.map(item => {
            item.id_string = item.id.toString()
            return item
          })
          // el-tab 赋值null 会转成'0'，待修复
          if (!this.active_angle_id || parseInt(this.active_angle_id) === 0) {
            this.active_angle_id = this.angleList[0]?.id_string || null
          }
          this.$store.dispatch('project/updateInfo', { ...this.projectInfo, angles: this.angleList })
        })
        .finally(() => {
          this.loading = false
        })
    },
    saveDone() {
      this.getList()
    },

    handleChangeOrder() {
      this.$nextTick(() => {
        if (this.selectedAngleIds?.length) {
          this.handleSelectAngle(this.selectedAngleIds)
        } else {
          this.reloadList(true)
        }
      })
    },

    deleteAngle(angle_id, index) {
      this.$store.dispatch('tasks/REMOVE_SELECTION_ANGLE', angle_id)
      this.angleList.splice(index + 1, 1)
      this.$message({
        message: 'Discard successful',
        type: 'success',
      })
    },
    addCeilingToolSwitch() {
      this.$nextTick(() => {
        this.pageWidth = this.$el.clientWidth - 16
        // PopupManager默认2000与loading-mask冲突
        this.toolSwitchZIndex = PopupManager.nextZIndex() + 1
        var el = document.querySelector('.tool-switch-page1')
        this.ceilingToolSwitchTop = el.getBoundingClientRect().top
        document.addEventListener('scroll', this.ceilingToolSwitch)
        window.addEventListener('resize', this.resizeWindow)
      })
    },
    resizeWindow() {
      this.pageWidth = this.$el.clientWidth
    },
    ceilingToolSwitch() {
      if (!this.selectedAdvisor.length) {
        return
      }
      var el = this.$refs.tool_switch_page2

      if (this.selectedAdvisor.length &&
        (document.documentElement.scrollTop > this.ceilingToolSwitchTop) &&
        !this.isFixedToolSwitch) {
        this.ceilingToolSwitchheight = el.clientHeight
        this.isFixedToolSwitch = true
        return
      }
      if (this.selectedAdvisor.length &&
        (document.documentElement.scrollTop < this.ceilingToolSwitchTop)) {
        this.ceilingToolSwitchheight = 0
        this.isFixedToolSwitch = false
      }
    },
    removeCeilingToolSwitch() {
      document.removeEventListener('scroll', this.ceilingToolSwitch)
      window.removeEventListener('resize', this.resizeWindow)
    },
    checkArrangeDisabled() {
      if (!this.selectedAdvisor.length) {
        return true
      }
      this.client_is_execute = this.selectedAdvisor[0].client.status === 'EXECUTE'
      const isGenerationIM = [this.GenerationGrowthEquityId, this.GenerationIMId].includes(
        this.selectedAdvisor[0].client.id)
      const no_expert_code = this.selectedAdvisor.filter(item => {
        return !item.client_custom_fields || !item.client_custom_fields['task_expert_code']
      })
      const has_blacklisted_expert = this.selectedAdvisor.filter(item => {
        return item.advisor.status === 'BLACKLIST'
      }).length > 0
      const GenerationIMNeedExpertCode = isGenerationIM && no_expert_code.length > 0
      if (GenerationIMNeedExpertCode) this.arrange_disabled_text = 'There is no Expert code for the presence of a task.'
      if (has_blacklisted_expert) this.arrange_disabled_text = 'Contains blacklisted experts.'
      this.arrange_disabled = !this.client_is_execute || GenerationIMNeedExpertCode || has_blacklisted_expert
    },

    downloadTask(angle_id = 0) {
      this.downloadLoading = true
      this.$refs.taskDownload.checkBeforeDownload(this.project, 'ADVISOR_RECOMMEND', false, angle_id, this.selectedAdvisor, this.blacklistFilter)
    },
    downloadAllTasks() {
      this.downloadLoading = true
      const allAngles = this.angleList.map(item => item.id)
      this.$refs.taskDownload.checkBeforeDownload(this.project, 'ADVISOR_RECOMMEND', false, allAngles, this.selectedAdvisor, this.blacklistFilter)
    },
    goToTasksArrange() {
      /*
      当只操作一个 Task 时，警告信息用弹窗显示
      当操作多个 Task 时，如有通过检测的则跳转
       */
      const not_passed_tasks = checkIsOkToArrange(this.selectedAdvisor)
      const is_single_not_passed =
        this.selectedAdvisor.length === 1 && not_passed_tasks.length === 1

      if (is_single_not_passed) {
        this.$alert(
          'Task ' +
          this.selectedAdvisor[0].id +
          ' is not allow to Arrange due it has been Completed',
          'Cannot Arrange',
        )
      } else {
        this.$router.push({
          name: 'TasksArrange',
          query: {
            id: this.selectedAdvisor.map((item) => item.id)
              .join(','),
            client_id: this.selectedAdvisor[0].client_id,
          },
        })
      }

      function checkIsOkToArrange(tasks) {
        const not_passed_tasks = []

        tasks.forEach((item) => {
          if (taskCannotArrange(item)) {
            not_passed_tasks.push(item)
          }
        })

        return not_passed_tasks

        /**
         * 已经被 Complete 不可再 Arrange
         * @param data
         * @returns {boolean}
         */
        function taskCannotArrange(data) {
          return ['COMPLETED'].includes(data.general_status)
        }
      }
    },

    searchByTaskId(angle_task_id) {
      this.selectedAngleIds = [+angle_task_id.split('-')[0]] || []
      this.searchAngleTaskId = angle_task_id || null
    },

    clearSearchByTaskId() {
      if (this.searchAngleTaskId) {
        this.$refs['taskAdvisorSearch'].clearSearch()
        this.searchAngleTaskId = null
      }
    },

    loadSortable() {
      const options = {
        handle: '.cursor-move',
        draggable: '.angle',
        onStart: () => {
          this.angleList.forEach(item => {
            this.$refs[`taskAngleTable${item.id}`][0].isCollapse = false
          })
        },
        onEnd: ({ newIndex, oldIndex, item, from }) => {
          if (newIndex === oldIndex) return

          const dragAngle = this.angleList[oldIndex]
          const prevAngleIndex = newIndex - oldIndex > 0 ? newIndex : newIndex - 1
          const rank = newIndex === 0 ? 0.5 : prevAngleIndex + 1.5

          return ProjectAPI.customizeSortTaskAngle({
            project_id: this.project.id,
            id_position_map: {
              [`${dragAngle.id}`]: rank,
            },
          }).then(data => {
            // 避免sortable.js操作DOM 还原DOM
            // 强制使用vue更新页面
            removeNode(item)
            insertNodeAt(from, item, oldIndex)

            var id_map = {}
            data.forEach(item => {
              id_map[item.id] = item
            })

            this.angleList.forEach(task => {
              task.display_order = id_map[task.id]['customize_order']['display_order']
            })

            this.angleList.sort((a, b) => a.display_order - b.display_order)
          })
        },
      }

      const angles_els = this.$el.querySelector('.angles')
      if (angles_els) this._sortable = new Sortable(angles_els, options)
    },
  },
}
</script>

<style lang="scss" scoped>
.tasks {
  .stripe-table {
    border: 1px solid #EBEEF5;
    border-bottom: 0;

    thead .gutter {
      display: table-cell !important;
    }
  }

  .el-table__row:hover {
    .dropdown-link .el-icon--right {
      display: inline-block;
    }
  }

  .dropdown-link {
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;

    .el-icon--right {
      display: none;
    }
  }
}

.tool-switch {
  box-sizing: content-box;

  .tool-switch-page2 {
    background: #fff;
  }
}

::v-deep .angle-view-type .el-tabs__nav{
  float: right;
}

.angle-view-type {
  ::v-deep .el-tabs__item {
    color:#797b80;
  }

  ::v-deep.el-tabs__item.is-active {
    color: #409EFF;
  }
}

.angle-tabs{
  ::v-deep .el-tabs__nav{
    display: flex;
    float: left;
  }
  ::v-deep .el-tabs__nav-scroll{
    overflow:auto;
  }
  ::v-deep .el-tabs__item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    line-height: 1.2rem;
    max-height:5rem;
    height:inherit;
    max-width:200px;
  }
}

.keyword-custom-input {
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }

  ::v-deep .el-checkbox__input {
    display: none;
  }

  ::v-deep .el-checkbox__label {
    padding-left: 0; // 移除label的左边距
  }
}
</style>
