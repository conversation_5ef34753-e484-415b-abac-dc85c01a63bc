<template>
  <el-button type="primary" @click="copyTaskLinks">Copy Task Links</el-button>
</template>

<script>
import { copyUtil } from '@/utils/tool'

export default {
  name: 'CopyTaskLinks',
  props: {
    tasks: Array,
  },
  methods: {
    copyTaskLinks() {
      const links = this.tasks.map(item => {
        const _url = window.location.origin +
          `/#/project/consultation/${item.project_id}/tasks?angle_id=${item.angle_id}&task_id=${item.id}`

        return `<tr><td><u><span lang="EN-US" style="font-size:11.0pt;color:#0563C1"><a href="${_url}">${item.advisor.name_prefix || ''} ${item.advisor.full_name}</a></span></u></td></tr>`
      }).join('')

      const result = `<table><tbody>${links}</tbody></table>`

      copyUtil(result, true)
        .then((result) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
  },
}
</script>

<style scoped>

</style>
