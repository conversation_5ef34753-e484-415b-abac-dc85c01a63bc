<template>
  <div class="inline-block">
    <el-button
      type="primary"
      size="mini"
      :disabled="outsourceIsNotApproved"
      @click="action"
    >
      <svg-icon icon-class="export" />
      Export To Outsource
    </el-button>

    <el-dialog
      :visible.sync="confirmDialogVisible"
      append-to-body
      title="Confirm to export those advisors to outsource?"
      width="620px"
    >
      <div>
        <el-alert
          v-if="filteredTasks.length"
          title="These experts will be filtered, because TC expired."
          type="warning"
          show-icon
          class="mb-8">
          <span v-for="(item, index) in filteredTasks" :key="index">
            <router-link
              class="text-truncate link"
              :to="{name: 'ConsultantDetail', params: {advisor_id: item.advisor.id}}"
            >
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.firstname }}
              {{ item.advisor.lastname }}
              #{{ item.display_id }}
            </router-link><span v-if="index < filteredTasks.length - 1">, </span>
          </span>
        </el-alert>
        <p v-if="hasPassTasks">
          <span v-for="(item, index) in passTasks" :key="index">
            <router-link
              class="text-truncate link"
              :to="{name: 'ConsultantDetail', params: {advisor_id: item.advisor.id}}"
            >
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.firstname }}
              {{ item.advisor.lastname }}
              #{{ item.display_id }}
            </router-link><span v-if="index < passTasks.length - 1">, </span>
          </span>
        </p>
        <p v-else
           class="info"
           style="font-style: italic">
          No advisor can be exported.
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-finished"
          :loading="isLoading"
          :disabled="!hasPassTasks"
          @click="actionSubmit"
        >
          Select
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="alertDialogVisible"
      append-to-body
      title="Notice"
    >
      <el-alert
        title="The following tasks failed to export:"
        type="error"
        show-icon
        :closable="false">
        <ul>
          <li v-for="item in failedTasks" :key="item.task_id">
            ID: {{ item.task_id }}, {{ item.error_message }}
          </li>
        </ul>
      </el-alert>
      <div slot="footer" class="dialog-footer">
        <el-button @click="alertDialogVisible = false">Close</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import OutsourceAPI from '@/api/outsource'

export default {
  name: 'ExportToOutsource',
  props: {
    tasks: Array,
    project: Object,
  },
  data() {
    return {
      confirmDialogVisible: false,
      alertDialogVisible: false,
      isLoading: false,
      passTasks: [],
      filteredTasks: [],
      failedTasks: [],
    }
  },
  computed: {
    hasPassTasks() {
      return this.passTasks.length !== 0
    },
    outsourceIsNotApproved() {
      if (this.project?.outsource_approvals?.length) {
        const approval = this.project.outsource_approvals.find(item => item.is_latest_approval)
        return approval.outsource_approval !== 'APPROVED'
      }
    }
  },
  methods: {
    action() {
      this.reset()
      this.filterTaskList()
      this.confirmDialogVisible = true
    },

    reset() {
      this.passTasks = []
      this.filteredTasks = []
    },

    /**
     * [过滤器] 保留可 pick task
     */
    filterTaskList() {
      this.tasks.forEach(item => {
        if (!['READY_TO_EXPORT'].includes(item.task_outsource_status)) return
        if (item.advisor.is_outsource_statistic_advisor) return
        if (item.advisor.tc_term_valid['CONSULTATION']) {
          this.passTasks.push(item)
        } else {
          this.filteredTasks.push(item)
        }
      })
    },

    actionSubmit() {
      const params = {
        project_id: this.project.id,
        task_ids: this.passTasks.map(item => item.id),
        target_region: this.project.exported_to,
      }

      this.isLoading = true
      return OutsourceAPI.exportTasksToOutsource(params)
        .then(data => {
          this.confirmDialogVisible = false

          this.failedTasks = data.failed_tasks
          if (this.failedTasks?.length) {
            this.$nextTick(() => {
              this.alertDialogVisible = true
            })
          } else {
            this.$message({
              type: 'success',
              message: `Success to Export!`,
            })
          }
          this.$emit('update')
        })
        .finally(() => {
          this.isLoading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
