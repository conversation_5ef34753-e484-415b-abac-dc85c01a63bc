<template>
  <div class="inline-block">
    <el-button-group>
      <el-button
        v-if="!project.exported_to"
        type="primary"
        @click.stop="actionOpenDialog"
      >
        <i class="el-icon-s-promotion" />
        Outsource Team Support
      </el-button>
      <el-button
        v-if="project.outsource_approvals.length"
        type="primary"
        @click.stop="approvalsDialogVisible= true"
      >
        <i class="el-icon-s-comment" />
        Outsource Approvals
      </el-button>
    </el-button-group>

    <el-dialog
      :visible.sync="dialogVisible"
      title="Outsource Team Support"
      append-to-body
      @open="handleOpenDialog"
      @opened="autoFocus"
      @closed="resetData"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="150px"
        size="mini"
        :disabled="submitting">
        <el-form-item label="Screening Questions">
          <select-project-sq
            v-model="form.sq_branch_id"
            class="multiple-select"
            placeholder="Search"
            clearable
            :project="project" />
        </el-form-item>
        <el-form-item label="Outsource Team">
          <el-select
            v-model="form.request_region"
            placeholder="Search"
            class="w-100"
          >
            <el-option
              v-for="region in options.region"
              :key="region"
              :label="region"
              :value="region"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Request Content">
          <tinymce v-model="form.request_content" :toolbar="toolbar" :height="300" is-email />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitting" @click="onSave">Confirm</el-button>
      </div>
    </el-dialog>

    <email-template
      v-if="rules"
      ref="rulesView"
      :rules="rules"
      class="display-none" />

    <el-dialog
      :visible.sync="approvalsDialogVisible"
      title="Outsource Approvals"
      append-to-body
      width="70%"
      @open="handleOpenEventsDialog"
    >
      <outsource-approval-table
        v-loading="approvalsDialogLoading"
        :data.sync="outsourceApprovals"
        in-project
        class="approvals-dialog-body"
        @reply="handleOpenEventsDialog"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalsDialogVisible = false">Close</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import OutsourceAPI from '@/api/outsource'
import Tinymce from '@/components/Tinymce'
import SelectProjectSq from '@/views/project/consultation/detail/client-tasks/components/SelectProjectSq.vue'
import Mixin from '@/components/FormPage/mixin'
import EmailTemplate from '@/views/client/detail/compliance-preference/component/EmailTemplate.vue'
import OutsourceApprovalTable from '@/views/compliance/outsource-approval/components/OutsourceApprovalTable.vue'

export default {
  name: 'OutsourceTeamSupport',
  components: { Tinymce, SelectProjectSq, EmailTemplate, OutsourceApprovalTable },
  mixins: [Mixin],
  data() {
    return {
      project: this.$store.state.project.data,
      toolbar: ['fontselect fontsizeselect | undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      dialogVisible: false,
      submitting: false,
      form: this.initForm(),
      options: {
        region: [],
      },
      rules: null,

      approvalsDialogLoading: false,
      approvalsDialogVisible: false,
      outsourceApprovals: [],
    }
  },
  mounted() {
    this.rules = this.project.client?.compliance_preference?.rule.compliance_rules
  },
  methods: {
    initForm() {
      return {
        sq_branch_id: '',
        request_region: '',
        request_content: '',
      }
    },
    actionOpenDialog() {
      if (this.project.client?.compliance_status !== 'APPROVED') {
        return this.$message({
          message: 'The Client Compliance status must be approved.',
          type: 'error',
        })
      }
      this.dialogVisible = true
    },
    async getOutsourceRegions() {
      this.options.region = await OutsourceAPI.getAvailableRegions()
      this.form.request_region = this.options.region[0]
    },
    handleOpenDialog() {
      this.initForm()
      this.getOutsourceRegions()
    },
    handleSave() {
      const params = {
        project_id: this.project.id,
        sq_branch_id: this.form.sq_branch_id || undefined,
        request_region: this.form.request_region,
        request_content: this.form.request_content,
        client_event_rules: this.rules
          ? this.$refs['rulesView']?.$el?.querySelector('#expertRestrictions')?.innerHTML
          : undefined,
      }

      return OutsourceAPI.requestOutsourceTeamSupport(params)
        .then(() => {
          this.$message({
            type: 'success',
            message: `Success to Request ${this.form.request_region} Team support!`,
          })
          this.dialogVisible = false
          this.getProjectInfo()
        })
    },

    async handleOpenEventsDialog() {
      try {
        this.approvalsDialogLoading = true
        await this.getProjectInfo()

        const outsource_approvals = [...this.project.outsource_approvals]
        this.outsourceApprovals = outsource_approvals.map(item => {
          item.expandActiveTab = 'events'
          item.events = item.events.sort((a, b) => b.id - a.id)
          return item
        })
      } finally {
        this.approvalsDialogLoading = false
      }
    },
    async getProjectInfo() {
      await this.$store.dispatch('project/getInfo', this.project.id)
      this.project = this.$store.state.project.data
    },
  },
}
</script>

<style scoped lang="scss">
.approvals-dialog-body {
  max-height: calc(85vh - 150px);
  overflow: auto
}
</style>
