<template>
  <div class="inline-block">
    <el-button type="primary"
               size="mini"
               icon="el-icon-finished"
               :loading="loading"
               @click="action">Client Select
    </el-button>

    <el-dialog :visible.sync="confirmDialogVisible"
               append-to-body
               title="Confirm to select those advisor?"
               width="620px">
      <div>
        <el-alert
          v-if="hasPassTasks"
          type="success"
          show-icon
          :closable="false"
        >
          <template #title>
            <div>
              <span v-for="(task,_index) in passTasks" :key="task.id">
                <router-link
                  class="text-truncate link"
                  :to="{name: 'ConsultantDetail', params: {advisor_id: task.advisor.id}}">
                  {{ task.advisor.name_prefix || '' }}
                  {{ task.advisor.firstname }}
                  {{ task.advisor.lastname }}
                  #{{ task.display_id }}
                </router-link><span v-if="_index < passTasks.length - 1">, </span>
              </span>
            </div>
          </template>
        </el-alert>

        <template v-for="(item, key) in filterTasks">
          <el-alert
            v-if="item.list.length"
            :key="key"
            type="warning"
            show-icon
            :closable="false"
            class="mt-8"
          >
            <template #title>
              <div class="mb-5px"><b>{{ item.message }}</b></div>
              <div>
              <span v-for="(task,_index) in item.list" :key="task.id">
                <router-link
                  class="text-truncate link"
                  :to="{name: 'ConsultantDetail', params: {advisor_id: task.advisor.id}}">
                  {{ task.advisor.name_prefix || '' }}
                  {{ task.advisor.firstname }}
                  {{ task.advisor.lastname }}
                  #{{ task.display_id }}
                </router-link><span v-if="_index < item.list.length - 1">, </span>
              </span>
              </div>
            </template>
          </el-alert>
        </template>

        <el-alert
          v-if="!hasPassTasks && !hasFilteredTasks"
          type="info"
          show-icon
          :closable="false"
        >
          <template #title>
            <b style="font-style: italic">All advisors have been selected.</b>
          </template>
        </el-alert>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary"
                   icon="el-icon-finished"
                   :loading="submitting"
                   :disabled="!hasPassTasks"
                   @click="actionSubmit">Select
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { AppHttpService } from '@/utils/request'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'

export default {
  name: 'SimulateClientSelectAdvisor',
  mixins: [TaskActionsMixin],
  props: {
    tasks: Array,
    project: Object,
  },
  data() {
    return {
      loading: false,
      submitting: false,
      confirmDialogVisible: false,
      task_action_type: 'Client Select',
      passTasks: [],
      filterTasks: {
        needToVettingCallTasks: {
          message: 'These experts need to complete the vetting call first.',
          list: [],
        },
        needToExportTasks: {
          message: 'Please export these tasks first.',
          list: [],
        },
        needValidNpi: {
          message: 'These experts have no valid NPI.',
          list: [],
        },
      },
    }
  },
  computed: {
    hasPassTasks() {
      return this.passTasks.length !== 0
    },

    hasFilteredTasks() {
      for (const key in this.filterTasks) {
        if (this.filterTasks[key].list.length) return true
      }
      return false
    },

    needVettingCall() {
      return this.project?.sub_type === 'Investor Call' && this.project?.client.type === 'INVESTMENT_BANK'
    },
  },
  methods: {
    action() {
      this.loading = true
      this.getTasksByIDS()
        .then(() => {
          this.reset()
          this.filterTaskList()
          this.confirmDialogVisible = true
        })
        .finally(() => {
          this.loading = false
        })
    },

    reset() {
      this.passTasks = []
      this.filterTasks.needToVettingCallTasks.list = []
      this.filterTasks.needToExportTasks.list = []
      this.filterTasks.needValidNpi.list = []
    },

    /**
     * [过滤器] 保留可 pick task
     */
    filterTaskList() {
      this.tasks.forEach(item => {
        const need_to_complete_vetting_call = this.needVettingCall && !item.investor_call?.completed
        const need_to_export = item.task_outsource_status === 'READY_TO_EXPORT'
        const need_valid_npi = item.angle.tags?.includes('PHYSICIAN') && item.advisor.npi_status !== 'VALID'
        if (['INITIAL', 'SENT', 'REJECTED', 'HOLD'].includes(item.client_contact_status) &&
          !need_to_complete_vetting_call &&
          !need_to_export &&
          !need_valid_npi) {
          this.passTasks.push(item)
        } else {
          if (need_to_complete_vetting_call) {
            this.filterTasks.needToVettingCallTasks.list.push(item)
          }
          if (need_to_export) {
            this.filterTasks.needToExportTasks.list.push(item)
          }
          if (need_valid_npi) {
            this.filterTasks.needValidNpi.list.push(item)
          }
        }
      })
    },

    actionSubmit() {
      const params = {
        review_list: this.passTasks.map(item => {
          return {
            task_id: item.id,
            approved: true,
          }
        }),
      }

      this.submitting = true
      return AppHttpService
        .patch('/tasks/select/batch', params)
        .then(() => {
          this.confirmDialogVisible = false
          this.$emit('update')
        })
        .finally(() => {
          this.submitting = false
        })
    },
  },
}
</script>

<style scoped>

</style>
