<template>
  <div class="inline-block">
    <el-button
      type="primary"
      :disabled="!is_can_show_dialog"
      icon="el-icon-position"
      @click="showSelectDialog"
    >
      Move To Angle
    </el-button>

    <el-dialog
      title="Select Target Angle"
      :visible.sync="selectDialog"
      append-to-body
      :close-on-click-modal="false"
      width="480px"
      @opened="autoFocus"
    >
      <div class="flex justify-content-center align-items-center">
        <el-select
          ref="select"
          v-model="selectedID"
          filterable
          remote
          reserve-keyword
          :popper-append-to-body="false"
          placeholder="Search"
          class="w-100"
          no-data-text="No matching data"
        >
          <el-option
            v-for="item in angleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <span>{{ item.name }}</span>
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="selectDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          :disabled="!selectedID || !move_tasks.length"
          @click="activeMove"
        >OK</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'MoveToAngle',
  props: {
    selectedAdvisors: {
      type: Array,
      default: () => [],
    },
    angleList: {
      type: Array,
      default: undefined,
    },
    projectId: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      selectDialog: false,
      selectedID: '',
    }
  },
  computed: {
    is_can_show_dialog() {
      return !!this.selectedAdvisors.find(item => !this.hadSentToClient(item) || (this.hadSentToClient(item) && !item.display_id_locked))
    },
    move_tasks() {
      return this.selectedAdvisors
        .filter(item => {
          return (
            (!this.hadSentToClient(item) || (this.hadSentToClient(item) && !item.display_id_locked)) && +item.angle_id !== +this.selectedID
          )
        })
        .map(({ id }) => {
          return id
        })
    },
  },
  methods: {
    autoFocus() {
      this.$nextTick(() => {
        this.$refs.select.focus()
      })
    },
    activeMove() {
      return ProjectAPI.moveToAngle(this.projectId, {
        angle_id: this.selectedID,
        task_ids: this.move_tasks,
      }).then(() => {
        this.hideSelectDialog()
        this.$emit('done', this.selectedID)
      })
    },
    hadSentToClient(item) {
      return (
        item.client_compliance_status !== 'INITIAL' ||
        item.client_contact_status !== 'INITIAL'
      )
    },
    showSelectDialog() {
      this.selectDialog = true
    },
    hideSelectDialog() {
      this.selectDialog = false
    },
  },
}
</script>
