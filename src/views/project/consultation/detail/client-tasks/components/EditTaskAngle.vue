<template>
  <div class="inline-block">
    <el-button
      v-if="type === 'create'"
      type="success"
      @click.stop="showDialog"
    >
      <svg-icon icon-class="plus" />
      {{ btnText }}
    </el-button>

    <ico-button
      v-if="type === 'edit'"
      tooltip="Edit"
      no-border
      @action="showDialog">
      <el-icon slot="ico" class="el-icon-edit" />
    </ico-button>

    <el-dialog
      :title="dialogTitle"
      append-to-body
      width="40%"
      :close-on-click-modal="false"
      :visible.sync="dialogShow"
      @open="form = initForm()"
      @opened="autoFocus"
      @closed="resetData"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        :inline="false"
        size="mini"
        :disabled="loading">
        <el-form-item label="Task Angle Name" prop="name">
          <el-input ref="name" v-model="form.name" />
        </el-form-item>
        <el-form-item label="Support Members" prop="members">
          <el-select
            v-model="form.members"
            class="multiple-select"
            placeholder="search"
            value-key="user_id"
            multiple
            clearable
            filterable>
            <el-option
              v-for="item in project_members"
              :key="item.user_id"
              :label="item.name"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Clients to Receive Calendar Invites">
          <el-select
            v-model="form.calendar_invite_to_emails"
            class="multiple-select"
            multiple
            filterable>
            <el-option
              v-for="item in project_client_contact_email_list"
              :key="item.email"
              :label="item.label"
              :value="item.email" />
          </el-select>
        </el-form-item>
        <el-form-item label="Screening Questions">
          <select-project-sq
            v-model="form.inquiry_branch_id"
            class="multiple-select"
            placeholder="Search"
            clearable
            :project="project"
            sq-alert-visible
          />
        </el-form-item>
        <div v-if="project.sub_type === 'Survey'">
          <el-form-item label="Outreach">
            <el-select
              v-model="form.related_email_template_id"
              placeholder="search"
              :loading="templateLoading"
              value-key="id"
              filterable>
              <el-option
                v-for="item in templateList"
                :key="item.id"
                :label="item.name"
                :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="Survey Honorarium">
            <el-checkbox v-model="follow_rate">Follow the project honorarium
              <span v-if="project.rate!==null"> ({{ project.rate }} / {{ project.rate_currency }})</span>
            </el-checkbox>
            <el-input-number v-model="form.rate" :disabled="follow_rate" class="w-120px" />
            <el-select
              v-model="form.rate_currency"
              placeholder="Currency"
              :disabled="follow_rate"
              class="w-120px ml-3">
              <el-option
                v-for="item in advisor_options.currency"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="Cost Per Interview Override" prop="cost_per_interview_override">
          <el-input-number v-model="form.client_rate" :min="0" class="w-120px inline-block" />
          USD
        </el-form-item>
        <el-form-item label="Call Opportunity #" prop="call_opportunity">
          <el-input-number v-model="form.call_opportunity" :min="undefined" class="w-120px inline-block" />
        </el-form-item>
        <el-form-item label="Angle Topic" prop="topic">
          <el-input v-model="form.topic" />
        </el-form-item>
        <el-form-item label="Discussion Topics">
          <el-input v-model="form.discussion_topic" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div class="flex justify-content-between">
          <div>
            <el-button v-if="angle.id" type="danger" @click="onDiscard">Discard</el-button>
          </div>
          <div>
            <el-button type="primary" :loading="submitting" @click="onSave">OK</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import Mixin from '@/components/FormPage/mixin'
import SelectProjectSq from './SelectProjectSq'
import IcoButton from '@/components/IcoButton'
import { mapGetters } from 'vuex'
import EmailAPI from '@/api/email'
import _ from 'lodash'

export default {
  name: 'EditTaskAngle',
  components: {
    SelectProjectSq,
    IcoButton,
  },
  mixins: [Mixin],
  props: {
    angle: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: 'create',
    },
  },
  data() {
    return {
      project: this.$store.state.project.data,
      project_members: this.initProjectMember(),
      btnText: 'Task Angle',
      dialogTitle: `${this.angle.id ? 'Edit' : 'New'} Task Angle`,
      dialogShow: false,
      loading: false,
      form: this.initForm(),
      templateList: [],
      templateLoading: false,
      rules: {
        name: [{ required: true, trigger: 'change' }],
        members: [{ required: true, message: 'support members is required', trigger: 'change' }],
        topic: [{ required: true, message: 'topic is required', trigger: 'change' }],
      },

      follow_rate: true,
      project_client_contact_email_list: [],
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
  },
  watch: {
    'follow_rate': {
      handler(newVal) {
        if (newVal) {
          this.form.rate = this.project.rate
          this.form.rate_currency = this.project.rate_currency
        } else {
          this.form.rate = this.angle.rate
          this.form.rate_currency = this.angle.rate_currency
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.follow_rate = typeof this.angle.rate !== 'number' || typeof this.form.rate !== 'number'
  },
  methods: {
    handleSave(val) {
      var params = {
        project_id: this.type === 'create' ? this.project.id : undefined,
        name: val.name,
        inquiry_branch_id: val.inquiry_branch_id,
        calendar_invite_to_emails: val.calendar_invite_to_emails.join() || null,
        members: [],
        rate: this.follow_rate ? null : val.rate,
        rate_currency: this.follow_rate ? null : val.rate_currency,
        client_rate: val.client_rate || null,
        client_rate_currency: val.client_rate ? 'USD' : null,
        call_opportunity: val.call_opportunity,
        topic: val.topic,
        discussion_topic: val.discussion_topic,
        related_email_template_id: val.related_email_template_id,
      }
      params.rate = typeof params.rate === 'number' ? params.rate : null

      return this.loadHttp(params)
        .then(data => {
          if (this.angle.id) {
            data.members.forEach(member => {
              member.user = this.form.members.find(item => item.user_id === member.user_id)
            })
            this.$emit('update:angle', data)
          }
          ProjectAPI.getProject(this.project.id, 'call_opportunity_sum_jit').then(projectData => {
            this.$store.dispatch('project/updateInfo',
              { ...this.project, call_opportunity_sum_jit: projectData.call_opportunity_sum_jit })
            this.$emit('Done')
            this.hideDialog()
          })
        })
    },
    onDiscard() {
      this.confirmDialog('Discard')
        .then(() => {
          return ProjectAPI.deleteAngle(this.angle.id)
            .then(() => {
              this.hideDialog()
              this.$emit('delete')
            })
        })
    },
    confirmDialog(status) {
      return this.$confirm(`Confirm ${status}?`, 'Warning', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
        cancelButtonClass: 'el-button--text',
      })
    },
    loadHttp(params) {
      if (!this.angle.id) {
        this.form.members.forEach(({ user_id }) => {
          params.members.push({
            user_id,
          })
        })
        return ProjectAPI.saveAngle(params)
      }

      var temp_ids = []
      this.angle.members.forEach(({ user_id, id }) => {
        if (this.form.members.some(member => member.user_id === user_id)) {
          params.members.push({
            user_id,
            id,
          })
          temp_ids.push(user_id)
        }
      })

      this.form.members.forEach(({ user_id }) => {
        if (!temp_ids.includes(user_id)) {
          params.members.push({
            user_id,
          })
        }
      })

      return ProjectAPI.updateAngle(this.angle.id, params)
    },
    initForm() {
      if (this.project) {
        this.project.project_client_contacts.forEach(item => {
          const email_list = item.client_contact.contact_infos_without_mosaic.filter(i => i.type === 'EMAIL' && i.value)
          if (email_list.length) {
            const result = email_list.map(temp => {
              return { label: `${item.client_contact.name} -- ${temp.value}`, email: temp.value }
            })
            this.project_client_contact_email_list = _.uniqBy(this.project_client_contact_email_list.concat(result), 'email')
          }
        })
      }

      const project_manager = this.$store.state.project.data.members.filter(item => item.role === 'PROJECT_MANAGER')
        .map(({ user, uid }) => ({ name: user.name, 'user_id': uid }))

      let form = {
        name: '',
        members: project_manager,
        inquiry_branch_id: '',
        rate: null,
        rate_currency: null,
        client_rate: null,
        call_opportunity: null,
        topic: '',
        discussion_topic: '',
        related_email_template_id: null,
        calendar_invite_to_emails: this.project_client_contact_email_list?.map(item => item.email) || [],
      }

      if (this.angle.id) {
        form = {
          name: this.angle.name,
          members: this.angle.members.map(({ user, user_id }) => ({ name: user.name, user_id })),
          inquiry_branch_id: this.angle.inquiry_branch_id || '',
          rate: this.angle.rate,
          rate_currency: this.angle.rate_currency,
          client_rate: this.angle.client_rate || undefined,
          call_opportunity: this.angle.call_opportunity,
          topic: this.angle.topic,
          discussion_topic: this.angle.discussion_topic,
          related_email_template_id: this.angle.related_email_template_id,
          calendar_invite_to_emails: this.angle?.calendar_invite_to_emails?.split(',') || [],
        }
        this.follow_rate = typeof form.rate !== 'number'
      }

      return form
    },

    getOutreachList() {
      const params = {
        reference_ids: this.project.id,
        reference_type: 'PROJECT',
        content_type: 'PROJECT_OUTREACH',
        has_permission: true,
        page: 1,
        size: 999,
        include_default_reference_type: true,
        uid: this.$store.state.user.uid,
      }

      this.templateLoading = true
      return EmailAPI.getEmailList(params)
        .then(data => {
          this.templateList = data.list.filter(
            item => item.tags && item.tags.includes('SURVEY_OUTREACH'))
        })
        .finally(() => {
          this.templateLoading = false
        })
    },
    initProjectMember() {
      // 项目目前的Support Members
      const project_members = this.$store.state.project.data.members
        .reduce((accumulator, { user }) => {
          // debugger
          if (!accumulator.map(({ user_id }) => user_id).includes(user.id)) {
            accumulator.push({
              name: user.name,
              user_id: user.id,
            })
          }
          return accumulator
        }, [])
      const project_member_ids = project_members.map(item => item.user_id)
      // 检查当前Angle的Support Members 是否还在项目的Support Members中
      if (!this.angle.members) return project_members
      const angle_members = this.angle.members.map(({ user, user_id }) => ({ name: user.name, user_id }))
      angle_members.forEach(item => {
        if (!project_member_ids.includes(item.user_id)) {
          project_members.unshift(item)
        }
      })
      return project_members
    },
    showDialog() {
      this.getOutreachList()
      this.dialogShow = true
    },
    hideDialog() {
      this.dialogShow = false
    },
  },
}
</script>

<style lang="scss" scoped>

.dialog-title {
  font-size: 18px;
  line-height: 24px;

  .project-name {
    color: red;
  }
}

.svg-container {
  padding: 10px;
  cursor: pointer;
}

.consultant-tag {
  margin: 5px;
  cursor: pointer;
  user-select: none;
}

.is-selected {
  background-color: #43b370;
  border-color: #43b370;
  color: #fff;
}

</style>

