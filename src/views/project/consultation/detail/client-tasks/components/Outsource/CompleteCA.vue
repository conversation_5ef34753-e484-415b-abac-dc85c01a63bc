<template>
  <el-button
    v-if="showBtn"
    type="primary"
    plain
    :loading="btn_loading"
    @click="completeCA()">Complete CA</el-button>
</template>

<script>
import Outsource from '@/api/outsource'
export default {
  name: 'CompleteCA',
  props: {
    taskData: Object,
  },
  data() {
    return {
      btn_loading: false,
    }
  },
  computed: {
    showBtn() {
      return this.taskData && this.taskData.task_outsource_status === 'EXPORTED' &&
       ['US', 'SEA'].includes(this.taskData.task_outsource_info.outsource_advisor_to)
    },
  },
  methods: {
    completeCA() {
      this.btn_loading = true
      return Outsource.sendCaToSupport({ task_id: this.taskData.id })
        .then(() => {
          this.$message.success('Complete successful.')
          this.$emit('update')
        })
        .finally(() => {
          this.btn_loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
