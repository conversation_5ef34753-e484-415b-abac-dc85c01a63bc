<template>
  <el-button
    v-if="showBtn"
    type="primary"
    plain
    :loading="btn_loading"
    @click="generateLink()">Generate CA Link</el-button>
</template>

<script>
import Outsource from '@/api/outsource'
import { copyUtil } from '@/utils/tool'
export default {
  name: 'GenerateCaLink',
  props: {
    taskData: Object,
  },
  data() {
    return {
      btn_loading: false,
    }
  },
  computed: {
    showBtn() {
      return this.taskData && this.taskData.task_outsource_status === 'EXPORTED' &&
        ['US', 'SEA'].includes(this.taskData.task_outsource_info.outsource_advisor_to)
    },
  },
  methods: {
    generateLink() {
      this.btn_loading = true
      return Outsource.generateCALink(this.taskData.id)
        .then(data => {
          copyUtil(data?.ca_link)
            .then((val) => {
              this.$emit('update')
              this.$message({
                message: 'The CA link has been copied to the clipboard.',
                type: 'success',
              })
            })
        })
        .finally(() => {
          this.btn_loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
