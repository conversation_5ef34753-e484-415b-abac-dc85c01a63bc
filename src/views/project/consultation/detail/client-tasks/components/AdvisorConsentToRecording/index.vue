<template>
  <div class="inline-block">
    <el-button :loading="dialogLoading" :disabled="disabled || advisorBanned" type="primary" @click="getEmail()">
      <svg-icon icon-class="email" />
      Recording Consent
    </el-button>

    <el-dialog
      :visible.sync="dialogVisible"
      width="90%"
      custom-class="email-dialog"
      append-to-body>

      <div>
        <el-link type="primary" :underline="false" @click.stop="routeToConsultantDetail(task.advisor_id)">
          {{ task.advisor.name_prefix || ''}} {{task.advisor.firstname }} {{ task.advisor.lastname }}
        </el-link>
      </div>
      <div>
        <to-cc-editor :data="email" />
        <el-input v-model="email.subject" class="email-subject-input" />
        <tinymce
          :ref="`tinymce_content_${task.id}`"
          v-model="email.content"
          :height="300"
          inline
          is-email />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button
type="primary"
                   :loading="sendLoading"
                   @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import Tinymce from '@/components/Tinymce'
export default {
  name: 'Index',
  components: { ToCcEditor, Tinymce },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    task: Object,
  },
  data() {
    return {
      dialogLoading: false,
      sendLoading: false,
      dialogVisible: false,
      email: {},
    }
  },

  computed: {
    advisorBanned() {
      return this.task.advisor.status === 'BLACKLIST'
    },
  },

  methods: {
    getEmail() {
      this.dialogLoading = true
      return ProjectAPI.getAdvisorRecordingEmail(this.task.id)
        .then(data => {
          this.email = data
        })
        .finally(() => {
          this.dialogVisible = true
          this.dialogLoading = false
        })
    },

    routeToConsultantDetail(advisor_id) {
      const route = this.$router.resolve({
        name: 'ConsultantDetail',
        params: { advisor_id },
      })
      window.open(route.href, '_blank')
    },

    sendEmails() {
      this.sendLoading = true
      return ProjectAPI.sendAdvisorRecordingEmail(this.task.id, this.email)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Success to send emails!',
          })
          this.dialogVisible = false
        })
        .finally(() => {
          this.sendLoading = false
        })
    },
  },
}
</script>

<style scoped lang="scss">
/* 邮件编辑 主题 */
.email-subject-input {
  margin-bottom: 5px;
  margin-top: 5px;

::v-deep .el-input__inner {
  font-weight: bold;
  font-size: 20px;
  text-align: center;
}
}
</style>
