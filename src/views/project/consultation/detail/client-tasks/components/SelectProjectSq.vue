<template>
  <div>
    <el-select
      value-key="id"
      filterable
      remote
      placeholder="Select"
      class="remote-select"
      :loading="loading"
      no-data-text="No matching data"
      v-bind="$attrs"
      v-on="$listeners"
      @visible-change="visibleChang"
    >
      <el-option
        v-for="item in list"
        :key="item.id"
        :label="item.title"
        :value="item.id"
      />
    </el-select>

    <el-alert
      v-if="isPhysicianSq && sqAlertVisible"
      title="This is a Physician SQ."
      type="info"
      show-icon
      :closable="false"
      class="physician-sq-alert"
    />
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import InquiryAPI from '@/api/inquiry'

export default {
  name: 'SelectProjectSQ',
  props: {
    initRequest: {
      type: Boolean,
      default: true,
    },
    project: {
      type: Object,
      default: undefined,
    },
    sqAlertVisible: Boolean,
  },
  data() {
    return {
      is_visible: false,
      loading: false,
      list: [],
      searchQuery: {
        name_like: undefined,
      },
    }
  },
  computed: {
    isPhysicianSq() {
      const sq = this.list?.find(item => item.id === this.$attrs.value)
      return sq?.tags?.includes('PHYSICIAN')
    },
  },
  mounted() {
    if (this.initRequest) {
      this.getInquiry()
        .then(() => {
          this.$emit('init-completed')
        })
    }
  },
  methods: {
    getInquiry() {
      this.loading = true
      return ProjectAPI.getProjectInquiry(this.project.id)
        .then(data => {
          return this.getInquiries(data.sq)
        })
        .then((data) => {
          this.list = data.branches
        })
        .finally(() => {
          this.loading = false
        })
    },
    getInquiries(sq) {
      if (!sq) {
        this.list = []
        this.loading = false
        return Promise.resolve([])
      }
      return InquiryAPI.getInquiryOne(sq.id)
    },
    visibleChang(val) {
      this.is_visible = val
    },
  },
}
</script>

<style scoped>
.physician-sq-alert {
  margin-top: 8px;
  padding: 4px 16px;
}
</style>
