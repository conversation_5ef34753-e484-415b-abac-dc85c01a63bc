<template>
  <div>
    <div class="text-center">
      <el-input v-model="ca.title" class="title" placeholder="title" />
    </div>
    <div
      v-for="(item, index) in ca.questions"
      :id="`question-`+index"
      :key="item.temp_index"
      class="question-box"
    >
      <div>
        <edit-question
          :question.sync="ca.questions[index]"
          :question-index="index"
          language-type="en-US"
          :show-question="true" />
        <div class="question-actions-row">
          <el-button type="danger" circle icon="el-icon-delete" @click="removeQuestion(index)" />
          <el-button v-if="index > 0" circle icon="el-icon-top" @click="moveUp(index)" />
          <el-button v-if="index < ca.questions.length-1" circle icon="el-icon-bottom" @click="moveDown(index)" />
        </div>
      </div>
    </div>
    <div>
      <el-button
        icon="el-icon-plus"
        type="primary"
        @click="actionAddQuestion()">
        Add Question
      </el-button>
      <el-popover
        v-if="ca.questions.length"
        v-model="popoverVisible"
        width="300"
        placement="top"
      >
        <div v-if="noOptionsQuestion.length>0">
          There are some problematic options that are not set, please check number:
          <span class="danger">{{ noOptionsQuestion.join() }}</span>
        </div>
        <div v-else>
          <p><i class="el-icon-info" /> Confirm to generate？</p>
          <div style="text-align: right;">
            <el-button type="text" @click="popoverVisible = false">Cancel</el-button>
            <el-button type="primary" @click="$emit('update',ca);popoverVisible = false">Create</el-button>
          </div>
        </div>
        <el-button
          slot="reference"
          :disabled="saveDisabled"
          type="success"
          style="float:right"
          icon="el-icon-s-claim"
        >
          Save
        </el-button>
      </el-popover>
    </div>
  </div>
</template>

<script>
import EditQuestion from '@/views/compliance/client-agreement/components/EditQuestion'
import _ from 'lodash'

export default {
  name: 'ShowItem',
  components: { EditQuestion },
  props: {
    inquiryId: {
      type: [String, Number],
      default: null,
    },
    questionForm: {
      type: Array,
      default: () => {
        return []
      },
    },
    title: String,
  },
  data() {
    return {
      popoverVisible: false,
      temp_index_count: this.questionForm.length,
      ca: {
        title: '',
        questions: '',
      },
    }
  },
  computed: {
    noOptionsQuestion() {
      const no_options_question = []
      this.ca.questions.forEach((item, index) => {
        if (item.type === 'RADIO' && !item.options.length) {
          no_options_question.push(index + 1)
        }
      })
      return no_options_question
    },

    saveDisabled() {
      return this.ca.questions.length < 1 || this.ca.questions.filter(item => !item.title).length > 0
    },
  },

  methods: {
    actionAddQuestion() {
      this.temp_index_count++
      console.log(this.ca.questions)
      this.ca.questions.push({
        type: 'RADIO',
        show_type: 'acknowledgement',
        tags: [],
        title: '',
        is_required: true,
        options: [
          { type: 'ALLOW', value: 'AGREE', text: 'I Agree', description: false },
          { type: 'DENY', value: 'AGREE_NOT', text: 'I Disagree', description: false }],
        temp_index: this.temp_index_count,
        sort_order: this.ca.questions.length,
      })
    },

    removeQuestion(index) {
      this.ca.questions.splice(index, 1)
    },

    moveDown(index) {
      const arr = this.ca.questions
      const temp = arr[index]
      arr.splice(index, 1)
      setTimeout(() => {
        arr.splice(index + 1, 0, temp)
      }, 0)
    },

    moveUp(index) {
      const arr = this.ca.questions
      const temp = arr[index]
      arr.splice(index, 1)
      setTimeout(() => {
        arr.splice(index - 1, 0, temp)
      }, 0)
    },

    formatQuestions() {
      return _.cloneDeep(this.ca.questions)
        .filter(item => item.show_type && item.title)
        .map((item, index) => {
          const { title, type, options, is_required, tags } = item
          return { title, type, sort_order: index, options, is_required, tags }
        })
    },

    init() {
      this.ca = {
        title: _.cloneDeep(this.title),
        questions: _.cloneDeep(this.questionForm),
      }
    },

  },
}
</script>

<style scoped>
.title{
  width:80%;
  margin-bottom: 20px;
}

/*.question-box{*/
/*  border: 1px solid #333333;*/
/*  padding: 20px;*/
/*}*/
</style>
