<template>
  <div class="app-container">
    <div class="flex justify-content-between align-items-center mb-3">
      <div v-if="is_edited">
        <el-button plain icon="el-icon-back" @click="is_edited=false">Back</el-button>
      </div>
      <div v-if="!is_edited">
        <el-button
          v-if="!inquiry_id"
          class="filter-item"
          type="success"
          icon="el-icon-plus"
          @click="editInquiry()">
          New
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="editInquiry()">
          Edit
        </el-button>
      </div>
      <div>
        <span v-if="branch" class="info">Create at {{branch.create_at |momentFormat()}}</span>
        <el-tooltip
          effect="dark"
          placement="top"
          content="Customized CA for experts imported into other DBs.">
          <el-link :underline="false" icon="el-icon-question" />
        </el-tooltip>
      </div>
    </div>
    <div class="questionnaire-container ">
      <Edit v-show="is_edited" ref="editCA" :title="title" :question-form="question_form" @update="saveCA" />
      <div v-if="!is_edited">
        <div class="text-center bold mb-normal">{{ title }}</div>
        <div
          v-for="(item, index) in question_form"
          :id="`question-`+index"
          :key="item.temp_index"
          class="question-box-item"
        >
          <show-question :form="item" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import Edit from './editCA'
import ShowQuestion from '@/views/compliance/client-agreement/components/showQuestion'
import _ from 'lodash'
import { mapState } from 'vuex'

export default {
  name: 'Index',
  components: { Edit, ShowQuestion },
  data() {
    return {
      question_form: [],
      title: '',
      inquiry_id: null,
      branch: null,
      is_edited: false,
    }
  },
  computed: {
    ...mapState({
      project_id: state => state.project.data.id,
    }),
  },
  mounted() {
    this.getInquiry()
  },
  methods: {

    getInquiry() {
      const params = {
        project_id: this.project_id,
        type: 'PRE_CALL_CA',
        extra: 'branches.questions',
      }
      return InquiryAPI.getInquiryList(params)
        .then(data => {
          const inquiry = data.list[0]
          this.inquiry_id = inquiry.id
          this.branch = inquiry.branches[0]
          this.question_form = inquiry.branches[0].questions
          this.title = inquiry.branches[0].title
        }).finally(() => {
          console.log(this.question_form)
        })
    },
    editInquiry() {
      this.$refs.editCA.init()
      if (!this.question_form.length) {
        this.$refs.editCA.actionAddQuestion()
      }
      this.is_edited = true
    },

    saveCA(ca) {
      const params = {
        locale: 'en-US',
        title: ca.title,
        description: '',
        questions: this.formatQuestions(ca),
      }
      this.submit_loading = true
      const request = !this.inquiry_id ? this.createInquiry(params) : this.updateBranch(params)

      return request.then((data) => {
        this.is_edited = false
        this.question_form = ca.questions
        this.title = ca.title
      }, err => {
        console.log(err)
      })
        .finally(() => {
          this.submit_loading = false
        })
    },
    updateBranch(params) {
      return InquiryAPI.updateQuestion(this.inquiry_id, this.branch.id, params, 'SAVE,UPDATE,DELETE')
    },
    createInquiry(params) {
      const data = {
        project_id: this.project_id,
        client_id: null,
        type: 'PRE_CALL_CA',
        name: params.title,
        branch: params,
      }

      return InquiryAPI.addInquiry(data)
        .then(data => {
          this.inquiry_id = data.id
        })
    },

    formatQuestions(ca) {
      return _.cloneDeep(ca.questions)
        .filter(item => item.show_type && item.title)
        .map((item, index) => {
          const { title, type, options, is_required, tags } = item
          return { title, type, sort_order: index, options, is_required, tags }
        })
    },

  },
}
</script>

<style scoped lang="scss">
.question-box-item {
  margin-bottom: 10px;
  padding: 20px;

.order {
  min-width: 1rem;
}

.content-html {
  width: 90%;
  padding-left: 3px;
  overflow-wrap: break-word;
  word-break: break-all;
}
}
</style>
