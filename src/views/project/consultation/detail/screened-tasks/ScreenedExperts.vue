<template>
  <div>
    <clientTasksMcK v-if="$route.params.type === 'MCK'" screened />
    <clientTasks v-if="$route.params.type === 'normal'" screened />
    <ClientTasksSST v-if="$route.params.type === 'SST'" screened />
  </div>
</template>

<script>
import ClientTasksMcK from '@/views/project/consultation/detail/client-tasks/ClientTasksMcK'
import ClientTasks from '@/views/project/consultation/detail/client-tasks/ClientTasks'
import ClientTasksSST from '@/views/project/consultation/detail/client-tasks/ClientTasksSST'
export default {
  name: 'ScreenedExperts',
  components: { ClientTasksMcK, ClientTasks, ClientTasksSST },
}
</script>

<style scoped>

</style>
