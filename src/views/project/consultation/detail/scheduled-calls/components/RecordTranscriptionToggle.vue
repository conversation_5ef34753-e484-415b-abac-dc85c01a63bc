<template>
  <div>
    <el-tooltip
v-if="changeConsentRecordDisabled"
                effect="dark"
                class="inline-block"
                placement="top-start">
      <div slot="content" class="w-300px">
        <span v-if="conferenceStared">The meeting has already started or been cancelled and cannot be modified.</span>
        <span v-else-if="!row.advisor_record_consent || !row.advisor_record_consent.is_consent">
        To enable recording, please get written consent from the expert and toggle Consent to Record in the Client Task progress bar to YES. Note: Only effective if the recording setting is turned on before the meeting starts.</span>
      </div>
      <div>
        <el-button type="text" disabled>
          <span v-if="advisorConsentIsTooLate">N/N</span>
          <span v-else>{{row.arrangement.enable_record?'Y':'N'}} / {{row.arrangement.enable_transcription?'Y':'N'}}</span>
        </el-button>
      </div>
    </el-tooltip>

    <el-popover
      v-else
      v-model="visible"
      placement="top"
      class="inline-block"
      width="300"
      @show="init">
      <div class="mb-normal">
        <el-checkbox
          v-model="enable_record"
          @change="recordChange"
        >
          Requires a Call Recording
        </el-checkbox>
        <el-checkbox
          v-model="enable_transcription"
          :disabled="!enable_record"
        >
          Requires a Transcript
        </el-checkbox>
        <!--          // 暂时注释，后期上线-->
        <!--        <div v-if="enable_transcription" class="inline-block ml-3 text-left">-->
        <!--          <span>Transcription channels:</span>-->
        <!--          <el-select-->
        <!--            v-model="transcription_type"-->
        <!--            class="w-200px">-->
        <!--            <el-option-->
        <!--              v-for="option in options.transcription_type"-->
        <!--              :key="option.value"-->
        <!--              :label="option.label"-->
        <!--              :value="option.value" />-->
        <!--          </el-select>-->
        <!--        </div>-->
      </div>
      <div style="text-align: right; margin: 0">
        <el-button size="mini" type="text" @click="visible = false">Cancel</el-button>
        <el-button type="primary" size="mini" :loading="loading" @click="conferenceArrangementUpdate">Update</el-button>
      </div>
      <el-button slot="reference" type="text" :disabled="changeConsentRecordDisabled">
        {{row.arrangement.enable_record?'Y':'N'}} / {{row.arrangement.enable_transcription?'Y':'N'}}
      </el-button>
    </el-popover>

    <el-tooltip
v-if="showConsentTip"
                effect="dark"
                class="inline-block"
                placement="top-start">
      <div slot="content" class="w-300px">
        <span v-if="advisorConsentIsTooLate"> Call will not be recorded because consent to record was not received prior to the conference start time</span>
        <span v-else> Recording will automatically be enabled as soon as the expert consents to record.</span>
      </div>
      <i class="el-icon-warning danger" />
    </el-tooltip>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import moment from 'moment/moment'

export default {
  name: 'RecordTranscriptionToggle',
  props: {
    row: Object,
    clientType: String,
  },
  data() {
    return {
      visible: false,
      loading: false,
      enable_record: false,
      enable_transcription: false,
      transcription_type: 'DEFAULT',
      options: {
        transcription_type: [
          { label: 'Default', value: 'DEFAULT' },
          { label: 'Cadence', value: 'CADENCE' },
        ],
      },
    }
  },

  computed: {
    conferenceStared() {
      const twilio_started = this.row.arrange_bridge_type === 'TWILIO' && ['IN_PROGRESS', 'AWAITING_PARTICIPANTS', 'COMPLETED'].includes(
        this.row.arrangement.twilio_voice_conference?.brief_insight?.status)
      const zoom_started = this.row.arrange_bridge_type === 'ZOOM' && ['STARTED', 'ENDED'].includes(this.row.arrangement.zoom_meeting.status)
      return twilio_started || zoom_started
    },
    changeConsentRecordDisabled() {
      const toggle_disabled = !this.row.advisor_record_consent?.is_consent && this.clientType !== 'CONSULTING_FIRM'
      return this.conferenceStared || toggle_disabled || this.row.is_cancelled_twilio
    },

    needAdvisorConsent() {
      return !this.row.advisor_record_consent?.is_consent && this.row.arrangement.enable_record
    },

    advisorConsentIsTooLate() {
      if (!this.row.advisor_record_consent?.is_consent || !this.row.twilio_brief_insight?.actual_start_time) {
        return false
      } else {
        const consent_is_after_conference_start = moment(this.row.advisor_record_consent.update_at).isAfter(this.row.twilio_brief_insight.actual_start_time)
        return !!this.row.advisor_record_consent?.is_consent && consent_is_after_conference_start
      }
    },

    showConsentTip() {
      return this.needAdvisorConsent || this.advisorConsentIsTooLate
    },

  },

  methods: {
    init() {
      this.enable_record = this.row.arrangement.enable_record
      this.enable_transcription = this.row.arrangement.enable_transcription
      this.transcription_type = this.row.arrangement.transcription_type
    },

    recordChange() {
      if (!this.enable_record) {
        this.enable_transcription = false
        this.transcription_type = null
      }
    },

    conferenceArrangementUpdate() {
      this.loading = true
      const params = {
        id: this.row.arrangement.id,
        enable_record: this.enable_record,
        enable_transcription: this.enable_transcription,
        transcription_type: this.enable_transcription ? this.transcription_type : null,
      }
      return ProjectAPI.updateTaskArrangement(this.row.id, params)
        .then(() => {
          Object.assign(this.row.arrangement, params)
          this.visible = false
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
