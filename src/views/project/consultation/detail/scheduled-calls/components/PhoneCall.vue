<template>
  <el-dialog
    title="Call Expert"
    append-to-body
    class="phone-call-dialog"
    :visible.sync="showDialog"
    width="40%">
    <el-form
      ref="form"
      class="form-data show-form"
      label-position="left"
      :rules="rules"
      :model="form"
      label-width="110px">
      <el-form-item label="Country Code" prop="country_code">
        <el-select v-model="form.country_code"
                   filterable
                   class="w-70px"
                   placeholder="Code"
                   @change="setCountry">
          <el-option v-for="item in country_list" :key="item.iso2" class="w-200px" :label="`+${item.dialCode}`" :value="item.iso2">
            {{`+${item.dialCode} ${item.name}`}}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Phone Number" prop="phone_number">
        <el-input
          id="phone"
          v-model="form.phone_number"
          placeholder="Phone Number"
          class="w-200px"
          type="number"
        />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="text" size="mini" @click="cancelCall">Cancel</el-button>
      <el-button :loading="call_loading" type="primary" size="mini" @click="actionCallExpert">Call</el-button>
    </span>
  </el-dialog>
</template>

<script>
import intlTelInput from 'intl-tel-input'
import _ from 'lodash'
import ProjectAPI from '@/api/project'
export default {
  name: 'PhoneCall',
  props: {
    task: Object,
  },
  data() {
    return {
      isInit: false,
      call_loading: false,
      showDialog: false,
      phone_number_valid: true,
      form: {
        phone_number: '',
        country_code: '',
      },
      country_list: [],
      phone_number_format: '',
      rules: {
        phone_number: [{ required: true,
          trigger: ['blur', 'change', 'input'],
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('Phone number is required'))
            } else if (!this.phoneNumberValid) {
              callback(new Error('Please enter a valid phone number without country code'))
            } else {
              callback()
            }
          } }],
        country_code: [{ required: true, trigger: 'change' }],
      },
    }
  },
  computed: {
    phoneNumberValid() {
      return this.phone_number_valid || typeof this.phone_number_valid !== 'boolean'
    },
  },
  watch: {
    'form.phone_number': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getNumberInfo()
        }
      },
    },
  },
  methods: {
    initTelInput() {
      this.showDialog = true
      this.isInit = true
      this.$nextTick(() => {
        const input = document.getElementById('phone')
        input.addEventListener('countrychange', this.getNumberInfo)

        this.iti = intlTelInput(input, {
          utilsScript: 'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.0/js/utils.js',
          initialCountry: 'auto',
          allowDropdown: false,
          separateDialCode: true,
        })

        this.form.phone_number = this.getAdvisorPhone()
        this.getCountryList()
        // this.country_format = this.country_list.find(item => item.name === this.country)?.iso2
        // this.country_format ? this.iti.setCountry(this.country_format) : ''
        this.iti.setNumber(this.form.phone_number)
        this.getNumberInfo()
        this.isInit = false
        this.showDialog = true
      })
    },

    setCountry() {
      this.iti.setCountry(this.form.country_code)
    },
    getNumberInfo() {
      this.phone_number_format = this.iti.getNumber()
      if (this.iti.getSelectedCountryData().name) {
        this.country = this.iti.getSelectedCountryData().name
      }
      this.phone_number_valid = this.iti.isValidNumber()
      if (this.form.phone_number && this.form.phone_number.includes('+')) {
        const country_code = `+${this.iti.getSelectedCountryData().dialCode}`
        this.form.phone_number = this.form.phone_number.replace(country_code, '')
      }
      if (!this.isInit) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            return true
          } else {
            return false
          }
        })
      }
    },

    getCountryList() {
      const list = window.intlTelInputGlobals.getCountryData()
      const us = list.find(item => item.iso2 === 'us')
      const without_us = list.filter(item => item.iso2 !== 'us')
      this.country_list = [us].concat(_.sortBy(without_us, 'dialCode'))
    },

    getAdvisorPhone() {
      return this.task.arrangement.conference_invitees.find(item => item.role === 'EXPERT')?.phone_number
    },

    cancelCall() {
      this.phone_number_valid = true
      this.form = {
        phone_number: '',
        country_code: '',
      }
      this.showDialog = false
    },

    actionCallExpert() {
      this.call_loading = true
      const params = {
        task_id: this.task.id,
        phone_number: this.phone_number_format,
      }
      const is_using_zoom = this.task.arrange_bridge_type === 'ZOOM'
      if (is_using_zoom) {
        params['zoom_meeting_id'] = this.task.arrangement.zoom_meeting.meeting_id.toString()
      }
      const promise = is_using_zoom ? ProjectAPI.callExpertByZoom(params)
        : ProjectAPI.callExpertByTwilio(params)
      return promise.then(() => {
        this.$message({
          message: 'Successful call.',
          type: 'success',
        })
      })
        .finally(() => {
          this.call_loading = false
        })
    },
  },
}
</script>

<style scoped lang="scss">

::v-deep .iti__flag-container{
  width:0;
  padding:0;
  display: none;
}

.phone-call-dialog{
  ::v-deep .el-input__inner{
    padding-left: 8px !important;
  }

  .w-70px{
    width: 70px;
  }
}
</style>
