<template>
  <div>
    <div class="info-row">
      <div>
        {{ role }}<span v-if="!['Expert','Unknown'].includes(role)">(s)</span>
      </div>
      <div class="flex-1" />
      <div class="flex-1 pl-24px" :class="{'final-time':conferenceCompleted}">{{billing_time}}</div>
    </div>
    <el-table
      :data="list"
      border
      :show-header="false"
      style="width: 100%">
      <el-table-column
        label="Participant Type"
        width="220">
        <template slot-scope="{row}">
          {{ identifierEmailOrPhone(row) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        width="140">
        <template slot-scope="{row}">
          {{row.status | getLabel(status_options)}}
        </template>
      </el-table-column>
      <el-table-column
        label="Joined By">
        <template slot-scope="{row}">
          <span v-if="isZoom">{{ row.joined_by}}</span>
          <span v-else>{{ row.connect_type | getLabel(connect_types)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Joined Time">
        <template slot-scope="{row}">
          {{ row.joined_time | momentFormat('MM/DD/YYYY hh:mm A')}}
        </template>
      </el-table-column>
      <el-table-column
        label="Duration">
        <template slot-scope="{row}">
          {{row.timer}}
        </template>
      </el-table-column>
      <el-table-column
        label="Conference Bridge Link">
        <template slot-scope="scope">
          <div v-if="scope.$index===0">

            <div v-if="role==='Alternative Host' && conferenceData.zoom_meeting">
              <a v-if="conferenceData.zoom_meeting.alternative_host_join_url"
                 :href="conferenceData.zoom_meeting.alternative_host_join_url"
                 class="link"
                 target="_blank">Meeting Link</a>
              <div>Meeting ID:{{conferenceData.zoom_meeting.meeting_id}}</div>
              <div>Passcode:{{conferenceData.zoom_meeting.password}}</div>
            </div>
            <div v-else>
              <el-popconfirm
                v-if="scope.row.join_conference_url"
                confirm-button-text="Yes, Copy"
                cancel-button-text="Cancel"
                icon="el-icon-info"
                :title="`Are you sure you want to copy the ${role} Link?`"
                @confirm="copyLink(scope.row.join_conference_url)"
              >
                <el-button slot="reference" :disabled="isCancelled" type="text"> Copy {{role}} Link</el-button>
              </el-popconfirm>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="">
        <template slot-scope="scope">
          <el-popconfirm
            v-if="scope.row.status === 'JOINED' && !isZoom"
            confirm-button-text="Yes"
            cancel-button-text="Cancel"
            icon="el-icon-info"
            title="Are you sure you want to end the conference for this participant?"
            @confirm="hangUpCall(scope.row)"
          >
            <el-button slot="reference" type="primary">End Conference</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import moment from 'moment-timezone'
import { copyUtil } from '@/utils/tool'
import _ from 'lodash'
import { mapState } from 'vuex'
import ProjectAPI from '@/api/project'

export default {
  name: 'ParticipantsList',
  props: {
    list: Array,
    conferenceData: Object,
    role: String,
    isCancelled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      interval_id: null,
      billing_time: '',
      connect_types: [
        { label: 'Phone called', value: 'OUTBOUND_CALL_API' },
        { label: 'Phone called', value: 'OUTBOUND_CALL_DIAL' },
        { label: 'Mobile dialing', value: 'INBOUND_CALL' },
        { label: 'Web Audio Link', value: 'BROWSER_DEVICE' },
      ],
      status_options: [
        // for twilio
        { label: 'Not joined yet', value: 'NOT_JOINED_YET' },
        { label: 'Joined', value: 'JOINED' },
        { label: 'Left', value: 'LEFT' },
        { label: 'Failed', value: 'FAILED' },
        // for zoom
        { label: 'Not joined yet', value: 'INIT' },
        { label: 'Left', value: 'EXITED' },
      ],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),

    isZoom() {
      return this.conferenceData.bridge_type === 'ZOOM'
    },

    conferenceCompleted() {
      return this.isZoom ? this.conferenceData.zoom_meeting.status : this.conferenceData.twilio_voice_conference.brief_insight.status === 'COMPLETED'
    },

    zoomConferenceEndTime() {
      return this.conferenceData.zoom_meeting?.latest_instance?.end_at
    },
  },
  mounted() {
    this.$set(this, 'billing_time', this.computedDuration())
    this.interval_id = setInterval(() => {
      this.$set(this, 'billing_time', this.computedDuration())

      for (let i = 0; i < this.list.length; i++) {
        this.$set(this.list[i], 'timer', this.getDuration(this.list[i]))
      }
    }, 1000)
  },
  destroyed() {
    clearInterval(this.interval_id)
  },
  methods: {
    getDuration(item) {
      let second = 0

      const is_participated = this.isZoom ? item.joined_time : item.participant.personal_calls.length

      if (is_participated) {
        if (this.isZoom) {
          second += item.cumulative_duration_of_previous_joins_in_seconds
          if (item.latest_entry.leave_time) {
            second += moment(item.latest_entry.leave_time).diff(moment(item.latest_entry.join_time), 's')
          } else if (this.isZoom && this.zoomConferenceEndTime) {
            second += moment(this.zoomConferenceEndTime).diff(moment(item.latest_entry.join_time), 's')
          } else {
            second += moment().tz(this.timeZone).diff(moment(item.latest_entry.join_time).tz(this.timeZone), 's')
          }
        } else {
          item.participant.personal_calls.forEach(temp => {
            if (temp.end_at) {
              second += moment(temp.end_at).diff(moment(temp.start_at), 's')
            } else {
              second += moment().tz(this.timeZone).diff(moment(temp.start_at).tz(this.timeZone), 's')
            }
          })
        }
        const duration = moment.duration(second, 's')
        const hours = duration.get('hours') > 0 ? `${duration.get('hours')}:` : ''
        const minutes = duration.get('minutes')
        const minutes_format = minutes < 10 ? `0${minutes}` : minutes
        const seconds = duration.get('seconds')
        const seconds_format = seconds < 10 ? `0${seconds}` : seconds
        return `${hours}${minutes_format}:${seconds_format}`
      } else {
        return ''
      }
    },

    /*
    eg:
    * [
    * {timestamp:'8:00',type:'BEGIN'}
    * {timestamp:'8:10',type:'SUSPEND'}
    * {timestamp:'8:20',type:'BEGIN'}
    * {timestamp:'8:25',type:'SUSPEND'}
    * {timestamp:'8:30',type:'BEGIN'}
    * ]
    * computed_start_time => 8:15
    * result => 15:00
    * */
    // timer 逻辑变更 专家以及客户取收费时长，其他角色取实际参会时长
    computedDuration() {
      if (!['Expert', 'Client'].includes(this.role)) return ''
      const conference = this.isZoom ? this.conferenceData.zoom_meeting : this.conferenceData.twilio_voice_conference

      if (!conference.state_time_travel?.timer_actions.length > 0) return ''

      const role_type = this.role === 'Expert' ? 'EXPERT' : 'CLIENT'
      const timer_list = conference.state_time_travel.timer_actions
      const target_list = timer_list.filter(item => item.role === role_type)

      const join_list = _.orderBy(target_list.filter(i => i.type === 'BEGIN'), 'timestamp')
      const leave_list = _.orderBy(target_list.filter(i => i.type === 'SUSPEND'), 'timestamp')
      if (join_list.length < 1) return ''
      // 一个参会者可以多次加入和退出会议 计算合计参会时间
      const last_join = _.orderBy(join_list, 'timestamp', 'desc')[0]
      const last_leave = _.orderBy(leave_list, 'timestamp', 'desc')[0]

      let past_time_seconds = 0
      // 加入次数大于1 表示需要计算 中断前的参会时间(最后一次加入之前的中断时间)
      if (join_list.length > 1) {
        let computed_list = _.cloneDeep(leave_list)
        if (join_list.length === leave_list.length) {
          computed_list = leave_list.slice(0, -1)
        }
        computed_list.forEach((temp, index) => {
          const join_time = join_list[index].timestamp
          past_time_seconds += moment(temp.timestamp).diff(moment(join_time), 's')
        })
      }
      // 把最后一次加入的时间 减去之前已经在会的合计时间，作为计算总时长的开始时间
      const computed_start_time = moment(last_join.timestamp).subtract(past_time_seconds, 's')
      let duration
      // 参会者正在会议中，计算截止到当前时刻的时长，并根据计时器按秒累加时长
      if (join_list.length > leave_list.length) {
        duration = moment.duration(moment() - computed_start_time, 'ms')
      } else {
        // 参会者已经离开会议，计算总时长，不根据计时器累加时长
        duration = moment.duration(moment(last_leave.timestamp) - moment(computed_start_time), 'ms')
      }

      const hours = duration.get('hours') > 0 ? `${duration.get('hours')}:` : ''
      const minutes = duration.get('minutes')
      const minutes_format = minutes < 10 ? `0${minutes}` : minutes
      const seconds = duration.get('seconds')
      const seconds_format = seconds < 10 ? `0${seconds}` : seconds

      return `${hours}${minutes_format}:${seconds_format}`
    },

    identifierEmailOrPhone(p) {
      // zoom参会者的身份信息可能只有一个display_name
      if (this.isZoom && p.display_name !== '') {
        return p.display_name
      }
      if ((p.role === 'EXPERT' || p.participant?.role === 'EXPERT') && p.advisor) {
        return p.email ? `${p.advisor.full_name} (${p.email})` : p.advisor.full_name
      } else {
        return p.participant?.name || p.email || p.inv_phone_number || p.participant?.email_address || p.participant?.phone_number || 'Anonymous'
      }
    },
    hangUpCall(row) {
      row.loading = true
      return ProjectAPI.twilioKickOutParticipant(this.conferenceData.task_id, row.participant.id)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Success!',
          })
        })
        .finally(() => {
          row.loading = false
        })
    },

    copyLink(val) {
      copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
  },
}
</script>

<style scoped lang="scss">
.info-row{
  background: #eff4fa;
  padding:6px;
  display:flex;
>div{
  width:200px
}

.pl-24px{
  padding-left: 24px;
}
  .final-time{
    font-weight: bold;
    color: #2cad01;
  }
}
</style>
