<template>
  <div>
    <div class="mb-3">
      <refresh-button class="filter-item" @action="getList" />
      <el-input
        v-model="searchQuery.ids"
        clearable
        placeholder="Task IDs(separated by a comma)"
        class="filter-item w-200px"
        @input="debounceSearch"
      />
      <el-select
        v-if="isInvestorCall"
        v-model="conference_type"
        value-key="value"
        class="ml-8 filter-item w-200px"
        @change="debounceSearch">
        <el-option
          label="Normal Conference"
          :value="undefined" />
        <el-option
          label="Vetting Call"
          value="VETTING_CALL" />
      </el-select>
      <el-select
        v-model="arrange_bridge_type_in"
        value-key="value"
        multiple
        placeholder="Arrange Bridge"
        class="ml-8 filter-item w-200px"
        @change="debounceSearch">
        <el-option
          v-for="room in bridge_types"
          :key="room.value"
          :label="room.label"
          :value="room.value" />
      </el-select>
    </div>
    <el-table
      v-loading="loading"
      :data="list"
      :row-class-name="rowSetIndex"
      style="width: 100%">
      <el-table-column type="expand">
        <template v-if="isTwilioOrZoom(props.row)" slot-scope="props">
          <el-table
            v-loading="props.row.row_loading"
            :data="props.row.conference_participants"
            border
            :span-method="arraySpanMethod"
            cell-class-name="expand-table"
            style="width: 100%">
            <el-table-column
              :resizable="false"
              label="Participant Type"
              width="220">
              <template slot-scope="{row}">
                <participants-list
                  :list="row.list"
                  :is-cancelled="props.row.is_cancelled_twilio"
                  :role="row.role | getLabel(twilio_role)"
                  :conference-data="props.row.arrangement" />
              </template>
            </el-table-column>
            <el-table-column
              label="Status"
              :resizable="false"
              width="140" />
            <el-table-column
              label="Joined By"
              :resizable="false" />
            <el-table-column
              label="Joined Time"
              :resizable="false" />
            <el-table-column
              label="Duration"
              :resizable="false" />
            <el-table-column
              label="Conference Bridge Link"
              :resizable="false" />
            <el-table-column
              label="Actions"
              :resizable="false" />
          </el-table>
        </template>
      </el-table-column>
      <el-table-column
        label="Task ID"
        width="70"
        prop="id">
        <template slot-scope="{row}">
          {{row.id}}
          <div v-if="row.is_cancelled_twilio" class="warning">(Cancelled)</div>
        </template>
      </el-table-column>
      <el-table-column
        label="Expert Name"
      >
        <template slot-scope="{row}">
          <router-link-advisor :data="row.advisor" />
        </template>
      </el-table-column>
      <el-table-column
        label="Bridge"
      >
        <template slot-scope="{row}">
          {{row.arrange_bridge_type | getLabel(bridge_types)}}
        </template>
      </el-table-column>
      <el-table-column
        label="Scheduled Start Time"
      >
        <template slot-scope="{row}">
          <span v-if="row.schedule">{{row.schedule.start_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Scheduled End Time"
      >
        <template slot-scope="{row}">
          <span v-if="row.schedule">{{row.schedule.end_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Actual Start Time"
      >
        <template slot-scope="{row}">
          <span v-if="row.arrange_bridge_type==='TWILIO'">
            <span v-if="row.twilio_brief_insight">
              {{row.twilio_brief_insight.actual_start_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}
            </span>
          </span>
          <span v-else-if="row.arrange_bridge_type==='ZOOM'">
            <span v-if="row.actual_start_time">
              {{row.actual_start_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}
            </span>
          </span>
          <span v-else>Check {{row.arrange_bridge_type | getLabel(bridge_types)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Actual End Time"
      >
        <template slot-scope="{row}">
          <span v-if="row.arrange_bridge_type==='TWILIO'">
            <span v-if="row.twilio_brief_insight">
              {{row.twilio_brief_insight.actual_end_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}
            </span>
          </span>
          <span v-else-if="row.arrange_bridge_type==='ZOOM'">
            <span v-if="row.actual_end_time">
              {{row.actual_end_time | momentFormat('MM/DD/YYYY hh:mm:ss A')}}
            </span>
          </span>
          <span v-else>Check {{row.arrange_bridge_type | getLabel(bridge_types)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Participant Types"
      >
        <template slot-scope="{row}">
          {{getRoles(row) | getLabels(twilio_role)}}
        </template>
      </el-table-column>
      <el-table-column
        label="Conference Status"
        prop="twilio_voice_conference.status">
        <template slot-scope="{row}">
          <div v-if="row.arrange_bridge_type==='TWILIO'">
            <div v-if="row.arrangement">
              <span v-if="row.is_cancelled_twilio">Cancelled</span>
              <span v-else>
                <span v-if="row.twilio_brief_insight">
                  {{row.twilio_brief_insight.status | getLabel(status_options)}}
                </span>
              </span>
            </div>
          </div>
          <div v-else-if="row.arrange_bridge_type==='ZOOM'">
            <div v-if="row.arrangement">
              <span v-if="row.arrangement.zoom_meeting">
                {{row.arrangement.zoom_meeting.status | getLabel(status_options)}}
              </span>
            </div>
          </div>
          <span v-else>Check {{row.arrange_bridge_type | getLabel(bridge_types)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Task Status"
        prop="task.general_status">
        <template slot-scope="{row}">
          {{transformValue(row.general_status)}}
        </template>
      </el-table-column>
      <el-table-column
        label="Consent to Record">
        <template slot-scope="{row}">
          <span v-if="row.advisor_record_consent && !isInvestorCall">
            {{row.advisor_record_consent.is_consent | yesOrNo}}
               <el-tooltip effect="dark" :content="row.advisor_record_consent.update_at | momentFormat('MM/DD/YYYY (HH:mm:ss)')" placement="top">
                <el-link type="primary" :underline="false" icon="el-icon-time" />
              </el-tooltip>
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Record / Transcription"
        width="90"
      >
        <template slot-scope="{row}">
          <span v-if="isTwilioOrZoom(row)">
            <RecordTranscriptionToggle v-if="row.arrangement" :row="row" :client-type="clientType" />
          </span>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Event"
      >
        <template slot-scope="{row}">
          <el-tooltip
            v-if="isTwilioOrZoom(row)"
            effect="dark"
            :enterable="false"
            placement="top"
            :open-delay="300"
          >
            <div v-if="row.arrangement" slot="content">
              <template v-if="row.arrangement.twilio_voice_conference">
                <div v-for="(item,index) in row.arrangement.twilio_voice_conference.event_stream" :key="index">
                  {{item.description}}
                </div>
              </template>
              <template v-if="row.arrangement.zoom_meeting">
                <div v-for="(item,index) in row.arrangement.zoom_meeting.readable_events" :key="index">
                  {{item.statement}}
                </div>
              </template>
            </div>
            <i class="el-icon-data-analysis link cursor-pointer" />
          </el-tooltip>
          <span v-else>N/A</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Actions"
        width="100"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-dropdown v-if="showActionBtns(row)" @command="handleAction(row, $event)">
            <el-button type="primary">
              Action<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="refresh">
                Refresh Data
              </el-dropdown-item>
              <el-dropdown-item :disabled="callExpertDisabled(row)" command="phoneCall">
                Call Expert
              </el-dropdown-item>
              <el-dropdown-item :disabled="zoomMeetingStarted(row)" command="alternativeHost">
                Edit Alternative Host
              </el-dropdown-item>
              <el-popconfirm
                v-if="clientComplianceReviewFirst"
                confirm-button-text="Yes"
                cancel-button-text="Cancel"
                icon="el-icon-info"
                width="550"
                placement="top"
                :title="clientComplianceReviewFirst"
                @confirm="handleAction(row, 'copyAssetLink')"
              >
                <el-dropdown-item slot="reference" :hide-on-click="false" :disabled="copyAssetLinkDisabled(row)">
                  Copy Recording/Transcript Link
                  <span v-if="assetExpired(row)">(Expired)</span>
                  <span v-if="!advisorConsentToRecording(row)">(Expert did not consent)</span>
                </el-dropdown-item>
              </el-popconfirm>
              <el-dropdown-item v-else :disabled="copyAssetLinkDisabled(row)" command="copyAssetLink">
                Copy Recording/Transcript Link
                <span v-if="assetExpired(row)">(Expired)</span>
                <span v-if="!advisorConsentToRecording(row)">(Expert did not consent)</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getList()"
    />

    <phone-call ref="phoneCall" :task="action_task" />
    <el-dialog
      v-if="action_task"
      ref="alternativeHosts"
      title="Update"
      :visible.sync="alternative_hosts_visible"
      width="30%">
      <alternative-hosts v-model="alternative_host_email_list" :arrangement="action_task.arrangement" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="alternative_hosts_visible = false">Cancel</el-button>
        <el-button type="primary" :loading="dialog_loading" @click="updateConferenceArrangement">Update</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import _ from 'lodash'
import { debounce, copyUtil } from '@/utils/tool'
import Pagination from '@/components/Pagination'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import { mapState } from 'vuex'
import ParticipantsList from '@/views/project/consultation/detail/scheduled-calls/components/ParticipantsList'
import PhoneCall from '@/views/project/consultation/detail/scheduled-calls/components/PhoneCall'
import Filters from '@/utils/filters'
import RefreshButton from '@/components/RefreshButton/index'
import RecordTranscriptionToggle from '@/views/project/consultation/detail/scheduled-calls/components/RecordTranscriptionToggle'
// import AllowVideo from '@/views/project/consultation/detail/scheduled-calls/components/AllowVideo'
import AlternativeHosts from '@/views/project/consultation/detail/tasks-arrange/components/AlternativeHosts'

export default {
  name: 'List',
  components: { RouterLinkAdvisor, AlternativeHosts, Pagination, ParticipantsList, PhoneCall, RefreshButton, RecordTranscriptionToggle },
  data() {
    return {
      loading: false,
      alternative_hosts_visible: false,
      dialog_loading: false,
      alternative_host_email_list: [],
      action_task: null,
      conference_type: undefined,
      arrange_bridge_type_in: [],
      searchQuery: {
        page: 1,
        size: 50,
        ids: null,
        sort: 'schedule.start_time desc',
        extra: [
          'advisor',
          'advisor_record_consent',
          'schedule',
          'arrangement.twilio_voice_conference.brief_insight.individual_participants',
          'arrangement.conference_invitees.portal_dial_info',
          'arrangement.conference_invitees.advisor',
          'arrangement.twilio_voice_conference.event_stream',
          'arrangement.zoom_meeting.readable_events',
          'arrangement.zoom_meeting.state_time_travel',
          'arrangement.zoom_meeting.scheduled_call_tab_participants',
          'arrangement.zoom_meeting.latest_instance',
          'vetting_call_arrangement.twilio_voice_conference.brief_insight.individual_participants',
          'vetting_call_arrangement.conference_invitees.portal_dial_info',
          'vetting_call_arrangement.conference_invitees.advisor',
          'vetting_call_arrangement.twilio_voice_conference.event_stream',
          'vetting_call_arrangement.zoom_meeting.readable_events',
          'vetting_call_arrangement.zoom_meeting.state_time_travel',
          'vetting_call_arrangement.zoom_meeting.scheduled_call_tab_participants',
          'vetting_call_arrangement.zoom_meeting.latest_instance',
        ].join(),
      },
      list: [],
      total: 0,
      twilio_role: [
        { label: 'Expert', value: 'EXPERT' },
        { label: 'Client', value: 'CLIENT' },
        { label: 'Client Compliance', value: 'CLIENT_COMPLIANCE' },
        { label: 'Client Sponsor', value: 'CLIENT_SPONSOR' },
        { label: 'Moderator', value: 'MODERATOR' },
        { label: 'Translator', value: 'TRANSLATOR' },
        { label: 'TBD', value: 'TBD' },
        { label: 'Alternative Host', value: 'ALTERNATIVE_HOST' },
      ],
      participants_status: [
        { label: 'Initial', value: 'INITIAL', type: 'info' },
        { label: 'Connected', value: 'CONNECTED', type: 'primary' },
        { label: 'Complete', value: 'COMPLETE', type: 'success' },
        { label: 'Failed', value: 'FAILED', type: 'error' },
      ],
      bridge_types: [
        { label: 'Client Provided Bridge', value: 'CLIENT_PROVIDED' },
        { label: 'Internal Zoom', value: 'INTERNAL_ZOOM' },
        { label: 'ZipDX', value: 'ZIPDX' },
        { label: 'Other', value: 'OTHER' },
      ],
      status_options: [
        // for twilio
        { label: 'Scheduled', value: 'SCHEDULED' },
        { label: 'In Progress', value: 'IN_PROGRESS' },
        { label: 'Awaiting Participants', value: 'AWAITING_PARTICIPANTS' },
        { label: 'Completed', value: 'COMPLETED' },

        // for zoom
        { label: 'Created', value: 'CREATED' },
        { label: 'Started', value: 'STARTED' },
        { label: 'Ended', value: 'ENDED' },
        { label: 'Delete', value: 'DELETE' },
      ],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
      projectInfo: state => state.project.data,
      userInfo: state => state.user.info,
      isSGDB: state => state.app.isSGDB,
    }),
    // 客户法务规则 需要法务预先审核 会议录音及速记
    clientComplianceReviewFirst() {
      const has_rule = this.projectInfo?.client.compliance_preference?.rule
      const need_client_compliance_review = has_rule && has_rule.compliance_rules.transcript_notetaking_rules.master_switch &&
        has_rule.compliance_rules.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first
      if (need_client_compliance_review) {
        return `Attention: Please confirm to send all transcript and recording links to (${has_rule.compliance_rules.transcript_notetaking_rules.require_compliance_review_email_list.join()}) for review and approval before sending to the client analyst.`
      } else {
        return false
      }
    },

    clientType() {
      return this.projectInfo?.client.type || null
    },

    isInvestorCall() {
      return this.projectInfo?.sub_type === 'Investor Call' || null
    },
  },

  mounted() {
    this.getList()
    if (!this.isSGDB) {
      this.bridge_types.push({ label: 'Loopup', value: 'LOOPUP' })
      this.bridge_types.push({ label: 'Twilio', value: 'TWILIO' })
      this.bridge_types.push({ label: 'Zoom', value: 'ZOOM' })
    }
  },

  methods: {
    getList(task_id = undefined, row = {}) {
      if (!task_id) this.loading = true
      else row.row_loading = true

      const params = {
        id: task_id,
        'project_id': this.$route.params.project_id,
        general_status_in: 'SCHEDULED,ARRANGED,COMPLETED',
        ...this.searchQuery,
        arrange_bridge_type_in: this.arrange_bridge_type_in.join() || undefined,
      }

      if (this.conference_type === 'VETTING_CALL') {
        params.general_status_in = undefined
        params.arrange_bridge_type_in = undefined
        params['vetting_call_arrangement.bridge_type_in'] = this.arrange_bridge_type_in.join() || undefined
        params['vetting_call_arrangement.type'] = this.conference_type
      }

      return ProjectAPI.getConsultationList(params)
        .then(data => {
          if (task_id) {
            const target = this.list.findIndex(item => item.id === task_id)
            Object.assign(this.list[target], formatData(data.list)[0])
          } else {
            const target_list = this.handleData(data.list)
            this.list = formatData(target_list)
            this.total = data.count
          }
        })
        .finally(() => {
          task_id ? row.row_loading = false : this.loading = false
        })

      function formatData(list) {
        return list
          .map(task => {
            task.conference_participants = []
            task.twilio_brief_insight = null
            if (task.arrangement) {
              if (task.arrangement.bridge_type === 'TWILIO') {
                handleDataForTwilio(task)
              }

              if (task.arrangement.bridge_type === 'ZOOM') {
                handleDataForZoom(task)
              }
            }
            return task
          })
      }

      function handleDataForTwilio(item) {
        // 未被邀请的人 也可以加入会议
        // 将会议最新状态的参会人员participant_list 与 被邀请的参会人员invited_participant_list 进行融合展示
        const conference_invitees = _.cloneDeep(item.arrangement.conference_invitees)
        item.twilio_brief_insight = item.arrangement.twilio_voice_conference?.brief_insight
        const participant_list = item.arrangement.twilio_voice_conference?.brief_insight?.individual_participants
        const participants_type = _.uniqBy(conference_invitees, 'role')
        const invited_participant_list = item.arrangement.conference_invitees
        if (participant_list && participant_list.find(temp => temp.participant.role === 'TBD')) {
          participants_type.push({ role: 'TBD' })
        }

        if (participant_list) {
          participant_list.forEach(p => {
            const match_invitee = conference_invitees.find(
              i => i.participant_identifier === p.participant.identifier || i.email === p.participant.email_address,
            )
            // console.log('find invitee for ' + temp.participant.email_address)
            // console.log(JSON.stringify(match_invitee))
            if (match_invitee) {
              p.advisor = match_invitee.advisor
              p.email = match_invitee.email
              p.inv_phone_number = match_invitee.phone_number
              p.join_conference_url = match_invitee.portal_dial_info?.computer_audio_link || undefined
            }
            p.timer = ''
          })
          participants_type.forEach(temp => {
            temp.list = participant_list.filter(i => i.participant.role === temp.role)
          })
        } else {
          participants_type.forEach(temp => {
            temp.list = invited_participant_list.filter(i => i.role === temp.role)
          })
        }
        item.row_loading = false
        item.conference_participants = participants_type
      }

      function handleDataForZoom(task) {
        const conference_invitees = _.cloneDeep(task.arrangement.zoom_meeting?.scheduled_call_tab_participants)
        const participant_list = task.arrangement.zoom_meeting?.scheduled_call_tab_participants
        const participants_type = _.uniqBy(conference_invitees, 'role')
        const invited_participant_list = task.arrangement.conference_invitees

        if (participant_list) {
          participant_list.forEach(p => {
            const match_invitee = invited_participant_list.find(item => item.role === p.role)
            p.advisor = task.advisor
            p.email = p.participant?.email
            p.inv_phone_number = p.participant?.phone_number
            p.join_conference_url = match_invitee?.portal_dial_info?.computer_audio_link
            p.timer = ''
          })
          participants_type.forEach(temp => {
            temp.list = participant_list.filter(i => i.participant.role === temp.role)
          })
        } else {
          participants_type.forEach(temp => {
            temp.list = invited_participant_list.filter(i => i.role === temp.role)
          })
        }
        task.row_loading = false
        task.actual_start_time = task.arrangement.zoom_meeting?.latest_instance?.start_at || undefined
        task.actual_start_time = task.arrangement.zoom_meeting?.latest_instance?.end_at || undefined
        task.conference_participants = participants_type
      }
    },

    /*
    vetting call的数据从vetting_call_arrangement中提取
    task 先使用了Twilio进行会议'(task A)，后续使用其他接桥进行会议(task B)，将原有Twilio内容展示出来
    新增一行 task A，放在 task B下一行，标记为cancelled
    */
    handleData(list) {
      if (this.conference_type) {
        list = list.filter(item => item.vetting_call_arrangement?.id)
        list.forEach(item => {
          item.arrangement = item.vetting_call_arrangement
          item.arrange_bridge_type = item.vetting_call_arrangement.bridge_type
          item.schedule = item.vetting_call_arrangement
        })
      }

      const clone_list = _.cloneDeep(list)
      clone_list.forEach(item => {
        if (item.arrangement && item.arrangement.bridge_type === 'TWILIO' && item.arrange_bridge_type !== 'TWILIO') {
          const new_data = {
            arrange_bridge_type: 'TWILIO',
            is_cancelled_twilio: true,
          }
          const index = list.findIndex(i => i.id === item.id)
          list.splice(index + 1, 0, Object.assign(item, new_data))
        }
      })
      return list
    },

    handleAction(task, type) {
      this.action_task = task

      if (type === 'refresh') {
        this.getList(task.id, task)
      }
      if (type === 'phoneCall') {
        this.$refs.phoneCall.initTelInput()
      }
      if (type === 'alternativeHost') {
        this.alternative_hosts_visible = true
        this.alternative_host_email_list = task.arrangement.conference_invitees.filter(item => item.role === 'ALTERNATIVE_HOST').map(item => item.email)
      }

      if (type === 'copyAssetLink') {
        const params = {
          require_compliance_review: !!this.clientComplianceReviewFirst,
          user_id: this.userInfo.id,
        }
        return ProjectAPI.getTwilioAssetLink(task.id, params)
          .then(data => {
            copyUtil(data.link_jit)
              .then((val) => {
                task.popover_visiable = false
                this.$message({
                  message: 'Copy successful',
                  type: 'success',
                })
                this.subject_copied = true
              })
              .catch(() => {
                this.$message({
                  message: 'Copy failed',
                  type: 'error',
                })
              })
          })
      }
    },
    updateConferenceArrangement() {
      this.loading = true
      const params = {
        id: this.action_task.arrangement.id,
        alternative_hosts: this.alternative_host_email_list.join(),
      }
      return ProjectAPI.updateTaskArrangement(this.action_task.id, params)
        .then(() => {
          this.alternative_hosts_visible = false
          this.getList(this.action_task.id, this.action_task)
        })
        .finally(() => {
          this.loading = false
        })
    },
    copyAssetLinkDisabled(row) {
      if (row.arrange_bridge_type === 'TWILIO') {
        const conference_completed = row.arrangement?.twilio_voice_conference?.brief_insight?.status === 'COMPLETED'
        return !conference_completed || !row.arrangement.enable_record || this.assetExpired(row) || !this.advisorConsentToRecording(row)
      } else if (row.arrange_bridge_type === 'ZOOM') {
        return !row.arrangement?.zoom_meeting?.asset_completed || !row.arrangement.enable_record
      } else {
        return true
      }
    },

    advisorConsentToRecording(row) {
      return row.advisor_record_consent?.is_consent
    },

    assetExpired(row) {
      const twilio_conference = row.arrangement?.twilio_voice_conference
      return twilio_conference?.recording_expired && twilio_conference?.transcript_expired
    },
    zoomMeetingStarted(row) {
      return row.arrangement?.zoom_meeting?.status !== 'CREATED'
    },
    callExpertDisabled(row) {
      if (!row.arrangement) return true
      const supported_bridges = new Set(['TWILIO', 'ZOOM'])
      if (!supported_bridges.has(row.arrange_bridge_type)) return true
      // const time_over = moment(row.arrangement.twilio_voice_conference.expected_end_time).tz(this.timeZone).isBefore(moment().tz(this.timeZone))
      // const expertInConference = row.arrangement.twilio_voice_conference.brief_insight.individual_participants.filter(item => item.participant.role === 'EXPERT' && item.status === 'JOINED').length > 0
      const taskCompleted = row.general_status === 'COMPLETED'
      return taskCompleted
    },

    transformValue(i) {
      return Filters.transformKey(i.toLowerCase())
    },

    debounceSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },

    getRoles(row) {
      if (['TWILIO', 'ZOOM'].includes(row.arrange_bridge_type) && row.arrangement) {
        return _.uniq(row.arrangement?.conference_invitees.map(item => item.role))
      } else {
        return ['EXPERT', 'CLIENT']
      }
    },

    isTwilioOrZoom(item) {
      return ['TWILIO', 'ZOOM'].includes(item.arrange_bridge_type)
    },

    showActionBtns(row) {
      return this.isTwilioOrZoom(row) && !row.is_cancelled_twilio && row.arrangement?.type !== 'VETTING_CALL'
    },

    rowSetIndex({ row, rowIndex }) {
      if (row.is_cancelled_twilio) return 'grey-bgc'
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return [1, 7]
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__expanded-cell[class*=cell]{
  padding: 0;
}

.dropdown-link {
  cursor: pointer;
}
</style>
