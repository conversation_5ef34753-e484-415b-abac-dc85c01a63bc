<template>
  <div>
    <div v-if="project.sub_type === 'Survey'" class="ml-3">
      <el-checkbox
        v-model="need_compliance_approval"
        :disabled="!hasPermission"
        @change="updateSurveyApproval">
        Compliance Approval
      </el-checkbox>
      <span v-if="need_compliance_approval_latest_change" class="info f-14 ml-3">
        Latest Changes :
        {{ need_compliance_approval_latest_change.create_by.name }}
        {{ need_compliance_approval_latest_change.create_at | momentFormat('MM/DD/YYYY (HH:mm)') }}
      </span>
    </div>
    <el-tabs v-model="current_compliance_rules_type" v-loading="rulesLoading">
      <el-tab-pane label="Client Compliance Rules" name="client_compliance_rules">
        <div v-if="client_compliance_rules">
          <div>
            <communication :type-rule="client_compliance_rules" show-title />
            <language-rule :type-rule="client_compliance_rules" show-title />
            <general-rule :type-rule="client_compliance_rules" show-title />
            <email-copy :type-rule="client_compliance_rules" show-title />
            <internal-copy :type-rule="client_compliance_rules" :client-am="client_am" show-title />
            <project-creation :type-rule="client_compliance_rules" show-title />
            <pre-call-ca :type-rule="client_compliance_rules" show-title />
            <post-call :type-rule="client_compliance_rules" show-title />
            <call-scheduling-email :type-rule="client_compliance_rules" show-title />
            <calendar-invite :type-rule="client_compliance_rules" show-title />
            <transcription-note :type-rule="client_compliance_rules" show-title />
            <Events :type-rule="client_compliance_rules" show-title />
          </div>

          <div>
            <expert-restriction :type-rule="client_compliance_rules" show-title />
            <recommend :type-rule="client_compliance_rules" show-title />
            <approval :type-rule="client_compliance_rules" :client-am="client_am" show-title />
            <arrange :type-rule="client_compliance_rules" show-title />
            <survey-rules :type-rule="client_compliance_rules" show-title />
          </div>
        </div>

        <div v-if="!has_rule" class="messageBox">No Data</div>
      </el-tab-pane>
      <el-tab-pane
        v-if="project.outsource_client_compliance_rules"
        label="Outsource Compliance Rules"
        name="outsource_compliance_rules"
      >
        <div v-html="$sanitizeHtml(project.outsource_client_compliance_rules)" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Approval from '@/components/ComplianceWarning/components/Approval'
import Arrange from '@/components/ComplianceWarning/components/Arrange'
import Recommend from '@/components/ComplianceWarning/components/RecommendEmail'
import EmailCopy from '@/components/ComplianceWarning/components/EmailCopyRule'
import ProjectCreation from '@/components/ComplianceWarning/components/ProjectCreation'
import CallSchedulingEmail from '@/components/ComplianceWarning/components/CallSchedulingEmail'
import PreCallCa from '@/components/ComplianceWarning/components/PreCallCA'
import CalendarInvite from '@/components/ComplianceWarning/components/CalendarInvite'
import ExpertRestriction from '@/components/ComplianceWarning/components/ExpertRestriction'
import Communication from '@/components/ComplianceWarning/components/Communication'
import InternalCopy from '@/components/ComplianceWarning/components/InternalCopy'
import LanguageRule from '@/components/ComplianceWarning/components/Language'
import GeneralRule from '@/components/ComplianceWarning/components/GeneralRule'
import PostCall from '@/components/ComplianceWarning/components/PostCall'
import Events from '@/components/ComplianceWarning/components/Events'
import TranscriptionNote from '@/components/ComplianceWarning/components/TranscriptNotetaking'
import SurveyRules from '@/components/ComplianceWarning/components/Survey'
import detailMixins from './../mixins/mixins'
import RouterTools from '@/utils/tool-router'
import ProjectAPI from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: 'ComplianceIndex',
  components: {
    Events,
    EmailCopy,
    Approval,
    Arrange,
    Recommend,
    ProjectCreation,
    CallSchedulingEmail,
    PreCallCa,
    CalendarInvite,
    ExpertRestriction,
    Communication,
    InternalCopy,
    LanguageRule,
    GeneralRule,
    PostCall,
    TranscriptionNote,
    SurveyRules,
  },
  mixins: [detailMixins],
  props: {
    clientId: Number,
  },
  data() {
    return {
      project: this.$store.state.project.data,
      current_compliance_rules_type: 'client_compliance_rules',
      need_compliance_approval: false,
      need_compliance_approval_latest_change: null,
    }
  },

  computed: {
    ...mapGetters([
      'roles',
      'info',
    ]),
    hasPermission() {
      return this.roles.some(role => {
        return [1, 4].includes(role.role_id)
      })
    },
  },

  mounted() {
    if (this.project.legal_rules) {
      this.need_compliance_approval = this.project.legal_rules.need_compliance_approval
      this.need_compliance_approval_latest_change = this.project.legal_rules.need_compliance_approval_latest_change
    }

    RouterTools.resolveRouteQuery(this.$route)
    if (this.clientId || this.$route.query.client_id) {
      this.getClientComplianceRules(this.clientId || this.$route.query.client_id)
      RouterTools.saveQueryToRouter({ client_id: this.$route.query.client_id })
    }
  },

  methods: {
    updateSurveyApproval() {
      const params = {
        project_id: this.project.id,
        need_compliance_approval: this.need_compliance_approval,
      }
      return ProjectAPI.updateSurveyApproval(params)
        .then(() => {
          this.$store.dispatch('project/getInfo', this.project.id).then(data => {
            this.need_compliance_approval = data.legal_rules.need_compliance_approval
            this.need_compliance_approval_latest_change = data.legal_rules.need_compliance_approval_latest_change
          })
        })
    },
  },
}
</script>

<style scoped lang="scss">

.no-border {
  border: 0;
}

.card-body-custom {
  display: flex;
  align-items: flex-start;
}

::v-deep li {
  margin-bottom: 10px;
}

::v-deep .email-content {
  font-size: 1rem;
  color: #5a5e66;

  ul {
    margin-top: 2px;
  }

  ::v-deep li {
    margin-bottom: 0;
  }
}

.messageBox {
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;

  color: #ccc;
  text-shadow: 1px 1px #ffffffb3;
  font-style: italic;
  text-align: center;
  font-size: 1em;
  padding: 2.4em 0;
}

::v-deep .el-checkbox__label {
  font-size: 14px;
}
</style>
