import { mapGetters, mapState } from 'vuex'
import _ from 'lodash'
import { insertNodeAt, removeNode } from '@/utils/tool'
import Sortable from 'sortablejs'
import ProjectAPI from '@/api/project'
import TaskProgressV2
  from '@/views/project/consultation/detail/components/task-progress/TaskProgressV2'
import MarkBtn from '@/views/project/consultation/detail/components/MarkBtn'
import ContactTags
  from '@/views/project/consultation/detail/components/ContactTags'
import OutreachToAdvisor
  from '@/views/project/consultation/detail/components/SendEmail/OutreachToAdvisor'
import IcoButton from '@/components/IcoButton'
import detailMixins from '@/views/project/consultation/detail/mixins/mixins'
import UploadCaEmail
  from '@/views/project/consultation/detail/client-tasks/angle/TaskAngleTable/components/UploadCaEmail'
import EditTask from '@/views/project/consultation/detail/components/EditTask'
import RefreshButton from '@/components/RefreshButton/index'
import moment from 'moment-timezone'

export default {
  components: {
    Refresh<PERSON><PERSON>on,
    UploadCaEmail,
    TaskProgressV2,
    MarkBtn,
    ContactTags,
    OutreachToAdvisor,
    IcoButton,
    EditTask,
  },
  mixins: [detailMixins],
  props: {
    angle: {
      type: Object,
      default() {
        return {
          id: 0,
          name: '',
          members: {},
          inquiry_branch_id: 0,
          tasks: [],
          rate: null,
          rate_currency: null,
        }
      },
    },
    sequence: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
      taskTableColsWidth: state => state.project.task_cols_width_list,
    }),
    ...mapGetters(['advisor_options', 'project_options']),
    isAngle() {
      return !!this.angle.id
    },
    projectMemberOption() {
      return _.unionBy(this.project.members, 'uid').map(item => {
        return { id: item.uid, name: item.user.name }
      })
    },
    isVettingCall() {
      // https://www.notion.so/capvision/DB-Workflow-Investment-Bank-Client-Type-9485c68aa73a431b8b6b959b05a563dc?pvs=4
      return this.project.sub_type === 'Investor Call' && this.project.client.type === 'INVESTMENT_BANK'
    },
  },
  data() {
    return {
      project: this.$store.state.project.data,
      projectId: this.$route.params.project_id,
      loading: false,
      isCollapse: true,
      _sortable: null,
      tableData: {
        data: [],
        count: 0,
      },
      formThead: [
        'client_contact_id',
        'create_at',
        'events',
        'id',
        'name',
        'project_id',
        'update_at',
      ],
      selectedAdvisor: [],
      clone_select: [],
      need_request_for_re_selected: true,
      selectable: true,
      data: {
        status: -1,
        time_range_type: 1,
        time_range: undefined,
        recent_activities: 0,
      },
      pickerOptions: {
        shortcuts: [
          {
            text: 'Recent Week',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Recent Month',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '3 Months',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  beforeDestroy() {
    if (this._sortable !== undefined) this._sortable.destroy()
  },
  methods: {
    reloadList() {
      this.clone_select = this.selectedAdvisor.map((item) => item.id)
      return this.getList()
    },
    formatList(data) {
      this.tableData.data = data['list']
        .map(item => {
          item.consult_date = formConsultDate(item)
          item.task_hours = formTaskHour(item)
          item.arrange_disabled = this.checkArrangeDisabled(item)
          this.formatListWiseValid(item)
          this.formatMostRecentEmail(item)
          item.client = this.project.client

          return item
        })

      return data

      function formConsultDate(item) {
        if (item.general_status === 'COMPLETED') {
          return item.start_time
        }
        if (item.schedule) {
          return item.schedule.start_time
        }
        return ''
      }

      function formTaskHour(item) {
        if (!item.revenues || !item.revenues.length) {
          return
        }
        const res = {
          client_hours: 0,
          billing_hours: 0,
        }
        item.revenues.forEach(({ client_hours, billing_hours }) => {
          res.client_hours += client_hours
          res.billing_hours += billing_hours
        })
        res.client_hours = res.client_hours.toFixed(4)
        res.billing_hours = res.billing_hours.toFixed(4)

        return res
      }
    },
    formatListWiseValid(item) {
      const list_wise_invalid_status = [
        'invalid',
        'no-reply',
        'spam-trap',
        'bad-mx',
        'bounced',
        'suspicious',
        'unknown',
        'disposable',
        'rejected',
        'deferred']
      if (item.advisor?.email_validation_records?.length) {
        item.advisor_email_list_wise_records = item.advisor.email_validation_records.filter(
          i => i.provider === 'LIST_WISE')
        const latest_status = _.orderBy(item.advisor_email_list_wise_records,
          'create_at', 'desc')[0]
        if (!latest_status) {
          item.list_wise_email_status = 'info'
        } else {
          item.list_wise_email_status = list_wise_invalid_status.includes(
            latest_status.result_status) ? 'danger' : 'success'
        }
      } else {
        item.list_wise_email_status = 'info'
        item.advisor_email_list_wise_records = []
      }
    },
    formatMostRecentEmail(item) {
      item.most_recent_email_status = 'info'
      if (item.advisor?.email_address_score) {
        item.most_recent_email_status = item.advisor.email_address_score.score
          ? 'success'
          : 'danger'
      }
    },
    selectForUser() {
      const selected_list = this.tableData.data.filter((item) =>
        this.clone_select.includes(item.id),
      )

      this.clone_select = []

      selected_list.forEach((item, index) => {
        this.need_request_for_re_selected = index === selected_list.length - 1
        this.$refs.advisorMultipleTable.toggleRowSelection(item)
      })
    },
    handleSelectionChange(val) {
      if (this.clone_select.length || !this.need_request_for_re_selected) {
        return
      }
      this.selectedAdvisor = val
      this.$store.dispatch('tasks/SET_SELECTION_LIST', {
        angle_id: this.angle.id,
        val: this.selectedAdvisor,
      })
    },

    getSelectedTasksDetail(select_list) {
      if (!select_list.length) return
      this.$emit('update:selectLoading', true)
      const ids = select_list.map(item => item.id)
      this.getTasksByIDS(ids)
        .then(data => {
          this.$store.dispatch('tasks/SET_SELECTION_LIST', {
            angle_id: this.angle.id,
            val: data['list'],
          })
          this.selectedAdvisor = data['list']
        })
        .finally(() => {
          this.$emit('update:selectLoading', false)
        })
    },

    getTasksByIDS(ids) {
      const params = {
        params: {
          ids: ids.join(),
          page: 1,
          size: 300,
          extra: [
            'angle',
            'advisor_schedules',
            'advisor.jobs.company',
            'advisor.location',
            'advisor_decline',
            'advisor.current_job',
            'advisor.sourced_at',
            'advisor_profile.company',
            'advisor.email_address_score',
            'advisor.email_validation_records',
            'advisor.jobs.legal_validate_result',
            'advisor.contact_infos',
            'advisor.latest_signed_tc',
            'latest_submitted_pre_ca.files',
            'latest_submitted_pre_ca.inquiry_instance.questions',
            'latest_submitted_pre_ca.inquiry_instance.answers',
            'latest_submitted_post_ca.files',
            'latest_submitted_post_ca.inquiry_instance',
            'latest_submitted_sq.questions',
            'latest_submitted_sq.answers',
            'latest_sq',
            'client.contracts',
            'client.am_team',
            'client.preference',
            'client.compliance_preference',
            'compliance_approve_policy_jit',
            'compliance_passed_jit',
            'loopup_room',
            'project',
            'advisor.location_id_path',
            'default_client_rate_usd',
            'default_client_rate_usd_considering_unsigned_tc',
            'specified_client_rate_currency_after_multiple',
            'standard_rate_multiplier_display_to_client',
            'given_senior_rate_multiplier',
            'task_outsource_info',
          ].join(),
        },
        project_id: this.projectId,
      }
      // 按照用户勾选的顺序返回数据
      return ProjectAPI.getTasksDetail(params).then(data => {
        return {
          list: ids.map(item => {
            const target = data.list.find(i => i.id === item)
            this.formatListWiseValid(target)
            this.formatMostRecentEmail(target)
            return target
          }),
        }
      })
    },

    actionGotoProfile(id) {
      this.$router.push({
        name: 'ConsultantDetail',
        params: { advisor_id: id },
      })
    },
    openToLinkedIn() {
      const temp_ids = []
      this.selectedAdvisor.forEach((item) => {
        if (
          item.advisor.contact_infos.length &&
          !temp_ids.includes(item.advisor_id)
        ) {
          temp_ids.push(item.advisor_id)
          item.advisor.contact_infos.forEach((contact_item) => {
            if (contact_item.type === 'LINKEDIN_URL') {
              window.open(contact_item.value, '_blank')
            }
          })
        }
      })
    },

    isShowActionBtn(row, action) {
      const client_selected = row.client_contact_status === 'APPROVED'
      const investor_call_client_requested = !!row.investor_call?.client_requested
      const investor_call_not_completed = !row.investor_call?.completed
      const not_completed = !['COMPLETED'].includes(row['general_status'])
      switch (action) {
        case 'vettingCall':
          // from outlook email 'Investment Bank Clients Bugs/Updates' 2024-01-08
          return investor_call_client_requested && investor_call_not_completed && not_completed && this.isVettingCall && !this.isSGDB
        case 'arrange': {
          const compliance_can_arrange = row.client_compliance_status !== 'REJECTED'

          const is_advisor = row.advisor.tc_term_valid['CONSULTATION']
          let need_compliance = false

          // 客户规则 未签TC OR approval/to compliance 可以arrange
          const client_rules_can_arrange = !this.checkClientRulesDisabled(row)

          // 如需审核，审核中/已通过/直接通过 均可 Arrange
          const compliance_in_process = [
            'SENT',
            'APPROVED',
            'DIRECT_APPROVED',
          ].includes(row['capvision_compliance_status']) && is_advisor

          // Email to compliance 前后用不同的值来判断
          if (row.capvision_compliance_status === 'INITIAL') {
            if (!this.project.client) return false

            const compliance_preference = this.project.client['compliance_preference']

            // 无设置要求可随意 Arrange
            if (!compliance_preference) {
              need_compliance = true
            } else {
              // 无需审核则可直接 Arrange
              const flag =
                compliance_preference.approve_policy === 'NO_NEED'
              need_compliance = flag ? true : compliance_in_process
            }
          } else {
            // 无需审核则可直接 Arrange
            const flag =
              !row.compliance_approve_policy_jit ||
              row.compliance_approve_policy_jit === 'NO_NEED'
            need_compliance = flag ? true : compliance_in_process
          }

          // 若专家是导出到Outsource DB使用的，且在Outsource DB中已经Arrange了，则可以Arrange（无需这里的Client Select）
          const is_exported_to_outsource_arranged = row.task_outsource_status === 'EXPORTED' &&
            row.task_outsource_info.task_outsource_general_status === 'ARRANGED'

          return not_completed &&
            (need_compliance || client_rules_can_arrange) &&
            compliance_can_arrange &&
            (client_selected || is_exported_to_outsource_arranged)
        }
        case 'complete': {
          // 从中国导的已经completed 的task 不可以再次 complete
          const cn_complete_task = row.ndb_id !== null &&
            row.prohibit_complete_again
          // survey 类型的项目随时可以 complete
          if (this.project.sub_type === 'Survey') {
            return !cn_complete_task
          } else {
            // task 状态已经到arranged||completed，视为流程中法务已经审核通过
            // task 状态SCHEDULED，展示操作图标，但disabled
            return ['SCHEDULED', 'ARRANGED', 'COMPLETED'].includes(row.general_status) && !cn_complete_task
          }
        }
        case 'cancel':
          return row['schedule']
        case 'uploadCAEmail': {
          const ca_sent = row.ca_status === 'SENT'

          const latest_pre_ca = row['latest_pre_ca']
          const ca_need_upload = latest_pre_ca &&
            ['EMAIL', 'EXTERNAL'].includes(latest_pre_ca.method)

          return ca_sent && ca_need_upload
        }
        case 'uploadPostCAEmail': {
          const ca_sent = row.post_ca_status === 'SENT'

          if (!row['post_ca_list'].length) return false
          const latest_post_ca = row['post_ca_list'].reduce((p, v) =>
            p.id < v.id ? v : p,
          )
          const is_email_way = latest_post_ca.method === 'EMAIL'

          return ca_sent && is_email_way
        }
      }
    },

    checkArrangeDisabled(row) {
      return this.checkClientExecute(row) || this.checkGenerationIMCode(row) ||
        this.checkAdvisorInBlacklist(row) || this.checkClientRulesDisabled(row) ||
        this.checkOutsourceApprovedDisabled(row) || this.checkNpiValidatedDisabled(row)
    },

    checkClientExecute(row) {
      return row.client && row.client.status !== 'EXECUTE'
    },

    checkGenerationIMCode(row) {
      const isGeneration = [
        this.GenerationIMId,
        this.GenerationGrowthEquityId].includes(+row.client_id)
      return isGeneration && (!row.client_custom_fields ||
        !row.client_custom_fields['task_expert_code'])
    },

    checkAdvisorInBlacklist(row) {
      return row.advisor.status === 'BLACKLIST'
    },

    checkClientRulesDisabled(row) {
      const client_compliance_rules = this.project.client.compliance_preference?.rule?.compliance_rules
      const client_has_call_scheduling_rules = client_compliance_rules &&
        client_compliance_rules.call_scheduling_emails?.master_switch &&
        client_compliance_rules.call_scheduling_emails?.can_send_placeholder_calendar_invite_before_approval
      const client_can_arrange_before_signed_tc_or_approved = client_has_call_scheduling_rules &&
        client_compliance_rules.call_scheduling_emails?.send_placeholder_calendar_invite_before_approval_type ===
        'Can send'
      const is_leads = !row.advisor.tc_term_valid['CONSULTATION']
      const task_need_approval = row.compliance_approve_policy_jit !==
        'NO_NEED' && !row.compliance_passed_jit
      const is_not_imported = row.task_outsource_status !== 'IMPORTED'

      row.arrange_need_tc_signed = !client_can_arrange_before_signed_tc_or_approved &&
        is_leads && is_not_imported

      row.arrange_need_task_approved = !client_can_arrange_before_signed_tc_or_approved &&
        task_need_approval

      // 客户规则没有勾选 call scheduling email:send_placeholder_calendar_invite_before_approval_type时,
      // 没有签TC的专家或没有approved的task.不可以 arrange
      return row.arrange_need_tc_signed || row.arrange_need_task_approved
    },

    checkOutsourceApprovedDisabled(row) {
      // 若为Outsource协作专家，且不为Survey项目，应在外部法务审核通过后才可以 arrange
      row.arrange_need_outsource_approved = row.task_outsource_info && this.project.sub_type !== 'Survey'
        ? row.task_outsource_info.task_outsource_compliance_status !== 'APPROVED'
        : false
      return row.arrange_need_outsource_approved
    },

    checkNpiValidatedDisabled(row) {
      // 若Task的Angle为Physician Angle，专家需验证通过NPI才可以 arrange
      row.arrange_need_validated_npi = this.angle.tags?.includes('PHYSICIAN')
        ? row.advisor.npi_status !== 'VALID'
        : false
      return row.arrange_need_validated_npi
    },

    goToTaskArrange(row, type) {
      const router_name = type === 'vetting-call' ? 'TasksVettingCall' : 'TasksArrange'
      this.$router.push({
        name: router_name,
        query: { id: row.id, client_id: row.client_id },
      })
    },

    goToTaskComplete(id) {
      let router_name = 'TaskComplete'
      switch (this.project.sub_type) {
        case 'Survey':
          router_name = 'SurveyTaskComplete'
          break
        case 'Patients':
          router_name = 'PatientsTaskComplete'
          break
      }
      const route = this.$router.resolve({
        name: router_name,
        query: { id },
      })
      window.open(route.href, '_blank')
    },
    cancelArrangedSchedule(item) {
      this.$confirm(
        `Confirm to cancel arranged schedule in task "${item.advisor.name_prefix || ''} ${item.advisor.full_name}"?`,
        'Notice',
        {
          type: 'warning',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              return ProjectAPI.cancelArrangedScheduleByTask(item.id)
                .then(() => {
                  done()
                })
                .finally(() => {
                  instance.confirmButtonLoading = false
                })
            } else {
              done()
            }
          },
        },
      )
        .then(
          () => {
            item.schedule.status = 'CANCELLED'
            this.$message({
              type: 'success',
              message: 'Cancel Schedule Success!',
            })
          },
          () => {
          },
        )
    },
    handleCollapseClick() {
      this.isCollapse = !this.isCollapse
    },
    loadSortable(ref_table = this.$refs['advisorMultipleTable']) {
      const table =
        ref_table &&
        ref_table.$el.querySelector(
          '.el-table__body-wrapper tbody',
        )

      if (!table) return

      var options = {
        group: {
          name: 'AngleTasks',
          pull: !this.angle.angle_for_outsource_import,
          put: !this.angle.angle_for_outsource_import,
        },
        handle: '.cursor-move',
        filter: '.locked',
        fallbackOnBody: true,
        onChoose: ({ oldIndex, item }) => {
          item.id = this.tableData.data[oldIndex].id
        },
        onStart: evtData => {
          onStart.call(this, evtData)
        },
        onUpdate: evtData => {
          onEnd.call(this, evtData)
        },
        onRemove: ({ oldIndex, item, from }) => {
          insertNodeAt(from, item, oldIndex)
          const index = this.tableData.data.findIndex(i => +i.id === +item.id)
          if (index > -1) this.tableData.data.splice(index, 1)
        },
        onAdd: evtData => {
          onAdd.call(this, evtData)
        },
      }

      this._sortable = new Sortable(table, options)

      function onStart() {
        this.tableData.data.forEach((item, index) => {
          this.toggleRowExpansion(this.tableData.data[index], false)
        })
      }

      function onEnd({ newIndex, oldIndex, item, from }) {
        if (newIndex === oldIndex) return

        this.saveAngleRank(newIndex, oldIndex, item, from)
      }

      function onAdd({ newIndex, oldIndex, item, from }) {
        this.moveToAngle(newIndex, oldIndex, item, from)
      }
    },
    saveAngleRank(newIndex, oldIndex, drag_node, drag_parent) {
      // 向下移动 放置在newIndex位置的task后面
      // 向上移动 放置在newIndex-1位置的task后面
      const prev_task_index = newIndex - oldIndex > 0 ? newIndex : newIndex - 1

      // leeds index与rank的对应关系
      // index: 0->1->2->3->4
      // rank:  1->2->3->4->5
      // 向下移动 rank = newIndex+1 放置在这个rank后面
      // 向上移动 rank = newIndex 放置在这个rank后面
      const prev_lead_index = newIndex - oldIndex > 0 ? newIndex + 1 : newIndex
      const rank =
        newIndex === 0
          ? 0.5
          : this.isAngle
            ? this.tableData.data[prev_task_index].rank + 0.5
            : prev_lead_index + 0.5

      return ProjectAPI.updateTaskRank(
        this.project.id,
        {
          angle_id: this.isAngle ? this.angle.id : null,
          id_position_map: {
            [`${drag_node.id}`]: rank,
          },
        },
      )
        .then(data => {
          // 避免sortable.js操作DOM 还原DOM
          // 强制使用vue更新页面
          removeNode(drag_node)
          insertNodeAt(drag_parent, drag_node, oldIndex)

          this.setNewDisplayId(data)
        })
    },
    moveToAngle(newIndex, oldIndex, drag_node) {
      const rank =
        newIndex === 0
          ? 0.5
          : this.tableData.data[newIndex - 1].rank + 0.5

      return ProjectAPI.moveToAngle(
        this.project.id,
        {
          angle_id: this.isAngle ? this.angle.id : null,
          task_ids: [+drag_node.id],
          id_position_map: {
            [`${+drag_node.id}`]: rank,
          },
        },
      ).then(() => {
        removeNode(drag_node)

        this.reloadList()
      })
    },
    setNewDisplayId(new_data) {
      var id_map = {}
      new_data.forEach(item => {
        id_map[item.id] = item
      })

      this.tableData.data.forEach(task => {
        task.display_id = id_map[task.id]['display_id']
        task.rank = id_map[task.id]['rank']
      })

      this.tableData.data.sort((a, b) => a.rank - b.rank)
    },
    toggleRowExpansion(row, expanded) {
      this.$refs.advisorMultipleTable.toggleRowExpansion(row, expanded) // 点击button展开
    },
    batchRemove() {
      this.deleteWarning()
        .then(
          () => {
            this.removeUpdate()
          },
          () => {
          },
        )
    },

    goToTasksArrange() {
      /*
      当只操作一个 Task 时，警告信息用弹窗显示
      当操作多个 Task 时，如有通过检测的则跳转
       */
      const not_passed_tasks = checkIsOkToArrange(this.selectedAdvisor)
      const is_single_not_passed =
        this.selectedAdvisor.length === 1 && not_passed_tasks.length === 1

      if (is_single_not_passed) {
        this.$alert(
          'Task ' +
          this.selectedAdvisor[0].id +
          ' is not allow to Arrange due it has been Completed',
          'Cannot Arrange',
        )
      } else {
        this.$router.push({
          name: 'TasksArrange',
          query: {
            id: this.selectedAdvisor.map((item) => item.id)
              .join(','),
            client_id: this.selectedAdvisor[0].client_id,
          },
        })
      }

      function checkIsOkToArrange(tasks) {
        const not_passed_tasks = []

        tasks.forEach((item) => {
          if (taskCannotArrange(item)) {
            not_passed_tasks.push(item)
          }
        })

        return not_passed_tasks

        /**
         * 已经被 Complete 不可再 Arrange
         * @param data
         * @returns {boolean}
         */
        function taskCannotArrange(data) {
          return ['COMPLETED'].includes(data.general_status)
        }
      }
    },
    removeUpdate() {
      const params = this.selectedAdvisor
        .filter(item => {
          return item.client_compliance_status === 'INITIAL' &&
            item.client_contact_status === 'INITIAL'
        })
        .map(item => {
          return item.id
        })
      ProjectAPI.deleteTask(params)
        .then(() => {
          this.getList()
        })
    },
    hadSentToClient(item) {
      return item.client_compliance_status !== 'INITIAL' ||
        item.client_contact_status !== 'INITIAL'
    },

    jobDateFormat(date) {
      const list = date.split('-')
      switch (list.length) {
        case 2:
          if (!list[1] || ['undefined', 'null', ' '].includes(list[1])) {
            return moment(list[0])
              .format('YYYY')
          } else {
            return moment(date)
              .format('MM/YYYY')
          }
        case 3:
          return moment(date)
            .format('MM/DD/YYYY')
        default:
          return date
      }
    },

    checkSelectable(row) {
      // GDPR 专家已删除 & 专家在客户黑名单中 & local Outsource Approval已Approved
      return !row.advisor?.gdpr_delete_by_id && !row.advisor_blocked_status?.length && this.selectable
    },
    tableHeaderCellClassName({ row, column, rowIndex }) {
      if (this.taskTableColsWidth) {
        const filter_item = this.taskTableColsWidth.find(item => item.label === column.label)
        if (filter_item) {
          column.width = filter_item.width
        }
      }

      if (column.label === 'Opened') {
        return 'custom-hidden-sort-asc'
      }
    },

    handleHeaderDragend(newWidth, oldWidth, column, event) {
      if (!this.taskTableColsWidth) this.taskTableColsWidth = []
      column.width = Math.ceil(newWidth)
      const target = this.taskTableColsWidth?.find(item => item.label === column.label)
      if (this.taskTableColsWidth && target) {
        target.width = column.width
      } else {
        this.taskTableColsWidth.push({ label: column.label, width: column.width })
      }

      this.$emit('updateTableColsWidth')
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.advisor?.gdpr_delete_by_id) {
        if (columnIndex === 7) {
          return [1, 4]
        } else if ([7, 8, 9, 10].includes(columnIndex)) {
          return [0, 0]
        }
      }
    },
  },
}

