import Client<PERSON><PERSON> from '@/api/client'
import ProjectAP<PERSON> from '@/api/project'
import ConsultantAP<PERSON> from '@/api/consultant'
import UserAP<PERSON> from '@/api/user'
import _ from 'lodash'
import ProjectWebSocketAlert from '@/components/WebSocket/unreadAlert'
import { mapState } from 'vuex'

export default {
  components: { ProjectWebSocketAlert },
  props: {
    screened: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      session_key: `${this.$route.name}-${this.$route.params.project_id}`,
      advisorProfileForm: {
        visible: false,
        position: '',
      },
      supportMembers: this.initSupportMembers(),
      rulesLoading: false,
      client_compliance_rules: {},
      has_rule: false,
      client_am: null,
    }
  },
  computed: {
    ...mapState({
      unreadEvents: state => state.project.unread_subscription_events,
    }),
  },
  watch: {
    // 点击新消息提示，展示对应task
    'unreadEvents.mask_read_client_task': {
      handler(newVal) {
        if (newVal) {
          this.angle_view_type = 'stacked'
          const client_task_ids = this.unreadEvents.client_task_ids
          const new_angle_ids = client_task_ids.map(item => parseInt(item.split('-')[0]))
          const new_task_ids = client_task_ids.map(item => item.split('-')[1])
          const origin_task_ids = this.searchTaskIds ? this.searchTaskIds.split() : []

          this.selectedAngleIds = [...new Set(this.selectedAngleIds.concat(new_angle_ids))]
          this.searchTaskIds = [...new Set(origin_task_ids.concat(new_task_ids).map(Number))].join()

          this.unreadEvents.client_task_ids = []
          this.unreadEvents.mask_read_client_task = false
        }
      },
    },
  },
  methods: {
    deleteWarning() {
      return this.$confirm('Confirm delete?', 'Warning', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
        cancelButtonClass: 'el-button--text',
      })
    },
    getClientComplianceRules(client_id) {
      this.rulesLoading = true

      return ClientAPI.getCompliancePreference(client_id).then(data => {
        const compliance_preference = data['compliance_preference']
        this.client_am = data.account_managers.filter(item => item.user)
          .map(item => item.user.name)
          .join(', ')

        if (compliance_preference && compliance_preference.rule) {
          const compliance_rules = compliance_preference.rule.compliance_rules

          if (compliance_rules) {
            this.client_compliance_rules = Object.assign({}, compliance_rules)
            this.has_rule = true
          }
        }
      }, error => {
        this.$message({
          showClose: true,
          message: error,
          type: 'error',
        })
      }).finally(() => {
        this.rulesLoading = false
      })
    },
    initSupportMembers() {
      return this.$store.state.project.data.members
        .reduce((accumulator, { user }) => {
          if (!accumulator.map(({ user_id }) => user_id).includes(user.id)) {
            accumulator.push({
              name: user.name,
              user_id: user.id,
            })
          }
          return accumulator
        }, [])
    },
    changeTaskJob(job, row) {
      if (['company_id', 'position', 'start_date', 'end_date', 'is_current']
        .every(key => job[key] === row.advisor_profile[key])) {
        return
      }
      const params = {
        id: row.advisor_profile.id,
        company_id: job.company.id,
        position: job.position,
        start_date: job.start_date,
        end_date: job.end_date,
        job_id: job.id,
        is_current: job.is_current,
      }

      return ProjectAPI.updateTaskAdvisorProfile(row.id, params)
        .then(data => {
          // row.advisor_profile_id = job.id
          row.advisor_profile = data
          this.$message({
            message: 'success',
            type: 'success',
          })
        })
    },
    changeStatus(status, row) {
      const params = {
        id: row.advisor.id,
        status,
      }

      return ConsultantAPI.updateConsultant(params)
        .then(data => {
          row.advisor.status = status
          this.$message({
            message: 'success',
            type: 'success',
          })
        })
    },
    changeTaskAdvisorProfilePosition(ref, row, from) {
      if (from.position === row.advisor_profile.position ||
        !from.position) {
        return
      }
      const params = {
        id: row.advisor_profile.id,
        position: from.position,
      }

      return ProjectAPI.updateTaskAdvisorProfile(row.id, params)
        .then(data => {
          row.advisor_profile = data
          ref.doClose()
          this.$message({
            message: 'success',
            type: 'success',
          })
        })
    },
    changeTaskSupport(member, row) {
      if (row.support_uid !== member.user_id) {
        const params = {
          project_id: this.projectId,
          id: row.id,
          support_uid: member.user_id,
        }
        return ProjectAPI.updateTask(params)
          .then(data => {
            row.support_uid = data.support_uid
            row.support = data.support
            this.$message.success('success')
          })
      }
    },

    updateTasksTableColsWidth() {
      // 保存当前用户 对这个项目 task 列表列宽的设置
      UserAPI.updateUserRemoteStorage(`project_task_cols_width_${this.projectId}`,
        this.$store.state.project.task_cols_width_list)
    },

    reloadList(all = false) {
      const selectionMap = _.cloneDeep(Object.keys(this.$store.state.tasks.selectionMap))
      const reload_angle_list = all ? this.angleList.map(item => item.id) : selectionMap
      reload_angle_list.forEach(item => {
        if (this.$refs[`taskAngleTable${item}`]) {
          this.$refs[`taskAngleTable${item}`].forEach(angle => {
            angle.reloadList()
          })
        }
      })
    },

    saveDataToSessionStorage() {
      const angle_data = {
        view_type: this.angle_view_type,
        selected_angle_ids: this.selectedAngleIds,
        active_angle_id: this.active_angle_id,
        selected_task_ids: this.searchTaskIds,
      }
      sessionStorage.setItem(this.session_key, JSON.stringify(angle_data))
    },
    clearSelectedAndReload() {
      const angle_ids = Array.from(new Set(this.selectedAdvisor.map(item => item.angle_id)))
      angle_ids.forEach(item => {
        if (this.$refs[`taskAngleTable${item}`]) {
          this.$refs[`taskAngleTable${item}`].forEach(angle => {
            angle.$refs.advisorMultipleTable.clearSelection()
            angle.reloadList()
          })
        }
      })
    },

    moveTasksDone(tartget_angle_id) {
      if (!(`${tartget_angle_id}` in this.$store.state.tasks.selectionMap)) {
        this.$store.state.tasks.selectionMap[`${tartget_angle_id}`] = {}
      }
      const selectionMap = _.cloneDeep(Object.keys(this.$store.state.tasks.selectionMap))
      this.$store.dispatch('tasks/CLEAR_SELECTION_LIST')

      selectionMap.forEach(item => {
        if (this.$refs[`taskAngleTable${item}`]) {
          this.$refs[`taskAngleTable${item}`].forEach(angle => {
            angle.$refs.advisorMultipleTable.clearSelection()
            angle.reloadList()
          })
        }
      })
    },

    handleSelectAngle(ids) {
      if (ids.length > 0) {
        ids.forEach(item => {
          this.$refs[`taskAngleTable${item}`][0].reloadList()
        })
      }

      this.clearSearchByTaskId()
    },
    // angle view tab
    viewTabClick() {
      const angle_ids = Array.from(new Set(this.selectedAdvisor.map(item => item.angle_id)))
      angle_ids.forEach(item => {
        this.$refs[`taskAngleTable${item}`].forEach(angle => {
          angle.$refs.advisorMultipleTable.clearSelection()
        })
      })
    },

    removeTasksDone() {
      const selectionMap = _.cloneDeep(Object.keys(this.$store.state.tasks.selectionMap))
      selectionMap.forEach(item => {
        if (this.$refs[`taskAngleTable${item}`]) {
          this.$refs[`taskAngleTable${item}`].forEach(angle => {
            angle.reloadList()
          })
        }
      })
      this.$store.dispatch('tasks/CLEAR_SELECTION_LIST')
    },

  },
}
