import moment from 'moment-timezone'

export default {
  data() {
    return {
      /**
       * Ask gpt to convert for us
       */
      deciperSurveyStatusDict: [
        {
          value: 'NOT_ENTERED',
          label: 'Not Entered',
        },
        {
          value: 'QUALIFIED',
          label: 'Qualified',
        },
        {
          value: 'TERMINATED',
          label: 'Terminated',
        },
        {
          value: 'OVERQUOTA',
          label: 'Overquota',
        },
        {
          value: 'INCOMPLETE',
          label: 'Incomplete',
        },
        {
          value: 'QUALIFIED_AND_TERMINATED',
          label: 'Qualified & Terminated',
          expanded_values: [
            'QUALIFIED',
            'TERMINATED',
          ],
        },
        {
          value: 'QUALIFIED_AND_OVERQUOTA',
          label: 'Qualified & Overquota',
          expanded_values: [
            'QUALIFIED',
            'OVERQUOTA',
          ],
        },
        {
          value: 'TERMINATED_AND_OVERQUOTA',
          label: 'Terminated & Overquota',
          expanded_values: [
            'TERMINATED',
            'OVERQUOTA',
          ],
        },
        {
          value: 'QUALIFIED_TERMINATED_AND_OVERQUOTA',
          label: 'Qualified & Terminated & Overquota',
          expanded_values: [
            'QUALIFIED',
            'TERMINATED',
            'OVERQUOTA',
          ],
        },
        {
          value: 'MANUAL_QUALIFIED',
          label: 'Qualified',
          is_manual: true,
          mask_value: 'QUALIFIED',
        },
        {
          value: 'MANUAL_TERMINATED',
          label: 'Terminated',
          is_manual: true,
          mask_value: 'TERMINATED',
        },
        {
          value: 'MANUAL_OVERQUOTA',
          label: 'Overquota',
          is_manual: true,
          mask_value: 'OVERQUOTA',
        },
      ],

      syncInfoDivStyle: {
        fontSize: '10px',
        fontStyle: 'italic',
        fontWeight: 'normal',
      },
    }
  },
  computed: {
    decipherStatusSyncedStr() {
      const project = this.$store.state.project.data
      return this.generateTimeAgoStr(
        project.decipher_statuses_last_update)
    },
    /**
     * @return {boolean}
     */
    projectHasDecipherSurveyId() {
      const project = this.$store.state.project.data
      if (project.sub_type !== 'Survey') return false
      return !!project?.decipher_id
    },
  },
  methods: {
    generateTimeAgoStr(isoTimeString) {
      if (!isoTimeString) return ''
      const now = moment()
      const time = moment(isoTimeString)
      const diffDuration = moment.duration(now.diff(time))

      if (diffDuration.asMilliseconds() < 0) {
        return 'in the future'
      }

      const days = diffDuration.days()
      const hours = diffDuration.hours()
      const minutes = diffDuration.minutes()

      const parts = []
      if (days > 0) parts.push(`${days}d`)
      if (hours > 0) parts.push(`${hours}h`)
      if (days <= 0 && minutes > 0) parts.push(`${minutes}m`)

      return parts.length > 0
        ? `synced ${parts.join('')} ago`
        : 'synced just now'
    },

    /**
     * @param {string,undefined} statusVal
     * @return {boolean}
     */
    isManuallySetStatus(statusVal) {
      if (!statusVal) return false
      return statusVal?.includes('MANUAL')
    },
  },
}
