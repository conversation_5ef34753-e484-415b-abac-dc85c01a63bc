<template>
  <div>
    <div class="flex align-items-center justify-content-between">
      <div>
        <div class="mb-5px">
          <div>
            <add-to-project
              :advisors.sync="selected_list"
              :init-project="projectInfo"
              @update="clearSelection()" />

            <add-to-cart :advisors="selected_list" />

            <el-switch
              v-model="isListView"
              active-text="List View"
              class="ml-3"
              @change="actionToggleView" />

            <el-button
              v-if="!isListView"
              type="info"
              class="ml-3"
              icon="el-icon-office-building"
              @click="openAddCompanyTopicDialog('addCompanyDialog')"
            >
              Add Company
            </el-button>
            <el-button
              v-if="!isListView"
              type="warning"
              class="ml-3"
              icon="el-icon-chat-line-round"
              @click="openAddCompanyTopicDialog('addTopicDialog')"
            >
              Add Topic
            </el-button>
          </div>
        </div>

        <div class="mb-5px">
          <div class="inline-block">
            <el-select
              v-model="all_filter.project_status"
              multiple
              clearable
              collapse-tags
              placeholder="Project Status"
            >
              <el-option
                v-for="item in options.project_status"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
            <el-checkbox v-model="all_filter.advisor_not_in_project_id">
              Filter out experts already attached
            </el-checkbox>
          </div>
          <div v-if="isListView ? isScreenedTab : true" class="tab-filter-item">
            <holding-advisor-sort :sort.sync="screened_filter.sort" />
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>Screened</b> Tab</div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
          <div v-if="isListView ? !isScreenedTab : true" class="tab-filter-item">
            <el-date-picker
              v-model="formers_filter.start_date_gte"
              type="month"
              format="MM/yyyy"
              placeholder="Former Start"
              value-format="yyyy-MM"
            />
            <el-date-picker
              v-model="formers_filter.end_date_lte"
              type="month"
              format="MM/yyyy"
              placeholder="Former End"
              value-format="yyyy-MM"
            />
            <el-checkbox v-model="formers_filter.same_current_and_former_employer">
              Exclude experts with the same current and former employer
            </el-checkbox>
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>Formers</b> Tab</div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
          <div v-if="isListView" class="tab-filter-item">
            <advisor-search
              v-model="list_view_filter.advisor_id"
              class="w-150px"
              :multiple="false"
              :initial-id="$route.query.advisor_id"
            />
            <el-tooltip placement="top">
              <div slot="content">Only works on <b>List View</b></div>
              <el-link type="primary" :underline="false" icon="el-icon-question" />
            </el-tooltip>
          </div>
        </div>
      </div>

      <el-tabs v-if="isListView" v-model="active_tab_label">
        <el-tab-pane label="Screened" name="Screened" />
        <el-tab-pane label="Formers" name="Formers" />
      </el-tabs>
    </div>

    <div v-show="!isListView" v-loading="loading">
      <el-tabs v-model="active_type" type="card" @tab-click="actionToggleView">
        <el-tab-pane label="Investment Target" name="InvestmentTarget">
          <span slot="label"><i class="el-icon-data-line" /> &nbsp;Investment Target</span>
          <holding-item
            v-for="item in holding_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            :formers-filter="formers_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @update-tag="updateTagAfterRegenerate(item, 'tag', $event)"
          />
          <div v-if="!holding_list.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
        <el-tab-pane label="Companies" name="Companies">
          <span slot="label"><i class="el-icon-office-building" /> &nbsp;Companies</span>
          <company-topic-item
            v-for="item in company_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            :formers-filter="formers_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @update-tag="updateTagAfterRegenerate(item, 'advisor_tag', $event)"
          />
          <div v-if="!company_list.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
        <el-tab-pane label="Topics" name="Topics">
          <span slot="label"><i class="el-icon-chat-line-square" /> &nbsp;Topics</span>
          <company-topic-item
            v-for="item in topic_list"
            :key="item.format_id"
            :ref="`holdingTable${item.format_id}`"
            :holding="item"
            :all-filter="all_filter"
            :screened-filter="screened_filter"
            class="mb-8"
            @selection-change="itemSelected($event, item)"
            @update-tag="updateTagAfterRegenerate(item, 'advisor_tag', $event)"
          />
          <div v-if="!topic_list.length" class="no-data-box">No Data.</div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <holding-advisor-table
      v-if="isListView"
      ref="holdingAdvisorTable"
      v-loading="loading"
      :is-list-view="isListView"
      :is-collapse="false"
      :active-tab-label="active_tab_label"
      :tag-ids="ticker_tag_ids"
      :all-filter="all_filter"
      :screened-filter="screened_filter"
      :formers-filter="formers_filter"
      :holding-list="holding_list"
      @selection-change="itemSelected($event)"
    />

    <add-company-dialog
      ref="addCompanyDialog"
      :project="projectInfo"
      :company-list="company_list"
      :topic-list="topic_list"
      @update="updateAdvisorTagMaps"
    />
    <add-topic-dialog
      ref="addTopicDialog"
      :project="projectInfo"
      :company-list="company_list"
      :topic-list="topic_list"
      @update="updateAdvisorTagMaps"
    />
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import AddToProject from '@/views/consultant/components/addToProject'
import HoldingItem from '@/views/client/detail/AMDashboard/HoldingItem'
import HoldingAdvisorSort from '@/views/client/detail/AMDashboard/components/HoldingAdvisorSort'
import HoldingAdvisorTable from '@/views/client/detail/AMDashboard/HoldingAdvisorTable'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import CompanyTopicItem from '@/views/client/detail/AMDashboard/CompanyTopicItem'
import AddTopicDialog from '@/views/client/detail/AMDashboard/components/AddTopicDialog'
import AddCompanyDialog from '@/views/client/detail/AMDashboard/components/AddCompanyDialog'
import AddToCart from '@/components/ShoppingCart/AddToCart'

export default {
  name: 'ProjectRecommendations',
  components: {
    HoldingItem, AddToProject, HoldingAdvisorSort, HoldingAdvisorTable, AdvisorSearch, CompanyTopicItem,
    AddTopicDialog, AddCompanyDialog, AddToCart,
  },
  props: {
    projectInfo: Object,
  },
  data() {
    return {
      loading: false,
      options: {
        project_status: [
          'Call Completed',
          'Scheduled',
          'Sent to Client',
          'Screened',
        ],
      },
      selected_list: [],
      selected_holding_list: [],
      holding_list: [],
      holding_list_origin: [],
      company_list: [],
      topic_list: [],
      ticker_tag_ids: '',
      active_tab_label: 'Screened',
      active_type: 'InvestmentTarget',

      all_filter: {
        project_status: [],
        advisor_not_in_project_id: true,
      },
      screened_filter: {
        sort: 'match_type_bits desc,latest_sq_submit_time desc',
      },
      formers_filter: {
        start_date_gte: '',
        end_date_lte: '',
        same_current_and_former_employer: false,
      },
      list_view_filter: {
        advisor_id: null,
      },

      isListView: false,
    }
  },

  computed: {
    ...mapGetters([
      'info',
      'uid',
    ]),
    isScreenedTab() {
      return this.active_tab_label === 'Screened'
    },
  },

  mounted() {
    this.getHoldingList()
    this.initRecommendations()
  },

  methods: {
    getHoldingList() {
      if (!this.projectInfo) return
      const companies = this.projectInfo.investment_target_companies.filter(item => item.stock_code)
      this.holding_list = companies.map((item, index) => {
        item.format_id = `${item.id}-${index}`
        item.stock_name = item.name
        item.stock_ticker = item.stock_code
        return item
      })
      this.holding_list_origin = [...this.holding_list]
    },

    itemSelected(val, holding) {
      if (holding) {
        const data = { list: val, format_id: holding.format_id }
        const selected_index = this.selected_holding_list.findIndex(item => item.format_id === data.format_id)

        if (selected_index > -1) {
          this.selected_holding_list[selected_index] = data
        } else {
          this.selected_holding_list.push(data)
        }
        const advisor_list = []
        this.selected_holding_list.forEach(item => {
          advisor_list.push(...item.list.map(i => i.advisor))
        })
        this.selected_list = _.uniq(advisor_list, 'id')
      } else {
        this.selected_list = _.uniq(val.map(i => i.advisor), 'id')
      }
    },

    actionToggleView() {
      this.selected_list = []
      this.$nextTick(() => {
        this.clearSelection()
      })
    },

    clearSelection() {
      if (!this.isListView) {
        this.selected_holding_list.forEach(item => {
          this.$refs[`holdingTable${item.format_id}`][0].clearSelection()
        })
        this.selected_holding_list = []
      } else {
        this.$refs['holdingAdvisorTable'].clearSelection()
      }
    },

    initRecommendations() {
      this.loading = true
      return Promise.all([this.getHoldingTagsByTicker(), this.getHoldingTagsById()])
        .then(() => {
          this.getAllTagIds()
        })
        .finally(() => {
          this.loading = false
        })
    },

    async getHoldingTagsByTicker() {
      const company_ids = this.holding_list_origin.map(item => item.id).join()

      let list = []
      if (company_ids) {
        const res = await ConsultantAPI.getAdvisorTagsByTicker({
          'company_maps.company_ids': company_ids,
          reevaluate_stale: true,
          size: 999,
          extra: 'third_party_company_maps.third_party_company',
        })
        list = res.list
      }

      this.holding_list.forEach(item => {
        const tag = list.find(tag => {
          return tag.third_party_company_maps.some(m => m.third_party_company.ticker === item.stock_ticker)
        })
        this.$set(item, 'tag', tag)
        this.$set(item, 'tag_id', tag?.id)
      })
    },

    async getHoldingTagsById() {
      const { list } = await ConsultantAPI.getAdvisorTagsByTicker({
        'project_maps.project_id': this.$route.params.project_id,
        reevaluate_stale: true,
        size: 999,
        extra: 'third_party_company_maps.third_party_company',
      })
      list.forEach(item => {
        item.advisor_tag = item
        item.format_id = item.id
      })
      this.company_list = list.filter(item => item.advisor_tag.category === 'COMPANY')
      this.topic_list = list.filter(item => item.advisor_tag.category === 'OTHER')
    },

    getAllTagIds() {
      const ticker_tag_ids = [...this.company_list, ...this.topic_list].map(item => item.advisor_tag.id)
      this.holding_list.forEach(item => {
        const tag = item.tag
        if (tag?.id && !ticker_tag_ids.includes(tag.id)) {
          ticker_tag_ids.push(tag.id)
        }
      })
      this.ticker_tag_ids = ticker_tag_ids.join()
    },

    openAddCompanyTopicDialog(name) {
      this.$refs[name].show()
    },

    async updateAdvisorTagMaps() {
      try {
        this.loading = true
        await this.getHoldingTagsById()
        this.getAllTagIds()
      } finally {
        this.loading = false
      }
    },

    updateTagAfterRegenerate(item, tagKey, val) {
      for (const key in item[tagKey]) {
        if (typeof item[tagKey][key] !== 'object') {
          item[tagKey][key] = val[key]
        }
      }
    },
  },
}
</script>

<style scoped>
.no-data-box{
  width: 100%;
  height: 200px;
  line-height: 200px;
  text-align: center;
  color: #909399;
}

.tab-filter-item {
  display: inline-block;
  background-color: #EBEEF5;
  padding: 5px;
  margin: 2px;
}

::v-deep .el-tabs__header {
  margin-bottom: 0;
}

::v-deep .el-date-editor.el-input {
  width: 180px;
}
</style>

