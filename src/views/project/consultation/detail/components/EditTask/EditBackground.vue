<template>
  <div>
    <h4 class="task-edit-title">
      Background
      <el-link
        v-if="!isEditing"
        class="ml-3"
        type="primary"
        size="mini"
        icon="el-icon-edit"
        :underline="false"
        @click="editBackground"
      />
    </h4>
    <div v-if="!isEditing">
      <el-form
        class="display"
        size="mini"
        inline
        label-position="left"
      >
        <el-form-item>
          <div class="pre-line">{{ display.background }}</div>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="isEditing">
      <el-form
        ref="form"
        :model="form"
        size="mini"
        label-position="left"
      >
        <el-form-item>
          <!--          <el-tooltip effect="dark" content="Auto Generate From Employment History" placement="top">-->
          <!--            <el-link type="primary" :underline="false" @click="autoGenerateBackground">Auto Generate</el-link>-->
          <!--          </el-tooltip>-->
          <background-format ref="background-format" :data="advisor" class="display-none" />
          <el-input v-model="form.background" type="textarea" :rows="6" placeholder="Background" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="loading"
            icon="el-icon-check"
            @click="onSave()">Save
          </el-button>
          <el-button type="text" @click="hideEdit">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'
import mixins from './mixins'
import BackgroundFormat from '@/components/FormatProfile/Background'

export default {
  name: 'EditBackground',
  components: {
    BackgroundFormat,
  },
  mixins: [mixins],
  data() {
    return {
      display: {
        background: this.task.advisor_profile.background,
      },
      form: this.initForm(),
    }
  },
  mounted() {
    this.initDisplayData(this.task.advisor_profile)
  },
  methods: {
    initForm() {
      return {
        id: this.task.advisor_profile.id,
        background: this.task.advisor_profile.background,
      }
    },
    initDisplayData(advisor) {
      this.display.background = advisor.background
    },

    async editBackground() {
      await this.goToEdit()
      if (!this.form.background) {
        this.autoGenerateBackground()
      }
    },

    autoGenerateBackground() {
      const background = this.$refs['background-format']
      background.handleFormatProfile()
      this.$nextTick(() => {
        this.form.background = background.$el.innerText.trim()
          .replace(/\s+/g, ' ')
      })
    },
    handleSave(val) {
      return ProjectAPI.updateTaskAdvisorProfile(this.task.id, val)
        .then(data => {
          this.task.advisor_profile.background = data.background
          this.initDisplayData(data)
          this.$emit('save', this.task)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.display .el-form-item {
  // width: 40%;
  margin-bottom: 10px;
}

.el-form-item ::v-deep label {
  font-weight: 400;
}

.el-form-item ::v-deep .el-form-item__label,
.el-form-item ::v-deep .el-form-item__content {
  line-height: 20px;
}

::v-deep .form-item {
  display: inline-block;
  margin-bottom: 5px;

  > .el-form-item__content {
    margin-left: 0;
  }
}
</style>
