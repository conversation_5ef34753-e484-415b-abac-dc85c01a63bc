<template>
  <div>
    <h4 class="task-edit-title">
      Physician Information
    </h4>

    <el-form
      size="mini"
      inline
      label-width="80px"
      label-position="left"
    >
      <el-form-item label="NPI #" class="fs-13">
        <npi-number :advisor="task.advisor" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixins from './mixins'
import NpiNumber from '@/views/consultant/detail/components/NpiNumber'

export default {
  name: 'PhysicianInformation',
  components: { NpiNumber },
  mixins: [mixins],
  data() {
    return {}
  },
}
</script>

<style scoped lang="scss">
.el-form-item ::v-deep label {
  font-weight: 400;
  color: #909399;
  line-height: 20px;
}

.el-form-item ::v-deep .el-form-item__content {
  flex: 1;
  line-height: 20px;
}
</style>
