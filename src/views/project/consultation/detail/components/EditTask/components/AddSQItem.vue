<template>
  <div class="inline-block pull-right">
    <el-button
      type="success"
      size="mini"
      icon="el-icon-plus"
      @click="initDialog">New
    </el-button>
    <el-dialog
      title="Add question"
      append-to-body
      :visible.sync="dialogVisible"
      width="50%">
      <el-form v-loading="loading" label-width="100px" class="mt-normal mb-normal">

        <el-select v-model="form.type" placeholder="Please select" class="w-408px" @change="changeType">
          <el-option
            v-for="item in options.question_types"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <tinymce v-if="form.type" v-model="form.title" :height="100" :toolbar="toolbar" class="mt-normal mb-8" />

        <div v-if="['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(form.type)">
          <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(form.type)">
            <div v-for="(temp, temp_index) in form.options" :key="temp_index" class="mt-normal">
              <el-input v-model="temp.option" placeholder="option" @keydown.native="optionInputKey($event)" />
              <div class="flex mt-8">
                <el-input v-if="form.type === 'RANKING_SET'" v-model="temp.rank" placeholder="Rank" class="mr-8" style="width:80px" />
                <el-input v-model="temp.text" class="flex-1" type="textarea" placeholder="answer" :rows="1" />
              </div>
              <el-link
                :underline="false"
                type="primary"
                class="ml-3"
                icon="el-icon-plus"
                @click="addOption()" />
              <el-link
                v-if="form.options.length > 1"
                :underline="false"
                type="danger"
                class="ml-3"
                icon="el-icon-minus"
                @click="removeOption(temp_index)" />
            </div>
          </div>

          <div v-if="form.type === 'RADIO'">
            <div
              v-for="(temp, index) in form.options"
              :key="index"
              class="option-item"
            >
              <div class="flex">
                <el-radio v-model="form.answer_text" :label="index" class="flex-1">
                  <el-input v-model="temp.text" placeholder="option" @keydown.native="optionInputKey($event)" />
                </el-radio>
                <el-link
                  :underline="false"
                  type="primary"
                  class="ml-3"
                  icon="el-icon-plus"
                  @click="addOption()" />
                <el-link
                  v-if="form.options.length > 1"
                  :underline="false"
                  type="danger"
                  class="ml-3"
                  icon="el-icon-minus"
                  @click="removeOption(index)" />
              </div>
              <div class="option-desc">
                <el-checkbox v-model="temp.description" class="ma-3px">Ask expert to explain answer choices</el-checkbox>
                <template v-if="temp.description">
                  <el-input
                    v-model="temp.desc_prompt"
                    class="ma-3px"
                    @keydown.native="optionInputKey($event)" />
                  <el-input
                    v-model="temp.desc"
                    type="textarea"
                    class="ma-3px"
                  />
                  <el-checkbox v-model="temp.require_desc">Require expert to explain their answer</el-checkbox>
                </template>
              </div>
            </div>
          </div>

          <div v-if="form.type === 'CHECKBOX'">
            <el-checkbox-group v-model="form.answer_text">
              <div
                v-for="(temp, index) in form.options"
                :key="index"
                class="option-item">
                <el-checkbox
                  :label="index"
                >
                  <div class="flex-1">
                    <el-input
                      v-model="temp.text"
                      placeholder="option"
                      @keydown.native="optionInputKey($event)" />
                  </div>
                  <el-link
                    :underline="false"
                    type="primary"
                    class="ml-3"
                    icon="el-icon-plus"
                    @click.prevent="addOption()" />
                  <el-link
                    v-if="form.options.length > 1"
                    :underline="false"
                    type="danger"
                    class="ml-3"
                    icon="el-icon-minus"
                    @click.prevent="removeOption(index)" />
                </el-checkbox>
                <div class="option-desc">
                  <el-checkbox-group v-model="temp.description">
                    <el-checkbox :label="true" class="ma-3px">Ask expert to explain answer choices</el-checkbox>
                  </el-checkbox-group>
                  <template v-if="temp.description">
                    <el-input
                      v-model="temp.desc_prompt"
                      class="ma-3px"
                      @keydown.native="optionInputKey($event)" />
                    <el-input
                      v-model="temp.desc"
                      type="textarea"
                      class="ma-3px"
                    />
                    <el-checkbox-group v-model="temp.require_desc">
                      <el-checkbox v-model="temp.require_desc">Require expert to explain their answer</el-checkbox>
                    </el-checkbox-group>
                  </template>
                </div>
              </div>
            </el-checkbox-group>
          </div>

        </div>
        <el-input
          v-else
          v-model="form.answer_text"
          type="textarea"
          placeholder="answer"
          :rows="2"
          class="flex-1 mt-8" />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancelEdit">Cancel</el-button>
        <el-button type="primary" :disabled="saveDisabled()" @click="actionUpdate">Save</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import InquiryAPI from '@/api/inquiry'
import { mapGetters } from 'vuex'
import _ from 'lodash'

export default {
  name: 'AddSQItem',
  components: { Tinymce },
  props: {
    specialQuestions: Array,
    instanceId: [Number, String],
    instance: Object,
    questions: [Array],
    answered: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      dialogVisible: false,
      form: this.initForm(),
      options: {
        question_types: [
          { label: 'Comment Box', value: 'TEXT' },
          { label: 'Ranking With Explanation', value: 'RANKING_SET' },
          { label: 'Multiple Choice - Single-select', value: 'RADIO' },
          { label: 'Multiple Choice - Multi-select', value: 'CHECKBOX' },
          { label: 'Multiple Explanation', value: 'NO_RANKING_SET' },
        ],
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  methods: {
    initDialog() {
      this.form = this.initForm()
      this.dialogVisible = true
    },
    initForm() {
      return {
        type: 'TEXT',
        title: '',
        answer_text: null,
        answer_desc: null,
        options: this.initOptions(),
        sort_order: !this.specialQuestions?.length ? this.questions.length : this.questions.length - this.specialQuestions?.length,
      }
    },

    changeType() {
      this.form.answer_text = this.form.type === 'CHECKBOX' ? [] : null
      this.form.answer_desc = this.form.type === 'CHECKBOX' ? {} : ''
      this.form.options = this.initOptions()
    },

    initOptions() {
      return [
        { option: '',
          rank: '',
          text: '',
          description: false,
          desc: '',
          desc_prompt: 'Please explain your answer below:',
          require_desc: false },
        { option: '',
          rank: '',
          text: '',
          description: false,
          desc: '',
          desc_prompt: 'Please explain your answer below:',
          require_desc: false },
      ]
    },

    actionUpdate() {
      if (['RANKING_SET', 'NO_RANKING_SET'].includes(this.form.type)) {
        this.form.answer_text = this.form.options
      }

      if (this.answered) {
        const origin_question_ids = this.questions.map(item => item.id)
        this.updateAnswer()
          .then(data => {
            const new_item = data.qa_list_snapshot.filter(item => !origin_question_ids.includes(item.id))[0]
            this.$emit('update', Object.assign(this.form, {
              question_id: new_item?.id,
              id: new_item?.id,
              answer: new_item?.answer,
            }))
            this.$emit('update:instance', data)
            this.cancelEdit()
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        this.$emit('update', this.form)
        this.cancelEdit()
      }
    },

    updateAnswer() {
      this.loading = true
      const params = {
        id: this.instanceId,
      }
      const new_question = {
        type: this.form.type,
        sort_order: this.form.sort_order,
        title: this.form.title,
        options: this.handleOptions(this.form),
        answer: {
          content:
            {
              result: this.form.answer_text,
              desc: this.handleDesc(this.form),
            },
          update_by_id: this.uid,
        },
      }
      params.qa_list_snapshot = _.cloneDeep(this.questions)
      params.qa_list_snapshot.push(new_question)
      if (this.specialQuestions?.length) {
        params.qa_list_snapshot.forEach(item => {
          const index = this.specialQuestions.findIndex(q => q.id === item.id)
          if (index > -1) {
            item.sort_order = this.questions.length - this.specialQuestions.length + index
          }
        })
      }
      return InquiryAPI.updateSQOneOff(params)
    },

    handleOptions(item) {
      switch (item.type) {
        case 'RANKING_SET':
          return item.options.map(temp => {
            return { value: temp.option, text: temp.text, rank: temp.rank }
          })
        case 'NO_RANKING_SET':
          return item.options.map(temp => {
            return { value: temp.option, text: temp.text }
          })
        case 'RADIO':
        case 'CHECKBOX':
          return item.options.map(temp => {
            delete temp.option
            delete temp.rank
            return temp
          })
        default:
          return null
      }
    },

    handleDesc(item) {
      const desc = {}

      switch (item.type) {
        case 'RADIO':
          item.answer_desc = item.options.find(temp => temp.description)?.desc
          break
        case 'CHECKBOX':
          item.options.forEach((temp, index) => {
            if (temp.description && item.answer_text.includes(index)) {
              desc[index] = temp.desc
            }
          })
          item.answer_desc = desc
          break
        default:
          item.answer_desc = null
      }
      return item.answer_desc
    },

    cancelEdit() {
      this.dialogVisible = false
      this.form = {}
    },

    saveDisabled() {
      const not_enough_options = ['CHECKBOX', 'RADIO', 'RANKING_SET', 'NO_RANKING_SET'].includes(this.form.type) && this.form.options.filter(i => i.text).length < 2
      return !this.form.title || this.loading || not_enough_options
    },

    optionInputKey(event) {
      if (event.ctrlKey && event.keyCode === 78) {
        this.addOption()
      }
    },

    addOption() {
      this.form.options.push({
        option: '',
        rank: '',
        text: '',
        description: false,
        desc: '',
        desc_prompt: 'Please explain your answer below:',
        require_desc: false })
    },
    removeOption(index) {
      this.form.options.splice(index, 1)
    },
  },
}
</script>

<style scoped lang="scss">

.option-item {
  display: flex;
  flex-direction: column;
  margin:16px 0;

  .option-desc {
    margin-left: 20px;
    margin-right: 20px;
  }

  ::v-deep .el-link{
    font-size: 16px;
  }

  ::v-deep .el-radio{
    margin-right: 24px;
  }
  ::v-deep .el-checkbox {
    display: flex;
    align-items: center;
    flex: 1;
  }
  ::v-deep .el-checkbox__label {
    flex: 1;
    display: flex;
  }
}
</style>
