<template>
  <div>
    <el-tooltip class="item" :disabled="publish_log.length<1" effect="light" placement="top-start" :open-delay="500">
      <div slot="content">
        <el-table
          border
          stripe
          :data="publish_log"
        >
          <el-table-column label="Publish" width="80">
            <template slot-scope="scope">
              {{ scope.row.publish_sq_str }}
            </template>
          </el-table-column>
          <el-table-column label="User">
            <template slot-scope="scope">
              <span v-if="scope.row.create_by">{{ scope.row.create_by.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Time" width="150">
            <template slot-scope="scope">
              {{ scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-link
        type="primary"
        :underline="false"
        title="Set filter questions to be visible"
        class="el-icon-view"
        @click="dialogShow" />
    </el-tooltip>
    <el-dialog
      :visible.sync="editDialogVisible"
      :show-close="false"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="Set screen questions to be visible on the client portal"
      width="30%"
    >
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="item in sort_questions"
                     :key="item.id"
                     :disabled="selectDisabled(item)"
                     :label="item.id">
          Q{{ item.sort_order + 1 }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateSQPublish()">Save</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import _ from 'lodash'

export default {
  name: 'SQPublish',
  props: {
    instance: Object,
    questions: [Object, Array],
  },
  data() {
    return {
      editDialogVisible: false,
      checkList: [],
      sort_questions: [],
      publish_log: [],
      client_publish_question_ids: null,
      client_publish_status_options: [
        { value: 'INITIAL', label: '---' },
        { value: 'PUBLISHED', label: 'Yes' },
        { value: 'UNPUBLISHED', label: 'No' },
      ],
    }
  },
  watch: {
    'instance': {
      handler(newVal) {
        if (newVal) {
          this.client_publish_question_ids = this.instance.client_publish_question_ids
          this.publish_log = this.formatLogList(this.instance.client_publish_log)
        }
      },
    }, immediate: true,
  },
  mounted() {
    this.client_publish_question_ids = this.instance.client_publish_question_ids
    this.publish_log = this.formatLogList(this.instance.client_publish_log)
  },
  methods: {
    selectDisabled(item) {
      // 该类型问题只用于确认专家信息 不给客户看
      return item.tags?.includes('ADVISOR_JOB_CONFIRM') || item.tags?.includes('ADVISOR_NPI_CONFIRM')
    },
    dialogShow() {
      /* client_publish_question_ids
          null=>初始化全部可见
          [] => 全部不可见
          [1,3,4[ => sq id 1,3,4 可见
      * */
      this.sort_questions = _.sortBy(_.cloneDeep(this.questions), 'sort_order')

      if (this.client_publish_question_ids === null) {
        this.checkList = this.instance.qa_list_snapshot.map(item => item.id)
      } else {
        this.checkList = this.client_publish_question_ids
      }
      this.editDialogVisible = true
    },
    updateSQPublish() {
      const params = {
        id: this.instance.id,
        'client_publish_question_ids': this.checkList,
      }
      return InquiryAPI.updateTaskSQClientPublish(params)
        .then(data => {
          this.client_publish_question_ids = data.client_publish_question_ids
          this.publish_log = this.formatLogList(data.client_publish_log)
          this.$emit('update:instance', data)
        })
        .finally(() => {
          this.editDialogVisible = false
        })
    },
    actionCancel() {
      this.editDialogVisible = false
    },

    formatLogList(list) {
      if (!list || list.length < 1) return []
      return list.map(item => {
        const question_ids = item.json.client_publish_question_ids_str.split(',')

        const sq_list = this.instance.qa_list_snapshot.filter(i => question_ids.includes(i.id.toString()))
        const order_sq_list = _.orderBy(sq_list, 'sort_order')

        item.publish_sq_str = order_sq_list.map(item => {
          return 'Q' + (item.sort_order + 1)
        }).join()

        return item
      })
    },
  },
}
</script>

<style scoped>

</style>
