<template>
  <div class="inline-block">
    <el-link
      type="primary"
      size="mini"
      title="Edit Screening Questions Summary"
      icon="el-icon-chat-dot-square"
      :underline="false"
      @click="getSQSummary()"
    />
    <el-dialog
      :visible.sync="SQSummaryDialogVisible"
      title="Screening Questions Summary"
      width="50%"
      append-to-body
    >
      <div class="dialog-body scrollbar-narrow">
        <el-input
          v-model="sq_summary"
          class="description"
          type="textarea"
          :autosize="{ minRows: 4, maxRows:7 }"
          placeholder="Summary of questionnaires for client portal"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="SQSummaryDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="saveSQSummary()">Save</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import moment from 'moment-timezone'

export default {
  name: 'ScreenQuestionsSummary',
  props: {
    summary: String,
    taskId: [String, Number],
    projectId: [String, Number],
  },

  data() {
    return {
      SQSummaryDialogVisible: false,
      sq_summary: '',
    }
  },
  methods: {
    getSQSummary() {
      this.sq_summary = this.summary || ''
      this.SQSummaryDialogVisible = true
    },
    saveSQSummary() {
      const params = {
        project_id: this.projectId,
        screening_summary: this.sq_summary,
        id: this.taskId,
        screening_summary_update_time: moment.tz(moment(), this.timeZone).toISOString(),
      }
      return ProjectAPI.updateTask(params)
        .then(data => {
          this.$emit('update:summary', data['screening_summary'])
          this.$emit('update')
        })
        .finally(() => {
          this.SQSummaryDialogVisible = false
        })
    },
  },
}
</script>

<style scoped>

</style>
