<template>
  <div class="inline-block ml-2">
    <el-link
      type="primary"
      :underline="false"
      title="Set the question as filterable"
      @click="dialogShow">
      <svg-icon icon-class="question-circle" />
    </el-link>
    <el-dialog
      :visible.sync="editDialogVisible"
      :show-close="false"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="Include questions when screening/rescreening experts"
      width="50%"
    >
      <el-checkbox-group v-model="checkList">
        <div v-for="item in sort_questions" :key="item.id">
          <el-checkbox :label="item.id">
            <div class="flex mb-8"> <div>Q{{ item.sort_order + 1 }}.</div><span class="pl-5 white-space-normal" v-html="item.title" /></div>
          </el-checkbox>
        </div>

      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateSQFilter()">Save</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import _ from 'lodash'

export default {
  name: 'SQFilter',
  props: {
    instance: [Object, Array],
    questions: [Object, Array],
  },
  data() {
    return {
      sort_questions: [],
      editDialogVisible: false,
      checkList: [],
    }
  },
  methods: {
    dialogShow() {
      /* advisor_screening_question_ids
         null=>初始化全部可见
         [] => 全部不可见
         [1,3,4] => sq id 1,3,4 可见
     * */
      this.sort_questions = _.sortBy(_.cloneDeep(this.questions), 'sort_order')
      if (this.instance.advisor_screening_question_ids === null) {
        this.checkList = this.instance.qa_list_snapshot.map(item => item.id)
      } else {
        this.checkList = this.instance.advisor_screening_question_ids
      }

      this.editDialogVisible = true
    },

    updateSQFilter() {
      const params = {
        id: this.instance.id,
        'advisor_screening_question_ids': this.checkList,
      }
      return InquiryAPI.updateAnswer(params)
        .then(data => {
          this.$emit('update:instance', data)
        })
        .finally(() => {
          this.editDialogVisible = false
        })
    },

    actionCancel() {
      this.editDialogVisible = false
    },
  },
}
</script>

<style scoped>
.ml-2 {
  margin-left: 2px;
}

.white-space-normal{
  white-space: normal;
}
</style>
