<template>
  <div class="tag-box">
    <div
      v-if="!tagsEditing"
      class="advisor-tags"
      @click="actionStartEditTags"
    >
      <div class="advisor-tags-inner">
        <div
          v-for="item in task.client_selection_tag_refs"
          :key="item.id"
          class="advisor-tag">
          <el-tag
            v-if="item.tag"
            type="warning"
            effect="plain">
            {{ item.tag.name }}
          </el-tag>
        </div>
        <div
          v-if="!task.client_selection_tag_refs.length"
          class="advisor-tag-placeholder">
          Click To Select Tags
        </div>
      </div>
    </div>
    <div
      v-show="tagsEditing"
      v-loading="loading"
    >
      <el-select
        ref="TagsSelector"
        v-model="tags_id"
        multiple
        filterable
        allow-create
        automatic-dropdown
        placeholder="Tags"
        class="w-100"
        @visible-change="handleVisibleChange"
      >
        <el-option-group label="Select an option or create one">
          <el-option
            v-for="(item,index) in options.tags"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <div class="flex align-items-center justify-content-between h-100">
              <div class="option-tag">{{ item.name }}</div>
              <div>
                <el-link
                  v-if="!item.deleting"
                  type="danger"
                  :underline="false"
                  icon="el-icon-delete"
                  @click.stop="item.deleting=true" />
                <div v-else>
                  <el-link
                    type="danger"
                    :underline="false"
                    class="mr-8"
                    @click.stop="deleteTag(item,index)">Delete</el-link>
                  <el-link
                    type="primary"
                    :underline="false"
                    @click.stop="item.deleting=false">Cancel</el-link>
                </div>
              </div>

            </div></el-option>
        </el-option-group>
      </el-select>
    </div>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'TagsForClientPortal',
  props: {
    task: Object,
  },
  data() {
    return {
      tagsEditing: false,
      loading: false,
      tagName: '',
      isDeleting: false,
      options: {
        tags: [
          { name: 'create', id: 0 }, // 暂时解决<el-option-group options默认值为[],第一次focus不展示列表
        ],
      },
      tags_id: [],
    }
  },
  computed: {
    disabled() {
      return this.options.tags.some(item => item.popover_visible)
    },
  },

  methods: {
    actionStartEditTags() {
      this.loading = true
      this.tags_id = this.task.client_selection_tag_refs.map(item => item.tag_id)

      return ProjectAPI.getProjectAvailableTags({
        project_id: this.task.project_id,
        client_id: this.task.client_id,
        scope: 'PROJECT',
      }).then((data) => {
        this.options.tags = data.map(item => {
          item.deleting = false
          return item
        })

        this.$nextTick(() => {
          this.$refs.TagsSelector.$el.click()
          this.tagsEditing = true
        })
      })
        .finally(() => {
          this.loading = false
        })
    },
    async deleteTag(item, index) {
      this.loading = true
      await ProjectAPI.deleteClientSelectionTag(item.id)

      // 若删除的tag已经在专家中，也要删除对应tag
      const tag_index = this.tags_id.findIndex(id => id === item.id)
      if (tag_index > -1) {
        this.tags_id.splice(tag_index, 1)
      }
      await this.updateTags()

      this.options.tags.splice(index, 1)

      this.loading = false
    },
    handleVisibleChange(value) {
      if (!value) {
        this.updateTags()
        this.tagsEditing = false
      }
    },

    async updateTags() {
      const createRequest = []
      this.tags_id.forEach((item, index) => {
        if (typeof item === 'string') {
          createRequest.push(this.createTag(item, index))
        }
      })
      await Promise.all(createRequest)

      const params = {
        id: this.task.id,
        client_selection_tag_refs: this.tags_id.map(item => ({
          task_id: this.task.id,
          tag_id: item,
        })),
      }
      const data = await ProjectAPI.selectTagForTask(this.task.id, params)

      const originTagsId = this.task.client_selection_tag_refs.map(item => item.tag_id)
      if (originTagsId.join() !== this.tags_id.join()) {
        await ProjectAPI.saveUpdateLog({
          entity_table_name: 'task',
          entity_id: this.task.id,
          topic: 'USER_EDIT_TASK_CLIENT_SELECTION_TAGS',
          create_by_id: this.$store.state.user.uid,
          raw_json: {
            'tags': this.options.tags.filter(item => originTagsId.includes(item.id))
              .map(item => item.name)
              .join(', '),
          },
          json: {
            'tags': this.options.tags.filter(item => this.tags_id.includes(item.id))
              .map(item => item.name)
              .join(', '),
          },
        })
      }
      this.task.client_selection_tag_refs = data.client_selection_tag_refs
    },
    createTag(name, index) {
      return ProjectAPI.createClientSelectionTag({
        project_id: this.task.project_id,
        client_id: this.task.client_id,
        scope: 'PROJECT',
        name,
      }).then(newTag => {
        this.options.tags.push(newTag)
        this.tags_id[index] = newTag.id
      })
    },
  },
}
</script>

<style scoped lang="scss">
.tag-box{
  margin-bottom:6px;
}

.advisor-tags {
  padding:4px;
  user-select: none;
  transition: background 20ms ease-in 0s;
  cursor: pointer;
  align-items: center;
  border-radius: 3px;
  width: 100%;
  min-height: 28px;
  font-size: 13px;
  overflow: hidden;

  &:hover {
    background: rgba(55, 53, 47, 0.08);
  }

  .advisor-tags-inner {
    display: flex;
    flex-wrap: wrap;

    .advisor-tag-placeholder {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
    }

    .advisor-tag {
      margin-right:6px
    }
  }
}

::v-deep .el-select-group__title {
  padding-right: 20px;
}

.option-tag {
  border-radius: 3px;
  padding: 0 6px;
  height: 24px;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>
