<template>
  <el-dropdown
    placement="bottom-start"
    trigger="click"
    @command="val => changeTaskAdvisorStatus(val)">
    <div class="advisorStatus dropdown-link el-tag--plain" :class="advisor.status">
      <slot>
        <div v-if="!advisor.status" class="fs-14">{{type === 'LEAD' ? 'Lead' : 'Advisor'}}</div>
        <div v-if="advisor.status" class="fs-14">{{ advisor.status | getLabel(advisor_options.status) }}</div>
      </slot>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="option in advisor_options.status"
        :key="option.value"
        class="fs-14"
        :command="option.value">
        {{ option.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import ConsultantAPI from '@/api/consultant'
import { mapGetters } from 'vuex'

export default {
  name: 'AdvisorStatus',
  props: {
    advisor: Object,
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      options: {
        advisor_status_options: [
          {
            value: null,
            label: 'Normal',
          },
          {
            value: 'NOT_INTERESTED',
            label: 'Not Interested',
          },
          {
            value: 'DO_NOT_CONTACT',
            label: 'Do Not Contact',
          },
        ],
      },
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
  },
  methods: {
    changeTaskAdvisorStatus(val) {
      const params = {
        id: this.advisor.id,
        status: val,
      }
      return ConsultantAPI.updateConsultant(params)
        .then(data => {
          this.$emit('changeTaskAdvisorStatus', data)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.advisorStatus {
  border: 1px solid #d9ecff;
  background-color: #ecf5ff;
  border-radius: 50%/8px;
  padding: 10px 12px;
  display: inline-block;
  min-width: 70px;
  text-align: center;

  &:hover {
    filter: brightness(101%);
  }

  &.NOT_INTERESTED {
    background-color: #fef0f0;
    border-color: #fde2e2;
    color: #f56c6c;
  }

  &.DO_NOT_CONTACT {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
  }

}
</style>
