<template>
  <div class="inline-block">
    <el-link
      type="success"
      size="mini"
      title="Edit Screening Questions Summary"
      :underline="false"
      @click="showDialog()"
    >
      <svg-icon icon-class="comment-add" />
    </el-link>
    <el-dialog
      :visible.sync="attachSummaryDialogVisible"
      title="Expert Summary List"
      width="60%"
      append-to-body
    >
      <div class="dialog-body scrollbar-narrow">
        <el-table
          v-loading="loading"
          stripe
          class="headerFixed"
          :data="list">
          <el-table-column
            label="Date Added"
            width="150"
            sortable="custom"
            prop="screening_summary_update_time"
          >
            <template slot-scope="scope">
              {{ scope.row.screening_summary_update_time | momentFormat('MM/DD/YYYY (HH:mm)') }}
            </template>
          </el-table-column>
          <el-table-column
            label="Project"
            width="150"
          >
            <template slot-scope="scope">
              <router-link-project v-if="scope.row.project_id" :data="scope.row.project" />
              <span v-else>N/A</span>
            </template>
          </el-table-column>
          <el-table-column
            label="Summary"
          >
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.screening_summary" width="500" placement="top" :open-delay="300">
                <div class="overflow-three">{{ scope.row.screening_summary }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="Actions"
            width="140"
          >
            <template slot-scope="scope">
              <el-button type="success" class="el-button--xs" plain @click="attachSummary(scope.row.screening_summary)">Attach Summary</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          :page.sync="page"
          :limit.sync="size"
          @pagination="getAdvisorSummaryList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment-timezone'
import ProjectAPI from '@/api/project'
import RouterLinkProject from '@/components/RouterLink/Project'
import Pagination from '@/components/Pagination'
import { mapState } from 'vuex'

export default {
  name: 'AttachSQSummary',
  components: { RouterLinkProject, Pagination },
  props: {
    task: Object,
  },
  data() {
    return {
      loading: false,
      attachSummaryDialogVisible: false,
      list: [],
      page: 1,
      size: 20,
      total: 0,
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },
  methods: {
    showDialog() {
      this.getAdvisorSummaryList()
      this.attachSummaryDialogVisible = true
    },
    getAdvisorSummaryList() {
      this.loading = true
      const params = {
        page: this.page,
        size: this.size,
        advisor_id: this.task.advisor_id,
        screening_summary_is_blank: false,
        extra: 'project',
        sort: 'screening_summary_update_time desc',
      }
      return ProjectAPI.getConsultationList(params)
        .then(data => {
          this.total = data.count
          this.list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    attachSummary(summary) {
      this.loading = true
      const params = {
        project_id: this.task.project_id,
        screening_summary: summary,
        id: this.task.id,
        screening_summary_update_time: moment.tz(moment(), this.timeZone).toISOString(),
      }
      return ProjectAPI.updateTask(params)
        .then(data => {
          this.$message.success('success')
          this.$emit('update:summary', data['screening_summary'])
          this.$emit('update:task', Object.assign(this.task, { screening_summary: data.screening_summary }))
        })
        .finally(() => {
          this.loading = false
          this.attachSummaryDialogVisible = false
        })
    },
  },
}
</script>

<style scoped>

</style>
