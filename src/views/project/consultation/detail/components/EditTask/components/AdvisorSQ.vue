<template>
  <div>
    <el-link
      type="primary"
      size="mini"
      title="Select the answered questionnaire"
      icon="el-icon-document"
      :underline="false"
      @click="getAdvisorAllSQ()"
    />
    <el-dialog
      :visible.sync="SQDialogVisible"
      :close-on-click-modal="false"
      title="Screening Questions / Responses for Previous"
      width="50%"
      append-to-body
    >
      <div class="dialog-body scrollbar-narrow">
        <div v-if="questionnaire_list.length<1" class="no-data-box">This expert has not submitted Screening Questions.
        </div>
        <div v-for="(item, index) in questionnaire_list" :key="index" class="SQ-item">
          <div class="flex justify-content-between">
            <div v-if="item.task" class="link pa-5px" @click="linkToTask(item)">{{linkToTaskText(item)}}</div>
            <el-popconfirm
              icon="el-icon-info"
              icon-color="#13ce66"
              title="Are you sure you want to add this questionnaire to the current task?"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="success"
              class="action-btn"
              @confirm="addToTask(item)"
            >
              <div slot="reference">
                <el-button slot="reference" class="el-button--xs" type="primary" size="mini">Add To Task</el-button>
              </div>
            </el-popconfirm>
          </div>
          <show-answered-questions where="task_detail_dialog" :list="item.qa_list_snapshot" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import moment from 'moment-timezone'
import { mapState } from 'vuex'
import ProjectAPI from '@/api/project'
import Filters from '@/utils/filters'
import ShowAnsweredQuestions from '@/components/Question/ShowAnsweredQuestions'

export default {
  name: 'AdvisorSQ',
  components: { ShowAnsweredQuestions },
  props: {
    advisorId: Number,
    taskId: Number,
    projectId: Number,
  },
  data() {
    return {
      SQDialogVisible: false,
      questionnaire_list: [],
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
  },
  methods: {
    getAdvisorAllSQ() {
      const params = {
        inquiry_type: 'SCREENING',
        status_ne: 'ASSIGNED',
        receiver_id: this.advisorId,
        extra: 'task.project,task.angle',
        page: 1,
        // 数据增多 加分页
        size: 100,
      }
      return InquiryAPI.getInstances(params).then((data) => {
        this.questionnaire_list = data.list.filter(item => item.qa_list_snapshot.length > 0)
        this.SQDialogVisible = true
      })
    },
    addToTask(item) {
      item.source_id = this.taskId
      item.submit_time = moment.tz(this.timeZone).utc().format()

      delete item.id
      delete item.create_at
      delete item.update_at

      return InquiryAPI.copyInstance(item).then((data) => {
        this.updateTaskSqStatus(data.id)
      }).finally(() => {
        this.SQDialogVisible = false
      })
    },

    updateTaskSqStatus(instance_id) {
      const params = {
        project_id: this.projectId,
        id: this.taskId,
        sq_status: 'RESPONDED',
      }
      return ProjectAPI.updateTask(params).then((data) => {
        this.$emit('update', instance_id)
        this.$message({
          message: 'success',
          type: 'success',
        })
      })
    },

    linkToTaskText(item) {
      if (!item.task?.project || !item?.task) return ''
      return `${item.task?.project?.name}-${item.task?.angle?.name || ''}-${Filters.momentFormat(item.submit_time, 'MM/DD/YYYY')}`
    },

    linkToTask(item) {
      if (!item.task?.project_id) return
      let target_router_name = ''

      if (item.task.project.sub_type === 'Investor Call') {
        target_router_name = 'ProjectClientTasksSST'
      } else if (item.task.client_id === this.McKinseyId) {
        target_router_name = 'ProjectClientTasksMcK'
      } else {
        target_router_name = 'ProjectClientTasks'
      }

      if (!item.task.angle_id) {
        target_router_name = 'ProjectLeads'
      }

      this.$router.push({
        name: target_router_name,
        params: {
          project_id: item.task.project_id,
        },
        query: {
          task_id: item.task?.id || undefined,
          angle_id: item.task.angle_id || undefined,
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.SQ-item {
  position: relative;
  margin: 10px;
  border: 1px solid #5a5e66;
  border-radius: 4px;

  .action-btn {
   margin-left: auto;
  }
}

.q-list {
  padding: 10px;
}

.dialog-body {
  max-height: 500px;
  overflow: auto;
}

.no-data-box {
  height: 200px;
  line-height: 200px;
  color: #61676d;
  text-align: center;
}

.pa-5px{
  padding:5px
}

</style>
