<template>
  <div class="fs-14" :class="{'flex align-items-center': !task.angle_id}">
    <div :class="{'mb-8': task.angle_id, 'mr-8': !task.angle_id}">
      <advisor-status
        :type="type"
        :advisor="task.advisor"
        @changeTaskAdvisorStatus="changeTaskAdvisorStatus" />
      <el-dropdown
        placement="bottom-start"
        trigger="click"
        @command="val => changeAdvisorPrefix(val)">
        <div>
          <slot>
            <el-tag effect="plain">{{ advisor.name_prefix || 'Prefix' }}</el-tag>
          </slot>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            class="fs-14"
            :command="null">
            --
          </el-dropdown-item>
          <el-dropdown-item
            v-for="item in advisor_options.name_prefix"
            :key="item"
            class="fs-14"
            :command="item">
            {{ item }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-tag v-if="type !== 'LEAD'" effect="plain">
        {{ `TC: ${advisor.tc_status.slice(0, 1).toUpperCase()}${advisor.tc_status.slice(1).toLowerCase()}` }}
      </el-tag>
      <el-tag v-if="type !== 'LEAD'" effect="plain">
        {{ `Rate: ${advisor.rate} ${advisor.rate_currency}` }}
      </el-tag>
      <el-tag v-if="type === 'LEAD'" effect="plain">
        <el-popover
          v-model="advisorForm.visible"
          append-to-body
          placement="top"
          width="200">
          <el-form
            ref="form"
            :model="advisorForm"
            inline
            size="mini">
            <el-form-item
              style="margin-right: 0;margin-bottom: 17px;"
              class="w-75px"
              prop="default_rate">
              <el-input
                v-model.number="advisorForm.default_rate"
                type="number"
                :step="150"
                :min="0" />
            </el-form-item>
            <el-form-item
              class="w-75px"
              prop="currency">
              <el-select v-model="advisorForm.rate_currency">
                <el-option
                  v-for="item in advisor_options.currency"
                  :key="item"
                  :label="item"
                  :value="item" />
              </el-select>
            </el-form-item>

            <el-form-item
              style="margin-bottom: 0px;width: 100%;text-align: center;">
              <el-button
                :loading="submitting"
                type="primary"
                @click="changeLeadRate()"
              >Save
              </el-button>
              <el-button
                type="text"
                @click="advisorForm.visible = false">Cancel
              </el-button>
            </el-form-item>
          </el-form>

          <span
            slot="reference"
            class="text-truncate cursor-pointer fs-14"
          >{{ `Rate: ${advisor.default_rate || '---'} ${advisor.rate_currency || '---'}` }}</span>
        </el-popover>
      </el-tag>
      <el-tooltip content="Edit Advisor Schedules" placement="bottom" effect="dark">
        <el-link
          class="fs-24"
          style="margin-top: -4px;"
          type="success"
          :underline="false"
          icon="el-icon-date"
          @click="updateSchedule"
        />
      </el-tooltip>
    </div>

    <div
      v-if="projectInfo.client && projectInfo.client.compliance_preference && projectInfo.client.compliance_preference.require_chaperone"
      class="flex align-items-center mb-8"
    >
      <b class="mr-8">Chaperones</b>
      <router-link
        v-if="task.task_client_chaperones.length"
        :to="{ name: 'ClientComplianceRules', params: { client_id: task.client_id }, query: {view: 'Approval'} }"
        target="_blank"
        class="text-truncate link"
      >
        {{ task.task_client_chaperones.map(item => item.chaperone?.name).join(', ') }}
      </router-link>
      <span v-else class="info">-- Require Chaperones --</span>
    </div>
    <div v-if="advisor.npi" class="flex align-items-center mb-8">
      <b class="mr-8">NPI #</b>{{ advisor.npi }}
    </div>
    <div
v-if="advisor.spoken_language_list && advisor.spoken_language_list.length"
         class="flex align-items-center mb-8">
      <b class="mr-8">Spoken Language</b>{{ advisor.spoken_language_list.join(', ') }}
    </div>

    <div class="flex align-items-center">
      <slot
        v-if="!['ARRANGED', 'COMPLETED'].includes(task.general_status) && ['INITIAL', 'IN_PROGRESS'].includes(advisorForm.scheduling_status)">
        <b class="mr-8">Scheduling In Process</b>
        <el-tooltip
          effect="dark"
          placement="top"
          :disabled="task.general_status === 'SELECTED'"
          content="Available when task has been selected">
          <el-switch
            v-model="advisorForm.scheduling_status"
            :disabled="task.general_status !== 'SELECTED'"
            active-color="#13ce66"
            active-value="IN_PROGRESS"
            inactive-value="INITIAL"
            @change="updateSchedulingStatus" />
        </el-tooltip>
      </slot>
      <slot
        v-if="['ARRANGED', 'COMPLETED'].includes(task.general_status) || ['SCHEDULED'].includes(advisorForm.scheduling_status)">
        <b class="mr-8">Scheduled</b>
        <div v-if="task.schedule && task.schedule.creator_type === 'PM'">
          {{ task.schedule.start_time| momentFormatZone('ddd MM/DD: h:mma', task.schedule.zone_id_string || timeZone) }}
          -
          {{ task.schedule.end_time| momentFormatZone('h:mma', task.schedule.zone_id_string || timeZone) }}
          <slot v-if="task.loopup_room">
            <a class="text-truncate link ml-8" :href="'//' + task.loopup_room.url" target="_blank">Loopup
              #{{ task.loopup_room.id }}</a>
          </slot>
        </div>
      </slot>
    </div>

    <el-dialog
      title="Edit Advisor Schedules"
      append-to-body
      :visible.sync="updateScheduleDialog"
      width="90%">
      <div>
        <div>
          <time-zone-select
v-model="selectTimeZone"
                            class="mb-8 inline-block"
                            @change="formatSchedule()" />
          <el-dropdown
            trigger="click"
            @command="selectTimeZone = $event; formatSchedule();">
            <el-tooltip content="Quick Select Time Zone" placement="right">
              <el-button type="primary" plain circle icon="el-icon-caret-bottom" class="ml-8" @click.stop />
            </el-tooltip>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :disabled="!advisor.zone_id_string"
                :command="advisor.zone_id_string">
                Advisor’s Time Zone
                <i v-if="advisor.zone_id_string">-- {{ advisor.zone_id_string }}</i>
                <i v-else>(Not available, please update the advisor record)</i>
              </el-dropdown-item>
              <el-dropdown-item :command="timeZone">My Time Zone <i>-- {{ timeZone }}</i></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <calendar
          v-model="advisor_schedules"
          title=""
          :select-time-zone="selectTimeZone"
          :advisor-schedules="false" />
      </div>
      <span slot="footer">
        <el-button type="primary" @click="saveSchedule">Save</el-button>
        <el-button type="text" @click="updateScheduleDialog = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import ProjectAPI from '@/api/project'
import { mapGetters, mapState } from 'vuex'
import mixins from './mixins'
import moment from 'moment-timezone'
import Calendar from '@/components/Calendar'
import AdvisorStatus from '@/views/project/consultation/detail/components/EditTask/components/AdvisorStatus'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'

export default {
  name: 'AdvisorTask',
  components: {
    AdvisorStatus,
    Calendar,
    TimeZoneSelect,
  },
  mixins: [mixins],
  data() {
    return {
      submitting: false,
      prefix_submitting: false,
      updateScheduleDialog: false,
      advisor: this.task.advisor,
      selectTimeZone: undefined,
      advisor_schedules: [],
      typeColor: {
        advisor: '#67C23A',
        client: '#E6A23C',
        arranged: '#8CC5FF',
        pm: '#3788D8',
        outsource: '#fdb4b4',
      },
      advisorForm: {
        visible: false,
        default_rate: this.task.advisor.default_rate || '---',
        rate_currency: this.task.advisor.rate_currency || 'USD',
        scheduling_status: this.task.scheduling_status || false,
      },
      newData: [],
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
      projectInfo: state => state.project.data,
    }),
  },
  mounted() {
    // this.initSchedule()
  },
  methods: {
    formatSchedule() {
      this.advisor_schedules.forEach(item => {
        item.start = moment(item.start).tz(this.selectTimeZone).format()
        item.end = moment(item.end).tz(this.selectTimeZone).format()
      })
    },
    updateSchedule() {
      this.selectTimeZone = this.projectInfo.zone_id_string || this.timeZone
      this.showUpdateScheduleDialog()
    },
    showUpdateScheduleDialog() {
      this.initSchedule()
      this.updateScheduleDialog = true
    },
    hideUpdateScheduleDialog() {
      this.updateScheduleDialog = false
    },
    initSchedule() {
      this.advisor_schedules = this.task.advisor_schedules
        .filter(({ creator_type }) => creator_type === 'ADVISOR')
        .map(item => {
          return {
            id: item.id,
            start: moment(item.start_time).tz(this.selectTimeZone).format(),
            end: moment(item.end_time).tz(this.selectTimeZone).format(),
            editable: true,
            title: 'Advisor',
            creator_type: 'PM',
            backgroundColor: this.typeColor.pm,
            borderColor: this.typeColor.pm,
          }
        })
    },
    saveSchedule() {
      const params = {
        ranges: this.advisor_schedules
          .map(item => {
            return {
              id: item.id,
              start_time: moment.tz(item.start, this.timeZone).toISOString(),
              end_time: moment.tz(item.end, this.timeZone).toISOString(),
            }
          }),
      }
      return ProjectAPI.updateTaskAdvisorSchedules(this.task.id, params)
        .then(data => {
          this.$set(this.task, 'advisor_schedules', data.advisor_schedules)
          this.$emit('update:task', this.task)
          this.hideUpdateScheduleDialog()
        })
    },
    changeTaskAdvisorStatus(data) {
      this.task.advisor.status = data.status
      this.$emit('save', this.task)
    },
    changeLeadRate() {
      this.submitting = true
      const params = {
        id: this.advisor.id,
        default_rate: this.advisorForm.default_rate,
        rate_currency: this.advisorForm.rate_currency,
      }
      return ConsultantAPI.updateConsultant(params)
        .then(data => {
          this.task.advisor.default_rate = data.default_rate
          this.task.advisor.rate_currency = data.rate_currency
          this.advisorForm.visible = false
          this.$emit('save', this.task)
        })
        .finally(() => {
          this.submitting = false
        })
    },
    changeAdvisorPrefix(val) {
      const params = {
        id: this.advisor.id,
        name_prefix: val,
      }
      return ConsultantAPI.updateConsultant(params)
        .then(data => {
          this.advisor.name_prefix = data.name_prefix
          this.task.advisor.name_prefix = data.name_prefix
          this.$emit('update')
        })
    },

    updateSchedulingStatus(val) {
      const params = {
        scheduling_status: val,
      }
      return ProjectAPI.updateTaskScheduling(this.task.id, params)
        .then(() => {
          this.$set(this.task, 'scheduling_status', val)
          this.$emit('update:task', this.task)
        })
    },
  },
}
</script>

<style scoped>
.el-tag {
  font-size: 14px;
  line-height: 16px;
  border: 2px solid #6bb8ff;
}

.w-75px {
  width: 75px;
}
</style>
