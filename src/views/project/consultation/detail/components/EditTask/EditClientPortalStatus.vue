<template>
  <div>
    <h4 class="task-edit-title">
      Client Portal Status
      <el-link
        v-if="!isEditing"
        class="ml-3"
        type="primary"
        size="mini"
        icon="el-icon-edit"
        :underline="false"
        @click="editPortalInfo"
      />
      <el-tooltip
        effect="light"
        placement="right"
        class="ml-3">
        <div slot="content" class="scrollbar-narrow" style="overflow-y: auto; max-height: 80vh;">
          <div class="mb-8"><b>&lt; Edit Log &gt;</b></div>
          <slot v-for="(item, index) in task.entity_update_logs">
            <hr v-if="index" :key="item.id + 'hr'">
            <div
              :key="item.id"
              class="flex justify-content-between">
              <div class="flex">
                <slot v-if="item.topic === 'USER_EDIT_TASK_CLIENT_SELECTION_TAGS'">
                  <div style="width: 70px;"><b>Tags:</b></div>
                  <div>{{ item.json.tags }}</div>
                </slot>
                <div v-if="['CLIENT_EDIT_CONTACT_SCHEDULE', 'USER_EDIT_CONTACT_SCHEDULE'].includes(item.topic)">
                  <div class="flex">
                    <div style="width: 70px;"><b>Duration:</b></div>
                    <div v-if="item.json.contact_schedules.length">
                      {{ item.json.contact_schedules[0].expected_duration_in_minutes }}
                    </div>
                  </div>
                  <div class="flex">
                    <div style="width: 70px;"><b>Time Zone:</b></div>
                    <div v-if="item.json.contact_schedules.length">{{ item.json.contact_schedules[0].zone_id_string }}
                    </div>
                  </div>
                  <div class="flex">
                    <div style="width: 70px;"><b>Time Slot:</b></div>
                    <div>
                      <div v-for="schedule in item.json.contact_schedules" :key="schedule.id">
                        {{ schedule.start_time | momentFormat('MM/DD/YYYY hh:mm A') }} -
                        {{ schedule.end_time | momentFormat('MM/DD/YYYY hh:mm A') }}
                      </div>
                    </div>
                  </div>
                  <div class="flex">
                    <div style="width: 70px;"><b>Note:</b></div>
                    <div v-if="item.json.contact_schedules.length">{{ item.json.contact_schedules[0].comment }}</div>
                  </div>
                  <div class="flex">
                    <div style="width: 70px;"><b>Email:</b></div>
                    <div v-if="item.json.contact_schedules.length">{{ item.json.contact_schedules[0].email }}</div>
                  </div>
                </div>
              </div>
              <div class="ml-8">
                ( By
                <slot v-if="item.topic === 'USER_EDIT_CONTACT_SCHEDULE'">
                  {{ item.create_by.name }},
                </slot>
                <slot v-if="item.topic === 'CLIENT_EDIT_CONTACT_SCHEDULE'">
                  Client User,
                </slot>
                {{ item.create_at | momentFormat('MM/DD/YYYY hh:mm A') }})
              </div>
            </div>
          </slot>
        </div>
        <el-link
          type="primary"
          size="mini"
          icon="el-icon-document"
          :underline="false"
        />
      </el-tooltip>
    </h4>
    <div v-if="!isEditing">
      <el-form
        class="display"
        size="mini"
        inline
        label-width="85px"
        label-position="left"
      >
        <el-form-item label="Duration" class="fs-13">
          {{ form.expected_duration_in_minutes }}
          <slot v-if="form.expected_duration_in_minutes">minutes</slot>
        </el-form-item>
        <el-form-item label="Time Zone" class="fs-13">
          {{ form.zone_id_string }}
        </el-form-item>
        <el-form-item label="Time Slot" class="fs-13">
          <div v-for="item in form.time_slot_selected" :key="item.id">
            {{ item.start_time | momentFormat('dddd M/D: hh:mm A') }}
            - {{ item.end_time | momentFormat('hh:mm A') }}
          </div>
        </el-form-item>
        <el-form-item label="Note" class="fs-13">
          {{ form.comment }}
        </el-form-item>
        <el-form-item label="Email" class="fs-13">
          {{ form.email }}
        </el-form-item>
        <!--        <el-form-item label="Tags" class="fs-13">-->
        <!--          <el-tag v-for="item in form.tags_id" :key="item" class="client-portal-tag">-->
        <!--            {{ item | getName(options.tags) }}-->
        <!--          </el-tag>-->
        <!--        </el-form-item>-->
      </el-form>
    </div>
    <div v-if="isEditing">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="mini"
        label-width="85px"
        label-position="left"
      >
        <el-form-item label="Duration" class="fs-13">
          <el-select v-model="form.expected_duration_in_minutes">
            <el-option :value="30" label="30 minutes" />
            <el-option :value="45" label="45 minutes" />
            <el-option :value="60" label="60 minutes" />
          </el-select>
        </el-form-item>
        <el-form-item label="Time Zone" prop="zone_id_string" class="fs-13">
          <el-select
            v-model="form.zone_id_string"
            clearable
            filterable>
            <el-option v-for="item in timezone_list" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Time Slot" prop="time_slot_selected" class="fs-13">
          <div
            v-for="(item, index) in form.time_slot_selected"
            :key="item.id"
            :style="{ 'margin-top': index ? '5px' : 0 }"
          >
            <el-date-picker
              v-model="item.start_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              placeholder="choose time" />
            <el-date-picker
              v-model="item.end_time"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              placeholder="choose time" />
            <el-link
              type="success"
              :underline="false"
              icon="el-icon-plus"
              class="ml-3"
              @click="addTimeSlot()" />
            <el-link
              v-if="form.time_slot_selected.length > 1"
              type="danger"
              :underline="false"
              icon="el-icon-delete"
              class="ml-3"
              @click="removeTimeSlot(index)" />
          </div>
        </el-form-item>
        <el-form-item label="Note" class="fs-13">
          <el-input v-model="form.comment" type="textarea" class="w-408px" />
        </el-form-item>
        <el-form-item label="Email" prop="email" class="fs-13">
          <el-input v-model="form.email" type="text" class="w-200px" />
        </el-form-item>
        <!--        <el-form-item label="Tags" class="fs-13">-->
        <!--          <el-select-->
        <!--            v-model="form.tags_id"-->
        <!--            multiple-->
        <!--            filterable-->
        <!--            allow-create-->
        <!--            default-first-option-->
        <!--            placeholder="Select or Create Tag">-->
        <!--            <el-option-->
        <!--              v-for="item in options.tags"-->
        <!--              :key="item.id"-->
        <!--              :label="item.name"-->
        <!--              :value="item.id" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-button
            type="primary"
            :disabled="loading"
            icon="el-icon-check"
            @click="onSave()">Save
          </el-button>
          <el-button type="text" @click="handleCancel">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'
import mixins from './mixins'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment-timezone'

export default {
  name: 'EditClientPortalStatus',
  mixins: [mixins],
  data() {
    return {
      form: this.initForm(),
      rules: {
        zone_id_string: [{ required: true, message: 'Time Zone is required', trigger: 'change' }],
        email: [{ required: true, message: 'Location is required', trigger: 'change' }],
        time_slot_selected: [
          {
            validator: (rule, value, callback) => {
              if (value.some(item => !item.start_time || !item.end_time)) {
                callback(new Error('Time Slot is required'))
              } else {
                callback()
              }
            },
          },
        ],
      },
      options: {
        tags: [],
      },
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.info,
    }),
    ...mapGetters([
      'timezone_list',
    ]),
  },
  watch: {
    'task': {
      handler(newVal) {
        if (newVal) { this.init() }
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.form = this.initForm()
      // this.options.tags = this.task.client_selection_tag_refs.map(item => item.tag).filter(item => item)
    },
    initForm() {
      return {
        id: this.task.advisor.id,
        expected_duration_in_minutes: this.task.contact_schedules?.[0]?.expected_duration_in_minutes,
        time_slot_selected: JSON.parse(JSON.stringify(this.task.contact_schedules)),
        comment: this.task.contact_schedules?.[0]?.comment,
        zone_id_string: this.task.contact_schedules?.[0]?.zone_id_string,
        email: this.task.contact_schedules?.[0]?.email,
        // tags_id: this.task.client_selection_tag_refs.map(item => item.tag_id),
      }
    },
    async editPortalInfo() {
      await this.goToEdit()
      if (!this.form.time_slot_selected.length) {
        this.form.time_slot_selected = [{ start_time: '', end_time: '', id: 0 }]
        this.form.email = this.userInfo.email
      }
      // this.options.tags = await ProjectAPI.getProjectAvailableTags({
      //   project_id: this.task.project_id,
      //   client_id: this.task.client_id,
      //   scope: 'PROJECT',
      // })
    },
    addTimeSlot() {
      this.form.time_slot_selected.push({
        start_time: '',
        end_time: '',
        id: 0,
      })
    },
    removeTimeSlot(index) {
      this.form.time_slot_selected.splice(index, 1)
    },
    handleCancel() {
      this.isEditing = false
      this.form = this.initForm()
    },
    handleSave(val) {
      return Promise.all([this.updateTaskSchedule(val)])
        .then(() => {
          this.$emit('save', this.task)
        })
    },
    updateTaskSchedule(val) {
      const params = {
        id: this.task.id,
        contact_schedules: val.time_slot_selected.map(item => ({
          id: item.id,
          expected_duration_in_minutes: val.expected_duration_in_minutes,
          start_time: moment(item.start_time).toISOString(),
          end_time: moment(item.end_time).toISOString(),
          comment: val.comment || undefined,
          zone_id_string: val.zone_id_string,
          email: val.email,
          pm_id: this.$store.state.user.uid,
          creator_type: 'PM',
        })),
      }

      return ProjectAPI.updateAngleTaskSchedule(this.task.id, params)
        .then(data => {
          this.task.contact_schedules = data
        })
    },
    // async updateTags() {
    //   const createRequest = []
    //   this.form.tags_id.forEach((item, index) => {
    //     if (typeof item === 'string') {
    //       createRequest.push(this.createTag(item, index))
    //     }
    //   })
    //   await Promise.all(createRequest)
    //
    //   const params = {
    //     id: this.task.id,
    //     client_selection_tag_refs: this.form.tags_id.map(item => ({
    //       task_id: this.task.id,
    //       tag_id: item,
    //     })),
    //   }
    //   const data = await ProjectAPI.selectTagForTask(this.task.id, params)
    //
    //   const originTagsId = this.task.client_selection_tag_refs.map(item => item.tag_id)
    //   if (originTagsId.join() !== this.form.tags_id.join()) {
    //     await ProjectAPI.saveUpdateLog({
    //       entity_table_name: 'task',
    //       entity_id: this.task.id,
    //       topic: 'USER_EDIT_TASK_CLIENT_SELECTION_TAGS',
    //       create_by_id: this.$store.state.user.uid,
    //       raw_json: {
    //         'tags': this.options.tags.filter(item => originTagsId.includes(item.id))
    //           .map(item => item.name)
    //           .join(', '),
    //       },
    //       json: {
    //         'tags': this.options.tags.filter(item => this.form.tags_id.includes(item.id))
    //           .map(item => item.name)
    //           .join(', '),
    //       },
    //     })
    //   }
    //   this.task.client_selection_tag_refs = data.client_selection_tag_refs
    // },
    // createTag(name, index) {
    //   return ProjectAPI.createClientSelectionTag({
    //     project_id: this.task.project_id,
    //     client_id: this.task.client_id,
    //     scope: 'PROJECT',
    //     name,
    //   }).then(newTag => {
    //     this.options.tags.push(newTag)
    //     this.form.tags_id[index] = newTag.id
    //   })
    // },
  },
}
</script>

<style scoped>
.display .el-form-item {
  width: 45%;
  margin-bottom: 10px;
  display: inline-flex;
}

.el-form-item ::v-deep label {
  font-weight: 400;
  color: #909399;
  line-height: 20px;
}

.el-form-item ::v-deep .el-form-item__content {
  flex: 1;
  line-height: 20px;
}

.client-portal-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
