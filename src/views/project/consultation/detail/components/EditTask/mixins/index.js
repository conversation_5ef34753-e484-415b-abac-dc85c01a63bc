export default {
  props: {
    task: {
      type: Object,
      default() {
        return {}
      },
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      advisor: this.task.advisor,
      isEditing: false,
      loading: false,
    }
  },
  methods: {
    goToEdit() {
      this.form = this.initForm()
      this.isEditing = true
    },
    hideEdit() {
      this.isEditing = false
    },
    onSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.handleSave(this.form)
            .then(() => {
              this.hideEdit()
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
  },
}
