<template>
  <div>
    <h4 class="task-edit-title">
      Contact Information
      <el-link
        v-if="!isView && !task.advisor.is_cn"
        size="mini"
        type="primary"
        :disabled="isGKM"
        :underline="false"
        @click="handleIsView"> View
      </el-link>
      <el-link
        v-if="(isView || task.advisor.is_cn) && !isEditing"
        class="ml-3"
        type="primary"
        size="mini"
        icon="el-icon-edit"
        :underline="false"
        :disabled="isGKM"
        @click="goToEdit"
      />
    </h4>
    <!-- {{task}} -->
    <div>
      <el-form
        v-if="!isEditing"
        :model="display"
        class="display"
        size="mini"
        inline
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="Mobile" class="fs-13">
          <slot v-if="isView">
            <span v-for="(item, index) in display.mobile" :key="index">
              <a :href="'tel:'+item">{{ item }}</a>
              <span v-if="index!==display.mobile.length-1">, </span>
            </span>
            <el-button v-if="display.mobile.length>0" type="text" icon="el-icon-copy-document" @click="copyMobile" />
          </slot>
          <span v-else>
            {{ display.mobile.join(', ') }}
          </span>
        </el-form-item>
        <el-form-item label="Email" class="fs-13">
          {{ display.email.join(', ') }}
        </el-form-item>
        <el-form-item label="Linkedin" class="fs-13">
          <a v-for="(item, index) in display.linkedin" :key="index" class="link" target="_blank" :href="item">
            {{ item }}</a>
        </el-form-item>
        <el-form-item label="Location" class="fs-13">
          {{ display.location }}
        </el-form-item>
        <el-form-item label="Notes" class="fs-13 danger">
          {{ display.notes }}
        </el-form-item>
        <el-form-item label="Time zone" class="fs-13">
          {{ display.zone_id_string }}
        </el-form-item>
      </el-form>
      <div v-if="isEditing">
        <el-form
          ref="form"
          :rules="rules"
          :model="form"
          size="mini"
          label-width="80px"
          label-position="left"
        >
          <el-form-item
            label="Location"
            prop="location_id">
            <location
              v-model="form.location_id"
              @change="actionLocationChange"
            />
          </el-form-item>
          <el-form-item label="Time zone">
            <el-select
              v-model="form.zone_id_string"
              clearable
              filterable>
              <el-option v-for="item in timezone_list" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <div v-if="!task.advisor.is_cn">
            <div class="contact-warning-label warning">
              Must have one of the following
            </div>
            <el-form-item
              v-for="(con, index) in form.contact_infos"
              :key="`contact-${index}`"
              :show-message="false"
              :label="!index ? 'Contact' : ''"
              prop="contact_infos">
              <el-form-item
                class="form-item"
                :prop="`contact_infos[${index}].value`"
                :rules="contactRules[con.type]"
              >
                <el-input v-model="con.value" class="w-400px">
                  <template slot="prepend">
                    <div class="text-center" style="min-width: 70px;">
                      {{ con.type | getLabel(contact_info_options) }}
                    </div>
                  </template>
                </el-input>

              </el-form-item>
              <el-link
                v-if="con.origin"
                type="primary"
                icon="el-icon-plus"
                class="icon-font"
                :underline="false"
                @click="addContact(con.type, index)" />
              <el-link
                v-if="isDisplayRemove(con.type)"
                type="danger"
                icon="el-icon-delete"
                class="icon-font"
                :underline="false"
                @click="removeContact(con, index)" />
              <el-tooltip v-if="isDisplaySetMain(con)"
                          class="item"
                          effect="dark"
                          content="Main Contact"
                          placement="right">
                <el-link
                  v-if="con.is_main"
                  type="success"
                  class="icon-font"
                  :underline="false"
                  icon="el-icon-star-on"
                />
                <el-link
                  v-else
                  class="icon-font"
                  :underline="false"
                  icon="el-icon-star-off"
                  @click="setMainContact(con, index)" />
              </el-tooltip>
            </el-form-item>
          </div>
          <el-form-item label="Notes">
            <el-input
              ref="notes"
              v-model="form.notes"
              type="textarea"
              :min-rows="3"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :disabled="loading"
              icon="el-icon-check"
              @click="onSave()">Save
            </el-button>
            <el-button
              type="text"
              @click="hideEdit">Cancel
            </el-button>
          </el-form-item>
        </el-form>
      </div>

    </div>
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import Location from '@/components/Location'
import mixins from './mixins'
import ToolAPI from '@/api/tool'
import { copyUtil, getLocationById } from '@/utils/tool'

export default {
  name: 'EditContactInformation',
  components: {
    Location,
  },
  mixins: [mixins],
  data() {
    const contactValidator = (rule, value, callback) => {
      if (!value.filter(item => item.value).length) {
        callback(new Error('Must have one of the following'))
      } else {
        callback()
      }
    }
    const emailValidator = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      }, 100)
    }
    const phoneValidator = (rule, value, callback) => {
      const reg = /^\+?\d+(?:-?\d+){5,}$/
      if (!reg.test(value) && value !== '') {
        callback(new Error('Please enter a valid phone number'))
      } else {
        return callback()
      }
    }
    return {
      isView: false,
      display: this.initDislay(),
      contactRules: {
        EMAIL: [{ validator: emailValidator, trigger: 'blur' }],
        PHONE: [{ validator: phoneValidator, trigger: 'blur' }],
        LINKEDIN_URL: [{ required: false, message: 'Please input the value', trigger: 'blur' }],
      },
      rules: {
        contact_infos: [{ required: true, validator: contactValidator, trigger: 'change' }],
        location_id: [{ required: true, message: 'Location is required', trigger: 'change' }],
      },
      form: this.initForm(),
    }
  },
  computed: {
    ...mapGetters([
      'contact_info_options',
      'timezone_list',
      'roles',
    ]),

    isGKM() {
      return this.roles.some(item => {
        return ['GKM'].includes(item.role.name)
      })
    },
  },

  mounted() {
    this.initDisplayData(this.task.advisor)
  },

  methods: {
    handleIsView() {
      const params = {
        'owner_type': 'ADVISOR',
        'owner_id': this.task.advisor.id,
      }
      return ToolAPI.getContactInfo(params)
        .then(data => {
          this.task.advisor.contact_infos.forEach(item => {
            const contact = data['list'].find(list => list.id === item.id)
            if (contact) item.value = contact.value
          })
          this.initDisplayContact(data['list'])
        })
        .finally(() => {
          this.isView = true
        })
    },

    initDislay() {
      return {
        mobile: [],
        email: [],
        linkedin: [],
        location: '',
        zone_id_string: '',
        notes: '',
      }
    },
    initForm() {
      return {
        id: this.task.advisor.id,
        location_id: this.task.advisor.location_id,
        zone_id_string: this.task.advisor.zone_id_string,
        contact_infos: this.initFormContactInfos(this.task.advisor.contact_infos),
        notes: this.task.advisor.notes,
      }
    },
    initFormContactInfos(contact_infos) {
      const list = _.cloneDeep(contact_infos)
      const type_list = ['EMAIL', 'PHONE', 'LINKEDIN_URL']

      type_list.forEach(type => {
        if (list.find(item => item.type === type)) {
          list.find(item => item.type === type).origin = true
        } else {
          list.push({
            type: type,
            value: '',
            is_main: true,
            origin: true,
          })
        }
      })

      return list
    },
    initDisplayData(advisor) {
      this.initDisplayContact(advisor.contact_infos)
      this.display.notes = advisor.notes
      this.display.location = advisor.location?.name
      this.display.zone_id_string = advisor.zone_id_string
    },

    initDisplayContact(contact_infos) {
      this.display.mobile = []
      this.display.email = []
      this.display.linkedin = []
      contact_infos.forEach(contact => {
        const dict = {
          'LINKEDIN_URL': 'linkedin',
          'PHONE': 'mobile',
          'EMAIL': 'email',
        }
        this.display[dict[contact.type]].push(contact.value)
      })
    },

    goToEdit() {
      this.form = this.initForm()
      this.isEditing = true
    },

    addContactItem() {
      this.form.contact_infos.push({
        owner_type: 'CLIENT_CONTACT',
        type: 'EMAIL',
        value: '',
        is_main: false,
      })
      this.checkMainContact()
    },

    addContact(val, index) {
      this.form.contact_infos.splice(index + 1, 0, {
        type: val,
        value: '',
        is_main: false,
      })
    },

    removeContact(val, index) {
      this.form.contact_infos.splice(index, 1)
      if (val.is_main) {
        this.form.contact_infos.find(item => item.type === val.type && item.origin).is_main = true
      }

      if (val.origin) {
        this.form.contact_infos.find(item => item.type === val.type).origin = true
      }
    },

    removeContactItem(index) {
      this.form.contact_infos.splice(index, 1)
      this.checkMainContact()
    },

    // 每种类型的联系方式 只允许有一个 main联系方式
    setMainContact(item, index) {
      if (item.is_main && this.form.contact_infos.length > 1) {
        this.$set(item, 'is_main', false)
      } else {
        this.$set(item, 'is_main', true)
        this.form.contact_infos.forEach((info, ind) => {
          if (info.type === item.type && ind !== index) {
            this.$set(info, 'is_main', false)
          }
          return info
        })
      }
    },
    changeType(select, index) {
      const contact_list = this.form.contact_infos
      const list = contact_list.filter(item => item.type === select.type)
      const has_main = list.filter(item => item.is_main).length
      if (list.length < 2) {
        this.form.contact_infos[index].is_main = true
      } else if (has_main && list.length > 1) {
        this.form.contact_infos[index].is_main = false
      } else {
        this.form.contact_infos.find(item => item.type === select.type).is_main = true
      }
      this.checkMainContact()
    },
    checkMainContact() {
      const data = _.cloneDeep(this.form.contact_infos)
      const email_list = data.filter(item => item.type === 'EMAIL')
      const phone_list = data.filter(item => item.type === 'PHONE')

      function checkMain(o_list, list) {
        if (!list.length) return []
        const has_main = list.filter(item => item.is_main).length
        if (!has_main) {
          o_list.find(item => item.type === list[0].type).is_main = true
        }
      }

      checkMain(this.form.contact_infos, phone_list)
      checkMain(this.form.contact_infos, email_list)
    },

    isDisplayRemove(type) {
      const type_list = this.form.contact_infos.filter(item => item.type === type)
      return type_list.length > 1
    },

    /**
     * 是否显示 "设置主要联系方式" 按钮
     * 同一种联系方式出现多次时，提供 "set main" 选项
     * @param data
     * @returns {boolean}
     */
    isDisplaySetMain(data) {
      return this.form.contact_infos.filter(item => {
        return item.type === data.type
      }).length > 1
    },
    handleSave(val) {
      val.contact_infos = val.contact_infos.filter(item => item.value)
      return ConsultantAPI.updateConsultant(val)
        .then(data => {
          this.$set(this.task.advisor, 'contact_infos', data.contact_infos)
          this.task.advisor.location = {
            name: data.location?.name,
          }
          this.task.advisor.location_id = data.location?.id
          this.task.advisor.zone_id_string = data.zone_id_string
          // this.task.advisor.email = data.email
          // this.task.advisor.mobile = data.mobile
          this.task.advisor.notes = val.notes
          this.display = this.initDislay()
          this.isView = false
          this.initDisplayData(this.task.advisor)
          this.initDisplayContact(data.contact_infos)
          this.$emit('save', this.task)
        })
    },

    actionLocationChange(location_id) {
      return getLocationById(location_id)
        .then(data => {
          if (data) {
            if (data['id_path'].includes('108')) {
              this.form.locale = 'zh-CN'
            } else {
              this.form.locale = 'en-US'
            }
          }
        })
    },

    copyMobile() {
      const val = this.display.mobile.join()
      copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.display .el-form-item {
  width: 45%;
  margin-bottom: 10px;
  display: inline-flex;
}

.el-form-item ::v-deep label {
  font-weight: 400;
  color: #909399;
  line-height: 20px;
}

.el-form-item ::v-deep .el-form-item__content {
  flex: 1;
  line-height: 20px;
}

::v-deep .form-item {
  display: inline-block;
  margin-bottom: 0;

  > .el-form-item__content {
    margin-left: 0;
  }

  .el-form-item__label {
    font-size: 13px;
  }
}

.contact-warning-label {
  margin-left: 80px;
  display: block;
  font-size: 12px;
  line-height: 14px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.w-400px {
  width: 400px;
}

</style>
