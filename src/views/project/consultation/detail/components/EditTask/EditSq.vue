<template>
  <div>
    <h4 class="task-edit-title">
      Screening Questions / Responses
      <span v-if="no_instance">
        <el-link
          class="ml-3"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          :underline="false"
          @click="getSQOptions()"
        />
        <span v-if="isSelect" class="ml-3">
          <el-select v-model="select_sq" placeholder="Please select" @change="selectMatchSQ()">
            <el-option
              v-for="item in sq_options"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </span>
      </span>
      <span v-if="!no_instance">
        <el-link
          v-if="!hasAnswer && !isEditQuestionnaire"
          class="sq-icon-link"
          type="primary"
          size="mini"
          title="Edit Screening Questions / Responses"
          icon="el-icon-edit"
          :underline="false"
          @click="isEditQuestionnaire=true"
        />
        <span class="sqStatus">{{ task.sq_status }}</span>
      </span>
      <SQSummary
        :task-id="task.id"
        :project-id="project_id"
        :summary.sync="sq_summary"
        class="sq-icon-link"
        @update="$emit('update')" />
      <AttachSQSummary
        :task.sync="task"
        :summary.sync="sq_summary"
        class="sq-icon-link"
        @update="$emit('update')" />
      <AdvisorSQ
        :advisor-id="advisor.id"
        :task-id="task.id"
        :project-id="project_id"
        class="inline-block sq-icon-link"
        @update="reload"
      />
      <slot v-if="hasAnswer && instance">
        <client-portal-sq-publish class="inline-block ml-2" :instance.sync="instance" :questions="questions" />
      </slot>
      <SQFilter v-if="instance" :instance.sync="instance" :questions="questions" />
    </h4>

    <div v-loading="loading" class="min-h-50">
      <div v-if="no_instance">
        <div v-if="!isSelect" class="warning">This task has no associated Screening Questions.</div>
      </div>
      <div v-if="sq_summary" class="mb-normal pre-line flex">
        <b class="inline-block">Summary:&nbsp;</b> {{ sq_summary }}
      </div>
      <ClientPortalTags :task="task" />
      <div
        v-for="(item, index) in questions"
        :id="`question-${index}`"
        :key="index"
      >
        <div v-if="!isSpecialQuestion(item)">
          <div class="question-item flex">
            <div class="mr-2 text-right" style="width:30px">
              <el-tooltip
                class="item"
                effect="dark"
                offset="7"
                content="Display on client portal"
                placement="top-start">
                <i
                  v-show="showOnClientAdvisorPortal(item,'client_publish_question_ids')"
                  class="el-icon-view success" />
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                offset="7"
                content="Display on the screening question page to the experts"
                placement="top-start">
                <svg-icon
                  v-show="showOnClientAdvisorPortal(item,'advisor_screening_question_ids')"
                  icon-class="question-circle" />
              </el-tooltip>
            </div>
            <slot>
              <b v-if="item.id && item.id > 0" class="mr-2">Q{{ item.sort_order + 1 + '.' }}</b>
              <el-tooltip v-else class="item" effect="dark" offset="5" content="Created in task" placement="top-start">
                <b class="mr-2 warning">Q{{ item.sort_order + 1 + '.' }}</b>
              </el-tooltip>
            </slot>

            <div class="flex flex-column flex-1">
              <div v-if="!item.edit_answer">
                <div class="outermost-p-td-0 inline-block" v-html="item.title" />
                <el-link
                  v-if="hasAnswer"
                  type="primary"
                  size="mini"
                  class="ml-3"
                  icon="el-icon-edit"
                  :underline="false"
                  @click="editAnswer(item)"
                />
              </div>
              <div v-else>
                <tinymce
                  v-model="item.title"
                  :height="100"
                  :toolbar="toolbar"
                  class="flex-1" />
              </div>

              <div class="mt-8">
                <div v-if="isEditQuestionnaire || item.edit_answer" class="flex-1">
                  <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                    <div v-for="(temp, temp_index) in item.answer_text" :key="temp_index" class="mt-normal">
                      <el-input v-model="temp.option" disabled />
                      <div class="flex mt-8">
                        <el-input v-if="item.type === 'RANKING_SET'" v-model="temp.rank" placeholder="Rank" class="mr-8" style="width:80px" />
                        <el-input v-model="temp.text" class="flex-1" type="textarea" :rows="3" />
                      </div>
                    </div>
                  </div>
                  <template v-if="item.type === 'RADIO'">
                    <div
                      v-for="(temp, _index) in item.options"
                      :key="_index"
                    >
                      <el-radio
                        v-model="item.answer_text"
                        :label="_index"
                        class="sq-option-block">{{temp.text}}</el-radio>
                      <div v-show="temp.description && item.answer_text===_index">
                        <div>{{temp.desc_prompt}}</div>
                        <el-input
                          v-model="item.answer_desc"
                          class="desc"
                          :class="{'required':temp.require_desc}"
                          type="textarea" />
                      </div>
                    </div>
                  </template>
                  <template v-if="item.type === 'CHECKBOX'">
                    <div
                      v-for="(temp, _index) in item.options"
                      :key="_index"
                    >
                      <el-checkbox
                        v-model="item.answer_text"
                        :label="_index"
                        class="sq-option-block">{{temp.text}}</el-checkbox>
                      <template v-if="temp.description && item.answer_text.includes(_index)">
                        <div>{{temp.desc_prompt}}</div>
                        <el-input
                          v-model="item.answer_desc[_index]"
                          class="desc"
                          :class="{'required':temp.require_desc}"
                          type="textarea" />
                      </template>
                    </div>
                  </template>
                  <div v-if="!['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(item.type)" class="flex-1 flex">
                    <el-input-number
                      v-if="item.type==='RANKING_WITH_EXPLANATION'"
                      v-model="item.answer_rank"
                      :min="0"
                      controls-position="right"
                      class="custom-num-input"
                    />
                    <el-input v-model="item.answer_text" type="textarea" :rows="5" class="flex-1 mt-8" />
                  </div>
                </div>
                <slot v-else>
                  <div v-if="hasAnswer || item.answer_text">
                    <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)" class="answer-item">
                      <div v-for="(temp, _index) in item.answer_text" :key="_index">
                        <div v-if="temp.option" class="c-333 mb-5px">{{ temp.option }}</div>
                        <span v-if="item.type==='RANKING_SET'" class>{{ temp.rank }} -</span> {{ temp.text }}
                      </div>
                    </div>
                    <div v-if="item.type === 'RADIO'">
                      <el-radio-group v-model="item.answer_text">
                        <el-radio
                          v-for="(temp, _index) in item.options"
                          :key="_index"
                          disabled
                          :label="_index"
                          class="sq-option-block">
                          {{temp.text}}
                          <div v-if="temp.description && item.answer_text===_index" class="text-decoration-underline ml-24px">{{item.answer_desc}}</div>
                        </el-radio>
                      </el-radio-group>
                    </div>
                    <div v-if="item.type === 'CHECKBOX'">
                      <el-checkbox-group v-model="item.answer_text" class="w-100">
                        <el-checkbox
                          v-for="(temp, _index) in item.options"
                          :key="_index"
                          disabled
                          class="sq-option-flex"
                          :label="_index"
                        >
                          {{temp.text}}
                          <div v-if="temp.description" class="text-decoration-underline">
                            {{item.answer_desc[_index]}}
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                    <div v-if="!['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(item.type)" class="answer-item">
                      <span v-if="item.answer_rank">{{ item.answer_rank }} </span>{{ item.answer_text }}
                    </div>
                  </div>
                </slot>
                <div v-if="item.edit_answer" class="icon-btn mb-3px">
                  <el-button
                    type="primary"
                    :disabled="updateAnswerDisabled(item)"
                    size="mini"
                    icon="el-icon-check"
                    @click="updateAnswer(item)">
                    Save
                  </el-button>
                  <el-button type="text" @click="cancelEditAnswer(item)">
                    Cancel
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="specialQuestions.length">
        <div
          v-for="(question, index) in specialQuestions"
          :key="question.id"
          class="question-item flex"
        >
          <div class="mr-2 text-right" style="width: 30px">
            <el-tooltip
              class="item"
              effect="dark"
              offset="7"
              content="Display on client portal"
              placement="top-start">
              <i
                v-show="showOnClientAdvisorPortal(question,'client_publish_question_ids')"
                class="el-icon-view success" />
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              offset="7"
              content="Display on the screening question page to the experts"
              placement="top-start">
              <svg-icon
                v-show="showOnClientAdvisorPortal(question,'advisor_screening_question_ids')"
                icon-class="question-circle" />
            </el-tooltip>
          </div>
          <el-tooltip
            class="item"
            effect="dark"
            offset="5"
            content="This question is used to confirm the expert's information and will not be displayed to the customer."
            placement="top-start">
            <b class="mr-2 primary">Q{{ questions.length - specialQuestions.length + 1 + index + '.' }}</b>
          </el-tooltip>
          <div class="flex flex-column flex-1">
            <div v-if="!question.edit_answer">
              <div
                class="outermost-p-td-0 inline-block"
                v-html="question.title" />
              <el-link
                v-if="hasAnswer "
                type="primary"
                size="mini"
                class="ml-3"
                icon="el-icon-edit"
                :underline="false"
                @click="editAnswer(question)"
              />
            </div>
            <div v-else>
              <tinymce
                v-model="question.title"
                :height="100"
                :toolbar="toolbar"
                class="flex-1" />
            </div>

            <div class="mt-8">
              <div v-if="isEditQuestionnaire || question.edit_answer" class="flex-1">
                <el-input
                  v-if="question.tags.includes('ADVISOR_JOB_CONFIRM')"
                  v-model="question.answer_text"
                  type="textarea"
                  :rows="5"
                  class="flex-1 mt-8"
                />
                <template v-if="question.tags.includes('ADVISOR_NPI_CONFIRM')">
                  <npi-number-input
                    v-model="question.answer_text"
                    :npi-status.sync="npiStatus"
                    :advisor-id="advisor.id"
                    :loading.sync="npiLoading"
                    class="w-200px"
                  />
                  <span
                    v-if="question.answer_text.length !== 10"
                    class="danger ml-8"
                  >
                    NPI # is 10 digits.
                  </span>
                </template>
              </div>
              <slot v-else>
                <div v-if="hasAnswer || question.answer_text" class="answer-item">{{ question.answer_text }}</div>
              </slot>
              <div v-if="question.edit_answer" class="icon-btn mb-3px">
                <el-button
                  type="primary"
                  :disabled="question.tags.includes('ADVISOR_NPI_CONFIRM') && npiStatus !== 'VALID'"
                  :loading="loading"
                  size="mini"
                  icon="el-icon-check"
                  @click="updateAnswer(question)">
                  Save
                </el-button>
                <el-button type="text" @click="cancelEditAnswer(question)">
                  Cancel
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <add-item
        v-if="hasAnswer"
        :questions="questions"
        answered
        :instance-id="instanceId"
        :instance.sync="instance"
        :special-questions="specialQuestions"
        @update="QuestionnaireAddItemUpdate" />
    </div>
    <div v-if="isEditQuestionnaire" class="mt-8">
      <el-button
        type="primary"
        :disabled="loading"
        size="mini"
        icon="el-icon-check"
        @click="responseQuestions()">
        Save
      </el-button>
      <el-button type="text" size="mini" @click="cancelEditQuestionnaire">Cancel</el-button>
      <add-item
        :questions="questions"
        :instance-id="instanceId"
        :special-questions="specialQuestions"
        @update="QuestionnaireAddItem" />
    </div>
  </div>
</template>

<script>
import mixins from './mixins'
import InquiryAPI from '@/api/inquiry'
import AdvisorSQ from '@/views/project/consultation/detail/components/EditTask/components/AdvisorSQ'
import SQSummary from '@/views/project/consultation/detail/components/EditTask/components/ScreenQuestionsSummary'
import AttachSQSummary from '@/views/project/consultation/detail/components/EditTask/components/AttachSQSummary'
import ClientPortalSqPublish from '@/views/project/consultation/detail/components/EditTask/components/SQPublish'
import ClientPortalTags from '@/views/project/consultation/detail/components/EditTask/components/TagsForClientPortal'
import SQFilter from '@/views/project/consultation/detail/components/EditTask/components/SQFilter'
import AddItem from '@/views/project/consultation/detail/components/EditTask/components/AddSQItem'
import NpiNumberInput from '@/views/consultant/detail/components/NpiNumberInput'
import { mapGetters, mapState } from 'vuex'
import Tinymce from '@/components/Tinymce'
import _ from 'lodash'

export default {
  name: 'EditSq',
  components: {
    AdvisorSQ,
    Tinymce,
    SQSummary,
    AttachSQSummary,
    ClientPortalSqPublish,
    SQFilter,
    AddItem,
    ClientPortalTags,
    NpiNumberInput,
  },
  mixins: [mixins],
  data() {
    return {
      loading: false,
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      project_id: +this.$route.params.project_id,
      no_instance: false,
      inquiryId: undefined,
      branchId: undefined,
      questions: [],
      instanceId: undefined,
      instance: null,
      isEditQuestionnaire: false,
      hasAnswer: true,
      isSelect: false,
      sq_options: undefined,
      select_sq: undefined,
      sq_summary: '',
      npiLoading: false,
      npiStatus: 'INITIAL',
    }
  },

  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
    ...mapGetters([
      'uid',
    ]),
    hasAngleSQ() {
      return this.task.angle && this.task.angle.inquiry_branch_id
    },

    // https://www.notion.so/capvision/Screening-Questions-Automatic-screener-78ff9e60ae444790a1f7de84a0fe4ba9
    // 用于确认专家信息的问题 始终在问卷的最底部
    // ADVISOR_JOB_CONFIRM 和 ADVISOR_NPI_CONFIRM
    specialQuestions() {
      return this.questions.filter(item => this.isSpecialQuestion(item))
    },
  },

  watch: {
    'task.sq_status': {
      handler(newVal) {
        if (newVal) {
          setTimeout(() => {
            this.checkSQStatus()
          }, 300)
        }
      },
    },
    'task.angle.inquiry_branch_id': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.task.sq_status === 'INITIAL') {
          this.checkSQStatus()
        }
      },
    },
  },

  mounted() {
    this.checkSQStatus()
    this.sq_summary = this.task.screening_summary
  },

  methods: {
    async getSQOptions() {
      this.isSelect = true
      const data = this.projectInfo
      if (this.sq_options || !data.sq_id) return

      this.inquiryId = data.sq_id

      await InquiryAPI.getInquiryOne(this.inquiryId)
        .then(data => {
          this.sq_options = data.branches.map(item => {
            return {
              name: item.title,
              id: item.id,
            }
          })
        })
    },

    showOnClientAdvisorPortal(item, type) {
      const id = +item.id
      if (!this.instance) return false
      // advisor_screening_question_ids 初始化null，所有问题在advisor portal 可见
      // client_publish_question_ids 初始化null，所有问题在client portal 可见
      if (typeof this.instance[type] === 'object' && this.instance[type] === null) return true
      return this.instance[type].includes(id)
    },

    checkSQStatus() {
      this.loading = true
      switch (this.task.sq_status) {
        case 'INITIAL':
          this.hasAnswer = false
          return this.matchSQ()
        case 'SENT':
          this.instanceId = this.task['latest_sq'].id
          this.no_instance = false
          this.hasAnswer = false
          return this.getInstance()
        case 'RESPONDED':
          if (this.task['latest_submitted_sq']) {
            this.instanceId = this.task['latest_submitted_sq'].id
            this.hasAnswer = true
            this.no_instance = false
            return this.getInstance()
          } else {
            this.loading = false
          }
      }
    },

    getInstance() {
      return InquiryAPI.getInstanceOne(this.instanceId)
        .then(data => {
          this.$set(this, 'instance', data)
          this.formatQuestions(data['qa_list_snapshot'])
        })
        .finally(() => {
          this.loading = false
        })
    },

    matchSQ() {
      // 未发送SQ时 自动分配angle绑定的SQ
      if (this.hasAngleSQ) {
        const data = this.projectInfo
        this.inquiryId = data.sq_id
        this.getBranch(this.task.angle.inquiry_branch_id)
          .then(data => {
            if (data) {
              this.branchId = this.task.angle.inquiry_branch_id
            }
          })
      } else {
        this.no_instance = true
        this.loading = false
      }
    },

    selectMatchSQ() {
      this.getBranch(this.select_sq)
        .then(() => {
          this.isEditQuestionnaire = true
          this.hasAnswer = false
        })
    },

    getBranch(branchId) {
      return InquiryAPI.getBranchOne(this.inquiryId, branchId)
        .then(data => {
          if (data) {
            this.formatQuestions(data['questions'])
            return data
          } else {
            this.no_instance = true
          }
          return data
        })
        .finally(() => {
          this.loading = false
        })
    },

    async responseQuestions() {
      const params = { qa_list_snapshot: [] }
      const format_answers = this.questions.map(item => {
        item.answer = {
          content: {
            result: item.answer_text,
            desc: this.transferDesc(item),
            rank: item.answer_rank || undefined,
          },
        }
        return item
      })
      const branch_sq = format_answers.filter(item => item.id)
      const user_create_sq = format_answers.filter(item => !item.id)
      params['qa_list_snapshot'] = branch_sq

      if (!this.instanceId) {
        await this.getInstanceID()
      }
      params.id = this.instanceId

      await InquiryAPI.answerQuestion(this.task.id, params)
        .then(data => {
          // 用户在代答问卷时  添加了针对这种份问卷的一次性问题
          if (user_create_sq.length > 0) {
            const oneoff_params = { id: this.instanceId, qa_list_snapshot: format_answers }
            if (this.specialQuestions.length) {
              oneoff_params.qa_list_snapshot.forEach(item => {
                const index = this.specialQuestions.findIndex(q => q.id === item.id)
                if (index > -1) {
                  item.sort_order = this.questions.length - this.specialQuestions.length + index
                }
              })
            }
            return InquiryAPI.updateSQOneOff(oneoff_params)
          } else {
            return data
          }
        })
        .then((data) => {
          this.task.sq_status = 'RESPONDED'
          this.formatQuestions(data['qa_list_snapshot'])
          this.instance = data
          this.hasAnswer = true
          this.no_instance = false
          this.$emit('update')
          this.$message({
            message: 'Save successful',
            type: 'success',
          })
        }, (err) => {
          this.$message({
            message: err,
            type: 'error',
          })
        })
        .finally(() => {
          this.cancelEditQuestionnaire()
        })
    },

    // 系统代答  生成Instance
    getInstanceID() {
      const params = {
        is_through_advisor: false,
        inquiry_id: this.inquiryId,
        branch_id: this.branchId || this.select_sq,
        source_id: this.task.id,
        receiver_id: this.advisor.id,
      }
      return InquiryAPI.createInstance(params)
        .then(data => {
          this.instanceId = data.id
        })
    },

    cancelEditQuestionnaire() {
      if (!this.hasAnswer) {
        this.questions.forEach(item => {
          item.answer_text = item.type === 'CHECKBOX' ? [] : ''
          item.answer_desc = item.type === 'CHECKBOX' ? {} : ''
          item.answer_rank = null
        })
      }

      if (this.no_instance) {
        this.isSelect = false
        this.select_sq = undefined
        this.questions = []
      }

      this.isEditQuestionnaire = false
    },

    QuestionnaireAddItem(val) {
      if (this.specialQuestions?.length) {
        this.questions.splice(this.questions.length, 0, val)
        this.questions.forEach(item => {
          const index = this.specialQuestions.findIndex(q => q.id === item.id)
          if (index > -1) {
            item.sort_order = this.questions.length - this.specialQuestions.length + index
          }
        })
      } else {
        this.questions.push(val)
      }
    },

    QuestionnaireAddItemUpdate(val) {
      this.QuestionnaireAddItem(val)
      this.$emit('update')
    },

    formatQuestions(list) {
      const new_list = _.cloneDeep(list)
      this.questions = new_list.map(item => {
        item.answer_text = formatAnswer(item)
        item.answer_desc = item.answer ? item.answer.content.desc : (item.type === 'CHECKBOX' ? {} : '')
        item.answer_rank = item.answer ? item.answer.content.rank : null
        item.question_id = item.id || 0
        item.edit_answer = false
        return item
      })

      function formatAnswer(item) {
        let result
        if (item.type === 'RANKING_SET') {
          result = item.answer?.content?.result || item.options.map(i => {
            return { option: i.value, rank: null, text: '' }
          })
        } else if (item.type === 'NO_RANKING_SET') {
          result = item.answer?.content?.result || item.options.map(i => {
            return { option: i.value, text: '' }
          })
        } else if (item.type === 'CHECKBOX') {
          result = item.answer ? item.answer.content.result : []
        } else {
          result = item.answer ? item.answer.content.result : ''
        }
        return result
      }
    },

    // 修正专家答案

    editAnswer(item) {
      item.origin_title = item.title
      item.origin_answer = item.answer_text
      item.origin_desc = item.answer_desc
      item.origin_rank = item.answer_rank
      item.origin_options = item.options
      this.$set(item, 'edit_answer', true)
    },

    cancelEditAnswer(item) {
      item.answer_text = item.origin_answer
      item.answer_desc = item.origin_desc
      item.answer_rank = item.origin_rank
      item.title = item.origin_title
      item.options = item.origin_options
      this.$set(item, 'edit_answer', false)
    },

    updateAnswer(data_item) {
      this.loading = true
      const params = {
        id: this.instanceId,
      }
      params.qa_list_snapshot = this.questions.map(e => {
        let format = {}
        if (e.id === data_item.question_id) {
          format = {
            title: data_item.title,
            answer: {
              content:
                {
                  result: data_item.answer_text,
                  desc: this.transferDesc(data_item),
                  rank: data_item.answer_rank,
                },
              update_by_id: this.uid,
            },
            edit_answer: false,
          }
        }
        return Object.assign({ ...e }, format)
      })

      return InquiryAPI.updateAnswer(params).then((data) => {
        this.task.latest_submitted_sq = data
        this.questions = params.qa_list_snapshot
        this.$emit('save', this.task)
        this.$emit('update')
        this.$message({
          message: 'Save successful',
          type: 'success',
        }, () => {})
      }).finally(() => {
        this.loading = false
        this.$set(data_item, 'edit_answer', false)
      })
    },

    updateAnswerDisabled(item) {
      if (this.loading) return true
      const empty_input = !item.title.replace(/\s/g, '').replace(/<[^\/>][^>]*><\/[^>]*>/g, '') || !item.title.trim()
      if (empty_input) return true
      if (item.type === 'CHECKBOX') {
        return item.options.filter((i, index) => item.answer_text.includes(index) && i.description && i.require_desc && !item.answer_desc[index]).length > 0
      } else if (item.type === 'RADIO') {
        return item.options.filter((i, index) => item.answer_text === index && i.description && i.require_desc && !item.answer_desc).length > 0
      } else {
        return false
      }
    },
    reload(id) {
      this.instanceId = id
      this.hasAnswer = true
      this.getInstance()
      this.$emit('update')
    },

    isSpecialQuestion(item) {
      return item.tags?.includes('ADVISOR_JOB_CONFIRM') || item.tags?.includes('ADVISOR_NPI_CONFIRM')
    },

    transferDesc(item) {
      if (item.type === 'RADIO') {
        return item.options[item.answer_text]?.description ? item.answer_desc : undefined
      } else if (item.type === 'CHECKBOX') {
        const desc = {}
        item.options.forEach((option, index) => {
          if (item.answer_text.includes(index)) {
            desc[index] = item.answer_desc[index]
          }
        })
        return desc
      } else {
        return undefined
      }
    },
  },
}
</script>

<style scoped lang="scss">
.question-item {
  margin: 6px 0;
}

.mb-3px {
  margin-bottom: 3px;
}

.ml-2 {
  margin-left: 2px;
}

.mr-2 {
  margin-right: 2px;
}

.min-h-50 {
  min-height: 50px;
}

.answer-item {
  padding: 3px 5px;
  border: 1px solid #cbcdd2;
  border-radius: 4px;
  color: #3f9eff;
  font-style: italic;
  min-height: 1.3rem;
  white-space: pre-wrap;
}
.sqStatus {
  color: #d8dadc;
  font-size: 0.5rem;
  font-weight: 400;
  margin-left: 3px;
  margin-right: 3px;
}

.sq-icon-link {
  margin-left: 3px;
  margin-right: 3px;
}

.ml-24px{
  margin-left: 24px;
}

.desc{
  margin-top: 4px;
  position: relative;
}

.required:before {
  position: absolute;
  font-size: 16px;
  top: -2px;
  left: -8px;
  content: "*";
  color: #f56c6c;
}

.icon-btn {
  margin-top: 8px;

  ::v-deep.el-button--mini {
    padding: 1px 2px;
  }

  ::v-deep.el-button {
    margin-left: 0;
  }
}

.custom-num-input {
  width: 50px;
  margin-right: 5px;

  ::v-deep .el-input__inner {
    height: 48px;
    padding-right: 10px;
  }

  ::v-deep .el-input-number__increase {
    display: none;
  }

  ::v-deep .el-input-number__decrease {
    display: none;
  }
}

::v-deep .mce-menubar {
  border: 0;
}

::v-deep .mce-statusbar .mce-container-body {
  display: none;
}
</style>
