<template>
  <div>
    <h4 class="task-edit-title">
      Employment History
      <el-link
        v-if="!isEditing"
        class="ml-3"
        type="primary"
        size="mini"
        icon="el-icon-edit"
        :underline="false"
        @click="goToEdit"
      />
    </h4>
    <el-form
      v-if="!isEditing"
      class="display"
      size="mini"
      inline
      label-position="left"
    >
      <el-form-item>
        <div v-if="display.jobs.length">
          <li v-for="job in display.jobs" :key="job.id">
            {{
              `${job.company && job.company.name} | ${job.position} | ${jobDateFormat(job.start_date)} ~
                  ${job.is_current ? 'current' : jobDateFormat(job.end_date)}`
            }}
          </li>
        </div>
      </el-form-item>
    </el-form>
    <div v-if="isEditing">
      <el-form
        ref="form"
        :model="form"
        size="mini"
        label-position="left"
      >
        <el-form-item
          v-for="(exp, index) in form.jobs"
          :key="`experience-${index}`"
          :label="''">
          <el-form-item
            class="form-item"
            :prop="`jobs[${index}].company`"
            :rules="{required: true, message: 'Company is required', trigger: 'change'}"
          >
            <el-select
              v-model="exp.company"
              value-key="id"
              class="remote-select"
              filterable
              remote
              reserve-keyword
              placeholder="Company"
              loading-text="loading..."
              :remote-method="getCompanyOptions"
              no-data-text="No matching data"
              @change="(val)=> {exp.company_id = val.id}"
            >
              <el-option v-for="item in companyList.data" :key="item.id" :label="item.name" :value="item">
                <span>{{ item.name }}</span>
                <span class="ml-3 mr-normal pull-right" style="color: #8492a6;">{{ item.stock_code }}</span>
              </el-option>
              <template slot="empty">
                <p class="el-select-dropdown__empty">
                  <el-link
                    type="primary"
                    :underline="false"
                    @click="goToCreateCompany(companySelectInput, index)">
                    Create <strong>{{ companySelectInput }}</strong> ...
                  </el-link>
                </p>
              </template>
            </el-select>
          </el-form-item>
          <el-link
            v-if="exp.company_name && !exp.company_id"
            type="primary"
            :underline="false"
            @click="goToCreateCompany(exp.company_name, index)">
            Create Company: {{ exp.company_name }}
          </el-link>
          <el-form-item
            class="form-item"
            :prop="'jobs.' + index + '.position'"
            :rules="{required: true, message: 'Position is required', trigger: 'change'}"
          >
            <el-input
              v-model="exp.position"
              placeholder="Position" />
          </el-form-item>
          <el-form-item
            class="form-item"
            :prop="'jobs.' + index + '.start_date'"
            :rules="{required: true, message: 'Start date is required', trigger: 'change'}"
          >
            <el-date-picker
              v-model="exp.start_date"
              type="month"
              placeholder="Start Date"
              format="MM/yyyy"
              value-format="yyyy-MM"
            />
          </el-form-item>
          -
          <div class="inline-block">
            <el-form-item
              class="form-item"
              :prop="'jobs.' + index"
              :rules="{required: true, validator: endTimeValidator , trigger: 'change'}"
            >
              <el-date-picker
                v-model="exp.end_date"
                :disabled="exp.is_current"
                type="month"
                placeholder="End Date"
                format="MM/yyyy"
                value-format="yyyy-MM"
              />
            </el-form-item>
            <el-form-item
              class="form-item ml-8"
              :prop="'jobs.' + index + '.is_current'"
            >
              <el-checkbox v-model="exp.is_current">Current
              </el-checkbox>
            </el-form-item>
          </div>
          <el-link
            v-if="form.jobs.length > 1"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeFormItem(form.jobs, index)" />
        </el-form-item>
        <el-form-item>
          <el-link
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addExperience" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="loading"
            icon="el-icon-check"
            @click="onSave()">Save
          </el-button>
          <el-button type="text" @click="hideEdit">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-dialog
      title="Create Company"
      width="600px"
      append-to-body
      :visible.sync="dialogVisible"
      @open="openCompanyDialog">
      <create-company ref="create-company" is-dialog :data="{name: companySelectInput}" @create="afterCreateCompany" />
    </el-dialog>
  </div>
</template>

<script>
import ConsultantAPI from '@/api/consultant.js'
import CompanyAPI from '@/api/company'
import _ from 'lodash'
import moment from 'moment-timezone'
import mixins from './mixins'
import createCompany from '@/views/company/update'

export default {
  name: 'EditEmploymentHistory',
  components: {
    createCompany,
  },
  mixins: [mixins],
  data() {
    return {
      advisor: this.task.advisor,
      dialogVisible: false,
      display: {
        jobs: [],
      },
      form: this.initForm(),
      companyList: {
        data: [],
        count: 0,
      },
      companySelectInput: '',
    }
  },
  mounted() {
    this.task.advisor.jobs = this.sortJobs(this.task.advisor.jobs)
    this.initDisplayData(this.task.advisor)
    this.initSelect()
  },
  methods: {
    sortJobs(list) {
      let current_list = []
      let other_list = []
      list.forEach(item => {
        if (item.is_current) {
          current_list.push(item)
        } else {
          other_list.push(item)
        }
      })
      other_list = _.orderBy(
        other_list,
        'end_date', 'desc',
      )
      current_list = _.orderBy(
        current_list,
        'start_date', 'desc',
      )
      return current_list.concat(other_list)
    },

    initJobData() {
      return
    },
    initSelect() {
      const editJobIds = this.form.jobs.map(item => {
        return item.company && item.company.id
      }).join()
      this.loadGetCompany({
        ids: editJobIds,
        page: 1,
        size: 100,
      })
    },
    initForm() {
      return {
        id: this.task.advisor.id,
        jobs: _.cloneDeep(this.task.advisor.jobs),
      }
    },
    initDisplayData(advisor) {
      this.display.jobs = _.cloneDeep(advisor.jobs)
    },
    goToEdit() {
      this.initSelect()
      this.form = this.initForm()
      this.isEditing = true
    },
    jobDateFormat(date) {
      const list = date.split('-')
      switch (list.length) {
        case 2:
          return moment(date).format('MM/YYYY')
        case 3:
          return moment(date).format('MM/DD/YYYY')
        default:
          return date
      }
    },
    getCompanyOptions(val) {
      if (!val) return
      this.companySelectInput = val
      this.loadGetCompanyByEs(val)
    },
    loadGetCompany(params) {
      this.selectLoading = true
      return CompanyAPI.getCompanyList(params)
        .then(data => {
          this.companyList.data = data.list
          this.companyList.count = data.count
        })
        .finally(() => {
          this.selectLoading = false
        })
    },
    loadGetCompanyByEs(val) {
      this.selectLoading = true
      const params = {
        keyword: val,
        page: 1,
        size: 20,
      }
      return CompanyAPI.getCompanyListByEs(params)
        .then(data => {
          this.companyList.data = data.list.map(item => item.source)
          this.companyList.count = data.total
        })
        .finally(() => {
          this.selectLoading = false
        })
    },
    goToCreateCompany(name, index) {
      this.companySelectInput = name
      this.jobEditingIndex = index
      this.dialogVisible = true
    },
    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },
    afterCreateCompany(val) {
      this.dialogVisible = false
      const job = this.form.jobs[this.jobEditingIndex]
      job.company = val
      job.company_id = val.id
      this.companyList.data.push(val)
    },
    autoGenerateBackground() {
      const background = this.$refs['background-format']
      background.handleFormatProfile()
      this.$nextTick(() => {
        this.form.background = background.$el.innerText.trim().replace(/\s+/g, ' ')
      })
    },
    endTimeValidator(rule, value, callback) {
      if (!value.end_date && !value.is_current) {
        callback(new Error('end date is required'))
      } else {
        callback()
      }
    },
    removeFormItem(form, index) {
      form.splice(index, 1)
    },
    addExperience() {
      this.form.jobs.push({
        company: null,
        company_id: '',
        position: '',
        start_date: '',
        end_date: '',
        is_current: false,
      })
    },
    handleSave(val) {
      if (this.isJobsEdited(val.jobs)) {
        val.latest_manually_update_at = moment().toISOString()
      }

      val.jobs.forEach(item => {
        item.legal_validate_result = null
      })
      return ConsultantAPI.updateConsultant(val)
        .then(data => {
          this.$set(this.task.advisor, 'jobs', data.jobs)
          this.$set(this.task.advisor, 'current_job', data.current_job)
          this.initDisplayData(data)
          this.$emit('save', this.task)
        })
    },

    isJobsEdited(jobs) {
      if (jobs.length !== this.task.advisor.jobs.length) {
        return true
      }
      return jobs.filter(item => checkItem(item, this.task.advisor.jobs)).length > 0

      function checkItem(data, origin_list) {
        let flag = false
        const origin_company = origin_list?.find(item => item.id === data.id)
        if (origin_company) {
          const check_keys = ['company_id', 'position', 'end_date', 'is_current', 'start_date']
          check_keys.forEach(key => {
            if (data[key] !== origin_company[key]) {
              flag = true
            }
          })
        } else {
          flag = true
        }
        return flag
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.display .el-form-item {
  // width: 40%;
  margin-bottom: 10px;
}

.el-form-item ::v-deep label {
  font-weight: 400;
}

.el-form-item ::v-deep .el-form-item__label,
.el-form-item ::v-deep .el-form-item__content {
  line-height: 20px;
}

::v-deep .form-item {
  display: inline-block;
  margin-bottom: 5px;

  > .el-form-item__content {
    margin-left: 0;
  }
}

</style>
