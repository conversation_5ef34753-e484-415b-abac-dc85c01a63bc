<template>
  <div v-loading="loading" v-bind="$attrs" style="word-break: break-all;min-height: 100px">
    <div v-if="task_item">
      <el-row :gutter="15" class="w-100 mb-8">
        <el-col v-if="task.angle_id" :sm="12">
          <edit-client-portal-status :task.sync="task_item" @save="save" />
        </el-col>
        <el-col :sm="task.angle_id ? 12 : 24">
          <advisor-task :task.sync="task_item" :type="type" @save="save" @update="reloadList" />
        </el-col>
      </el-row>
      <el-row :gutter="15" class="w-100 mb-8">
        <el-col :sm="12">
          <edit-contact-information :task.sync="task_item" @save="save" />
        </el-col>
        <el-col :sm="12">
          <edit-employment-history :task.sync="task_item" @save="save" />
        </el-col>
      </el-row>
      <physician-information v-if="task_item.angle && task_item.angle.tags.includes('PHYSICIAN')" :task.sync="task_item" />
      <edit-background class="w-100 mb-8" :task.sync="task_item" @save="save" />
      <edit-sq class="edit-sq" :task.sync="task_item" @save="save" @update="reloadList" />
      <outsource-sq-comments v-if="task.task_outsource_status === 'IMPORTED'" :task.sync="task_item" class="mt-8" />
    </div>
  </div>
</template>

<script>
import EditClientPortalStatus from './EditClientPortalStatus'
import EditContactInformation from './EditContactInformation'
import AdvisorTask from './AdvisorTask'
import EditBackground from './EditBackground'
import EditEmploymentHistory from './EditEmploymentHistory'
import EditSq from './EditSq'
import ProjectAPI from '@/api/project'
import OutsourceSqComments from '@/views/project/consultation/detail/components/EditTask/OutsourceSqComments.vue'
import PhysicianInformation from '@/views/project/consultation/detail/components/EditTask/PhysicianInformation.vue'
import { mapState } from 'vuex'

export default {
  name: 'EditTask',
  components: {
    EditClientPortalStatus,
    EditContactInformation,
    AdvisorTask,
    EditBackground,
    EditEmploymentHistory,
    EditSq,
    OutsourceSqComments,
    PhysicianInformation,
  },
  props: {
    task: {
      type: Object,
      default() {
        return {}
      },
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      task_item: null,
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
    isLeads() {
      return this.type === 'LEAD'
    },
  },
  watch: {
    'task': {
      handler(newVal) {
        if (newVal) {
          this.getTaskDetail()
        }
      },
    },
  },
  mounted() {
    this.getTaskDetail()
  },
  methods: {
    getTaskDetail() {
      const params = {
        project_id: this.task.project_id,
        id: this.task.id,
        params: {
          extra: [
            'schedule',
            'angle.inquiry_branch_id',
            'advisor_schedules',
            'contact_schedules',
            'advisor.location',
            'advisor_profile',
            'advisor.jobs.company',
            'advisor.contact_infos',
            'advisor.current_job',
            'advisor.latest_signed_tc',
            'advisor.advisor_npi_registry',
            'client_selection_tag_refs.tag',
            'create_user',
            'entity_update_logs.create_by',
            'latest_submitted_sq',
            'latest_sq',
            'task_outsource_info',
            'task_client_chaperones.chaperone',
          ].join(),
        },
      }
      this.loading = true
      return ProjectAPI.getTaskDetail(params)
        .then(data => {
          this.task_item = data
        })
        .finally(() => {
          this.loading = false
        })
    },
    save(val) {
      this.$emit('update:task', val)
    },
    reloadList() {
      this.$emit('update')
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .task-edit-title {
  padding: 0 0 8px;
  margin: 0;
  font-size: 14px;
}

::v-deep .el-form-item__label, .el-form-item__content {
  font-size: 13px;
}

.edit-sq {
  width: 86vw;
}
</style>
