<template>
  <div>
    <el-button
      type="success"
      @click="dialogVisible = true"
    >
      To Bcg Template
    </el-button>

    <el-dialog
      :visible.sync="dialogVisible"
      title="To Bcg Template"
      width="75%"
      @open="handleOpen"
    >
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="Send For Approval" name="send" />
        <el-tab-pane label="Send History" name="list" />
      </el-tabs>

      <template v-if="activeTab === 'send'">
        <el-form
          ref="form"
          v-loading="loading"
          :model="form"
          :disabled="submitting"
          label-width="100px"
        >
          <el-form-item label="Template" prop="template" required>
            <el-select
              v-model="form.template"
              value-key="id"
              filterable
              placeholder="Select Template"
              class="w-100"
              @change="handleChangeTemplate"
            >
              <el-option
                v-for="item in options.template_list"
                :key="item.id"
                :label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Expert Types" prop="expert_types" required>
            <el-select v-model="form.expert_types" multiple filterable class="w-300px">
              <el-option
                v-for="item in options.expert_types"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Content">
            <tinymce ref="tinymce_content" v-model="form.content" :toolbar="toolbar" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" :loading="submitting" @click="handleSendBcgTemplate">Send</el-button>
        </span>
      </template>

      <template v-if="activeTab === 'list'">
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
        >
          <el-table-column label="ID" prop="id" width="50" />
          <el-table-column label="Date" width="80">
            <template slot-scope="scope">
              {{ scope.row.update_at | momentFormat() }}
            </template>
          </el-table-column>
          <el-table-column label="Expert Types" prop="expert_types_str" width="200" />
          <el-table-column label="Subject" prop="subject_template" />
          <el-table-column label="Content" prop="content_template" width="70">
            <template v-slot="scope">
              <el-tooltip placement="top" effect="light">
                <div slot="content" v-html="scope.row.content_template" style="max-width: 90vw;" />
                <el-link type="primary" :underline="false">View</el-link>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="Status" prop="status" width="90" />
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">Close</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/tool'
import EmailAPI from '@/api/email'
import { mapState } from 'vuex'
import Tinymce from '@/components/Tinymce/index.vue'

export default {
  name: 'ToBcgTemplate',
  components: { Tinymce },
  data() {
    return {
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      dialogVisible: false,
      loading: false,
      submitting: false,
      activeTab: 'send',
      form: {
        template: null,
        expert_types: [],
        content: '',
      },
      options: {
        expert_types: [],
        template_list: [],
      },

      tableData: [],
    }
  },
  computed: {
    ...mapState({
      project: state => state.project.data,
    }),
  },
  mounted() {
    const expert_type = [...this.project.bcg_hub_project.expert_types]
    this.form.expert_types = expert_type
    this.options.expert_types = expert_type
  },
  methods: {
    handleOpen() {
      this.activeTab = 'send'
      this.$refs['form']?.clearValidate()
      this.getEmailTemplates()
    },
    getEmailTemplates() {
      const params = {
        reference_ids: this.project.id,
        reference_type: 'PROJECT',
        content_type: 'PROJECT_OUTREACH',
        has_permission: true,
        page: 1,
        size: 999,
        include_default_reference_type: true,
        uid: this.$store.state.user.uid,
      }
      this.loading = true
      return EmailAPI.getEmailList(params)
        .then(data => {
          data.list.sort((a, b) => a.rank - b.rank)
          this.options.template_list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleChangeTemplate(val) {
      this.form.content = val?.content_template
      this.$refs['tinymce_content']?.setContent(this.form.content)
    },
    handleSendBcgTemplate() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.submitting = true
          const params = {
            project_id: this.project.id,
            expert_types: this.form.expert_types,
            template: { ...this.form.template, content_template: this.form.content },
          }
          return API.sendTemplateToBcgForApproval(params)
            .then(() => {
              this.$message.success('success')
              this.form.template = null
              this.form.content = ''
              this.$nextTick(() => {
                this.$refs['tinymce_content']?.setContent(this.form.content)
                this.$refs['form']?.clearValidate()
              })
              this.activeTab = 'list'
              this.handleTabClick()
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          return false
        }
      })
    },
    handleTabClick() {
      if (this.activeTab === 'list') {
        this.getToBcgTemplates()
      }
    },
    getToBcgTemplates() {
      this.loading = true
      return API.getToBcgTemplates({ project_id: this.project.id })
        .then(data => {
          this.tableData = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>
::v-deep .el-dialog {
  margin-bottom: 5vh;

  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
