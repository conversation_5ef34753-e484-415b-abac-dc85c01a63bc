<template>
  <div v-if="isUSGovernmentCurrentEmployer.length > 0"
       :class="governmentType === 'GOVERNMENT_US_STATE_OR_LOCAL'?'yellow-text':'red-text'"
       class="inline-block">
    <el-icon name="s-flag" />
    <span>{{ governmentType|getLabel(government_options) }}</span>
  </div>
</template>

<script>
export default {
  name: 'USGovernmentMark',
  props: { advisor: Object },
  data() {
    return {
      government_options: [
        { label: 'G-USF', value: 'GOVERNMENT_US_FEDERAL' },
        { label: 'G-USSL', value: 'GOVERNMENT_US_STATE_OR_LOCAL' },
        { label: 'G-NUS', value: 'GOVERNMENT_NON_US' }],
    }
  },
  computed: {
    isUSGovernmentCurrentEmployer() {
      if (this.advisor.jobs.length < 1) {
        return []
      } else {
        return this.advisor.jobs.filter(
          item => item.is_current && item.company &&
            ['GOVERNMENT_US_FEDERAL', 'GOVERNMENT_US_STATE_OR_LOCAL', 'GOVERNMENT_NON_US'].includes(
              item.company.classification))
      }
    },

    governmentType() {
      if (this.isUSGovernmentCurrentEmployer.length > 0) {
        const GovernmentCurrentEmployer = this.isUSGovernmentCurrentEmployer[0].company
        return GovernmentCurrentEmployer.classification
      } else {
        return null
      }
    },
  },
}
</script>

<style scoped>
.yellow-text {
  color: #c5a52c;
}

.red-text {
  color: #df3c3c;
}
</style>
