/**
 * show-dialog-mixins.js
 * 2020/6/2
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      pageData: undefined,
    }
  },
  methods: {
    show(params) {
      this.dialogVisible = true
      this.isLoading = true
      this.pageData = undefined

      return this.loadData(params)
        .then(data => {
          this.pageData = data
        })
        .finally(() => {
          this.isLoading = false
        })
    },
  },
}
