<template>
  <div v-if="data">
    <el-popover
class="popover-container"
                placement="left"
                width="240"
                trigger="hover"
                transition="none"
                :append-to-body="true"
                :open-delay="300">

      <!-- Progress Bar -->
      <div slot="reference">
        <div class="progress-container flex">
          <div
v-for="(item, index) in progress_steps"
               :key="index"
               class="progress-item"
               :class="item.status | statusClass" />
        </div>
      </div>

      <!-- Popover Panel -->
      <div class="progress-list">
        <div class="progress-detail">
          <div style="font-size: 16px;">
            Task Progress
          </div>
          <div>
            <table class="w-100">
              <tbody>
                <tr
v-for="(item, index) in progress_steps_flat"
                    :key="index"
                    :class="{'row-title': !item.detail, 'row-data': item.detail}">

                  <!-- Main Step -->
                  <td
v-if="!item.detail"
                      colspan="2">
                    <div class="step-name">
                      {{ item.name }}
                      <el-link
v-if="item.name !== 'Vetting Call Status'"
                               type="default"
                               class="step-link"
                               @click="goToDetail(item.name)">
                        Detail...
                      </el-link>
                    </div>
                  </td>
                  <!-- Step Detail -->
                  <slot v-if="item.detail">
                    <td
v-if="item.name === 'Decline Reason'"
                        colspan="2"
                        class="step-name">
                      <div :class="item.detail.status | detailStatusClass">
                        {{ data.advisor_decline.reason }}
                      </div>
                    </td>
                    <slot v-else>
                      <td
v-if="item.detail"
                          class="step-name">
                        {{ item.name }}
                      </td>
                      <td
v-if="item.detail"
                          class="step-status">
                        <div>
                          <slot v-if="['Client Requested','Scheduled','Completed','Consent to Record'].includes(item.name)">
                            <el-dropdown
                              placement="top-end"
                              trigger="click"
                              :class="item.detail.status | detailStatusClass"
                              @command="(val) => changeTaskVettingCallStatus(val, item.name)"
                            >
                              <span class="dropdown-link">
                                {{ item.detail.status | SSTStatusText }}
                              </span>
                              <el-dropdown-menu slot="dropdown" :append-to-body="false" class="vetting-call-dropdown">
                                <el-dropdown-item
                                  :disabled="VettingCallStatusDisabled(item)"
                                  :command="true">
                                  Yes
                                </el-dropdown-item>
                                <el-dropdown-item
                                  :disabled="VettingCallStatusDisabled(item)"
                                  :command="false">
                                  No
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </slot>
                          <slot v-else-if="['Compliance Approved Transcript / Recording'].includes(item.name)">
                            <el-dropdown
                              placement="top-end"
                              trigger="click"
                              :class="item.detail.status | detailStatusClass"
                              @command="(val) =>changeTaskAssetComplianceApproved(val, item.name)"
                            >
                              <span class="dropdown-link">
                                <span v-if="item.detail.status===4">Declined</span>
                                <span v-else>{{ item.detail.status | detailStatusText }}</span>
                              </span>
                              <el-dropdown-menu slot="dropdown" :append-to-body="false" class="dropdown-menu-76px">
                                <el-dropdown-item
                                  :command="true">
                                  Approved
                                </el-dropdown-item>
                                <el-dropdown-item
                                  :command="false">
                                  Declined
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </slot>
                          <slot v-else-if="item.name !== 'Client Agreement' || isSurveyLeads">
                            <span v-if="item.name==='Outreach'&& item.detail.status===4">Declined</span>
                            <span v-else>{{ item.detail.status | detailStatusText }}</span>
                          </slot>
                          <slot v-else>
                            {{ item.detail.status | caStatusText }}
                            <span v-if="item.detail.status === 6 && data[item.detail.type].files.length > 0">
                              <download-file :file-list="data[item.detail.type].files" />
                            </span>
                            <span v-if="item.detail.status === 8">
                              Overridden
                            </span>
                          </slot>
                        </div>
                      </td>
                    </slot>
                  </slot>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </el-popover>

    <event-drawer v-if="drawer_show" ref="drawer" :data="data" :advisor-decline.sync="data.advisor_decline" @update="$emit('update')" />
  </div>
</template>

<script>
/*
Status
==========
                          黑0     黄1   绿2        绿3       红4
TC                        INITIAL SENT  SIGNED
CA                        INITIAL SENT  RESPONDED
SQ                        INITIAL SENT  RESPONDED
CLIENT_COMPLIANCE         INITIAL SENT            APPROVED  REJECTED
CLIENT_CONTACT            INITIAL SENT            APPROVED  REJECTED
ADVISOR_SCHEDULE          INITIAL SENT  RESPONDED
CLIENT_CONTACT_SCHEDULE   INITIAL SENT  RESPONDED
ADVISOR_FEEDBACK          INITIAL SENT  RESPONDED
CLIENT_CONTACT_FEEDBACK   INITIAL SENT  RESPONDED

status:
    Common           CA
  0 INITIAL
  1 SENT             AUTO_HOLD
  2 RESPONDED
  3 APPROVED         AUTO_PASSED
  4 REJECTED         AUTO_REJECTED
  5 DIRECT APPROVED
  6                  UPLOADED
*/

import EventDrawer from '@/views/project/consultation/detail/components/task-progress/components/EventDrawer'
import DownloadFile from '@/components/DownloadList/index'
import moment from 'moment'
import ProjectAPI from '@/api/project'
import { mapState } from 'vuex'

export default {
  name: 'TaskProgressV2',
  components: { EventDrawer, DownloadFile },
  filters: {
    statusClass(status) {
      return ['', 'warning', 'success', 'success', 'danger', 'success', 'success', 'primary'][status]
    },
    /*
     * 显示文字对应状态
     * '-',     'In progress',  'OK',     'Approved', 'Rejected',       'Approved',   'N/A'            'Not Match',
     * INITIAL  SENT            SIGNED    APPROVED    DIRECT_APPROVED   REJECTED      NOT_APPLICABLE    RESPONDED && !contains_matched_schedule
     *          HOLD            RESPONDED
     */
    detailStatusText(status) {
      return ['-', 'In progress', 'OK', 'Approved', 'Rejected', 'Approved', 'N/A', '', 'Selected', 'Not Match'][status]
    },
    SSTStatusText(status) {
      return ['-', '', 'Yes', '', 'No'][status]
    },
    detailStatusClass(status) {
      return ['', 'warning', 'success', 'success', 'danger', 'success', '', 'warning', 'success', 'warning'][status]
    },
    caStatusText(status) {
      return ['-', 'In progress', '', 'PASS', 'FAIL', '', 'UPLOADED', 'HOLD'][status]
    },
  },
  props: {
    data: {
      type: Object,
      default: undefined,
    },
    project: {
      type: Object,
      default: undefined,
    },
    taskType: {
      type: String, // Accept: tasks / leads
      default: undefined,
    },
    // SellSideTeleconference 类型task 多一个进度块(Vetting Call Status)
    isSellSideTeleconference: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      progress_steps: [],
      progress_steps_flat: [],
      drawer_show: false,
    }
  },

  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    isSurveyLeads() {
      return this.project.sub_type === 'Survey' && this.taskType === 'leads'
    },
  },
  watch: {
    'data': {
      handler(newVal) {
        this.analyzeData(newVal)
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    analyzeData(data) {
      const task_advisor_declined = data.advisor_decline?.is_valid
      const progress_steps = [
        {
          name: 'Outreach',
          detail: [
            { name: 'Outreach', status: getOutreachStatus(data) },
          ],
        },
        {
          name: 'Consultant',
          detail: [],
        },
        {
          name: 'Client',
          detail: [
            { name: 'Contact Select', status: getStatus('CLIENT_CONTACT', data) },
            { name: 'Client Contact Schedule', status: getStatus('CLIENT_CONTACT_SCHEDULE', data) },
          ],
        },
        {
          name: 'Compliance',
          detail: [
            { name: 'Capvision Approve', status: getStatus('CAPVISION_COMPLIANCE', data) },
          ],
        },
        {
          name: 'Arrange',
          detail: [
            { name: 'Task Arrange', status: getArrangeStatus(data) },
          ],
        },
        {
          name: 'Complete',
          status: 0,
          detail: [
            { name: 'Task Complete', status: getCompleteStatus(data) },
            { name: 'Advisor Payment Confirm', status: getStatus('advisor_payment_confirm', data) },
            {
              name: 'Client Agreement',
              status: getCaStatus(data, 'latest_submitted_post_ca'),
              type: 'latest_submitted_post_ca',
            },
            // 暂时移除
            // { name: 'Client Feedback', status: getStatus('CLIENT_CONTACT_FEEDBACK', data) },
          ],
        },
      ]

      if (this.isSellSideTeleconference) {
        const VettingCallStatus = {
          name: 'Vetting Call Status',
          detail: [
            { name: 'Client Requested', status: getVettingCallStatus('client_requested', data) },
            { name: 'Scheduled', status: getVettingCallStatus('scheduled', data) },
            { name: 'Completed', status: getVettingCallStatus('completed', data) },
          ],
        }
        progress_steps.splice(2, 0, VettingCallStatus)
      }

      // set "Consultant" details
      const consultant_details = getStepFromProgress(progress_steps, 'consultant').detail

      if (this.taskType === 'leads') {
        consultant_details.push({ name: 'Terms & Conditions', status: getStatus('TC', data) })
      }

      if (this.project['sq_id']) {
        consultant_details.push({ name: 'Screening Question', status: getStatus('SQ', data) })
      }
      const client_has_pre_call_ca = Array.isArray(this.project.ca_list) && this.project.ca_list.length > 0
      const task_ca_status = getStatus('CA', data)
      if ((client_has_pre_call_ca || task_ca_status !== 0) && data?.latest_submitted_pre_ca?.inquiry_instance) {
        consultant_details.push({
          name: 'Client Agreement',
          status: getCaStatus(data, 'latest_submitted_pre_ca'),
          type: 'latest_submitted_pre_ca',
        })
      }

      consultant_details.push({ name: 'Advisor Schedule', status: getStatus('ADVISOR_SCHEDULE', data) })

      // survey 项目 leads 进度条 CA/AG 展示为’N/A'
      if (this.isSurveyLeads) {
        consultant_details.forEach(item => {
          if (['Client Agreement', 'Advisor Schedule'].includes(item.name)) {
            item.status = 6
          }
        })
      }

      // set "Compliance" details
      const compliance_details = getStepFromProgress(progress_steps, 'compliance').detail
      const client_compliance_preference = this.project.client.compliance_preference

      let flag
      if (data.capvision_compliance_status === 'INITIAL') {
        flag = this.project.client.compliance_preference && this.project.client.compliance_preference.approve_policy !==
          'NO_NEED'
      } else {
        flag = data.compliance_approve_policy_jit && data.compliance_approve_policy_jit !== 'NO_NEED'
      }
      if (flag) {
        compliance_details.push({ name: 'Client Approve', status: getStatus('CLIENT_COMPLIANCE', data) })
      }

      const client_rules = client_compliance_preference?.rule?.compliance_rules
      const twilio_asset_need_client_compliance_review_first = client_rules && client_rules.transcript_notetaking_rules.master_switch &&
        client_rules.transcript_notetaking_rules.is_require_compliance_review_of_recordings_and_transcripts_first

      if (twilio_asset_need_client_compliance_review_first) {
        compliance_details.push({ name: 'Compliance Approved Transcript / Recording', status: getStatus('asset_portal_link_approved', data) })
      }

      if (!this.isSGDB) {
        compliance_details.push({ name: 'Consent to Record', status: getStatus('advisor_record_consent', data) })
      }

      // "Complete" dynamic steps
      // const complete_details = getStepFromProgress(progress_steps, 'complete').detail

      // TODO 无法知晓 Task 是否有 "Post-call CA" 步骤
      // { name: 'Post-call CA', status: getStatus('post_ca', data) },
      // { name: 'Advisor Payment Confirm', status: getStatus('advisor_payment_confirm', data) },
      // if (taskHasEvent(this.data.events, 'ADVISOR_PAYMENT_CONFIRM_SEND')) {
      //   complete_details.splice(-2, 0, {
      //     name: 'Advisor Payment Confirm',
      //     status: getStatus('advisor_payment_confirm', data)
      //   })
      // }

      // set overall status
      progress_steps.forEach(item => {
        item.status = getOverallStatus(item)
      })

      if (this.taskType === 'tasks') {
        progress_steps.splice(0, 1, {
          name: 'Outreach',
          status: task_advisor_declined ? 4 : 2,
          detail: [{ name: 'Outreach', status: task_advisor_declined ? 4 : 2 }],
        })
      }

      // 如果专家在邮件中点击 decline，则在进度条中的第一块显示红色，并展示decline reason
      if (task_advisor_declined) {
        progress_steps.forEach(item => {
          if (item.name === 'Outreach') {
            item.detail.push({ name: 'Decline Reason', status: 4, detail: [{ name: 'Decline Reason', status: 4 }] })
          }
        })
      }

      // survey 项目 leads 进度条 第二个颜色取决于 TC状态
      if (this.isSurveyLeads) {
        progress_steps.forEach(item => {
          if (item.name === 'Consultant') {
            item.status = item.detail.find(i => i.name === 'Terms & Conditions').status
          }
        })
      }

      this.progress_steps = progress_steps
      this.progress_steps_flat = this.getStepsV2(progress_steps)

      // 如果专家在邮件中点击 decline，则在进度条中的第一块显示红色
      function getOutreachStatus(data) {
        if (task_advisor_declined) {
          return 4
        } else {
          return data['has_outreached'] ? 2 : 0
        }
      }

      function getVettingCallStatus(key, data) {
        if (!data.investor_call) {
          return 0
        } else {
          return data.investor_call[key] ? 2 : 4
        }
      }

      function getArrangeStatus(data) {
        const status = ['ARRANGED', 'COMPLETED']
        if (status.includes(data['general_status'])) {
          return 2
        } else if (data['general_status'] === 'SCHEDULED') {
          return 1
        } else {
          return 0
        }
      }

      function getCompleteStatus(data) {
        return data['general_status'] === 'COMPLETED' ? 2 : 0
      }

      function getCaStatus(data, key) {
        const status = getStatus(key === 'latest_submitted_pre_ca' ? 'CA' : 'POST_CA', data)
        if (status === 2) {
          if (['EMAIL', 'EXTERNAL'].includes(data[key]?.method)) {
            return 6
          } else if (data[key]?.method === 'OVERRIDDEN') {
            return 8
          } else {
            switch (data[key]?.inquiry_instance.status) {
              case 'AUTO_PASSED':
                return 3
              case 'AUTO_REJECTED':
                return 4
              default:
                return 7
            }
          }
        } else {
          return status
        }
      }

      function getStatus(key, data) {
        const status_key = `${key}_status`.toLowerCase()
        // const status_value = data[status_key].replace(`${key.toUpperCase()}_`, '')
        let status_value = data[status_key]
        const status_to_id = {
          INITIAL: 0,
          SENT: 1,
          SIGNED: 2,
          RESPONDED: 2,
          HOLD: 1,
          APPROVED: 3,
          DIRECT_APPROVED: 5,
          REJECTED: 4,
          NOT_APPLICABLE: 6,
          NOT_MATCH: 9,
        }

        // Client Select 特殊处理
        if (key === 'CLIENT_CONTACT') {
          status_to_id['APPROVED'] = 8
        }

        // CLIENT_CONTACT_SCHEDULE 特殊处理
        if (key === 'CLIENT_CONTACT_SCHEDULE' && status_value === 'RESPONDED' && !data.contains_matched_schedule) {
          status_value = 'NOT_MATCH'
        }

        // survey 项目 leads list  如果leads的survey tc 已签署且未过期 =》绿色

        if (key === 'TC' && data.project_sub_type === 'Survey') {
          const survey_tc_signed = data.advisor.tc_term_valid.SURVEY
          const survey_tc_note_expired = data.advisor.survey_tc_expire_at &&
            moment(data.advisor.survey_tc_expire_at).isAfter()

          if (survey_tc_signed && survey_tc_note_expired) return 2
        }

        if (key === 'asset_portal_link_approved') {
          if (typeof data.asset_portal_link_approved === 'boolean') {
            return data.asset_portal_link_approved ? 3 : 4
          } else {
            return 0
          }
        }

        if (key === 'advisor_record_consent') {
          if (!data.advisor_record_consent) {
            return 0
          } else {
            return data.advisor_record_consent.is_consent ? 2 : 4
          }
        }

        return status_to_id[status_value]
      }

      function getOverallStatus(data) {
        // Vetting Call Status:The progress bar color for this should be Blue if Client Requested, Orange if Scheduled, and Green if Completed
        // ['', 'warning', 'success', 'success', 'danger', 'success', 'success', 'primary'][status]
        if (data.name === 'Vetting Call Status') {
          if (data.detail.find(item => item.name === 'Completed').status === 2) {
            return 3
          } else if (data.detail.find(
            item => item.name === 'Scheduled').status === 2) {
            return 1
          } else if (data.detail.find(item => item.name === 'Client Requested').status === 2) {
            return 7
          } else {
            return 0
          }
        }

        // screening questions submitted=>蓝色；&& availability submitted => 绿色
        // https://www.notion.so/capvision/View-Project-New-screening-questions-submitted-color-in-progress-bar-bdea4e0f9f764bbead0ce4d3cea3de19?pvs=4
        if (data.name === 'Consultant') {
          const SQ = data.detail.find(item => item.name === 'Screening Question')
          const SQ_submitted = SQ && SQ.status === 2
          const AG_submitted = data.detail.find(
            item => item.name === 'Advisor Schedule').status === 2
          if (SQ_submitted) {
            return AG_submitted ? 3 : 7
          }
        }

        let detail_list = data.detail

        // 这项只关系到 会议的录音、速记 是否需要审核，不参与task compliance 的状态
        if (data.name === 'Compliance') {
          detail_list = data.detail.filter(item => item.name !== 'Compliance Approved Transcript / Recording')
          detail_list = data.detail.filter(item => item.name !== 'Consent to Record')
        }

        // all not start
        if (detail_list.every(item => item.status === 0)) {
          return 0
        }

        // any Rejected
        if (detail_list.some(item => item.status === 4)) {
          return 4
        }

        // any direct approved
        if (detail_list.some(item => item.status === 5)) {
          return 5
        }

        // has in progress step / CA in HOLD status / Schedule Not Match
        if (detail_list.some(item => [1, 7, 9].includes(item.status))) {
          return 1
        }

        // mixed status
        if (detail_list.some(item => item.status !== 0) && detail_list.some(item => item.status === 0)) {
          return 1
        }

        // all done!
        return 2
      }

      /**
       * 从流程中获取指定步骤
       */
      function getStepFromProgress(list, name) {
        return list.filter(item => {
          return item.name.toLowerCase() === name.toLowerCase()
        })[0]
      }

      // /**
      //  * Task 是否经历指定状态
      //  */
      // function taskHasEvent(events, name) {
      //   return events.some(item => {
      //     return item.type === name
      //   })
      // }
    },

    goToDetail(name) {
      this.drawer_show = true
      setTimeout(() => {
        const drawer = this.$refs.drawer

        drawer.drawerTitle = name
        drawer.drawerShow()
      }, 0)
    },

    /**
     * 扁平化步骤数据
     * @param progress_steps
     * @returns {*[]}
     */
    getStepsV2(progress_steps) {
      const steps = []

      progress_steps.forEach(step => {
        /* step - title */
        steps.push({
          name: step.name,
          status: step.status,
          type: step.type,
        })

        /* step - detail */
        step.detail.forEach((item, index) => {
          steps.push({
            name: item.name,
            status: item.status,
            type: item.type,
            detail: step.detail[index],
          })
        })
      })

      return steps
    },

    VettingCallStatusDisabled(item) {
      if (item.name === 'Client Requested') {
        return this.data.investor_call?.scheduled || this.data.investor_call?.completed
      }
      if (item.name === 'Scheduled') {
        return !this.data.investor_call?.client_requested || this.data.investor_call?.completed
      }
      if (item.name === 'Completed') {
        return !this.data.investor_call?.client_requested || !this.data.investor_call?.scheduled
      }
    },

    changeTaskVettingCallStatus(val, item_name) {
      if (item_name === 'Consent to Record') {
        this.changeTaskAdvisorConsentToRecord(val)
        return
      }
      const investor_call = {
        'id': this.data.investor_call?.id,
      }
      const key = item_name.replace(/(?:^|\s)\S/g, a => a.toLowerCase()).replace(/ /g, '_')
      investor_call[key] = val

      if (key === 'scheduled' && !val) {
        return ProjectAPI.cancelVettingCall({ task_id: this.data.id })
          .then(() => {
            this.data.investor_call = Object.assign(this.data.investor_call, { scheduled: false })
            this.$emit('update')
          })
      } else {
        const params = Object.assign({
          project_id: this.data.project_id,
          id: this.data.id,
          investor_call,
        })

        return ProjectAPI.updateTask(params).then(data => {
          this.data.investor_call = data.investor_call
          this.$emit('update')
        })
      }
    },
    changeTaskAssetComplianceApproved(val) {
      const params = {
        project_id: this.data.project_id,
        id: this.data.id,
        asset_portal_link_approved: val,
      }

      return ProjectAPI.updateTask(params).then(data => {
        this.data.asset_portal_link_approved = data.asset_portal_link_approved
        this.$emit('update')
      })
    },
    changeTaskAdvisorConsentToRecord(val) {
      const params = {
        is_consent: val,
      }

      return ProjectAPI.updateAdvisorRecordingStatus(this.data.id, params).then(data => {
        this.data.advisor_record_consent = data
        this.$emit('update')
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.popover-container {
  flex: 1;

  ::v-deep .progress-container {
    max-width: 400px;

    &:hover {
      box-shadow: 0 0 0 3px #4444441c;
      border-radius: 2px;
    }

    .progress-item {
      flex: 1;
      background-color: #dadce0;
      border-radius: 1px;
      height: 8px;
      margin: 1px;

      &.primary {
        background-color: #1a73e8;
      }

      &.warning {
        background-color: #f1b238;
      }

      &.danger {
        background-color: #e73e3e;
      }

      &.success {
        background-color: #61c82d;
      }
    }
  }
}

.progress-list {
  font-size: 12px;

  .progress-detail {

    .row-title {
      .step-name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1px 5px;
        background-color: #ececec;
        font-weight: bold;
        border-radius: 3px;
        margin-top: 14px;
      }

      .step-link {
        zoom: 0.8;

        &:not(:hover) {
          color: #999;
        }
      }
    }

    .row-data {
      .step-name {
        padding-left: 1em;
        letter-spacing: -0.3px;
      }
    }

    .step-status {
      text-align: right;

      &.warning {
        color: #e6a23c;
      }

      &.danger {
        color: #d40000;
      }

      &.success {
        color: #67c23a;
      }
    }
  }
}

.dropdown-link {
  cursor: pointer;
  white-space: nowrap;
}

.vetting-call-dropdown {
  width: 48px;
  top: -60px !important;
  // 配合 append-to-body 使 dropdown和 popover 都能符合需求 正常使用
  ::v-deep .popper__arrow {
    display: none;
  }
}

.dropdown-menu-76px {
  width: 76px;
  top: -60px !important;
  // 配合 append-to-body 使 dropdown和 popover 都能符合需求 正常使用
  ::v-deep .popper__arrow {
    display: none;
  }
}
</style>
