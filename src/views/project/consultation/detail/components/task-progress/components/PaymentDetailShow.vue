<template>
  <el-dialog
    title="Payment Detail"
    :visible.sync="dialogVisible">
    <loading-status :status="isLoading" :data="pageData" />

    <div v-if="pageData">
      <el-form label-width="160px">
        <el-form-item label="Consultation Duration">
          {{pageData.hours * 60}} Mins
        </el-form-item>
        <el-form-item label="Hourly Rate">
          {{pageData.rate}} {{pageData.currency}}/Hour
        </el-form-item>
        <el-form-item label="Total Payment">
          {{pageData.amount}} {{pageData.currency}}
        </el-form-item>

        <el-divider />

        <el-form-item label="Payee Type">
          {{pageData.receiver_type | getName(options.payment_receiver_type_list)}}
        </el-form-item>
        <el-form-item label="Account Holder Name" prop="account_name">
          {{pageData.account_name}}
        </el-form-item>
        <el-form-item label="Bank Name" prop="bank_name">
          {{pageData.bank_name}}
        </el-form-item>
        <el-form-item label="Bank Country" prop="bank_area">
          {{pageData.bank_area | getName(options.country_list)}}
        </el-form-item>
        <el-form-item
          v-if="display.account_number"
          key="account_number"
          :label="displayNameOfAccountNumber"
          prop="account_number">
          {{pageData.account_number}}
        </el-form-item>
        <el-form-item v-if="display.routing_number" key="routing_number" label="Routing Number" prop="routing_number">
          {{pageData.routing_number}}
        </el-form-item>
        <el-form-item v-if="display.account_type" key="account_type" label="Account Type" prop="account_type">
          {{pageData.account_type | getName(options.account_type)}}
        </el-form-item>
        <el-form-item v-if="display.transit_number" key="transit_number" label="Transit Number" prop="transit_number">
          {{pageData.transit_number}}
        </el-form-item>
        <el-form-item v-if="display.swift_code" key="swift_code" label="SWIFT Code" prop="swift_code">
          {{pageData.swift_code}}
        </el-form-item>
        <el-form-item v-if="display.sort_code" key="sort_code" label="Sort Code" prop="sort_code">
          {{pageData.sort_code}}
        </el-form-item>
        <el-form-item v-if="display.cpf_code" key="cpf_code" label="CPF Code" prop="cpf_code">
          {{pageData.cpf_code}}
        </el-form-item>
        <el-form-item v-if="display.ifsc_code" key="ifsc_code" label="IFSC Code" prop="ifsc_code">
          {{pageData.ifsc_code}}
        </el-form-item>
        <el-form-item v-if="display.cmnd_code" key="cmnd_code" label="CMND Code" prop="cmnd_code">
          {{pageData.cmnd_code}}
        </el-form-item>
        <el-form-item v-if="display.tax_id_code" key="tax_id_code_code" label="Tax ID Code" prop="tax_id_code_code">
          {{pageData.tax_id_code}}
        </el-form-item>
        <el-form-item v-if="display.birthday" key="birthday" label="Date Of Birth" prop="tax_id_code_code">
          {{pageData.tax_id_code}}
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dialogVisible = false">OK</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ProjectAPI from '@/api/project'
import ShowDialogMixins from '../show-dialog-mixins'
import LoadingStatus from '@/components/LoadingStatus'

const LOCATION_ARGENTINA = 'ARGENTINA'
const LOCATION_AUSTRALIA = 'AUSTRALIA'
const LOCATION_BRAZIL = 'BRAZIL'
const LOCATION_CANADA = 'CANADA'
const LOCATION_CHINA = 'CHINA'
const LOCATION_INDIA = 'INDIA'
const LOCATION_MEXICO = 'MEXICO'
const LOCATION_PERU = 'PERU'
const LOCATION_RUSSIA = 'RUSSIA'
const LOCATION_SINGAPORE = 'SINGAPORE'
const LOCATION_SOUTH_KOREA = 'SOUTH_KOREA'
const LOCATION_UK = 'UK'
const LOCATION_US = 'US'
const LOCATION_VIETNAM = 'VIETNAM'
const LOCATION_REST_OF_WORLD = 'REST_OF_WORD'

export default {
  name: 'PaymentDetailShow',
  components: { LoadingStatus },
  mixins: [ShowDialogMixins],
  data() {
    return {
      options: {
        identifier_type: [
          {
            id: 1,
            name: 'SWIFT BIC (International)',
          }, {
            id: 2,
            name: 'CHIPS ABA (International)',
          }, {
            id: 3,
            name: 'ROUTING NUMBER (US Accounts Only)',
          }, {
            id: 4,
            name: 'SORT CODE (International)',
          },
        ],
        payment_receiver_type_list: [
          { id: 'ONESELF', name: 'Consultant (Self)' }, // 专家本人
          { id: 'ANOTHER_PERSON', name: 'Others (Third Party)' }, // 他人
          { id: 'CORPORATION', name: 'Company (Public Accounts)' }, // 机构, 公司
        ],
        country_list: [
          { id: LOCATION_ARGENTINA, name: 'Argentina' },
          { id: LOCATION_AUSTRALIA, name: 'Australia' },
          { id: LOCATION_BRAZIL, name: 'Brazil' },
          { id: LOCATION_CANADA, name: 'Canada' },
          { id: LOCATION_CHINA, name: 'China (Mainland)' },
          { id: LOCATION_INDIA, name: 'India' },
          { id: LOCATION_MEXICO, name: 'Mexico' },
          { id: LOCATION_PERU, name: 'Peru' },
          { id: LOCATION_RUSSIA, name: 'Russia' },
          { id: LOCATION_SINGAPORE, name: 'Singapore' },
          { id: LOCATION_SOUTH_KOREA, name: 'South Korea' },
          { id: LOCATION_UK, name: 'UK' },
          { id: LOCATION_US, name: 'United States' },
          { id: LOCATION_VIETNAM, name: 'Vietnam' },
          { id: LOCATION_REST_OF_WORLD, name: 'Rest of world' },
        ],
        account_type: [
          { id: 'SAVING', name: 'SAVING' },
          { id: 'CHECKING', name: 'CHECKING' },
        ],
      },
      display: {
        account_number: false,
        account_type: false,
        routing_number: false,
        transit_number: false,
        swift_code: false,
        sort_code: false,
        cpf_code: false,
        ifsc_code: false,
        payee_mobile: false,
        tax_id_code: false,
        birthday: false,
      },
    }
  },
  computed: {
    // Receiver Type 为 "Others" 时，需要填写手机号
    isPayeeMobileDisplay() {
      return this.pageData?.receiver_type === 'ANOTHER_PERSON'
    },
    /*
     * Account Number for US & SINGAPORE
     * IBAN for Europe
     * Account Number / IBAN for others
     */
    displayNameOfAccountNumber() {
      let output

      switch (this.pageData.bank_area) {
        case LOCATION_US:
        case LOCATION_SINGAPORE:
        case LOCATION_CHINA:
          output = 'Account Number'
          break
        case LOCATION_UK:
        case LOCATION_REST_OF_WORLD:
          output = 'IBAN'
          break
        case LOCATION_MEXICO:
          output = 'CLABE'
          break
        case LOCATION_ARGENTINA:
          output = 'CBU'
          break
        case LOCATION_PERU:
          output = 'CCI'
          break
        default:
          output = 'Account Number / IBAN'
      }

      return output
    },
  },
  watch: {
    /**
     * 依据 Bank 所属地区调整填写项目
     */
    'pageData.bank_area': {
      handler(bank_area) {
        const data = {
          bank_branch: false,
          bank_location: false,
          account_number: false,
          account_type: false,
          routing_number: false,
          transit_number: false,
          swift_code: false,
          sort_code: false,
          cpf_code: false,
          ifsc_code: false,
          cmnd_code: false,
          tax_id_code: false,
          birthday: false,
        }

        // 界面内容控制
        switch (bank_area) {
          case LOCATION_AUSTRALIA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_BRAZIL:
            data.account_number = true
            data.swift_code = true
            data.cpf_code = true
            break
          case LOCATION_CANADA:
            data.account_number = true
            data.transit_number = true
            data.swift_code = true
            break
          case LOCATION_CHINA:
            data.bank_branch = true
            data.bank_location = true
            data.account_number = true
            break
          case LOCATION_INDIA:
            data.account_number = true
            data.swift_code = true
            data.ifsc_code = true
            break
          case LOCATION_RUSSIA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_UK:
            data.account_number = true
            data.swift_code = true
            data.sort_code = true
            break
          case LOCATION_US:
            data.account_number = true
            data.account_type = true
            data.routing_number = true
            break
          case LOCATION_VIETNAM:
            data.account_number = true
            data.swift_code = true
            data.cmnd_code = true
            data.birthday = true
            break
          case LOCATION_ARGENTINA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_PERU:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          case LOCATION_SOUTH_KOREA:
            data.account_number = true
            data.swift_code = true
            data.tax_id_code = true
            break
          default:
            data.account_number = true
            data.swift_code = true
            break
        }
        Object.assign(this.display, data)
      },
      immediate: true,
    },
  },
  created() {
    this.display.payee_mobile = this.isPayeeMobileDisplay
  },
  methods: {
    loadData(params) {
      return ProjectAPI
        .getTaskPaymentDetail(params)
    },
  },
}
</script>
