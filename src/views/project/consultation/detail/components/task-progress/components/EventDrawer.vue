<template>
  <div>
    <el-drawer
      :visible.sync="drawer"
      direction="rtl"
      size="50%"
      :destroy-on-close="true"
      :show-close="false"
      :append-to-body="true"
      @open="initFrom"
      @opened="setData"
    >
      <template slot="title">
        <div class="">
          <el-tabs v-model="drawerTitle" type="card" @tab-click="handleTabClick">
            <el-tab-pane label="Outreach" name="Outreach" />
            <el-tab-pane label="Consultant" name="Consultant" />
            <el-tab-pane label="Client" name="Client" />
            <el-tab-pane label="Compliance" name="Compliance" />
            <el-tab-pane label="Arrange" name="Arrange" />
            <el-tab-pane label="Complete" name="Complete" />
          </el-tabs>
        </div>
      </template>
      <div v-loading="drawerLoading">
        <div>
          <div v-if="['Consultant'].includes(drawerTitle)" class="schedule">
            <divider>
              <template #left>
                <span>Schedule Time</span>
              </template>
              <template #right>
                <el-link
                  type="primary"
                  title="Link To Portal"
                  icon="el-icon-time"
                  :underline="false"
                  :disabled="disableScheduleLink"
                  @click="linkToPortal('advisor_schedule')" />
              </template>
            </divider>
            <el-table
              stripe
              fit
              style="width: 100%"
              class="stripe-table"
              :data="consultantSchedules">
              <el-table-column label="Time" min-width="150">
                <template slot-scope="scope">
                  <span>{{
                    scope.row.start_time | momentFormat('MM/DD/YYYY (HH:mm)')
                  }} ~ {{ scope.row.end_time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Time Duration (/h)" min-width="150">
                <template slot-scope="scope">
                  <span>{{
                    ((new Date(scope.row.end_time) - new Date(scope.row.start_time)) / (3600 * 1000)).toFixed(2)
                      .slice(0, -1)
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="['Client'].includes(drawerTitle)" class="schedule">
            <el-divider content-position="left">Schedule Time</el-divider>
            <el-table
              stripe
              fit
              style="width: 100%"
              class="stripe-table"
              :data="contactSchedules">
              <el-table-column label="Time" min-width="150">
                <template slot-scope="scope">
                  <div v-for="(item, index) in scope.row.schedule" :key="index">
                    <span>{{
                      item.start_time | momentFormat('MM/DD/YYYY (HH:mm)')
                    }} ~ {{ item.end_time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="Time Duration (/h)" min-width="100">
                <template slot-scope="scope">
                  <div v-for="(item, index) in scope.row.schedule" :key="index">
                    <span>{{
                      ((new Date(item.end_time) - new Date(item.start_time)) / (3600 * 1000)).toFixed(2).slice(0, -1)
                    }}</span>
                  </div>
                  <!-- <span>{{ ((new Date(scope.row.end_time) - new Date(scope.row.start_time))/(3600*1000)).toFixed(2).slice(0,-1)}}</span> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="['Arrange'].includes(drawerTitle)" class="schedule">
            <el-divider content-position="left">Schedule Time</el-divider>
            <el-table
              stripe
              fit
              style="width: 100%"
              class="stripe-table"
              :data="arrangehSchedules">
              <el-table-column label="Time" min-width="150">
                <template slot-scope="scope">
                  <span>{{
                    scope.row.start_time | momentFormat('MM/DD/YYYY(HH:mm)')
                  }} ~ {{ scope.row.end_time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Time Duration (/h)" min-width="150">
                <template slot-scope="scope">
                  <span>{{
                    ((new Date(scope.row.end_time) - new Date(scope.row.start_time)) / (3600 * 1000)).toFixed(2)
                      .slice(0, -1)
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Dial-In Code" min-width="100">
                <template v-if="scope.row.loopup_room" slot-scope="scope">
                  <span>{{ scope.row.loopup_room.guest_code }}#</span>
                </template>
              </el-table-column>
              <el-table-column label="Client Conference Access Number" min-width="100">
                <template v-if="scope.row.contact_loopup_area_ids" slot-scope="scope">
                  <span>{{ getLabels(scope.row.contact_loopup_area_ids) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Advisor Conference Access Number" min-width="100">
                <template v-if="scope.row.advisor_loopup_area_id" slot-scope="scope">
                  <span>{{ getLabels(scope.row.advisor_loopup_area_id) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="Object.keys(taskDetail).includes('email')">
            <el-divider content-position="left">Email</el-divider>
            <el-table
              stripe
              fit
              style="width: 100%"
              class="stripe-table"
              :data="taskDetail['email']">
              <el-table-column label="Time" min-width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row['create_at'] | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="From" min-width="150">
                <template slot-scope="scope">
                  <span
                    class="link"
                    @click="openToLink('EMAIL', scope.row['email_record']['from'])">{{
                      scope.row['email_record']['from']
                    }}</span>
                </template>
              </el-table-column>
              <el-table-column label="Type" min-width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row['email_content_type'] }}</span>
                </template>
              </el-table-column>
              <el-table-column label="To" min-width="150">
                <template slot-scope="scope">
                  <div
v-for="(detail_item, detail_index) in scope.row['email_record']['to_list_with_mosaic']"
                       :key="detail_index">
                    <span class="link" @click="openToLink('EMAIL', detail_item)">{{ detail_item }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="Subject" min-width="150">
                <template slot-scope="scope">
                  <el-popover
                    class="popover-container"
                    placement="right"
                    trigger="hover"
                    popper-class="subject-popper"
                  >
                    <span slot="reference">{{ scope.row.email_record.subject }}</span>
                    <div class="popover-content" v-html="scope.row.email_record.content" />
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template v-for="(item, key) in taskDetail">
            <div v-if="!['email','client_contact_reviews','advisor_payment_status'].includes(key)" :key="key">
              <divider>
                <template #left>
                  <span>{{ keyToName(key) }}</span>
                </template>
              </divider>
              <el-table
                stripe
                fit
                class="stripe-table"
                :data="item">
                <el-table-column label="Time">
                  <template slot-scope="scope">
                    <span>{{ scope.row['create_at'] | momentFormat('MM/DD/YYYY hh:mm A') }}</span>
                  </template>
                </el-table-column>
                <el-table-column property="type" label="Type" />
                <el-table-column
                  property="target_status"
                  label="">
                  <template slot-scope="scope">
                    <div v-if="['SQ_RESPOND','CA_RESPOND','POST_CA_RESPOND'].includes(scope.row.type)">
                      <div v-if="key === 'screening_question' && taskDetail['screening_question'].length">
                        <i
                          title="Respond Detail"
                          class="link el-icon-tickets"
                          @click="_linkToDetail('screening_question', scope.row.payload_id)" />
                      </div>
                      <div
                        v-if="key === 'compliance_question' && taskDetail['compliance_question'].length">
                        <i
                          title="Respond Detail"
                          class="link el-icon-tickets"
                          @click.stop="_linkToDetail('compliance_question', scope.row.payload_id)" />
                      </div>
                      <div
                        v-if="key === 'post_compliance' && taskDetail['post_compliance'].length">
                        <i
                          title="Respond Detail"
                          class="link el-icon-tickets"
                          @click.stop="_linkToDetail('post_compliance',scope.row.payload_id)" />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="['client_feedback'].includes(key)"
                  property="target_status"
                  label="Status">
                  <template slot-scope="scope">
                    <div v-if="scope.row.type === 'ADVISOR_PAYMENT_CONFIRM_RESPOND'">
                      <el-link type="primary" @click="actionViewAdvisorPaymentConfirmDetail(scope.row)">
                        View...
                      </el-link>
                    </div>
                    <div v-if="scope.row.type === 'CONTACT_FEEDBACK_RESPOND'">
                      <el-link type="primary" @click="actionViewContactFeedback(scope.row)">
                        View...
                      </el-link>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="['client_contact_reviews'].includes(key)" :key="key">
              <el-divider content-position="left">{{ keyToName(key) }}</el-divider>
              <el-table
                stripe
                fit
                style="width: 100%"
                class="stripe-table"
                :data="item">
                <el-table-column label="Time" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row['create_at'] | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Type" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row['type'] }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Status" min-width="150">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.approved">
                      <el-popover
                        class="popover-container"
                        placement="right"
                        width="200"
                        trigger="hover"
                      >
                        <span slot="reference" class="danger">Rejected</span>
                        <div class="popover-content">{{ scope.row.reject_reason ? scope.row.reject_reason : 'No Data' }}
                        </div>
                      </el-popover>
                    </div>
                    <div v-if="scope.row.approved">
                      <span class="success">Approved</span>
                    </div>

                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="['advisor_payment_status'].includes(key)" :key="key">
              <el-divider content-position="left">{{ keyToName(key) }}</el-divider>
              <el-table
                stripe
                fit
                style="width: 100%"
                class="stripe-table"
                :data="item">
                <el-table-column label="Update Time" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row['update_at'] | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Amount/Currency" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row.amount }} / {{ scope.row.currency }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Status" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row.status}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Paid Time" min-width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row['paid_at'] | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </div>
        <div v-if="['Outreach'].includes(drawerTitle)">
          <el-divider content-position="left">Progress</el-divider>
          <el-table
            stripe
            fit
            style="width: 100%"
            class="stripe-table"
            :data="declinedStatus">
            <el-table-column label="Time" min-width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.time | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Status" min-width="250">
              <template slot-scope="scope">
                <slot v-if="scope.row.status">
                  <slot v-if="!new_decline_status">
                    <span>{{ scope.row.status }}</span>
                    <span v-if="scope.row.status==='DECLINED'"> {{ taskData.advisor_decline.reason }}</span>
                    <el-link
:underline="false"
                             type="primary"
                             class="el-icon-edit ml-8 mr-8"
                             @click="editDeclinedStatus(scope.row.status,taskData.advisor_decline.reason)" />
                  </slot>
                  <slot v-else>
                    <el-select v-model="new_decline_status" class="w-120px">
                      <el-option
                        label="DECLINED"
                        value="DECLINED"
                      />
                      <el-option
                        label="OK"
                        value="OK" />
                    </el-select>
                    <el-input
v-if="new_decline_status==='DECLINED'"
                              v-model="new_decline_reason"
                              placeholder="reason"
                              class="w-200px" />
                    <div class="mt-8">
                      <el-button type="success" @click="updateDeclinedStatus(scope.row.status)">Save</el-button>
                      <el-button type="text" @click="editDeclinedStatus(null,null)">Cancel</el-button>
                    </div>
                  </slot>
                </slot>
              </template>
            </el-table-column>
            <el-table-column label="" min-width="150" />
          </el-table>
        </div>
      </div>

    </el-drawer>

    <questionnaire-show :branch-id="instanceId" has-answer @close="instanceId = null" />
    <ca-questionnaire-show :task-ca-id="taskCAId" :task-data="data" :ca-type="currentCaType" :task-id="data.id" @close="taskCAId = null" />

    <payment-detail-show ref="paymentDetailViewer" />
    <client-feedback-show ref="contactFeedbackViewer" />
  </div>
</template>

<script>
import _ from 'lodash'
import Divider from './Divider'
import ProjectAPI from '@/api/project'
import SchedulerAPI from '@/api/scheduler'

import QuestionnaireShow from '@/components/Question/QuestionnaireShowV2'
import CaQuestionnaireShow from '@/components/Question/CAQuestionShow_v2'
import PaymentDetailShow from '@/views/project/consultation/detail/components/task-progress/components/PaymentDetailShow'
import ClientFeedbackShow from '@/views/project/consultation/detail/components/task-progress/components/ClientFeedbackShow'
import { AppHttpService } from '@/utils/request'

export default {
  name: 'EventDrawer',
  components: { ClientFeedbackShow, PaymentDetailShow, Divider, QuestionnaireShow, CaQuestionnaireShow },
  props: {
    data: {
      type: Object,
      default: undefined,
    },
    advisorDecline: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      drawerTitle: '',
      drawer: false,
      drawerLoading: false,
      taskDetail: {},
      consultantSchedules: [],
      contactSchedules: [],
      arrangehSchedules: [],
      declinedStatus: [],
      new_decline_status: null,
      new_decline_reason: null,
      SQShow: false,
      completeTime: '',
      instanceId: 0,
      taskCAId: 0,
      currentCaType: 'pre-call',
      loopUp_location_list: [],
      taskData: {},
    }
  },
  computed: {
    disableScheduleLink() {
      return ['ARRANGED', 'COMPLETED'].includes(this.data['general_status'])
    },
  },

  methods: {
    drawerShow() {
      this.drawer = true
    },
    setData() {
      this.drawerLoading = true
      this.getTaskDetail()
        .then(() => {
          this.getOptions()
          this.getConsultantSchedule()
          this.getContactSchedules()
        })
        .finally(() => {
          this.$nextTick(() => {
            this.drawerLoading = false
          })
        })
    },

    getOptions() {
      return AppHttpService
        .get('/common/loopup/numbers')
        .then(list => {
          this.loopUp_location_list = list
        })
    },

    latestSubmitFromPortal(type) {
      return this.taskData[type] && this.taskData[type].method === 'PORTAL'
    },
    latestSubmitFromUpload(type) {
      return this.taskData[type] && this.taskData[type].method !== 'PORTAL'
    },

    getLabels(list) {
      const list_ids = list.toString().split(',')
      if (!list || !list_ids.length) return '-'
      return list_ids.map(item => {
        const result = this.loopUp_location_list.filter(opt => opt.id === +item)
        return result.length ? result[0].area_name + ' ' + result[0].dial_in_number : undefined
      }).join(', ')
    },

    getTaskDetail() {
      const params = {
        id: this.data.id,
        project_id: this.data.project_id,
        params: {
          extra: [
            'email_records',
            'email_records.email_record',
            'events',
            'client_contact_reviews',
            'client_compliance_reviews',
            'latest_submitted_sq',
            'latest_submitted_pre_ca',
            'latest_submitted_pre_ca.files',
            'latest_submitted_post_ca',
            'latest_submitted_post_ca.files',
            'client_contact_reviews.contact',
            'schedule',
            'contact_feedback_sent',
            'contact_feedback_respond',
            'capvision_compliance_status',
            'client_compliance_status',
            'loopup_room',
            'project',
            'advisor_decline',
            'advisor_normal_payment.task_amount_hours',
            'task_outsource_info',
            'payment_topic.payments.items',
          ].join(),
        },
      }
      return ProjectAPI.getTaskDetail(params)
        .then(data => {
          this.taskDetail = this.dataFormat(data)[this.nameToKey(this.drawerTitle)]
          console.log(this.taskDetail)
          this.taskData = data

          /* project 信息补至 task 中 */
          Object.assign(this.data, {
            project: data.project,
          })

          this.completeTime = data.complete_time
          this.getArrangeSchedules(data.schedule, data)

          /* https://www.notion.so/capvision/Expert-Emails-One-Click-Decline-Edit-Decline-status-in-Details-b0cf1be4392f4dd9ac52461ba581f944
            - If the expert has not been sent outreach, the Status section will be null
            - If the expert has, it will state OK in green
            - If the expert has declined, it will state DECLINED in red and display the Decline note to the right
            - The edit option will display and allow the user to change the status from OK to DECLINED or from DECLINED to OK.  It will not allow the user to change it back to Null or to change it from Null to OK (since sending outreach is what saves that) */
          this.getDeclineStatus(data.advisor_decline, this.taskDetail)
        })
    },
    dataFormat: (data) => {
      const events = data.events
      const email = data['email_records']

      /*
        Event:
          type
          target_status

        Email:
          email_content_type
       */

      const output_data = {
        outreach: {
          email: [],
          outreach: [],
        },
        consultant: {
          email: [],
          terms_conditions: [],
          screening_question: [],
          compliance_question: [],
          advisor_schedule: [],
        },
        client: {
          email: [],
          contact_approve: [],
          client_contact_schedule: [],
          client_contact_reviews: [],
        },
        compliance: {
          email: [],
          capvision_compliance: [],
          client_compliance: [],
        },
        arrange: {
          email: [],
          arrange: [],
        },
        complete: {
          email: [],
          complete: [],
          advisor_payment: [],
          post_compliance: [],
          client_feedback: [],
          advisor_payment_status: [],
        },
      }

      categorizeEvent(events, output_data)
      categorizeEmail(email, output_data)
      categorizeReview(data, output_data)
      categorizeAdvisorPaymentStatus(data, output_data)

      return output_data

      function categorizeEvent(events, output_data) {
        events.forEach(function(item) {
          const eventsCategory = getEventsCategory(item)
          if (eventsCategory) {
            const [level_1, level_2] = eventsCategory.split('>')
            output_data[level_1][level_2].push(item)
          }
        })

        function getEventsCategory(event) {
          const type = event.type

          const type_mapper = {
            OUTREACH: 'outreach>outreach',
            ARRANGE: 'arrange>arrange',
            COMPLETE: 'complete>complete',

            SQ_SEND: 'consultant>screening_question',
            SQ_RESPOND: 'consultant>screening_question',
            ADVISOR_SCHEDULE_SEND: 'consultant>advisor_schedule',
            ADVISOR_SCHEDULE_RESPOND: 'consultant>advisor_schedule',
            CLIENT_COMPLIANCE_SEND: 'consultant>compliance_question',
            CA_SEND: 'consultant>compliance_question',
            CA_RESPOND: 'consultant>compliance_question',
            CA_REVOKE: 'consultant>compliance_question',
            CA_OVERRIDDEN: 'consultant>compliance_question',

            CAPVISION_COMPLIANCE_APPROVE: 'compliance>capvision_compliance',
            CAPVISION_COMPLIANCE_DIRECT_APPROVE: 'compliance>capvision_compliance',
            CAPVISION_COMPLIANCE_REJECT: 'compliance>capvision_compliance',
            CAPVISION_CONSENT_TO_RECORD: 'compliance>capvision_compliance',
            CAPVISION_COMPLIANCE_HOLD: 'compliance>capvision_compliance',
            CAPVISION_REFUSE_TO_RECORD: 'compliance>capvision_compliance',
            CLIENT_COMPLIANCE_APPROVE: 'compliance>client_compliance',
            CLIENT_COMPLIANCE_REJECT: 'compliance>client_compliance',
            CLIENT_COMPLIANCE_HOLD: 'compliance>client_compliance',
            CLIENT_COMPLIANCE_AUTO_APPROVE: 'compliance>client_compliance',
            CLIENT_COMPLIANCE_AUTO_REJECT: 'compliance>client_compliance',

            CONTACT_SEND: 'client>contact_approve',
            CONTACT_APPROVE: 'client>contact_approve',
            CONTACT_REJECT: 'client>contact_approve',
            CONTACT_HOLD: 'client>contact_approve',
            CONTACT_SCHEDULE_SEND: 'client>client_contact_schedule',
            CONTACT_SCHEDULE_RESPOND: 'client>client_contact_schedule',

            ADVISOR_PAYMENT_CONFIRM_SEND: 'complete>advisor_payment',
            ADVISOR_PAYMENT_CONFIRM_RESPOND: 'complete>advisor_payment',
            POST_CA_SEND: 'complete>post_compliance',
            POST_CA_RESPOND: 'complete>post_compliance',
            ADVISOR_FEEDBACK_SEND: 'complete>client_feedback',
            ADVISOR_FEEDBACK_RESPOND: 'complete>client_feedback',
            CONTACT_FEEDBACK_SEND: 'complete>client_feedback',
            CONTACT_FEEDBACK_RESPOND: 'complete>client_feedback',
          }

          return type_mapper[type]
        }
      }

      /**
       * Email 分组
       * @param email
       * @param output_data
       */
      function categorizeEmail(email, output_data) {
        email.forEach(function(item) {
          const emailCategory = getEmailCategory(item['email_content_type'])

          if (emailCategory) {
            output_data[emailCategory].email.push(item)
          }
        })

        function getEmailCategory(type) {
          const mapper = {
            PROJECT_OUTREACH: 'outreach',

            PORTAL_ADVISOR_PRE_CALL: 'consultant',
            PORTAL_ADVISOR_FEEDBACK: 'consultant',

            PORTAL_CONTACT_PRE_CALL: 'client',
            PORTAL_CONTACT_POST_CALL: 'client',
            CLIENT_CONTACT_LOGIN_CODE: 'client',

            PORTAL_COMPLIANCE: 'compliance',

            TASK_ARRANGE: 'arrange',
            CALENDAR_TASK_ARRANGE_CONTACT: 'arrange',
            CALENDAR_TASK_ARRANGE_ADVISOR: 'arrange',

            TASK_COMPLETE: 'complete',
            PORTAL_ADVISOR_POST_CALL: 'complete',
            CUSTOM: '',
          }

          return mapper[type]
        }
      }

      function categorizeReview(data, output_data) {
        output_data.client.client_contact_reviews = data['client_contact_reviews']
      }

      function categorizeAdvisorPaymentStatus(data, output_data) {
        output_data.complete.advisor_payment_status = []
        if (data.general_status === 'COMPLETED') {
          const is_payment_v2 = !!data.payment_topic_id
          if (is_payment_v2) {
            output_data.complete.advisor_payment_status = data.payment_topic.payments
              .flatMap(item => item.items)
              .map(item => ({
                ...item,
                currency: item.currency_name,
              }))
          }

          const advisor_normal_payment = data['advisor_normal_payment']
          if (advisor_normal_payment) {
            output_data.complete.advisor_payment_status.unshift({
              ...advisor_normal_payment,
              amount: advisor_normal_payment.task_amount_hours?.amount,
            })
          }
        }
      }
    },

    drawerHide() {
      this.drawer = false
    },
    nameToKey(name) {
      const dict = {
        'Outreach': 'outreach',
        'Consultant': 'consultant',
        'Client': 'client',
        'Compliance': 'compliance',
        'Arrange': 'arrange',
        'Complete': 'complete',
      }
      return dict[name]
    },
    keyToName(key) {
      const dict = {

        'outreach': 'Outreach',
        'consultant': 'Consultant',
        'client': 'Client',
        'compliance': 'Compliance',
        'arrange': 'Arrange',
        'complete': 'Complete',

        'terms_conditions': 'Terms Conditions',
        'screening_question': 'Screening Question',
        'compliance_question': 'Client Agreement',
        'contact_approve': 'Contact Approve',
        'advisor_schedule': 'Advisor Schedule',

        'client_contact_schedule': 'Client Schedule',
        'client_contact_reviews': 'Client Reviews',

        'capvision_compliance': 'Capvision Approve',
        'client_compliance': 'Client Approve',

        'advisor_payment': 'Advisor Payment',
        'post_compliance': 'Post Compliance',
        'client_feedback': 'Client Feedback',
        'advisor_payment_status': 'Advisor Payment Status',

      }
      return dict[key]
    },
    getConsultantSchedule() {
      if (this.drawerTitle !== 'Consultant') {
        return
      }
      const params = {
        advisor_id: this.data.advisor_id,
        task_id: this.data.id,
        start_time_gt: this.data.create_at,
        end_time_lt: this.completeTime,
        creator_type: 'ADVISOR',
      }
      return SchedulerAPI.getTaskSchedule(params)
        .then(data => {
          this.consultantSchedules = data
        })
    },
    getContactSchedules() {
      if (this.drawerTitle !== 'Client') {
        return
      }

      const params = {
        task_id: this.data.id,
        start_time_gt: this.data.create_at,
        end_time_lt: this.completeTime,
        creator_type: 'CLIENT_CONTACT',
        extra: 'client_contact',
      }
      return SchedulerAPI.getTaskSchedule(params)
        .then(data => {
          const temp = []
          data.forEach(item => {
            var index = temp.indexOf(item.client_contact_id)
            if (index === -1) {
              this.contactSchedules.push({
                client_contact_id: item.client_contact_id,
                schedule: [
                  {
                    start_time: item.start_time,
                    end_time: item.end_time,
                  },
                ],
              })
              temp.push(item.client_contact_id)
            } else {
              this.contactSchedules[index].schedule.push({
                start_time: item.start_time,
                end_time: item.end_time,
              })
            }
          })
        })
    },
    getArrangeSchedules(schedules, data) {
      if (this.drawerTitle !== 'Arrange' || !schedules) {
        return
      }
      const result = Object.assign({}, schedules, data)
      this.arrangehSchedules.push(result)
    },

    getDeclineStatus(advisor_decline, taskDetail) {
      if (advisor_decline) {
        this.declinedStatus = advisor_decline.is_valid
          ? [{ time: advisor_decline.update_at, status: 'DECLINED' }]
          : [{ time: advisor_decline.update_at, status: 'OK' }]
      } else {
        const has_outreach = taskDetail.outreach?.length > 0
        if (has_outreach) {
          this.declinedStatus = [{ time: _.sortBy(taskDetail.outreach, 'create_at')[0].update_at, status: 'OK' }]
        } else {
          this.declinedStatus = []
        }
      }
    },
    editDeclinedStatus(status, reason) {
      this.new_decline_status = status
      this.new_decline_reason = reason
    },

    updateDeclinedStatus(origin_status) {
      if (this.new_decline_status === origin_status && origin_status === 'OK') {
        this.editDeclinedStatus(null, null)
      } else {
        const params = {
          task_id: this.taskData.id,
          reason: this.new_decline_reason,
        }
        const request = this.new_decline_status === 'DECLINED'
          ? ProjectAPI.taskAdvisorDeclined(params)
          : ProjectAPI.taskAdvisorDeclinedInvalidate(this.taskData.advisor_decline.id)

        request.then(data => {
          this.declinedStatus[0].time = data.update_at

          if (data.is_valid) {
            this.declinedStatus[0].status = 'DECLINED'
            this.taskData.advisor_decline = data
          } else {
            this.declinedStatus[0].status = 'OK'
          }

          this.$emit('update:advisorDecline', data)
          this.$emit('update')

          this.$message({
            type: 'success',
            message: 'Update successful!',
          })
        }, () => {
          this.$message({
            type: 'error',
            message: 'Update failed!',
          })
        })
          .finally(() => {
            this.editDeclinedStatus(null, null)
          })
      }
    },
    linkToPortal(key) {
      let expect_steps
      switch (key) {
        case 'advisor_schedule':
          expect_steps = ['AG']
          break
        case 'screening_question':
          expect_steps = ['SQ']
          break
      }
      const data = {
        requests: [
          {
            portal: {
              type: 'ADVISOR_PRE_CALL',
              task_id: this.data.id,
              advisor_id: this.data.advisor_id,
              expect_steps,
              is_public_portal: true,
            },
          }],
      }
      return ProjectAPI.portalToAdvisor(data)
        .then(res => {
          window.open(res[0].pm_link, '_blank')
        })
    },
    _linkToDetail(key, instanceId) {
      switch (key) {
        case 'screening_question':
          this.instanceId = instanceId
          break
        case 'compliance_question':
          this.currentCaType = 'pre-call'
          this.taskCAId = instanceId
          break
        case 'post_compliance':
          this.currentCaType = 'post-call'
          this.taskCAId = instanceId
          break
      }
    },
    linkToDetail(key) {
      switch (key) {
        case 'screening_question':
          this.SQdetail()
          break
        case 'compliance_question':
          this.CAdetail(key)
          break
        case 'post_compliance':
          this.CAdetail(key)
          break
      }
    },
    openToLink(key, value) {
      if (key === 'EMAIL') {
        const aTag = document.createElement('a')
        aTag.href = `mailto:${value}`
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    },
    SQdetail() {
      const responded_list = this.taskDetail['screening_question'].filter(item => item.type === 'SQ_RESPOND')
      const all_list = this.taskDetail['screening_question']

      if (!responded_list.length && !all_list.length) {
        return
      }
      if (!responded_list.length && all_list.length) {
        this.instanceId = all_list[all_list.length - 1].payload_id
        return
      }
      this.instanceId = responded_list[responded_list.length - 1].payload_id
    },
    CAdetail(key) {
      const type = {
        send: 'CA_SEND',
        respond: 'CA_RESPOND',
      }
      if (key === 'post_compliance') {
        type.send = 'POST_' + type.send
        type.respond = 'POST_' + type.respond
      }
      const responded_list = this.taskDetail[key].filter(item => item.type === type.respond)
      const all_list = this.taskDetail[key].filter(item => [type.send, type.respond].includes(item.type))

      if (!responded_list.length && !all_list.length) {
        return
      }
      if (!responded_list.length && all_list.length) {
        this.taskCAId = getPayload(all_list)
        return
      }
      this.taskCAId = getPayload(responded_list)

      // 获取当前最近回答过的CA 或 最新的CA
      function getPayload(list) {
        if (list.length === 1) {
          return list[0].payload_id
        } else {
          let max_time = list[0].create_at
          let newest_ca = {}
          list.forEach((item) => {
            if (item.create_at > max_time) {
              max_time = item.create_at
              newest_ca = item
            }
          })
          return newest_ca.payload_id
        }
      }
    },

    initFrom() {
      this.taskDetail = {}
      this.consultantSchedules = []
      this.contactSchedules = []
      this.arrangehSchedules = []
    },
    handleTabClick() {
      // this.drawerTitle = this.activeName
      this.initFrom()
      this.setData()
    },
    actionViewAdvisorPaymentConfirmDetail(item) {
      this.$refs.paymentDetailViewer.show({
        id: item['payload_id'],
        task_id: item.task_id,
      })
    },
    actionViewContactFeedback(item) {
      this.$refs.contactFeedbackViewer.show({
        id: item['payload_id'],
        task_id: item.task_id,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.stripe-table {
  border: 1px solid #EBEEF5;
  border-bottom: 0;

  thead .gutter {
    display: table-cell !important;
  }
}

::v-deep .popover-container {
  .popover-content {
    a {
      cursor: pointer;
      color: #409eff;
    }
  }
}

::v-deep .subject-popper {
  max-width: 800px;
}

.schedule {
  font-size: 14px;
  line-height: 22px;
}
</style>
