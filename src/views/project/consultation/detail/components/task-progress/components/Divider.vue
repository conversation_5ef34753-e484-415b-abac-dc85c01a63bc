<template>
  <div>
    <div class="el-divider el-divider--horizontal">

      <div class="el-divider__text is-left">
        <slot name="left" />
      </div>
      <div class="el-divider__text is-center">
        <slot name="middle" />
      </div>
      <div class="el-divider__text is-right">
        <slot name="right" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Divider',
  props: {
    leftText: {
      type: String,
      default: undefined,
    },
    middleText: {
      type: String,
      default: undefined,
    },
    rightText: {
      type: String,
      default: undefined,
    },
  },
}
</script>
