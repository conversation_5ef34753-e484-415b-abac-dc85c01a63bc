<template>
  <el-dialog
    title="Client Feedback"
    :visible.sync="dialogVisible">
    <loading-status :status="isLoading" :data="pageData" />
    <div v-if="pageData">
      <p>
        <label>Feedback Time:</label> {{pageData.create_at | momentFormat('MM/DD/YYYY (HH:mm)')}}
      </p>
      <p>
        <label>Expert Quality rate:</label> {{pageData['expertise_rate']}}
      </p>
      <p>
        <label>Communication rate:</label> {{pageData['communication_rate']}}
      </p>
      <div>
        <label>Comment:</label>
        <div class="pre-wrap">{{pageData.comment || '-'}}</div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dialogVisible = false">OK</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ProjectAPI from '@/api/project'
import ShowDialogMixins from '../show-dialog-mixins'
import LoadingStatus from '@/components/LoadingStatus'

export default {
  name: 'ClientFeedbackShow',
  components: { LoadingStatus },
  mixins: [ShowDialogMixins, LoadingStatus],
  methods: {
    loadData(params) {
      return ProjectAPI
        .getTaskContactFeedbackDetail(params)
    },
  },
}
</script>

<style scoped>

</style>
