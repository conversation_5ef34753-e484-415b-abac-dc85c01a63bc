<template>
  <div class="inline-block">
    <el-tooltip
      :disabled="hasDecipherSurveyId"
      content="Available when decipher survey id is attached to the project">
      <el-button
        type="success"
        class="filter-item"
        :disabled="!hasDecipherSurveyId"
        @click="updateSurveyProjectDecipherStatus">
        Pull Decipher Status
      </el-button>
    </el-tooltip>
  </div>

</template>

<script>
import SurveyProjectApi from '@/api/project-survey'

export default {
  name: 'DecipherUpdateBtn',
  props: {
    project: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    hasDecipherSurveyId() {
      return !!this.project?.decipher_id
    },

  },
  methods: {
    /**
     * 根据project.third_party_survey_url来判断是否提供decipher survey
     * @param third_party_survey_url {string}
     * @return boolean
     */
    isDecipherSurveyAttachedToProject(third_party_survey_url) {
      if (!third_party_survey_url) return false
      return third_party_survey_url.includes('selfserve')
    },

    updateSurveyProjectDecipherStatus() {
      this.$emit('update-start', true)
      return SurveyProjectApi.updateProjectDecipherStatus(this.project.id)
        .then(response => {
          this.$emit('update-done', {})
        })
    },
  },
}

</script>

<style scoped>

</style>
