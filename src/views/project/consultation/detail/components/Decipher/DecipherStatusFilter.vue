<template>
  <div class="inline-block">
    <el-select
      v-model="selectedStatusValues"
      multiple
      clearable
      placeholder="Select Decipher Status"
      @visible-change="onVisibleChange"
      @clear="onVisibleChange">
      <el-option
        v-for="item in filterOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value" />
    </el-select>
  </div>
</template>

<script>
import _ from 'lodash'
import SurveyDecipherMixin from '@/views/project/consultation/detail/mixins/survey-decipher-mixin'

export default {
  name: 'DecipherStatusFilter',
  mixins: [SurveyDecipherMixin],
  props: {},
  data() {
    return {
      selectedStatusValues: [],
    }
  },
  computed: {
    /**
     * Provide all none-manually-set statuses
     * @
     */
    filterOptions() {
      return this.deciperSurveyStatusDict.filter(it => !it.is_manual)
    },
  },
  methods: {
    /**
     * 举例：QUALIFIED 筛选应转为包含QUALIFIED 状态的statuses列表
     * @param {String[]} selectedValues
     * @return String[]
     */
    expandStatusCondition(selectedValues) {
      const mapped = selectedValues.flatMap(v => {
        return this.deciperSurveyStatusDict.filter(
          it => it.value === v || it.mask_value === v || (it.expanded_values || []).includes(v))
      }).map(it => it.value)
      return _.uniq(mapped)
    },

    /**
     * 只在选好了（收起）时进行查询
     */
    onVisibleChange(isShow) {
      if (isShow) return
      this.$emit('selected', this.expandStatusCondition(this.selectedStatusValues))
    },
  },
}

</script>

<style scoped>

</style>

