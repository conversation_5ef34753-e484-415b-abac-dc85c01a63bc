<template>
  <div>
    <el-dropdown
      class="dropdown"
      @command="(op) => updateTaskDecipherSurveyStatus(op)">
          <span class="dropdown-link" :class="{'manual-set': isManuallySetStatus(task.decipher_status)}">
            {{ task.decipher_status | getLabel(deciperSurveyStatusDict) }}
            <i class="el-icon-arrow-down el-icon--right arrow-icon" />
          </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="op in columnDropdownStatusDict(task.decipher_status)"
          :key="op.value"
          :command="op"
        >
          {{ op.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import SurveyDecipherMixin from '@/views/project/consultation/detail/mixins/survey-decipher-mixin'
import SurveyProjectApi from '@/api/project-survey'

export default {
  name: 'DecipherStatusColumn',
  mixins: [SurveyDecipherMixin],
  props: {
    task: Object,
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    /**
     * @param {string|null} currentStatus
     */
    columnDropdownStatusDict(currentStatus) {
      const optionValues = [
        'NOT_ENTERED',
        'INCOMPLETE',
        'MANUAL_QUALIFIED',
        'MANUAL_TERMINATED',
        'MANUAL_OVERQUOTA',
      ]
      const fixedOptions = this.deciperSurveyStatusDict.filter(it => optionValues.includes(it.value))
      const selectable = fixedOptions.filter(it => it.value !== currentStatus)
      return selectable
    },

    updateTaskDecipherSurveyStatus(option) {
      const selected_status = option.value
      const body_data = {
        'decipher_status': selected_status,
        'task_id': this.task.id,
      }
      return SurveyProjectApi.updateTaskDecipherSurveyStatus(this.task.id, body_data)
        .then(response => {
          this.task.decipher_status = selected_status
          this.$message({
            message: 'success',
            type: 'success',
          })
        })
    },
  },

}
</script>

<style scoped>
.dropdown {
  font-size: 12px;
  cursor: pointer;
  color: #409eff;

  .dropdown-link {
    white-space: normal;
    word-break: break-word;
    width: 100%;

    .arrow-icon {
      display: none;
    }

    &:hover {
      .arrow-icon {
        display: inline-block;
      }
    }
  }
}

.manual-set {
  color: #606266;
}
</style>
