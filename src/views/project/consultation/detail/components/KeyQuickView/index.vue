<template>
  <el-drawer
    size="60%"
    :visible.sync="drawer"
    :direction="direction"
    :append-to-body="true"
    @open="open"
    @close="close"
    @opened="opened"
    @closed="closed"
  >
    <div slot="title">{{pageTitle}}</div>
    <compliance-index v-if="pageName === 'Compliance'" :client-id="project.client_id" />
    <information v-if="pageName === 'Project Info'" />
    <client-detail v-if="pageName === 'Client'" :client-id="project.client_id" style="height: 100%;" />
  </el-drawer>
</template>

<script>

import ComplianceIndex from '@/views/project/consultation/detail/compliance/index'
import ClientDetail from '@/views/client/detail/index'
import Information from '@/views/project/consultation/detail/information/index'

export default {
  name: 'KeyQuickView',
  components: { Information, ClientDetail, ComplianceIndex },
  props: {
    project: Object,
  },
  data() {
    return {
      // 抽屉是否开启
      drawer: false,
      // 动画进行中
      isAnimating: false,
      // 内容名
      pageName: undefined,
      pageTitle: undefined,
      pageHTML: undefined,
      animationStopWatcher: undefined,
      direction: 'btt',
      keyListener: (event) => {
        const keyName = event.key
        const isMetaKey = event.metaKey
        const isCtrlKey = event.ctrlKey
        const isShiftKey = event.shiftKey
        const isAltKey = event.altKey
        const isEditable = [true, 'true'].includes(event.target.contentEditable)
        const isNodeInputElement = ['input', 'textarea', 'select'].includes(event.target.nodeName.toLowerCase())

        const ignore = isMetaKey || isCtrlKey || isShiftKey || isAltKey || isEditable || isNodeInputElement
        if (ignore) {
          return
        }

        this.drawerHandle(this.getPageNameByKey(keyName))
      },
    }
  },
  computed: {
    isDrawOpened() {
      return this.drawer
    },
  },
  mounted() {
    // 加载按键监听
    document.addEventListener('keypress', this.keyListener)
  },
  beforeDestroy() {
    // 卸载按键监听
    document.removeEventListener('keypress', this.keyListener)
  },
  methods: {
    /**
     * 按键合法触发抽屉的处理
     * @param pageName
     */
    drawerHandle(pageName) {
      // 忽略无效按键
      if (!pageName) {
        return
      }

      // Drawer 已开启时, 按下相同按键收起, 按下不同按键收起当前 Drawer 后展开目标 Drawer
      if (this.isDrawOpened) {
        this.closeDrawer()

        // 按下相同按键收起
        if (this.pageName === pageName) {
          this.pageName = undefined
        } else {
          // Drawer 关闭后展开目标 Drawer

          // 防止快速按键, 直接使用新目标
          if (this.animationStopWatcher) {
            this.animationStopWatcher()
          }

          this.animationStopWatcher = this.$watch('isAnimating', function(isAnimating) {
            if (isAnimating === false) {
              this.animationStopWatcher()

              // 记录使用中的 pageName.
              this.pageName = pageName

              // 展开 Drawer
              this.openDrawer(this.pageName)
            }
          })
        }
      } else {
        if (this.isAnimating) {
          return
        }

        // 记录使用中的 pageName
        this.pageName = pageName

        // 展开 Drawer
        this.openDrawer(pageName)
      }
    },
    getPageNameByKey(keyName) {
      const map = {
        z: 'Compliance',
        x: 'Project Info',
        c: 'Client',
      }

      return map[keyName]
    },
    openDrawer(pageName) {
      this.drawer = true
      this.loadData(pageName)
    },
    closeDrawer() {
      this.drawer = false
    },
    open() {
      this.isAnimating = true
    },
    close() {
      this.isAnimating = true
    },
    opened() {
      this.isAnimating = false
    },
    closed() {
      this.isAnimating = false
    },
    loadData(pageName) {
      this.pageTitle = pageName
      this.pageHTML = pageName
    },
  },
}
</script>

<style scoped>

</style>
