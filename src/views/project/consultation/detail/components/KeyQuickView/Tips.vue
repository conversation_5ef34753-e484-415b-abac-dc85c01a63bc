<template>
  <div>
    <el-link v-if="!show_tips" class="tips-icon" :underline="false" icon="el-icon-info" @click="actionShowTips" />
    <div v-if="show_tips" class="tips-box">
      <el-row type="flex" justify="space-between">
        <b>Tips!</b>
        <el-link @click="actionHideTips">[x]</el-link>
      </el-row>
      <div>Press <code>Z</code> to view Compliance</div>
      <div>Press <code>X</code> to view Project Info</div>
      <div>Press <code>C</code> to view Client</div>
    </div>
  </div>
</template>

<script>
const name = 'tooltip__project_keyQuickView__hidden'

export default {
  name: 'Tips',
  data() {
    return {
      show_tips: !localStorage.getItem(name),
    }
  },
  methods: {
    actionShowTips() {
      this.show_tips = true
      localStorage.removeItem(name)
    },
    actionHideTips() {
      this.show_tips = false
      localStorage.setItem(name, 'true')
    },
  },
}
</script>

<style lang="scss" scoped>
.tips-icon {
  font-size: 13px;
  color: #999;
}

.tips-box {
  font-size: 12px;
  padding: 4px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 2px 12px 0;
  color: #777;

  code {
    display: inline-block;
    border-radius: 2px;
    border: 1px solid #e0e0e0;
    background-color: #f5f5f5;
    line-height: 0.8;
    color: #333;
  }

  div {
    white-space: nowrap;
  }
}
</style>
