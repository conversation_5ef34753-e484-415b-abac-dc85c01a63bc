<template>
  <a @click="change">
    <div class="circle-btn">
      <i v-if="params.willingness === 'GOOD'" class="el-icon-success success" />
      <i v-if="params.willingness === 'BAD'" class="el-icon-error danger" />
      <div v-if="params.willingness === 'GENERAL'">
        <div class="triangle" />
      </div>
      <div v-if="params.willingness === 'UNKNOWN'" class="circle" />
    </div>
  </a>
</template>

<script>
import { mapGetters } from 'vuex'
import ProjectAPI from '@/api/project'
import { debounce } from '@/utils/tool'

export default {
  name: 'MarkBtn',
  props: {
    data: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      sum: 0,
      params: {
        project_id: 0,
        id: 0,
        willingness: 'UNKNOWN',
      },
    }
  },
  computed: {
    ...mapGetters(['project_options']),
    current_index() {
      let result = 0
      for (let index = 0; index < this.project_options.task.mark.length; index++) {
        const item = this.project_options.task.mark[index]
        if (item.value === this.params.willingness) {
          result = index
        }
      }
      return result
    },

  },
  mounted() {
    this.sum = this.project_options.task.mark.length
    this.params.project_id = this.data.project_id
    this.params.id = this.data.id
    this.params.willingness = this.data.willingness
  },
  methods: {
    change() {
      if (this.current_index + 1 === this.sum) {
        this.params.willingness = this.project_options.task.mark[0].value
      } else {
        this.params.willingness = this.project_options.task.mark[this.current_index + 1].value
      }
      this.updatedTask(this.params)
    },
    updatedTask(params) {
      debounce(() => {
        return ProjectAPI.updateTask(params)
          .then(() => {
            this.$message.success('success')
          })
      }, 500)
    },
  },

}
</script>
<style lang="scss" scoped>
.circle-btn {
  width:26px;
  height:26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  font-size: 16px;

  &:hover {
    cursor: pointer;
    background: #e2e6ea;
  }

  .triangle {
    width: 0px;
    height: 0px;
    border-color: transparent transparent  #e6a23c transparent;
    border-width: 0px 8px 12px 8px;
    border-style: solid;
  }

  .circle {
    width: 14px;
    height: 14px;
    -moz-border-radius: 50px;
    -webkit-border-radius: 50px;
    border-radius: 50%;
    background: #909399;
  }
}
</style>
