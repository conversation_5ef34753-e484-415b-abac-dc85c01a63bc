<template>
  <div class="inline-block">
    <el-popover
      v-model="visible"
      trigger="manual"
      placement="top"
      @after-leave="cleanData">
      <div>
        <el-tag size="small">
          <el-link
            type="primary"
            :href="portal_data['public_link']"
            target="_blank">
            {{ portal_data['public_link'] }}
          </el-link>
        </el-tag>
        <el-button type="primary" size="mini" @click="actionCopy">Copy</el-button>
      </div>
      <el-button
        slot="reference"
        type="primary"
        disabled
        icon="el-icon-link"
        :loading="loading"
        @click="actionPortal">
        Copy Portal Link
      </el-button>
    </el-popover>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'

export default {
  name: 'CopyPortalLink',
  props: {
    project: {
      type: Object,
      default: () => { return {} },
    },
    tasks: {
      type: Array,
      default: () => { return [] },
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      portal_data: {},
    }
  },
  methods: {
    actionPortal() {
      if (!this.visible) {
        this.start()
          .then(() => {
            this.visible = true
          })
      } else {
        this.visible = false
      }
    },

    cleanData() {
      this.portal_data = {}
      this.loading = false
    },

    start() {
      this.loading = true
      return this.getPortalToClient()
        .then(data => {
          this.portal_data = data
          this.copy(data['public_link'])
        })
        .finally(() => {
          this.loading = false
        })
    },

    actionCopy() {
      this.copy(this.portal_data['public_link'])
      this.visible = false
    },

    copy(link) {
      return new Promise((resolve) => {
        const textarea = document.createElement('textarea')
        textarea.value = link
        document.body.appendChild(textarea)
        textarea.select()

        if (document.execCommand('copy')) {
          document.execCommand('copy')
          this.$message({
            message: 'Copy successful',
            type: 'success',
            duration: 2000,
            onClose: () => {
              resolve()
            },
          })
        } else {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        }
        document.body.removeChild(textarea)
      })
    },
    getPortalToClient() {
      const params = {
        portal: {
          type: 'CONTACT_PRE_CALL',
          project_id: this.project.id,
          is_public_portal: true,
          task_id_set: this.tasks.map(({ id }) => id),
          trigger_events: ['CONTACT_SEND'],
        },
      }

      return ProjectAPI.portalToClient(params)
    },
  },
}
</script>

<style scoped>

</style>
