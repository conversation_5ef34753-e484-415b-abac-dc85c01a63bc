<template>
  <div style="display: inline-block;">
    <el-button style="margin-left: 0" type="primary" icon="el-icon-document-add" @click="attachToProject">
      {{ isClientTask ? 'Attach To...' : 'Move to Project' }}
    </el-button>

    <el-dialog
      title="Select Target Project"
      :visible.sync="selectProjectDialog"
      append-to-body
      :close-on-click-modal="false"
      width="480px"
      @open="initCheckbox"
      @opened="autoFocus"
    >
      <div class="flex justify-content-center align-items-center">
        <el-select
          ref="select"
          v-model="selectedProjectID"
          filterable
          :filter-method="getProjectList"
          remote
          reserve-keyword
          :popper-append-to-body="false"
          placeholder="Search"
          :style="{
            'width': origin ? '75%' : '70%'
          }"
          no-data-text="No matching data"
          :loading="selectLoading"
          @change="matchAngle()"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="projectId === item.id">
            <span>{{ `[${item.id}]  ${item.name}` }}</span>
          </el-option>
        </el-select>
        <el-select
          v-if="selectedProjectID && !origin && isClientTask"
          v-model="selectedAngleID"
          filterable
          clearable
          class="pl-5"
          placeholder="Angle"
        >
          <el-option
            v-for="item in angle_option"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <el-checkbox v-if="origin" v-model="copyAngle">Copy Angle</el-checkbox>
      </div>
      <div class="mt-8">
        <el-checkbox v-model="includeCheckbox.sq_responses">
          Include SQ Responses
        </el-checkbox>
      </div>
      <div class="mt-8">
        <el-checkbox v-model="includeCheckbox.sq_summary">
          Include SQ Summary
        </el-checkbox>
      </div>
      <div class="mt-8">
        <el-checkbox v-model="includeCheckbox.relevant_employer_and_title">
          Include Relevant Employer and Title
        </el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="selectProjectDialog = false">Cancel</el-button>
        <el-button type="primary" :loading="loading" :disabled="submitDisabled" @click="checkBeforeAddToProject">OK
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="warningDialogVisible"
      append-to-body
      title="Add To Project"
      width="50%">
      <div>
        <slot v-for="item in disabled_data">
          <el-alert
            v-if="item.list.length"
            :key="item.key"
            type="warning"
            show-icon
            class="mb-8">
            <template #title>
              {{item.prompt}}
              <span v-for="(temp,index) in item.list" :key="index">
                <router-link-advisor :data="getAdvisorData(temp.advisor_id)" />
                <slot v-if="index + 1 !== item.list.length">,</slot>&nbsp;
              </span>
            </template>
          </el-alert>
        </slot>
        <el-alert
          v-if="allow_list.length"
          title="These experts are allowed to be added:"
          type="success"
          show-icon
          class="mb-8">
          <span v-for="(item, index) in allow_list" :key="index">
            <router-link-advisor
              :data="getAdvisorData(item.task.advisor_id)" />
            <slot v-if="index+1 !== allow_list.length">,</slot>
          </span>
        </el-alert>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!allow_list.length"
          @click="activeAttach(allow_list)">Add To Project</el-button>
        <el-button type="text" @click="warningDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { mapGetters } from 'vuex'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import { disableAddToProjectPromptFormat } from '@/utils/tool'

export default {
  components: { RouterLinkAdvisor },
  props: {
    selectedAdvisors: {
      type: Array,
      default: undefined,
    },
    projectId: {
      type: Number,
      default: undefined,
    },
    origin: {
      type: String,
      default: undefined,
    },
    originData: {
      type: Object,
      default: undefined,
    },
    type: {
      type: String,
      default: 'clientTask',
    },
  },
  data() {
    return {
      loading: false,
      warningDialogVisible: false,
      selectProjectDialog: false,
      selectLoading: false,
      selectedProjectID: '',
      selectedAngleID: '',
      projectList: [],
      angle_option: [],
      copyAngle: false,
      includeCheckbox: {
        sq_responses: false,
        sq_summary: false,
        relevant_employer_and_title: false,
      },
      has_disabled_advisor: false,
      disabled_data: [],
      allow_list: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),

    isClientTask() {
      return this.type === 'clientTask'
    },

    /* 在 client task 中
       如果所选专家都是未签署过consultation TC 的专家 则允许不选择angle就可以添加到项目的leads list,
       如果包含签署过consultation TC 的专家， 则只能添加到项目的Angle中 */
    submitDisabled() {
      const has_advisor_type = this.selectedAdvisors.find(item => item.advisor.tc_term_valid.CONSULTATION)
      const disabled = has_advisor_type && this.isClientTask ? !this.selectedAngleID : !this.selectedProjectID
      return disabled || this.loading
    },
  },
  methods: {
    initCheckbox() {
      for (const key in this.includeCheckbox) {
        this.includeCheckbox[key] = false
      }
    },
    autoFocus() {
      this.$nextTick(() => {
        this.$refs.select.focus()
      })
    },
    attachToProject() {
      this.selectProjectDialog = true
      this.getProjectList()
    },
    getProjectList(val) {
      const params = {
        name_like: val || undefined,
        member_ids: this.uid,
        type: 'CONSULTATION',
        sort: 'id desc',
        extra: 'angles',
      }
      this.selectLoading = true
      return ProjectAPI.getProjectList(params)
        .then(data => {
          this.projectList = data.list
          this.selectLoading = false
        })
    },

    matchAngle() {
      this.selectedAngleID = undefined
      this.angle_option = this.projectList.find(item => +item.id === +this.selectedProjectID).angles
    },

    checkBeforeAddToProject() {
      if (this.selectedProjectID === '') {
        return
      }
      this.allow_list = []
      // this.has_disabled_advisor = false
      this.loading = true
      const params = []
      const tasks = this.selectedAdvisors.reduce((arr, item) => {
        const result = {
          task: {
            advisor_id: item.advisor_id,
            client_contact_id: null,
            angle_id: this.selectedAngleID || undefined,
            willingness: 'UNKNOWN',
          },
        }
        arr.push({ ...result })
        this.formatTasksData(result, item)
        params.push({ ...result })
        return arr
      }, [])

      return ProjectAPI.checkBeforeAdvisorAddToProject(this.selectedProjectID, tasks)
        .then(data => {
          const validated_types = [
            this.isClientTask ? 'duplicate_in_angle' : 'duplicate_in_project',
            'empty_employment_history_advisor',
            'employees_conflict_investment_target',
            'with_not_current_region_advisor',
            'outsource_limit_advisor',
          ]

          this.disabled_data = disableAddToProjectPromptFormat(validated_types, data)

          const allow_to_add = !validated_types.some(type => data[type].length)
          if (allow_to_add) {
            this.allow_list = this.selectedAdvisors.map(item => {
              const result = { task: item }
              this.formatTasksData(result, item)
              return result
            })
            return this.activeAttach(params)
          } else {
            this.allow_list = this.selectedAdvisors.filter(item => allowToAdd(validated_types, item.advisor_id, data)).map(item => {
              const result = {
                task: {
                  advisor_id: item.advisor_id,
                  client_contact_id: null,
                  angle_id: this.selectedAngleID || undefined,
                  willingness: 'UNKNOWN',
                  id: item.id,
                },
              }
              this.formatTasksData(result, item)
              return result
            })
            this.warningDialogVisible = true
          }
        })
        .finally(() => {
          this.loading = false
        })
      function allowToAdd(validated_types, advisor_id, data) {
        return !validated_types.some(key => data[key] && data[key].find(item => parseInt(item.advisor_id) === parseInt(advisor_id)))
      }
    },

    formatTasksData(result, item) {
      if (this.includeCheckbox.sq_responses) {
        result.include_sq = true
        result.original_task_id = item.id
      }
      if (this.includeCheckbox.sq_summary) {
        result.task.screening_summary = item.screening_summary
        result.task.screening_summary_update_time = item.screening_summary_update_time
      }
      if (this.includeCheckbox.relevant_employer_and_title) {
        result.advisor_job_id = item.advisor_profile?.job_id
      }
    },

    getAdvisorData(advisor_id) {
      return this.selectedAdvisors.find(item => item.advisor_id === advisor_id)?.advisor
    },

    activeAttach(advisors) {
      return this.addAdvisor(advisors)
        .then(() => {
          if (this.type === 'leads') {
            this.$emit('done', this.allow_list)
          }
          this.selectProjectDialog = false
          this.$message.success('success')
        }).finally(() => {
          this.warningDialogVisible = false
        })
    },
    addAdvisor(params) {
      if (this.copyAngle) {
        return this.copyAngleMethod(params)
      }

      return ProjectAPI.addAdvisor(this.selectedProjectID, params)
    },
    copyAngleMethod(params) {
      return this.creatAngle()
        .then(data => {
          params.forEach(item => (item.task.angle_id = data.id))

          return ProjectAPI.addAdvisor(this.selectedProjectID, params)
        })
    },
    creatAngle() {
      const params = {
        project_id: this.selectedProjectID,
        name: this.originData.name,
        inquiry_branch_id: '',
        members: [],
      }

      return ProjectAPI.saveAngle(params)
    },
  },
}
</script>
