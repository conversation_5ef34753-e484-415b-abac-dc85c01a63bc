<template>
  <el-dialog
    ref="taskDownload"
    title="Download Template"
    width="40%"
    :visible="dialogVisible"
  >
    <el-radio-group v-model="selected_template_id">
      <el-radio
        v-for="item in client_file_template"
        :key="item.id"
        :label="item.id"
        class="mb-8">
        Custom Templates - {{item.name}}
      </el-radio>
      <el-radio :label="null">Default</el-radio>
    </el-radio-group>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">Cancel</el-button>
      <el-button type="primary" @click="downloadTask(selected_template_id)">Download</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'Index',
  data() {
    return {
      selected_advisors: [],
      client_file_template: [],
      angle_id: null,
      download_type: '',
      project: null,
      download_all_leads: false,
      dialogVisible: false,
      selected_template_id: null,
      blacklistFilter: false,
    }
  },
  methods: {
    checkBeforeDownload(project, type, all_leads = false, angle_id = null, selected_advisors = [], blacklistFilter = false) {
      this.project = project
      this.download_type = type
      this.download_all_leads = all_leads
      this.angle_id = angle_id
      this.selected_advisors = selected_advisors
      this.blacklistFilter = blacklistFilter

      this.checkClientDownloadTemplate(type)
        .then(() => {
          if (this.client_file_template.length > 0) {
            this.selected_template_id = this.client_file_template[0].id
            this.dialogVisible = true
          } else {
            this.downloadTask()
          }
        })
    },

    downloadTask(template_id = null) {
      const params = {
        client_file_template_id: template_id,
      }
      if (this.download_type === 'ADVISOR_RECOMMEND') {
        if (this.angle_id) {
          if (this.angle_id.length > 0) {
            params.angle_ids = this.angle_id.join()
          } else {
            params.angle_id = this.angle_id
          }
        } else {
          params.ids = this.selected_advisors
            .map((item) => {
              return item.id
            })
            .join()
        }
      } else {
        params.file_name = this.project.name + '.Leads'
        if (this.download_all_leads) {
          params.project_id = this.project.id
          params.angle_id_is_null = true
        } else {
          params.ids = this.selected_advisors
            .map((item) => {
              return item.id
            })
            .join()
        }
      }
      if (this.blacklistFilter === 'BLACKLIST' || this.blacklistFilter === 'B&D') {
        params['advisor.or-0.status_not_in'] = 'BLACKLIST'
        params['advisor.or-1.status_is_null'] = true
      }
      if (this.blacklistFilter === 'DECLINED' || this.blacklistFilter === 'B&D') {
        params['advisor_decline.or-0.id_is_null'] = true
        params['advisor_decline.or-1.is_valid'] = false
      }
      return ProjectAPI.downloadTask(params)
        .then(() => {
          this.closeDialog()
        })
    },

    closeDialog() {
      this.dialogVisible = false
      this.$emit('update')
    },
    async checkClientDownloadTemplate(type) {
      const params = {
        page: 1,
        size: 10,
        content_type: type,
        type: 'EXCEL',
        client_id: this.project.client_id,
      }
      return ProjectAPI.checkDownloadTemplate(params)
        .then(data => {
          this.client_file_template = data.list
        }, () => {
          this.$emit('update')
        })
    },
  },
}
</script>

<style scoped>

</style>
