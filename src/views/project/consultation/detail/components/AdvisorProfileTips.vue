<template>
  <el-tooltip
    v-if="need_update"
    effect="dark"
    content="Employment history has been updated, please reselect the relevant employer"
    placement="top">
    <el-link
      type="danger"
      :underline="false"
      icon="el-icon-warning"
    />
  </el-tooltip>
</template>

<script>
import moment from 'moment'

export default {
  name: 'AdvisorProfileTips',
  props: {
    advisorProfile: Object,
    advisorJobs: Array,
  },
  computed: {
    need_update() {
      const target_job = this.advisorJobs.find(item => item.id === this.advisorProfile.job_id)
      const target_job_updated = target_job &&
        moment(target_job.update_at).isAfter(moment(this.advisorProfile.update_at))
      return this.advisorProfile.job_id && (!target_job || target_job_updated)
    },
  },
}
</script>

<style scoped>

</style>
