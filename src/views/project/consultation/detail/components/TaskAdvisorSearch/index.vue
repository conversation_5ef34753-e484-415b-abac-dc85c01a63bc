<template>
  <div class="filter-item inline-block">
    <el-select
      v-model="selected_task_id"
      :loading="loading"
      filterable
      remote
      clearable
      reserve-keyword
      placeholder="Search Name,Phone,Email"
      :remote-method="querySearchAsync"
      @change="handleSelect"
    >
      <el-option
        v-for="item in extraOption"
        :key="item.id"
        :label="item.label"
        :value="item.id">
        <span v-html="$sanitizeHtml(item.value)" />
      </el-option>
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.label"
        :value="item.id">
        <span v-html="$sanitizeHtml(item.value)" />
      </el-option>
    </el-select>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { debounce, highlightHtml } from '@/utils/tool'

export default {
  name: 'Index',
  props: {
    projectId: [String, Number],
    extraOption: {
      type: Array,
      default() {
        return []
      },
    },
    initId: [String, Number],
  },
  data() {
    return {
      selected_task_id: '',
      loading: false,
      options: [],
    }
  },
  methods: {
    handleSelect() {
      this.$emit('search', this.selected_task_id)
    },

    querySearchAsync(query) {
      debounce(() => {
        this.searchAsync(query)
      }, 300)
    },

    searchAsync(query) {
      const params = {
        project_id: this.projectId,
        angle_id_is_null: false,
        or: [
          {
            advisor: {
              full_name_contains: query,
            },
          },
          {
            advisor: {
              email_contains: query,
            },
          },
          {
            advisor: {
              contact_infos: {
                type: 'PHONE',
                value_contains: query,
              },
            },
          },
        ],
      }
      this.loading = true
      return ProjectAPI.searchTask(params)
        .then(data => {
          this.options = data.list.map(item => {
            const phone_list = []
            const email_list = []

            item.advisor.contact_infos.forEach(i => {
              if (i.type === 'PHONE') {
                const format_phone = this.formatSearchResult(i.value,
                  item.advisor.contact_infos_without_mosaic.find(contact => contact.id === i.id)['value'], query)
                phone_list.push(format_phone)
              }
              if (i.type === 'EMAIL') {
                const format_email = this.formatSearchResult(i.value,
                  item.advisor.contact_infos_without_mosaic.find(contact => contact.id === i.id)['value'], query)
                email_list.push(format_email)
              }
            })

            const value = [item.angle.name, item.advisor.full_name, phone_list.join(), email_list.join()].join(' ')
            const value_html = [
              highlightHtml(item.angle.name, [query]),
              highlightHtml(item.advisor.full_name, [query]),
              highlightHtml(phone_list.join(), [query]),
              highlightHtml(email_list.join(), [query])].join('&nbsp;&nbsp;&nbsp;')

            return { value: value_html, id: `${item.angle_id}-${item.id}-${item.advisor.id}`, label: value }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    formatSearchResult(value, value_without_mosaic, query) {
      // '324***23521' 规范化=> '324****23521'
      // '324****23521', '324235523521' =》输入 242,展示为 3242***23521
      // 将带星号的值的长度 与完整值保持一致
      const mosaic_number = value.split('*').filter(item => item !== '')
      const mosaic_number_length = mosaic_number[0].length + mosaic_number[1]?.length
      const need_mosaic_length = value_without_mosaic.length - mosaic_number_length
      let mosaic_star = ''
      for (let i = 1; i < need_mosaic_length + 1; i++) {
        mosaic_star += '*'
      }
      const valid_mosaic = mosaic_number[0] + mosaic_star + mosaic_number[1]

      // 匹配完整值 将匹配到的值替换为对应长度的'#'
      let replace_data = ''
      for (let i = 1; i < query.length + 1; i++) {
        replace_data += '#'
      }
      const _value = value_without_mosaic.replace(new RegExp(`${query}`, 'gi'), replace_data)

      // 遍历 带星号的值，将匹配到的值展示出来，未匹配到的*保持原样
      let result = ''
      for (let i = 0; i < valid_mosaic.length; i++) {
        if (valid_mosaic[i] !== '*') {
          result += valid_mosaic[i]
        } else {
          result += _value[i] === '#' ? value_without_mosaic[i] : '*'
        }
      }
      return result
    },

    clearSearch() {
      this.selected_task_id = null
    },
  },
}
</script>

<style scoped>

</style>
