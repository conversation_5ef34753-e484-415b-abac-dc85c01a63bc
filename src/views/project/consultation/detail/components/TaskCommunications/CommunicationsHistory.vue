<template>
  <el-popover
    placement="top-start"
    width="400"
    trigger="click">
      <el-table
        class="stripe-table scrollbar-narrow"
        height="400"
        :data="item.task_communication"
        stripe
      >
        <el-table-column width="150" label="Name" prop="name" />
        <el-table-column label="Time">
          <template scope="scope">
            {{ scope.row.create_at | momentFormat('MM/DD/YYYY hh:mm A') }}
          </template>
        </el-table-column>
        <el-table-column label="Type" width="50">
          <template scope="scope">
            <span class="capitalize">{{ scope.row.type.toLowerCase() }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Create By" width="80" prop="create_by_name" />
      </el-table>
        <div slot="reference">
          {{ item.latest_task_communication_time | momentFormat('MM/DD/YYYY hh:mm A') }}
          <span class="link">({{ item.task_communication.length }})</span>
        </div>
  </el-popover>
</template>

<script>
export default {
  name: 'LastOutreachHistory',
  props: {
    item: Object,
  },
}
</script>

<style scoped>

</style>
