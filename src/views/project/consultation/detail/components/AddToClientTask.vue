<template>
  <div class="inline-block">
    <el-button
      :disabled="!angleList.length"
      type="primary"
      icon="el-icon-document-add"
      @click="showSelectDialog"
    >
      {{ btnText }}
    </el-button>

    <el-dialog
      title="Select Target Angle"
      :visible.sync="selectDialogVisible"
      append-to-body
      :close-on-click-modal="false"
      width="480px"
    >
      <el-alert v-if="advisor_duplicate_in_angle" type="warning" show-icon :closable="false" class="mb-8">
        <PERSON><PERSON> already attached to this project.
      </el-alert>
      <el-alert v-if="advisor_no_work_experience" type="warning" show-icon :closable="false" class="mb-8">
        <PERSON><PERSON> does not have any work experience.
      </el-alert>
      <el-alert v-if="advisor_job_conflict_investment_target" type="warning" show-icon :closable="false" class="mb-8">
        The current employers of the expert conflict with the investment target of the project.
      </el-alert>
      <el-alert v-if="advisor_job_conflict_investment_target" type="warning" show-icon :closable="false" class="mb-8">
        <PERSON><PERSON> belongs to outsource db.
      </el-alert>
      <el-alert v-if="advisor_outsource_limit" type="warning" show-icon :closable="false" class="mb-8">
        Expert is automatically created by the system and is only used in Outsource projects to record payment.
      </el-alert>
      <div class="flex justify-content-center align-items-center">
        <el-select
          ref="select"
          v-model="selectedID"
          filterable
          remote
          reserve-keyword
          popper-append-to-body
          placeholder="Search"
          no-data-text="No matching data"
          class="w-100"
        >
          <el-option
            v-for="item in angleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <span>{{ item.name }}</span>
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="selectDialogVisible = false">Cancel</el-button>
        <el-button
          :disabled="!selectedID"
          :loading="loading"
          type="primary"
          @click="activeOK"
        >OK</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'AddToClientTask',
  props: {
    tasks: {
      type: Array,
      default: () => {
        return []
      },
    },
    advisor: {
      type: Object,
      default: () => {
        return {}
      },
    },
    btnText: {
      type: String,
      default: 'Add to Client Task',
    },
  },
  data() {
    return {
      loading: false,
      project_id: this.$route.params.project_id,
      selectDialogVisible: false,
      selectedID: undefined,
      angleList: [],
      advisor_duplicate_in_angle: false,
      advisor_no_work_experience: false,
      advisor_job_conflict_investment_target: false,
      advisor_belong_to_outsource_db: false,
      advisor_outsource_limit: false,
    }
  },
  mounted() {
    this.getAngleList()
  },
  methods: {
    getAngleList() {
      return ProjectAPI.getAngleList(
        { project_id: this.project_id },
        { sort: 'id ASC' },
      ).then(data => {
        this.angleList = data['list']

        /* Auto select */
        if (data['list'].length) {
          this.selectedID = data['list'][0].id
        }
      })
    },

    activeOK() {
      if (this.tasks.length) {
        this.activeAttach()
      }
      if (this.advisor.id) {
        this.checkBeforeAddToProject()
      }
    },

    checkBeforeAddToProject() {
      this.loading = true
      const params = [{
        task: {
          advisor_id: this.advisor.id,
          angle_id: this.selectedID,
          client_contact_id: null,
          willingness: 'UNKNOWN',
        },
      }]

      return ProjectAPI.checkBeforeAdvisorAddToProject(this.project_id, params)
        .then(data => {
          const validated_types = [
            'duplicate_in_angle',
            'empty_employment_history_advisor',
            'employees_conflict_investment_target',
            'with_not_current_region_advisor',
            'outsource_limit_advisor',
          ]
          if (!validated_types.some(type => data[type].length)) {
            this.$emit('add', this.selectedID)
            return this.hideSelectDialog()
          } else {
            this.advisor_duplicate_in_angle = data.duplicate_in_angle.length > 0
            this.advisor_no_work_experience = data.empty_employment_history_advisor.length > 0
            this.advisor_job_conflict_investment_target = data.employees_conflict_investment_target.length > 0
            this.advisor_belong_to_outsource_db = data.with_not_current_region_advisor.length > 0
            this.advisor_outsource_limit = data.outsource_limit_advisor.length > 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    activeAttach() {
      return ProjectAPI.moveToAngle(this.project_id, {
        angle_id: this.selectedID,
        task_ids: this.tasks.map(({ id }) => {
          return id
        }),
      }).then(() => {
        this.hideSelectDialog()
        this.$emit('done')
      })
    },

    showSelectDialog() {
      this.selectDialogVisible = true
    },
    hideSelectDialog() {
      this.selectDialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
