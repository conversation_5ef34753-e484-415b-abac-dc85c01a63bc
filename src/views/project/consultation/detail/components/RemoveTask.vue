<template>
  <div class="inline-block">
    <el-button
      type="danger"
      icon="el-icon-delete"
      @click="showDialog"
    >
      Remove
    </el-button>

    <el-dialog
      append-to-body
      title="Warning"
      :visible.sync="removeDialogVisible"
      @closed="hideDialog">
      <el-alert
        v-if="filterTasks.length"
        :title="reasonText"
        type="warning"
        show-icon
        class="mb-8">
        <a
          v-for="(item, index) in filterTasks"
          :key="item.id"
          class="link"
          @click="routeToConsultantDetail(item.advisor_id)">
          {{item.advisor.name_prefix || ''}} {{item.advisor.full_name}}
          {{repeatedTasksID.includes(item.advisor_id) ?
            !isLeads ?
              `#${item.display_id}` : `#${item.sequence}` : ''}}
          <slot v-if="index+1 !== filterTasks.length">,</slot>
        </a>
      </el-alert>
      <el-alert
        v-if="passTasks.length"
        title="These Advisors will be Removed:"
        type="error"
        show-icon
        class="mb-8">
        <a
          v-for="(item, index) in passTasks"
          :key="item.id"
          class="link"
          @click="routeToConsultantDetail(item.advisor_id)">
          {{item.advisor.name_prefix || ''}} {{item.advisor.full_name}}
          {{repeatedTasksID.includes(item.advisor_id) ?
            !isLeads ?
              `#${item.display_id}` : `#${item.sequence}` : ''}}
          <slot v-if="index+1 !== passTasks.length">,</slot>
        </a>
      </el-alert>
      <!-- <div class="el-message-box__container">
        <div class="el-message-box__status el-icon-warning" />
        <div class="el-message-box__message">
          <p>Confirm delete?</p>
        </div>
      </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button type="text" size="mini" @click="removeDialogVisible = false">Cancel</el-button>
        <el-button type="danger" :disabled="!passTasks.length" size="mini" @click="batchRemove">Confirm</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'

export default {
  name: 'RemoveTask',
  props: {
    type: {
      type: String,
      default: 'client-tasks',
    },
    selectedAdvisors: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      removeDialogVisible: false,
      repeatedTasksID: [],
      filterTasks: [],
      passTasks: [],
      reasonText: '',
    }
  },
  computed: {
    isLeads() {
      return this.type === 'Leads'
    },
  },
  mounted() {},
  methods: {
    showDialog() {
      this.filterTaskList()
      this.checkRepeatedTasks()
      this.removeDialogVisible = true
    },
    hideDialog() {
      this.repeatedTasksID = []
      this.filterTasks = []
      this.passTasks = []
    },
    checkRepeatedTasks() {
      this.repeatedTasksID = []
      const ids = this.selectedAdvisors.map(item => item.advisor_id)
      ids.sort().sort((a, b) => {
        if (a === b && this.repeatedTasksID.indexOf(a) === -1) {
          this.repeatedTasksID.push(a)
        }
      })
    },
    filterTaskList() {
      this.selectedAdvisors.forEach(task => {
        if (this.removeFilterMethod(task)) {
          this.passTasks.push(task)
        } else {
          this.filterTasks.push(task)
        }
      })
    },
    removeFilterMethod(task) {
      if (['IMPORTED', 'EXPORTED'].includes(task.task_outsource_status)) {
        this.reasonText = 'Cannot remove outsource imported or exported tasks'
        return false
      }

      if (!this.isLeads) {
        if (task.client_compliance_status === 'INITIAL' &&
         task.client_contact_status === 'INITIAL') {
          return true
        } else {
          this.reasonText = 'Cannot remove after sent to client'
          return false
        }
      }

      if (this.isLeads) {
        if (task.sq_status === 'INITIAL' &&
         task.sq_status === 'INITIAL' &&
         task.ca_status === 'INITIAL' &&
         task.advisor_schedule_status === 'INITIAL') {
          return true
        } else {
          this.reasonText = 'Cannot remove after contacted'
          return false
        }
      }
    },
    batchRemove() {
      const params = this.passTasks
        .map(item => {
          return item.id
        })
      ProjectAPI.deleteTask(params).then(() => {
        this.$message({
          message: 'Remove successful',
          type: 'success',
        })
        this.removeDialogVisible = false
        this.$emit('done')
      })
    },
    routeToConsultantDetail(advisor_id) {
      const route = this.$router.resolve({
        name: 'ConsultantDetail',
        params: { advisor_id },
      })
      window.open(route.href, '_blank')
    },
  },
}
</script>

<style scoped>
  .el-message-box__container {
    padding: 8px 16px;
  }
</style>
