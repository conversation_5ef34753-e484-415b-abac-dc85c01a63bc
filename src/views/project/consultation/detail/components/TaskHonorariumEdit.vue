<template>
  <div>
    <el-popover
      v-model="popoverVisible"
      placement="top"
      width="330px">
      <div v-if="visible" class="mt-8">
        <el-input-number v-model="form.rate" class="w-120px" />
        <el-select
          v-model="form.rate_currency"
          placeholder="Currency"
          class="w-120px ml-3">
          <el-option
            v-for="item in advisor_options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
        <el-button type="text" size="mini" class="ml-3" @click="updateTaskRate">Save</el-button>
        <el-button type="text" size="mini" class="info" @click="cancelEdit">Cancel</el-button>
      </div>
      <el-button slot="reference" type="text" @click="initTaskRate()">
        <span v-if="has_rate">{{ task_format.rate }}/{{ task_format.rate_currency }}</span>
        <span v-else>--</span>
      </el-button>
    </el-popover>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: 'TaskHonorariumEdit',
  props: {
    task: {
      type: Object,
    },
  },
  data() {
    return {
      popoverVisible: false,
      visible: false,
      form: {},
      task_format: {
        rate: null,
        rate_currency: null,
      },
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
    ]),
    has_rate() {
      return typeof this.task_format.rate === 'number'
    },
  },

  watch: {
    'task': {
      handler(newVal) {
        if (newVal) {
          this.getTaskRate()
        }
      },
    },
  },
  mounted() {
    this.getTaskRate()
  },
  methods: {
    initTaskRate() {
      this.form = {
        rate: this.task_format.rate,
        rate_currency: this.task_format.rate_currency,
      }
      this.visible = true
    },

    getTaskRate() {
      this.task_format.rate = this.task.rate
      this.task_format.rate_currency = this.task.rate_currency
    },

    cancelEdit() {
      this.popoverVisible = false
    },

    updateTaskRate() {
      const params = {
        project_id: this.task.project_id,
        id: this.task.id,
        rate: this.form.rate,
        rate_currency: this.form.rate_currency,
      }
      params.rate = typeof params.rate === 'number' ? params.rate : null
      return ProjectAPI.updateTask(params)
        .then(() => {
          this.$set(this.task_format, 'rate', this.form.rate)
          this.$set(this.task_format, 'rate_currency', this.form.rate_currency)
          this.$message.success('success')
        }).finally(() => {
          this.popoverVisible = false
        })
    },
  },
}
</script>

<style scoped>

</style>
