<template>
  <div class="unread-badge">
    <el-badge :value="count" size="mini" :max="99" type="primary" />
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    count: [Number, String],
  },
}
</script>

<style scoped lang="scss">
.unread-badge {
  font-weight: normal;
  display: inline-block;

  .el-badge {
    display: flex;
  }

  ::v-deep .el-badge__content--primary {
    background-color: #1989fa;
  }

  ::v-deep .el-badge__content {
    height: unset;
  }

  ::v-deep sup {
    top: 0
  }
}
</style>
