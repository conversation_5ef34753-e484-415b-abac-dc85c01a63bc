<template>
  <el-button type="primary" :disabled="expertDisabled" :loading="loading" @click="exportToDB(CNDBProjectID)">
    <svg-icon icon-class="link" />
    Add to DB Project
  </el-button>
</template>

<script>
import ExportMixins from './mixins'

export default {
  name: 'ExpertToCNDBProject',
  mixins: [ExportMixins],
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    expertDisabled() {
      if (this.selectedAdvisors.length > 1 || this.advisorDisabled) return true
      const client = this.selectedAdvisors[0].client
      const task = this.selectedAdvisors[0]
      const advisor = this.selectedAdvisors[0].advisor
      const is_cn_advisor = advisor.is_cn
      // const is_GUS_client = client.am_team_id === 2
      const client_compliance_need_approved = client.compliance_status !== 'APPROVED'
      const client_need_approved = task.compliance_approve_policy_jit !== 'NO_NEED'
      const need_client_select = task.client_contact_status !== 'APPROVED'
      const task_need_approved = client_need_approved && !task.compliance_passed_jit
      const client_close_workflow = client.compliance_preference && client.compliance_preference.rule &&
        client.compliance_preference.rule.compliance_rules &&
        client.compliance_preference.rule.compliance_rules.cn_db_workflow_close
      // Add To DB Project按钮
      // 只有法务审核通过的才可以（或者客户不需要)，
      // 只有Recruited by US KM: Yes的专家可以
      // 客户Compliance Preference中添Close Workflow 勾选不可以
      // GUS 客户不可以

      /* 兼容新加坡使用该系统，中国客户的项目中专家签过访谈TC就可添加到中国项目*/
      const is_cn_client = client.am_team && client.am_team.name !== 'US AM Team'
      const advisor_signed_tc = advisor.tc_term_valid.CONSULTATION

      if (is_cn_client) {
        return !advisor_signed_tc || !this.CNDBProjectID || is_cn_advisor
      } else {
        return client_compliance_need_approved || need_client_select || is_cn_advisor || task_need_approved ||
          client_close_workflow || !this.CNDBProjectID
      }
    },

    CNDBProjectID() { // 替换为store中的project
      if (!this.selectedAdvisors.length) return 0
      return this.project.ndb_id
    },
    advisorDisabled() {
      /* us db专家 && 没有签过 consultation tc*/
      return !this.selectedAdvisors[0].advisor.is_cn && !this.selectedAdvisors[0].advisor.tc_term_valid.CONSULTATION
    },
  },
  methods: {},
}
</script>

<style scoped>

</style>
