import TcAPI from '@/api/tc'
import { AppHttpService } from '@/utils/request'
import _ from 'lodash'

export default {
  props: {
    selectedAdvisors: Array,
    project: Object,
  },
  data() {
    return {
      loading: false,
      cn_db_tc_id: 0,
      advisor: null,
      cn_db_url: import.meta.env.VITE_APP_URL_DB + '/#/consultant/newlife/create?',
      company_type: [
        { label: 'PRIVATE', id: 1 },
        { label: 'LISTED', id: 2 },
        { label: 'PART_OF_LISTED_GROUP', id: 3 },
      ],
    }
  },
  computed: {
    // 法务规则控制专家导入到CN DB时，联系方式是否带入
    CNDBWorkflowExcludeAdvisorContacts() {
      const client = this.selectedAdvisors[0].client
      const compliance_rules = client.compliance_preference?.rule?.compliance_rules
      const exclude_contact_info = compliance_rules && compliance_rules.cn_db_workflow?.exclude_advisor_contact_info

      return exclude_contact_info
    },
  },
  methods: {
    getTcIdInCNDb(latest_signed_tc) {
      // 匹配不到tc  在中国DB 默认
      if (!latest_signed_tc) return
      return TcAPI.getTcIdInCNDb(latest_signed_tc.tc_template_id).then(data => {
        this.cn_db_tc_id = data ? data.cn_tc_id : 0
        // if (!data) {
        //   // this.delay_time = 3000
        //   this.$notify({
        //     title: 'Tips',
        //     message: 'The TC signed by the expert, for which no equivalent TC was found in the CN DB.',
        //     type: 'warning',
        //     duration: 0,
        //   })
        //   return Promise.reject()
        // } else {
        //   this.cn_db_tc_id = data.cn_tc_id
        // }
      })
    },

    async getConsultantInfo(id) {
      this.advisor = await AppHttpService({
        url: '/advisors/' + id,
        method: 'get',
        params: {
          extra: 'bank_account,contact_infos_without_mosaic',
        },
      })
    },

    exportToDB(add_to_project_id = 0) {
      const item = _.cloneDeep(this.selectedAdvisors[0])

      this.loading = true
      return Promise.all(
        [
          this.getTcIdInCNDb(item.advisor.latest_signed_tc),
          this.getConsultantInfo(item.advisor_id)])
        .then(() => {
          let has_current
          const work_experience_list = _.sortBy(
            item.advisor.jobs,
            'end_date',
          ).map(item => {
            if (item.is_current) {
              item.is_end_date_current = 1
              item.end_date = 'Current'
              has_current = true
            } else {
              item.is_end_date_current = 0
              item.end_date = item.end_date ? {
                YYYY: item.end_date.split('-')[0],
                MM: item.end_date.split('-')[1],
              } : ''
            }
            return {
              end_date: item.end_date,
              is_end_date_current: item.is_end_date_current,
              start_date: item.start_date ? {
                YYYY: item.start_date.split('-')[0],
                MM: item.start_date.split('-')[1],
              } : '',
              company_name: item.company.name,
              position_name: item.position,
            }
          })

          const work_experience = has_current
            ? work_experience_list.filter(
              item => item.is_end_date_current === 1)
            : work_experience_list.splice(-1, 1)

          /* 使用无模糊版联系方式 */
          const contact_infos = this.advisor['contact_infos_without_mosaic'] ||
            []
          const email = contact_infos.find(contact => contact.type === 'EMAIL')
          const mobile = contact_infos.find(contact => contact.type === 'PHONE')
          const linkedin = contact_infos.find(
            contact => contact.type === 'LINKEDIN_URL')

          let bank_account
          if (this.advisor.bank_account && this.advisor.bank_account.account) {
            bank_account = {}
            for (const key in this.advisor.bank_account.account) {
              if (this.advisor.bank_account.account[key] &&
                !['id', 'create_at', 'update_at', 'advisor_id'].includes(key)) {
                bank_account[key] = this.advisor.bank_account.account[key]
              }
            }
            const account_type = this.$store.state.options.account_type_options.find(
              item => item.value === bank_account['account_type'])
            if (account_type) bank_account['account_type'] = account_type.id
            const receiver_type = this.$store.state.options.receiver_type_options.find(
              item => item.value === bank_account['receiver_type'])
            if (receiver_type) bank_account['receiver_type'] = receiver_type.id
          }

          const params = {
            is_cn_client: item.client.am_team && item.client.am_team.name !== 'US AM Team',
            add_to_project_id: add_to_project_id,
            us_task_id: item.id,
            origin: import.meta.env.VITE_APP_TO_CN_DB_ORIGIN,
            tsid: item.advisor.tsid,
            us_db_id: item.advisor_id,
            /* 签过 访谈相关的tc(default tc)*/
            is_advisor: item.advisor.tc_term_valid.CONSULTATION,
            // advisor leads all true
            is_us: true,
            us_consultant_id: item.advisor_id,
            firstname: item.advisor.firstname,
            lastname: item.advisor.lastname,
            rate: item.advisor.rate || 0,
            rate_currency: item.advisor.rate_currency,
            location: item.advisor.location?.name,
            location_cn: item.advisor.location?.cn_name,
            company: item.advisor_profile.company?.name,
            position: item.advisor_profile.position,
            company_type: this.formatCompanyType(item.advisor_profile.company),
            work_experience,
            // work_experience_count: 0,
            // should not take employment history until CapIQ is integrated with our system(us request)
            work_experience_count: work_experience.length,
            allow_no_contact_info: this.CNDBWorkflowExcludeAdvisorContacts ? 1 : 0,
            email: email && !this.CNDBWorkflowExcludeAdvisorContacts ? email.value : '',
            mobile: mobile && !this.CNDBWorkflowExcludeAdvisorContacts ? mobile.value : '',
            linkedin: linkedin ? linkedin.value : '',
            background: item.advisor.background,
            tc_id: this.cn_db_tc_id,
            tc_update_time: item.advisor.latest_signed_tc ? Date.parse(
              item.advisor.latest_signed_tc.respond_time) /
              1000 : null,
            bank_account,
          }

          // setTimeout(() => {
          const newWin = window.open('about:blank')
          newWin.location.href = this.cn_db_url + this.serialize(params)
          // }, this.delay_time)
        })
        .finally(() => {
          this.loading = false
        })
    },

    serialize(obj, prefix) {
      const str = []
      let p
      for (p in obj) {
        if (Object.hasOwnProperty.call(obj, p)) {
          const k = prefix ? prefix + '[' + p + ']' : p
          const v = obj[p]
          str.push((v !== null && typeof v === 'object')
            ? this.serialize(v, k)
            : encodeURIComponent(k) + '=' + encodeURIComponent(v))
        }
      }
      return str.join('&')
    },

    formatCompanyType(company) {
      if (!company || !company.type) return 0
      const type = this.company_type.find(item => item.label === company.type)
      return type.id || 0
    },
  },
}
