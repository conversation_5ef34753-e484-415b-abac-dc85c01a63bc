<template>
  <el-button type="primary" :disabled="expertDisabled" :loading="loading" @click="exportToDB">
    <svg-icon icon-class="link" />
    Add to DB
  </el-button>
</template>

<script>
import ExportMixins from './mixins'

export default {
  name: 'ExportToDB',
  mixins: [ExportMixins],

  data() {
    return {}
  },
  computed: {
    // cn advisor 不用导入CN DB
    expertDisabled() {
      const client = this.selectedAdvisors[0].client
      const client_close_workflow = client.compliance_preference && client.compliance_preference.rule &&
        client.compliance_preference.rule.compliance_rules &&
        client.compliance_preference.rule.compliance_rules.cn_db_workflow_close
      // 审计 要求
      const has_cn_advisor = this.selectedAdvisors.filter(item => item.advisor.is_cn).length > 0
      const no_signed_tc = this.selectedAdvisors.filter(item => !item.advisor.tc_term_valid.CONSULTATION).length > 0

      return this.selectedAdvisors.length > 1 || no_signed_tc || has_cn_advisor || client_close_workflow
    },
  },
}
</script>

<style scoped>

</style>
