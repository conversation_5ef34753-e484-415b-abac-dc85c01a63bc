<template>
  <div>
    <el-button type="primary" size="mini" class="copy-btn" plain @click="copyLink">Copy Link</el-button>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { copyUtil } from '@/utils/tool'

export default {
  name: 'SurveyLinkCopy',
  props: {
    taskData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    copyLink() {
      const params = {
        task_id: this.taskData.id,
      }
      return ProjectAPI.createSurveyLink(params).then((data) => {
        const value = `<a :href=${data.link_jit}>${data.link_jit}</a>`
        copyUtil(value, true)

        if (document.execCommand('copy')) {
          document.execCommand('copy')
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        } else {
          this.$message({
            message: '<PERSON><PERSON> failed',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-button--mini, .el-button--mini.is-round {
  padding: 3px 4px;
}
</style>
