<template>
  <div>
    <el-tooltip
      class="item"
      :disabled="!advisorInBlacklist(taskItem)"
      effect="dark"
      content="Unsubscribed"
      placement="top">
      <div class="inline-block">
        <el-button
          :loading="link_loading"
          :disabled="advisorInBlacklist(taskItem)"
          type="text"
          icon="el-icon-link"
          class="f-14"
          @click="dialogShow" />
      </div>
    </el-tooltip>

    <el-dialog
      :visible.sync="dialogVisible"
      placement="top"
      append-to-body
      width="1300px"
      title="Copy Link"
      @closed="resetData">
      <el-alert
        v-if="needInvestmentTarget"
        type="error"
        show-icon
        class="mb-8">
        <div class="flex align-items-center">You need to show Investment Target in CA, please fill in the project.</div>
      </el-alert>
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="id"
      >
        <el-table-column :label="expertType" width="120">
          <template #default="scope">
            <router-link-advisor :data="scope.row.task.advisor" />
            <el-tooltip
              v-if="advisorInfoDisabled || scope.row.task.advisor.tc_expired"
              effect="dark"
              placement="top">
              <div slot="content">
                <div v-if="!scope.row.advisor_has_email">No Email</div>
                <div v-if="scope.row.task.advisor.tc_expired">TC Expired</div>
                <div v-if="!scope.row.advisor_has_current_job">No Current Job</div>
              </div>
              <el-link type="danger" :underline="false" icon="el-icon-warning" style="vertical-align: text-bottom;" />
            </el-tooltip>
            <el-tooltip
              v-if="!scope.row.advisor_has_phone"
              effect="dark"
              content="No Phone"
              placement="top">
              <el-link type="warning" :underline="false" icon="el-icon-warning" style="vertical-align: text-bottom;" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Email Template" min-width="250">
          <template #header>
            <span class="mr-normal">Link type</span>
          </template>
          <template #default>
            <div class="flex">
              <el-select
                v-model="select_tag"
                class="w-100">
                <el-option
                  v-for="(tag, index) in options.tags"
                  :key="index"
                  :label="tag.label"
                  :value="tag.value" />
              </el-select>
              <el-tooltip
                v-if="!existSQ && hasSqTemplate"
                class="ml-8"
                effect="dark"
                content="No SQ"
                placement="top">
                <el-link type="danger" :underline="false" icon="el-icon-warning" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="hasTcTemplate" :key="Math.random()+'TC'" width="460">
          <template #header>
            <span>TC</span>
          </template>
          <template #default="scope">
            <tc-search
              v-model="scope.row.advisor_tc.tc"
              :init-request="false"
              :init-list="options.tc"
              @change="(val) => {handleTcChange(val, scope.row)}" />
            <el-select v-model="scope.row.advisor_tc.tc_template_id" class="w-85px">
              <slot v-if="scope.row.advisor_tc.tc">
                <el-option
                  v-for="item in scope.row.advisor_tc.tc.templates"
                  :key="item.id"
                  :label="item.locale"
                  :value="item.id" />
              </slot>
            </el-select>
            <el-input
              v-model="scope.row.advisor_tc.rate"
              type="number"
              :step="150"
              :min="0"
              class="w-75px" />
            <el-select v-model="scope.row.advisor_tc.rate_currency" class="w-75px">
              <el-option
                v-for="item in options.currency"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column v-if="hasCaTemplate" :key="Math.random()+'CA'" label="CA" width="200">
          <template #header>
            CA
            <el-tooltip v-if="!options.ca.length" effect="dark" content="No CA" placement="top">
              <el-link type="danger" :underline="false" icon="el-icon-warning" />
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-select
              v-model="scope.row.task_ca.inquiry_id"
              filterable
              class="w-100">
              <el-option
                v-for="item in options.ca"
                :key="item.id"
                :label="item.name"
                :value="item.id" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column v-if="hasCaTemplate" :key="Math.random()+'CA_LANG'" label="Language" width="130">
          <template #header>
            Language
          </template>
          <template #default="scope">
            <el-select
              v-model="scope.row.task_ca.inquiry_branch"
              value-key="id"
              class="w-100">
              <el-option
                v-for="item in options.caLang"
                :key="item.id"
                :label="item.customName"
                :value="item" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column v-if="hasSqTemplate" :key="Math.random()+'SQ'" label="SQ" width="200">
          <template #header>
            Screening Questions
            <el-tooltip v-if="!options.sq || !options.sq.length" effect="dark" content="No SQ" placement="top">
              <el-link type="danger" :underline="false" icon="el-icon-warning" />
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-select
              v-model="scope.row.sq_branch_id"
              filterable>
              <el-option
                v-if="scope.row.task.latest_sq && scope.row.task.latest_sq.contains_one_off_questions"
                label="One off screening of this task"
                value="task_origin_sq"
              />
              <el-option
                v-for="item in options.sq"
                :key="item.id"
                :label="item.title"
                :value="item.id" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="" width="">
          <el-button type="success" class="ma-3px" :disabled="copyDisabled" @click="copyLink">Copy Link</el-button>
          <el-tooltip
            effect="dark"
            :disabled="!needChineseEnglishCA"
            content="Send CA to CN Supporter must use EN & ZH language."
            placement="top">
            <div class="inline-block">
              <el-button
                v-if="showSendCALinkBtn"
                type="primary"
                class="ma-3px"
                :disabled="SendCALinkBtnDisabled"
                :loading="send_mail_loading"
                @click="sendCaLinkEmail">Send CA Link to Supporter</el-button>
            </div>
          </el-tooltip>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import TcSearch from '@/components/SelectRemoteSearch/TcSearch/index'
import InquiryAPI from '@/api/inquiry'
import ProjectAPI from '@/api/project'
import ConsultationAPI from '@/api/consultant'
import TcAPI from '@/api/tc'
import Outsource from '@/api/outsource'
import moment from 'moment-timezone'
import Mixin from './SendEmail/mixin_v2'
import { copyUtil } from '@/utils/tool'

export default {
  name: 'WorkflowLink',
  components: { RouterLinkAdvisor, TcSearch },
  mixins: [Mixin],
  props: {
    taskItem: Object,
    expertType: {
      type: String,
      default: 'Advisor',
    },
  },
  data() {
    return {
      tableData: [],
      send_mail_loading: false,
      loading: false,
      link_loading: false,
      dialogVisible: false,
      existSQ: false,
      select_tag: null,
      options: {
        tc: [],
        ca: [],
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        caLang: [],
        tags: [
          { label: 'T&C', value: 'TC' },
          { label: 'T&C + Availability', value: 'TC,AG' }],
      },
      lang_type: [
        { value: 'en-US', label: 'English' },
        { value: 'zh-CN', label: 'Chinese' },
        { value: 'en-zh', label: 'EN & ZH' },
      ],
    }
  },
  computed: {
    copyDisabled() {
      const sq_disabled = this.hasSqTemplate && !!this.tableData.find(item => !item.sq_branch_id)
      const ca_disabled = this.hasCaTemplate &&
        !!this.tableData.find(item => !item.task_ca.inquiry_branch || !item.task_ca.inquiry_id)
      const tc_disabled = this.hasTcTemplate && !!this.tableData.find(
        item => !item.advisor_tc.rate_currency || !item.advisor_tc.tc || !item.advisor_tc.tc_template_id ||
          !/^\d+(?=\.{0,1}\d+$|$)/.test(item.advisor_tc.rate))
      return this.advisorInfoDisabled || !this.select_tag || sq_disabled || tc_disabled || ca_disabled || this.needInvestmentTarget || this.advisorInfoDisabled
    },
    needInvestmentTarget() {
      return this.hasCaTemplate && this.checkClientRuleShowInvestmentTargetToAdvisor() &&
        this.project.investment_target_company_ids.length < 1
    },
    showInvestmentTargetInPortal() {
      return [1189, 2].includes(parseInt(this.project.client_id)) && !this.isSGDB
    },
    showInvestmentTarget() {
      if (this.checkClientRuleShowInvestmentTargetToAdvisor() &&
        this.project.investment_target_company_ids.length > 0) {
        return this.project.investment_target_companies.map(item => item.name).join(', ')
      } else {
        return null
      }
    },
    existNoTc() {
      return this.tableData.findIndex(item => !item.task.advisor.tc) > -1
    },
    hasSqTemplate() {
      return this.select_tag && this.select_tag.includes('SQ')
    },
    hasCaTemplate() {
      return this.select_tag && this.select_tag.includes('CA')
    },
    hasTcTemplate() {
      return this.select_tag && this.select_tag.includes('TC')
    },

    advisorInfoDisabled() {
      return this.tableData.some(
        item => (!item.advisor_has_email || !item.advisor_has_current_job) && item.task.task_outsource_status !== 'IMPORTED')
    },

    showSendCALinkBtn() {
      return ['IMPORTED'].includes(this.taskItem?.task_outsource_status) && this.select_tag && this.select_tag === 'CA'
    },
    needChineseEnglishCA() {
      return this.taskItem?.task_outsource_info?.outsource_advisor_from === 'CN' && this.tableData[0]?.task_ca?.inquiry_branch?.locale !== 'en-zh'
    },
    SendCALinkBtnDisabled() {
      return this.taskItem?.client_contact_status !== 'APPROVED' || this.needChineseEnglishCA
    },
  },
  methods: {
    dialogShow() {
      this.link_loading = true
      return Promise.all([this.getCaList(), this.getTcList(), this.checkSqExisted()])
        .then(() => {
          this.initTagsOption()
          this.tableData = [this.taskItem].map(task => {
            const res = {
              tags: null,
              task_ca: {
                inquiry_branch: null,
                inquiry_id: this.options.ca.length ? this.options.ca[0].id : null,
              },
              sq_branch_id: undefined,
              advisor_tc: {
                tc: 0,
                tc_template_id: 0,
                rate: task.advisor.rate || task.advisor.default_rate || null,
                rate_currency: task.advisor.rate_currency || 'USD',
              },
              advisor_has_email: !!task.advisor.contact_infos.find(item => item.type === 'EMAIL'),
              advisor_has_phone: !!task.advisor.contact_infos.find(item => item.type === 'PHONE'),
              advisor_has_current_job: !!task.advisor.current_job,
              task,
            }
            // 优先选择 task 中含有一次性新增问题的问卷
            if (task.latest_sq && task.latest_sq.contains_one_off_questions) {
              res.sq_branch_id = 'task_origin_sq'
            }
            // 初始化默认选择的 CA TC 和语言版本
            if (this.options.ca.length) {
              const ca_lang = this.options.caLang.find(item => item.locale === task.advisor.locale)
              res.task_ca.inquiry_branch = ca_lang || this.options.caLang[0]
            }
            if (this.options.tc.length) {
              res.advisor_tc.tc = this.options.tc.find(tc => tc.is_current)
              let template = res.advisor_tc.tc.templates.find(template => template.locale === task.advisor.locale)

              if (!template) {
                template = res.advisor_tc.tc.templates[0]
              }

              res.advisor_tc.file_name = template.file_name
              res.advisor_tc.file_url = template.file_url
              res.advisor_tc.tc_template_id = template.id
            }
            return res
          })
          this.dialogVisible = true
        }).finally(() => {
          this.link_loading = false
        })
    },

    sendCaLinkEmail() {
      this.send_mail_loading = true
      const item = this.tableData[0]
      const params = {
        'type': 'ADVISOR_PRE_CALL',
        'is_display_investment_target': false,
        'task_id': item.task.id,
        'advisor_id': item.task.advisor_id,
        'expect_steps': ['CA'],
        'task_ca': {
          inquiry_id: item.task_ca.inquiry_id,
          inquiry_branch_id: item.task_ca.inquiry_branch.id,
        },
      }

      return Outsource.sendCaLinkToSupport(params)
        .then(() => {
          this.$message.success('Send email successfully')
        })
        .finally(() => {
          this.send_mail_loading = false
        })
    },

    copyLink() {
      const item = this.tableData[0]
      if (this.select_tag.includes('CONSENT_RECORD')) {
        this.copyRecordingConsent(item)
        return
      }
      const params = {
        portal: {
          type: 'ADVISOR_PRE_CALL',
          is_public_portal: true,
          is_display_investment_target: !!this.showInvestmentTarget || this.showInvestmentTargetInPortal,
          task_id: item.task.id,
          advisor_id: item.task.advisor_id,
          task_ca: this.select_tag.includes('CA') ? {
            inquiry_id: item.task_ca.inquiry_id,
            inquiry_branch_id: item.task_ca.inquiry_branch.id,
          } : undefined,
          method: 'PORTAL',
          advisor_tc: this.select_tag.includes('TC') ? {
            tc_id: item.advisor_tc.tc.id,
            rate: item.advisor_tc.rate,
            tc_template_id: item.advisor_tc.tc_template_id,
            rate_currency: item.advisor_tc.rate_currency,
            locale: Object.entries(item.advisor_tc.tc.locale_template_id_map)
              .find(arr => arr[1] === item.advisor_tc.tc_template_id)?.[0],
          } : undefined,
          expect_steps: this.select_tag.split(','),
        },
      }

      /* sq 处理 */
      if (this.select_tag.includes('SQ')) {
        if (item.sq_branch_id === 'task_origin_sq') {
          params.portal.sq_id = item.task.latest_sq.id
        } else {
          params.portal.sq_branch_id = item.sq_branch_id || undefined
        }
      }

      return ConsultationAPI.sendConsultantPortal(params).then(data => {
        copyUtil(data.link_jit)
          .then((val) => {
            this.$message({
              message: 'Copy successful',
              type: 'success',
            })
          })
          .catch(() => {
            this.$message({
              message: 'Copy failed',
              type: 'error',
            })
          })
      })
    },

    copyRecordingConsent(item) {
      return ProjectAPI.getAdvisorRecordingPortalLink(item.task.id)
        .then(data => {
          copyUtil(data.link_jit)
            .then((val) => {
              this.$message({
                message: 'Copy successful',
                type: 'success',
              })
            })
            .catch(() => {
              this.$message({
                message: 'Copy failed',
                type: 'error',
              })
            })
        })
    },

    initTagsOption() {
      const common_options = [{ label: 'T&C + Availability + Screeners', value: 'TC,AG,SQ' }]
      if (this.options.ca.length) {
        common_options.push({ label: 'T&C + Availability + Screeners + CA', value: 'TC,AG,SQ,CA' })
      }
      if (this.expertType === 'Leads') {
        this.options.tags = this.options.tags.concat(common_options)
      } else {
        this.options.tags.push({ label: 'T&C + Screeners', value: 'TC,SQ' })
        this.options.tags = this.options.tags.concat(common_options)
        this.options.tags.push({ label: 'Availability only', value: 'AG' })
        if (this.taskItem.advisor.tc_term_valid['CONSULTATION']) {
          const signed_tc_list = [
            { label: 'Screeners only', value: 'SQ' },
            { label: 'Client Agreement only', value: 'CA' },
            { label: 'Availability + Screeners', value: 'AG,SQ' },
          ]
          if (this.options.ca.length) {
            signed_tc_list.push({ label: 'Availability + Screeners + CA', value: 'AG,SQ,CA' })
          }
          this.options.tags = this.options.tags.concat(signed_tc_list)
        }
      }
      this.options.tags.push({ label: 'Consent to Record', value: 'CONSENT_RECORD' })

      if (!this.options.tags.includes(item => item.value === 'CA') && this.taskItem?.task_outsource_status === 'IMPORTED') {
        this.options.tags.unshift({ label: 'Client Agreement only', value: 'CA' })
      }
    },
    async getCaList() {
      const params = {
        types: 'PRE_CALL_CA',
        extra: 'branches,branches.questions,branches.name',
        'client.page': 0,
        client_id: this.taskItem.client_id,
      }

      // 为其他DB导出专家的项目 发送项目级别的CA
      if (this.project.exported_to === 'CN') {
        params.project_id = this.project.id
        params.client_id = null
      }

      const data = await InquiryAPI.getInquiryList(params)
      data.list.forEach(item => {
        item.branches.forEach(branch => {
          branch.questions.forEach(question => {
            if (!question.answer) question.answer = { result: '' }
            if (question.sub_questions) {
              question.sub_questions.forEach(sub => {
                if (!sub.answer) sub.answer = { result: '' }
              })
            }
          })
        })
      })
      this.options.ca = data['list']
      if (data['list'].length) {
        const list = data['list'][0].branches.filter(item => item.is_default_using)
        let language_list = []
        if (this.taskItem?.task_outsource_info?.outsource_advisor_from === 'CN') {
          language_list = list.filter(item => item.locale === 'en-zh')
        } else {
        // 审计：系统内GKM不操作 禁用 英文版以外的CA
          language_list = list.filter(item => item.locale === 'en-US')
        }
        this.options.caLang = language_list.map((item) => {
          item.customName = this.lang_type.find((type) => type.value === item.locale)['label']
          return item
        })
      } else {
        this.options.caLang = []
      }
    },
    async checkSqExisted() {
      const data = await ProjectAPI.getProjectInquiryBranches(this.taskItem.project_id)
      this.options.sq = data.sq ? data.sq.branches : []
      this.existSQ = data.sq && data.sq.branches && data.sq.branches.length > 0
    },
    async getTcList() {
      if (!this.options.tc.length) {
        const params = {
          is_current: true,
          type: 'DEFAULT',
        }
        this.options.tc = await TcAPI.getTCList(params)
      }
    },

    handleTcChange(val, row) {
      let template = this.options.tc.find(template => template.locale === row.advisor.locale)

      if (!template) {
        template = this.options.tc[0]
      }
      row.advisor_tc.file_name = template.file_name
      row.advisor_tc.file_url = template.file_url
      row.advisor_tc.tc_template_id = template.id
    },

    /**
     * 至少工作 1 年
     * 需求: Can we require at least 12-months of employment for advisors for TC to be sent to them?
     * @returns {boolean}
     */
    has12MonthsOfEmployment(task) {
      return task.advisor.jobs.some(item => {
        /* 开始日期距今 > 1y */
        return !!item.start_date && moment(item.start_date).add(1, 'year').isBefore()
      })
    },

    resetData() {
      this.options.tags = [
        { label: 'T&C', value: 'TC' },
        { label: 'T&C + Availability', value: 'TC,AG' }]
    },
  },
}
</script>

<style scoped>
.w-75px {
  width: 75px;
}

::v-deep.el-button {
  margin-left: 0;
}
.w-85px {
  width: 85px;
}
</style>
