<template>
  <el-popover
    placement="top"
    width="450"
    :open-delay="300"
    trigger="hover"
    @show="getOutreachRecord">
    <div v-loading="loading" style="min-height:20px;">
      <div v-for="(i,index) in records" :key="index">
        <span>Sent: {{ formatTime(i.create_at) }}</span>
        <span class="pl-5">| Opened:
          <span v-if="i.is_opened">Yes | Opened on: {{ formatTime(i.opened_at) }}</span>
          <span v-else>No</span>
        </span>
        <span v-if="i.is_clicked" class="pl-5">| Clicked: Yes | Clicked on:{{ formatTime(i.clicked_at) }}
        </span>
      </div>
    </div>
    <div slot="reference">
      <span v-if="isOpened" class="success">Yes</span>
      <slot v-else>
        <span v-if="item.email_records.length>0">No</span>
      </slot>
    </div>

  </el-popover>
</template>

<script>
import ProjectAPI from '@/api/project'
import _ from 'lodash'
import Filters from '@/utils/filters'

export default {
  name: 'OutreachOpened',
  props: { item: Object },
  data() {
    return {
      records: null,
      loading: false,
    }
  },
  computed: {
    isOpened() {
      const outreach_records = this.item.email_records.filter(item => item.email_content_type === 'PROJECT_OUTREACH')
      return outreach_records.length > 0 && outreach_records.filter(
        item => item.advisor_tracking && ['OPEN', 'CLICK'].includes(item.advisor_tracking.status)).length > 0
    },
  },
  methods: {
    formatTime(time) {
      if (time) {
        return Filters.momentFormat(time, 'MM/DD/YYYY') + ' at ' + Filters.momentFormat(time, 'h:mm A')
      }
    },
    getOutreachRecord() {
      if (this.item.email_records.length < 1) {
        this.records = 'Initial'
        return
      }
      this.loading = true
      const params = {
        project_id: this.item.project_id,
        params: {
          ids: this.item.id,
          extra: 'email_records.advisor_tracking.send_grid_events',
        },
      }
      return ProjectAPI.getTasksDetail(params).then(data => {
        const outreach_records = data.list[0].email_records.filter(
          item => item.email_content_type === 'PROJECT_OUTREACH')

        const format_list = outreach_records.map(item => {
          item.is_opened = false
          item.is_clicked = false
          if (item.advisor_tracking && item.advisor_tracking.send_grid_events) {
            item.is_opened = ['OPEN', 'CLICK'].includes(item.advisor_tracking.status)
            item.is_clicked = item.advisor_tracking.status === 'CLICK'
            item.clicked_at = item.advisor_tracking.send_grid_events.find(i => i.event === 'click')?.create_at
            item.opened_at = item.advisor_tracking.send_grid_events.find(i => i.event === 'open')?.create_at || item.clicked_at
          }

          return item
        })

        this.records = _.orderBy(format_list, 'create_at')
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
