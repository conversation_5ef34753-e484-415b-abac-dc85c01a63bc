<template>
  <div class="inline-block">
    <el-popover
      v-model="popoverVisible"
      placement="top"
      width="330">
      <div v-if="visible" class="mt-8">
        <time-zone-select
          v-model="selectTimeZone"
          v-loading="init_loading"
          :default-options="defaultTimeZoneOptions"
          element-loading-spinner="el-icon-loading"
          class="inline-block" />
        <el-button type="text" size="mini" class="ml-3" :loading="btn_loading" @click="copy">Copy</el-button>
        <el-button type="text" size="mini" class="info" @click="popoverVisible = false">Cancel</el-button>
      </div>
      <el-button
        slot="reference"
        type="primary"
        icon="el-icon-copy-document"
        @click="initTimeZone()">Copy Availability
      </el-button>
    </el-popover>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import Filters from '@/utils/filters'
import moment from 'moment-timezone'
import _ from 'lodash'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
import { copyUtil } from '@/utils/tool'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'

export default {
  name: 'CopyAvailability',
  components: { TimeZoneSelect },
  mixins: [TaskActionsMixin],
  props: {
    project: {
      type: Object,
      default: () => { return {} },
    },
    tasks: {
      type: Array,
      default: () => { return [] },
    },
  },

  data() {
    return {
      init_loading: false,
      btn_loading: false,
      visible: false,
      popoverVisible: false,
      selectTimeZone: undefined,
      task_action_type: 'Copy Availability',
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
    }),
    isConsultingFirm() {
      return this.project.client.type === 'CONSULTING_FIRM'
    },

    defaultTimeZoneOptions() {
      return this.isSGDB ? ['Asia/Singapore', 'Asia/Shanghai', 'America/New_York'] : [
        'America/New_York',
        'America/Chicago',
        'America/Denver',
        'America/Los_Angeles']
    },
  },
  methods: {
    async initTimeZone() {
      this.visible = true
      this.init_loading = true
      await this.getTasksByIDS()
      await this.setDefaultTimeZone()
      this.init_loading = false
    },

    advisorSchedulesFormat(advisor_schedules) {
      const validSchedules = advisor_schedules.filter(
        item => moment(item.end_time).valueOf() >= moment().valueOf())
      const break_date = this.processContinuousTime(validSchedules)

      if (break_date.length > 0) {
        const sort_list = _.orderBy(break_date, 'start_time', 'asc')
        const list = sort_list.map(item => {
          item.date_format = moment.tz(item.start_time, this.selectTimeZone).format('dddd M/D:')
          return item
        })
        const format_list = _.groupBy(list, 'date_format')
        return _.mapValues(format_list, (item) => {
          item = _.orderBy(item, 'start_time', 'asc')
          return item
        })
      } else {
        return []
      }
    },
    /* 处理跨天的连续时间 */
    processContinuousTime(data) {
      const result_data = []
      data.forEach(item => {
        const start_date = item.start_time.split('T')[0]
        const end_date = item.end_time.split('T')[0]
        const daysDifference = moment(item.end_time).diff(moment(item.start_time), 'day')
        if (daysDifference > 0) {
          for (let i = 0; i <= daysDifference; i++) {
            const obj = _.cloneDeep(item)
            if (i === 0) {
              obj.end_time = start_date + 'T23:59:59Z'
              obj.start_time = item.start_time
            } else if (i === daysDifference) {
              obj.start_time = end_date + 'T00:00:00Z'
              obj.end_time = item.end_time
            } else {
              obj.start_time = moment(start_date).add(i, 'day').format('YYYY-MM-DD') + 'T00:00:00Z'
              obj.end_time = moment(start_date).add(i, 'day').format('YYYY-MM-DD') + 'T23:59:59Z'
            }
            result_data.push(obj)
          }
        } else {
          result_data.push(item)
        }
      })
      return result_data
    },

    getHtmlStyle(task) {
      let html_style = null
      if (this.isConsultingFirm) {
        const advisor_location = task.advisor.location ? ` - <i>${task.advisor.location.name}<i>` : ''
        html_style = `<b>${this.formatAdvisorName(
          task)}</b><span v-if="task.advisor.location"> ${advisor_location} </span><span style="color:#F56C6C;"> - Availability `
      } else {
        html_style = `<b>${this.formatAdvisorName(task)}</b> <span><b> Availability `
      }
      return html_style
    },

    formatAdvisorName(task) {
      /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
      const client_status_is_not_EXECUTE = task.client?.status !== 'EXECUTE'
      const client_compliance_status_no_pass = task.client?.compliance_status !== 'APPROVED'
      const client_blind_expert_name = task.client?.preference?.blind_expert_names_in_to_client_and_portal
      const project_blind_expert_name = task.project?.blind_expert_profiles
      const need_blind = client_status_is_not_EXECUTE || client_compliance_status_no_pass || client_blind_expert_name ||
        project_blind_expert_name
      return need_blind ? task.display_id : `${task.display_id} ${task.advisor['name_prefix'] ||
      ''} ${task.advisor['full_name']}`
    },
    copy() {
      const element = []
      this.btn_loading = true
      this.tasks.forEach(task => {
        let name_and_zone = this.getHtmlStyle(task)
        let schedules_text

        const advisor_schedules = task.advisor_schedules
          .filter(item => item.creator_type === 'ADVISOR')
        const format_list = this.advisorSchedulesFormat(advisor_schedules)
        if (format_list) {
          name_and_zone += `(${this.selectTimeZone})`
          name_and_zone += this.isConsultingFirm ? ':' : `</b><br>`

          schedules_text = Object.entries(format_list)
            .map(([date, times]) => {
              return `${date} ${times.map(({ start_time, end_time }) => ([
                `${Filters.momentFormatZone(start_time, 'h:mma', this.selectTimeZone)}-${Filters.momentFormatZone(
                  end_time, 'h:mma', this.selectTimeZone)}`,
              ])).join(', ')}`
            }).join('<br/>')
        }

        element.push([name_and_zone].concat(schedules_text).join('\n'))
      })

      const value = `<div style="font-size: 11pt;font-family: Calibri;">${element.join('</span><br/>')}</div>`

      copyUtil(value, true)

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      }
      this.btn_loading = false
      this.visible = false
      this.popoverVisible = false
      // document.body.removeChild(textarea)
    },

    setDefaultTimeZone() {
      const primary_client_contact_zone = this.project.project_client_contacts?.find(
        item => item.client_contact_type === 'PRIMARY')?.client_contact?.zone_id_string

      this.selectTimeZone = this.isSGDB
        ? (this.project.zone_id_string || this.timeZone)
        : (this.project.zone_id_string || primary_client_contact_zone || 'America/New_York')
    },
  },
}
</script>

<style scoped>

</style>
