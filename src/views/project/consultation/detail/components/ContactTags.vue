<template>
  <div>
    <div v-if="data" class="contact-tags-container">
      <template
        v-for="(value, key, index) in contact_options"
      >
        <el-popover
          v-if="value.length"
          :key="index"
          class="contact-tags-item-container"
          placement="top"
          popper-class="contact-tags-popover"
          :append-to-body="true"
          :popper-options="popperOptions"
          trigger="hover">
          <div slot="reference" class="contact-tags-item">
            <svg-icon :icon-class="icon_options[key]" />
          </div>
          <div v-for="(detail_item, detail_index) in value" :key="detail_index" class="contact-tags-detail">
            <div v-if="key === 'LINKEDIN_URL' && data.is_linkedin_premium">The expert has a premium account.</div>
            <span :class="{'link': ['LINKEDIN_URL','EMAIL'].includes(key)}" @click="openToLink(key, value)">{{detail_item}}</span>
          </div>
        </el-popover>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContactTags',
  props: {
    data: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      contact_options: {
        EMAIL: [],
        PHONE: [],
        LINKEDIN_URL: [],
      },
      icon_options: {
        EMAIL: 'email',
        PHONE: 'phone',
        LINKEDIN_URL: 'linkedin',
      },
      popperOptions: {
        boundariesElement: 'scrollParent',
      },
    }
  },

  watch: {
    'data': {
      handler(newVal) {
        this.icon_options['LINKEDIN_URL'] = newVal.is_linkedin_premium ? 'linkedin_premium' : 'linkedin'
        this.contact_options = {
          EMAIL: [],
          PHONE: [],
          LINKEDIN_URL: [],
        }
        this.data.contact_infos.forEach(item => {
          this.contact_options[item.type].push(item.value)
        })
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    openToLink(key, value) {
      if (key === 'LINKEDIN_URL') {
        window.open(value, '_blank')
      }
      if (key === 'EMAIL') {
        const aTag = document.createElement('a')
        aTag.href = `mailto:${value}`
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
  .contact-tags-container {
    display: flex;

    .contact-tags-item-container {
      &:not(:last-child) {
        margin-right: 8px;
      }

      .contact-tags-item {
        cursor: pointer;
        display: inline-block;
      }
    }
  }

  .contact-tags-popover {
    .el-popover__title {
      font-size: 14px
    }

    .contact-tags-detail {
      font-size: 12px;
    }
  }

</style>
