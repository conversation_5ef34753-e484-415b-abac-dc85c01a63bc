<template>
  <div>
    <el-button type="primary" @click="dialogVisible = true">Clone Project</el-button>

    <el-dialog
      :visible.sync="dialogVisible"
      title="Clone Project"
      width="450px">
      <div class="f-14 info mb-8">Select what you would like to copy</div>

      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
        --- Check All ---
      </el-checkbox>
      <el-checkbox-group
        v-model="checkedItems"
        class="checkbox-group-vertical"
        @change="handleCheckedItemsChange">
        <el-checkbox
          v-for="item in options"
          :key="item.value"
          :label="item.value"
          :disabled="disableItem(item)"
          @change="handleSingleItemChange($event, item.value)">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>

      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleCloneProject">Clone</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'CloneProject',
  data() {
    return {
      dialogVisible: false,
      checkAll: false,
      isIndeterminate: false,
      checkedItems: [],
      options: [
        {
          label: 'Task Angles',
          value: 'task_angles',
        }, {
          label: 'Attached Advisors / Client Tasks (Need check \'Task Angles\' first)',
          value: 'attached_advisors',
          disabled: true,
        }, {
          label: 'Attached Leads',
          value: 'attached_leads',
        }, {
          label: 'Screening Questions',
          value: 'screening_questions',
        }, {
          label: 'Client Account',
          value: 'client',
          needData: true,
        }, {
          label: 'Client User (Need check \'Client Account\' first)',
          value: 'client_contacts',
          disabled: true,
          needData: true,
        }, {
          label: 'Client Provided Bridge Link',
          value: 'client_provided_bridge_link',
          needData: true,
        }, {
          label: 'Project Code',
          value: 'code',
          needData: true,
        }, {
          label: 'Project Manager',
          value: 'project_manager',
          needData: true,
        }, {
          label: 'Support Members',
          value: 'support_members',
          needData: true,
        },
      ],
    }
  },
  computed: {
    ...mapState({
      project: state => state.project.data,
    }),
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkedItems = val ? this.options.map(item => item.value) : []
      this.isIndeterminate = false
    },
    handleCheckedItemsChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.options.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.options.length
    },
    handleSingleItemChange(value, target) {
      // 同时取消勾选
      if (!value) {
        let delete_item
        if (target === 'client') delete_item = 'client_contacts'
        if (target === 'task_angles') delete_item = 'attached_advisors'

        if (delete_item) {
          const index = this.checkedItems.findIndex(item => item === delete_item)
          if (index > -1) this.checkedItems.splice(index, 1)
        }
      }

      // 同时勾选
      if (value) {
        if (target === 'client' && !this.checkedItems.includes('client_contacts')) {
          this.checkedItems.push('client_contacts')
        }
        if (target === 'task_angles' && !this.checkedItems.includes('attached_advisors')) {
          this.checkedItems.push('attached_advisors')
        }
      }
    },
    handleCloneProject() {
      const urlQuery = {
        project_id: this.project.id,
        project_name: this.project.name,
        industry: this.project.industry,
        sub_type: this.project.sub_type,
      }
      this.checkedItems.forEach(item => {
        const obj = this.options.find(option => option.value === item)
        if (!obj.needData) {
          urlQuery[item] = true
        } else {
          switch (item) {
            case 'client':
              urlQuery['client'] = {
                name: this.project.client.name,
                id: this.project.client.id,
              }
              break
            case 'client_contacts':
              urlQuery['client_contacts'] = this.project.project_client_contacts
                .map(contact => contact.client_contact_id)
              break
            case 'project_manager':
              urlQuery['project_manager'] = this.handleMembers('PROJECT_MANAGER')
              break
            case 'support_members':
              urlQuery['support_members'] = this.handleMembers('SUPPORT_MEMBER')
              break
            default:
              urlQuery[item] = this.project[item]
              break
          }
        }
      })

      const route = this.$router.resolve({ name: 'CreateProject', query: { clone: JSON.stringify(urlQuery) } })
      window.open(route.href, '_blank')

      this.dialogVisible = false
      this.checkAll = false
      this.isIndeterminate = false
      this.checkedItems = []
    },
    handleMembers(role) {
      return this.project.members
        .filter(member => member.role === role)
        .map(member => ({
          uid: member.uid,
          user: {
            id: member.user.id,
            name: member.user.name,
          },
        }))
    },
    disableItem(item) {
      if (item.value === 'client_contacts') {
        return !this.checkedItems.includes('client') && !this.checkedItems.includes('client_contacts')
      }
      if (item.value === 'attached_advisors') {
        return !this.checkedItems.includes('task_angles') && !this.checkedItems.includes('attached_advisors')
      }
      return false
    },
  },
}
</script>

<style scoped>

</style>
