<template>
  <div class="inline-block">
    <el-tooltip
      v-if="project.bcg_hub_project"
      effect="dark"
      content="No current BCG approved template"
      :disabled="!!approvedBcgTemplate"
      placement="top"
    >
      <el-button v-loading.fullscreen.lock="dialog_loading" element-loading-background="rgba(0, 0, 0, 0.5)" type="primary"
                 :disabled="!approvedBcgTemplate" @click="handleBcgTemplate">
        <svg-icon icon-class="email" />
        Outreach
      </el-button>
    </el-tooltip>
    <el-dropdown v-else trigger="click" placement="bottom-start" @command="handleCommand">
      <el-button v-loading.fullscreen.lock="dialog_loading" element-loading-background="rgba(0, 0, 0, 0.5)" type="primary" @click="handleDropdown">
        <svg-icon icon-class="email" />
        Outreach<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-if="templateLoading" disabled>
          <i class="el-icon-loading" />Loading...
        </el-dropdown-item>
        <el-dropdown-item v-if="!templateLoading && !templateList.length" disabled>No Data</el-dropdown-item>
        <el-dropdown-item v-for="item in templateList" :key="item.id" :command="item">{{ item.name }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-dialog
      v-loading.fullscreen="emailLoading"
      element-loading-background="rgba(0, 0, 0, 0.5)"
      :title="selectedTemplate ? selectedTemplate.name : ''"
      :visible.sync="dialogVisible"
      :close-on-click-modal="!Boolean(mail_interval_other || mail_interval_resend)"
      :close-on-press-escape="!Boolean(mail_interval_other || mail_interval_resend)"
      append-to-body
      width="60%"
      @closed="resetData()">
        <!--邮件模板共用主题-->
        <div v-if="emailData.length > 1" class="inline-block ml-3 mb-normal">
          <div v-if="!edit_same_subject" class="inline-block">
            <el-button
              type="primary"
              @click="editSameSubject">Override Subject Lines
            </el-button>
            <span v-if="same_subject">{{ same_subject }}</span>
          </div>
          <div v-if="edit_same_subject" class="inline-block">
            <el-input v-if="edit_same_subject" v-model="same_subject" class="w-408px" />
            <el-button
              type="primary"
              :disabled="!same_subject"
              @click="SameSubjectAction('set')">Override
            </el-button>
            <el-button
              type="info"
              class="ml-3"
              @click="SameSubjectAction('cancel')">Cancel
            </el-button>
          </div>
        </div>
        <div v-if="rejectedTasks.length" class="el-alert el-alert--warning is-light mb-8">
          <i class="el-alert__icon el-icon-warning is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks will be filtered:</span>
            <p class="el-alert__description">
              <a
                v-for="(item, index) in rejectedTasks"
                :key="index"
                class="link"
                @click="routeToConsultantDetail(item.advisor_id)">
                {{ item.advisor.name_prefix || ''}} {{ item.advisor.firstname }} {{ item.advisor.lastname }} {{
                  repeatIds.includes(item.advisor_id)
                    ? `#${item.sequence}`
                    : ''
                }}
                <span class="info">{{ formatRejectedTasksReason(item) }}</span>
                <span v-if="index+1 !== rejectedTasks.length">, </span>
              </a>
            </p>
          </div>
        </div>
        <el-collapse v-if="emailData.length" accordion>
          <el-collapse-item v-for="(item, index) in emailData" :key="item.task.id" :name="index">
            <template slot="title">
              <el-popconfirm
                title="Confirm removal of this LEAD?"
                confirm-button-text="Confirm"
                cancel-button-text="Cancel"
                @confirm="actionRemoveLead(index)"
              >
                <el-button
                  slot="reference"
                  type="danger"
                  icon="el-icon-close"
                  circle
                  size="mini"
                  class="remove-btn"
                  @click.stop />
              </el-popconfirm>
              <el-link type="primary" :underline="false" @click.stop="routeToConsultantDetail(item.task.advisor_id)">
                {{ item.task.advisor.name_prefix || ''}} {{ item.task.advisor.firstname }} {{ item.task.advisor.lastname }} {{
                  repeatIds.includes(item.task.advisor_id) ? `#${item.task.sequence}` : ''
                }}
              </el-link>
              <!--              <span v-if="!item.task.advisor_has_email" class="danger">(No Email)</span>-->
            </template>
            <div>
              <to-cc-editor :data="item" />
              <el-input v-model="item.subject" class="email-subject-input" />
              <tinymce
                :ref="`tinymce_content_${item.task.id}`"
                v-model="item.content"
                :height="300"
                inline
                is-email />
            </div>
          </el-collapse-item>
        </el-collapse>

      <span slot="footer" class="dialog-footer flex justify-content-between">
        <div>
          <send-on-behalf
            v-if="emailData.length"
            :checked.sync="is_send_on_behalf"
            :user.sync="send_on_behalf_user"
            :members="project.members"
            @change="generateEmails(selectedTemplate)" />
        </div>

        <el-button
          type="primary"
          :disabled="!emailData.length || emailLoading"
          :loading="sendLoading"
          @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="Email sending progress"
      :visible.sync="progressDialogVisible"
      :close-on-press-escape="false"
      :show-close="false"
      append-to-body
      width="60%">
      <div class="mt-8">
        <!--邮件发送的进度-->
        <el-progress :percentage="percentage" :text-inside="true" :stroke-width="24" status="success" />
        <div class="text-right mt-3">{{formatForProgress}}<span v-if="failed_list.length"> | failed {{failed_list.length}}</span></div>
        <div v-if="failed_list.length">
          <div v-for="item in failed_list" :key="item.task_id">{{item.advisor_name}}<span class="info"> (failed)</span></div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer right">
        <el-button
          type="primary"
          :disabled="percentage<100"
          @click="closeProgressDialog">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email.js'
import ProjectAPI from '@/api/project.js'
import Tinymce from '@/components/Tinymce'
import Mixin from './mixin'
import Mixin_v2 from './mixin_v2'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'
import Filters from '@/utils/filters'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import SendOnBehalf from './components/SendOnBehalf'

export default {
  name: 'OutreachToAdvisor',
  components: { ToCcEditor, Tinymce, SendOnBehalf },
  mixins: [Mixin, Mixin_v2, TaskActionsMixin],
  data() {
    return {
      templateLoading: false,
      templateList: [],
      selectedTemplate: null,
      emailLoading: false,
      emailData: [],
      same_subject: null,
      edit_same_subject: false,
      is_send_on_behalf: false,
      send_on_behalf_user: null,
      number_of_emails_sent_other: 0,
      number_of_emails_sent_resend: 0,
      mail_interval_other: null,
      mail_interval_resend: null,
      progressDialogVisible: false,
      failed_list: [],
    }
  },
  computed: {
    existNoEmail() {
      return this.emailData.findIndex(item => !item.task.advisor_has_email) > -1 ||
        this.emailData.findIndex(item => item.to_list.length === 0) > -1
    },
    percentage() {
      return Math.ceil(((this.number_of_emails_sent_other + this.number_of_emails_sent_resend) / (this.emailData.length)) * 100) || 0
    },

    formatForProgress() {
      return `Processed: ${(this.number_of_emails_sent_other + this.number_of_emails_sent_resend - (this.failed_list.length))} / ${this.emailData.length}`
    },

    approvedBcgTemplate() {
      return this.project?.approved_bcg_templates?.[0]
    },
  },
  methods: {
    editSameSubject() {
      this.edit_same_subject = true
      this.same_subject = this.same_subject || this.emailData[0].subject
    },
    SameSubjectAction(type) {
      this.emailData.forEach(item => {
        item.subject = type === 'cancel' ? item.origin_email_subject : this.same_subject
      })
      type === 'cancel' ? this.same_subject = null : ''
      this.edit_same_subject = false
    },
    handleDropdown() {
      if (!this.templateList.length) {
        const params = {
          reference_ids: this.$route.params.project_id,
          reference_type: 'PROJECT',
          content_type: 'PROJECT_OUTREACH',
          has_permission: true,
          page: 1,
          size: 999,
          include_default_reference_type: true,
          uid: this.$store.state.user.uid,
        }

        this.templateLoading = true
        return EmailAPI.getEmailList(params).then(data => {
          if (this.project.sub_type === 'Survey') {
            this.templateList = data.list.filter(
              item => item.tags && item.tags.includes('SURVEY_OUTREACH'))
          } else {
            this.templateList = data.list.filter(
              item => !(item.tags && item.tags.includes('SURVEY_OUTREACH')))
          }
        }).finally(() => {
          this.templateLoading = false
        })
      }
    },
    async handleCommand(template) {
      this.dialog_loading = true
      await this.getTasksByIDS()
      await this.markingExpertConflictWithProjectTarget()
      await this.repeatTasks()
      await this.initTaskList()
      await this.generateEmails(template)
      await this.checkBeforeOpen()
      await this.getSystemLeadEmailVendor()
      this.dialog_loading = false
    },
    handleBcgTemplate() {
      const { original_template, content_template, subject_template } = this.approvedBcgTemplate
      const template = {
        ...original_template,
        content_template,
        subject_template,
      }
      this.handleCommand(template)
    },
    initTaskList() {
      this.rejectedTasks = []
      this.passTasks = []
      this.tasks.forEach(item => {
        if (this.filterOutreach(item)) {
          this.passTasks.push(item)
        } else {
          this.rejectedTasks.push(item)
        }
      })
    },
    actionRemoveLead(index) {
      this.emailData.splice(index, 1)
    },
    generateEmails(template) {
      this.selectedTemplate = template
      const ids = this.passTasks.map(item => ({
        client_id: this.project.client_id,
        contact_id: null,
        user_id: this.is_send_on_behalf ? this.send_on_behalf_user.id : null,
        project_id: item.project_id,
        task_id: item.id,
        advisor_id: item.advisor_id,
      }))

      this.emailLoading = true
      return this.getEmailsByTemplate(template, ids).then(data => {
        data.forEach((item, index) => {
          item.origin_email_subject = item.subject
          item.task = this.passTasks[index]
          item.task.advisor_has_email = !!item.task.advisor.contact_infos.find(item => item.type === 'EMAIL')
        })

        this.emailData = data

        this.$nextTick(() => {
          this.emailData.forEach(item => {
            if (this.$refs[`tinymce_content_${item.task.id}`]) {
              this.$refs[`tinymce_content_${item.task.id}`][0].setContent(item.content)
            }
          })
        })
      }).finally(() => {
        this.emailLoading = false
      })
    },
    getEmailsByTemplate(template, ids) {
      if (!this.project.bcg_hub_project) {
        return EmailAPI.getEmailsByTemplate(template.id, ids)
      } else {
        const params = {
          ids,
          template,
        }
        return EmailAPI.getEmailsByTemplateData(params)
      }
    },
    sendEmails() {
      if (this.existNoEmail) {
        this.$alert('Exist member has no email.', 'Notice', {
          confirmButtonText: 'OK',
          type: 'error',
        })
        return
      }

      if (this.checkMailAddress()) return

      this.$confirm('Confirm to send emails?', 'Notice', {
        type: 'warning',
      }).then(() => {
        const resend_params = {
          prefer_vendor: 'RESEND',
          batch: [],
        }
        const params = {
          batch: [],
        }
        this.emailData.forEach(item => {
          const email_vendor = this.preferVendor(item.task.advisor?.type)
          const format_item = {
            task_id: item.task.id,
            email: {
              prefer_vendor: email_vendor,
              template_id: item.template_id,
              from: this.is_send_on_behalf ? this.send_on_behalf_user.email : item.from,
              from_name: this.is_send_on_behalf ? this.send_on_behalf_user.name : item.from_name,
              subject: item.subject,
              content: item.content,
              to_list: item.to_list,
              cc_list: item.cc_list,
              bcc_list: this.is_send_on_behalf ? [this.send_on_behalf_user.email] : item.bcc_list,
              reply_to_list: item.reply_to_list,
            },
          }
          if (email_vendor === 'RESEND') {
            resend_params.batch.push(format_item)
          } else {
            params.batch.push(format_item)
          }
        })

        this.sendLoading = true
        if (this.emailData.length > 20) {
          this.whenSendingMoreThan20Emails(resend_params, 'resend')
          this.whenSendingMoreThan20Emails(params)
        } else {
          const result_params = { batch: resend_params.batch.concat(params.batch) }
          return ProjectAPI.mailOutreach(result_params)
            .then((data) => {
              if (data.length > 0) {
                this.dialogVisible = false
                this.updateTasksStatus(data)
              } else {
                this.$message({
                  type: 'error',
                  message: 'Sending failed!',
                })
              }
            }).finally(() => {
              this.sendLoading = false
              this.same_subject = null
              this.edit_same_subject = false
            })
        }
      }).catch(() => {})
    },

    whenSendingMoreThan20Emails(params, type = 'other') {
      this.progressDialogVisible = true
      if (!params.batch.length) return Promise.resolve()
      const request_api = type === 'resend' ? ProjectAPI.mailOutreachBulkAsync(params) : ProjectAPI.mailOutreachAsync(params)
      return request_api
        .then((data) => {
          this[`mail_interval_${type}`] = setInterval(() => {
            ProjectAPI.outreachAsyncProcess(data.id)
              .then(data => {
                this[`number_of_emails_sent_${type}`] = data.succeed_count + data.failed_count
                if (data.finished) {
                  const failed = data.failed_tasks.map(item => {
                    item.advisor_name = this.emailData.find(i => i.task.id === item.task_id)?.task.advisor.full_name
                    return item
                  })
                  this.failed_list = this.failed_list.concat(failed)
                  clearInterval(this[`mail_interval_${type}`])
                }
              })
          }, 2000)
        }).catch(() => {
          clearInterval(this[`mail_interval_${type}`])
        })
    },

    closeProgressDialog() {
      this.$emit('update')
      this.progressDialogVisible = false
      this.dialogVisible = false
      this.same_subject = null
      this.edit_same_subject = false
    },

    checkMailAddress() {
      const error_address = this.emailData.filter(
        item => (!Filters.checkValidMail(item.to_list) || !Filters.checkValidMail(item.cc_list)))

      if (error_address.length > 0) {
        const error_leads = error_address.map(item => (`${item.task.advisor.name_prefix || ''} ${item.task.advisor.full_name}`)).join()
        const message = `The email addresses of these experts are incorrect.<br/>Please check: <span class="danger">${error_leads}</span>`
        this.$alert(message,
          'Incorrect email address', {
            confirmButtonText: 'OK',
            type: 'error',
            dangerouslyUseHTMLString: true,
          })
      }
      return error_address.length > 0
    },
    resetData() {
      this.emailData = []
      this.same_subject = null
      this.edit_same_subject = false
      this.is_send_on_behalf = false
      this.send_on_behalf_user = null
      this.failed_list = []
      this.number_of_emails_sent_other = 0
      this.number_of_emails_sent_resend = 0
    },
    showNotify(rejectedTasks) {
      const h = this.$createElement
      this.$nextTick(() => {
        this.$notify({
          type: 'warning',
          customClass: 'task-notify',
          message: h('div', null, [
            h('p', null, 'These tasks will be filtered:'),
            h('i', { style: 'color: teal' }, rejectedTasks.map((item, index) => {
              return item.name + (index < rejectedTasks.length - 1 ? ', ' : '')
            })),
          ]),
        })
      })
    },
    formatRejectedTasksReason(item) {
      let reason = ''
      if (this.leadsInBlacklist(item)) {
        reason = '(These leads are inactive and can not be sent outreach because they have unsubscribed)'
      } else if (this.existDNC(item)) {
        reason = '(Blacklisted Company)'
      } else if (item.most_recent_email_status === 'danger' || item.list_wise_email_status === 'danger') {
        reason = '(These expert\'s email addresses are marked as unreachable by listwise or sendgrid)'
      } else if (item.advisor_decline?.is_valid) {
        reason = '(Declined)'
      } else if (!item.advisor.contact_infos.find(item => item.type === 'EMAIL')) {
        reason = '(No Email)'
      } else if (this.governmentEmail(item)) {
        reason = '(The expert has an email address in the national government domain[.gov or .go])'
      } else if (item.advisor_conflict_with_investment_target) {
        reason = '(The current employer conflicts with the investment target of the project)'
      } else if (item.ca_status !== 'INITIAL' || item.sq_status !== 'INITIAL' || item.advisor_schedule_status !==
        'INITIAL') {
        reason = '(Has already sent SQ/CA/Availability Email to Leads)'
      }
      return reason
    },

    /*
    * 后端数据处理 返回发送成功的数据 前端处理失败数据提示 */
    updateTasksStatus(data) {
      const success_mail = data.map(item => item.id)
      const failed_task = this.emailData.filter(item => !success_mail.includes(item.task.id))

      if (failed_task.length > 0) {
        const error_leads = failed_task.map(item => (`${item.advisor.name_prefix || ''} ${item.advisor.full_name}`)).join()
        this.$message({
          type: 'warning',
          duration: 0,
          showClose: true,
          message: `These leads failed to be sent: ${error_leads}. `,
        })
      } else {
        this.$message({
          type: 'success',
          message: 'Success to send emails!',
        })
      }
      this.$emit('update')
    },
  },
}
</script>

<style scoped lang="scss">
.remove-btn {
  margin-right: 5px;
}

::v-deep .el-button--mini.is-circle {
  padding: 2px;
}

/* 邮件编辑 主题 */
.email-subject-input {
  margin-bottom: 5px;
  margin-top: 5px;

  ::v-deep .el-input__inner {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }
}
</style>
