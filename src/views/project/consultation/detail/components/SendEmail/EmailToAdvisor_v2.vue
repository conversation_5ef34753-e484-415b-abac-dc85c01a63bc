<template>
  <div class="inline-block">
    <el-button :loading="dialog_loading" type="primary" @click="supplementTaskData()">
      <svg-icon icon-class="email" />
      To {{ type }}
    </el-button>
    <el-dialog
      :title="`Email To ${type}`"
      :visible.sync="dialogVisible"
      width="90%"
      custom-class="email-dialog"
      append-to-body
      :show-close="false"
      @open="openDialog"
      @closed="resetData">
      <div slot="title">
        <span> Email To {{ type }}</span>
        <!--邮件模板共用主题-->
        <div v-if="data.length > 1 && !someSendingFailures" class="inline-block ml-3">
          <div v-if="!edit_same_subject" class="inline-block">
            <el-button
              type="primary"
              @click="editSameSubject">Override Subject Lines
            </el-button>
            <b v-if="same_subject">{{ same_subject }}</b>
          </div>
          <div v-if="edit_same_subject" class="inline-block">
            <div class="flex flex-column">
              <div>
                <el-input v-if="edit_same_subject" v-model="same_subject" class="w-408px mr-8" />
                <el-button
                  type="primary"
                  :disabled="setSameSubjectDisabled"
                  @click="SameSubjectAction('set')">Override
                </el-button>
                <el-button
                  type="info"
                  @click="SameSubjectAction('cancel')">Cancel
                </el-button>
              </div>
              <div v-if="sameSubjectRegDisabled" class="warning mt-2px">The placeholder will not be replaced. Please fill in again.</div>
            </div>
          </div>
        </div>
      </div>
      <el-alert
        v-if="warning_text.length"
        type="warning"
        show-icon
        class="mb-8">
        <div v-for="(item) in warning_text" :key="item.id" class="flex align-items-center">
          {{ item.text }}
        </div>
      </el-alert>
      <el-alert
        v-if="needInvestmentTarget"
        type="error"
        show-icon
        class="mb-8">
        <div class="flex align-items-center">You need to show Investment Target in CA, please fill in the project.</div>
      </el-alert>
      <el-alert
        v-if="filterTasks.length"
        title="These tasks will be filtered:"
        type="warning"
        show-icon
        class="mb-8">
        <div v-if="filterTasks.length">
          <el-link
            v-for="(item, index) in filterTasks"
            :key="index"
            type="primary"
            :underline="false"
            :name="index"
            @click="routeToConsultantDetail(item.advisor_id)">
            {{ item.advisor.name_prefix || ''}}
            {{ item.advisor.full_name }}
            <span class="info">{{ formatRejectedTasksReason(item) }}</span>
            <slot v-if="index+1 !== filterTasks.length">,</slot>&nbsp;
          </el-link>
        </div>
      </el-alert>

      <el-table
        v-loading="loading"
        :data="data"
        row-key="task.id"
        :expand-row-keys="expands"
        @expand-change="handleExpandChange">
        <el-table-column width="25">
          <template #default="scope">
            <el-popconfirm
              title="Confirm removal of this advisor?"
              confirm-button-text="Confirm"
              cancel-button-text="Cancel"
              confirm-button-type="danger"
              @confirm="actionRemoveAdvisor(scope.row.task)"
            >
              <el-link
                slot="reference"
                :underline="false"
                type="danger"
                icon="el-icon-close"
                @click.stop />
            </el-popconfirm>
          </template>
        </el-table-column>
        <el-table-column type="expand">
          <template #default="scope">
            <div v-loading="scope.row.email_loading" class="email-section">
              <to-cc-editor :data="scope.row.email" />
              <div v-if="showAttachments(scope.row)" class="flex">
                <h6>Attachments: </h6>
                <div class="flex-1 mt-8 ml-3">
                  <div v-for="(item, index) in scope.row.email.attachments" :key="index" class="mt-8">
                    <div v-if="item.file_path">
                      <el-link
                        type="primary"
                        :href="item.file_path">{{ item.name }}
                      </el-link>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex align-items-center">
                <el-input
                  v-model="scope.row.email.subject"
                  class="email-subject-input mr-8"
                  @change="changeSubject" />
                <el-tooltip :content="subject_copied ? 'Copied' : 'Copy'" placement="top">
                  <span style="margin-top: 8px;margin-right: 3px;font-size: 12px;">
                    <i class="el-icon-document-copy link cursor-pointer" @click="copy(scope.row.email.subject)" />
                  </span>
                </el-tooltip>
              </div>
              <tinymce
                :ref="`tinymce_content_${scope.row.task.id}`"
                v-model="scope.row.email.content"
                inline
                is-email />
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="type" width="150">
          <template #default="scope">
            <i v-show="scope.row.send_status === 1" class="el-icon-loading primary f-14 mr-8" style="vertical-align: text-bottom;" />
            <i v-show="scope.row.send_status === 2" class="el-icon-success success f-14 mr-8" style="vertical-align: text-bottom;" />
            <el-tooltip
              v-if="scope.row.send_status === 3"
              effect="dark"
              :content="scope.row.error_hint"
              placement="top">
              <i class="el-icon-error danger f-14 mr-8" style="vertical-align: text-bottom;" />
            </el-tooltip>

            <el-link type="primary" :underline="false" @click.stop="routeToConsultantDetail(scope.row.task.advisor_id)">
              {{ scope.row.task.advisor.name_prefix || ''}}
              {{ scope.row.task.advisor.full_name }}
              {{
                repeatedTasksID.includes(scope.row.task.advisor_id) ?
                  !isLeads ?
                    `#${scope.row.task.display_id}` :
                    `#${scope.row.task.sequence}` : ''
              }}
            </el-link>
            <el-tooltip
              v-if="!scope.row.advisor_has_email || scope.row.task.advisor.tc_expired"
              effect="dark"
              placement="top">
              <div slot="content">
                <div v-if="!scope.row.advisor_has_email">No Email</div>
                <div v-if="scope.row.task.advisor.tc_expired">TC Expired</div>
              </div>
              <el-link type="danger" :underline="false" icon="el-icon-warning" style="vertical-align: text-bottom;" />
            </el-tooltip>
            <el-tooltip
              v-if="!scope.row.advisor_has_phone"
              effect="dark"
              content="No Phone"
              placement="top">
              <el-link type="warning" :underline="false" icon="el-icon-warning" style="vertical-align: text-bottom;" />
            </el-tooltip>
            <el-tooltip
              v-if="!scope.row.task.advisor.current_job"
              effect="dark"
              content="No Current Job"
              placement="top">
              <el-link type="danger" :underline="false" icon="el-icon-warning" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Email Template" min-width="300">
          <template #header>
            <span class="mr-normal">Email Template</span>
            <el-select
              v-if="data.length > 1"
              v-model="same_template"
              value-key="id"
              placeholder="Same Template"
              @change="setSameTemplate()">
              <el-option
                v-for="template in options.emailTemplates"
                :key="template.id"
                :label="template.display_name"
                :value="template" />
            </el-select>
            <template-batch-edit v-if="same_template" :email-template.sync="same_template" @update="batchSetSameTemplateContent" />
          </template>
          <template #default="scope">
            <div class="flex">
              <el-select
                v-model="scope.row.template"
                value-key="id"
                class="w-100"
                :disabled="!scope.row.task.advisor.current_job"
                @change="templateChange(scope.row)">
                <el-option
                  v-for="template in scope.row.options.emailTemplates"
                  :key="template.id"
                  :label="template.display_name"
                  :value="template" />
              </el-select>
              <el-tooltip
                v-if="!existSQ && includesTargetTag(scope.row,'SQ')"
                class="ml-8"
                effect="dark"
                content="No SQ"
                placement="top">
                <el-link type="danger" :underline="false" icon="el-icon-warning" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="hasTcTemplate" :key="Math.random()+'TC'" width="460">
          <template #header>
            <span>TC</span>
            <div v-if="data.length > 1">
              <tc-search
                v-model="same_task_tc"
                :init-request="false"
                :init-list="options.tc"
                @change="setSameOption('advisor_tc.tc', same_task_tc)"
              />
              <el-select
                v-model="same_task_tc_lang"
                class="w-85px"
                @change="setSameOption('advisor_tc.tc_template_id', same_task_tc_lang)"
              >
                <slot v-if="same_task_tc">
                  <el-option
                    v-for="item in same_task_tc.templates"
                    :key="item.id"
                    :label="item.locale"
                    :value="item.id" />
                </slot>
              </el-select>
            </div>
          </template>
          <template #default="scope">
            <div v-if="includesTargetTag(scope.row,'TC')" class="no-wrap">
              <tc-search
                v-model="scope.row.advisor_tc.tc"
                :init-request="false"
                :init-list="options.tc"
                @change="(val) => {handleTcChange(val, scope.row)}" />
              <el-select v-model="scope.row.advisor_tc.tc_template_id" class="w-85px">
                <slot v-if="scope.row.advisor_tc.tc">
                  <el-option
                    v-for="item in scope.row.advisor_tc.tc.templates"
                    :key="item.id"
                    :label="item.locale"
                    :value="item.id" />
                </slot>
              </el-select>
              <el-input
                v-model="scope.row.advisor_tc.rate"
                type="number"
                :step="150"
                :min="0"
                class="w-75px" />
              <el-select v-model="scope.row.advisor_tc.rate_currency" class="w-75px">
                <el-option
                  v-for="item in options.currency"
                  :key="item"
                  :label="item"
                  :value="item" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="hasCaTemplate" :key="Math.random()+'CA'" label="CA" width="200">
          <template #header>
            CA
            <el-tooltip v-if="!options.ca.length" effect="dark" content="No CA" placement="top">
              <el-link type="danger" :underline="false" icon="el-icon-warning" />
            </el-tooltip>
            <el-select
              v-if="options.ca.length > 0 && data.length > 1"
              v-model="same_task_ca"
              filterable
              class="w-100"
              @change="setSameOption('task_ca.inquiry_id',same_task_ca)">
              <el-option
                v-for="item in options.ca"
                :key="item.id"
                :label="item.name"
                :value="item.id" />
            </el-select>
          </template>
          <template #default="scope">
            <div v-if="includesTargetTag(scope.row,'CA')" class="no-wrap">
              <el-select
                v-model="scope.row.task_ca.inquiry_id"
                filterable
                class="w-100">
                <el-option
                  v-for="item in options.ca"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id" />
              </el-select>
              <div class="display-none">
                <ca-copy
                  v-if="scope.row.task_ca.inquiry_branch && includesTargetTag(scope.row,'CA_EMBEDDING')"
                  :id="`ca-copy-${scope.row.task.id}`"
                  :investment-target="showInvestmentTarget"
                  :data="scope.row.task_ca.inquiry_branch" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="hasCaTemplate" :key="Math.random()+'CA_LANG'" label="Language" width="110">
          <template #header>
            Language
            <el-select
              v-if="options.ca.length > 0 && data.length > 1"
              v-model="same_task_ca_lang"
              filterable
              @change="setSameOption('task_ca.inquiry_branch', same_task_ca_lang,true)">
              <el-option
                v-for="item in options.caLang"
                :key="item.id"
                :label="item.customName"
                :value="item" />
            </el-select>
          </template>
          <template #default="scope">
            <div v-if="includesTargetTag(scope.row,'CA')" class="no-wrap">
              <el-select
                v-model="scope.row.task_ca.inquiry_branch"
                value-key="id"
                class="w-100"
                @change="getEmailContent(scope.row)">
                <el-option
                  v-for="item in options.caLang"
                  :key="item.id"
                  :label="item.customName"
                  :value="item" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="hasSqTemplate" :key="Math.random()+'SQ'" label="SQ" width="200">
          <template #header>
            Screening Questions
            <el-tooltip v-if="!options.sq || !options.sq.length" effect="dark" content="No SQ" placement="top">
              <el-link type="danger" :underline="false" icon="el-icon-warning" />
            </el-tooltip>
            <el-select
              v-if="options.sq.length > 0 && data.length > 1"
              v-model="same_task_sq"
              filterable
              @change="setSameOption('sq_branch_id', same_task_sq)"
            >
              <el-option
                v-for="item in options.sq"
                :key="item.id"
                :label="item.title"
                :value="item.id" />
            </el-select>
          </template>
          <template #default="scope">
            <div
              v-if="includesTargetTag(scope.row,'SQ')"
              class="no-wrap">
              <el-select
                v-model="scope.row.sq_branch_id"
                filterable>
                <el-option
                  v-if="scope.row.task.latest_sq && scope.row.task.latest_sq.contains_one_off_questions"
                  label="One off screening of this task"
                  value="task_origin_sq"
                />
                <el-option
                  v-for="item in options.sq"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id" />
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer flex justify-content-between">
        <div>
          <send-on-behalf
            v-if="data.length && isLeads"
            :checked.sync="is_send_on_behalf"
            :user.sync="send_on_behalf_user"
            :members="project.members"
            @change="data.forEach(row => templateChange(row))" />
        </div>

        <div v-if="!someSendingFailures">
          <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" :loading="isSending" :disabled="sendDisabled" @click="submit">Send</el-button>
        </div>
        <div v-else>
          <el-button type="primary" @click="dialogVisible = false">OK</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { copyUtil } from '@/utils/tool'
import EmailAPI from '@/api/email'
import InquiryAPI from '@/api/inquiry'
import TcAPI from '@/api/tc'
import ProjectAPI from '@/api/project'
import Mixin from './mixin_v2'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'
import TcSearch from '@/components/SelectRemoteSearch/TcSearch/index'
import CaCopy from '@/components/Question/CACopy'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'
import SendOnBehalf from './components/SendOnBehalf'
import _ from 'lodash'
import TemplateBatchEdit from '@/views/project/consultation/detail/components/SendEmail/components/TemplateBatchEdit'

export default {
  name: 'EmailToAdvisor',
  components: { TemplateBatchEdit, ToCcEditor, TcSearch, CaCopy, SendOnBehalf },
  mixins: [Mixin, TaskActionsMixin],
  props: {
    tasks: Array,
    project: Object,
    type: String,
  },
  data() {
    return {
      expands: [],
      options: {
        emailTemplates: [],
        ca: [],
        caLang: [],
        tc: [],
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
        sq: [],
      },
      data: [],
      lang_type: [
        { value: 'en-US', label: 'English' },
        { value: 'zh-CN', label: 'Chinese' },
        { value: 'en-zh', label: 'Both' },
      ],
      subject_copied: false,
      warning_text: [],
      same_template: null,
      same_task_ca: null,
      same_task_ca_lang: null,
      same_task_sq: null,
      same_task_tc: null,
      same_task_tc_lang: null,
      edit_same_subject: false,
      same_subject: null,
      is_send_on_behalf: false,
      send_on_behalf_user: null,
      task_action_type: 'To Advisor',
    }
  },
  computed: {
    isLeads() {
      return this.type === 'Leads'
    },
    existNoEmail() {
      return this.data.findIndex(item => !item.advisor_has_email) > -1
    },
    existNoCurrentJob() {
      return this.data.findIndex(item => !item.task.advisor.current_job) > -1
    },
    existNoTc() {
      return this.data.findIndex(item => !item.task.advisor.tc) > -1
    },
    hasSqTemplate() {
      return this.data.findIndex(item => item.template && (item.template.tags.includes('SQ'))) > -1
    },
    hasCaTemplate() {
      return this.data.findIndex(item => item.template && item.template.tags.includes('CA')) > -1
    },
    hasTcTemplate() {
      return this.data.findIndex(item => item.template && item.template.tags.includes('TC')) > -1
    },
    isSending() {
      return this.data.findIndex(item => item.send_status === 1) > -1
    },
    someSendingFailures() {
      return this.data.findIndex(item => item.send_status === 3) > -1
    },
    needInvestmentTarget() {
      return this.hasCaTemplate && this.checkClientRuleShowInvestmentTargetToAdvisor() &&
        this.project.investment_target_company_ids.length < 1
    },
    setSameSubjectDisabled() {
      return !this.same_subject || !this.same_subject.trim() || this.sameSubjectRegDisabled
    },
    /* 防止用户在subject里面填写占位符，目前不会被替换*/
    sameSubjectRegDisabled() {
      const template_flag = /{|}/g
      return this.same_subject && !!this.same_subject?.match(template_flag)?.length
    },
    showInvestmentTargetInPortal() {
      return [1189, 2].includes(parseInt(this.project.client_id)) && !this.isSGDB
    },
    showInvestmentTarget() {
      if (this.checkClientRuleShowInvestmentTargetToAdvisor() && this.project.investment_target_company_ids.length >
        0) {
        const target_company = this.project.investment_target_companies.map(item => item.name).join(', ')
        const title = (parseInt(this.project.client_id) === 900 && !this.isSGDB) ? 'A message from Our Client. The subject matter of this consultation is:' : 'Target Company:'
        return `<b>${title}</b><span> ${target_company}</span>`
      } else {
        return null
      }
    },
    sendDisabled() {
      return (!this.options.ca.length && this.hasCaTemplate) || (!this.existSQ && this.hasSqTemplate) ||
        (!this.existNoTc && this.hasTcTemplate) || this.needInvestmentTarget || this.passTasks.length < 1
    },
  },
  methods: {
    // 调接口获取所需的更多信息
    async supplementTaskData() {
      this.dialog_loading = true
      await this.getTasksByIDS()
      await this.markingExpertConflictWithProjectTarget()
      await this.checkBeforeOpen()
      this.dialog_loading = false
    },
    templateChange(row) {
      this.warnTextFilter(row)
      this.getEmailContent(row)
      this.same_template = null
    },

    setSameTemplate() {
      this.batchSetSameTemplateContent(this.same_template)

      this.same_task_ca = null
      this.same_task_ca_lang = null
      this.same_task_sq = null
      this.same_task_tc = null
      this.same_task_tc_lang = null

      this.same_subject = null
      this.edit_same_subject = false
    },

    setSameOption(type, value, changeEmailContent = false) {
      const type_is_object = [
        'advisor_tc.tc',
        'advisor_tc.tc_template_id',
        'task_ca.inquiry_branch',
        'task_ca.inquiry_id'].includes(type)
      const father_name = type.split('.')[0] || undefined
      const children_name = type.split('.')[1] || undefined
      const target_object = {}
      target_object[children_name] = value

      this.data.forEach(item => {
        type_is_object
          ? this.$set(item, father_name, Object.assign({}, item[father_name], target_object))
          : item[type] = value

        if (type === 'advisor_tc.tc') {
          this.handleTcChange(value, item)
        }

        changeEmailContent ? this.getEmailContent(item) : ''
      })
    },

    editSameSubject() {
      this.edit_same_subject = true
      this.same_subject = null
    },

    SameSubjectAction(type) {
      this.data.forEach(item => {
        const target_subject = type === 'cancel' ? item.origin_email_subject : this.same_subject

        if (target_subject) {
          if (item.email.subject) {
            item.email.subject = target_subject
          } else {
            this.getEmailContent(item).then(() => {
              item.email.subject = target_subject
            })
          }
        }
      })
      type === 'cancel' ? this.same_subject = null : ''
      this.edit_same_subject = false
    },

    warnTextFilter(row) {
      if (row.task.latest_submitted_sq && row.template?.tags.includes('SQ')) {
        this.push(this.warning_text, row.task, 'SQ')
      } else {
        this.remove(this.warning_text, row.task, 'SQ')
      }

      if (row.task.latest_submitted_pre_ca && row.template?.tags.includes('CA')) {
        this.push(this.warning_text, row.task, 'CA')
      } else {
        this.remove(this.warning_text, row.task, 'CA')
      }
    },
    openDialog() {
      this.warning_text = []
      this.checkRepeatedTasks()
      this.filterTaskListForAdvisor()
      this.loading = true
      return Promise.all([this.getEmailTemplateList(), this.getCaList(), this.getTcList(), this.checkSqExisted(), this.getSystemLeadEmailVendor()])
        .then(() => {
          this.data = this.passTasks.map(task => {
            const res = {
              options: {
                emailTemplates: [],
              },
              template: null,
              send_status: 0, // 0: 未发送 1: 发送中 2: 已发送 3: 发送失败
              email_loading: false,
              task_ca: {
                inquiry_branch: null,
                inquiry_id: this.options.ca.length ? this.options.ca[0].id : null,
              },
              sq_branch_id: undefined,
              advisor_tc: {
                tc: 0,
                tc_template_id: 0,
                rate: task.advisor.rate || task.advisor.default_rate || null,
                rate_currency: task.advisor.rate_currency || 'USD',
              },
              email: {
                subject: '',
                content: '',
              },
              advisor_has_email: !!task.advisor.contact_infos.find(item => item.type === 'EMAIL'),
              advisor_has_phone: !!task.advisor.contact_infos.find(item => item.type === 'PHONE'),
              task,
            }

            /* 针对consultation 类型的项目 验证专家是否签过 tc */
            const consultation_need_tc = !task.advisor.tc_term_valid['CONSULTATION'] && this.project.sub_type ===
              'Consultation'

            // 初始化邮件模板列表 和 默认选择的邮件模板
            res.options.emailTemplates = this.options.emailTemplates.filter(item => {
              /* CN Advisor 不允许发送 ca_link */
              let advisor_filter = !item.name.includes('New Expert')
              if (task.advisor.is_cn) {
                advisor_filter = !item.name.includes('New Expert') && !item.tags.includes('CA_LINK')
              }

              /* "leads" 或 "TC过期" 给予 TC 模板 */
              const filter_template = (this.isLeads || consultation_need_tc)
                ? item.tags.includes('TC')
                : advisor_filter

              /* leads 不允许发送 SQ */
              // const filter_SQ = this.isLeads ? !item.tags.includes('SQ') : true

              return filter_template
            })

            /* 根据专家状态选择默认模板 */
            let target_tag
            let without_tag = []
            if (!consultation_need_tc) {
              without_tag.push('TC')
              if (!task.advisor.tc_expired) {
                if (['SENT', 'INITIAL'].includes(task.sq_status)) {
                  target_tag = this.existSQ ? 'SQ' : 'NO_SQ'
                  without_tag = without_tag.concat(['CA', 'CA_LINK'])
                } else {
                  target_tag = 'POST_SQ'
                }
              }
            } else {
              target_tag = this.existSQ ? 'SQ' : 'TC_PROJECT'
              without_tag.push('CA')
            }
            if (task.advisor.tc_expired) {
              target_tag = 'TC'
            }

            // 依据模板的tags 过滤后，取list ID较小（前期系统级的模板）的模板
            // 但是如果TC过期了，优先选 Re-Accept TC 的模版
            const target_templates = res.options.emailTemplates.filter(item => item.tags.includes(target_tag) && item.tags.filter(val => without_tag.includes(val)).length < 1)
            if (target_tag === 'TC') {
              res.template = target_templates.find(item => item.display_name.includes('Re-Accept TC'))
            } else {
              res.template = _.sortBy(target_templates, 'id')[0]
            }

            // 初始化默认选择的 CA TC 和语言版本
            if (this.options.ca.length) {
              const ca_lang = this.options.caLang.find(item => item.locale === task.advisor.locale)
              res.task_ca.inquiry_branch = ca_lang || this.options.caLang[0]
            }
            console.log(res.task_ca.inquiry_branch)
            if (this.options.tc.length) {
              res.advisor_tc.tc = this.options.tc.find(tc => tc.is_current)
              let template = res.advisor_tc.tc.templates.find(template => template.locale === task.advisor.locale)

              if (!template) {
                template = res.advisor_tc.tc.templates[0]
              }

              res.advisor_tc.file_name = template.file_name
              res.advisor_tc.file_url = template.file_url
              res.advisor_tc.tc_template_id = template.id
            }

            // 初始化默认选择的 SQ
            // 优先选择 task 中含有一次性新增问题的问卷
            if (task.latest_sq && task.latest_sq.contains_one_off_questions) {
              res.sq_branch_id = 'task_origin_sq'
            } else {
              if (this.options.sq && this.options.sq.length) {
                if (task.angle && task.angle.inquiry_branch_id) {
                  res.sq_branch_id = task.angle.inquiry_branch_id
                } else {
                  res.sq_branch_id = null
                }
              }
            }
            this.warnTextFilter(res)
            return res
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    async getEmailTemplateList() {
      const params = {
        has_permission: true,
        content_type: 'PORTAL_ADVISOR_PRE_CALL',
        reference_ids: [0, this.project.client_id].join(),
        reference_type: 'CLIENT',
        sort: 'reference_id desc',
        include_default_reference_type: true,
      }
      const data = await EmailAPI.getEmailList(params)
      data.list.sort((a, b) => a.rank - b.rank)
      this.options.emailTemplates = data.list
    },
    async getCaList() {
      const params = {
        types: 'PRE_CALL_CA',
        extra: 'branches,branches.questions,branches.name,branches.description',
        'client.page': 0,
        client_id: this.project.client_id,
      }

      // 为其他DB导出专家的项目 发送项目级别的CA
      if (this.project.exported_to === 'CN') {
        params.project_id = this.project.id
        params.client_id = null
      }
      const data = await InquiryAPI.getInquiryList(params)
      // data.list.forEach(item => {
      //   item.branches.forEach(branch => {
      //     branch.questions.forEach(question => {
      //       if (!question.answer) question.answer = { result: '' }
      //       if (question.sub_questions) {
      //         question.sub_questions.forEach(sub => {
      //           if (!sub.answer) sub.answer = { result: '' }
      //         })
      //       }
      //     })
      //   })
      // })
      this.options.ca = data['list']
      if (data['list'].length) {
        const list = data['list'][0].branches.filter(item => item.is_default_using)
        // 审计：系统内GKM不操作 禁用 英文版以外的CA
        this.options.caLang = list.filter(item => item.locale === 'en-US').map((item) => {
          item.customName = this.lang_type.find((type) => type.value === item.locale)['label']
          return item
        })
      } else {
        this.options.caLang = []
      }
    },
    async getTcList() {
      if (!this.options.tc.length) {
        const params = {
          is_current: true,
          type: 'DEFAULT',
        }
        this.options.tc = await TcAPI.getTCList(params)
      }
    },

    batchSetSameTemplateContent(target_content) {
      this.same_template = target_content
      this.data.forEach(item => {
        if (item.task.advisor.current_job) {
          item.template = target_content
          item.email.content = ''
          if (this.expands.includes(item.task.id)) {
            this.getEmailContent(item)
          }
        }
      })
    },

    async getEmailContent(row) {
      const params = {
        ids: {
          client_id: row.task.client_id,
          contact_id: null,
          user_id: this.is_send_on_behalf ? this.send_on_behalf_user.id : null,
          project_id: row.task.project_id,
          task_id: row.task.id,
          advisor_id: row.task.advisor_id,
        },
        template: row.template,
      }
      row.email_loading = true
      const email = await EmailAPI.getEmailContentWithTemplateParams(params)
      // const email = await EmailAPI.getEmailByTemplate(row.template.id, params)
      email.content = this.handleMailContent(email.content)
      if (row.template.tags.includes('CA_EMBEDDING') && this.options.ca.length) {
        const copyHtml = '<div contenteditable="false">' + document.getElementById(`ca-copy-${row.task.id}`).innerHTML +
          '</div>'
        email.content = email.content.replace(`{CA_CONTENT}`, copyHtml)
      }
      row.origin_email_subject = email.subject
      row.email = email
      this.$nextTick(() => {
        if (this.$refs[`tinymce_content_${row.task.id}`]) {
          this.$refs[`tinymce_content_${row.task.id}`].setContent(row.email.content)
        }
      })
      row.email_loading = false
    },
    handleExpandChange(row, expandedRows) {
      if (expandedRows.length) {
        this.expands = []
        this.expands.push(row.task.id)
      }
      if (!row.email.content) this.getEmailContent(row)
    },
    handleTcChange(val, row) {
      let template = val.templates.find(template => template.locale === row.task.advisor.locale)

      if (!template) {
        template = val.templates[0]
      }
      row.advisor_tc.file_name = template.file_name
      row.advisor_tc.file_url = template.file_url
      row.advisor_tc.tc_template_id = template.id
    },

    submit() {
      let alert_content_template = 'Exist member has'
      const alert_content = []
      const exist_no_match_template = this.data.filter(item => {
        return !item.options.emailTemplates.find(temp => (temp.id === item.template.id))
      })

      const exist_no_tc_rate = this.data.filter(item => {
        return (item.template && item.template.tags.includes('TC')) &&
          (!item.advisor_tc.rate_currency || !/^\d+(?=\.{0,1}\d+$|$)/.test(item.advisor_tc.rate))
      }).length > 0

      const exist_no_sq = this.data.filter(item => {
        return item.template && item.template.tags.includes('SQ') && !item.sq_branch_id
      }).length > 0

      if (exist_no_match_template.length > 0) {
        alert_content.push('no match template')
      }

      if (exist_no_tc_rate) {
        alert_content.push('no rate(greater than or equal to 0) or currency')
      }

      if (exist_no_sq) {
        alert_content.push('no screening questions')
      }

      if (this.existNoEmail) {
        alert_content.push('no email')
      }

      if (this.existNoCurrentJob) {
        alert_content.push('no current job')
      }

      if (alert_content.length) {
        alert_content_template = `${alert_content_template} ${alert_content.join(' and ')}`
        return this.$alert(alert_content_template, 'Notice', {
          confirmButtonText: 'OK',
          type: 'error',
        })
      }

      this.$confirm('Confirm to send emails?', 'Notice', {
        type: 'warning',
      })
        .then(() => {
          const batch_email_list = []
          const requests = this.data.map(item => {
            const emailPromise = new Promise((resolve) => {
              if (!item.email.subject && !item.email.content) {
                this.getEmailContent(item)
                  .then(() => {
                    return resolve()
                  })
              } else {
                return resolve()
              }
            })
            item.send_status = 1
            return emailPromise.then(() => {
              batch_email_list.push(this.handleEmailParams(item))
              // return this.handleEmailParams(item)
              //   .then(new_task => {
              //     new_task.advisor = null
              //     item.send_status = 2
              //     for (const key in item.task) {
              //       if (new_task[key]) item.task[key] = new_task[key]
              //     }
              //   }, () => {
              //     item.send_status = 3
              //   })
            }, () => {
              item.error_hint = 'Failed to retrieve email content.'
              item.send_status = 3
            })
          })
          return Promise.all(requests)
            .then(() => {
              return this.batchEmail(batch_email_list)
            })
            .then(() => {
              if (!this.someSendingFailures) {
                this.updateTaskStatus()
                this.dialogVisible = false
                this.same_template = null
                this.same_subject = null
              }
            })
        }, () => {
        })
    },
    batchEmail(batch_email_list) {
      const params = {
        requests: batch_email_list,
      }
      const success_list = []

      return ProjectAPI.mailToAdvisor(params)
        .then((data) => {
          data.forEach(item => {
            const target_task = this.data.find(temp => temp.task.id === item.task_id)
            const advisor_display_name = `${target_task.task.advisor.name_prefix || ''} ${target_task.task.advisor.full_name}`
            if (item.success) {
              success_list.push(advisor_display_name)
              target_task.send_status = 2
            } else {
              target_task.error_hint = item.error_hint
              target_task.send_status = 3
              setTimeout(() => {
                this.$message({
                  type: 'error',
                  message: `${item.error_hint} (${advisor_display_name})!`,
                })
              })
            }
          })
        }).then(() => {
          if (success_list.length > 0) {
            this.$message({
              type: 'success',
              message: `Successfully send email to (${success_list.join(', ')})!`,
            })
          }
        })
    },

    handleEmailParams(item) {
      const data = {
        portal: {
          type: 'ADVISOR_PRE_CALL',
          is_display_investment_target: !!this.showInvestmentTarget || this.showInvestmentTargetInPortal,
          task_id: item.task.id,
          advisor_id: item.task.advisor_id,
          task_ca: item.template.tags.includes('CA') ? {
            inquiry_id: item.task_ca.inquiry_id,
            inquiry_branch_id: item.task_ca.inquiry_branch.id,
          } : undefined,
          advisor_tc: item.template.tags.includes('TC') ? {
            tc_id: item.advisor_tc.tc.id,
            rate: item.advisor_tc.rate,
            tc_template_id: item.advisor_tc.tc_template_id,
            rate_currency: item.advisor_tc.rate_currency,
            locale: Object.entries(item.advisor_tc.tc.locale_template_id_map)
              .find(arr => arr[1] === item.advisor_tc.tc_template_id)?.[0],
          } : undefined,
        },
        email: {
          ...item.email,
          prefer_vendor: this.preferVendor(item.task.advisor?.type),
        },
        email_template_tags: item.template.tags,
      }

      /* 选择其他同事代为发送 */
      if (this.is_send_on_behalf) {
        data.email.from = this.send_on_behalf_user.email
        data.email.from_address = { ...this.send_on_behalf_user, type: 'USER' }
        data.email.from_name = this.send_on_behalf_user.name
        data.email.bcc_list = [this.send_on_behalf_user.email]
      }

      /* sq 处理 */
      if (item.template.tags.includes('SQ')) {
        if (item.sq_branch_id === 'task_origin_sq') {
          data.portal.sq_id = item.task.latest_sq.id
        } else {
          data.portal.sq_branch_id = item.sq_branch_id || undefined
        }
      }

      /* 添加邮件附件 (TC).pdf */
      if (item.template.tags.includes('TC')) {
        if (item.advisor_tc.file_name) {
          data.email.attachments = [
            {
              name: item.advisor_tc.file_name,
              file_path: item.advisor_tc.file_url,
            },
          ]
        }
      }
      return data
    },

    updateTaskStatus() {
      this.$emit('update')
    },
    resetData() {
      if (this.someSendingFailures) {
        this.updateTaskStatus()
      }
      this.filterTasks = []
      this.passTasks = []
      this.data = []
      this.same_template = null
      this.same_subject = null
      this.edit_same_subject = false
      this.same_task_ca = null
      this.same_task_ca_lang = null
      this.same_task_sq = null
      this.same_task_tc = null
      this.same_task_tc_lang = null
      this.is_send_on_behalf = false
      this.send_on_behalf_user = null
      this.dialogVisible = false
    },

    formatRejectedTasksReason(item) {
      let reason = ''
      if (this.advisorInBlacklist(item)) {
        reason = '(This lead is inactive and can not be sent outreach because they have unsubscribed)'
      } else if (this.checkDNC(item)) {
        reason = '(Blacklisted Company)'
      } else if (this.advisorDeclined(item)) {
        reason = '(Declined)'
      } else if (this.advisorNoEmail(item)) {
        reason = '(No Email)'
      } else if (this.emailStatusFailed(item)) {
        reason = '(These expert\'s email addresses are marked as unreachable by listwise or sendgrid)'
      } else if (item.advisor_conflict_with_investment_target) {
        reason = '(The current employer conflicts with the investment target of the project)'
      } else if (this.governmentEmail(item)) {
        reason = '(This expert\'s email address is identified as government-related (suffix containing \'go\' or \'gov\'))'
      } else {
        reason = 'have been arranged or completed or in blacklisted companies.'
      }
      return reason
    },
    copy(val) {
      copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
          this.subject_copied = true
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
    actionRemoveAdvisor(task) {
      const index = this.data.findIndex(item => item.task.id === task.id)
      this.remove(this.warning_text, task, 'CA')
      this.remove(this.warning_text, task, 'SQ')
      this.data.splice(index, 1)
    },
    changeSubject() {
      this.subject_copied = false
    },

    showAttachments(row) {
      return row.email.attachments && row.email.attachments.length > 0 && this.type === 'Advisors'
    },

    includesTargetTag(item, tag) {
      return item.template?.tags.includes(tag)
    },

    push(data, task, type) {
      const has_index = data.findIndex(item => item.id === task.id + type)
      if (has_index < 0) {
        data.push(
          {
            id: task.id + type,
            text: `${task.advisor.name_prefix || ''} ${task.advisor.full_name} has already answered ${type}.`,
          })
      }
    },

    remove(data, task, type) {
      const has_index = data.findIndex(item => item.id === task.id + type)
      if (has_index > -1) {
        data.splice(has_index, 1)
      }
    }
    ,
  },
}
</script>

<style scoped lang="scss">

.mt-2px{
  margin-top: 2px;
}
.w-75px {
  width: 75px;
}

.w-85px {
  width: 85px;
}

::v-deep .el-table__expanded-cell {
  padding: 10px 0 10px 50px;
}

.email-section {
  max-height: 450px;
  overflow-y: auto;
}

/* 邮件编辑 主题 */
.email-subject-input {
  margin: 5px;

  ::v-deep .el-input__inner {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }
}
</style>
