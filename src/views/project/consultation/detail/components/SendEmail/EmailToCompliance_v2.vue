<template>
  <div class="inline-block">
    <el-tooltip
      effect="dark"
      :disabled="!toComplianceDisable"
      :content="disabledTips"
      placement="top">
      <div class="inline-block">
        <el-button
            type="primary"
            :loading="sendLoading"
            :disabled="toComplianceDisable"
            @click="beforeOpen">
          <svg-icon icon-class="email" />
          To Compliance
        </el-button>
      </div>
    </el-tooltip>

    <el-dialog
      title="Email To Compliance"
      :visible.sync="dialogVisible"
      width="1000px"
      append-to-body
      @open="openDialog"
      @closed="resetData">
      <div v-loading="loading">
        <approval-for-investment-bank-client
             v-if="isSpeciallyProjectForInvestmentBankClient"
             :project-type="project.sub_type"
             :client-id="project.client_id"
             :customized-rules="investmentBankClientCustomizedRules"
        />

        <approval-warning v-else :client-id="project.client_id" type="approval" />

        <el-alert
          v-if="filterTasks.length"
          :title="`This task will be filtered, ${filter_reason}.`"
          type="warning"
          show-icon
          class="mb-8">
          <a
            v-for="(item, index) in filterTasks"
            :key="index"
            :name="index"
            class="link"
            @click="routeToConsultantDetail(item.advisor_id)">
            {{ item.advisor['name_prefix'] || ''}}
            {{ item.advisor['full_name'] }}
            {{ repeatedTasksID.includes(item.advisor_id) ? `#${item.sequence}` : '' }}
            <slot v-if="index+1 !== filterTasks.length">,</slot>
          </a>
        </el-alert>
        <el-alert
          v-if="passTasks.length"
          title="This task will send to compliance:"
          type="success"
          show-icon
          :closable="false"
          class="mb-8 pass-task-alert">
          <template slot="title">
            <div class="flex justify-content-between">
              <div>
                <div class="success">This task will send to compliance:</div>
                <a
                  v-for="(item, index) in passTasks"
                  :key="index"
                  :name="index"
                  class="link"
                  @click="routeToConsultantDetail(item.advisor_id)">
                  {{ item.advisor['name_prefix'] || ''}}
                  {{ item.advisor['full_name'] }}
                  {{ repeatedTasksID.includes(item.advisor_id) ? `#${item.sequence}` : '' }}
                  <slot v-if="index+1 !== passTasks.length">,</slot>
                </a>
              </div>
              <div class="flex">
                <div class="danger mr-2px">*</div>
                <div>
                  <span class="success">Earliest estimated date to be scheduled:</span>
                  <br>
                  <el-date-picker
                    v-model="expect_schedule.date"
                    type="date"
                    :clearable="false"
                    format="MM/dd/yyyy"
                    value-format="yyyy-MM-dd"
                    placeholder="Selected"
                    :picker-options="pickerOptions"
                    class="mb-8" />
                  <br>
                  <el-checkbox
                    v-if="showUrgentCheckBox"
                    v-model="expect_schedule.urgent">
                    Urgent Review Required
                  </el-checkbox>
                </div>
              </div>
            </div>
          </template>
        </el-alert>

        <el-alert
v-if="advisor_job_error_list.length"
                  type="error"
                  show-icon
                  :closable="false"
                  class="mb-8">
          <div v-for="(item,index) in advisor_job_error_list" :key="index" class="line-height-normal">
            <span v-if="item.type==='company_error'">
              {{item.text_header}}
              <span v-for="(company, _index) in item.company_list" :key="_index">
                <router-link-company :data="company" />
                <span v-if="_index !== item.company_list.length-1">,</span>
              </span>
              {{item.text_footer}}
            </span>
            <span v-else>{{item.text}}</span>
          </div>
        </el-alert>

        <div v-if="displayApprovalOptions" class="approval-rules">
          Approval Rules:
          <el-checkbox
            v-if="data.approval.capvision.display"
            v-model="data.approval.capvision.value"
            :disabled="data.approval.capvision.disabled"
            label="Capvision"
            class="ml-12">
            Capvision Approval
          </el-checkbox>
          <div v-if="data.approval.client.display" class="inline-block ml-12">
            <el-checkbox
              v-model="data.approval.client.value"
              :disabled="data.approval.client.disabled"
              label="Client"
            >
              Client Approval
            </el-checkbox>
            <span v-if="data.approval.client.method" class="ml-8">( {{ data.approval.client.method }} / </span>
            <span v-if="data.approval.client.policy">{{ data.approval.client.policy }} )</span>
          </div>
          <div v-if="data.approval.client.policy === 'Internal'" class="warning inline-block ml-8">You need to send the
            CA to the client contact yourself.
          </div>
        </div>
        <el-alert
          v-if="isApprovedByEmail"
          title="This client approves calls using Email."
          class="mb-8"
          type="info" />
        <el-alert
          v-if="isEmailNoContact"
          title="The project does not have a common type of customer contact, please bind it first."
          type="error" />

        <!-- profile-format -->
        <profile-format
          v-for="item in passTasks"
          :key="item.id"
          ref="profile-format"
          :data="item"
          :custom-time-zone="selectTimeZone"
          class="display-none" />

        <!-- client Agreement -->
        <ca-copy
          v-if="hasCAContent"
          id="ca-copy"
          class="display-none"
          :data="passTasks[0].latest_submitted_pre_ca.inquiry_instance" />

        <CaPdf
v-if="hasCAContent"
               ref="ca-pdf"
               :ca-content=" {
                 title: passTasks[0].latest_submitted_pre_ca.title,
                 questions: passTasks[0].latest_submitted_pre_ca.inquiry_instance.qa_list_snapshot,
               }"
               :task-data="passTasks[0]"
               ca-type="caType"
               class="display-none" />

        <email-editor
          v-if="displayClientApprovalEmail"
          ref="email-editor"
          :options="options.emailTemplates"
          :template="template"
          :email="email"
          :to-cc-options="projectClientContactEmailList"
          @change="changeEmailTemplate" />

      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button
          type="primary"
          :disabled="isSubmitButtonDisabled"
          :loading="sendLoading"
          @click="actionSubmit">{{ submitButtonText }}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'
import Mixin from './mixin_v2'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'
import EmailAPI from '@/api/email'
import CaCopy from '@/components/Question/CACopy'
import ApprovalWarning from '@/components/ComplianceWarning/Index'
import ApprovalForInvestmentBankClient from '@/components/ComplianceWarning/components/ApprovalForInvestmentBankClient'
import ProfileFormat from '@/components/FormatProfile/profileForCopy'
import EmailEditor from '@/components/EmailEditor'
import RouterLinkCompany from '@/components/RouterLink/Company'
import { mapGetters, mapState } from 'vuex'
import CaPdf from '@/components/Question/CAPDF'
import moment from 'moment/moment'
import _ from 'lodash'
import ComplianceAPI from '@/api/compliance'

export default {
  name: 'EmailToCompliance',
  components: {
    ApprovalWarning,
    ApprovalForInvestmentBankClient,
    EmailEditor,
    ProfileFormat,
    CaCopy,
    RouterLinkCompany,
    CaPdf },
  mixins: [Mixin, TaskActionsMixin],
  data() {
    return {
      task_action_type: 'To Compliance',
      options: {
        emailTemplates: [],
      },
      filter_reason: '',
      selectTimeZone: this.project.zone_id_string,
      template: null,
      client_after_cap_approval: false,
      advisor_job_error_list: [],
      data: {
        approval: {
          capvision: {
            value: false,
            display: false,
            disabled: false,
          },
          client: {
            value: false,
            display: false,
            disabled: false,
            method: undefined,
            policy: undefined,
            ca_send_to_compliance: true,
            ca_as_attachment: false,
          },
        },
      },
      email: {
        subject: '',
        content: '',
      },
      RD_config_to_list: [],
      client_rules_send_approval_to_list: [],
      approval_bcc_list: [],
      expect_schedule: {
        date: '',
        urgent: false,
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < moment().subtract(1, 'day')
        },
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
      VikingGlobalInvestorsLPId: state => state.app.VikingGlobalInvestorsLPId,
    }),

    isVikingGlobalInvestorsLP() {
      return this.project.client_id === this.VikingGlobalInvestorsLPId
    },
    displayApprovalOptions() {
      const approval = this.data.approval
      return approval.capvision.display || approval.client.display
    },
    displayClientApprovalEmail() {
      return this.data.approval.client.value && !this.isEmailNoContact
    },
    submitButtonText() {
      return this.displayClientApprovalEmail ? 'Send' : 'OK'
    },

    isApprovedByEmail() {
      return !this.isApprovedByPortal && this.data.approval.client.value
    },

    isApprovedByPortal() {
      return this.project.client?.compliance_preference?.approve_method === 'PORTAL'
    },

    investmentBankClientCustomizedRules() {
      const has_customized_rules = this.project.client.compliance_preference?.rule?.compliance_rules?.approval
      return has_customized_rules?.master_switch && has_customized_rules[`${this.project.sub_type.toLowerCase().replace(/\s+/g, '_')}_project_rule`]
    },

    // investment bank client 对于'Consultation', 'Investor Call' 这两个类型的项目有定制化规则时，根据定制化规则发送邮件
    isSpeciallyProjectForInvestmentBankClient() {
      return ['Consultation', 'Investor Call'].includes(this.project.sub_type) && this.project.client.type === 'INVESTMENT_BANK' && this.investmentBankClientCustomizedRules?.approve_policy
    },

    // investment bank client有定制化规则时 优先取定制规则
    isClientRequiredPreCallCa() {
      if (this.isSpeciallyProjectForInvestmentBankClient && this.investmentBankClientCustomizedRules?.pre_ca) {
        return this.investmentBankClientCustomizedRules.pre_ca.required
      } else {
        return this.project.client.require_pre_call_ca
      }
    },
    isEmailNoContact() {
      const hasCommonContact = this.project.project_client_contacts.filter(
        item => item.client_contact.types.includes('COMMON')).length > 0
      return this.isApprovedByEmail && !hasCommonContact
    },
    clientApprovalNoNeed() {
      const client_approval_no_need = this.project.client?.compliance_preference?.approve_policy === 'NO_NEED'
      const client_approval_no_set = !this.project.client?.compliance_preference?.approve_policy
      return client_approval_no_need || client_approval_no_set
    },

    hasUnselectedAdvisor() {
      return this.tasks.filter(item => item.client_contact_status !== 'APPROVED').length > 0
    },

    hasUnsignedTCAdvisor() {
      return this.tasks.filter(item => !item.advisor.tc_term_valid.CONSULTATION && item.task_outsource_status !== 'IMPORTED').length > 0
    },

    hasBlacklistedAdvisor() {
      return this.tasks.filter(item => {
        return item.advisor.status === 'BLACKLIST'
      }).length > 0
    },

    toComplianceDisable() {
      return this.tasks.length > 1 || this.clientApprovalNoNeed || this.projectNeedInvestmentTarget || this.hasBlacklistedAdvisor || this.hasUnselectedAdvisor || this.hasUnsignedTCAdvisor || this.clientNotExecuting
    },

    disabledTips() {
      if (this.clientApprovalNoNeed) {
        return 'This client does not require legal approval.'
      } else if (this.clientNotExecuting) {
        return 'The client is not in the execution state'
      } else if (this.projectNeedInvestmentTarget) {
        return 'The client requires the Investment Target to be completed before it can be sent to Compliance.'
      } else if (this.tasks.length > 1) {
        return 'Only one task can be selected at a time for the \'To Compliance\' operation'
      } else if (this.hasBlacklistedAdvisor) {
        return 'The expert is on the blacklist.'
      } else if (this.hasUnselectedAdvisor) {
        return 'The expert was not selected by the client'
      } else {
        // hasUnsignedTCAdvisor
        return 'This expert needs to sign the Terms and Conditions.'
      }
    },
    projectNeedInvestmentTarget() {
      return this.checkClientRuleInvestmentTarget() && this.project.investment_target_company_ids.length < 1
    },
    needToBlindAdvisorName() {
      /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
      const client_blind_expert_name = this.project.client.preference?.blind_expert_names_in_to_client_and_portal
      const project_blind_expert_name = this.project.blind_expert_profiles
      return this.project.client.status !== 'EXECUTE' || this.project.client.compliance_status !== 'APPROVED' || client_blind_expert_name || project_blind_expert_name
    },

    hasCAContent() {
      return this.passTasks.length && this.passTasks[0].latest_submitted_pre_ca &&
        !['EMAIL', 'EXTERNAL', 'OVERRIDDEN'].includes(this.passTasks[0].latest_submitted_pre_ca.method)
    },

    showUrgentCheckBox() {
      return this.expect_schedule.date === moment().format('YYYY-MM-DD')
    },
    isSubmitButtonDisabled() {
      const noPassTasks = this.passTasks.length < 1
      const hasEmailTo = Array.isArray(this.email.to_list) && this.email.to_list.length > 0
      const needFillEmailTo = this.displayClientApprovalEmail && !hasEmailTo
      const needExpectScheduleDate = !this.expect_schedule.date

      return noPassTasks || needFillEmailTo || this.isEmailNoContact || needExpectScheduleDate
    },

    projectClientContactEmailList() {
      if (this.project?.project_client_contacts.length) {
        const result_list = []
        this.project.project_client_contacts.forEach(item => {
          const list = item.client_contact?.contact_infos_without_mosaic?.filter(temp => temp.type === 'EMAIL' && temp.value) || []
          list.forEach(i => {
            result_list.push({ email: i.value, name: item.name })
          })
        })
        return result_list
      } else {
        return []
      }
    },
  },
  watch: {
    'data.approval.client.value': {
      handler(need_email) {
        if (this.loading) {
          return
        }

        if (need_email) {
          return this.getEmailTemplateList()
            .then(() => {
              this.getEmailContent()
            })
        }
      },
    },
  },
  methods: {
    // 调接口获取所需的更多信息
    beforeOpen() {
      this.sendLoading = true
      this.getTasksByIDS()
        .then(() => {
          this.dialogVisible = true
        })
        .finally(() => {
          this.sendLoading = false
        })
    },

    openDialog() {
      this.checkRepeatedTasks()
      this.advisor_job_error_list = []
      this.loading = true
      this.loadCompliancePreference()
        .then(() => {
          this.filterTaskList()
          return this.getEmailTemplateList()
            .then(() => {
              this.getEmailContent()
            })
        })
        .finally(() => {
          this.loading = false
        })
    },

    resetData() {
      this.filterTasks = []
      this.passTasks = []
    },
    getApprovalSet(task) {
      if (task.capvision_compliance_status === 'APPROVED') {
        this.data.approval.capvision.value = false
        this.data.approval.capvision.disabled = true
        this.data.approval.client.value = true
        this.data.approval.client.disabled = true
      } else {
        this.data.approval.capvision.value = true
        this.data.approval.capvision.disabled = true
        this.data.approval.client.value = false
        this.data.approval.client.disabled = true
      }
    },
    filterTaskList() {
      this.tasks.forEach(item => {
        const notCompleted = !['COMPLETED'].includes(item.general_status)
        const hasSubmitPreCa = item.latest_submitted_pre_ca
        const preCAAnswerPassed = hasSubmitPreCa && checkCAPass(item)
        // const clientApprovalNo = !this.data.approval.client.value
        const hasSignTC = item.advisor.tc_term_valid.CONSULTATION
        const isOutsourceImported = item.task_outsource_status === 'IMPORTED'
        const isReadyToExport = item.task_outsource_status === 'READY_TO_EXPORT'
        const preCAPass = !this.isClientRequiredPreCallCa || (this.isClientRequiredPreCallCa && preCAAnswerPassed)

        if ((hasSignTC || isOutsourceImported) && notCompleted && preCAPass && !isReadyToExport) {
          this.passTasks.push(item)
          /* 如果 客户要求 capvision法务通过后 才可以发给客户法务，判断此时发给cap 还是客户 */
          if (this.client_after_cap_approval) this.getApprovalSet(item)
        } else {
          this.filterTasks.push(item)
          if (['ARRANGED'].includes(item.general_status)) this.filter_reason = 'because it has been arranged'
          if (['COMPLETED'].includes(item.general_status)) this.filter_reason = 'because it has been completed'
          if (!hasSubmitPreCa) this.filter_reason = 'because CA has not been in process or submitted'
          if (hasSubmitPreCa && ['AUTO_REJECTED', 'REJECTED'].includes(
            item.latest_submitted_pre_ca.inquiry_instance.status)) this.filter_reason = 'because CA is FAIL'
          if (isReadyToExport) this.filter_reason = 'please export the task first'
        }
      })

      function checkCAPass(item) {
        const submit_ca = item.latest_submitted_pre_ca
        return ['EXTERNAL', 'OVERRIDDEN'].includes(submit_ca.method) ||
          !['AUTO_REJECTED', 'REJECTED'].includes(submit_ca.inquiry_instance.status)
      }
    },

    async getEmailTemplateList() {
      const params = {
        has_permission: true,
        content_type: this.isApprovedByPortal ? 'PORTAL_COMPLIANCE' : 'CLIENT_LEGAL_CONSULTATION_NEED_APPROVE',
        reference_ids: [0, this.project.client_id].join(),
        reference_type: 'CLIENT',
        include_default_reference_type: true,
        sort: 'reference_id desc',
      }
      const data = await EmailAPI.getEmailList(params)
      data.list.sort((a, b) => a.rank - b.rank)
      this.options.emailTemplates = data.list.filter(item => (this.isSpeciallyProjectForInvestmentBankClient && !this.isApprovedByPortal) ? (item.tags && item.tags.includes('INVESTMENT_BANK')) : !item.tags?.includes('INVESTMENT_BANK'))
      this.template = this.options.emailTemplates[0]
    },
    async getEmailContent() {
      if (!this.passTasks.length) return

      const params = {
        query: {
          client: {
            id: this.project.client_id,
          },
          contact: {
            id: this.tasks[0].client_contact_id,
          },
          project: {
            id: this.project.id,
          },
          task: {
            id: this.tasks[0].id,
            include: ['advisor_profile.job', 'advisor', 'default_client_rate_usd', 'default_client_rate_usd_considering_unsigned_tc'],
          },
          advisor: {
            id: this.tasks[0].advisor_id,
          },
          user: {
            id: this.uid,
          },
        },
      }
      this.loading = true
      this.email = await EmailAPI.getEmailTemplateWithQuery(this.template.id, params)
        .then(data => {
          // 收件人 添加客户审核规则中的邮箱
          data.to_addresses = _.uniqBy([...data.to_addresses, ...this.client_rules_send_approval_to_list], 'email')
          data.to_list = [...new Set([...data.to_list, ...this.RD_config_to_list, ...this.client_rules_send_approval_to_list.map(item => item.email)])]
          // INVESTMENT BANK client & ECOM => 密送 user & project manager
          data.bcc_list = [...new Set([...data.bcc_list, ...this.approval_bcc_list])]
          return data
        })

      if (this.isVikingGlobalInvestorsLP) {
        const job_status = this.tasks[0].advisor.current_job ? 'Current' : 'Former'
        this.email.subject = `/PN: ${this.project.name}/EN: ${this.tasks[0].advisor.name_prefix || ''} ${this.tasks[0].advisor.full_name}/ET: ${job_status}`
      }

      // Investment Bank client 客户在compliance rules 设置需要CA Pdf作为附件
      if (this.isSpeciallyProjectForInvestmentBankClient && this.data.approval.client.ca_as_attachment) {
        this.getCaPDFAttachments(this.email)
      }

      let content = this.handleMailContent(this.email.content)
      content = this.replaceAdvisorBios(content, this.passTasks)
      content = this.replaceAdvisorCA(content)
      this.email.content = content

      if (this.$refs['email-editor']) {
        this.$refs['email-editor'].setContent()
      }
      this.loading = false
    },
    replaceAdvisorBios(email, passTasks) {
      const refs = this.$refs['profile-format'] || []
      const html = []
      let angle_id

      for (let i = 0; i < refs.length; i++) {
        let consultant_profile_HTML = refs[i].$el.innerHTML
        const task = passTasks[i]
        const angle = task.angle

        /* 添加标题 angle.title */
        if (angle && angle.id !== angle_id) {
          angle_id = angle.id
          const angle_title_HTML = `<p style="font-size: 14pt; font-weight: bold; text-decoration: underline; margin-top: 4px; margin-bottom: 4px;">${angle.name}</p>`
          consultant_profile_HTML = `${angle_title_HTML}${consultant_profile_HTML}`
        }

        if (this.needToBlindAdvisorName) {
          consultant_profile_HTML = consultant_profile_HTML.replace(passTasks[i].advisor.full_name, 'Advisor')
        }

        html.push(consultant_profile_HTML)
      }
      return email.replace('{TASK_ADVISOR_BIOS}', html.join('<br>'))
    },

    replaceAdvisorCA(email) {
      if (!this.data.approval.client.value || !this.isClientRequiredPreCallCa) return email.replace('{CA_CONTENT}', '')
      if (!document.getElementById('ca-copy')) {
        return email.replace('{CA_CONTENT}', '')
      }
      const html = '<div contenteditable="false" style="margin-top: 10px;">' +
        '<p style="margin-top: 2px; margin-bottom: 0;font-weight: bold; font-size: 14pt;text-decoration: underline; ">\n' +
        'Consultation Agreement' +
        '</p>' + document.getElementById('ca-copy').innerHTML + '</div>'
      return email.replace('{CA_CONTENT}', html)
    },

    changeEmailTemplate(val) {
      this.template = val
      this.getEmailContent()
    },
    checkCAUpload(data) {
      const latest_submitted_pre_ca = this.passTasks[0].latest_submitted_pre_ca
      if (latest_submitted_pre_ca && latest_submitted_pre_ca.method !== 'PORTAL') {
        data.email_to_client_legal.attachments = latest_submitted_pre_ca.files.map(item => {
          return { name: item.name, file_path: item.path }
        })
      }
    },

    actionSubmit() {
      if (this.advisorJobsInValid()) return

      const data = {
        project_id: this.passTasks[0].project_id,
        requests: this.passTasks.map(item => ({
          task_id: item.id,
          need_client_compliance_approve: this.data.approval.client.value,
          need_cap_compliance_approve: this.data.approval.capvision.value,
          expect_schedule_date: this.expect_schedule.date,
          urgent: this.showUrgentCheckBox ? this.expect_schedule.urgent : false,
        })),
        email_to_client_legal: this.email,
        email_template_tags: this.template.tags,
      }
      this.checkCAUpload(data)
      this.sendLoading = true
      return ProjectAPI
        .batchUpdateTaskStatusNeedApprove(data)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Change status to Need Approve!',
          })
          this.dialogVisible = false
          this.$emit('update')
        }).finally(() => {
          this.sendLoading = false
        })
    },
    async loadCompliancePreference() {
      await this.getRDConfigToList()
      await this.$store.dispatch('project/getInfo', this.project.id)
        .then(data => {
          if (data.client &&
            data.client['compliance_preference'] &&
            data.client['compliance_preference'].rule &&
            data.client['compliance_preference'].approve_policy
          ) {
            const client_rule = data.client['compliance_preference'].rule
            const capvision = {
              value: false,
              display: false,
              disabled: false,
            }
            const client = {
              value: false,
              display: false,
              disabled: false,
              method: data.client['compliance_preference'].approve_method === 'EMAIL' ? 'Email' : 'Portal',
            }

            if (this.isSpeciallyProjectForInvestmentBankClient) {
              handleForInvestmentBankClient(capvision, client, this.investmentBankClientCustomizedRules)
              if (this.investmentBankClientCustomizedRules.approve_policy === 'CAPVISION_AND_CLIENT_APPROVAL_ALL') {
                const project_manager_email = this.project.members?.find(item => item.role === 'PROJECT_MANAGER')?.user?.email
                this.approval_bcc_list = [...new Set([this.$store.state.user.info.email, project_manager_email])]
              }
            } else {
              const client_conditional_approve_type = client_rule.compliance_rules.approval.client_conditional_approve_type

              switch (data.client['compliance_preference'].approve_policy) {
                case 'NO_NEED':
                  break
                case 'CAPVISION_APPROVAL':
                  capvision.value = true
                  capvision.display = true
                  capvision.disabled = true
                  break
                /* 客户要求 capvision法务通过后 才可以发给客户法务 */
                case 'CAPVISION_THEN_CLIENT_APPROVAL':
                  this.client_after_cap_approval = true
                  capvision.value = true
                  capvision.display = true
                  capvision.disabled = true
                  client.value = false
                  client.display = true
                  client.disabled = true
                  client.policy = 'All'
                  break
                case 'CAPVISION_AND_CLIENT_APPROVAL_INTERNAL':
                  capvision.value = true
                  capvision.display = true
                  capvision.disabled = true
                  client.value = false
                  client.display = false
                  client.disabled = true
                  client.policy = 'Internal'
                  break
                case 'CAPVISION_AND_CLIENT_APPROVAL_SOME':
                  capvision.value = true
                  capvision.display = true
                  capvision.disabled = true
                  client.value = needClientApproveForSOME(this.tasks[0], client_conditional_approve_type)
                  client.display = true
                  client.disabled = needClientApproveForSOME(this.tasks[0], client_conditional_approve_type)
                  client.policy = getSomeDetail(client_conditional_approve_type)
                  break
                case 'CAPVISION_AND_CLIENT_APPROVAL_ALL':
                  capvision.value = true
                  capvision.display = true
                  capvision.disabled = true
                  client.value = true
                  client.display = true
                  client.disabled = true
                  client.policy = 'All'
                  break
              }
            }

            this.data.approval.capvision = capvision
            this.data.approval.client = client

            this.client_rules_send_approval_to_list = client_rule.compliance_rules?.approval?.names_and_emails_to_send_approval_to || []
            return data.client['compliance_preference']
          }
        })

      function handleForInvestmentBankClient(capvision, client, custom_rules) {
        switch (custom_rules.approve_policy) {
          case 'CAPVISION_APPROVAL':
            capvision.value = true
            capvision.display = true
            capvision.disabled = true
            break
          case 'CAPVISION_AND_CLIENT_APPROVAL_ALL':
            capvision.value = true
            capvision.display = true
            capvision.disabled = true
            client.value = true
            client.display = true
            client.disabled = true
            client.policy = 'All'
            client.ca_send_to_compliance = custom_rules.pre_ca.send_to_client_legal_in_email
            client.ca_as_attachment = custom_rules.pre_ca.send_to_client_legal_in_email && custom_rules.pre_ca.send_to_client_legal_in_email_attachment
            break
        }
      }

      function getSomeDetail(value) {
        const options = [
          { value: 'Based on CA', label: 'CA[Hold]' },
          { value: 'Listed company experts', label: 'Listed' },
          { value: 'Other', label: 'Others' },
        ]

        return options.filter(item => item.value === value)[0].label
      }

      // 客户法务规则设置依据CA状态为hold时才需要审核。当CA:AUTO_PASSED 无需强制通知客户法务审核
      function needClientApproveForSOME(task, client_conditional_approve_type) {
        const task_ca_not_auto_passed = task.latest_submitted_pre_ca?.inquiry_instance?.status !== 'AUTO_PASSED'
        return task_ca_not_auto_passed || client_conditional_approve_type !== 'Based on CA'
      }
    },

    getRDConfigToList() {
      const params = {
        type: 'EMAIL_ADDRESS',
      }
      return ComplianceAPI.getGlobalSetting(params)
        .then(data => {
          this.RD_config_to_list = data.find(item => item.key === 'CAPVISION_COMPLIANCE_OFFICER_EMAIL_GROUP')?.value?.split(',') || []
        })
    },

    // https://www.notion.so/capvision/Compliance-ICOM-block-d86cb32ee9da4bddb86531b538a1a678?pvs=4
    advisorJobsInValid() {
      const advisor_name = this.passTasks[0].advisor.full_name
      const jobs = this.passTasks[0].advisor.jobs
      const current_jobs = jobs.filter(item => item.is_current)
      const error_list = []

      if (current_jobs.length > 0) {
        const current_job_no_type = current_jobs.filter(item => !item.company.type)
        if (current_job_no_type.length > 0) {
          error_list.push({
            type: 'company_error',
            text_header: `${advisor_name}’s current employer(s) - `,
            company_list: _.uniqBy(current_job_no_type.map(item => item.company), 'id'),
            text_footer: ' - is missing a PLC (eg private, PLC) status, please edit the company to send.',
          })
        }
      } else {
        error_list.push({ text: `${advisor_name} is missing a current employment record in profile(Independent Consultant is considered a current employer).` })
      }

      const two_years_ago_jobs = jobs.filter(item => moment(item.start_date) < moment().subtract(2, 'years'))
      if (two_years_ago_jobs.length > 0) {
        const need_to_be_checked = jobs.filter(item => item.is_current || moment(item.end_date) > moment().subtract(2, 'years'))
        if (need_to_be_checked.filter(item => !item.start_date || (!item.end_date && !item.is_current)).length > 0) {
          error_list.push({ text: `${advisor_name}’s employment history contains invalid dates during the last 24 months and must be edited.` })
        }

        const no_type = need_to_be_checked.filter(item => !item.is_current && !item.company.type)
        if (no_type.length > 0) {
          error_list.push({ type: 'company_error',
            text_header: `${advisor_name}’s former employer(s) - `,
            company_list: _.uniqBy(no_type.map(item => item.company), 'id'),
            text_footer: ' - is missing a PLC (eg private, PLC) status, please edit the company to send.' })
        }
      } else {
        error_list.push({ text: `${advisor_name}’s employment history must go back 24 months.` })
      }

      this.advisor_job_error_list = error_list
      return error_list.length > 0
    },
  },
}
</script>

<style scoped lang="scss">
.approval-rules {
  font-size: 12px;
  margin: 0 0 0 12px;
}

.ml-12 {
  margin-left: 12px;
}

.pass-task-alert {
  align-items: unset;

  ::v-deep .el-date-editor.el-input {
    width: 150px;
  }

  ::v-deep .el-alert__content {
    width: 100%;
  }
}

.mr-2px {
  margin-right: 2px;
}

</style>
