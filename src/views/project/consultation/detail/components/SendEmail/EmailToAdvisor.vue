<template>
  <div class="inline-block">
    <el-button type="primary" @click="dialogVisible = true">
      <svg-icon icon-class="email" />
      To {{type}}
    </el-button>

    <el-dialog
      :title="`Email To ${type}`"
      :visible.sync="dialogVisible"
      width="1000px"
      @open="openDialog">

      <el-table
        v-loading="tc_loading || sq_loading || ca_loading"
        :data="emailData">
        <el-table-column
          :label="type"
          prop="name">
          <template #default="scope">
            <el-link type="primary" :underline="false" @click.stop="routeToConsultantDetail(scope.row.advisor_id)">
              {{scope.row.advisor.name_prefix || ''}} {{scope.row.advisor.firstname}} {{scope.row.advisor.lastname}} {{repeatIds.includes(scope.row.advisor_id)
                ? `#${scope.row.sequence}` : ''}}
            </el-link>
            <span v-if="!scope.row.advisor.email" class="danger">(No Email)</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="isLeads"
          label="TC"
          :width="tc_thWidth">
          <template #header>
            <el-checkbox
              v-model="allChecked.tc"
              :class="{'checkbox-indeterminate': tcIsIndeterminate}"
              disabled
              @change="(checked)=>{toggleSelectAll(checked, 'tc_checked')}">
              TC
            </el-checkbox>
          </template>
          <template #default="scope">
            <div class="no-wrap">
              <el-checkbox
                v-model="scope.row.portal.tc_checked"
                disabled />
              <slot v-if="scope.row.portal.tc_checked">
                <tc-search
                  v-model="scope.row.portal.tc"
                  :init-request="false"
                  :init-list="options.tc"
                  class="ml-8"
                  @change="(val) => {handleTcChange(val, scope.row)}" />
                <el-select
                  v-model="scope.row.portal.tc_locale"
                  value-key="id"
                  class="w-85px">
                  <slot v-if="scope.row.portal.tc">
                    <el-option
                      v-for="item in scope.row.portal.tc.templates"
                      :key="item.id"
                      :label="item.locale"
                      :value="item" />
                  </slot>
                </el-select>
                <el-input
                  v-model="scope.row.portal.tc_rate"
                  type="number"
                  :step="150"
                  :min="0"
                  class="w-80px" />
                <el-select
                  v-model="scope.row.portal.tc_rate_currency"
                  class="w-80px">
                  <el-option
                    v-for="item in options.currency"
                    :key="item"
                    :label="item"
                    :value="item" />
                </el-select>
              </slot>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="existSQ"
          label="SQ"
          :width="thWidth.sm">
          <template #header>
            <el-tooltip :disabled="existSQ" effect="dark" content="Need to create first" placement="top-start">
              <el-checkbox
                v-model="allChecked.sq"
                class="test-class"
                :class="{'checkbox-indeterminate': sqIsIndeterminate}"
                :disabled="!existSQ"
                @change="(checked)=>{toggleSelectAll(checked, 'SQ_SEND')}">
                <span :class="{'danger': !existSQ}">SQ
                  <i v-if="!existSQ" class="el-icon-info" />
                </span>
              </el-checkbox>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.portal.trigger_events.SQ_SEND.checked"
              :disabled="scope.row.portal.trigger_events.SQ_SEND.disabled" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="existCA"
          label="CA"
          :width="ca_thWidth">
          <template #header>
            <el-checkbox
              v-model="allChecked.ca"
              :class="{'checkbox-indeterminate': caIsIndeterminate}"
              @change="(checked)=>{toggleSelectAll(checked, 'CA_SEND')}">CA
            </el-checkbox>
          </template>
          <template #default="scope">
            <div class="no-wrap">
              <el-checkbox
                v-model="scope.row.portal.trigger_events.CA_SEND.checked"
                :disabled="scope.row.portal.trigger_events.CA_SEND.disabled" />
              <el-select
                v-show="scope.row.portal.trigger_events.CA_SEND.checked"
                v-model="scope.row.portal.trigger_events.CA_SEND.ca"
                value-key="id"
                filterable
                :disabled="scope.row.portal.trigger_events.CA_SEND.select_disabled"
                class="ml-8">
                <el-option
                  v-for="item in options.ca"
                  :key="item.id"
                  :label="item.name"
                  :value="item" />
              </el-select>
              <el-select
                v-show="scope.row.portal.trigger_events.CA_SEND.checked"
                v-model="scope.row.portal.trigger_events.CA_SEND.ca_branch_id"
                class="w-85px">
                <el-option
                  v-for="item in options.caLang"
                  :key="item.id"
                  :label="item.locale"
                  :value="item.id" />
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="Availability"
          :width="thWidth.sm">
          <template #header>
            <el-checkbox
              v-model="allChecked.availability"
              :class="{'checkbox-indeterminate': agIsIndeterminate}"
              @change="(checked)=>{toggleSelectAll(checked, 'ADVISOR_SCHEDULE_SEND')}">Availability
            </el-checkbox>
          </template>
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.portal.trigger_events.ADVISOR_SCHEDULE_SEND.checked"
              :disabled="scope.row.portal.trigger_events.ADVISOR_SCHEDULE_SEND.disabled" />
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="existNoSend" :loading="sendLoading" @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'
import TcAPI from '@/api/tc.js'
import Mixin from './mixin'
import TcSearch from '@/components/SelectRemoteSearch/TcSearch'

export default {
  name: 'EmailToAdvisor',
  components: { TcSearch },
  mixins: [Mixin],
  props: {
    type: String,
  },
  data() {
    return {
      allChecked: {
        tc: false,
        sq: false,
        ca: false,
        availability: false,
      },
      thWidth: {
        sm: '120',
        lg: '320',
        xl: '490',
      },
      options: {
        currency: ['USD', 'RMB'],
        tc: [],
        ca: [],
        caLang: [],
      },
      ca_required: false,
      emailData: [],
    }
  },
  computed: {
    isLeads() {
      return this.type === 'Leads'
    },
    existNoEmail() {
      return this.emailData.findIndex(item => !item.advisor.email) > -1
    },
    existNoSend() {
      return this.emailData.every(item => {
        const keys = Object.keys(item.portal.trigger_events)
        let flag = true && !item.portal.tc_checked
        keys.forEach(key => {
          if (!item.portal.trigger_events[key].disabled && item.portal.trigger_events[key].checked) {
            flag = false
          }
        })
        return flag
      })
    },
    tcIsIndeterminate() {
      const list = this.emailData
      const checked_length = list.filter(item => item.portal.tc_checked).length
      this.allCheckedWatch(list, checked_length, 'tc')
      return checked_length > 0 && checked_length < list.length
    },
    sqIsIndeterminate() {
      const list = this.emailData.filter(item => !item.portal.trigger_events['SQ_SEND'].disabled)
      const checked_length = list.filter(item => item.portal.trigger_events['SQ_SEND'].checked).length
      this.allCheckedWatch(list, checked_length, 'sq')
      return checked_length > 0 && checked_length < list.length
    },
    caIsIndeterminate() {
      const list = this.emailData.filter(item => !item.portal.trigger_events['CA_SEND'].disabled)
      const checked_length = list.filter(item => item.portal.trigger_events['CA_SEND'].checked).length
      this.allCheckedWatch(list, checked_length, 'ca')
      return checked_length > 0 && checked_length < list.length
    },
    agIsIndeterminate() {
      const list = this.emailData.filter(item => !item.portal.trigger_events['ADVISOR_SCHEDULE_SEND'].disabled)
      const checked_length = list.filter(item => item.portal.trigger_events['ADVISOR_SCHEDULE_SEND'].checked).length
      this.allCheckedWatch(list, checked_length, 'availability')
      return checked_length > 0 && checked_length < list.length
    },
    tc_thWidth() {
      return this.emailData.find(item => item.portal.tc_checked) ? this.thWidth.xl : this.thWidth.sm
    },
    ca_thWidth() {
      return this.emailData.find(item => item.portal.trigger_events.CA_SEND.checked) ? this.thWidth.lg : this.thWidth.sm
    },
  },
  methods: {
    openDialog() {
      Promise.all([this.checkSqExisted(), this.checkCaExisted(), this.checkCaRequired(), this.getInitTcList()])
        .then(() => {
          this.repeatTasks()
          this.getEmailData()
        })
    },
    getEmailData() {
      this.emailData = JSON.parse(JSON.stringify(this.tasks))
        .map(task => {
          task.portal = {
            tc_rate: 1000,
            tc_rate_currency: 'USD',
            tc_locale: null,
            tc: null,
            tc_checked: this.isLeads,
            trigger_events: {
              SQ_SEND: this.filterETA('SQ_SEND', task),
              CA_SEND: this.filterETA('CA_SEND', task),
              ADVISOR_SCHEDULE_SEND: this.filterETA('ADVISOR_SCHEDULE_SEND', task),
            },
          }
          if (this.options.tc.length) {
            task.portal.tc = this.options.tc.find(tc => tc.is_current)
            const template = task.portal.tc.templates.find(template => template.locale === task.advisor.locale)
            task.portal.tc_locale = template || task.portal.tc.templates[0]
          }
          return task
        })
    },
    getInitTcList() {
      this.allChecked.tc = this.isLeads
      if (!this.options.tc.length && this.isLeads) {
        this.tc_loading = true
        return TcAPI.getTCList().then(data => {
          this.options.tc = data
        }).finally(() => {
          this.tc_loading = false
        })
      }
    },
    allCheckedWatch(list, checked_length, key) {
      if (checked_length === list.length) {
        this.allChecked[key] = true
      }
      if (checked_length === 0) {
        this.allChecked[key] = false
      }
    },
    toggleSelectAll(checked, key) {
      if (key === 'tc_checked') {
        this.emailData.forEach(item => {
          item.portal.tc_checked = checked
        })
      } else {
        this.emailData.forEach(item => {
          if (item.portal.trigger_events[key].disabled !== true) {
            item.portal.trigger_events[key].checked = checked
          }
        })
      }
    },
    handleTcChange(val, row) {
      const template = val.templates.find(template => template.locale === row.advisor.locale)
      row.portal.tc_locale = template || val.templates[0]
    },
    sendEmails() {
      if (this.existNoEmail) {
        return this.$alert('Exist member has no email', 'Notice', {
          confirmButtonText: 'OK',
          type: 'error',
        })
      }
      this.$confirm('Confirm to send emails?', 'Notice', {
        type: 'warning',
      })
        .then(() => {
          const requests = this.emailData.filter(item => this.filterETAEmailData(item))
            .map(item => {
              const trigger_events = []
              // for (const arr of Object.entries(item.portal.trigger_events)) {
              //   console.log(arr);
              //   if (arr[1]) trigger_events.push(arr[0])
              // }
              for (const key in item.portal.trigger_events) {
                if (Object.prototype.hasOwnProperty.call(item.portal.trigger_events, key) && key !== 'CA_SEND') {
                  const event_item = item.portal.trigger_events[key]
                  if (!event_item.disabled && event_item.checked) {
                    trigger_events.push(key)
                  }
                }
              }

              return {
                portal: {
                  type: 'ADVISOR_PRE_CALL',
                  task_id: item.id,
                  advisor_id: item.advisor_id,
                  task_ca: item.portal.trigger_events.CA_SEND.checked ? {
                    inquiry_id: item.portal.trigger_events.CA_SEND.ca.id,
                    inquiry_branch_id: item.portal.trigger_events.CA_SEND.ca_branch_id,
                  } : undefined,
                  advisor_tc: item.portal.tc_checked ? {
                    tc_id: item.portal.tc.id,
                    tc_template_id: item.portal.tc_locale.id,
                    rate: item.portal.tc_rate,
                    rate_currency: item.portal.tc_rate_currency,
                  } : undefined,
                  trigger_events,
                },
              }
            })
          const params = { requests }
          this.sendLoading = true
          return ProjectAPI.portalToAdvisor(params)
            .then(() => {
              this.$message({
                type: 'success',
                message: 'Success to send emails!',
              })
              this.dialogVisible = false
              this.updateTasksStatus()
            })
            .finally(() => {
              this.sendLoading = false
            })
        })
        .catch(() => {
        })
    },
    updateTasksStatus() {
      this.$emit('update:tasks', this.tasks.map((item, index) => {
        const trigger_events = this.emailData[index].portal.trigger_events
        for (const key in trigger_events) {
          switch (key) {
            case 'SQ_SEND':
              if (!trigger_events[key].disabled && trigger_events[key].checked && item.sq_status === 'INITIAL') item.sq_status = key
              break
            case 'CA_SEND':
              if (!trigger_events[key].disabled && trigger_events[key].checked && item.ca_status === 'INITIAL') item.ca_status = key
              break
            case 'ADVISOR_SCHEDULE_SEND':
              if (!trigger_events[key].disabled && trigger_events[key].checked && item.advisor_schedule_status === 'INITIAL') item.advisor_schedule_status = key
              break
          }
        }
        if (this.emailData[index].portal.tc_checked && item.tc_status === 'INITIAL') {
          item.tc_status = 'SENT'
        }
        return item
      }))
    },
  },
}
</script>

<style lang="scss" scoped>
  .checkbox-indeterminate .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }

  .checkbox-indeterminate .el-checkbox__inner:before {
    content: "";
    position: absolute;
    display: block;
    background-color: #fff;
    height: 2px;
    transform: scale(.5);
    left: 0;
    right: 0;
    top: 5px;
  }

  .checkbox-indeterminate .el-checkbox__inner:after {
    display: none;
  }

  .w-85px {
    width: 85px;
  }

  .w-80px {
    width: 80px;
  }
</style>
