// import Tinymce from '@/components/Tinymce'
// import EmailTemplateSearch from '@/components/SelectRemoteSearch/EmailTemplateSearch'
import elDragDialog from '@/directive/el-drag-dialog'
import ProjectAPI from '@/api/project.js'
import InquiryAPI from '@/api/inquiry.js'
import ClientAPI from '@/api/client'
import { mapState } from 'vuex'

export default {
  // components: { Tinymce, EmailTemplateSearch },
  directives: { elDragDialog },
  props: {
    tasks: Array,
    project: Object,
  },
  data() {
    return {
      dialogVisible: false,
      tc_loading: false,
      sq_loading: false,
      ca_loading: false,
      sendLoading: false,
      existSQ: false,
      existCA: false,
      rejectedTasks: [],
      passTasks: [],
      repeatIds: [],
      rulesLoading: false,
    }
  },
  computed: {
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  methods: {
    repeatTasks() {
      this.repeatIds = []
      const repeat_ids = {}
      this.tasks.forEach(item => {
        if (!repeat_ids[`${item.advisor_id}`]) {
          repeat_ids[`${item.advisor_id}`] = 1
        } else {
          repeat_ids[`${item.advisor_id}`]++
        }
      })
      for (const id in repeat_ids) {
        if (Object.prototype.hasOwnProperty.call(repeat_ids, id) &&
          repeat_ids[id] > 1) {
          this.repeatIds.push(Number(id))
        }
      }
    },
    routeToConsultantDetail(advisor_id) {
      const route = this.$router.resolve({
        name: 'ConsultantDetail',
        params: { advisor_id },
      })
      window.open(route.href, '_blank')
    },
    checkSqExisted() {
      this.sq_loading = true
      return ProjectAPI.getProjectInquiryBranches(this.project.id)
        .then(data => {
          this.existSQ = data.sq && data.sq.branches.length > 0
        })
        .finally(() => {
          this.sq_loading = false
        })
    },
    checkCaExisted() {
      const data = {
        types: 'PRE_CALL_CA',
        extra: 'branches',
        'client.page': 0,
        client_id: this.project.client_id,
      }
      this.ca_loading = true
      return InquiryAPI.getInquiryList(data)
        .then(data => {
          this.existCA = data.list.length > 0
          this.options.ca = data.list
          this.options.caLang = this.options.ca.length
            ? data.list[0].branches.filter(item => item.is_default_using)
            : []
        })
        .finally(() => {
          this.ca_loading = false
        })
    },

    checkCaRequired() {
      return ClientAPI.getClient(this.project.client_id, '')
        .then(data => {
          this.ca_required = data.require_pre_call_ca
        })
    },

    filterOutreach(val) {
      // val.most_recent_email_status === 'danger' 专家当前使用的主邮箱在最近的一次发送未成功
      // val.list_wise_email_status === 'danger' 专家当前使用的主邮箱在最近的邮箱验证未成功
      // val.advisor_decline.is_valid === true 专家在某次邮件中 选择了decline（一键拒绝,后续该项目相关邮件不可以再发送给专家）
      // 专家工作经历与项目的investment target 冲突
      /* SGDB 不做邮件状态限制*/
      const email_status_for_US = (val.most_recent_email_status !== 'danger' &&
        val.list_wise_email_status !== 'danger') || this.isSGDB

      return val.ca_status === 'INITIAL' && val.sq_status === 'INITIAL' &&
        val.advisor_schedule_status === 'INITIAL' && !this.existDNC(val) &&
        !this.leadsInBlacklist(val) && email_status_for_US &&
        !val.advisor_decline?.is_valid &&
        val.advisor.contact_infos.find(item => item.type === 'EMAIL') &&
        !this.governmentEmail(val) && !val.advisor_conflict_with_investment_target
    },

    governmentEmail(data) {
      const gov_email_reg = /^(gov|go|mil)$/g
      const email_list = data.advisor.contact_infos.filter(
        item => item.type === 'EMAIL' && item.value)
      if (email_list.length) {
        return email_list.filter(item => {
          const suffix_list = item.value.split('@')[1].split('.')
          return suffix_list.find(i => gov_email_reg.test(i))
        }).length > 0
      } else {
        return false
      }
    },

    existDNC(data) {
      return !!data.advisor.jobs && checkValid(data.advisor.jobs)

      function checkValid(jobs) {
        if (jobs.length < 1) return false
        return jobs.filter(item => item.legal_validate_result &&
          !item.legal_validate_result.valid).length > 0
      }
    },

    leadsInBlacklist(data) {
      return data.advisor.status === 'BLACKLIST'
    },

    filterETA(key, task) {
      let result = {}
      switch (key) {
        case 'SQ_SEND': {
          // 不存在 sq || 客户审批中 || 客户审批通过 disabled = true
          const disabled = !this.existSQ ||
            task.client_contact_status === 'SENT' ||
            task.client_contact_status === 'APPROVED'
          result = {
            disabled,
            // 未发送给专家 && 未发送给客户 && 存在SQ 默认 true
            // 存在SQ(existSQ) || (存在SQ && 已发送给专家 && 未发送给客户 ) || (存在SQ && 已发送给专家 && 客户 reject) 默认 false
            checked: !disabled && task.sq_status === 'INITIAL',
          }
          break
        }
        case 'CA_SEND': {
          let ca
          let ca_branch_id
          if (this.existCA) {
            ca = this.options.ca[0]
            const lang = this.options.caLang.find(
              item => item.locale === task.advisor.locale)
            ca_branch_id = lang ? lang.id : this.options.caLang[0].id
          } else {
            ca = null
            ca_branch_id = null
          }

          // CA 是否必须发 判断移至 client profile 接口
          const required_ca = this.options.ca.length && this.ca_required
          // 不存在CA || 客户审批中 || 客户审批通过 disabled = true
          const disabled = !this.existCA ||
            ['SENT', 'APPROVED', 'HOLD'].includes(task.client_compliance_status)

          result = {
            // 未发送给专家 && 未发送给客户 && 存在CA 默认 true
            // 存在CA(existCA) || (存在CA && 已发送给专家 && 未发送给客户 ) || (存在CA && 已发送给专家 && 客户 reject) 默认 false
            checked: !disabled &&
              (task.ca_status === 'INITIAL' || task.ca_status === 'RESPONDED'),
            disabled: disabled || !!required_ca,
            select_disabled: !!required_ca,
            ca,
            ca_branch_id,
          }
          break
        }
        case 'ADVISOR_SCHEDULE_SEND':
          result = {
            checked: task.advisor_schedule_status === 'INITIAL',
            disabled: ['ARRANGED', 'COMPLETED'].includes(task.general_status),
          }
          break
      }
      return result
    },
    filterETAEmailData(val) {
      const temp = val.portal
      return temp['tc_checked'] ||
        temp.trigger_events['ADVISOR_SCHEDULE_SEND'].checked ||
        temp.trigger_events['CA_SEND'].checked ||
        temp.trigger_events['SQ_SEND'].checked
    },
    filterETClient(val) {
      return val.sq_status !== 'SENT'
    },
    filterETCompliance(val) {
      return val.ca_status !== 'SENT'
    },

  },
}
