<template>
  <div class="inline-block">
    <el-button type="primary" :loading="sendLoading" @click="show">
      Need Approve
    </el-button>

    <el-dialog
      title="Need Approve"
      :visible.sync="dialogVisible"
      width="1100px"
      @closed="hide">
      <div>
        <approval-warning v-if="!rulesLoading" :client-rule="client_compliance_rules" />
        <div v-if="rejectedTasks.length" class="el-alert el-alert--warning is-light mb-8">
          <i class="el-alert__icon el-icon-warning is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks will be filtered:</span>
            <p class="el-alert__description">
              <a
                v-for="(item, index) in rejectedTasks"
                :key="index"
                :name="index"
                class="link"
                @click="routeToConsultantDetail(item.advisor_id)">
                {{item.advisor.name_prefix || ''}} {{item.advisor.firstname}} {{item.advisor.lastname}} {{repeatIds.includes(item.advisor_id) ?
                  `#${item.sequence}` : ''}}
                <span v-if="index+1 !== rejectedTasks.length">, </span>
              </a>
            </p>
          </div>
        </div>
        <div v-if="passTasks.length" role="alert" class="el-alert el-alert--success is-light">
          <i class="el-alert__icon el-icon-success is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks will send to approval:</span>
            <p class="el-alert__description">
              <a
                v-for="(item, index) in passTasks"
                :key="index"
                :name="index"
                class="link"
                @click="routeToConsultantDetail(item.advisor_id)">
                {{item.advisor.name_prefix || ''}} {{item.advisor.firstname}} {{item.advisor.lastname}} {{repeatIds.includes(item.advisor_id) ?
                  `#${item.sequence}` : ''}}
                <span v-if="index+1 !== passTasks.length">, </span>
              </a>
            </p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="!passTasks.length"
          :loading="sendLoading"
          icon="el-icon-check"
          @click="actionSubmit">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project.js'
import ApprovalWarning from '@/components/ComplianceWarning/Approval'
import Mixin from './mixin'

export default {
  name: 'EmailToCompliance',
  components: { ApprovalWarning },
  mixins: [Mixin],
  props: {
    clientComplianceRules: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {}
  },
  mounted() {
  },
  methods: {
    show() {
      this.repeatTasks()
      this.initTaskList()
      this.getClientComplianceRules()
      this.dialogVisible = true
    },
    hide() {
      this.passTasks = []
      this.rejectedTasks = []
    },
    initTaskList() {
      this.tasks.forEach(item => {
        if (this.filterETCompliance(item)) {
          this.passTasks.push(item)
        } else {
          this.rejectedTasks.push(item)
        }
      })
    },
    actionSubmit() {
      this.sendLoading = true
      return this
        .updateTasksStatus()
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Change status to Need Approve!',
          })
          this.dialogVisible = false
        }).finally(() => {
          this.sendLoading = false
        })
    },
    updateTasksStatus() {
      const data = {
        project_id: this.passTasks[0].project_id,
        requests: this.passTasks.map(item => {
          return {
            task_id: item.id,
          }
        }),
      }
      return ProjectAPI
        .batchUpdateTaskStatusNeedApprove(data)
        .then(() => {
          this.$emit('update:tasks', this.passTasks.map(item => {
            if (item.client_compliance_status === 'INITIAL') item.client_compliance_status = 'SENT'
            return item
          }))
        })
    },
  },
}
</script>

<style scoped>

</style>
