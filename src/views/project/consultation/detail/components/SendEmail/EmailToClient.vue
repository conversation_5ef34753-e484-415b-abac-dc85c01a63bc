<template>
  <div class="inline-block">
    <el-button type="primary" :loading="sendLoading" @click="show">
      <svg-icon icon-class="email" />
      Recommend
    </el-button>

    <el-dialog
      title="Recommend"
      :visible.sync="dialogVisible"
      width="420px"
      @closed="hide">
      <div>
        <recommend-email class="mb-8" :rules="compliance_rules" type="recommend_email" />
        <div v-if="rejectedTasks.length" class="el-alert el-alert--warning is-light mb-8">
          <i class="el-alert__icon el-icon-warning is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks are already recommended:</span>
            <p class="el-alert__description">
              <a
                v-for="(item, index) in rejectedTasks"
                :key="index"
                :name="index"
                class="link"
                @click="routeToConsultantDetail(item.advisor_id)">
                {{item.advisor.name_prefix || ''}}
                {{item.advisor.firstname}}
                {{item.advisor.lastname}}
                {{repeatIds.includes(item.advisor_id) ? `#${item.sequence}` : ''}}
                <span v-if="index+1 !== rejectedTasks.length">, </span>
              </a>
            </p>
          </div>
        </div>
        <div v-if="passTasks.length" role="alert" class="el-alert el-alert--success is-light">
          <i class="el-alert__icon el-icon-success is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks will send email:</span>
            <p class="el-alert__description">
              <a
                v-for="(item, index) in passTasks"
                :key="index"
                :name="index"
                class="link"
                @click="routeToConsultantDetail(item.advisor_id)">
                {{item.advisor.name_prefix || ''}}
                {{item.advisor.firstname}}
                {{item.advisor.lastname}}
                {{repeatIds.includes(item.advisor_id) ? `#${item.sequence}` : ''}}
                <span v-if="index+1 !== passTasks.length">, </span>
              </a>
            </p>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="!passTasks.length"
          :loading="sendLoading"
          @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import ProjectAPI from '@/api/project.js'
import Mixin from './mixin'
import ClientAPI from '@/api/client'
import RecommendEmail from '@/components/ComplianceWarning/RecommendEmail'

export default {
  name: 'EmailToClient',
  components: { RecommendEmail },
  mixins: [Mixin],
  data() {
    return {
      compliance_rules: undefined,
    }
  },
  mounted() {
  },
  methods: {
    show() {
      if (!this.project.project_client_contacts.length) {
        return this.$message({
          message: 'The project has no client contact.',
          type: 'error',
        })
      }

      return Promise
        .all([
          this.loadClientCompliancePreference(this.project.client_id),
          this.checkSqExisted(),
        ])
        .then(() => {
          this.repeatTasks()
          this.initTaskList()
          this.dialogVisible = true
        })
    },
    hide() {
      this.passTasks = []
      this.rejectedTasks = []
    },
    initTaskList() {
      this.tasks.forEach(item => {
        if (this.filterActionRecommend(item)) {
          this.passTasks.push(item)
        } else {
          this.rejectedTasks.push(item)
        }
      })
    },
    /**
     * 已经被 Arrange, Complete 不可再 Recommend
     * @param data
     * @returns {boolean}
     */
    filterActionRecommend(data) {
      return !['ARRANGED', 'COMPLETED'].includes(data.general_status)
    },
    sendEmails() {
      const params = {
        portal: {
          type: 'CONTACT_PRE_CALL',
          project_id: this.project.id,
          task_id_set: this.passTasks.map(item => item.id),
        },
      }
      this.sendLoading = true
      return ProjectAPI.portalToClient(params).then(() => {
        this.$message({
          type: 'success',
          message: 'Success to send emails!',
        })
        this.dialogVisible = false
        this.updateTasksStatus()
      }).finally(() => {
        this.sendLoading = false
      })
    },
    updateTasksStatus() {
      this.$emit('update:tasks', this.passTasks.map(item => {
        if (item.client_contact_status === 'INITIAL') item.client_contact_status = 'SENT'
        // client portal 更改状态 db系统内不更改
        // if (item.client_contact_schedule_status === 'INITIAL') item.client_contact_schedule_status = 'SENT'
        return item
      }))
    },
    loadClientCompliancePreference(client_id) {
      return ClientAPI
        .getCompliancePreference(client_id)
        .then(data => {
          this.compliance_rules = data['compliance_preference'].rule.compliance_rules
        })
    },
  },
}
</script>

<style scoped>

</style>
