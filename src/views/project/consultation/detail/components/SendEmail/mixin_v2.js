import Tinymce from '@/components/Tinymce'
import elDragDialog from '@/directive/el-drag-dialog'
import ProjectAPI from '@/api/project.js'
import _ from 'lodash'
import { mapGetters, mapState } from 'vuex'
import ComplianceAPI from '@/api/compliance'

export default {
  components: { Tinymce },
  directives: { elDragDialog },
  props: {
    tasks: Array,
    project: Object,
  },
  data() {
    return {
      dialog_loading: false,
      dialogVisible: false,
      loading: false,
      sendLoading: false,
      existSQ: true,
      repeatedTasksID: [],
      filterTasks: [],
      passTasks: [],
      rulesLoading: false,
      lead_email_vendor: null,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
      'roles',
    ]),

    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
    clientNotExecuting() {
      return this.project.client.status !== 'EXECUTE'
    },
    toComplianceDisable() {
      return this.tasks.filter(
        item => item.client_contact_status !== 'APPROVED' ||
            !item.advisor.tc_term_valid.CONSULTATION).length > 0 ||
        this.project.client.status !== 'EXECUTE'
    },

    allowOutreach() {
      return this.roles.some(item => item.role.name === 'AllowOutreach')
    },
  },
  methods: {
    checkRepeatedTasks() {
      this.repeatedTasksID = []
      const ids = this.tasks.map(item => item.advisor_id)
      ids.sort().sort((a, b) => {
        if (a === b && this.repeatedTasksID.indexOf(a) === -1) {
          this.repeatedTasksID.push(a)
        }
      })
    },
    filterTaskList() {
      this.tasks.forEach(item => {
        if (!['COMPLETED'].includes(item.general_status)) {
          this.passTasks.push(item)
        } else {
          this.filterTasks.push(item)
        }
      })
    },

    filterTaskListForAdvisor() {
      this.tasks.forEach(item => {
        if (this.PassingConditions(item)) {
          this.passTasks.push(item)
        } else {
          this.filterTasks.push(item)
        }
      })
    },
    /* task 没有completed
    *  专家工作经历不在黑名单公司
    * 专家不在黑名单
    * 专家没有拒绝这个项目
    * 专家有邮箱
    * 非政府雇员的专家 (邮箱后缀包含 .gov || .go || .mil)
    * 专家工作经历与项目的investment target 冲突
    * 专家邮箱 没有通过 listwise || sendgrid */
    PassingConditions(item) {
      return !['COMPLETED'].includes(item.general_status) &&
        !this.checkDNC(item) && !this.advisorInBlacklist(item) &&
        !this.advisorDeclined(item) && !this.advisorNoEmail(item) && !this.governmentEmail(item) &&
        !this.emailStatusFailed(item) && !item.advisor_conflict_with_investment_target
    },

    emailStatusFailed(item) {
      return item.most_recent_email_status === 'danger' || item.list_wise_email_status === 'danger'
    },

    checkDNC(data) {
      return !!data.advisor.jobs && checkValid(data.advisor.jobs)

      function checkValid(jobs) {
        if (jobs.length < 1) return false
        return jobs.filter(item => item.legal_validate_result &&
          !item.legal_validate_result.valid).length > 0
      }
    },

    advisorInBlacklist(data) {
      return data.advisor.status === 'BLACKLIST'
    },

    advisorDeclined(data) {
      return data.advisor_decline?.is_valid
    },

    advisorNoEmail(data) {
      return data.advisor.contact_infos.filter(
        item => item.type === 'EMAIL').length < 1
    },

    governmentEmail(data) {
      const gov_email_reg = /^(gov|go|mil)$/g
      const email_list = data.advisor.contact_infos.filter(
        item => item.type === 'EMAIL' && item.value)
      if (email_list.length) {
        return email_list.filter(item => {
          const suffix_list = item.value.split('@')[1].split('.')
          return suffix_list.find(i => gov_email_reg.test(i))
        }).length > 0
      } else {
        return false
      }
    },

    checkSuspectedCompany(advisor) {
      const jobs = advisor.jobs
      const result = {
        in_suspected_blacklist: false,
        company_names: [],
        warning_text: null,
      }
      let company_names = []
      if (jobs && jobs.length > 0) {
        jobs.forEach(item => {
          if (item.legal_validate_result &&
            item.legal_validate_result.warn_companies.length > 0) {
            company_names = company_names.concat(
              item.legal_validate_result.warn_companies.map(
                company => company.alias))
          }
        })
      }
      if (company_names.length > 0) {
        const cause = `${advisor.name_prefix || ''} ${advisor.full_name} => ${company_names.join()}`
        result.in_suspected_blacklist = true
        result.company_names = company_names
        result.warning_text = cause
      } else {
        result.in_suspected_blacklist = false
      }
      return result
    },

    async checkBeforeOpen() {
      const check_list = []
      let check_company_list = []
      const advisor_list = this.tasks.map(item => item.advisor)

      advisor_list.forEach(item => {
        const check_result = this.checkSuspectedCompany(item)
        if (check_result.in_suspected_blacklist) {
          check_list.push(check_result.warning_text)
          check_company_list = check_company_list.concat(
            check_result.company_names)
        }
      })

      if (check_list.length > 0) {
        const company_names = _.uniq(check_company_list)

        let text = `WARNING! The following companies are on Capvision’s DNC List: ${company_names.join(
          ', ')}. <br>` +
          'Please confirm these experts is not employed at this company or a subsidiary of this company before contacting.<br/><br/>' +
          check_list.join('<br/>')
        text += '<br/><br/> If there are any questions, please contact Capvision Compliance.'
        return this.$confirm(text, 'Warning', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          customClass: 'width-auto',
          type: 'warning',
          cancelButtonClass: 'el-button--text',
          dangerouslyUseHTMLString: true,
        }).then(() => {
          this.dialogVisible = true
        }, () => {
          this.dialogVisible = false
        })
          .finally(() => {
            this.dialog_loading = false
          })
      } else {
        this.dialog_loading = false
        this.dialogVisible = true
      }
    },

    routeToConsultantDetail(advisor_id) {
      const route = this.$router.resolve({
        name: 'ConsultantDetail',
        params: { advisor_id },
      })
      window.open(route.href, '_blank')
    },
    async checkSqExisted() {
      const data = await ProjectAPI.getProjectInquiryBranches(this.project.id)
      this.options.sq = data.sq ? data.sq.branches : []
      this.existSQ = data.sq && data.sq.branches && data.sq.branches.length > 0
    },
    handleMailContent(data) {
      return '<div style="font-size: 11pt; font-family: Calibri;">' + data +
        '</div>'
    },
    checkClientRuleInvestmentTarget() {
      const rule_set = this.project.client.compliance_preference
      if (!rule_set) return false
      const rule = this.project.client.compliance_preference.rule
      if (rule_set && rule && rule.compliance_rules &&
        rule.compliance_rules['expert_restrictions']) {
        const investment_target = rule.compliance_rules['expert_restrictions']['investment_target']
        return investment_target['master_switch'] &&
          investment_target['is_user_must_give_target']
      } else {
        return false
      }
    },

    checkClientRuleShowInvestmentTargetToAdvisor() {
      const rule_set = this.project.client.compliance_preference
      if (!rule_set) return false
      const rule = this.project.client.compliance_preference.rule
      if (rule_set && rule && rule.compliance_rules &&
        rule.compliance_rules['expert_restrictions']) {
        const investment_target = rule.compliance_rules['expert_restrictions']['investment_target']
        return investment_target['master_switch'] &&
          investment_target['show_investment_target_to_expert']
      } else {
        return false
      }
    },

    getCaPDFAttachments(email) {
      const advisor_name_for_show = this.needToBlindAdvisorName ? 'Advisor' : this.passTasks[0].advisor.full_name
      const file_name = `Capvision – ${advisor_name_for_show} Client Agreement.pdf`

      this.$refs['ca-pdf'].preCallCA(true, file_name)
        .then(res => {
          email.attachments.push({
            name: res.name,
            file_path: res.url,
          })
        }, () => {
          this.$message.error('Failed to upload CA PDF.')
        })
    },

    getSystemLeadEmailVendor() {
      return ComplianceAPI.getGlobalSetting({ type: 'SYSTEM' })
        .then(data => {
          this.lead_email_vendor = {
            normal: data.find(item => item.key === 'LEADS_EMAIL_VENDOR')?.value,
            allow_outreach: data.find(item => item.key === 'ALLOW_OUTREACH_USER_LEADS_EMAIL_VENDOR')?.value,
          }
        })
    },

    preferVendor(advisor_type) {
      if (this.isSGDB) return null

      if (advisor_type === 'LEAD') {
        const system_setting_vendor = this.allowOutreach ? this.lead_email_vendor.allow_outreach : this.lead_email_vendor.normal
        return calcWay(system_setting_vendor)
      }
      return 'SEND_GRID'

      // RD Config vendor_options选择 POSTMARK_RESEND_RANDOM/POSTMARK_MAILGUN_RANDOM/POSTMARK_MAILJET_RANDOM时,由前端随机选择
      function calcWay (value) {
        if (value === 'POSTMARK_RESEND_RANDOM') {
          return Math.random() < 0.5 ? 'POSTMARK' : 'RESEND'
        }
        if (value === 'POSTMARK_MAILGUN_RANDOM') {
          return Math.random() < 0.5 ? 'POSTMARK' : 'MAILGUN'
        }
        if (value === 'POSTMARK_MAILJET_RANDOM') {
          return Math.random() < 0.5 ? 'POSTMARK' : 'MAILJET'
        }
        return value
      }
    },
  },
}
