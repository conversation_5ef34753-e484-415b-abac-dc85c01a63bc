<template>
  <div v-if="showSendOnBehalf">
    <el-checkbox
      :value="checked"
      label="Send On Behalf"
      border
      style="vertical-align: bottom;"
      @change="changeIsSendOnBehalf" />
    <el-select
      v-show="checked"
      :value="user"
      value-key="email"
      filterable
      class="remote-select w-200px"
      @change="changeUser">
      <el-option v-for="item in options.sendOnBehalf" :key="item.user.email" :label="item.user.name" :value="item.user" />
    </el-select>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'SendOnBehalf',
  props: {
    checked: <PERSON><PERSON><PERSON>,
    user: Object,
    members: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      options: {
        sendOnBehalf: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'info',
      'roles',
    ]),
    showSendOnBehalf() {
      return this.roles.some(item => ['Admin', 'AllowSendOnBehalf'].includes(item.role?.name))
    },
  },
  mounted() {
    const arr = [this.info.email]
    this.options.sendOnBehalf = this.members.filter(item => {
      if (!arr.includes(item.user.email)) {
        arr.push(item.user.email)
        return true
      }
    })
  },
  methods: {
    changeIsSendOnBehalf(val) {
      this.$emit('update:checked', val)
      this.$emit('update:user', val ? this.options.sendOnBehalf[0].user : null)
      this.$emit('change')
    },
    changeUser(val) {
      this.$emit('update:user', val)
      this.$emit('change')
    },
  },
}
</script>

<style scoped>

</style>
