<template>
<div class="inline-block ml-3">
  <el-button type="text" @click="openDialog">Preview</el-button>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    append-to-body
    :show-close="false"
    width="70%"
  >
    <div slot="title" class="dialog-title">
      <h4 class="title"><b>Batch editing of templates</b></h4>
      <div class="info fs-14">Please ensure the integrity and correctness of the placeholders.</div>
    </div>
    <tinymce
      ref="tinymce_content"
      v-model="edit_content"
      inline
      is-email />
    <span slot="footer" class="dialog-footer">
         <el-button
           type="info"
           @click="closeDialog">Cancel</el-button>
        <el-button
          type="primary"
          @click="applyNewContent">Save</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import _ from 'lodash'
export default {
  name: 'TemplateBatchEdit',
  components: { Tinym<PERSON> },
  props: {
    emailTemplate: Object,
  },
  data() {
    return {
      dialogVisible: false,
      edit_content: '',
    }
  },
  methods: {
    openDialog() {
      this.edit_content = this.emailTemplate.content_template
      this.$nextTick(() => {
        this.$refs.tinymce_content.setContent(this.edit_content)
      })
      this.dialogVisible = true
    },
    closeDialog() {
      this.edit_content = ''
      this.dialogVisible = false
    },
    applyNewContent() {
      const result = _.cloneDeep(this.emailTemplate)
      result.content_template = this.edit_content
      this.$emit('update', result)
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped lang="scss">
.dialog-title {
  font-size: 18px;
  padding-bottom: 12px;
  border-bottom: 1px solid #E4E7ED;

  .title{
    margin-bottom: 10px;
    margin-top: 10px;
  }
}
</style>
