<template>
  <div class="inline-block">
    <el-tooltip
      :disabled="!buttonDisabled"
      :content="disabled_reason"
      placement="top">
      <div>
        <el-button
          type="primary"
          :loading="sendLoading"
          :disabled="buttonDisabled"
          @click="beforeOpen">
          <svg-icon icon-class="email" />
          To Client
        </el-button>
      </div>
    </el-tooltip>

    <el-dialog
      title="Email To Client"
      :visible.sync="dialogVisible"
      width="1000px"
      append-to-body
      @open="openDialog"
      @closed="resetData">

      <div v-loading="loading">
        <recommend-warning :client-id="project.client_id" :type="'recommend'" />

        <!--  Alert - Filtered -->
        <div v-if="filterTasks.length">
          <el-alert
            v-if="filter_task_no_tc.length"
            title="These tasks will be filtered, because these advisors did not sign the TC:"
            type="warning"
            show-icon
            class="mb-8">
            <a
              v-for="(item, index) in filter_task_no_tc"
              :key="item.id"
              class="link"
              @click="routeToConsultantDetail(item.advisor_id)">
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.full_name }}
              {{
                repeatedTasksID.includes(item.advisor_id) ?
                  `#${item.display_id}` : ''
              }}
              <slot v-if="index+1 !== filter_task_no_tc.length">,</slot>
            </a>
          </el-alert>
          <el-alert
            v-if="filter_task_no_npi.length"
            title="These tasks will be filtered, because their NPI number is not yet validated or it is null:"
            type="warning"
            show-icon
            class="mb-8"
          >
            <a
              v-for="(item, index) in filter_task_no_npi"
              :key="item.id"
              class="link"
              @click="routeToConsultantDetail(item.advisor_id)"
            >
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.full_name }}
              {{
                repeatedTasksID.includes(item.advisor_id) ?
                  `#${item.display_id}` : ''
              }}
              <slot v-if="index+1 !== filter_task_no_npi.length">,</slot>
            </a>
          </el-alert>
          <el-alert
            v-if="filter_task_completed.length"
            title="These tasks will be filtered, because these tasks are already completed:"
            type="warning"
            show-icon
            class="mb-8">
            <a
              v-for="(item, index) in filter_task_completed"
              :key="item.id"
              class="link"
              @click="routeToConsultantDetail(item.advisor_id)">
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.full_name }}
              {{
                repeatedTasksID.includes(item.advisor_id) ?
                  `#${item.display_id}` : ''
              }}
              <slot v-if="index+1 !== filter_task_completed.length">,</slot>
            </a>
          </el-alert>
          <el-alert
            v-if="filter_advisor_no_12months_of_employment.length"
            title="These tasks will be filtered, because these experts do not have at least 12 months of work experience:"
            type="warning"
            show-icon
            class="mb-8">
            <a
              v-for="(item, index) in filter_advisor_no_12months_of_employment"
              :key="item.id"
              class="link"
              @click="routeToConsultantDetail(item.advisor_id)">
              {{ item.advisor.name_prefix || ''}}
              {{ item.advisor.full_name }}
              {{
                repeatedTasksID.includes(item.advisor_id) ?
                  `#${item.display_id}` : ''
              }}
              <slot v-if="index+1 !== filter_advisor_no_12months_of_employment.length">,</slot>
            </a>
          </el-alert>
        </div>

        <!-- Alert - pass -->
        <el-alert
          v-if="passTasks.length"
          title="These Advisors will be Shared:"
          type="success"
          show-icon
          class="mb-8">
          <a
            v-for="(item, index) in passTasks"
            :key="item.id"
            class="link"
            @click="routeToConsultantDetail(item.advisor_id)">
            {{ item.advisor.name_prefix || ''}}
            {{ item.advisor.full_name }}
            {{
              repeatedTasksID.includes(item.advisor_id) ?
                `#${item.display_id}` : ''
            }}
            <slot v-if="index+1 !== passTasks.length">,</slot>
          </a>
        </el-alert>

        <!-- Additional Charges -->
        <el-alert
          v-if="!client_has_mean_contract"
          title="The client does not have a valid (non-project base) contract or a contract that is on probation."
          type="info" />
        <div class="flex align-items-center justify-content-between" style="margin-top: 16px; margin-bottom: 16px;">
          <div>
            <slot v-if="client_has_mean_contract" class="inline-block">
              <span style="font-size: 12px; font-weight: bold; margin-right: 1em;">Additional Charges:</span>
              <el-checkbox-group v-model="client_additional_charges" class="inline-block" @change="getEmailContent">
                <el-checkbox label="has_in_person">In Person</el-checkbox>
                <el-checkbox label="has_translation">Translator</el-checkbox>
                <el-checkbox label="has_transcription">Transcription</el-checkbox>
              </el-checkbox-group>
            </slot>
            <time-zone-select v-model="selectTimeZone" class="ml-8 inline-block" @change="getEmailContent" />
          </div>

          <div>
            <span style="font-size: 12px; font-weight: bold; margin-right: 1em;">Format</span>
            <el-select
              v-model="client_profile_format"
              @change="changeEmailProfileFormatType"
            >
              <slot v-for="item in client_options.to_client_default_profiles_format">
                <el-option
                  v-if="clientRequireDisplayNominalCharge ? true : !item.need_nominal_charge"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </slot>
            </el-select>
          </div>
        </div>

        <!-- profile-format-lite -->
        <div class="display-none">
          <profile-format-lite
            v-for="(item, index) in passTasks"
            :key="index"
            ref="profile-format-lite"
            :data="item"
            :client-profile-format="client_profile_format" />
        </div>

        <!-- profile-format -->
        <profile-format
          v-for="item in passTasks"
          :key="item.id"
          ref="profile-format"
          :data="item"
          :senior.sync="item.senior_rate"
          :custom-time-zone="selectTimeZone"
          :additional-charges="client_additional_charges"
          :client-profile-format="client_profile_format"
          class="display-none" />

        <!-- Email -->
        <email-editor
          ref="email-editor"
          :options="options.emailTemplates"
          no-options
          no-recipients
          :template="template"
          :email="email"
          :content.sync="email_content"
          @change="changeEmailTemplate" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-document-copy"
          :loading="sendLoading"
          @click="copyEmail">Copy</el-button>
        <el-button type="default" @click="dialogVisible = false">Close</el-button>

        <!-- 弃用 Send Mail 流程 -->
        <el-popover
          v-if="false"
          v-model="popoverConfirm.show"
          append-to-body
          trigger="click"
          placement="top"
          width="223"
          popper-class="popover-confirm"
        >
          <div class="title">Confirm Send to Client</div>
          <div class="mb-8">
            <el-button
              type="text"
              size="mini"
              @click="popoverConfirm.show=false">
              Cancel
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :loading="sendLoading"
              :disabled="!passTasks.length"
              @click="actionSubmit()">
              Confirm
            </el-button>
          </div>
          <div class="title">Copy Hyperlink & Send Advisors</div>
          <div class="mb-8">
            <el-button
              type="primary"
              size="mini"
              :disabled="sendLoading || spendFlag !== 'hasSpend'"
              @click="copyEmail">
              Copy
            </el-button>
          </div>
          <el-button
            slot="reference"
            type="primary"
            size="mini"
            :disabled="!passTasks.length"
            :loading="sendLoading">Send</el-button>
        </el-popover>

      </span>
    </el-dialog>
  </div>

</template>

<script>
import { copyUtil, getLocationById } from '@/utils/tool'
import ProjectAPI from '@/api/project.js'
import EmailAPI from '@/api/email'
import Mixin from './mixin_v2'
import TaskActionsMixin from '@/views/project/consultation/detail/components/taskActionsMixin'
import recommendWarning from '@/components/ComplianceWarning/Index'
import EmailEditor from '@/components/EmailEditor'
import ProfileFormat from '@/components/FormatProfile/ProfileIndex'
import ProfileFormatLite from '@/components/FormatProfile/ProfileLite'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
import ComplianceAPI from '@/api/compliance'
import Filters from '@/utils/filters'
import { mapGetters, mapState } from 'vuex'
import moment from 'moment-timezone'

export default {
  name: 'EmailToClient',
  components: { ProfileFormatLite, recommendWarning, EmailEditor, ProfileFormat, TimeZoneSelect },
  mixins: [Mixin, TaskActionsMixin],
  data() {
    return {
      options: {
        emailTemplates: [],
      },
      template: null,
      email: {
        subject: '',
        content: '',
      },
      origin_email: {
        content: '',
      },
      spendFlag: 'spend',
      email_content: '',
      popoverConfirm: {
        show: false,
        spend: {
          title: 'Copy Hyperlink & Send Advisors',
        },
        hasSpend: {
          title: 'Confirm Send to Client',
        },
      },
      buttonDisabled: false,
      disabled_reason: null,
      client_has_mean_contract: false,
      client_additional_charges: [],
      client_profile_format: '',
      filter_task_no_tc: [],
      filter_task_no_npi: [],
      filter_task_completed: [],
      filter_advisor_no_12months_of_employment: [],
      selectTimeZone: this.project.zone_id_string,
      allowed_to_send_before_TC_signed: {
        client_types: [],
        expert_geography_restriction: [],
        client_types_geography_restriction: [],
      },
      has_geographical_limitations_advisor: [],
      geographical_limitations: null,
      client_portal_link_url: '',
      task_action_type: 'To Client',
      experts_usage: [],
    }
  },
  computed: {
    ...mapState({
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
    }),
    ...mapGetters([
      'client_options',
    ]),

    isGenerationClient() {
      return [this.GenerationIMId, this.GenerationGrowthEquityId].includes(this.project.client_id)
    },

    needBlindAdvisorName() {
      if (!this.project?.client) return false
      /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
      const client_status_is_not_EXECUTE = this.project.client.status !== 'EXECUTE'
      const client_compliance_status_no_pass = this.project.client.compliance_status !== 'APPROVED'
      const client_blind_expert_name = this.project.client.preference?.blind_expert_names_in_to_client_and_portal
      const project_blind_expert_name = this.project.blind_expert_profiles
      return client_status_is_not_EXECUTE || client_compliance_status_no_pass || client_blind_expert_name || project_blind_expert_name
    },

    clientRequireDisplayNominalCharge() {
      return this.project?.client?.preference?.display_nominal_charge_rate_on_portal
    },

  },
  watch: {
    'tasks': {
      handler(val) {
        this.checkIfDisabled(this.project.client, this.tasks)
      },
    },
  },
  mounted() {
    this.getComplianceSettingWithSignTC().then(() => {
      this.checkIfDisabled(this.project.client, this.tasks)
    })
  },
  methods: {
    // 调接口获取所需的更多信息
    beforeOpen() {
      this.sendLoading = true
      this.getTasksByIDS()
        .then(() => {
          this.dialogVisible = true
        })
        .finally(() => {
          this.sendLoading = false
        })
    },
    async  openDialog() {
      if (!this.project.project_client_contacts.length) {
        return this.$message({
          message: 'The project has no client contact.',
          type: 'error',
        })
      }

      this.client_profile_format = this.project.client.preference?.to_client_default_profiles_format || 'FULL_PROFILES'

      this.checkRepeatedTasks()
      this.loading = true
      if (this.project.client.preference?.display_usage_in_to_client) await this.getAdvisorUsage()
      this.getEmailTemplateList()
        .then(() => {
          this.filterTaskList()
          this.getEmailContent()
        })
        .finally(() => {
          this.loading = false
        })
    },
    resetData() {
      this.filterTasks = []
      this.filter_task_completed = []
      this.filter_advisor_no_12months_of_employment = []
      this.filter_task_no_tc = []
      this.filter_task_no_npi = []
      this.passTasks = []
      this.spendFlag = 'spend'
    },

    /**
     * Recommend Email 过滤 Task
     */
    filterTaskList() {
      this.tasks.forEach(item => {
        /* 过滤: Task 进行 COMPLETED 步骤之前可发送给客户 */
        const isBeforeCompletedStep = !['COMPLETED'].includes(item['general_status'])

        /* 过滤: 签了 TC 才可发送 Sending Advisors: Can send BEFORE CA is completed, but after TC is accepted */
        /* outlook: "To Client" Restriction(2025-01-22) 改为签了TC（不区分类型）就通过TC验证*/
        const hasSignedTC = item.advisor.tc_term_valid.CONSULTATION || item.advisor.tc_term_valid.SURVEY

        /* 过滤: 某些类型的客户，接受没签TC的专家 */
        const specialClient = this.allowed_to_send_before_TC_signed.client_types.includes(this.project.client.type)

        /* 过滤: 如果是import进来的专家 就说明已在outsource DB中签署过TC */
        const isOutsourceImported = item.task_outsource_status === 'IMPORTED'

        /* 过滤: compliance 有地域限制的客户类型  没签TC的专家 */
        const experts_within_geographical_limits = !hasSignedTC && this.has_geographical_limitations_advisor.length >
          0 &&
          this.has_geographical_limitations_advisor.filter(limit => limit.advisor_id === item.advisor_id).length > 0

        /* 过滤: 专家工作经验不足一年 */
        const is_advisor_has_12months_of_employment = has12MonthsOfEmployment(item)

        /* 过滤: Task的Angle为Physician Angle，且NPI未通过验证或为空的专家 */
        const noValidNpi = item.angle.tags?.includes('PHYSICIAN') && item.advisor.npi_status !== 'VALID'

        const isPass = isBeforeCompletedStep && (hasSignedTC || specialClient || isOutsourceImported) &&
          !experts_within_geographical_limits && is_advisor_has_12months_of_employment && !noValidNpi

        if (isPass) {
          Object.assign(item.advisor, this.experts_usage[item.advisor_id])
          this.passTasks.push(item)
        } else {
          if (!hasSignedTC || experts_within_geographical_limits) this.filter_task_no_tc.push(item)
          if (noValidNpi) this.filter_task_no_npi.push(item)
          if (!isBeforeCompletedStep) this.filter_task_completed.push(item)
          if (!is_advisor_has_12months_of_employment) this.filter_advisor_no_12months_of_employment.push(item)
          this.filterTasks.push(item)
        }
      })

      /**
       * 至少工作 1 年
       * 需求: Can we require at least 12-months of employment for advisors for TC to be sent to them?
       * @returns {boolean}
       */
      function has12MonthsOfEmployment(task) {
        return task.advisor.jobs.some(item => {
          /* 开始日期距今 > 1y */
          return !!item.start_date && moment(item.start_date).add(1, 'year').isBefore()
        })
      }
    },
    async getAdvisorUsage() {
      const params = {
        advisor_ids: this.tasks.map(item => item.advisor_id).join(),
        client_id: this.project.client_id,
      }
      const data = await ProjectAPI.getExpertUsage(params)

      this.experts_usage = data
    },

    async getEmailTemplateList() {
      const params = {
        has_permission: true,
        content_type: 'ADVISOR_RECOMMENDATION',
        reference_ids: [0, this.project.client_id].join(),
        reference_type: 'CLIENT',
        sort: 'reference_id desc',
        include_default_reference_type: true,

      }
      const data = await EmailAPI.getEmailList(params)
      // data['list'].sort((a, b) => a.rank - b.rank)
      this.options.emailTemplates = data['list']
      this.template = data['list'][0]
    },

    async getEmailContent() {
      const contact = this.project.project_client_contacts.find(item => item.client_contact_type === 'PRIMARY')
      const params = {
        client_id: this.project.client_id,
        contact_id: contact ? contact.client_contact_id : null,
        user_id: null,
        project_id: this.project.id,
        task_id: null,
        advisor_id: null,
      }
      this.loading = true
      if (this.project.client.enable_client_portal) {
        const params_data = {
          context_ids: params,
          placeholders: ['ADVISOR_RECOMMEND_CLIENT_PORTAL_PAGE_URL'],
        }
        await EmailAPI.getEmailContentPlaceholder(params_data)
          .then(data => {
            this.client_portal_link_url = data['ADVISOR_RECOMMEND_CLIENT_PORTAL_PAGE_URL'] || ''
          })
      }
      this.email = await EmailAPI.getEmailByTemplate(this.template.id, params)
      this.origin_email = { ...this.email }
      this.setEmailContent(this.email)
      this.loading = false
    },

    setEmailContent(email) {
      let content = this.handleMailContent(email.content)
      content = this.replaceAdvisorBiosLite(content, this.passTasks)
      content = this.replaceAdvisorBios(content, this.passTasks)

      if (this.client_profile_format === 'SHORT_FORM_PROFILES') {
        content = content.replace(/<div style="font-size: 12pt;"><br \/><\/div>/, '')
      }

      this.email.content = content
      this.$refs['email-editor'].setContent()
    },

    changeEmailProfileFormatType() {
      this.$nextTick(() => {
        this.setEmailContent(this.origin_email)
      })
    },

    replaceAdvisorBiosLite(email, passTasks) {
      const refs = this.$refs['profile-format-lite'] || []
      const html = []
      let angle_id

      for (let i = 0; i < refs.length; i++) {
        let consultant_lite_profile_HTML = refs[i].$el.outerHTML
        const task = passTasks[i]
        const angle = task.angle

        /* 添加标题 angle.title */
        if (angle && angle.id !== angle_id) {
          angle_id = angle.id
          const average_cpi = ['SUMMARY_CHARGE_AND_AVERAGE_CPI'].includes(this.client_profile_format)
            ? getAngleAverageCPI(angle_id, this.project.client)
            : ''
          const angle_title_HTML = !this.isGenerationClient
            ? `<p style="font-size: 12pt; text-decoration: underline; margin-top: 4px; margin-bottom: 4px;">${angle.name}${average_cpi}</p>`
            : ''
          consultant_lite_profile_HTML = `${angle_title_HTML}${consultant_lite_profile_HTML}`
        }

        /* 给专家名称部分添加client portal链接*/
        if (this.project.client.enable_client_portal && this.client_portal_link_url) {
          const replace_element = refs[i].$el.querySelector('.advisor-display-name-lite').innerHTML
          const target_element = `<a href="${this.client_portal_link_url}?display=${passTasks[i].display_id}" >${replace_element}</a>`
          consultant_lite_profile_HTML = consultant_lite_profile_HTML.replace(replace_element, target_element)
        }

        /* 若 Client 状态不是 Execute || 法务审核没通过 || 客户设置不展示专家名字 ||项目级别不展示名字 =》 则隐藏专家名*/
        if (this.needBlindAdvisorName) {
          consultant_lite_profile_HTML = consultant_lite_profile_HTML.replace(passTasks[i].advisor.full_name, 'Advisor')
        }

        html.push(consultant_lite_profile_HTML)
      }
      return email.replace('{{TASK_ADVISOR_BIOS_LITE}}', html.join(''))

      function getAngleAverageCPI(target_angle_id, client) {
        const clientContract = client.contracts.filter(
          item => item.effective_status === 'EFFECTIVE' &&
            ['PREPAY', 'PAYGO', 'TRIAL'].includes(item.payway))
        if (!clientContract.length || !client.show_nominal_client_charges) return ''

        const obj = {}
        passTasks.forEach(task => {
          const angle_id = task.angle.id
          const client_rate = task.client_rate_usd ||
            task.specified_client_rate_currency_after_multiple ||
            task.default_client_rate_usd ||
            task.default_client_rate_usd_considering_unsigned_tc
          if (!obj[angle_id]) {
            obj[angle_id] = {
              tasks: [task],
              total_cpi: client_rate,
              average_cpi: client_rate,
            }
          } else {
            obj[angle_id].tasks.push(task)
            obj[angle_id].total_cpi += client_rate
            obj[angle_id].average_cpi = Math.round(obj[angle_id].total_cpi / obj[angle_id].tasks.length * 100) / 100
          }
        })
        return ` (Average CPI – $${obj[target_angle_id].average_cpi})`
      }
    },

    replaceAdvisorBios(email, passTasks) {
      if (['SUMMARY_ONLY', 'SUMMARY_CHARGE', 'SUMMARY_CHARGE_AND_AVERAGE_CPI'].includes(this.client_profile_format)) {
        return email.replace('{TASK_ADVISOR_BIOS}', '')
      }

      const refs = this.$refs['profile-format'] || []
      const html = []
      let angle_id

      for (let i = 0; i < refs.length; i++) {
        let consultant_profile_HTML = refs[i].$el.innerHTML
        const task = passTasks[i]
        const angle = task.angle

        /* 添加标题 angle.title */
        if (angle && angle.id !== angle_id) {
          angle_id = angle.id
          const angle_title_HTML = !this.isGenerationClient
            ? `<p style="font-size: 14pt; font-weight: bold; text-decoration: underline; margin-top: 4px; margin-bottom: 4px;">${angle.name}</p>`
            : ''
          consultant_profile_HTML = `${angle_title_HTML}${consultant_profile_HTML}`
        }

        /* 给专家名称部分添加client portal链接*/
        if (this.project.client.enable_client_portal && this.client_portal_link_url) {
          const replace_element = refs[i].$el.querySelector('.advisor-display-name').innerHTML
          const target_element = `<a href="${this.client_portal_link_url}?display=${passTasks[i].display_id}" >${replace_element}</a>`
          consultant_profile_HTML = consultant_profile_HTML.replace(replace_element, target_element)
        }

        /* 若 Client 状态不是 Execute，或 法务审核没通过，则隐藏专家名 ,或客户设置不展示专家名字*/
        if (this.needBlindAdvisorName) {
          consultant_profile_HTML = consultant_profile_HTML.replace(passTasks[i].advisor.full_name, 'Advisor')
        }

        html.push(consultant_profile_HTML)
      }
      let result_html = html.join('<br>')
      if (this.isGenerationClient) {
        const project_code_html = `<br><p
            style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:2pt;margin-left:0;">
            <b>Project Code: ${this.project.code}</b>
          </p>`
        result_html += project_code_html
      }
      return email.replace('{TASK_ADVISOR_BIOS}', result_html)
    },
    changeEmailTemplate(val) {
      this.template = val
      this.resetData()
      this.filterTaskList()
      this.getEmailContent()
    },
    actionSubmit() {
      const params = {
        portal: {
          type: 'CONTACT_PRE_CALL',
          project_id: this.project.id,
          task_id_set: this.passTasks.map(item => item.id),
        },
        email: this.email,
        email_template_tags: this.template.tags,
      }
      this.sendLoading = true
      return ProjectAPI.mailToClient(params)
        .then(tasks => {
          this.$message({
            type: 'success',
            message: 'Success to send emails!',
          })
          this.updateTaskStatus(tasks)
          this.spendFlag = 'hasSpend'
          // this.dialogVisible = false
        })
        .finally(() => {
          this.sendLoading = false
        })
    },
    updateTaskStatus(tasks) {
      this.passTasks.forEach((item, index) => {
        for (const key in item) {
          if (tasks[index][key]) item[key] = tasks[index][key]
        }
      })
      const data = this.tasks.map(task => {
        const obj = this.passTasks.find(item => item.id === task.id)
        if (obj) task = obj
        return task
      })
      this.$emit('update:tasks', data)
      this.$emit('update')
    },

    updateTaskCompleteData() {
      const update_tasks = this.passTasks.map(item => {
        item.has_transcription = false
        item.has_translation = false
        item.has_in_person = false
        this.client_additional_charges.forEach(add => {
          item[add] = true
        })
        const { project_id, id, has_transcription, has_translation, has_in_person } = item
        return { project_id, id, has_transcription, has_translation, has_in_person }
      })

      update_tasks.forEach(item => {
        ProjectAPI.updateTask(item)
      })
    },
    async copyEmail() {
      try {
        /* 更改 Task 状态为 "Send to client" */
        const params = {
          portal: {
            type: 'CONTACT_PRE_CALL',
            project_id: this.project.id,
            task_id_set: this.passTasks.map(item => item.id),
          },
          email: this.email,
          email_template_tags: this.template.tags,
        }
        this.sendLoading = true
        const data = await ProjectAPI.copyMailToClient(params)
        await copyUtil(data.content, true)
        this.updateTaskCompleteData()
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } catch (e) {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      } finally {
        this.sendLoading = false
      }
    },

    /**
     * TaskLeads 页禁止向非 "Consulting firm", "Corporate" Client 发送专家 Bio
     * Advisor bios are ok to send to "Consulting firm" and "Corporate" clients only.
     * @param client
     * @param tasks
     */
    checkIfDisabled(client, tasks) {
      /* 1. 判断当前专家是否都没有有效TC */
      /* outlook: "To Client" Restriction(2025-01-22) 改为签了TC（不区分类型）就通过TC验证*/
      const all_experts_not_signed_tc = tasks.filter(item => (!item.advisor.tc_term_valid.CONSULTATION && !item.advisor.tc_term_valid.SURVEY)).length === tasks.length

      /* 2. 某些client type类型 允许发送 没签TC */
      const isClientProhibit = !this.allowed_to_send_before_TC_signed.client_types.includes(client.type)
      const allowed_client_types = Filters.getLabels(this.allowed_to_send_before_TC_signed.client_types,
        this.client_options.type)

      /* 3.compliance 有地域限制的客户类型 */
      const isClientHasGeographicalLimitations = this.allowed_to_send_before_TC_signed.client_types_geography_restriction.includes(
        client.type)
      if (isClientHasGeographicalLimitations) {
        this.has_geographical_limitations_advisor = tasks.filter(item => {
          const advisor_location_ids = item.advisor.location_id_path?.split(' ') || []
          return advisor_location_ids.filter(
            location_id => this.allowed_to_send_before_TC_signed.expert_geography_restriction.includes(
              location_id)).length > 0
        })
      }

      /* 4. 如果是import进来的专家 就说明已在outsource DB中签署过TC */
      const isOutsourceImported = tasks.some(item => item.task_outsource_status === 'IMPORTED')

      const experts_within_geographical_limits = this.has_geographical_limitations_advisor.length > 0

      this.buttonDisabled = all_experts_not_signed_tc && (isClientProhibit || experts_within_geographical_limits) && !isOutsourceImported

      this.client_has_mean_contract = client.contracts.filter(
        item => item.effective_status === 'EFFECTIVE' && ['PREPAY', 'PAYGO', 'TRIAL'].includes(item.payway)).length

      /* 处理disabled reason*/
      if (!this.buttonDisabled) return
      if (isClientProhibit) {
        this.disabled_reason = 'Non-signatory TC advisor bios can only be sent to the following types of clients:' +
          allowed_client_types
      } else {
        const expert_names = this.has_geographical_limitations_advisor.map(item => {
          return `${item.advisor.name_prefix || ''} ${item.advisor.full_name}`
        }).join(', ')
        this.disabled_reason = 'These non-signatory TC experts cannot be sent bios because the client has regional (' +
          this.geographical_limitations + ') restrictions: ' +
          expert_names
      }
    },
    getLocationNames() {
      getLocationById(this.allowed_to_send_before_TC_signed.expert_geography_restriction.map(Number))
        .then(data => {
          this.geographical_limitations = data.map(item => item.name).join()
        })
      return this.geographical_limitations
    },

    async getComplianceSettingWithSignTC() {
      if (this.allowed_to_send_before_TC_signed.client_types.length < 1) {
        await ComplianceAPI.getAllowedToSendBeforeTCSigned('LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ALLOW_CLIENT_TYPES')
          .then(data => {
            this.allowed_to_send_before_TC_signed.client_types = data.value.split(',')
          })
      }
      if (this.allowed_to_send_before_TC_signed.expert_geography_restriction.length < 1) {
        await ComplianceAPI.getAllowedToSendBeforeTCSigned('LEGAL_TO_CLIENT_BEFORE_SIGN_TC_BLOCK_ADVISOR_LOCATIONS')
          .then(data => {
            this.allowed_to_send_before_TC_signed.expert_geography_restriction = data.value.split(',')
            this.getLocationNames()
          })
      }
      if (this.allowed_to_send_before_TC_signed.client_types_geography_restriction.length < 1) {
        await ComplianceAPI.getAllowedToSendBeforeTCSigned(
          'LEGAL_TO_CLIENT_BEFORE_SIGN_TC_ADVISOR_LOCATION_RESTRICTED_CLIENT_TYPES')
          .then(data => {
            this.allowed_to_send_before_TC_signed.client_types_geography_restriction = data.value.split(',')
          })
      }
    },

  },
}
</script>

<style lang="scss">
.popover-confirm {
  .title {
    font-weight: bold;
    font-size: 0.9em;
    margin-bottom: 5px;
  }
}

.pt-5 {
  padding-top: 5px;
}
</style>
