<template>
  <div class="inline-block">
    <el-tooltip
      v-if="project.bcg_hub_project"
      effect="dark"
      content="No current BCG approved template"
      :disabled="!!approvedBcgTemplate"
      placement="top"
    >
      <el-button
v-loading.fullscreen.lock="dialog_loading" element-loading-background="rgba(0, 0, 0, 0.5)" type="primary"
                 :disabled="!approvedBcgTemplate" @click="handleBcgTemplate">
        <svg-icon icon-class="email" />
        Outreach All
      </el-button>
    </el-tooltip>
    <el-dropdown v-else trigger="click" placement="bottom-start" @command="handleCommand">
      <el-button v-loading.fullscreen.lock="dialog_loading" element-loading-background="rgba(0, 0, 0, 0.5)" type="primary" @click="handleDropdown">
        <svg-icon icon-class="email" />
        Outreach All<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-if="templateLoading" disabled>
          <i class="el-icon-loading" />Loading...
        </el-dropdown-item>
        <el-dropdown-item v-if="!templateLoading && !templateList.length" disabled>No Data</el-dropdown-item>
        <el-dropdown-item v-for="item in templateList" :key="item.id" :command="item">{{ item.name }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-dialog
      :title="selectedTemplate ? selectedTemplate.name : ''"
      :visible.sync="dialogVisible"
      append-to-body
      width="60%"
      @closed="resetData()">
      <div>
        <div class="el-alert el-alert--success is-light mb-8">
          <i class="el-alert__icon el-icon-success is-big mr-8" />
          {{ check_list.valid_task_count }} tasks will be sent.
        </div>
        <div v-if="check_list.total !== check_list.valid_task_count" class="el-alert el-alert--warning is-light mb-8">
          <i class="el-alert__icon el-icon-warning is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold">These tasks will be filtered ({{
              check_list.total - check_list.valid_task_count
            }}):</span>
            <div v-for="tasks in reject_task_list" :key="tasks.key" class="mb-normal">
              <p v-if="tasks.total>0" class="el-alert__description">
                <b>{{ tasks.tip }} ({{ tasks.total }})</b>
                <span v-if="tasks.list.length>0">:
                  <a
                    v-for="(item, index) in tasks.list"
                    :key="index"
                    :name="index"
                    class="link"
                    @click="routeToConsultantDetail(item.advisor_id)">
                    {{ item.advisor.name_prefix || ''}} {{ item.advisor.full_name }} {{
                      repeatIds.includes(item.advisor_id)
                        ? `#${item.rank}` : ''
                    }}
                    <span v-if="index+1 !== tasks.list.length">, </span>
                  </a>
                </span>
              </p>
            </div>
          </div>
        </div>
        <div v-if="suspectedCompanyList.length > 0" class="el-alert el-alert--warning is-light mb-8">
          <i class="el-alert__icon el-icon-warning is-big" />
          <div class="el-alert__content">
            <span class="el-alert__title is-bold"> WARNING! The following companies are on Capvision’s DNC List:({{
              suspectedCompanyList.length
            }}):{{ DNC_list.join() }}<br>
              Please confirm these experts is not employed at this company or a subsidiary of this company before
              contacting。</span>
            <div v-for="(item, index) in suspectedCompanyList" :key="index">
              <p class="el-alert__description">
                {{ item.warning_text }}
              </p>
            </div>
            If there are any questions, please contact Capvision Compliance.
          </div>
        </div>

        <div v-if="emailPreview" class="mt-normal">
          <div class="mb-normal"><b>Email content example:</b></div>
          <div class="text-center" v-html="emailPreview.subject" />
          <div v-html="emailPreview.content" />
        </div>
      </div>

      <span slot="footer" class="dialog-footer flex justify-content-between">
        <div>
          <send-on-behalf
            :checked.sync="is_send_on_behalf"
            :user.sync="send_on_behalf_user"
            :members="project.members"
            @change="handleCommand(selectedTemplate)" />
        </div>

        <el-button
          type="primary"
          :disabled="!emailPreview"
          :loading="sendLoading"
          @click="sendEmails">Send</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email.js'
import Mixin from './mixin'
import Mixin_v2 from './mixin_v2'
import _ from 'lodash'
import SendOnBehalf from '@/views/project/consultation/detail/components/SendEmail/components/SendOnBehalf'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'OutreachAll',
  components: { SendOnBehalf },
  mixins: [Mixin, Mixin_v2],
  data() {
    return {
      templateLoading: false,
      templateList: [],
      selectedTemplate: null,
      emailLoading: false,
      emailPreview: null,
      check_list: [],
      suspectedCompanyList: [],
      DNC_list: [],
      reject_task_list: [
        { key: 'advisors_declined', tip: 'Expert declined', list: [], total: 0 },
        { key: 'advisors_have_dnc_experiences', tip: 'Blacklisted Company', list: [], total: 0 },
        { key: 'advisors_in_blacklist_status', tip: 'Blacklisted Advisors', list: [], total: 0 },
        { key: 'advisors_have_invalid_email_address', tip: 'No or incorrect email address', list: [], total: 0 },
        { key: 'incorrect_status_tasks', tip: 'Outreach is complete, no need to send outreach again', list: [], total: 0 },
        { key: 'list_wise_validation_failed', tip: 'Failed to pass ListWise verification', list: [], total: 0 },
        { key: 'email_address_score_too_low', tip: 'Failed to pass SendGrid verification', list: [], total: 0 },
        { key: 'email_with_sensitive_domain', tip: 'Contains email addresses from national government domains[.gov or .go]', list: [], total: 0 },
        { key: 'employees_conflict_investment_target', tip: 'The current employers of the expert conflict with the investment target of the project.', list: [], total: 0 },
      ],
      is_send_on_behalf: false,
      send_on_behalf_user: null,
    }
  },

  computed: {
    ...mapGetters([
      'roles',
    ]),

    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),

    approvedBcgTemplate() {
      return this.project?.approved_bcg_templates?.[0]
    },
  },
  methods: {
    async handleDropdown() {
      if (!this.lead_email_vendor) await this.getSystemLeadEmailVendor()
      if (!this.templateList.length) {
        const params = {
          reference_ids: this.$route.params.project_id,
          reference_type: 'PROJECT',
          content_type: 'PROJECT_OUTREACH',
          has_permission: true,
          page: 1,
          size: 999,
          include_default_reference_type: true,
          uid: this.$store.state.user.uid,
        }

        this.templateLoading = true
        await EmailAPI.getEmailList(params).then(data => {
          if (this.project.sub_type === 'Survey') {
            this.templateList = data.list.filter(
              item => item.tags && item.tags.includes('SURVEY_OUTREACH'))
          } else {
            this.templateList = data.list.filter(
              item => !(item.tags && item.tags.includes('SURVEY_OUTREACH')))
          }
        }).finally(() => {
          this.templateLoading = false
        })
      }
    },
    handleCommand(template) {
      this.dialog_loading = true
      this.rejectedTasks = []
      this.passTasks = []
      this.selectedTemplate = template
      const params = {
        project_id: this.project.id,
        email_template_id: this.selectedTemplate.id,
        angle_id: null,
        only_leads: true,
        represent_user_id: this.is_send_on_behalf ? this.send_on_behalf_user.id : undefined,
        extra: 'advisor.jobs.company',
      }

      return EmailAPI.outreachAllCheck(params).then(data => {
        this.emailPreview = data.email_preview
        this.check_list = data
        this.reject_task_list.forEach(item => {
          item.list = data[item.key]
          item.total = data[item.key].length
        })
        this.suspectedCompanyList = []
        if (data.advisors_have_dnc_experiences_warning.length > 0) {
          data.advisors_have_dnc_experiences_warning.forEach(item => {
            this.suspectedCompanyList.push(this.checkSuspectedCompany(item.advisor))
          })
          this.DNC_list = _.uniq(this.suspectedCompanyList.map(item => item.company_name))
        }
      }).finally(() => {
        this.dialog_loading = false
        this.dialogVisible = true
      })
    },
    handleBcgTemplate() {
      const { original_template, content_template, subject_template } = this.approvedBcgTemplate
      const template = {
        ...original_template,
        content_template,
        subject_template,
      }
      this.handleCommand(template)
    },

    sendEmails() {
      const vendor = this.allowOutreach ? this.lead_email_vendor.allow_outreach : this.lead_email_vendor.normal
      const params = {
        project_id: this.project.id,
        email_template_id: this.selectedTemplate.id,
        angle_id: null,
        only_leads: true,
        represent_user_id: this.is_send_on_behalf ? this.send_on_behalf_user.id : undefined,
        to_leads_vendor: ['POSTMARK_RESEND_RANDOM', 'POSTMARK_MAILGUN_RANDOM', 'POSTMARK_MAILJET_RANDOM'].includes(vendor) ? vendor.replace('_RANDOM', '') : vendor,
      }
      this.sendLoading = true
      return EmailAPI.outreachAllSend(params).then(() => {
        this.$message.success('Sending completed.')
        this.$emit('update')
        this.dialogVisible = false
      }).finally(() => {
        this.sendLoading = false
      })
    },

    resetData() {
      this.check_list = []
      this.emailPreview = null
      this.reject_task_list = [
        { key: 'advisors_declined', tip: 'Expert declined', list: [], total: 0 },
        { key: 'advisors_have_dnc_experiences', tip: 'Blacklisted Company', list: [], total: 0 },
        { key: 'advisors_in_blacklist_status', tip: 'Blacklisted Advisors', list: [], total: 0 },
        { key: 'advisors_have_invalid_email_address', tip: 'No or incorrect email address', list: [], total: 0 },
        { key: 'incorrect_status_tasks', tip: 'Outreach is complete, no need to send outreach again', list: [], total: 0 },
        { key: 'list_wise_validation_failed', tip: 'Failed to pass ListWise verification', list: [], total: 0 },
        { key: 'email_address_score_too_low', tip: 'Failed to pass SendGrid verification', list: [], total: 0 },
      ]
      this.suspectedCompanyList = []
      this.is_send_on_behalf = false
      this.send_on_behalf_user = null
    },

  },
}
</script>

<style scoped>

</style>
