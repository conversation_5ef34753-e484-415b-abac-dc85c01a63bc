<template>
  <div>
    <div v-if="remaining_usage" class="contract-box">
      <div class="title">Client Contract Usage</div>
      <div><b> MTD Usage : </b><span class="primary">{{month_usage}}</span></div>
      <div><b>Remaining Hours : </b>
        <span v-if="remaining_usage === '-'"><svg-icon icon-class="infinite" /></span>
        <span v-else class="success">{{remaining_usage}}</span>
      </div>
      <div><b>End of contract : </b><span class="warning">{{contract_end_time | momentFormat()}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'

export default {
  name: 'ClientContractView',
  props: {
    clientId: [Number, String],
  },
  data() {
    return {
      month_usage: 0,
      remaining_usage: null,
      contract_end_time: null,
    }
  },
  mounted() {
    this.getContract()
  },
  methods: {
    getContract() {
      return ClientAPI.getClient(this.clientId, 'month_usage_hours,contracts,contracts.used_hours').then(data => {
        this.month_usage = data.month_usage_hours
        if (data.contracts.length) {
          const contract = data.contracts.filter(item => item.effective_status === 'EFFECTIVE' && item.payway !== 'PROJECT_BASE')
          if (contract.length) {
            switch (contract[0].payway) {
              case 'PREPAY':
                this.remaining_usage = (contract[0].charge_hours - contract[0].used_hours).toFixed(4)
                break
              case 'PAYGO':
                this.remaining_usage = '-'
                break
              default:
                this.remaining_usage = '-'
                break
            }
            this.contract_end_time = contract[0].end_time
          }
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
  .contract-box {
    font-size: 12px;
    padding:0 4px;
    color: #777;
    margin-right: .5em;
    box-shadow: rgba(0, 0, 0, 0.1) 0 2px 12px 0;

    div {
      padding-bottom: 0.1em;
      white-space: nowrap;
    }
  }

  .title {
    font-weight: bold;
    margin-bottom: 0.4em;
  }
</style>
