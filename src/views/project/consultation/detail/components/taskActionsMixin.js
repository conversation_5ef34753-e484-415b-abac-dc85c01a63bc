import ProjectAPI from '@/api/project'

export default {
  data() {
    return {
      custom_extra: [
        'angle',
        'advisor_schedules',
        'advisor.email_address_score',
        'advisor.jobs.legal_validate_result',
        'latest_submitted_sq.questions',
        'latest_submitted_sq.answers',
        'client.contracts',
        'client.preference',

        /* 以下extra在后端有逻辑，必须全部传*/
        'senior_rate_multiplier_considering_unsigned_tc',
        'senior_rate_multiplier',
        'standard_rate_multiplier_display_to_client',
        'given_senior_rate_multiplier',
      ],
    }
  },

  methods: {
    getExtra() {
      switch (this.task_action_type) {
        case 'Copy Availability':
          return ['advisor_schedules']
        case 'To Advisor':
        case 'To Client':
        case 'To Compliance':
        case 'Client Select':
          return this.custom_extra
        default:
          return []
      }
    },
    /* 优化task列表的加载
     task table 加载时，只获取列表所需内容和勾选了task时弹出的btns的disabled所需数据
     在用户点击btn时，调接口获取对应action所需的额外内容,并补充到table中勾选行的原有内容*/

    getTasksByIDS() {
      if (this.getExtra().length) {
        const ids = this.tasks.map(item => item.id)
        const params = {
          params: {
            ids: ids.join(),
            page: 1,
            size: ids.length,
            extra: this.getExtra().join(),
          },
          project_id: this.tasks[0].project_id,
        }

        // 按照用户勾选的顺序返回数据

        return ProjectAPI.getTasksDetail(params).then(data => {
          this.tasks.forEach(item => {
            const target = data.list.find(i => i.id === item.id)
            item = deepMerge(item, target)
          })
        })
      } else {
        return Promise.resolve()
      }

      function deepMerge(target, source) {
        for (const key in source) {
          if (source[key] instanceof Object) {
            if (!target[key]) {
              target[key] = Array.isArray(source[key]) ? [] : {}
            }
            deepMerge(target[key], source[key])
          } else {
            if (!target[key] || ['screening_summary'].includes(key)) {
              target[key] = source[key]
            }
          }
        }
        return target
      }
    },

    markingExpertConflictWithProjectTarget() {
      const advisor_ids = [...new Set(this.tasks.map(item => item.advisor_id))]
      const params = {
        project_id: this.tasks[0].project_id,
        advisor_ids,
      }

      return ProjectAPI.checkAdvisorConflictWithInvestmentProjectTarget(params)
        .then(data => {
          const conflict_advisor_ids = data.map(item => item.id)
          if (conflict_advisor_ids.length) {
            this.tasks.forEach(item => {
              item.advisor_conflict_with_investment_target = conflict_advisor_ids.includes(item.advisor_id)
            })
          }
        })
    },
  },
}
