<template>
  <div>
    <div style="margin-top: 15px;display:flex">
      <div><span class="sort-class">Q{{ index + 1 }}.</span></div>
      <div class="flex-1">
        <div class="flex">
          <tinymce v-model="item.title" :height="100" :toolbar="toolbar" class="mr-8 flex-1" />
          <el-select v-model="item.type" placeholder="Please select">
            <el-option
              v-for="question_type in options.question_types"
              :key="question_type.value"
              :label="question_type.label"
              :value="question_type.value" />
          </el-select>
        </div>
        <div v-if="['CHECKBOX','RADIO','RANKING_SET', 'NO_RANKING_SET'].includes(item.type)">
          <div v-for="(option_item,_index) in item.options" :key="_index" class="mt-8">
            <div class="flex">
              <div v-if="item.type === 'RADIO'" class="option-item">
                <el-radio disabled :value="false" />
                <el-input
                  v-model="option_item.value"
                  placeholder="option"
                  class="mr-8 flex-1"
                  @keydown.native="optionInputKey($event, item)" />
              </div>
              <div v-if="item.type === 'CHECKBOX'" class="option-item">
                <el-checkbox disabled />
                <el-input
                  v-model="option_item.value"
                  placeholder="option"
                  class="mr-8 flex-1"
                  @keydown.native="optionInputKey($event, item)" />
              </div>
              <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)" class="option-item">
                <el-input
                  v-model="option_item.value"
                  placeholder="Description"
                  class="mr-8 w-200px"
                  @keydown.native="optionInputKey($event, item)" />
                <el-input v-if="item.type === 'RANKING_SET'" placeholder="Rank" disabled class="mr-8" style="width:80px" />
                <el-input disabled class="flex-1" />
              </div>
              <el-link
                :underline="false"
                type="primary"
                icon="el-icon-plus"
                @click="addOption(item)" />
              <el-link
                v-if="item.options.length > 1"
                :underline="false"
                type="danger"
                class="ml-8px"
                icon="el-icon-minus"
                @click="removeOption(item,_index)" />
            </div>
            <div v-if="['CHECKBOX','RADIO'].includes(item.type)" style="margin-right:180px;margin-left:20px">
              <el-checkbox v-model="option_item.description" class="ma-3px" @change="changeDescRequire(option_item)">Ask expert to explain answer choices</el-checkbox>
              <div v-if="option_item.description">
                <el-input
                  v-model="option_item.desc_prompt"
                  class="ma-3px"
                  @keydown.native="optionInputKey($event, item)" />
                <el-input
                  type="textarea"
                  class="ma-3px"
                  disabled
                />
                <el-checkbox v-model="option_item.require_desc">Require expert to explain their answer</el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="display:flex; justify-content:space-between;margin-top:20px;">
      <div>
        <el-button
          type="success"
          icon="el-icon-plus"
          :disabled="saveQuestionDisabled(item)"
          @click="$emit('createNextQuestion', item)">
          Next Question
        </el-button>
      </div>
      <div>
        <el-button type="text" @click="$emit('cancelEdit', '', index)">Remove</el-button>
        <el-button type="primary" :disabled="saveQuestionDisabled(item)" @click="$emit('saveQuestion', item)">OK
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
export default {
  name: 'EditSQ',
  components: { Tinymce },
  props: {
    index: {
      type: [String, Number],
      default: '',
    },
    item: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },

  data() {
    return {
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      options: {
        question_types: [
          { label: 'Comment Box', value: 'TEXT' },
          { label: 'Ranking With Explanation', value: 'RANKING_SET' },
          { label: 'Multiple Choice - Single-select', value: 'RADIO' },
          { label: 'Multiple Choice - Multi-select', value: 'CHECKBOX' },
          { label: 'Multiple Explanation', value: 'NO_RANKING_SET' },
        ],
      },
    }
  },

  methods: {

    optionInputKey(event, item) {
      if (event.ctrlKey && event.keyCode === 78) {
        this.addOption(item)
      }
    },

    addOption(item) {
      item.options.push({ value: '', description: false, desc_prompt: 'Please explain your answer below:', require_desc: false })
    },

    changeDescRequire(item) {
      if (!item.description) item.require_desc = false
    },
    removeOption(item, index) {
      item.options.splice(index, 1)
    },

    saveQuestionDisabled(item) {
      const empty_input = !item.title.replace(/\s/g, '').replace(/<[^\/>][^>]*><\/[^>]*>/g, '') || !item.title.trim()
      if (item.type !== 'TEXT') {
        return empty_input || item.options.filter(i => i.value).length < 2
      } else {
        return empty_input
      }
    },
  },
}
</script>

<style scoped lang="scss">
.option-item{
  display: flex;
  flex: 1;
  align-items: center;
  margin-right: 4px;

  ::v-deep .el-radio {
    margin-right: 8px;
  }

  ::v-deep .el-radio__input.is-disabled+span.el-radio__label {
  display: none;
}

  ::v-deep .el-checkbox:last-of-type{
    margin-right: 8px;
  }
}

.ml-8px{
  margin-left: 8px;
}
</style>
