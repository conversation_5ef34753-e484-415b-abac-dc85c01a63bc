<template>
  <div class="inline-block">
    <el-button type="primary" plain @click="dialogVisible = true">Paste</el-button>

    <el-dialog
      title="Paste and click to generate"
      :visible.sync="dialogVisible"
      width="40%"
    >
      <el-input v-model="paste_data" :autosize="{ minRows:4 }" type="textarea" />
      <el-button
        :disabled="!paste_data.trim()"
        type="primary"
        class="mt-normal w-100"
        @click="transformByLineBreak">Generate
      </el-button>
      <template v-if="result_list.length">
        <div
          v-for="(item, index) in result_list"
          :key="index"
          class="flex mt-3">
          <el-input
            v-model="item.title"
            autosize
            type="textarea"
            class="flex-1" />
          <el-link
            :underline="false"
            type="danger"
            icon="el-icon-close"
            @click="removeItem(index)" />
        </div>
        <el-button
          type="success"
          class="mt-normal w-100"
          @click="applyToQuestionnaire">Apply
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PasteToGenerateSQ',
  props: {
    questionForm: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      paste_data: '',
      result_list: [],
    }
  },

  methods: {
    applyToQuestionnaire() {
      this.$emit('update:questionForm', this.questionForm.concat(this.result_list))
      this.paste_data = ''
      this.result_list = []
      this.dialogVisible = false
    },
    transformByLineBreak() {
      this.result_list = this.paste_data.split(/\r?\n/).filter(line => line.trim() !== '').map((item, index) => {
        return {
          title: item,
          type: 'TEXT',
          options: [
            {
              value: '',
              text: '',
              description: false,
              desc_prompt: 'Please explain your answer below:',
              require_desc: false,
            }],
          sort_order: index,
          edit: false,
        }
      })
    },
    removeItem(index) {
      this.result_list.splice(index, 1)
    },
  },
}
</script>

<style scoped>

</style>
