<template>
  <div>
    <table
border="0"
           cellpadding="0"
           cellspacing="0"
           style="width:100%;border-collapse: collapse;border-spacing:0;border:none;">
      <thead>
        <tr>
          <th />
          <th />
          <th>
            <div
              v-if="data.title"
              style="width:100%;margin-bottom:1em;margin-top:1em;text-align: center;font-size: 1.5em;font-weight: bold;">
              {{ data.title }}
            </div>
          </th>
        </tr>
        <tr v-if="data.description">
          <th />
          <th />
          <th>
            <div
              v-if="data.description"
              style="width:100%;border-bottom: 1px solid #E0E0E0;padding-bottom: 5px;font-size: 1rem;margin-bottom:1em;font-weight: 400;text-align: center;">
              {{ data.description }}
            </div>
          </th>
        </tr>
      </thead>
      <tbody>

        <template v-for="(item,index) in question_list">
          <tr :key="`${item.id}-${index}`" style="padding-bottom:10px;word-break: break-all;">
            <td style="white-space:nowrap !important;vertical-align: top;text-align: right;" />
            <td style="width:2rem;white-space:nowrap !important;vertical-align: top;text-align: right;font-family: Calibri;vertical-align: top;">
              {{ item.sort_order + 1 + '. ' }}
            </td>
            <td style="width:96%;vertical-align: top;padding-left:2px;">
              <div
style="vertical-align: baseline;white-space:pre-wrap;font-family: Calibri;"
                   v-html="changeTitle(item.title)" />
            </td>
          </tr>
          <slot v-if="item.type !== 'TEXT'">
            <tr :key="item.id">
              <td />
              <td />
              <td
                v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)"
                style="word-break: break-all;padding-bottom:10px;padding-left:15px;font-family: Calibri;">

                <div
                  v-for="(option, indexKey) in item.options"
                  :key="indexKey"
                  style="max-width: 90%;padding-bottom:5px;">
                  <span
                    style="padding-right:10px;padding-left:5px;">
                    <span style="word-break: break-all;">{{ option.value }}:</span>
                  </span>
                </div>
              </td>
              <td
                v-if="item.type === 'RADIO'"
                style="word-break: break-all;padding-bottom:10px;padding-left:15px;font-family: Calibri;">
                <div
                  v-for="(option, indexKey) in item.options"
                  :key="indexKey"
                  style="max-width: 90%;padding-bottom:5px;">
                  <span
                    style="padding-right:10px;padding-left:5px;">(&nbsp;&nbsp;)
                    <span style="word-break: break-all;">{{ option.text }}
                      <span v-if="option.description">({{option.desc_prompt}})</span>
                    </span>
                  </span>
                </div>
              </td>
              <td
                v-if="item.type === 'CHECKBOX'"
                style="word-break: break-all;padding-bottom:10px;padding-left:15px;font-family: Calibri;">
                <div
                  v-for="(option, indexKey) in item.options"
                  :key="indexKey"
                  style="max-width: 90%;padding-bottom:5px;">
                  <span
                    style="padding-right:10px;padding-left:5px;">(&nbsp;&nbsp;)
                    <span style="word-break: break-all;">{{ option.text }}
                     <span v-if="option.description">({{option.desc_prompt}})</span>
                    </span>
                  </span>
                </div>
              </td>
            </tr>
          </slot>
          <slot v-else>
            <tr :key="item.id">
              <td />
              <td />
              <td />
            </tr>
          </slot>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'QuestionnaireForCopy',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      question_list: [],
    }
  },
  watch: {
    'data': {
      handler(newVal) {
        if (newVal) {
          this.question_list = this.data.qa_list_snapshot || this.data.questions
        }
      }, immediate: true,
    },
  },
  methods: {
    changeTitle(title) {
      return title
        .replace('<p', '<span')
        .replace('</p>', '</span>')
        .replace('<div>\n<div>', '')
        .replace('<div', '<span')
        .replace('</div>', '</span>')
    },
  },
}
</script>

<style scoped>

</style>
