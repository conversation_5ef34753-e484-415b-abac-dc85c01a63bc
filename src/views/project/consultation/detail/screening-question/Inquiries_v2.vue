<template>
  <div>
    <div class="flex">

      <div v-loading="loading" class="inquiries-list" :class="{ 'list-mt' :detail_show }">
        <el-card
          v-for="(item) in inquiryList"
          :key="item.id"
          class="box-card col-3"
          shadow="hover"
        >
          <div slot="header" class="card-head">
            <div>
              <span class="title">{{ item.title }}</span>
              <el-tooltip
                v-if="item.tags && item.tags.includes('PHYSICIAN')"
                effect="dark"
                content="Physician Screening Questions"
                placement="top">
                <svg-icon icon-class="physician" class="ml-8" />
              </el-tooltip>
            </div>
            <span class="card-id">ID - {{ item.id }}</span>
          </div>

          <div class="card-actions" @click.stop>
            <el-dropdown @command="editQuestion">
              <el-button size="mini" class="item">
                Preview & Edit
                <i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="ques in item.questions"
                  :key="ques.id"
                  :command="ques">
                  <div class="flex">
                    <b>
                      Q{{ ques.sort_order + 1 }}.</b>
                    <div class="outermost-p-td-0" v-html="ques.title" />
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown @command="addQuestionnaireToAngle">
              <el-button size="mini" class="item">
                Add to Task Angle
                <i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown" style="width:158px;">
                <el-dropdown-item
                  v-for="angle in angle_list"
                  :key="angle.id"
                  :command="{angle:angle,branch_id:item.id}">{{ angle.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              size="mini"
              class="item"
              @click="copyInquiry(item)"
            >Copy
            </el-button>
            <el-popconfirm
              icon="el-icon-info"
              icon-color="red"
              title="Are you sure you want to discard this questionnaire?"
              cancel-button-text="Cancel"
              confirm-button-text="Confirm"
              confirm-button-type="danger"
              @confirm="removeInquiry(item)"
            >
              <el-button
                slot="reference"
                size="mini"
                class="item"
              >Discard
              </el-button>
            </el-popconfirm>
            <el-button
              size="mini"
              class="item"
              @click="cloneQuestion(item)"
            >Clone
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!--    <el-dialog-->
    <!--      title="Update"-->
    <!--      :visible.sync="editDialogVisible"-->
    <!--      width="30%"-->
    <!--    >-->
    <!--      <div style="display: flex;">-->
    <!--        <div class="sort-class">Q{{ edit_question.sort_order + 1 }}</div>-->
    <!--        <el-input-->
    <!--          v-model="edit_question.question_content"-->
    <!--          type="textarea"-->
    <!--          :autosize="{ minRows: 1, maxRows: 6}" />-->
    <!--      </div>-->
    <!--      <span slot="footer" class="dialog-footer">-->
    <!--        <el-button @click="editDialogVisible = false">Cancel</el-button>-->
    <!--        <el-button type="primary" @click="updateQuestion">Save</el-button>-->
    <!--      </span>-->
    <!--    </el-dialog>-->
  </div>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import ProjectAPI from '@/api/project'

export default {
  name: 'InquiriesV2',

  props: {
    id: {
      type: Number,
      default: null,
    },
    project: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },

  data() {
    return {
      angle_list: [],
      inquiryList: [],
      inquiry_detail: {},
      detail_show: false,
      loading: true,
      edit_question: {
        branch_id: '',
        question_id: '',
        question_content: '',
        sort_order: '',
      },
    }
  },

  created() {
    this.getInquiries()
  },
  mounted() {
    this.getTaskAngleList()
  },

  methods: {
    getInquiries() {
      this.loading = true
      return InquiryAPI.getInquiryOne(this.id)
        .then(data => {
          this.inquiryList = data['branches']
          this.loading = false
        })
    },

    getTaskAngleList() {
      const params = {
        project_id: this.project.id,
      }
      return ProjectAPI.getProjectTaskAngle(params).then(data => {
        this.angle_list = data['list']
      })
    },

    addQuestionnaireToAngle(data) {
      const params = {
        inquiry_branch_id: data.branch_id,
      }
      return ProjectAPI.questionnaireBindingAngle(data.angle.id, params).then(data => {
        this.$message({
          message: 'Angle bind successfully',
          type: 'success',
        })
      }, () => {
        this.$message({
          message: 'Angle bind failed',
          type: 'error',
        })
      })
    },

    editQuestion(value) {
      this.$router.push({
        name: 'ProjectScreeningQuestionUpdate',
        params: { project_id: this.project.id, inquiry_id: this.id },
        query: { branch_id: value.branch_id, project_name: this.project.name },
      })
    },

    cloneQuestion(item) {
      this.$router.push({
        name: 'ProjectScreeningQuestionUpdate',
        params: { project_id: this.project.id, inquiry_id: this.id },
        query: { clone_branch_id: item.id, project_name: this.project.name },
      })
    },

    copyInquiry(item) {
      let questions_list = ''
      item.questions.forEach(item => {
        questions_list += `Q${item.sort_order + 1}. ${item.title}<br/>`
      })
      const temp_el = document.createElement('div')
      temp_el.innerHTML = `<div>` + questions_list + `</div>`

      document.body.appendChild(temp_el)
      const select = window.getSelection()
      select.removeAllRanges()

      const range = document.createRange()
      range.selectNode(temp_el)
      select.addRange(range) // to select text

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      }

      window.getSelection().removeAllRanges()// clear selection
      document.body.removeChild(temp_el)
    },

    defaultInquiry(item) {
      return InquiryAPI.setDefaultInquiry(item.inquiry_id, item.id)
        .then(() => {
          this.getInquiries()
        })
    },

    removeInquiry(item) {
      return InquiryAPI.deleteInquiry(item.inquiry_id, item.id)
        .then(() => {
          this.loading = true
          InquiryAPI.getInquiryOne(this.id)
            .then(data => {
              this.inquiryList = data['branches']
              this.loading = false
            })
        })
    },

  },
}

</script>

<style lang="scss" scoped>
.inquiries-list {
  flex: 2;
}

.list-mt {
  margin-left: 20px;
  margin-top: 42px;
}

.box-card {
  margin: 0 10px 10px 0;
}

.active {
  border: 1px solid #42B983;
}

.col-3 {
  display: inline-block;
  width: 32%;
}

.card-head {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;

  .title {
    font-size: 1.1rem;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:hover {
      color: #3f9eff;
      cursor: pointer;
    }
  }

  .card-id {
    white-space: nowrap;
    margin-left: 1rem;
  }
}

.card-actions {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .item {
    margin: 4px;
  }
}

.sort-class {
  height: 28px;
  width: 40px;
  font-size: 1rem;
  line-height: 28px;
  font-weight: bold;
  margin-right: 5px;
}
</style>

