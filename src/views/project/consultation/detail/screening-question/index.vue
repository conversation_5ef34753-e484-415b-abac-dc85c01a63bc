<template>
  <div>
    <slot v-if="isCurrentRoute">
      <div class="filter-container">
        <el-button
          type="success"
          class="filter-item"
          icon="el-icon-plus"
          @click="createInquiry()"
        >New Screening Question Set
        </el-button>
      </div>
      <inquiries v-if="has_inquiry" :id="inquiry_id" :project="projectInfo" />
      <loading-status v-if="!has_inquiry && !is_loading" :status="is_loading" :data="has_inquiry" />
    </slot>
    <router-view />
  </div>
</template>

<script>
import Inquiries from './Inquiries_v2'
import ProjectAPI from '@/api/project'
import LoadingStatus from '@/components/LoadingStatus/index'

export default {
  name: 'ProjectScreeningQuestion',
  components: { LoadingStatus, Inquiries },
  props: {
    projectInfo: Object,
  },
  data() {
    return {
      has_inquiry: false,
      inquiry_id: null,
      client_id: null,
      is_loading: true,
      inquiryList: [],
    }
  },
  computed: {
    isCurrentRoute() {
      if (this.$route.name === 'ProjectScreeningQuestion') {
        this.getInquiry()
      }
      return this.$route.name === 'ProjectScreeningQuestion'
    },
  },

  methods: {
    getInquiry() {
      this.is_loading = true
      return ProjectAPI.getProjectInquiry(this.$route.params.project_id)
        .then(data => {
          this.client_id = data.client_id
          if (data.sq_id) {
            this.inquiry_id = data.sq_id
            this.has_inquiry = true
          }
          this.is_loading = false
        })
    },

    createInquiry() {
      const project_name = this.projectInfo.name

      if (this.has_inquiry) {
        this.$router.push({
          name: 'ProjectScreeningQuestionUpdate',
          params: { inquiry_id: this.inquiry_id },
          query: { project_name: project_name },
        })
      } else {
        this.$router.push({
          name: 'ProjectScreeningQuestionCreate',
          params: { client_id: this.client_id },
          query: { project_name: project_name },
        })
      }
    },
  },

}

</script>

