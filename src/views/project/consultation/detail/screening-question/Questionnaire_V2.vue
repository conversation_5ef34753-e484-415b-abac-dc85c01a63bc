<template>
  <div>
    <div v-loading="loading" class="questionnaire-container w-1400">

      <!-- Basic Info -->
      <el-form label-width="150px">
        <el-form-item label="Question Set Name">
          <el-input v-model="new_title" :value="new_title" />
        </el-form-item>
        <el-form-item label="Title">
          <el-input v-model="new_name" :value="new_name" />
        </el-form-item>
        <el-form-item label="Prompt">
          <el-input
            v-model="description"
            class="description"
            type="textarea" />
        </el-form-item>
        <el-form-item label="Physician SQ">
          <el-switch
            v-model="is_physician_sq"
            @change="handleChangeIsPhysicianSq"
          />
        </el-form-item>
      </el-form>

      <!-- Questions -->
      <div>
        <div
          v-for="(item, index) in questionForm"
          :id="`question-`+index"
          :key="index"
          class="question-box"
        >
          <div v-if="!item.edit" class="first-level-title">
            <b>Q{{ index + 1 }}.</b>
            <span class="outermost-p-td-0 break-word" v-html="item.title" />
          </div>
          <div v-if="!item.edit" class="mt-8">
            <div v-if="['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(item.type)">

              <div v-for="(option_item, option_index) in item.options" :key="option_index" class="flex mt-8">

                <div class="option-item">
                  <template v-if="['CHECKBOX','RADIO'].includes(item.type)">
                    <el-radio v-if="item.type === 'RADIO'" disabled :value="false" />
                    <el-checkbox v-if="item.type === 'CHECKBOX'" disabled />
                    <div class="mr-8 flex-1">
                      <el-input v-model="option_item.value" disabled />
                      <el-input
                        v-if="option_item.description"
                        class="desc"
                        :class="{'required':option_item.require_desc}"
                        type="textarea"
                        :placeholder="option_item.desc_prompt"
                        disabled />
                    </div>
                  </template>

                  <template v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                    <el-input v-model="option_item.value" disabled class="mr-8 w-200px" />
                    <el-input v-if="item.type === 'RANKING_SET'" placeholder="Rank" disabled class="mr-8" style="width:80px" />
                    <el-input disabled class="flex-1" />
                  </template>
                </div>
              </div>
            </div>
            <el-input v-else disabled class="flex-1" />
          </div>
          <div v-if="!item.edit" class="short-box">
            <el-tooltip content="Move up" placement="left" :open-delay="300">
              <el-link
                v-if="index > 0"
                type="info"
                icon="el-icon-top"
                :underline="false"
                @click="moveUp(index)" />
            </el-tooltip>
            <el-tooltip content="Edit" placement="left" :open-delay="300">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-edit-outline"
                @click="editQuestion(item, index)" />
            </el-tooltip>
            <el-tooltip content="Delete" placement="left" :open-delay="300">
              <el-link
                type="danger"
                :underline="false"
                icon="el-icon-delete"
                @click="removeQuestion(index)" />
            </el-tooltip>
            <el-tooltip content="Copy" placement="left" :open-delay="300">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-document-copy"
                @click="copyQuestion(item, index)" />
            </el-tooltip>
            <el-tooltip content="Move Down" placement="left" :open-delay="300">
              <el-link
                v-if="index < questionForm.length-1"
                type="info"
                :underline="false"
                icon="el-icon-bottom"
                @click="moveDown(index)" />
            </el-tooltip>
          </div>

          <div v-else>
            <edit-s-q
              :index="index"
              :item="item"
              @createNextQuestion="createNextQuestion"
              @cancelEdit="cancelEdit"
              @saveQuestion="saveQuestion" />
          </div>
        </div>

        <div v-for="(item, index) in fixedQuestions" :key="item.tags[0]" class="question-box">
          <div v-if="item.tags && item.tags.includes('ADVISOR_NPI_CONFIRM')" class="info mb-normal">
            This question is only for "Physician Consultation" project.
          </div>
          <div v-if="item.tags && item.tags.includes('ADVISOR_JOB_CONFIRM')" class="info mb-normal">
            This question is used to confirm the expert's information and will not be displayed to the client.
          </div>
          <div v-if="!item.edit" class="first-level-title">
            <b>Q{{ questionForm.length + index + 1 }}.</b>
            <span class="outermost-p-td-0" v-html="item.title" />
          </div>
          <div v-if="!item.edit" class="mt-8">
            <el-input disabled class="flex-1" />
          </div>
          <div v-if="!item.edit" class="short-box">
            <el-tooltip content="Edit" placement="left" :open-delay="300">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-edit-outline"
                @click="editQuestion(item)" />
            </el-tooltip>
            <el-tooltip content="Delete" placement="left" :open-delay="300">
              <el-link
                type="danger"
                :underline="false"
                icon="el-icon-delete"
                @click="removeQuestion(index, 'fixedQuestions')" />
            </el-tooltip>
          </div>

          <div v-else>
            <div style="margin-top: 15px;display:flex">
              <div><span class="sort-class">Q{{ questionForm.length + index + 1 }}.</span></div>
                  <tinymce
                    v-model="item.title"
                    :height="100"
                    :toolbar="toolbar"
                    class="mr-8 flex-1" />
            </div>
            <div style="display:flex; justify-content:flex-end;margin-top:20px;">
              <div>
                <el-button type="text" @click="cancelEdit('fixedQuestions')">Remove</el-button>
                <el-button
                  type="primary"
                  :disabled="!item.title"
                  @click="saveQuestion(item)">OK
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!add">
          <el-button
            icon="el-icon-plus"
            type="success"
            @click="actionAddQuestion()">
            Add Question
          </el-button>
          <paste-to-generate-s-q :question-form.sync="questionForm" />
          <el-checkbox v-model="addToFirst" class="ml-8">
            To The First
          </el-checkbox>
          <div style="float:right">
            <el-button
              plain
              type="success"
              icon="el-icon-document-copy"
              :disabled="!questionForm.length"
              @click="copyQuestionnaire">
              Copy
            </el-button>
            <el-popover
              v-model="popoverVisible"
              width="300"
              placement="top"
            >
              <p><i class="el-icon-info" /> Confirm to generate？</p>
              <div style="text-align: right;">
                <el-button type="text" @click="popoverVisible = false">Cancel</el-button>
                <el-button
                  v-if="!inquiry_id"
                  type="primary"
                  :loading="submitting"
                  :disabled="!questionForm.length"
                  @click="checkBeforeCreateInquiry">
                  Create
                </el-button>
                <el-button
                  v-else
                  type="primary"
                  :loading="submitting"
                  :disabled="!questionForm.length"
                  @click="updateInquiry">Confirm
                </el-button>

              </div>

              <el-button
                slot="reference"
                type="primary"
                class="ml-3"
                :disabled="!questionForm.length"
                icon="el-icon-check">
                Generate
              </el-button>
            </el-popover>
          </div>
        </div>
      </div>
    </div>

    <questionnaire-for-copy
      v-if="copy_data"
      id="sq-copy"
      style="display: none;"
      :data="copy_data" />
  </div>
</template>

<script>
import { copyUtil } from '@/utils/tool'
import InquiryAPI from '@/api/inquiry'
import ProjectAPI from '@/api/project'
import Tinymce from '@/components/Tinymce'
import questionnaireForCopy
  from '@/views/project/consultation/detail/screening-question/components/QuestionnaireForCopy'
import PasteToGenerateSQ from '@/views/project/consultation/detail/screening-question/components/PasteToGenerateSQ'
import _ from 'lodash'
import editSQ from '@/views/project/consultation/detail/screening-question/components/editSQ'
import { mapState } from 'vuex'

export default {
  name: 'QuestionnaireV2',
  components: { Tinymce, editSQ, questionnaireForCopy, PasteToGenerateSQ },
  props: {
    questionContent: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      toolbar: ['undo redo | fontselect fontsizeselect | bold underline italic | numlist bullist backcolor | alignleft  aligncenter alignright'],
      loading: true,
      submitting: false,
      add: false,
      addToFirst: false,
      questionForm: [],
      fixedQuestions: [],
      editQues: {},
      editIndex: null,
      title: '',
      new_title: '',
      new_name: `Screening Question — ${this.$route.query.project_name}`,
      description: 'Please complete the below questions with as much detail as possible (3 – 5 sentence responses, if necessary). This will greatly assist in making sure you are a good fit for our client\'s request:',
      is_physician_sq: false,
      project_id: null,
      client_id: null,
      inquiry_id: null,
      branch_id: null,
      create: false,
      popoverVisible: false,
      default_inquiry: true,
      question_type: 0,
      isUpdate: false,
      isClone: false,
      copy_data: null,
      option_item_default: {
        value: '',
        text: '',
        description: false,
        desc_prompt: 'Please explain your answer below:',
        require_desc: false,
      },
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
    isClearView() {
      return this.projectInfo?.client.preference?.preferred_workflow === 'CLEARVIEW'
    },
  },
  created() {
    this.project_id = this.$route.params.project_id
    const router_name = this.$route.name
    const query = this.$route.query
    const params = this.$route.params

    if (router_name === 'ProjectScreeningQuestionUpdate') {
      // update or clone
      this.inquiry_id = params.inquiry_id
      this.branch_id = query.branch_id
      this.isUpdate = !!query.branch_id
      this.isClone = !!query.clone_branch_id
      if (this.isUpdate) this.getSQ(query.branch_id)
      if (this.isClone) this.getSQ(query.clone_branch_id)
      if (!this.isUpdate && !this.isClone) {
        this.loading = false
        this.initQuestionForm()
        this.initFixedQuestions()
      }
    } else {
      // create
      this.client_id = params.client_id
      this.loading = false
      this.is_physician_sq = this.projectInfo?.sub_type === 'Physician Consultation'
      this.initQuestionForm()
      this.initFixedQuestions()
    }
  },

  methods: {
    initQuestionForm() {
      if (!this.isClearView) return
      const titles = [
        '<div><strong>[COMPLIANCE]</strong> Do you consent to this interview being audio recorded for internal purposes (e.g., note-taking and quality assurance)? </div>',
        '<div><strong>[COMPLIANCE]</strong> Do you consent to a copy of the audio recording being shared with the sponsoring company, with all personal identifiers removed to ensure your anonymity? </div>',
        '<div><strong>[COMPLIANCE]</strong> Do you consent to representatives from the sponsoring company listening in on this interview in real time? Please note the sponsor will not know your identity and vice versa. Their role is to observe the discussion for research purposes only. </div>',
        '<div><strong>[COMPLIANCE]</strong> Do you consent to the client passing your contact details to the sponsoring company’s drug safety department if an adverse event, product compliant, or other safety finding/special situation is mentioned during the interview? </div>',
        '<div><strong>[COMPLIANCE]</strong> Your responses during this interview will be used solely for research purposes. The data collected will be anonymized, and any recordings will be stored securely. </div>',
        '<div><strong>[COMPLIANCE]</strong> Please be aware that you have the right to withdraw from this interview at any time, even after consenting. If you choose to withdraw, your data will not be used in the study. </div>',
      ]
      this.questionForm = [
        ...this.questionForm, ...titles.map((title, index) => ({
          title,
          type: 'RADIO',
          edit: false,
          options: [
            {
              ...this.option_item_default,
              value: index <= 3 ? 'Yes' : 'Yes, I understand and agree',
            }, {
              ...this.option_item_default,
              value: index <= 3 ? 'No' : 'No, I do not agree',
            },
          ],
        })),
      ]
    },
    initFixedQuestions() {
      this.fixedQuestions.push({
        title: 'Please confirm your current title and employer',
        type: 'TEXT',
        edit: false,
        id: null,
        tags: ['ADVISOR_JOB_CONFIRM'],
      })
      this.handleChangeIsPhysicianSq()
    },
    handleChangeIsPhysicianSq() {
      const index = this.fixedQuestions.findIndex(item => item.tags.includes('ADVISOR_NPI_CONFIRM'))
      if (this.is_physician_sq) {
        if (index > -1) return
        this.fixedQuestions.unshift({
          title: 'Please provide your National Provider Identifier below:',
          type: 'TEXT',
          edit: false,
          id: null,
          tags: ['ADVISOR_NPI_CONFIRM'],
        })
      } else {
        if (index > -1) this.fixedQuestions.splice(index, 1)
      }
    },
    copyQuestionnaire() {
      this.copy_data = {
        name: this.new_name,
        title: this.new_title,
        is_default_using: false,
        description: this.description,
        tags: this.is_physician_sq ? ['PHYSICIAN'] : [],
        client_publish_question_ids: null,
        qa_list_snapshot: this.formatQuestions(),
      }
      setTimeout(() => {
        const copyText = document.querySelector('#sq-copy').innerHTML

        copyUtil(copyText, true)
          .then((result) => {
            this.$message({
              message: 'Copy successful',
              type: 'success',
            })
          })
          .catch(() => {
            this.$message({
              message: 'Copy failed',
              type: 'error',
            })
          })
      }, 0)
    },
    getSQ(branch_id) {
      return InquiryAPI.getInquiryOne(this.inquiry_id)
        .then(data => {
          const result = data.branches.filter(item => item.id === +branch_id)[0]
          this.new_name = result.name || `Screening Question — ${this.$route.query.project_name}`
          this.new_title = this.isClone ? '' : result.title || ''
          this.description = result.description || ''
          this.is_physician_sq = result.tags.includes('PHYSICIAN')
          this.questionForm = result.questions.map(item => {
            if (!item.options) {
              item.options = [Object.assign({}, this.option_item_default)]
            }
            return item
          }) || []

          const special_tags = ['ADVISOR_NPI_CONFIRM', 'ADVISOR_JOB_CONFIRM']
          special_tags.forEach(tag => {
            const index = this.questionForm.findIndex(item => item.tags?.includes(tag))
            if (index > -1) {
              this.fixedQuestions.push(this.questionForm[index])
              this.questionForm.splice(index, 1)
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    actionAddQuestion() {
      this.add = true
      this.questionForm[this.addToFirst ? 'unshift' : 'push']({
        title: '',
        type: 'TEXT',
        options: [Object.assign({}, this.option_item_default)],
        sort_order: this.questionForm.length,
        edit: true,
      })
      this.$nextTick(() => {
        const element = document.querySelector(`#question-${this.addToFirst ? 0 : this.questionForm.length - 1}`)
        element?.scrollIntoView({ behavior: 'smooth' })
      })
    },

    saveQuestion(val) {
      val.edit = false
      this.add = false
    },

    createNextQuestion(item) {
      this.saveQuestion(item)
      this.actionAddQuestion()
    },

    cancelEdit(type, index) {
      if (type) {
        this.fixedQuestions.splice(index, 1)
      } else {
        this.questionForm.splice(index, 1)
      }
      this.add = false
    },

    editQuestion(val) {
      this.$set(val, 'edit', true)
      this.add = true
    },

    removeQuestion(index, type) {
      if (type) {
        this.fixedQuestions.splice(index, 1)
      } else {
        this.questionForm.splice(index, 1)
      }
    },

    copyQuestion(item, index) {
      const data = Object.assign({}, item)
      data.id = null
      this.questionForm.splice(index + 1, 0, data)
    },

    createInquiry() {
      this.submitting = true
      const data = {
        creator_type: 'KM',
        type: 'SCREENING',
        client_id: this.client_id,
        name: this.new_name,
        branch: {
          is_default_using: this.default_inquiry,
          description: this.description,
          title: this.new_title,
          tags: this.is_physician_sq ? ['PHYSICIAN'] : [],
        },

      }
      data.branch.questions = this.formatQuestions()

      return InquiryAPI.addInquiry(data)
        .then(data => {
          ProjectAPI.saveProjectInquiry(this.project_id, data.branch.inquiry_id)
            .then(() => {
              this.popoverVisible = false
              this.$message({
                message: 'Create successful',
                type: 'success',
              })
              this.$store.dispatch('project/getInfo', this.project_id)
              this.$router.push({ name: 'ProjectScreeningQuestion', params: { project_id: this.project_id } })
            }, () => {
            })
        })
        .finally(() => {
          this.submitting = false
        })
    },

    checkBeforeCreateInquiry() {
      if (!this.checkTitleBeforeSave()) return
      this.submitting = true
      return ProjectAPI.getProjectInquiry(this.project_id)
        .then(data => {
          if (data.sq_id) {
            this.inquiry_id = data.sq_id
            this.updateInquiry()
          } else {
            this.createInquiry()
          }
        })
        .finally(() => {
          this.submitting = false
        })
    },

    updateInquiry() {
      if (!this.checkTitleBeforeSave()) return
      this.submitting = true
      const params = {
        name: this.new_name,
        title: this.new_title,
        is_default_using: false,
        description: this.description,
        tags: this.is_physician_sq ? ['PHYSICIAN'] : [],
        questions: this.formatQuestions(),
      }
      const request = this.isUpdate
        ? InquiryAPI.updateQuestion(this.inquiry_id, this.branch_id, params, 'SAVE,UPDATE,DELETE')
        : InquiryAPI.updateInquiry(this.inquiry_id, params)

      return request.then(() => {
        this.popoverVisible = false
        this.$message({
          message: 'Update successful',
          type: 'success',
        })
        this.$router.push({ name: 'ProjectScreeningQuestion', params: { project_id: this.project_id } })
      }, () => {
      })
        .finally(() => {
          this.submitting = false
        })
    },

    checkTitleBeforeSave() {
      if (!this.new_title) {
        this.$notify({
          title: 'Warn', message: 'Please enter title', type: 'warning',
        })
        return false
      } else {
        return true
      }
    },

    formatQuestions() {
      const result_list = _.cloneDeep(this.questionForm)
        .map((item, index) => {
          item.sort_order = index
          item.display_order = index + 1
          if (['RADIO', 'CHECKBOX'].includes(item.type)) {
            item.options = item.options.map(i => {
              i.text = i.value || ''
              return i
            })
          }
          delete item.edit
          delete item.title_text
          // 兼容旧数据 替换 粘贴进来的部分样式
          item.title = item.title.trim().replace(/<p/g, '<div')
            .replace(/<\/p>/g, '</div>')

          return item
        })

      const fixed_questions = this.fixedQuestions.map((item, index) => ({
        title: item.title,
        type: item.type,
        id: item.id,
        tags: item.tags,
        sort_order: this.questionForm.length + index,
        display_order: this.questionForm.length + index + 1,
      }))

      return [...result_list, ...fixed_questions]
    },

    moveDown(index) {
      const arr = this.questionForm
      arr[index] = arr.splice(index + 1, 1, arr[index])[0]
    },

    moveUp(index) {
      const arr = this.questionForm
      arr[index] = arr.splice(index - 1, 1, arr[index])[0]
    },

  },
}

</script>

<style lang="scss" scoped>
.w-1400 {
  max-width: 1400px;
}

.title-box {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}

.title-label {
  white-space: nowrap;
  font-size: 14px;
  line-height: 28px;
  margin-right: 10px;
}

.sort-class {
  height: 28px;
  line-height: 28px;
  font-weight: bold;
  margin-right: 5px;
}

::v-deep .mce-menubar {
  border: 0;
}

::v-deep .mce-statusbar .mce-container-body {
  display: none;
}

.option-item{
  display: flex;
  flex: 1;
  align-items: flex-start;
  margin-right: 4px;

  .desc{
    margin-top: 4px;
    position: relative;
    }

  .required:before {
        position: absolute;
        font-size: 16px;
        top: -2px;
        left: -8px;
        content: "*";
        color: #f56c6c;
  }

  ::v-deep .el-radio {
    margin-right: 8px;
  }

  ::v-deep .el-radio__input.is-disabled+span.el-radio__label {
    display: none;
  }

  ::v-deep .el-checkbox:last-of-type{
    margin-right: 8px;
  }
}
</style>
