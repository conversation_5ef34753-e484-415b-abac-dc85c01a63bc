<template>
  <div class="mb-3">
    <el-card>
      <div slot="header" class="flex justify-content-between">
        <div><b>Cost Of Revenue</b></div>
        <refresh-button @action="init" />
      </div>

      <el-table
        v-loading="cost_loading"
        :data="paymentTableData"
        style="width: 100%"
      >
        <el-table-column
          label="Task ID"
          width="90"
          prop="id"
        >
          <template slot-scope="{row}">
            <router-link
              v-if="!row.isEditing"
              class="text-truncate link"
              :to="getRouterInfo(row)"
            > {{ row.task_id }}
            </router-link>
            <task-id-search v-else v-model="row.task_id" :init-list="task_id_list" />
          </template>
        </el-table-column>

        <el-table-column
          label="Type"
          width="120"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <span v-if="row.isPayment">{{ row.type }}</span>
              <span v-else>{{ row.type | getLabel(options.typesForShow) }}</span>
            </span>
            <el-select v-else v-model="row.type">
              <el-option
                v-for="item in options.payableTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          label="Expert"
          width="150"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              <router-link-advisor v-if="row.isPayment || row.advisor_id" :data="row.advisor" />
            </span>
            <span v-else>
              <task-advisor-search
                :project-id="$route.params.project_id"
                :extra-option="[{id:0,label:'Not Applicable',value:'Not Applicable'}]"
                @search="query=>handleSelectedAdvisor(query, row)"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Vendor"
          width="120"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              {{row.vendor_name}}
            </span>
            <span v-else>
              <el-input
                v-model="row.vendor_name"
                placeholder="vendor name"
                class="mt-2px" />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Transaction Date"
          align="right"
          width="150"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">
              {{ row.date_show }}
            </span>
            <el-date-picker
              v-else
              v-model="row.date"
              type="date"
              size="mini"
              placeholder="Transaction Date"
              value-format="yyyy/MM/dd"
              format="MM/dd/yyyy"
              class="filter-item date-picker"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="Payable Amount(USD)"
          width="170"
        >
          <template #default="{row}">
            <amount :data="row" />
          </template>
        </el-table-column>
        <el-table-column
          label="Payment Amount (Non-USD)"
          width="200"
        >
          <template #default="{row}">
            <span v-if="row.currency !== 'USD'">
              {{ row.amount }}  {{row.currency}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Notes"
          width="270"
        >
          <template #default="{row}">
            <span v-if="!row.isEditing">{{ row.notes }}</span>
            <el-input v-else v-model="row.notes" :class="{'need-notes':needNotes(row)}" />
          </template>
        </el-table-column>
        <el-table-column
          label="Profit"
          width="80"
        >
          <template #default="{row}">
            <span v-if="row.task && row.task.p_l_profit">{{ (row.task.p_l_profit * 100).toFixed(2) }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          label="Actions"
          class="flex align-items-center"
        >
          <template #default="{row}">
            <el-tooltip
              effect="dark"
              :enterable="false"
              content="Complete again"
              placement="top"
            >

              <el-link
                v-if="row.isPayment && !row.isEditing"
                type="primary"
                :underline="false"
                icon="el-icon-finished"
                @click="goToTaskComplete(row)"
              />
            </el-tooltip>
            <el-button
              v-if="showEditButtons(row)"
              type="primary"
              size="mini"
              @click="row.isEditing=true"
            >
              Edit
            </el-button>

            <el-popconfirm
              v-if="showEditButtons(row)"
              width="220"
              confirm-button-text="Yes, Delete"
              cancel-button-text="No, Cancel"
              title="Are you sure you want to delete this transaction?"
              @confirm="deletePayable(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="mini"
                >
                  Delete
                </el-button>
              </template>
            </el-popconfirm>

            <el-button
              v-if="row.isEditing"
              type="success"
              size="mini"
              :disabled="saveDisabled(row)"
              :loading="row.save_loading"
              @click="savePayableRow(row)"
            >
              Save
            </el-button>

            <el-button
              v-if="row.isEditing"
              type="info"
              size="mini"
              @click="cancelEditPayableRow(row)"
            >
              Cancel
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <div class="pl-total-box">
          <div class="pl-label">Subtotal</div>
          <div class="pl-amount">
            <amount :data="{'amount_us':total_payment_amount}" />
          </div>
          <el-button
            v-if="editPermission"
            type="success"
            class="float-right"
            icon="el-icon-plus"
            @click="addPayableTransaction()"
          >
            Add Payable Transaction
          </el-button>

        </div>
      </div>
    </el-card>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import PaymentAPI from '@/api/payment'
import Mixin from './mixin'
import moment from 'moment/moment'
import ProjectAPI from '@/api/project'

export default {
  name: 'CostOfRevenue',
  mixins: [Mixin],
  props: {
    cost: [Number, String],
    editPermission: {
      type: Boolean,
      default: false,
    },
    list: Array,
  },
  data() {
    return {
      loading: false,
      cost_loading: false,

      payments: [],
      payables: [],
      total_payment_amount: undefined,
      paymentTableData: [],

      task_id_list: [],
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
      McKinseyId: state => state.app.McKinseyId,
    }),
  },

  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.loading = true
      return Promise.all([
        this.fetchPayments(),
        this.fetchPayables(),
      ])
        .then(() => {
          this.calcPaymentTableData()
        })
        .finally(() => {
          this.loading = false
        })
    },

    needNotes(row) {
      return row.type === 'OTHER_PAYABLES' && !row.notes
    },

    saveDisabled(row) {
      return this.needNotes(row) || !row.type
    },

    showEditButtons(row) {
      return !row.isPayment && this.editPermission && !row.isEditing
    },

    fetchPayments() {
      this.cost_loading = true
      const params = {
        project_id: this.projectInfo.id,
        project_tsid: this.projectInfo.tsid,
        extra: 'task,advisor,project,amount_in_usd',
        page: 1,
        size: 999,
        sort: 'task_date asc',
      }
      return PaymentAPI.getPaymentForPL(params)
        .then(data => {
          this.payments = data.list
        })
        .finally(() => {
          this.cost_loading = false
        })
    },

    fetchPayables() {
      const params = {
        project_id: this.$route.params.project_id,
        page: 1,
        size: 99,
        extra: 'project,task,advisor,task.p_l_profit',
        sort: 'transaction_date asc',
      }
      return ProjectAPI.getPayableTransactions(params)
        .then(data => {
          this.payables = data.list
        })
    },

    calcPaymentTableData() {
      const standardPayments = this.payments.map(it => {
        return {
          isPayment: true,
          isEditing: false,
          save_loading: false,
          task_id: it.task.id,
          type: it.project.sub_type,
          advisor: it.advisor,
          vendor_name: it.vendor_name || '',
          advisor_id: it.advisor.id,
          date: moment(it.task_date).format('yyyy/MM/DD'),
          date_show: moment(it.task_date).format('MM/DD/yyyy'),
          amount: it.amount,
          amount_us: it.currency !== 'USD' ? it.amount_in_usd : it.amount,
          currency: it.currency,
          notes: it.remark1,
          payment_amount_non_usd: '',
          project: it.project,
          task: it.task,
        }
      },
      )
      const payables = this.payables.map(it => {
        return Object.assign({
          isPayment: false,
          isEditing: false,
          save_loading: false,
          delete_loading: false,
          elected_advisor_id: '',
          date: moment(it.transaction_date).format('yyyy/MM/DD'),
          date_show: moment(it.transaction_date).format('MM/DD/yyyy'),
          amount_us: it.amount,
        }, it)
      })
      this.paymentTableData = standardPayments.concat(payables)
      this.task_id_list = standardPayments
      this.calcPayment()
    },

    calcPayment() {
      this.total_payment_amount = this.paymentTableData.reduce((total, row) => total + row.amount_us, 0)
      this.$emit('update:cost', this.total_payment_amount)
      this.$emit('update:list', this.paymentTableData)
    },

    deletePayable(row) {
      row.delete_loading = true
      return ProjectAPI.deletePayableTransaction(row.id)
        .then(() => {
          const delete_index = this.paymentTableData.findIndex(item => item.id === row.id)
          this.paymentTableData.splice(delete_index, 1)
          this.calcPayment()
        })
        .finally(() => {
          row.delete_loading = false
        })
    },

    addPayableTransaction() {
      const row = {
        isPayment: false,
        isEditing: true,
        save_loading: false,
        delete_loading: false,
        task_id: 0,
        type: '',
        selected_advisor_id: '',
        date: moment().format('yyyy/MM/DD'),
        amount_us: 0,
        notes: '',
        vendor_name: '',
      }
      this.paymentTableData.push(row)
    },

    savePayableRow(row) {
      row.save_loading = true
      const data = {
        id: row.id,
        project_id: parseInt(this.$route.params.project_id),
        task_id: row.task_id,
        type: row.type,
        advisor_id: row.selected_advisor_id,
        transaction_date: row.date,
        amount: row.amount_us,
        notes: row.notes,
        vendor_name: row.vendor_name,
      }

      const promise = row.id > 0 ? ProjectAPI.updatePayableTransaction(row.id, data)
        : ProjectAPI.savePayableTransaction(data)

      promise.then((data) => {
        row.new_id = data.id
        this.fetchPayables()
          .then(() => {
            this.calcPaymentTableData()
          })
      })
        .finally(() => {
          row.save_loading = false
        })
    },
    cancelEditPayableRow(row) {
      const index = this.paymentTableData.indexOf(row)

      if (row.id > 0) {
        const origin_row = this.payables.filter(item => item.id === row.id) || {}
        Object.assign(row, {
          isPayment: false,
          isEditing: false,
          save_loading: false,
          delete_loading: false,
          elected_advisor_id: '',
          date: moment(origin_row.transaction_date).format('yyyy/MM/DD'),
          amount_us: origin_row.amount,
        }, origin_row)
      } else {
        if (index !== -1) {
          this.paymentTableData.splice(index, 1)
        }
      }
    },

    handleSelectedAdvisor(selected_id, row) {
      row.selected_advisor_id = selected_id === 0 ? 0 : selected_id.split('-')[2]
    },

  },
}
</script>
<style scoped lang="scss">
.mt-2px{
  margin-top:2px
}

.need-notes{
  display: flex;
  ::v-deep .el-input__inner {
    border: 1px solid #f56c6c;
  }
}

.need-notes:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.float-right{
  float: right;
}

.date-picker {
  width: 140px;
}
</style>
