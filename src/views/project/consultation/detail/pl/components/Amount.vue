<template>
  <div>
    <template v-if="!data.isEditing">
      <div class="amount" :class="{'danger':+data.amount_us<0}">
        <div>
          $
        </div>
        <div>
          <span v-if="+data.amount_us>=0">{{Number(data.amount_us).toFixed(2)}}</span>
          <span v-else>({{Math.abs(data.amount_us).toFixed(2)}})</span>
        </div>
      </div>
    </template>
    <template v-else>
      <el-input-number v-if="data.type==='CLIENT_REBATE_DISCOUNT'" v-model="edit_amount" :max="0" @change="data.amount_us=edit_amount" />
      <el-input-number v-else v-model="edit_amount" :min="0" @change="data.amount_us=edit_amount" />
    </template>
  </div>
</template>

<script>
export default {
  name: 'Amount',
  props: {
    data: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      edit_amount: this.data.amount_us,
    }
  },
}
</script>

<style scoped>
.amount {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}
</style>
