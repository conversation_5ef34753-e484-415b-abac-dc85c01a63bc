<template>
  <el-select
    v-bind="$attrs"
    filterable
    clearable
    placeholder="Task ID"
    remote
    :remote-method="searchTaskList"
    v-on="$listeners"
  >
    <el-option
      label="Not Applicable"
      :value="0"
    />
    <el-option
      v-for="item in task_list"
      :key="item.id"
      :label="item.id"
      :value="item.id"
    />

  </el-select>
</template>

<script>
import ProjectAPI from '@/api/project'
import _ from 'lodash'

export default {
  name: 'TaskIdSearch',
  props: {
    initList: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      task_list: [],
    }
  },
  mounted() {
    this.task_list = _.uniqBy(this.initList.map(item => ({ id: item.task_id })), 'id')
  },
  methods: {
    searchTaskList(val) {
      const params = {
        project_id: this.$route.params.project_id,
        params: {
          page: 1,
          size: 20,
          general_status: 'COMPLETED',
          id_starts_with: val || undefined,
        },
      }
      return ProjectAPI.getTasksDetail(params)
        .then(data => {
          this.task_list = data.list
        })
    },
  },
}
</script>

<style scoped>

</style>
