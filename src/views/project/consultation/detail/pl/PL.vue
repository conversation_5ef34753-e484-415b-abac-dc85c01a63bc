<template>
  <el-card class="mt-3">
    <div slot="header">
      <b>P&L</b>
    <div class="pl-total-box mb-3" style="margin-top:5px">
      <div class="pl-label">Total</div>
      <div class="pl-amount">
        <amount :data="{'amount_us':total.pl}" />
      </div>
      <div v-if="revenue" class="inline-block" style="margin-left:100px">{{total.percentage}}%</div>
    </div>
    <div v-if="selection.length" class="mb-3">
      <el-button
        type="primary"
        class="filter-item"
        icon="el-icon-download"
        :loading="download_loading"
        @click="downloadUsageReport">Download Usage
      </el-button>
      <template-download-usage :data-list.sync="download_tab" :option-list="available_fields" />
    </div>
    </div>
    <el-table
    v-if="!loading"
    v-loading="loading"
    :data="table_data"
    style="width: 100%"
    @selection-change="handleSelectionChange"
    >
    <el-table-column v-if="has_download_permission" type="selection" width="40" />
    <el-table-column
      label="Task ID"
      width="100"
      prop="id"
    >
      <template slot-scope="{row}">
        <router-link
          class="text-truncate link"
          :to="getRouterInfo(row)"
        > {{ row.task_id }}
        </router-link>
      </template>
    </el-table-column>

    <el-table-column
      label="Type"
      width="140"
    >
      <template #default="{row}">
        <span v-if="row.isRevenue">{{ row.type }}</span>
        <span v-else>{{ row.type | getLabel(options.typesForShow) }}</span>
      </template>
    </el-table-column>
    <el-table-column
      label="Expert"
      width="140"
    >
      <template #default="{row}">
       <router-link-advisor :data="row.advisor" />
      </template>
    </el-table-column>
    <el-table-column
      label="Transaction Date"
      align="right"
      width="120"
    >
      <template #default="{row}">{{ row.date_show }}</template>
    </el-table-column>
    <el-table-column
      label="Receivable Amount(USD)"
      width="170"
      prop="revenue"
    >
      <template slot-scope="{row}">
        <span v-if="+row.revenue>=0">$ {{Number(row.revenue).toFixed(2)}}</span>
        <span v-else class="danger">$ ({{Math.abs(row.revenue).toFixed(2)}})</span>
      </template>
    </el-table-column>
    <el-table-column
      label="Payable Amount(USD)"
      width="170"
      prop="cost">
      <template slot-scope="{row}">
        <span v-if="+row.cost>=0">$ {{Number(row.cost).toFixed(2)}}</span>
        <span v-else class="danger">$ ({{Math.abs(row.cost).toFixed(2)}})</span>
      </template>
    </el-table-column>
    <el-table-column
      label="$ Margin"
      width="80"
      prop="profit"
    />
    <el-table-column
      label="% Margin"
      width="80"
      prop="profit_margin"
    >
      <template slot-scope="{row}">
      <span v-if="row.task && row.task.p_l_profit">{{ (row.task.p_l_profit * 100).toFixed(2) }}%</span>
      <span v-else>-</span>
    </template>
    </el-table-column>
    <el-table-column
      label="Client Hours"
      width="100"
    >
      <template #default="{row}">
        {{row.client_hours}}
      </template>
    </el-table-column>
    <el-table-column
      label="Notes"
      width="270"
      prop="notes"
   />
  </el-table>
  </el-card>
</template>

<script>
import Mixin from './mixin'
import TemplateDownloadUsage from '@/components/ClientDownloadUsage/index'
import _ from 'lodash'
import ProjectAPI from '@/api/project'
import { mapState } from 'vuex'

export default {
  name: 'PL',
  components: { TemplateDownloadUsage },
  mixins: [Mixin],
  props: {
    costList: Array,
    revenueList: Array,
    revenue: [Number, String],
    cost: [Number, String],
  },
  data() {
    return {
      loading: false,
      download_loading: false,
      table_data: [],
      selection: [],
      download_tab: [],
      available_fields: undefined,
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    total() {
      return {
        pl: (this.revenue - this.cost).toFixed(2),
        percentage: (((this.revenue - this.cost) / this.revenue) * 100).toFixed(2),
      }
    },
  },
  watch: {
    'costList': {
      handler() {
        this.formatData()
      },
    },
    'revenueList': {
      handler() {
        this.formatData()
      },
    },
  },

  methods: {
    // 将收入与支出数据合并，没有task id 的数据不合并
    formatData() {
      this.table_data = []
      this.loading = true
      const format_revenue = this.revenueList.map(item => {
        item.orgin_type = 'revenue'
        return item
      })

      const list = []
      const merged_array = _.cloneDeep([...format_revenue, ...this.costList])
      merged_array.forEach(item => {
        if (!item.task_id || !list.find(i => i.task_id === item.task_id)) {
          if (item.task_id) {
            const revenue_group = format_revenue.filter(i => i.task_id === item.task_id)
            const cost_group = this.costList.filter(i => i.task_id === item.task_id)
            item.notes = revenue_group.concat(cost_group).filter(i => i.notes).map(i => i.notes).join('\n')
            item.client_hours = _.sumBy(revenue_group, 'client_hours')
            item.revenue = _.sumBy(revenue_group, 'amount_us')
            item.cost = _.sumBy(cost_group, 'amount_us')
          } else {
            item.revenue = item.orgin_type === 'revenue' ? item.amount_us : 0
            item.cost = item.orgin_type === 'revenue' ? 0 : item.amount_us
          }
          item.profit = item.revenue - item.cost
          list.push(item)
        }
      })

      this.$nextTick(() => {
        this.table_data = list
      })
      this.loading = false
    },

    async handleSelectionChange(val) {
      if (!this.available_fields) {
        await this.getAvailableFields()
      }
      this.selection = val
      if (val.length) {
        this.download_tab = this.available_fields.filter(item => item.isStandard).map(item => item.name)
      } else {
        this.download_tab = []
      }
    },

    downloadUsageReport() {
      const params = {
        revenue_ids: this.selection.map(item => item.id)
          .join(','),
        zone_id: this.timeZone,
        columns: this.download_tab.length ? this.download_tab.join() : undefined,
        receivable_transaction_ids: '',
      }
      this.download_loading = true
      return ProjectAPI.downloadRevenue(params)
        .finally(() => {
          this.download_loading = false
        })
    },

    getAvailableFields() {
      return ProjectAPI.getAvailableFields()
        .then(data => {
          this.available_fields = data
        })
    },
  },
}
</script>
