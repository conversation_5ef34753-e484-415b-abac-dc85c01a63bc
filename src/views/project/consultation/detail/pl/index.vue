<template>
  <div>
    <revenue :revenue.sync="revenue" :list.sync="list.revenue" :edit-permission="permissionEdit" />
    <cost-of-revenue :cost.sync="cost" :list.sync="list.cost" :edit-permission="permissionEdit" />
    <p-l :cost="cost" :revenue="revenue" :cost-list="list.cost" :revenue-list="list.revenue" />
  </div>
</template>

<script>
import Revenue from './Revenue'
import CostOfRevenue from '@/views/project/consultation/detail/pl/CostOfRevenue'
import PL from './PL'
import { mapGetters } from 'vuex'

export default {
  name: 'index',
  components: { Revenue, CostOfRevenue, PL },
  data() {
    return {
      list: {
        revenue: [],
        cost: [],
      },
      revenue: 0,
      cost: 0,
    }
  },
  computed: {
    ...mapGetters([
      'roles',
    ]),
    permissionEdit() {
      return this.roles.some(item => {
        return ['Admin', 'Edit_P_and_L'].includes(item.role.name)
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-button{
  margin: 2px;
}
</style>
