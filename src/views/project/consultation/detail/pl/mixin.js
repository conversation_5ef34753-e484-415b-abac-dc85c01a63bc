import TaskAdvisorSearch from '@/views/project/consultation/detail/components/TaskAdvisorSearch/index.vue'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import Amount from '@/views/project/consultation/detail/pl/components/Amount'
import TaskIdSearch from '@/views/project/consultation/detail/pl/components/TaskIdSearch'
import RefreshButton from '@/components/RefreshButton/index.vue'
import _ from 'lodash'
import { mapGetters } from 'vuex'

export default {
  components: { RefreshButton, RouterLinkAdvisor, TaskAdvisorSearch, Amount, TaskIdSearch },
  data() {
    return {
      options: {
        receivableTypes: [
          {
            label: 'Consultation',
            value: 'CONSULTATION',
          },
          {
            label: 'Written Follow Up',
            value: 'WRITTEN_FOLLOW_UP',
          },
          {
            label: 'Follow up Consultation',
            value: 'FOLLOW_UP_CONSULTATION',
          },
          {
            label: 'Investor Call',
            value: 'INVESTOR_CALL',
          },
          {
            label: 'Vetting Call',
            value: 'VETTING_CALL',
          },
          {
            label: 'Outsourced Patient Receivables',
            value: 'OUTSOURCED_PATIENT_RECEIVABLES',
          },
          {
            label: 'Platform Receivables',
            value: 'PLATFORM_RECEIVABLES',
          },
          {
            label: 'Survey Programming Receivables',
            value: 'SURVEY_PROGRAMMING_RECEIVABLES',
          },
          {
            label: 'Outsourced Survey Panel Receivables',
            value: 'OUTSOURCED_SURVEY_PANEL_RECEIVABLES',
          },
          {
            label: 'Transcription Receivables',
            value: 'TRANSCRIPTION_RECEIVABLES',
          },
          {
            label: 'Moderator Receivables',
            value: 'MODERATOR_RECEIVABLES',
          },
          {
            label: 'Translation Receivables',
            value: 'TRANSLATION_RECEIVABLES',
          },
          {
            label: 'Client Rebate/Discount',
            value: 'CLIENT_REBATE_DISCOUNT',
          },
          {
            label: 'Other',
            value: 'OTHER',
          },
        ],
        payableTypes: [
          {
            label: 'Consultation',
            value: 'CONSULTATION',
          },
          {
            label: 'Written Follow Up',
            value: 'WRITTEN_FOLLOW_UP',
          },
          {
            label: 'Follow up Consultation',
            value: 'FOLLOW_UP_CONSULTATION',
          },
          {
            label: 'Investor Call',
            value: 'INVESTOR_CALL',
          },
          {
            label: 'Vetting Call',
            value: 'VETTING_CALL',
          },
          {
            label: 'Outsourced Patient Payables',
            value: 'OUTSOURCED_PATIENT_PAYABLES',
          },
          {
            label: 'Outsourced Survey Panel Payables',
            value: 'OUTSOURCED_SURVEY_PANEL_PAYABLES',
          },
          {
            label: 'Transcription Payables',
            value: 'TRANSCRIPTION_PAYABLES',
          },
          {
            label: 'Moderator Payables',
            value: 'MODERATOR_PAYABLES',
          },
          {
            label: 'Translation Payables',
            value: 'TRANSLATION_PAYABLES',
          },
          {
            label: 'Other Payables',
            value: 'OTHER_PAYABLES',
          },
        ],
        typesForShow: [
          {
            label: 'Patient',
            value: 'PATIENT',
          },
          {
            label: 'Survey Respondent',
            value: 'SURVEY',
          },
        ],
      },
    }
  },

  computed: {
    ...mapGetters([
      'roles',
    ]),

    has_download_permission() {
      return this.roles.some(
        item => ['P&L_Revenue', 'Admin'].includes(item.role.name))
    },
  },

  mounted() {
    this.options.typesForShow = _.unionBy(this.options.typesForShow.concat(this.options.payableTypes).concat(this.options.receivableTypes), 'value')
  },
  methods: {
    goToTaskComplete(item) {
      let router_name
      switch (item.project.sub_type) {
        case 'Survey':
          router_name = 'SurveyTaskComplete'
          break
        case 'Investor Call':
          router_name = 'PatientsTaskComplete'
          break
        default:
          router_name = 'TaskComplete'
      }
      this.$router.push({
        name: router_name,
        params: { project_id: item.project.id },
        query: { id: item.task.id },
      })
    },

    getRouterInfo(item) {
      if (!item.task) return ''
      if (item.task.angle_id) {
        let router_name = 'ProjectTasks'
        if (item.project.sub_type ===
        'Investor Call') router_name = 'ProjectClientTasksSST'
        if (item.project.client_id ===
        this.McKinseyId) router_name = 'ProjectClientTasksMcK'
        return {
          name: router_name,
          params: { project_id: item.project.id },
          query: {
            task_id: item.task.id,
            angle_id: item.task.angle_id.toString(),
          },
        }
      } else {
        return {
          name: 'ProjectLeads',
          params: { project_id: item.project.id },
          query: { task_id: item.task.id },
        }
      }
    },
  },
}
