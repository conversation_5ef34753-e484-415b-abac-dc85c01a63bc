<template>
  <div class="inline-block">
    <el-button
      type="primary"
      icon="el-icon-copy-document"
      @click="openDialog">
      Copy Leads
    </el-button>

    <el-dialog
      :visible.sync="dialogVisible"
      title="Copy Leads"
      width="480px"
      @open="initDialog"
      @opened="autoFocus">

      <el-form>
        <el-form-item>
          <el-radio-group v-model="form.isCopySelected">
            <el-radio :label="true" border :disabled="!selectedTasks.length">Copy <b>Selected</b> Leads</el-radio>
            <el-radio :label="false" border>Copy <b>All</b> Leads</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-select
            ref="select"
            v-model="form.toProjectId"
            filterable
            clearable
            :filter-method="getProjectList"
            remote
            reserve-keyword
            placeholder="Search Target Project"
            no-data-text="No matching data"
            class="w-100"
            :loading="loading">
            <el-option
              v-for="item in options.project"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="currentProjectId === item.id">
              <span>{{ `[${item.id}]  ${item.name}` }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <router-link
        v-if="form.toProjectId"
        target="_blank"
        :to="{ name: 'ProjectLeads', params: { project_id: form.toProjectId } }"
        class="text-truncate link f-14">
        <i class="el-icon-link" /> View Target Project
      </router-link>

      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :disabled="!form.toProjectId" :loading="submitting" @click="handleCopy">
          Copy
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: 'CopyLeadsToProject',
  props: {
    selectedTasks: Array,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      submitting: false,

      form: {
        isCopySelected: false,
        toProjectId: null,
      },
      options: {
        project: [],
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    currentProjectId() {
      return +this.$route.params.project_id
    },
  },
  methods: {
    openDialog() {
      this.dialogVisible = true
      this.getProjectList()
    },
    initDialog() {
      this.form.isCopySelected = !!this.selectedTasks.length
    },
    autoFocus() {
      if (this.form.toProjectId) return
      this.$nextTick(() => {
        this.$refs.select.focus()
      })
    },
    getProjectList(val) {
      const params = {
        name_like: val || undefined,
        member_ids: this.uid,
        type: 'CONSULTATION',
        sort: 'id desc',
        extra: 'angles',
      }

      this.loading = true
      return ProjectAPI.getProjectList(params)
        .then(data => {
          this.options.project = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleCopy() {
      const params = {
        to_project_id: this.form.toProjectId,
      }
      if (this.form.isCopySelected) {
        params.task_ids = this.selectedTasks.map(item => item.id)
      } else {
        params.clone_all_leads_from_project_id = this.currentProjectId
      }

      this.submitting = true
      return ProjectAPI.copyTasksToProject(params)
        .then(() => {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: `Copy successfully!
                <a  href="#/project/consultation/${this.form.toProjectId}/leads" class="text-truncate link f-14" target="_blank">
                    <i data-v-352b6e86="" class="el-icon-link"></i>
                    View Target Project
                </a>`,
            type: 'success',
            duration: 5000,
          })
          this.dialogVisible = false
          this.form.toProjectId = null
        })
        .finally(() => {
          this.submitting = false
        })
    },
  },
}
</script>

<style scoped>

</style>
