<template>
  <div class="leads">
    <el-row
      type="flex"
      justify="space-between"
      align="top"
    />

    <div
      ref="tool_switch_box"
      class="position-relative tool-switch">
      <transition name="fade">
        <div
          v-if="!selectedAdvisor.length"
          :key="1"
          ref="tool_switch_page1"
          class="inline-block filter-container tool-switch-page1">
          <refresh-button class="filter-item" @action="reloadList" />
          <el-input
            v-model="searchQuery['ids']"
            placeholder="Task ID"
            class="inline-block filter-item w-150px"
            clearable
            @input="handleSearch" />
          <el-input
            v-model="searchQuery['advisor.full_name_contains']"
            placeholder="Name"
            class="inline-block filter-item w-150px"
            clearable
            @input="handleSearch" />
          <el-input
            v-model="searchQuery['company_name_contains']"
            :placeholder="searchQuery['company_is_contains_mode'] ? 'Keyword Contains' : 'Employer'"
            class="filter-item w-200px keyword-custom-input"
            clearable
            @input="handleSearch"
          >
            <el-checkbox
              slot="append"
              v-model="searchQuery['company_is_contains_mode']"
              @input="handleSearch"
            >
              <el-icon name="bottom" />
            </el-checkbox>
          </el-input>
          <div class="inline-block" style="vertical-align: top">
            <el-select
              v-model="searchQuery['company_is_current']"
              class="w-150px filter-item"
              @change="handleSearch">
              <el-option label="Currents" :value="true" />
              <el-option label="Formers" :value="false" />
              <el-option label="Currents and Formers" :value="undefined" />
            </el-select>
            <el-input
              v-model="searchQuery['advisor_profile.position_contains']"
              placeholder="Title"
              class="inline-block filter-item w-150px"
              clearable
              @input="handleSearch" />
            <el-input
              v-model="searchQuery['advisor.contact_infos.value_contains']"
              placeholder="Contact"
              class="inline-block filter-item input-with-select"
              clearable
              @input="handleSearch">
              <el-select
                slot="prepend"
                v-model="searchQuery['advisor.contact_infos.type']"
                placeholder="Select"
                @change="handleSearch">
                <el-option label="Email" value="EMAIL" />
                <el-option label="Phone" value="PHONE" />
                <el-option label="Linkedin" value="LINKEDIN_URL" />
              </el-select>
            </el-input>
            <br>
            <el-checkbox
              v-if="searchQuery['company_is_current']===false"
              v-model="searchQuery.should_exclude_current_company_matched"
              class="filter-item"
              @change="handleSearch">
              Exclude experts with the same current and former employer
            </el-checkbox>
          </div>
          <el-select
            v-model="searchQuery['advisor.status']"
            placeholder="Lead Status"
            class="inline-block filter-item w-150px"
            clearable
            filterable
            @change="handleSearch">
            <el-option
              v-for="item in advisor_options.status"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>

          <el-select
            v-model="searchQuery['create_by_id']"
            placeholder="Attached By"
            class="inline-block filter-item w-150px"
            clearable
            filterable
            @change="handleSearch">
            <el-option
              v-for="item in projectMemberOption"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
          <el-select
            v-model="searchQuery['task_status']"
            placeholder="Task Status"
            class="inline-block filter-item w-150px"
            clearable
            filterable
            @change="handleSearch">
            <el-option
              v-for="item in task_status_options"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
          <location
            v-model="searchQuery['advisor.location_ids']"
            region
            class="w-150px filter-item inline-block"
            @change="handleSearch"
          />
          <decipher-status-filter
            v-if="projectHasDecipherSurveyId"
            class="filter-item w-200px"
            @selected="(selectedStatuses)=> handleDecipherStatusFilter(selectedStatuses)" />
          <el-select
            v-model="blacklistFilter"
            class="filter-item w-200px"
            @change="getList">
            <el-option value="BLACKLIST" label="Exclude blacklisted advisors" />
            <el-option value="DECLINED" label="Exclude declined advisors" />
            <el-option value="B&D" label="Exclude blacklisted & declined advisors" />
            <el-option value="" label="All Advisors" />
          </el-select>
          <add-lead
            :project-id="projectId"
            :project="project"
            class="filter-item"
            @update="reloadList"
          />
          <copy-leads-to-project class="filter-item" :selected-tasks="selectedAdvisor" />
          <outreach-all
            class="filter-item"
            :tasks.sync="selectedAdvisor"
            :project="project"
            @update="reloadList" />
          <el-button class="filter-item" type="primary" icon="el-icon-download" @click="downloadTaskForLeads(true)">
            Download All
          </el-button>
          <decipher-update-btn
            v-if="isSurveyProject"
            class="filter-item"
            :project="project"
            @update-done="handleDecipherStatusUpdated" />
          <el-checkbox
            v-model="showAllColumns"
            border
            class="filter-item pull-right"
          >
            Show All Columns
          </el-checkbox>
        </div>
        <div
          v-else
          :key="2"
          ref="tool_switch_page2"
          v-loading="leadSelectLoading"
          class="inline-block filter-container tool-switch-page2">
          <refresh-button class="filter-item" @action="reloadList" />
          <outreach-to-advisor
            class="filter-item"
            :tasks.sync="selectedAdvisor"
            :project="project"
            @update="reloadList" />
          <email-to-advisor
            v-if="!isSurveyProject"
            class="filter-item"
            :tasks.sync="selectedAdvisor"
            :project="project"
            type="Leads"
            @update="reloadList" />
          <add-to-client-task class="filter-item" :tasks.sync="selectedAdvisor" @done="getList" />
          <copy-leads-to-project class="filter-item" :selected-tasks="selectedAdvisor" />
          <attach-to-project
            v-if="editPermission"
            class="filter-item"
            :selected-advisors="selectedAdvisor"
            :project-id="project.id"
            type="leads"
            @done="removeLeads"
          />
          <remove-task
            class="filter-item"
            :selected-advisors="selectedAdvisor"
            @done="getList"
          />
          <el-button class="filter-item" type="primary" icon="el-icon-download" @click="downloadTaskForLeads()">Download
          </el-button>
        </div>
      </transition>
    </div>

    <!--监听项目event并提示未读数据-->
    <ProjectWebSocketAlert tab-key="leads_task_ids" />

    <div v-show="selectedAdvisor.length>0" class="inline-block mt-8"> Selected: {{ selectedAdvisor.length }}</div>
    <pagination
      class="custom-pagination"
      :total="tableData.count"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :page-sizes="customPageSize"
      @pagination="getList" />
    <el-table
      ref="advisorMultipleTable"
      key="project_advisor_table"
      v-loading="loading"
      row-key="id"
      class="stripe-table"
      :data="tableData.data"
      stripe
      fit
      :span-method="arraySpanMethod"
      :header-cell-class-name="tableHeaderCellClassName"
      :row-class-name="$options.filters.rowSetIndex"
      highlight-current-row
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData.data, $refs['advisorMultipleTable'],checkSelectable)"
      @sort-change="sortData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column min-width="25">
        <template #default>
          <i class="cursor-move el-icon-rank" />
        </template>
      </el-table-column>
      <el-table-column type="selection" min-width="45" :selectable="checkSelectable" />
      <el-table-column
        v-if="!isSurveyProject"
        label="ID"
        type="index"
        min-width="45" />
      <el-table-column
        v-if="showAllColumns"
        key="task_id"
        label="Task ID"
        prop="id"
        sortable="column"
        min-width="65" />
      <el-table-column prop="advisor_id" label="Advisor ID" min-width="65" />
      <el-table-column
        label="View"
        type="expand"
        min-width="45">
        <template slot-scope="scope">
          <div v-if="!scope.row.advisor.gdpr_delete_by_id" class="table-expand">
            <edit-task
              :type="scope.row.advisor.type"
              class="flex flex-wrap edit-task justify-content-center"
              :task.sync="scope.row"
              @update="reloadList" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSurveyProject"
        label="Mark"
        prop="willingness"
        sortable="column"
        min-width="60">
        <template slot-scope="scope">
          <mark-btn :data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="projectHasDecipherSurveyId"
        min-width="100">
        <template #header>
          Decipher Status
          <div :style="syncInfoDivStyle"> {{ decipherStatusSyncedStr }}</div>
        </template>
        <template slot-scope="{row: task}">
          <decipher-status-column
            :task="task" />
        </template>
      </el-table-column>
      <el-table-column label="Progress" width="120" class-name="overflow-visible">
        <template slot-scope="scope">
          <task-progress-v2
            task-type="leads"
            :data="scope.row"
            :project="project"
            @update="getSelectedTasksDetailForLeads(selectedAdvisor)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="Name"
        prop="advisor.full_name"
        sortable="column"
        column-key="collapse-start"
        min-width="138">
        <template slot-scope="scope">
          <el-tooltip
            :enterable="false"
            :open-delay="1000"
            content="Copy Task URL"
            effect="dark"
            placement="top"
          >
            <el-link
              :underline="false"
              class="copy-task-link"
              @click="copyTaskUrl(scope.row.id)">
              <svg-icon icon-class="location-arrow-solid" />
            </el-link>
          </el-tooltip>
          <USGovernmentMark :advisor="scope.row.advisor" />
          <span v-if="scope.row.advisor && !scope.row.advisor.gdpr_delete_by_id">
            <el-tooltip
              effect="dark"
              placement="top"
              :disabled="!scope.row.advisor_blocked_status.length"
            >
              <div slot="content">
                <span v-for="(status, index) in scope.row.advisor_blocked_status" :key="status">
                  <slot v-if="index">,</slot>
                  <slot v-if="status === 'ADVISOR'">Expert</slot><slot
                  v-if="status === 'CURRENT_JOB_COMPANY'">Current Company</slot><slot
                  v-if="status === 'LOCATION'">Location</slot>
                </span>
                is in this Client’s blacklist.
              </div>
              <router-link-advisor
                :title="scope.row.advisor.full_name"
                :data="scope.row.advisor"
                :class="{'is-blocked': scope.row.advisor_blocked_status.length}" />
            </el-tooltip>
            <NewAdvisorTag :advisor="scope.row.advisor" />
          </span>
          <span v-if="scope.row.advisor.gdpr_delete_by_id" class="warning pl-5">
            Expert Deleted by {{ scope.row.advisor.gdpr_delete_by.name }} at {{
              scope.row.advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A')
            }}.
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Location"
        min-width="120"
        prop="advisor.location.name"
        column-key="advisor-info"
        sortable="column">
        <template slot-scope="scope">
          <span v-if="scope.row.advisor.location_id">
            {{ scope.row.advisor.location.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Company"
        column-key="advisor-info"
        min-width="150">
        <template slot-scope="scope">
          <el-dropdown
            placement="bottom-start"
            trigger="click"
            @command="(val) => changeTaskJob(val, scope.row)"
          >
            <span class="dropdown-link">
              <slot
                v-if="
                  scope.row['advisor_profile'] &&
                    scope.row['advisor_profile'].company &&
                    scope.row['advisor_profile'].company.name
                "
                :title="scope.row['advisor_profile'].company.name"
              >
                <advisor-profile-tips
                  :advisor-profile="scope.row['advisor_profile']"
                  :advisor-jobs="scope.row.advisor.jobs" />
                {{ scope.row['advisor_profile'].company.name }}
              </slot>
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="job in scope.row.advisor.jobs"
                :key="job.id"
                :command="job"
              >
                <span v-if="job.company && job.company.name">{{ job.company.name }}</span> | {{ job.position }} |
                {{ job.start_date }} ~
                {{ job.is_current ? 'Current' : job.end_date }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column
        label="Title"
        prop="advisor_profile.position"
        sortable="column"
        column-key="advisor-info"
        min-width="140">
        <template slot-scope="scope">
          <el-popover
            :ref="`popover-${sequence}-${scope.$index}`"
            :append-to-body="true"
            placement="top"
            width="200">
            <el-form
              ref="form"
              :model="advisorProfileForm"
              inline
              size="mini">
              <el-form-item
                style="margin-right: 0;margin-bottom: 17px;"
                prop="position"
                :rules="{required: true, message: 'position is required', trigger: 'change'}">
                <el-input v-model="advisorProfileForm.position" />
              </el-form-item>
              <el-form-item
                style="margin-bottom: 0px;width: 100%;text-align: center;">
                <el-button
                  type="primary"
                  @click="changeTaskAdvisorProfilePosition(scope._self.$refs[`popover-${sequence}-${scope.$index}`], scope.row, advisorProfileForm)"
                >Save
                </el-button>
                <el-button
                  type="text"
                  @click="scope._self.$refs[`popover-${sequence}-${scope.$index}`].doClose()">Cancel
                </el-button>
              </el-form-item>
            </el-form>

            <span
              v-if="scope.row['advisor_profile']"
              slot="reference"
              class="cursor-pointer text-truncate"
              :title="scope.row['advisor_profile'].position"
              @click="advisorProfileForm.position = scope.row['advisor_profile'].position"
            >{{ scope.row['advisor_profile'].position }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        label="Contact"
        prop="contact"
        column-key="advisor-info"
        min-width="100">
        <template slot-scope="scope">
          <contact-tags :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="project.sub_type === 'Survey'"
        label="Survey Honorarium"
        min-width="80"
        sortable="column"
        prop="rate_currency asc,rate"
      >
        <template slot-scope="scope">
          <honorarium-edit :task="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSurveyProject"
        label="Status"
        prop="advisor.status"
        sortable="column"
        min-width="110">
        <template slot-scope="scope">
          <el-dropdown
            placement="bottom-start"
            trigger="click"
            @command="(val) => changeStatus(val, scope.row)"
          >
            <span class="dropdown-link">
              <slot>
                <span v-if="!scope.row.advisor.status">Lead</span>
                <span v-if="scope.row.advisor.status"
                >{{ scope.row.advisor.status | getLabel(advisor_options.status) }}</span>
              </slot>
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="option in advisor_options.status"
                :key="option.value"
                :command="option.value"
              >
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column v-if="isSurveyProject" label="Survey Link" min-width="80">
        <template slot-scope="scope">
          <survey-link-copy :task-data="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSurveyProject"
        label="Last Outreach">
        <template slot-scope="scope">
          <span v-if="scope.row.task_communication.length">
            {{ scope.row.task_communication[0].name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Last Outreach Time"
        sortable="column"
        prop="latest_task_communication_time"
        min-width="155">
        <template slot-scope="{row}">
          <communications-history v-if="row.latest_task_communication_time" :item="row" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSGDB && showAllColumns"
        key="verified"
        label="Verified"
        min-width="80">
        <template slot-scope="scope">
          <recent-outreach-status
            api-type="ListWise"
            :email-records="scope.row.advisor_email_list_wise_records"
            :email-status="scope.row.list_wise_email_status" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSGDB && showAllColumns"
        key="recent_email_status"
        label="Recent Email Status"
        min-width="80">
        <template slot-scope="scope">
          <recent-outreach-status
            api-type="SendGrid"
            :item="scope.row"
            :email-status="scope.row.most_recent_email_status" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSGDB && showAllColumns"
        key="opened"
        label="Opened"
        prop="outreach_opened"
        sortable="column"
        :sort-orders="['descending', null]"
        min-width="80">
        <template slot-scope="scope">
          <outreach-opened :item="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="showAllColumns"
        key="attached_by"
        label="Attached By"
        prop="create_by_id"
        sortable="column"
        min-width="110">
        <template slot-scope="scope">
          <span v-if="scope.row.create_user">{{ scope.row.create_user['name'] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showAllColumns"
        key="attached_date"
        label="Attached Date"
        prop="create_at"
        sortable="column"
        min-width="130">
        <template slot-scope="scope">
          <span>{{ scope.row['create_at'] | momentFormat('MM/DD/YYYY (HH:mm)') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isSurveyProject"
        label="Links"
        width="60">
        <template slot-scope="scope">
          <workflow-link
            v-if="!scope.row.advisor_blocked_status.length"
            :task-item="scope.row"
            :project="project"
            expert-type="Leads" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="tableData.count > 20"
      :total="tableData.count"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      :page-sizes="customPageSize"
      @pagination="getList" />
    <download-task ref="taskDownload" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { copyUtil, debounce, isEmpty } from '@/utils/tool'
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import AddLead from '@/views/project/components/AddLead'
import EmailToAdvisor from '@/views/project/consultation/detail/components/SendEmail/EmailToAdvisor_v2'
import AddToClientTask from '@/views/project/consultation/detail/components/AddToClientTask'
import MarkBtn from '@/views/project/consultation/detail/components/MarkBtn'
import AttachToProject from '@/views/project/consultation/detail/components/AttachToProject'
import ContactTags from '@/views/project/consultation/detail/components/ContactTags'
import OutreachToAdvisor from '@/views/project/consultation/detail/components/SendEmail/OutreachToAdvisor'
import OutreachAll from '@/views/project/consultation/detail/components/SendEmail/OutreachAll'
import RefreshButton from '@/components/RefreshButton'
import TaskMixins from '@/views/project/consultation/detail/mixins/task'
import SurveyDecipherMixin from '@/views/project/consultation/detail/mixins/survey-decipher-mixin'
import RemoveTask from '@/views/project/consultation/detail/components/RemoveTask'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import HonorariumEdit from '@/views/project/consultation/detail/components/TaskHonorariumEdit'
import SurveyLinkCopy from '@/views/project/consultation/detail/components/SurveyLinkCopy'
import CopyLeadsToProject from '@/views/project/consultation/detail/leads/components/CopyLeadsToProject'
import AdvisorProfileTips from '@/views/project/consultation/detail/components/AdvisorProfileTips'
import WorkflowLink from '@/views/project/consultation/detail/components/WorkflowLink'
import USGovernmentMark from '@/views/project/consultation/detail/components/USGovernmentMark'
import RecentOutreachStatus from '@/components/RecentEmailStatus/RecentOutreachStatus'
import OutreachOpened from '@/views/project/consultation/detail/components/Outreach/OutreachOpened'
import NewAdvisorTag from '@/components/NewAdvisorTag'
import DownloadTask from '@/views/project/consultation/detail/components/DownloadTask'
import DecipherUpdateBtn from '@/views/project/consultation/detail/components/Decipher/DecipherUpdateBtn.vue'
import DecipherStatusColumn from '@/views/project/consultation/detail/components/Decipher/DecipherStatusColumn'
import DecipherStatusFilter from '@/views/project/consultation/detail/components/Decipher/DecipherStatusFilter'
import CommunicationsHistory
  from '@/views/project/consultation/detail/components/TaskCommunications/CommunicationsHistory'
import ProjectWebSocketAlert from '@/components/WebSocket/unreadAlert'
import Location from '@/components/Location/index.vue'

export default {
  name: 'Leads',
  components: {
    DecipherStatusFilter,
    DecipherStatusColumn,
    DecipherUpdateBtn,
    RouterLinkAdvisor,
    AddLead,
    MarkBtn,
    ContactTags,
    AddToClientTask,
    EmailToAdvisor,
    OutreachToAdvisor,
    OutreachAll,
    RefreshButton,
    Pagination,
    RemoveTask,
    AttachToProject,
    HonorariumEdit,
    SurveyLinkCopy,
    CopyLeadsToProject,
    AdvisorProfileTips,
    WorkflowLink,
    USGovernmentMark,
    RecentOutreachStatus,
    OutreachOpened,
    NewAdvisorTag,
    DownloadTask,
    CommunicationsHistory,
    ProjectWebSocketAlert,
    Location,
  },
  mixins: [TaskMixins, SurveyDecipherMixin],
  data() {
    return {
      sessionKey: `${this.$route.name}-${this.$route.params.project_id}`,
      editPermission: false,
      customPageSize: [20, 30, 50, 100, 150, 200, 250, 300],
      projectId: this.$route.params.project_id,
      loading: false,
      leadSelectLoading: false,
      selectProjectDialog: false,
      showAllColumns: false,
      dialog: {
        selectLoading: false,
        selectedID: '',
        projectList: [],
      },
      sort: {
        user: '',
      },
      project: this.$store.state.project.data,
      selectedDecipherSurveyStatuses: [],
      blacklistFilter: '',
      select: 'EMAIL',
      searchQuery: {
        project_id: this.$route.params.project_id,
        should_exclude_current_company_matched: false,
        task_status: undefined,
        ids: undefined,
        page: 1,
        size: 50,
        angle_id_is_null: true,
        decipher_status_in: undefined,
        'advisor.location_ids': [],
        sort: 'rank asc',
        'advisor.status': undefined,
        'advisor.full_name_contains': undefined,
        'advisor_profile.position_contains': undefined,
        'advisor.contact_infos.type': 'EMAIL',
        'advisor.contact_infos.value_contains': undefined,
        'advisor.or-0.status_not_in': undefined,
        'advisor.or-1.status_is_null': undefined,
        'advisor_decline.or-0.id_is_null': undefined,
        'advisor_decline.or-1.is_valid': undefined,
        'create_by_id': undefined,
        'company_name_contains': '',
        'company_is_contains_mode': false,
        'company_is_current': undefined,
        extra: [
          'advisor.current_job',
          'advisor.jobs.company',
          'advisor.email_address_score',
          'advisor.email_validation_records',
          'advisor_profile.company',
          'advisor.contact_infos',
          'advisor.gdpr_delete_by',
          'advisor.location',
          'advisor.sourced_at',
          'advisor_decline',
          'advisor_blocked_status',
          'create_user',
          'latest_submitted_pre_ca',
          'latest_submitted_pre_ca.inquiry_instance',
          'client.compliance_preference',
          'email_records.advisor_tracking',
          'task_communication',
        ].join(),
      },
      tableData: {
        data: [],
        count: 0,
      },
      selectedAdvisor: [],
      clone_select: [],
      data: {
        status: -1,
        time_range_type: 1,
        time_range: undefined,
        recent_activities: 0,
      },
      task_status_options: [
        { label: 'T&C Signed - Latest', value: 0 },
        { label: 'T&C Not Signed - Latest', value: 1 },
        { label: 'Screened', value: 2 },
        { label: 'Provided Availability - Future', value: 3 },
        { label: 'Provided Availability - Expired', value: 4 },
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: 'Recent Week',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          }, {
            text: 'Recent Month',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          }, {
            text: '3 Months',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          }],
      },
    }
  },
  computed: {
    ...mapGetters([
      'advisor_options',
      'project_options',
      'uid',
      'roles',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      unreadEvents: state => state.project.unread_subscription_events,
    }),
    isSurveyProject() {
      return this.project.sub_type === 'Survey'
    },
  },
  watch: {
    'unreadEvents.mask_read_leads': {
      handler(newVal) {
        if (newVal) {
          const read_task_ids = this.unreadEvents.leads_task_ids
          const origin_task_ids = this.searchQuery.ids?.split() || []
          this.searchQuery.ids = [...new Set(origin_task_ids.concat(read_task_ids).map(Number))].join()
          this.unreadEvents.leads_task_ids = []
          this.unreadEvents.mask_read_leads = false
          this.getList()
        }
      },
    },
  },
  created() {
    if (this.$route.query.task_id) {
      this.searchQuery.ids = +this.$route.query.task_id
    }
    this.showAllColumns = !this.isSurveyProject
  },

  beforeDestroy() {
    this.saveTaskIdsToSession()
    window.removeEventListener('beforeunload', this.saveTaskIdsToSession)
  },

  async mounted() {
    this.getPermission()
    await this.init()
    await this.getList()
      .then(() => {
        this.$nextTick(() => {
          this.loadSortable()
        })
        window.addEventListener('beforeunload', this.saveTaskIdsToSession)
      })
  },
  methods: {
    getPermission() {
      this.editPermission = this.roles.some(role => {
        return [7].includes(role.role_id)
      })
    },
    init() {
      const session_storage = JSON.parse(sessionStorage.getItem(this.sessionKey))
      if (session_storage?.task_ids) {
        this.searchQuery.ids = session_storage.task_ids
      }
    },
    getList() {
      this.loading = true
      this.initBlacklistFilter()
      if (this.blacklistFilter === 'BLACKLIST' || this.blacklistFilter === 'B&D') {
        this.searchQuery['advisor.or-0.status_not_in'] = 'BLACKLIST'
        this.searchQuery['advisor.or-1.status_is_null'] = true
      }
      if (this.blacklistFilter === 'DECLINED' || this.blacklistFilter === 'B&D') {
        this.searchQuery['advisor_decline.or-0.id_is_null'] = true
        this.searchQuery['advisor_decline.or-1.is_valid'] = false
      }

      const params = formatParams(this.searchQuery, this.isSurveyProject)
      return ProjectAPI.getProjectAdvisorList(params)
        .then(data => {
          this.tableData.data = this.formatList(data)['list']
          this.tableData.count = data['count']

          this.$nextTick(() => {
            this.loading = false
            this.clone_select ? this.selectForUser() : ''
          })
        })

      function formatParams(searchQuery, isSurveyProject) {
        const res = {
          project_id: searchQuery.project_id,
        }

        for (const key in searchQuery) {
          if (Object.hasOwnProperty.call(searchQuery, key)) {
            if (!isEmpty(searchQuery[key])) {
              res[key] = searchQuery[key]
            }
          }
          if (key === 'company_is_current') {
            res[key] = searchQuery[key]
          }
          if (key === 'advisor.location_ids') {
            res[key] = searchQuery[key].join() || undefined
          }
        }

        if (typeof res.task_status === 'number') {
          const tc_valid = isSurveyProject
            ? 'advisor.survey_tc_valid'
            : 'advisor.consultation_tc_valid'
          switch (res.task_status) {
            case 0:
              res[tc_valid] = true
              break
            case 1:
              res[tc_valid] = false
              break
            case 2:
              res['sq_status_in'] = 'RESPONDED'
              break
            case 3:
              res['advisor_available_slots.id_is_not_null'] = true
              break
            case 4:
              res['advisor_expired_slots.id_is_not_null'] = true
          }

          delete res.task_status
        }

        if (String(res['advisor.contact_infos.value_contains']) === 'undefined') {
          delete res['advisor.contact_infos.type']
          delete res['advisor.contact_infos.value_contains']
        }
        return res
      }
    },

    saveTaskIdsToSession() {
      const data = { task_ids: this.searchQuery.ids }
      sessionStorage.setItem(this.sessionKey, JSON.stringify(data))
    },

    handleSearch(val) {
      debounce(() => {
        this.searchQuery.decipher_status_in = this.selectedDecipherSurveyStatuses.join()
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },

    sortData({ column, prop, order }) {
      const dict = {
        'ascending': 'asc',
        'descending': 'desc',
      }

      if (prop === 'outreach_opened') {
        const outreach_sort = 'outreach_opened desc,outreach_opened_at desc,outreach_tracking_status,outreach_tracking_update_at desc'
        this.searchQuery.sort = order === 'descending' ? outreach_sort : undefined
      } else {
        if (!order) {
          this.searchQuery.sort = undefined
        } else {
          this.searchQuery.sort = `${prop} ${dict[order]}`
        }
      }

      this.getList()
    },

    tableHeaderCellClassName({ row, column, rowIndex }) {
      if (column.label === 'Opened') {
        return 'custom-hidden-sort-asc'
      }
    },
    removeLeads(val) {
      const params = val
        .map(item => {
          return item.task?.id
        })
      ProjectAPI.deleteTask(params).then(() => {
        this.getList()
      })
    },

    getTaskOutreachNum(email_records) {
      return email_records.filter(item => item.email_content_type === 'PROJECT_OUTREACH').length
    },
    handleSelectionChange(val) {
      if (this.clone_select.length || !this.need_request_for_re_selected) {
        return
      }
      const selected = val.length >= this.selectedAdvisor.length
      if (selected) {
        this.getSelectedTasksDetailForLeads(val)
      } else {
        this.selectedAdvisor = this.selectedAdvisor.filter(
          item => val.find(i => i.id === item.id))
      }
    },

    getSelectedTasksDetailForLeads(select_list) {
      if (!select_list.length) return
      this.leadSelectLoading = true
      const ids = select_list.map(item => item.id)
      this.getTasksByIDSForLeads(ids)
        .then(data => {
          this.selectedAdvisor = data['list']
        })
        .finally(() => {
          this.leadSelectLoading = false
        })
    },

    getTasksByIDSForLeads(ids) {
      const params = {
        params: {
          ids: ids.join(),
          page: 1,
          size: 300,
          extra: [
            'advisor_decline',
            'advisor.current_job',
            'advisor.email_address_score',
            'advisor.email_validation_records',
            'advisor.jobs.legal_validate_result',
            'advisor.contact_infos',
            'latest_submitted_pre_ca',
            'latest_submitted_sq',
            'client.compliance_preference',
          ].join(),
        },
        project_id: this.projectId,
      }
      return ProjectAPI.getTasksDetail(params).then(data => {
        return {
          list: ids.map(item => {
            const target = data.list.find(i => i.id === item)
            this.formatListWiseValid(target)
            this.formatMostRecentEmail(target)
            return target
          }),
        }
      })
    },

    downloadTaskForLeads(all_leads = false) {
      this.$refs.taskDownload.checkBeforeDownload(this.project, 'TASK', all_leads, null, this.selectedAdvisor,
        this.blacklistFilter)
    },
    copyTaskUrl(task_id) {
      const url = window.location.origin +
        `/#/project/consultation/${this.projectId}/tasks?task_id=${task_id}`
      return copyUtil(url)
        .then(() => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.advisor?.gdpr_delete_by_id) {
        if (column.columnKey === 'collapse-start') {
          return [1, 5]
        } else if (column.columnKey === 'advisor-info') {
          return [0, 0]
        }
      }
    },

    handleDecipherStatusUpdated() {
      this.reloadList(true)
      ProjectAPI.getProject(this.project.id, []).then(response => {
        // why not wrapped in 'data'?
        this.project.decipher_statuses_last_update = response.decipher_statuses_last_update
      }).finally(() => {
        this.$message.success('Success')
      })
    },

    handleDecipherStatusFilter(selectedStatuses) {
      this.selectedDecipherSurveyStatuses = selectedStatuses
      this.handleSearch()
    },

    initBlacklistFilter() {
      this.searchQuery['advisor.or-0.status_not_in'] = undefined
      this.searchQuery['advisor.or-1.status_is_null'] = undefined
      this.searchQuery['advisor_decline.or-0.id_is_null'] = undefined
      this.searchQuery['advisor_decline.or-1.is_valid'] = undefined
    },
  },
}
</script>

<style lang="scss">
.leads {
  .input-with-select {
    display: inline-table;
    width: 210px;

    .el-input-group__prepend {
      width: 95px;
      background-color: #fff;
    }
  }

  .stripe-table {
    border: 1px solid #EBEEF5;
    border-bottom: 0;

    thead .gutter {
      display: table-cell !important;
    }

    thead th:nth-of-type(2) label {
      margin-left: 4px;
    }

    tr.blue-background-class {
      background: #c8ebfb;
    }

    .el-table__expanded-cell {
      padding: 15px 25px;
    }

    .caret-wrapper {
      width: 8px;
    }

    .sort-caret {
      left: 4px;
    }
  }

  .tool-switch {
    min-height: 45px;
  }

  .el-table__row:hover {

    .dropdown-link .el-icon--right {
      display: inline-block;
    }
  }

  .dropdown-link {
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;

    .el-icon--right {
      display: none;
    }
  }

  .copy-task-link {
    color: #dadce0;

    :hover {
      color: #868686;
    }
  }

  .is-blocked {
    color: #F56C6C;

    &:hover {
      color: #f78989;
    }
  }
}

.custom-pagination {
  float: right;
  display: inline-block;
  padding: 5px 0 !important;
}
</style>

<style scoped lang="scss">
.keyword-custom-input {
  ::v-deep .el-input-group__append {
    padding: 0 10px;
  }

  ::v-deep .el-checkbox__input {
    display: none;
  }

  ::v-deep .el-checkbox__label {
    padding-left: 0; // 移除label的左边距
  }
}
</style>
