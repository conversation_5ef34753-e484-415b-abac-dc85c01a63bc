<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        Notes
      </div>
    </el-row>
    <div style="height:500px;">
      <div class="text-center">
        <el-radio-group v-model="activeName">
          <el-radio-button label="RECRUITING">Recruiting Notes {{ recruiting_notes.length }}</el-radio-button>
          <el-radio-button label="CLIENT">Client Notes / Feedback {{ client_notes.length }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="mt-8 mb-normal">
        <el-link
          type="primary"
          :underline="false"
          icon="el-icon-plus"
          @click="addNote()" />
        <note-tag-search
          v-if="tag_list.length>0 && isClientNote"
          v-model="search_tags"
          :tag-list="tag_list"
          :allow-create="false"
          class="pull-right inline-block"
          @change="getClientNoteList" />
      </div>
      <div v-loading="loading" class="scrollbar-narrow note-box">
        <div v-for="item in current_notes" :key="item.id" class="note-item">
          <div v-if="!item.is_edit" class="note">
            <div>
              <el-tag v-if="isClientNote" type="primary" size="mini">
                {{ item.tags | getNames(tag_list) }}
              </el-tag>
              <el-tag type="info" size="mini" style="color:#3c3a3a;">{{ item.creator_name }}</el-tag>
              <el-tag type="info" size="mini">{{ item.time_text }}</el-tag>
            </div>
            <div class="actions">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-edit"
                class="item"
                @click="initEdit(item)" />
              <el-popconfirm
                icon="el-icon-info"
                icon-color="red"
                title="Are you sure you want to delete this note?"
                cancel-button-text="Cancel"
                confirm-button-text="Confirm"
                confirm-button-type="danger"
                @confirm="deleteNote(item.id)"
              >
                <el-link
                  slot="reference"
                  type="danger"
                  class="item"
                  :underline="false"
                  icon="el-icon-delete" />
              </el-popconfirm>
            </div>
          </div>
          <div v-if="!item.is_edit" v-html="$sanitizeHtml(item.note)" />
          <div v-if="item.is_edit" class="w-100">
            <note-tag-search v-if="isClientNote" v-model="item.tags" :tag-list="tag_list" class="multiple-select" />
            <tinymce v-model="item.note_new" :height="300" class="mt-8" :toolbar="toolbar" style="width:99%" />
            <div class="mt-8 text-center">
              <el-button type="primary" :loading="loading" :disabled="!item.note_new.trim()" @click="saveNote(item)">Save</el-button>
              <el-button type="text" @click="cancelEditNote(item)">Cancel</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import moment from 'moment'
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import ProjectInformationMixins from '../mixins/'
import NoteTagSearch from '@/components/ClientNotes/NoteTagSearch'
import Tinymce from '@/components/Tinymce'
import _ from 'lodash'

export default {
  name: 'ProjectNotes',
  components: { NoteTagSearch, Tinymce },
  mixins: [ProjectInformationMixins],
  data() {
    return {
      toolbar: ['undo redo | bold italic link | alignleft  aligncenter alignright | numlist bullist | outdent indent'],
      activeName: 'RECRUITING',
      loading: false,
      project_id: this.$route.params.project_id,
      client_notes: [],
      recruiting_notes: [],
      current_notes: [],
      tag_list: [],
      search_tags: [],
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      projectInfo: state => state.project.data,
    }),
    clientId() {
      return this.projectInfo.client_id
    },
    isClientNote() {
      return this.activeName === 'CLIENT'
    },
  },
  watch: {
    'activeName': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.current_notes = newVal === 'RECRUITING' ? this.recruiting_notes : this.client_notes
        }
      },
    },
  },

  async mounted() {
    await this.getProjectNoteList()
    await this.getClientNoteList()
    this.current_notes = this.isClientNote ? this.client_notes : this.recruiting_notes
  },

  methods: {
    getProjectNoteList() {
      return this.getProjectInfo()
        .then(data => {
          const sort_note = _.sortBy(data.notes.filter(item => item.type === 'RECRUITING'), 'create_at').reverse()
          this.recruiting_notes = sort_note.map(item => {
            item.time_text = moment(item.create_at).fromNow()
            item.is_edit = false
            return item
          })
        })
        .finally(() => {
          this.current_notes = this.isClientNote ? this.client_notes : this.recruiting_notes
        })
    },

    getClientNoteList() {
      const params = {
        project_id: this.project_id,
        client_id: this.clientId,
        target_type: 'PROJECT',
        'tag_refs.tag.ids': this.search_tags.join() || undefined,
        page: 1,
        size: 9999,
        extra: 'create_by,tag_refs',
      }
      return ClientAPI.getClientNoteList(params).then(data => {
        this.client_notes = data.list.map(item => {
          item.time_text = moment(item.create_at).fromNow()
          item.creator_name = item.create_by.name
          item.tags = item.tag_refs.map(item => item.tag_id)
          item.note = item.content
          item.is_edit = false
          return item
        })
        if (this.search_tags.length < 1) this.getTagList()
      }).finally(() => {
        this.current_notes = this.isClientNote ? this.client_notes : this.recruiting_notes
      })
    },

    getTagList() {
      return ClientAPI.getClientNoteTags().then(data => {
        this.tag_list = data.list
      })
    },

    initEdit(item) {
      item.note_new = item.note
      item.is_edit = true
    },

    addNote() {
      this.current_notes.unshift({
        is_edit: true,
        note_new: '',
        tag_refs: this.isClientNote ? [{ tag_id: 1 }] : undefined,
      })
    },

    cancelEditNote(item) {
      item.id ? item.is_edit = false : this.current_notes.splice(0, 1)
    },

    deleteNote(note_id) {
      this.loading = true
      if (this.isClientNote) {
        return ClientAPI.deleteClientNote(note_id).then(() => {
          const index = this.client_notes.findIndex(item => item.id === note_id)
          this.client_notes.splice(index, 1)
          this.current_notes = this.client_notes
        }).finally(() => {
          this.loading = false
        })
      } else {
        return ProjectAPI.deleteProjectNotes(
          this.project_id, note_id).then(() => {
          const index = this.recruiting_notes.findIndex(item => item.id === note_id)
          this.recruiting_notes.splice(index, 1)
          this.current_notes = this.recruiting_notes
        }).finally(() => {
          this.loading = false
        })
      }
    },

    saveNote(item) {
      this.loading = true
      this.isClientNote ? this.updateClientNote(item) : this.updateProjectNote(item)
    },

    updateClientNote(item) {
      const params = {
        id: item.id || undefined,
        client_id: this.clientId,
        project_id: this.project_id,
        target_type: 'PROJECT',
        tag_refs: item.tags.map(item => {
          return { 'tag_id': item }
        }),
        content: item.note_new,
      }

      const request = params.id ? ClientAPI.updateClientNote(params) : ClientAPI.addClientNote(params)
      request.then((data) => {
        item.is_edit = false
        this.$message({
          message: 'Save successful',
          type: 'success',
        })
        return this.getClientNoteList()
      }).finally(() => {
        this.loading = false
      })
    },

    updateProjectNote(item) {
      const data = {
        note: item.note_new,
        creator_uid: this.uid,
        type: 'RECRUITING',
        project_id: this.project_id,
      }

      const request = item.id
        ? ProjectAPI.updateProjectNotes(this.project_id, item.id, data)
        : ProjectAPI.saveProjectNotes(this.project_id, [data])

      return request.then(() => {
        item.is_edit = false
        this.$message({
          message: 'Save successful',
          type: 'success',
        })
        return this.getProjectNoteList()
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">
.note-item {
  font-size: 12px;
  line-height: 1.5;
  padding-bottom: 0.5rem;

  .note {
    display: flex;
    justify-content: space-between;
  }

  .actions {
    text-align: right;
    flex-shrink: 0;

    .item {
      display: none;
    }

  }

  &:hover {
    .actions {
      .item {
        display: inline-block;
      }
    }
  }
}

::v-deep .el-card__body {
  padding: 14px;
  height: 700px;
}

.note-box {
  height: 620px;
  overflow: auto;
}
</style>
