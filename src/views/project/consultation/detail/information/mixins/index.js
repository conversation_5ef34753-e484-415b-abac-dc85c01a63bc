import ProjectAPI from '@/api/project'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      project_id: this.$route.params.project_id,
      loading: false,
      data: undefined,
      isEditing: false,
      editData: undefined,
      client_am_list: [],
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
  },
  methods: {
    loadInfo() {
      const data = this.projectInfo
      this.data = Object.assign({}, data)
      this.editData = this.formEditData(data)
      this.client_am_list = data.client.account_managers
    },
    updatedLoadInfo() {
      this.getProjectInfo().then((data) => {
        this.data = Object.assign({}, data)
        this.updatedVuexLoadInfo(data)
        this.editData = this.formEditData(data)
        this.client_am_list = data.client.account_managers
      })
    },
    updatedVuexLoadInfo(data) {
      return this.$store.dispatch('project/updateInfo', data)
    },
    getProjectInfo() {
      return ProjectAPI.getProject(this.project_id)
    },
    actionEdit() {
      this.isEditing = true
    },
    actionEditFinish() {
      this.isEditing = false
      this.updatedLoadInfo()
    },
    actionEditCancel() {
      this.isEditing = false
    },
  },
}

