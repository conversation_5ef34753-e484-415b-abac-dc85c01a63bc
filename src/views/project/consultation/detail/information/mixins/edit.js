import ProjectAPI from '@/api/project'
import _ from 'lodash'

export default {
  props: {
    projectData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      loading: false,
      data: {},
    }
  },
  methods: {
    actionCancel() {
      this.$emit('cancel')
    },
    actionSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          return this.update()
            .then(data => {
              this.$emit('done', data)
            })
        } else {
          return false
        }
      })
    },
    async update() {
      this.loading = true
      const params = this.formParams()
      const slackChannelChange = this.projectData.slack_channel_name !==
        this.data.slack_channel_name

      const survey_charge_change = params.survey_total_charge !==
        this.projectData.survey_total_charge
      const survey_charge_currency_change = params.survey_total_charge_currency !==
        this.projectData.survey_total_charge_currency
      const survey_charge_need_update = (survey_charge_change ||
        survey_charge_currency_change) && params.sub_type === 'Survey'

      const patients_charge_change = params.total_charge !==
        this.projectData.total_charge
      const patients_charge_currency_change = params.total_charge_currency !==
        this.projectData.total_charge_currency
      const patients_charge_need_update = (patients_charge_change ||
        patients_charge_currency_change) && params.sub_type === 'Patients'

      const client_need_update = params.client_id !== this.projectData.client_id

      const project_basic_info = _.cloneDeep(params)
      delete project_basic_info.survey_total_charge
      delete project_basic_info.survey_total_charge_currency
      delete project_basic_info.total_charge
      delete project_basic_info.total_charge_currency
      delete project_basic_info.client_id
      delete project_basic_info.client
      delete project_basic_info.client_contracts

      // 禁止修改该值 后端不接收字段
      delete project_basic_info.sub_type

      await ProjectAPI.updateProject(project_basic_info)

      const promise_all = []
      if (client_need_update) promise_all.push(this.updateProjectClient(params))
      if (survey_charge_need_update) promise_all.push(this.updateSurveyCharge(params))
      if (patients_charge_need_update) promise_all.push(this.updatePatientsCharge(params))
      if (slackChannelChange) promise_all.push(this.updateSlackChannel(this.data.slack_channel_name))

      return Promise.all(promise_all)
        .then((data_list) => {
          this.$message.success('success')
          return data_list[data_list.length - 1]
        })
        .finally(() => {
          this.loading = false
        })
    },

    updateSlackChannel(val) {
      const params = {
        slack_channel_name: val,
      }
      return ProjectAPI.updateProjectSlackChannel(this.projectData.id, params)
    },

    updateSurveyCharge(data) {
      const params = {
        'survey_total_charge': data.survey_total_charge,
        'survey_total_charge_currency': data.survey_total_charge_currency,
      }
      return ProjectAPI.updateSurveyRevenue(data.id, params)
    },

    updatePatientsCharge(data) {
      const params = {
        'total_charge': data.total_charge,
        'total_charge_currency': data.total_charge_currency,
      }
      return ProjectAPI.updatePatientsCharge(data.id, params)
    },

    updateProjectClient(data) {
      const params = {
        to_client_id: data.client_id,
      }
      return ProjectAPI.changeProjectClient(data.id, params)
    },
  },
}

