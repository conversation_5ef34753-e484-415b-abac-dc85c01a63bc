<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        {{ projectInfo.outsource_request_content ? 'Outsource Request' : 'Request' }}
      </div>
      <div>
        <el-link
          v-if="!isEditing"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          :underline="false"
          @click="actionEdit"
        />
      </div>
    </el-row>
    <div v-if="data" style="min-height:500px">
      <el-form
        v-if="!isEditing"
        :model="data"
      >
        <div class="pre-wrap" style="font-size:12px; line-height: 1.4;" v-html="$sanitizeHtml(data[requestType])" />
      </el-form>
      <request-edit
        v-else
        :request-type="requestType"
        :project-data="editData"
        @done="actionEditFinish"
        @cancel="actionEditCancel"
      />
    </div>
  </el-card>
</template>

<script>
import ProjectInformationMixins from '../mixins/'
import RequestEdit from './edit'

export default {
  name: 'ProjectRequest',
  components: { RequestEdit },
  mixins: [ProjectInformationMixins],
  data() {
    return {}
  },
  computed: {
    requestType() {
      return this.projectInfo.outsource_request_content ? 'outsource_request_content' : 'request_content'
    },
  },
  mounted() {
    this.loadInfo()
  },
  methods: {
    formEditData(data) {
      const result = {}
      result.id = data.id
      result[this.requestType] = data[this.requestType]

      return result
    },
  },
}
</script>

<style scoped>

</style>
