<template>
  <div>
    <el-form
      ref="form"
      v-loading="loading"
      :model="data"
      :rules="rules"
    >
      <el-form-item class="scrollbar-narrow">
        <el-input v-model="data[requestType]" type="textarea" :autosize="{ minRows: 7, maxRows:24 }" />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!data[requestType]"
          icon="el-icon-check"
          @click="actionSave">Save
        </el-button>
        <el-button type="text" @click="actionCancel">Cancel</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import mixinsEdit from '../mixins/edit'

export default {
  name: 'RequestEdit',
  mixins: [mixinsEdit],
  props: {
    requestType: String,
  },
  data() {
    return {
      rules: {},
    }
  },
  mounted() {
    this.data = Object.assign({}, this.projectData)
  },
  methods: {
    formParams() {
      const result = {
        id: this.data.id,
      }
      result[this.requestType] = this.data[this.requestType]
      return result
    },
  },

}
</script>

<style scoped>

</style>
