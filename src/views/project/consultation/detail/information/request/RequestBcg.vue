<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        {{ projectInfo.outsource_request_content ? 'Outsource Request' : 'Request' }}
      </div>
    </el-row>
    <el-form
      v-if="data"
      :model="data"
      label-width="140px"
      class="show-form"
    >
      <el-form-item label="Request ID" prop="request_id">
        {{ data.request_id }}
      </el-form-item>
      <el-form-item label="Project Description" prop="project_description">
        {{ data.project_description }}
      </el-form-item>
      <el-form-item label="Client Industries" prop="client_industries">
        {{ data.client_industries }}
      </el-form-item>
      <el-form-item label="Off Limit Companies" prop="off_limit_companies">
        {{ data.off_limit_companies }}
      </el-form-item>
      <el-form-item label="Expert Types" prop="expert_types">
        {{ data.expert_types.join() }}
      </el-form-item>
      <el-form-item label="Interview Anticipated">
        <el-form-item label="Count" label-width="75px" prop="interview_anticipated_count" style="margin-bottom: 0;">
          {{ data.interview_anticipated_count }}
        </el-form-item>
        <el-form-item label="Start Date" label-width="75px" prop="interview_anticipated_start_date" style="margin-bottom: 0;">
          {{ data.interview_anticipated_start_date }}
        </el-form-item>
        <el-form-item label="End Date" label-width="75px" prop="interview_anticipated_end_date" style="margin-bottom: 0;">
          {{ data.interview_anticipated_end_date }}
        </el-form-item>
      </el-form-item>
      <el-form-item label="Research Manager">
        <el-form-item label="Email" label-width="75px" prop="research_manager_email" style="margin-bottom: 0;">
          {{ data.research_manager_email }}
        </el-form-item>
      </el-form-item>
      <el-form-item label="Requestor">
        <el-form-item label="Email" label-width="75px" prop="requestor.email" style="margin-bottom: 0;">
          {{ data.requestor.email }}
        </el-form-item>
        <el-form-item label="Phone" label-width="75px" prop="requestor.phoneNumber" style="margin-bottom: 0;">
          {{ data.requestor.phoneNumber }}
        </el-form-item>
        <el-form-item label="Location" label-width="75px" prop="requestor.location" style="margin-bottom: 0;">
          {{ data.requestor.location }}
        </el-form-item>
        <el-form-item label="Time Zone" label-width="75px" prop="requestor.timeZone" style="margin-bottom: 0;">
          {{ data.requestor.timeZone }}
        </el-form-item>
      </el-form-item>
      <el-form-item label="Attachments">
        <el-link
          v-for="item in data.attachments"
          :key="item"
          type="primary"
          :href="item"
          target="_blank"
          icon="el-icon-link"
          class="vertical-top"
        >
          {{ item }}
        </el-link>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import ProjectInformationMixins from '../mixins/'

export default {
  name: 'ProjectRequestBCG',
  mixins: [ProjectInformationMixins],
  data() {
    return {}
  },
  mounted() {
    this.data = { ...this.projectInfo.bcg_hub_project }
  },
}
</script>

<style scoped>
::v-deep .show-form .el-form-item--mini.el-form-item {
  margin-bottom: 10px;
}
</style>
