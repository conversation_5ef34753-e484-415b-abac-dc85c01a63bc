<template>
  <div>
    <el-row class="row-bg" :gutter="20">
      <el-col :span="8" class="card">
        <basic />
      </el-col>
      <el-col :span="8" class="card">
        <request v-if="!projectInfo.bcg_hub_project" />
        <request-bcg v-else />
      </el-col>
      <el-col :span="8" class="card">
        <notes />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Basic from '@/views/project/consultation/detail/information/Basic/index'
import Request from '@/views/project/consultation/detail/information/request/index'
import RequestBcg from '@/views/project/consultation/detail/information/request/RequestBcg.vue'
import Notes from '@/views/project/consultation/detail/information/Notes/index'

export default {
  name: 'Information',
  components: { Basic, Request, RequestBcg, Notes },
  data() {
    return {
      project_id: this.$route.params.project_id,
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
  },
}
</script>

<style scoped>
.card {
  padding-bottom: 20px;
}
</style>
