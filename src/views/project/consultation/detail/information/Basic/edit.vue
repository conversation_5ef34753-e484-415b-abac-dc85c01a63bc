<template>
  <div>
    <el-form
      ref="form"
      v-loading="loading"
      :model="data"
      label-width="140px"
      :rules="rules"
    >
      <el-form-item
        label="Name"
        prop="name"
      >
        <el-input v-model="data.name" />
      </el-form-item>
      <el-form-item label="Client">
        <slot v-if="!isEditClient">
          <span>{{ data.client_name }}</span>
          <el-tooltip
            v-if="permissionEditClient"
            class="item"
            effect="dark"
            content="Changing client may cause data problems, so do not operate easily."
            placement="top">
            <el-link
              :underline="false"
              type="primary"
              class="el-icon-edit ml-8 mr-8"
              @click="editClientInit()" />
          </el-tooltip>
        </slot>
        <slot v-if="isEditClient">
          <el-select
            v-model="data.client"
            filterable
            remote
            reserve-keyword
            placeholder="Search"
            class="remote-select"
            value-key="id"
            :disabled="!allowChangeClient"
            :loading="selectLoading"
            :remote-method="getClient"
            no-data-text="No matching data"
            @change="changeClient"
          >
            <el-option v-for="item in client_list" :key="item.id" :label="item.name" :value="item" />
          </el-select>
          <span v-if="survey_only_client" class="warning pl-5">This Client is Survey Only.</span>
        </slot>
      </el-form-item>
      <el-form-item label="Industry" prop="industry">
        <el-select v-model="data.industry">
          <el-option
v-for="item in project_options.industry"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="data.investment_target"
        label="Investment Target (obsoleted)"
      >
        <el-input v-model="data.investment_target" disabled />
      </el-form-item>
      <el-form-item
        v-if="!isSurveySubType"
        key="investment_target_company_ids"
        label="Investment Target"
        prop="investment_target_company_ids"
      >
        <div class="multiple-select flex">
          <company-search
v-model="data.investment_target_company_ids"
                          multiple
                          stock-label
                          :origin-list="projectData.investment_target_companies"
                          show-link
                          class="multiple-select"
                          placeholder="Investment Target"
                          clearable
                          @init-completed="SearchInitCompleted()" />
          <el-button
v-if="isSSTOrConsultationSubType"
                     type="success"
                     class="ml-3"
                     icon="el-icon-plus"
                     @click="dialogVisible=true">New
          </el-button>
        </div>
        <div>
          <el-checkbox
v-show="isSSTOrConsultationSubType"
                       v-model="investment_target_not_confirmed">
            Investment Target is not confirmed or is not applicable.
          </el-checkbox>
        </div>
      </el-form-item>
      <el-form-item label="Project Type" prop="sub_type">
        <el-select v-model="data.sub_type" placeholder="Type" disabled @change="subTypeSelect()">
          <el-option
            v-for="item in project_options.sub_type"
            :key="item.value"
            :label="item.label"
            :disabled="item.value==='Consultation'&&survey_only_client"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="needCaseType" key="case_type" label="Case Type" prop="case_type">
        <el-select key="case_type" v-model="data.case_type" placeholder="Case Type" @change="handleChangeCaseType">
          <el-option
            v-for="item in project_options.case_type"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item
v-if="isSurveySubType"
                    key="third_party_survey_url"
                    label="Survey URL"
                    prop="third_party_survey_url">
        <el-input v-model="data.third_party_survey_url" />
      </el-form-item>
      <el-form-item
v-if="isSurveySubType"
                    key="decipher_id"
                    label="Decipher Survey ID for status sync"
                    prop="decipher_id">
        <el-input v-model="data.decipher_id" />
      </el-form-item>
      <el-form-item v-if="isSurveySubType" key="rate" label="Survey Honorarium" prop="rate">
        <el-input-number v-model="data.rate" :min="0" class="w-120px inline-block" />
        <el-select
          v-model="data.rate_currency"
          placeholder="Currency"
          class="w-80px inline-block">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="isSurveySubType" label="Client Charge per Complete" prop="client_charge_per_complete">
        <el-input-number v-model="data.client_charge_per_complete" :min="0" class="w-120px inline-block" />
        <el-select
          v-model="data.client_charge_per_complete_currency"
          placeholder="Currency"
          class="w-80px inline-block">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="Tickers">
        <company-search
          v-model="data.tickerIds"
          class="multiple-select"
          multiple
          :origin-list="projectData.ticker_list"
          @init-completed="SearchInitCompleted()" />
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="Proactive Project">
        <el-checkbox v-model="data.is_proactive">Yes</el-checkbox>
      </el-form-item>
      <el-form-item
        v-if="data.client && data.client.preference && data.client.preference.tpa_client"
        label="TPA Project"
      >
        <el-select v-model="data.tpa_project">
          <el-option label="Yes" :value="true" />
          <el-option label="No" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="Start Date" prop="start_date">
        <el-date-picker
          v-model="data.start_date"
          type="date"
          placeholder="Start Date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="End Date" prop="end_date">
        <el-date-picker
          v-model="data.end_date"
          type="date"
          placeholder="End Date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="CN DB Project ID">
        <el-input v-model="data.ndb_id" type="number" placeholder="CN DB Project ID" />
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="Time Zone">
        <time-zone-select v-if="project_time_zone!==undefined" v-model="project_time_zone" />
      </el-form-item>
      <br>
      <el-form-item label="Type" prop="client_type">
        <el-select v-model="data.client_type" disabled>
          <el-option v-for="item in client_options.type" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="Blind expert profiles" prop="blind_expert_profiles">
        <el-checkbox v-model="data.blind_expert_profiles" :disabled="client_blind_advisor_name" />
      </el-form-item>
      <el-form-item
        v-if="data.client && data.client.preference && data.client.preference.enable_client_office_in_project"
        label="Client Office"
        prop="client_office_id"
      >
        <client-office-search
          v-model="data.client_office_id"
          :client-id="data.client_id"
        />
      </el-form-item>
      <el-form-item key="code" label="Project Code" prop="code">
        <el-input v-model="data.code" placeholder="Project Code" />
        <el-checkbox v-if="need_code_project_types.includes(data.sub_type)" v-model="project_code_to_be_provided_later">
          Project code to be provided later
        </el-checkbox>
      </el-form-item>
      <client-custom-fields
        v-if="client_custom_fields.length > 0"
        :custom-fields.sync="client_custom_fields"
        :form-data.sync="data.client_custom_fields"
      />
      <template v-if="needRolesClient">
        <el-form-item label="Project Leader">
          <div class="multiple-select flex">
            <el-select
              v-model="data.project_leader"
              filterable
              multiple
              clearable
              placeholder="Project Leader"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="data.client_id"
              :bcg="needRolesClient"
              bcg-role="PROJECT_LEADER"
              class="ml-3"
              @update="updateClientUser($event, 'project_leader')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Principal">
          <div class="multiple-select flex">
            <el-select
              v-model="data.principal"
              filterable
              multiple
              clearable
              placeholder="Principal"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="data.client_id"
              :bcg="needRolesClient"
              bcg-role="PRINCIPAL"
              class="ml-3"
              @update="updateClientUser($event, 'principal')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Partner">
          <div class="multiple-select flex">
            <el-select
              v-model="data.partner"
              filterable
              multiple
              clearable
              placeholder="Partner"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="data.client_id"
              :bcg="needRolesClient"
              bcg-role="PARTNER"
              class="ml-3"
              @update="updateClientUser($event, 'partner')"
            />
          </div>
        </el-form-item>
        <el-form-item label="Manager Director / Partner (MDP)">
          <div class="multiple-select flex">
            <el-select
              v-model="data.mdp"
              filterable
              multiple
              clearable
              placeholder="Manager Director / Partner (MDP)"
              class="w-100"
            >
              <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <create-client-user
              :client-id="data.client_id"
              :bcg="needRolesClient"
              bcg-role="MANAGING_DIRECTOR_AND_PARTNER_MDP"
              class="ml-3"
              @update="updateClientUser($event, 'mdp')"
            />
          </div>
        </el-form-item>
      </template>
      <el-form-item key="project_client_contacts" label="Client Contacts" prop="project_client_contacts">
        <div class="flex">
          <el-select
            v-model="data.project_client_contacts"
            filterable
            remote
            reserve-keyword
            placeholder="Search"
            multiple
            clearable
            class="multiple-select flex-1"
            no-data-text="No matching data"
          >
            <el-option v-for="item in client_contacts_option" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <create-client-user
            v-if="data.client"
            class="ml-3"
            :client-id="data.client.id"
            :bcg="needRolesClient"
            @update="updateClientUser" />
        </div>
        <span class="prompt-words">This first is the primary contact</span>
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="External Contacts for Recording">
        <span v-if="!allowEditExternalRecordingContacts">{{data.external_contacts_for_recording_list && data.external_contacts_for_recording_list.join()}}</span>
        <email-editor v-else :origin-list.sync="data.external_contacts_for_recording_list" />
      </el-form-item>
      <el-form-item label="Client Provided Bridge Link">
        <el-input v-model="data.client_provided_bridge_link" type="textarea" />
      </el-form-item>
      <el-form-item label="Relationship Manager">
        <el-tag v-for="item in data.account_managers" :key="item.id" type="info" class="mr-8">{{ item.user.name }}
        </el-tag>
      </el-form-item>
      <br>
      <el-form-item v-if="isSurveySubType" label="Survey Charge" prop="survey_total_charge">
        <el-input-number v-model="data.survey_total_charge" :disabled="!permissionEditSurveyCharge" :min="0" class="w-120px inline-block" />
        <el-select
          v-model="data.survey_total_charge_currency"
          :disabled="!permissionEditSurveyCharge"
          placeholder="Currency"
          class="w-80px inline-block">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="data.sub_type==='Patients'" label="Patients Charge" prop="total_charge">
        <el-input-number v-model="data.total_charge" :min="0" class="w-120px inline-block" />
        <el-select
          v-model="data.total_charge_currency"
          placeholder="Currency"
          class="w-80px inline-block">
          <el-option
            v-for="item in options.currency"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <br>
      <el-form-item label="Project Manager" prop="pm_id">
        <user-search
          v-model="data.project_manager"
          placeholder="Search"
          :extra-member="getMemberByRole('PROJECT_MANAGER')" />
      </el-form-item>
      <el-form-item label="Support Members">
        <user-search
          v-model="data.support_members"
          clearable
          multiple
          allow-select-team
          class="multiple-select"
          placeholder="Search"
          role="create-project"
          :extra-member="extra_member.support_members"
          @select="(selected)=>{data.support_members = selected}"
          @init-completed="SearchInitCompleted()" />
      </el-form-item>

      <el-form-item label="Slack Channel" prop="slack_channel_name">
        <el-input v-model="data.slack_channel_name" placeholder="Slack Channel" />
        <div class="slack-channel-tip">Channel name can only contain lowercase letters, numbers, hyphens and underscores
        </div>
      </el-form-item>
      <el-form-item v-if="!isSurveySubType" label="Cost Per Interview Override" prop="cost_per_interview_override">
        <el-input-number v-model="data.client_rate" :min="0" class="w-120px inline-block" />
        USD
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-check" :loading="loading" @click="actionSave">Save</el-button>
        <el-button type="text" @click="actionCancel">Cancel</el-button>
      </el-form-item>
    </el-form>

    <el-dialog
      title="Create Company"
      width="600px"
      append-to-body
      :visible.sync="dialogVisible"
      @open="openCompanyDialog">
      <create-company
        ref="create-company"
        is-dialog
        @create="afterCreateCompany" />
    </el-dialog>
  </div>
</template>

<script>
import ToolAPI from '@/api/tool.js'
import ClientAPI from '@/api/client'
import ProjectAPI from '@/api/project'
import ComplianceAPI from '@/api/compliance'
import { debounce } from '@/utils/tool'
import mixinsEdit from '../mixins/edit'
import { mapGetters, mapState } from 'vuex'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'
import clientCustomFields from '@/components/ClientCustomFields/index'
import TimeZoneSelect from '@/components/TimeZoneSelect/Custom'
import CreateClientUser from '@/views/project/components/CreateClientUser'
import createCompany from '@/views/company/update'
import EmailEditor from '@/components/EmailEditor/ToCC'
import ClientOfficeSearch from '@/components/SelectRemoteSearch/ClientOfficeSearch/index.vue'

export default {
  name: 'BasicEdit',
  components: {
    UserSearch,
    clientCustomFields,
    CompanySearch,
    TimeZoneSelect,
    CreateClientUser,
    createCompany,
    EmailEditor,
    ClientOfficeSearch,
  },
  mixins: [mixinsEdit],
  data: function() {
    const validateSlackChannel = (rule, value, callback) => {
      const mailReg = /^[a-z0-9_-]*$/g
      setTimeout(() => {
        if (mailReg.test(value)) {
          callback()
        } else {
          callback(new Error('Please enter the correct slack channel'))
        }
      }, 100)
    }
    return {
      selectLoading: false,
      isEditClient: false,
      allowChangeClient: false,
      project_time_zone: undefined,
      client_list: [],
      test: [],
      tagsList: {
        data: [],
        count: 0,
      },
      tempTags: [],
      project_id: 0,
      rules: {
        name: [{ required: true, validator: this.uniqueValidator, trigger: 'change' }],
        sub_type: [{ required: true, message: 'project type is required', trigger: 'change' }],
        case_type: [{ required: true, message: 'case type is required', trigger: 'change' }],
        third_party_survey_url: [{ required: true, message: 'Survey URL is required', trigger: 'change' }],
        client_office_id: [{ required: true, message: 'Client Office is required', trigger: 'blur' }],
        project_client_contacts: [{ required: true, message: 'client contacts is required', trigger: 'blur' }],
        slack_channel_name: [{ required: false, validator: validateSlackChannel, trigger: 'change' }],
        client_provided_bridge_link: [{ required: false, trigger: 'blur' }],
        survey_total_charge: [{ required: false, validator: this.validateTotalCharge, trigger: 'blur' }],
        total_charge: [{ required: false, validator: this.validateTotalCharge, trigger: 'blur' }],
        rate: [{ required: false, validator: this.validateRate, trigger: 'blur' }],
        code: [{ required: false, validator: this.validateCode, trigger: 'change' }],
        decipher_id: [{ required: false, validator: this.validateDecipherSurveyIdForSync, trigger: 'change' }],
        client_type: [{ required: true, trigger: 'change' }],
        investment_target_company_ids: [
          {
            required: false,
            validator: this.validateInvestmentTarget,
            trigger: 'change',
          }],
      },
      flag: 0,
      client_contacts_option: [],
      client_custom_fields: [],
      survey_only_client: false,
      rmList: {
        data: [],
        count: 0,
      },
      pmList: {
        data: [],
        count: 0,
      },
      options: {
        currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
      },
      need_code_project_types: [],
      project_code_to_be_provided_later: false,
      investment_target_not_confirmed: false,
      dialogVisible: false,
      isConsultingFirmClient: false,
      client_blind_advisor_name: false,
      extra_member: {
        support_members: [],
      },
      need_roles_client_ids: [],
    }
  },
  computed: {
    ...mapGetters([
      'project_options', 'client_options', 'info',
    ]),
    ...mapState({
      timeZone: state => state.app.timeZone,
      isSGDB: state => state.app.isSGDB,
    }),
    isSSTOrConsultationSubType() {
      return ['Investor Call', 'Consultation'].includes(this.data.sub_type)
    },
    isSurveySubType() {
      return this.data?.sub_type === 'Survey'
    },
    needCaseType() {
      return this.isConsultingFirmClient && this.data.sub_type === 'Consultation'
    },
    permissionEditClient() {
      const SGPermission = this.isSGDB &&
        this.info.roles.find(item => ['SRM, RM, SRA, CA', 'Research Associate'].includes(item.role.name))

      const isRD = this.info.roles.find(item => item.role.name === 'RD')

      return SGPermission || isRD
    },
    permissionEditSurveyCharge() {
      return this.isSGDB || this.info.roles.some(item => ['Admin', 'EditSurveyCharge'].includes(item.role.name))
    },
    allowEditExternalRecordingContacts() {
      return this.info.roles.some(item => {
        return ['Admin', 'ExternalRecordingContacts'].includes(item.role.name)
      })
    },
    needRolesClient() {
      return this.data?.client?.preference?.preferred_workflow === 'BCG' || this.need_roles_client_ids.includes(this.data?.client_id)
    },
  },

  created() {
    this.getNeedRolesClients()
  },
  mounted() {
    this.loading = true
    this.isConsultingFirmClient = this.projectData.client_type === 'CONSULTING_FIRM'

    const SSTOrConsultation = ['Investor Call', 'Consultation'].includes(this.projectData.sub_type)
    if (SSTOrConsultation && !this.projectData.investment_target_company_ids?.length > 0) {
      this.investment_target_not_confirmed = true
    }
    this.getMemberByRole('SUPPORT_MEMBER')

    return this.getClientAndContacts(this.projectData.client_id)
      .then(data => {
        this.client_list.push(data)
        this.client_contacts_option = data['client_contacts'] || []
        this.SearchInitCompleted()
      })
      .finally(() => {
        this.project_time_zone = this.projectData.zone_id_string
        this.loading = false
      })
  },
  methods: {
    getClientCustom(val) {
      const client_custom_fields_object = {}
      if (val) {
        const custom_fields = val.custom_fields || []
        this.client_custom_fields = custom_fields.filter(item => item.type === 'PROJECT')
          .map(item => {
            client_custom_fields_object[item.field_name] = null
            return item
          })
      } else {
        this.client_custom_fields = this.projectData.client_custom_fields_config.filter(item => item.type === 'PROJECT')
          .map(item => {
            client_custom_fields_object[item.field_name] = this.data.client_custom_fields[item.field_name] || null
            return item
          })
      }
      this.data.client_custom_fields = client_custom_fields_object
    },

    SearchInitCompleted(val) {
      this.flag++
      this.initCompleted()
    },

    initCompleted() {
      if (this.flag === 4) {
        this.data = Object.assign({}, this.projectData)
        this.project_id = this.data.id
        this.getClientCustom()
        this.survey_only_client = this.onlySurveyContract(this.projectData.client_contracts)
        this.survey_only_client ? this.data.sub_type = 'Survey' : ''
        this.need_code_project_types = this.needCodeTypes(this.projectData.client)
        this.client_blind_advisor_name = this.projectData.client.preference?.blind_expert_names_in_to_client_and_portal
        if (this.client_blind_advisor_name) {
          this.data.blind_expert_profiles = true
        }
        this.$nextTick(() => {
          this.loading = false
        })
      }
    },

    // client 有且仅有 生效的survey合同时，只允许创建survey项目
    onlySurveyContract(contracts) {
      if (!contracts) return false
      const effective_contracts = contracts.filter(item => item.effective_status === 'EFFECTIVE').length
      const effective_survey_contract = contracts.filter(
        item => item.effective_status === 'EFFECTIVE' && item.applicable_project_types === 'SURVEY').length
      return !!effective_contracts && effective_contracts === effective_survey_contract
    },
    // client 有且仅有 生效的survey合同时，只允许创建survey项目
    needCodeTypes(data) {
      if (!data.compliance_preference?.rule.compliance_rules?.project_creation_rules?.remind_user_to_add_a_project_case_code) return []
      return data.compliance_preference.rule.compliance_rules.project_creation_rules.remind_user_to_add_a_project_case_code_project_types
    },

    getClientAndContacts(client_id) {
      return ClientAPI
        .getClient(client_id, 'client_contacts')
    },

    subTypeSelect() {
      if (this.isSSTOrConsultationSubType) {
        this.rules.investment_target_company_ids = [
          {
            required: true,
            validator: this.validateInvestmentTarget,
            trigger: 'change',
          }]
      } else {
        this.rules.investment_target_company_ids = [
          { required: false },
        ]
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          return
        } else {
          return false
        }
      })
    },

    formParams() {
      const params = Object.assign({}, this.data)
      const temp_tickers_index_arr = this.data.tickers.map(item => item.company_id)
      delete params.tickerIds
      delete params.investment_target_companies
      params.zone_id_string = this.project_time_zone
      params.tickers = []
      params.project_client_contacts = []
      params.client_rate = params.client_rate || null
      params.client_rate_currency = params.client_rate ? 'USD' : null
      params.decipher_id = this.data.decipher_id

      this.data.tickers.forEach(item => {
        if (this.data.tickerIds.includes(item.company_id)) {
          params.tickers.push(item)
        }
      })

      const add_tickers = this.data.tickerIds.filter(item => {
        return !temp_tickers_index_arr.includes(item)
      }).map(item => {
        return {
          project_id: this.project_id,
          company_id: item,
        }
      })

      params.tickers = [...add_tickers, ...params.tickers]

      // client
      params.client_id = this.data.client.id
      const add_client_contacts = this.data.project_client_contacts.map(item => {
        return {
          client_contact_id: item,
          project_id: this.projectData.id,
        }
      })

      params.project_client_contacts = [...add_client_contacts, ...params.project_client_contacts]

      params.project_client_contacts.forEach(item => {
        if (item.client_contact_id === this.data.project_client_contacts[0]) {
          item.client_contact_type = 'PRIMARY'
        } else {
          item.client_contact_type = 'GENERAL'
        }
      })

      params.project_client_contacts = [...params.project_client_contacts, ...this.formBcgRoles()]

      params.members = [...this.formPmParams(), ...this.formSmParams()]
      // params.start_date = moment(params.start_date).tz(this.timeZone).toISOString()
      if (typeof params.rate !== 'number' || params.sub_type !== 'Survey') {
        params.rate = null
        params.rate_currency = null
      }

      // 后端不需要更新该字段 单独更新
      delete params.slack_channel_name
      delete params.account_managers

      return params
    },

    formPmParams() {
      const result = []

      const project_manager_uid = this.data.project_manager
      const origin_manager = this.data.temp_members.find(
        item => item.role === 'PROJECT_MANAGER' && item.uid === project_manager_uid)

      if (origin_manager) {
        result.push(origin_manager)
      } else {
        result.push({
          uid: project_manager_uid,
          project_id: this.projectData.id,
          role: 'PROJECT_MANAGER',
        })
      }

      return result
    },

    formSmParams() {
      let result = []
      const temp_members_arr = this.data.temp_members.filter(item => item.role === 'SUPPORT_MEMBER')
      const temp_members_index_arr = this.data.temp_members.filter(item => item.role === 'SUPPORT_MEMBER')
        .map(item => item.uid)

      // delete
      temp_members_arr.forEach(item => {
        if (this.data.support_members.includes(item.uid)) {
          result.push(item)
        }
      })
      // add
      const add_members = this.data.support_members.filter(item => {
        return !temp_members_index_arr.includes(item)
      }).map(item => {
        return {
          uid: item,
          project_id: this.projectData.id,
          role: 'SUPPORT_MEMBER',
        }
      })
      result = [...result, ...add_members]
      return result
    },

    formBcgRoles() {
      if (!this.needRolesClient) return []
      const result = []
      const bcg_roles = [
        { name: 'project_leader', value: 'PROJECT_LEADER' },
        { name: 'principal', value: 'PRINCIPAL' },
        { name: 'partner', value: 'PARTNER' },
        { name: 'mdp', value: 'MANAGING_DIRECTOR_AND_PARTNER_MDP' },
      ]
      bcg_roles.forEach(role => {
        this.data[role.name]?.forEach(item => {
          const origin_role = this.data.project_client_contacts.find(
            c => c.client_contact_type === role.value && this.data[role.name].includes(item))
          if (origin_role) {
            result.push(origin_role)
          } else {
            result.push({
              client_contact_id: item,
              project_id: this.projectData.id,
              client_contact_type: role.value,
            })
          }
        })
      })
      return result
    },

    updateClientUser(val) {
      this.client_contacts_option.push(val)
      this.data.project_client_contacts.push(val.id)
    },

    // sg db 可以更改客户
    getClient(val) {
      const params = {
        name_like: val || undefined,
        extra: 'client_contacts,account_managers,account_managers.user,contracts,custom_fields,preference',
        page: 1,
        size: 15,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.client_list = data.list
        })
    },

    changeClient(val) {
      this.data.project_client_contacts = []
      this.customSupport()
      this.getClientCustom(val)
      this.client_contacts_option = val.client_contacts || []
      this.data.account_managers = val.account_managers || []
      this.survey_only_client = this.onlySurveyContract(val.contracts)
      this.survey_only_client ? this.data.sub_type = 'Survey' : ''
      this.isConsultingFirmClient = val.type === 'CONSULTING_FIRM'
      this.client_blind_advisor_name = val.preference?.blind_expert_names_in_to_client_and_portal
      this.data.blind_expert_profiles = this.client_blind_advisor_name
    },

    editClientInit() {
      this.checkAllowChangeClient()
        .then(() => {
          this.isEditClient = true
        })
    },

    checkAllowChangeClient() {
      return ProjectAPI.changeClientVerity(this.projectData.id, { to_client_id: 0 })
        .then(data => {
          this.allowChangeClient = data.valid
        })
    },

    // 如果用户勾选investment_target_not_confirmed，可以不必填
    validateInvestmentTarget(rule, value, callback) {
      setTimeout(() => {
        if (value.length > 0 || this.investment_target_not_confirmed) {
          callback()
        } else {
          this.isSSTOrConsultationSubType ? callback(new Error('Investment Target is required')) : callback()
        }
      }, 100)
    },

    uniqueValidator(rule, value, callback) {
      if (value === '') {
        callback(new Error('name is required'))
      } else if (value.toLowerCase() === this.projectData.name.toLowerCase()) {
        callback()
      } else {
        const data = { module: 'project', params: { name: value } }
        debounce(() => {
          ToolAPI.checkName(data).then((data) => {
            if (data) {
              callback(new Error('The name change already exists, please fill it in again'))
            } else {
              callback()
            }
          })
        }, 500)
      }
    },

    getMemberByRole(roleType) {
      if (roleType === 'SUPPORT_MEMBER') {
        this.extra_member.support_members = this.projectData.temp_members?.filter(item => item.role === roleType)
      }
      return this.projectData.temp_members?.filter(item => item.role === roleType)
    },

    handleChangeCaseType(val) {
      this.customSupport()
      if (val === 'PROPOSAL_OR_LOP') {
        this.data.is_proactive = true
      }
    },

    // https://www.notion.so/capvision/Create-Edit-Project-Add-user-to-Support-f8ad835a1d16498285cfec38df2f5b7a?pvs=4
    // 临时修改 后续通过规则完善
    customSupport() {
      if (this.isSGDB) return
      const need_push_member = [115, 98].includes(this.data.client.id) && this.needCaseType && this.data.case_type === 'DUE_DILIGENCE'
      if (need_push_member && !this.data.support_members.includes(427)) {
        this.data.support_members.push(427)
        const member = {
          uid: 427,
          user: {
            id: 427,
            name: 'Jasper Grothues',
          },
        }
        this.extra_member.support_members.push(member)
      }
    },

    afterCreateCompany(val) {
      this.projectData.investment_target_companies.push(val)
      this.data.investment_target_company_ids.push(val.id)
      this.dialogVisible = false
    },

    openCompanyDialog() {
      this.$nextTick(() => {
        this.$refs['create-company'].handleData()
      })
    },
    validateRate(rule, value, callback) {
      setTimeout(() => {
        if (typeof value !== 'number') {
          value = null
          this.data.rate_currency = null
          callback()
        } else {
          if (!this.data.rate_currency) {
            callback(new Error('Please enter the rate currency'))
          } else {
            callback()
          }
        }
      }, 100)
    },
    validateTotalCharge(rule, value, callback) {
      const key = this.data.sub_type === 'Survey' ? 'survey_total_charge_currency' : 'total_charge_currency'
      if (!this.permissionEditSurveyCharge) callback()
      setTimeout(() => {
        if (typeof value !== 'number') {
          value = null
          this.data[key] = null
          callback()
        } else {
          if (!this.data[key]) {
            callback(new Error('Please enter the charge currency'))
          } else {
            callback()
          }
        }
      }, 100)
    },

    // 客户 compliance rules 勾选对应需要user填写code时，必填；如果用户勾选project_code_to_be_provided_later，可以不必填
    validateCode(rule, value, callback) {
      setTimeout(() => {
        if (this.need_code_project_types.length < 1 || this.project_code_to_be_provided_later || value) {
          callback()
        } else {
          if (this.need_code_project_types.includes(this.data.sub_type) && !value) {
            callback(new Error('Please provide a project/case code'))
          } else {
            callback()
          }
        }
      }, 100)
    },

    validateDecipherSurveyIdForSync(rule, value, callback) {
      const regex = /^selfserve\/[a-z0-9]+\/[a-z0-9]+$/
      setTimeout(() => {
        if (!value) {
          callback()
        } else if (!regex.test(value)) {
          callback(new Error('The format should be similar to \'selfserve/xxxx/yyyyyy\''))
        } else {
          callback()
        }
      }, 100)
    },

    getNeedRolesClients() {
      return ComplianceAPI.getAppConfigEntriesByKey('DUE_DILEGENCE_USER_ROLES')
        .then(data => {
          if (data) {
            this.need_roles_client_ids = data.value.split(',').map(item => +item)
          }
        })
    },
  },
}
</script>

<style scoped lang="scss">
.w-80px {
  width: 80px;
}

.slack-channel-tip {
  color: #909399;
  line-height: 14px;
  margin-top: 5px;
}

::v-deep .el-checkbox__label {
  white-space: normal;
  word-break: break-word;
}
</style>
