<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        Information
      </div>
      <div>
        <el-link
          v-if="!isEditing"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          :underline="false"
          @click="actionEdit"
        />
      </div>
    </el-row>
    <div v-if="data" style="max-width:900px;min-height: 500px;">
      <el-form
        v-if="!isEditing"
        :model="data"
        class="show-form"
        label-width="140px"
      >
        <el-form-item label="Project Name" prop="name">
          {{ data.name }}
        </el-form-item>
        <el-form-item label="Ts ID">
          <div class="pre-wrap">{{ data.tsid }}</div>
        </el-form-item>
        <el-form-item v-if="data.exported_to" label="Outsource Region" prop="exported_to">
          {{ data.exported_to }}
        </el-form-item>
        <el-form-item label="Industry">
          {{ data.industry | getLabel(project_options.industry) }}
        </el-form-item>
        <el-form-item v-if="data.investment_target" label="Investment Target (obsoleted)">
          {{ data.investment_target }}
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Investment Target">
          <div v-for="item in data.investment_target_companies" :key="item.id">
            <router-link-company :data="item" />
            <span v-if="item.exchange">
              - {{ item.exchange }}
              <span v-if="item.stock_code"> | {{item.stock_code }}</span>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="Project Type">
          {{ data.sub_type | getLabel(project_options.sub_type) }}
        </el-form-item>
        <el-form-item v-if="showCaseType" label="Case Type">
          {{ data.case_type | getLabel(project_options.case_type) }}
        </el-form-item>
        <el-form-item v-if="isSurveySubType" label="Survey URL">
          <div class="link-box">
            <a :href="data.third_party_survey_url" target="_blank" class="link">
              {{ data.third_party_survey_url }}</a>
          </div>
        </el-form-item>
        <el-form-item v-if="isSurveySubType" label="Decipher Survey ID for status sync">
          {{ data.decipher_id || '-' }}
        </el-form-item>
        <el-form-item v-if="isSurveySubType" label="Survey Honorarium">
          <span v-if="data.rate !== null"> {{ data.rate }} / {{ data.rate_currency }} </span>
        </el-form-item>
        <el-form-item v-if="isSurveySubType" label="Client Charge per Complete">
          <span v-if="data.client_charge_per_complete !== null"> {{ data.client_charge_per_complete }} / {{ data.client_charge_per_complete_currency }} </span>
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Tickers">
          <div>
            <router-link
              v-for="(item, index) in data.tickers"
              :key="index"
              class="link enum-span"
              :to="{ name: 'CompanyDetail', params: { company_id: item.company_id } }">{{ item.company.name }}
            </router-link>
          </div>
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Proactive Project">
          {{ data['is_proactive'] | yesOrNo }}
        </el-form-item>
        <el-form-item v-if="data.client.preference && data.client.preference.tpa_client" label="TPA Project">
          {{ data['tpa_project'] | yesOrNo }}
        </el-form-item>
        <el-form-item label="Start Date">
          {{ data['start_date'] }}
        </el-form-item>
        <el-form-item label="End Date">
          {{ data['end_date'] }}
        </el-form-item>
        <el-form-item label="Project Start Time">
          {{ data['create_at'] | momentFormat('MM/DD/YYYY hh:mm A') }}
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="CN DB Project ID">
          <el-link :underline="false" class="link" type="primary" @click="goToCNProject(data.ndb_id)">
            {{ data.ndb_id }}
          </el-link>
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Time Zone">
          {{ data.zone_id_string }}
        </el-form-item>
        <br>
        <el-form-item label="Client Name">
          <router-link
            class="link overflow-break"
            :to="{ name:'ClientDetail', params: { client_id: data.client.id }}">
            {{ data.client.name }}
          </router-link>
          <el-tag v-if="data.client.preference && data.client.preference.is_outsource_client" class="ml-3">Outsource</el-tag>
        </el-form-item>
        <el-form-item label="Type">
          <div class="pre-wrap">{{ data.client.type | getLabel(client_options.type) }}</div>
        </el-form-item>
        <el-form-item label="Project Code">
          <div class="pre-wrap">{{ data.code }}</div>
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Blind expert profiles">
          <div>{{ data.blind_expert_profiles | yesOrNo }}</div>
        </el-form-item>
        <el-form-item v-if="data.client.preference && data.client.preference.enable_client_office_in_project" label="Client Office">
          <div v-if="data.client_office">{{ data.client_office.name }}</div>
        </el-form-item>
        <div v-if="data.client_custom_fields">
          <el-form-item
v-for="(value, key) in data.client_custom_fields"
                        :key="key"
                        :label="key | getName(data.client.custom_fields,'field_name','field_display_name')">
            <div v-if="typeof value === 'string'" class="pre-wrap">{{ value }}</div>
            <div v-else class="pre-wrap">{{ Array.isArray(value) ? value.join(', ') : (value || '') }}</div>
          </el-form-item>
        </div>
        <template v-if="data.client.preference && data.client.preference?.preferred_workflow === 'BCG'">
          <el-form-item label="Project Leader">
            <div v-for="(item, index) in data.project_client_contacts" :key="index">
              <div v-if="item.client_contact && item.client_contact_type === 'PROJECT_LEADER'">
                <span class="mr-8">{{ `${item.client_contact.name} ` }}</span>
                <div class="inline-block overflow-break">
                  {{showClientContactInfo(item.client_contact)}}
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="Principal">
            <div v-for="(item, index) in data.project_client_contacts" :key="index">
              <div v-if="item.client_contact && item.client_contact_type === 'PRINCIPAL'">
                <span class="mr-8">{{ `${item.client_contact.name} ` }}</span>
                <div class="inline-block overflow-break">
                  {{showClientContactInfo(item.client_contact)}}
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="Partner">
            <div v-for="(item, index) in data.project_client_contacts" :key="index">
              <div v-if="item.client_contact && item.client_contact_type === 'PARTNER'">
                <span class="mr-8">{{ `${item.client_contact.name} ` }}</span>
                <div class="inline-block overflow-break">
                  {{showClientContactInfo(item.client_contact)}}
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="Manager Director / Partner (MDP)">
            <div v-for="(item, index) in data.project_client_contacts" :key="index">
              <div v-if="item.client_contact && item.client_contact_type === 'MANAGING_DIRECTOR_AND_PARTNER_MDP'">
                <span class="mr-8">{{ `${item.client_contact.name} ` }}</span>
                <div class="inline-block overflow-break">
                  {{showClientContactInfo(item.client_contact)}}
                </div>
              </div>
            </div>
          </el-form-item>
        </template>
        <el-form-item label="Client Contacts">
          <div v-for="(item, index) in data.project_client_contacts" :key="index">
            <div v-if="item.client_contact && ['GENERAL', 'PRIMARY'].includes(item.client_contact_type)">
              <span class="mr-8">{{ `${item.client_contact.name} ` }}</span>
              <div class="inline-block overflow-break">
                {{showClientContactInfo(item.client_contact)}}
                <i v-if="item.client_contact_type === 'PRIMARY'" class="el-icon-star-on prompt-words" />
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="External Contacts for Recording">
          <div v-for="(item, index) in data.external_contacts_for_recording_list" :key="index">
            {{item}}
          </div>
        </el-form-item>
        <el-form-item label="Client Provided Bridge Link">
          <span class="pre-line">{{ data.client_provided_bridge_link || '-' }}</span>
        </el-form-item>
        <el-form-item label="Relationship Manager">
          <span v-for="(item, index) in client_am_list" :key="item.id" type="info">
            <slot v-if="index">, </slot>
            <slot v-if="item.user">{{ item.user.name }}</slot>
          </span>
        </el-form-item>
        <br>
        <el-form-item v-if="isSurveySubType" label="Survey Charge">
          {{ data.survey_total_charge }}
          <span v-if="data.survey_total_charge">/ {{ data.survey_total_charge_currency }}</span>
        </el-form-item>
        <el-form-item v-if="data.sub_type==='Patients'" label="Patients Charge">
          {{ data.total_charge }}
          <span v-if="data.total_charge">/ {{ data.total_charge_currency }}</span>
        </el-form-item>
        <br>
        <el-form-item label="Project Manager">
          {{ data.members | getMembers('PROJECT_MANAGER') }}
        </el-form-item>
        <el-form-item label="Support Members">
          {{ data.members | getMembers('SUPPORT_MEMBER') }}
        </el-form-item>
        <el-form-item label="Slack Channel">
          {{ data.slack_channel_name }}
        </el-form-item>
        <el-form-item v-if="!isSurveySubType" label="Cost Per Interview Override">
          <span v-if="data.client_rate">{{ data.client_rate }} {{ data.client_rate_currency }}</span>
        </el-form-item>

        <el-form-item v-if="!isSurveySubType" label="Google Sheet">
          <div>
            <template v-if="data.google_sheet_url">
              <a
                :href="data.google_sheet_url"
                target="_blank"
                class="text-truncate link mr-8"
                title="Project Google Sheet"
              >
                View Google Sheet
              </a>
              <el-button
                :loading="clear_chasing_loading"
                type="primary"
                plain
                @click="clearChasingInGoogleSheet"
              >
                Clear Chasing
              </el-button>
            </template>
            <el-button
              v-else
              :loading="google_sheet_loading"
              type="primary"
              @click="createGoogleSheet"
            >
              Create Google Sheet
            </el-button>
          </div>
          <div v-if="data.client.preference && data.client.preference.enable_client_google_sheet" class="mt-8">
            <template v-if="data.client_google_sheet_url">
              <a
                :href="data.client_google_sheet_url"
                target="_blank"
                class="text-truncate link mr-8"
                title="Project Google Sheet"
              >
                View External Google Sheet
              </a>
              <el-button
                :loading="client_google_sheet_loading"
                type="primary"
                plain
                @click="updateClientGoogleSheetData"
              >
                Update Data
              </el-button>
            </template>
            <el-button
              v-else
              :loading="client_google_sheet_loading"
              type="primary"
              @click="createClientGoogleSheet"
            >
              Create External Google Sheet
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <basic-edit
        v-else
        :project-data="editData"
        @done="actionEditFinish"
        @cancel="actionEditCancel"
      />
    </div>
  </el-card>
</template>

<script>
import _ from 'lodash'
import BasicEdit from './edit'
import ProjectAPI from '@/api/project'
import { mapGetters, mapState } from 'vuex'
import ProjectInformationMixins from '../mixins/'
import RouterLinkCompany from '@/components/RouterLink/Company'

export default {
  name: 'Basic',
  components: { RouterLinkCompany, BasicEdit },
  mixins: [ProjectInformationMixins],
  data() {
    return {
      google_sheet_loading: false,
      clear_chasing_loading: false,
      client_google_sheet_loading: false,
      update_sheet_loading: false,
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
    ...mapGetters([
      'project_options',
      'client_options',
    ]),
    isSurveySubType() {
      return this.data?.sub_type === 'Survey'
    },
    showCaseType() {
      return this.data.client.type === 'CONSULTING_FIRM' && this.data.sub_type === 'Consultation'
    },
  },
  mounted() {
    this.loadInfo()
  },
  methods: {
    formEditData(data) {
      const result = {}
      result.id = data.id
      result.ndb_id = data.ndb_id
      result.zone_id_string = data.zone_id_string
      result.start_date = data.start_date
      result.end_date = data.end_date
      result.name = data.name
      result.industry = data.industry
      result.sub_type = data.sub_type
      result.case_type = data.case_type
      result.survey_total_charge = data.survey_total_charge
      result.survey_total_charge_currency = data.survey_total_charge_currency
      result.total_charge = data.total_charge
      result.total_charge_currency = data.total_charge_currency
      result.third_party_survey_url = data.third_party_survey_url
      result.decipher_id = data.decipher_id
      result.rate = data.rate
      result.rate_currency = data.rate_currency
      result.client_charge_per_complete = data.client_charge_per_complete
      result.client_charge_per_complete_currency = data.client_charge_per_complete_currency
      result.investment_target = data.investment_target
      result.investment_target_company_ids = data.investment_target_company_ids
      result.investment_target_companies = data.investment_target_companies
      result.request_content = data.request_content
      result.is_proactive = data.is_proactive
      result.tpa_project = data.tpa_project
      // result.tags = data.tags
      result.tickers = data.tickers
      result.tickerIds = data.tickers.map(item => {
        return item.company_id
      })
      result.ticker_list = data.tickers.map(item => {
        return item.company
      })
      // result.tagIds = data.tags.map(item => {
      //   return item.tag_id
      // })

      result.code = data.code
      result.client = data.client
      result.client_id = data.client.id
      result.client_name = data.client.name
      result.client_type = data.client.type
      result.client_rate = data.client_rate || undefined
      result.client_rate_currency = data.client_rate_currency
      result.account_managers = data.client.account_managers
      result.client_contracts = data.client.contracts
      result.temp_client_contacts = data.project_client_contacts
      result.external_contacts_for_recording_list = data.external_contacts_for_recording_list || []
      result.client_provided_bridge_link = data.client_provided_bridge_link
      result.client_custom_fields = data.client_custom_fields || {}
      result.client_custom_fields_config = _.orderBy(data.client.custom_fields, 'id', 'desc') || []
      result.client_office = data.client_office
      result.client_office_id = data.client_office_id
      result.blind_expert_profiles = data.blind_expert_profiles

      result.project_leader = []
      result.partner = []
      result.principal = []
      result.mdp = []
      result.project_client_contacts = data.project_client_contacts.reduce((arr, cur) => {
        const bcg_role = this.client_options.contact_bcg_roles.find(item => item.value === cur.client_contact_type)
        if (bcg_role) {
          result[bcg_role.key].push(cur.client_contact_id)
          return arr
        }

        if (cur.client_contact_type === 'PRIMARY') {
          arr.unshift(cur.client_contact_id)
        } else {
          arr.push(cur.client_contact_id)
        }
        return arr
      }, [])

      result.slack_channel_name = data.slack_channel_name
      result.project_manager = undefined
      result.support_members = []
      result.temp_members = data.members

      data.members.forEach(item => {
        if (item.role === 'PROJECT_MANAGER') {
          result.project_manager = item.uid
        }
        if (item.role === 'SUPPORT_MEMBER') {
          result.support_members.push(item.uid)
        }
      })
      return result
    },

    showClientContactInfo(item) {
      if (!item.contact_infos?.length) {
        return ''
      }
      const has_email = item.contact_infos?.find(item => item.type === 'EMAIL')
      if (has_email) {
        return ` ${has_email.type.slice(0, 1)}: ${has_email.value}`
      } else {
        return ` ${item.contact_infos[0].type.slice(0, 1)}: ${item.contact_infos[0].value}`
      }
    },

    goToCNProject(project_id) {
      const cn_db_url = import.meta.env.VITE_APP_URL_DB + '#/project/consultation/index/detail/tasklist?'
      const newWin = window.open('about:blank')
      newWin.location.href = cn_db_url + `id=${project_id}`
    },

    openToLink(key, value) {
      if (key === 'EMAIL') {
        const aTag = document.createElement('a')
        aTag.href = `mailto:${value}`
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    },

    createGoogleSheet() {
      this.google_sheet_loading = true
      return ProjectAPI.createGoogleSheet(this.data.id)
        .then(data => {
          const url = data.google_sheet_url
          if (url) {
            this.data.google_sheet_url = url
            this.$store.dispatch('project/updateInfo', { ...this.projectInfo, google_sheet_url: url })
          }
        }).finally(() => {
          this.google_sheet_loading = false
        })
    },

    clearChasingInGoogleSheet() {
      this.clear_chasing_loading = true
      return ProjectAPI.clearChasingInGoogleSheet(this.data.id)
        .then(response => {
          this.$message({
            type: 'success',
            message: 'Clear chasing successfully!',
          })
        }).finally(() => {
          this.clear_chasing_loading = false
        })
    },

    createClientGoogleSheet() {
      this.client_google_sheet_loading = true
      return ProjectAPI.createClientGoogleSheet(this.data.id)
        .then(data => {
          const url = data.client_google_sheet_url
          if (url) {
            this.data.client_google_sheet_url = url
            this.$store.dispatch('project/updateInfo', { ...this.projectInfo, client_google_sheet_url: url })
          }
        }).finally(() => {
          this.client_google_sheet_loading = false
        })
    },

    updateClientGoogleSheetData() {
      this.client_google_sheet_loading = true
      return ProjectAPI.updateClientGoogleSheetData(this.data.id)
        .then(() => {
          this.$message({
            type: 'success',
            message: 'Update External Google Sheet successfully!',
          })
        }).finally(() => {
          this.client_google_sheet_loading = false
        })
    },
  },
}
</script>

<style scoped>
.enum-span:not(:last-child)::after {
  content: ', ';
  color: #303133;
}

::v-deep .show-form .el-form-item--mini.el-form-item {
  margin-bottom: 10px;
}

.link-box {
  word-break: break-word;
}
</style>
