<template>
  <div>
    <slot v-if="isCurrentRoute">
      <el-tabs tab-position="left">
        <el-tab-pane label="Email Outreach">
          <email-templates-list :project-id="project_id" />
        </el-tab-pane>
        <el-tab-pane label="Linkedin Request">
          <email-templates-list :project-id="project_id" linkedin />
        </el-tab-pane>
      </el-tabs>
    </slot>

    <router-view />
  </div>
</template>

<script>
import EmailTemplatesList from './components/EmailTemplatesList'

export default {
  name: 'CommonProjectOutreach',
  components: { EmailTemplatesList },
  data() {
    return {
      project_id: this.$route.params.project_id,
    }
  },
  computed: {
    isCurrentRoute() {
      return this.$route.name === 'CommonProjectOutreach'
    },
  },
}
</script>

<style scoped>

</style>

