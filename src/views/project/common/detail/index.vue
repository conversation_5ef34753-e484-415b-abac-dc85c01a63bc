<template>
  <div v-loading="loading" class="app-container">
    <div v-if="isProjectLoaded">
      <div>
        <h2 class="title-project-name">
          {{ project_name }}
          <el-tag type="info"># {{ project_id }}</el-tag>
          <el-tag v-if="is_custom_project" type="success">Customized service</el-tag>
          <el-tag :type="project_status | getProjectStausClass(project_options.status)">{{ project_status |
            getLabel(project_options.status) }}
          </el-tag>
        </h2>
      </div>
      <el-tabs v-model="active_tab_label" type="card" @tab-click="tabClick">
        <el-tab-pane v-for="item in tabs" :key="item.router_name" :label="item.label" :name="item.router_name" />
      </el-tabs>

      <router-view />
    </div>
  </div>
</template>

<script>
import { RouteTools } from '@/utils/tool'
import { mapGetters } from 'vuex'

export default {
  name: 'CommonDetailIndex',
  data() {
    return {
      loading: true,
      projectInfo: null,
      project_name: '',
      project_id: this.$route.params.project_id,
      project_status: '',
      project_type: '',
      project_sub_type: '',
      custom_client_id: '',
      tabs: [
        { label: 'Project Info', router_name: 'ProjectInfo' },
        { label: 'Consultant Tasks', router_name: 'ConsultantTasks' },
        { label: 'Leads', router_name: 'CommonProjectLeads' },
        { label: 'Contact Tasks', router_name: 'ContactTasks' },
        { label: 'Outreach', router_name: 'CommonProjectOutreach' },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'project_options',
    ]),
    active_tab_label: {
      get: function() {
        return this.$route.name
      },
      set: function() {
        return this.$route.name
      },
    },
    isProjectLoaded() {
      return this.projectInfo && Object.prototype.hasOwnProperty.call(this.projectInfo, 'id')
    },
    is_custom_project() {
      return ['GRM Customized Reports', 'GRM Customized Data Services', 'MF RM customized reports'].includes(this.project_sub_type) || this.project_type === 'GES_CONVEY'
    },
  },

  mounted() {
    this.getData()
  },

  methods: {
    getData() {
      this.loading = true
      return this.$store.dispatch('project/getInfo', this.project_id)
        .then(data => {
          this.projectInfo = data
          this.project_name = data.name
          this.project_id = data.id
          this.project_status = data.status
          this.project_type = data.type
          this.project_sub_type = data.sub_type
          if (this.is_custom_project) {
            this.custom_client_id = data.client_id
          }
          return RouteTools.setRouterMetaTitle(data.name, this.$route)
        })
        .finally(() => {
          this.loading = false
        })
    },

    tabClick(tab) {
      let query
      if (this.is_custom_project && tab.name === 'ContactTasks') {
        query = { client_id: this.custom_client_id }
      }
      this.$router.push({
        name: tab.name,
        query,
      })
    },
  },
}
</script>

<style scoped>
  .title-project-name {
    margin: 0 0 16px;
  }
</style>
