<template>
  <div class="task">
    <div ref="tool_switch_box" class="position-relative filter-container tool-switch" style="min-height: 45px">
      <refresh-button class="filter-item" @action="getLeadsList" />
      <transition name="fade">
        <div
          v-if="!selectedLeads.length"
          :key="1"
          ref="tool_switch_page1"
          class="inline-block tool-switch-page1">

          <add-lead
            :project-id="project.id"
            is-common-project
            class="filter-item"
            @update="getLeadsList"
          />
        </div>
        <div
          v-else
          :key="2"
          ref="tool_switch_page2"
          class="inline-block tool-switch-page2">
          <outreach-to-advisor class="filter-item" :tasks.sync="selectedLeads" :project="projectData" />
          <email-to-advisor class="filter-item" :tasks.sync="selectedLeads" :project="project" type="Leads" @update="getLeadsList" />
        </div>
      </transition>
    </div>

    <el-table
      ref="commonLeadsMultipleTable"
      v-loading="loading"
      border
      stripe
      :data="tableData"
      :span-method="arraySpanMethod"
      :row-class-name="$options.filters.rowSetIndex"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData, $refs['commonLeadsMultipleTable'],checkSelectable)"
      @sort-change="handleSortTable"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" min-width="55" :selectable="checkSelectable" />
      <el-table-column prop="sequence" label="ID" width="55" />
      <el-table-column prop="name" label="Name" min-width="138">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
          <span v-if="scope.row.advisor.gdpr_delete_by_id" class="warning pl-5">
            Expert Deleted by {{scope.row.advisor.gdpr_delete_by.name}} at {{ scope.row.advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A') }}.
          </span>
        </template>
      </el-table-column>
      <el-table-column label="Company" min-width="150">
        <template slot-scope="scope">
          <span
            v-if="scope.row.advisor_profile && scope.row.advisor_profile.company"
            class="text-truncate"
            :title="scope.row.advisor_profile.company.name">
            {{ scope.row.advisor_profile.company.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Title" min-width="150">
        <template slot-scope="scope">
          <span
            v-if="scope.row.advisor_profile"
            class="text-truncate"
            :title="scope.row.advisor_profile.position">{{ scope.row.advisor_profile.position }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Contact" min-width="100">
        <template slot-scope="scope">
          <contact-tags :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column label="TC Status" prop="tc_status" min-width="100">
        <template slot-scope="scope">
          <span class="capitalize">{{ scope.row.tc_status.toLowerCase() }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Attached By" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.create_user">{{ scope.row.create_user['name'] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Attached Date"
                       min-width="150"
                       prop="create_at"
                       sortable="column">
        <template slot-scope="scope">
          <span>{{ scope.row['create_at'] | momentFormat('MM/DD/YYYY(HH:mm)') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getLeadsList" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import ContactTags from '@/views/project/consultation/detail/components/ContactTags'
import OutreachToAdvisor from '@/views/project/consultation/detail/components/SendEmail/OutreachToAdvisor'
import EmailToAdvisor from '@/views/project/consultation/detail/components/SendEmail/EmailToAdvisor_v2'
import AddLead from '@/views/project/components/AddLead'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'LeadsIndex',
  components: {
    Pagination,
    AddLead,
    ContactTags,
    OutreachToAdvisor,
    EmailToAdvisor,
    RefreshButton,
    RouterLinkAdvisor,
  },
  data() {
    return {
      loading: false,
      project: {
        id: +this.$route.params.project_id,
      },
      projectData: this.$store.state.project.data,
      selectedLeads: [],
      clone_select: null,
      tableData: [],
      total: 0,

      searchQuery: {
        project_id: this.$route.params.project_id,
        page: 1,
        size: 20,
        sort: 'create_at asc',
        'advisor.type': 'LEAD',
        extra: 'events,advisor,advisor.jobs.company,advisor.gdpr_delete_by,advisor_profile.company,advisor.contact_infos,create_user',
      },
    }
  },

  computed: {
    ...mapGetters([
      'advisor_options',
      'project_options',
      'uid',
    ]),
  },

  mounted() {
    this.getLeadsList()
  },

  methods: {
    getLeadsList() {
      this.clone_select = this.selectedLeads.map(item => item.id)
      this.loading = true
      return ProjectAPI.getProjectAdvisorList(this.searchQuery)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
          this.clone_select ? this.selectForUser() : ''
        })
    },

    selectForUser() {
      const selected_list = this.tableData.filter(item => this.clone_select.includes(item.id))
      selected_list.forEach(item => {
        this.$refs.commonLeadsMultipleTable.toggleRowSelection(item)
      })
    },

    handleSelectionChange(val) {
      this.selectedLeads = val
    },

    handleSortTable(val) {
      if (val) {
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.prop + order
        this.getLeadsList()
      }
    },

    checkSelectable(row) {
      // GDPR 专家已删除
      return !row.advisor.gdpr_delete_by_id
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.advisor.gdpr_delete_by_id) {
        if (columnIndex === 2) {
          return [1, 4]
        } else if ([2, 3, 4, 5].includes(columnIndex)) {
          return [0, 0]
        }
      }
    },
  },
}
</script>

<style scoped>
</style>
