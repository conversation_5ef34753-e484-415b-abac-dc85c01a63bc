<template>
  <div>
    <el-row class="row-bg" :gutter="20">
      <el-col :span="8" class="card">
        <basic :data="projectInfo" @update="saveInfo" />
      </el-col>
      <el-col :span="8" class="card">
        <request :data="projectInfo" @update="saveInfo" />
      </el-col>
      <el-col :span="8" class="card">
        <notes />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import Basic from '@/views/project/common/detail/information/Basic/index'
import Request from '@/views/project/common/detail/information/request/index'
import Notes from '@/views/project/consultation/detail/information/Notes/index'
import { mapState } from 'vuex'

export default {
  name: 'CommonProjectInfoIndex',
  components: { Basic, Request, Notes },
  data() {
    return {
      project_id: this.$route.params.project_id,
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => state.project.data,
    }),
  },

  methods: {
    saveInfo(params) {
      return ProjectAPI.updateCommonProject(params).then(() => {
        return this.$store.dispatch('project/getInfo', this.project_id)
      })
    },
  },
}
</script>

<style scoped>

</style>
