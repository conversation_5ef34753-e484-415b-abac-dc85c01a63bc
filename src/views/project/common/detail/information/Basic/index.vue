<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        Information
      </div>
      <div>
        <el-link
          v-if="!is_edit"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          :underline="false"
          @click="is_edit = true"
        />
      </div>
    </el-row>
    <el-form
      v-if="!is_edit"
      ref="form"
      :model="form"
      label-width="140px"
      style="min-height:500px"
      :disabled="loading">
      <el-form-item label="Project Name">
        {{ form.name }}
      </el-form-item>
      <el-form-item label="Type">
        {{ form.type | getLabel(project_options.common_type) }}
        <span> / </span>
        {{ form.sub_type | getLabel(sub_type_options) }}
      </el-form-item>
      <el-form-item label="Industry">
        {{ form.industry | getLabel(project_options.industry) }}
      </el-form-item>
      <el-form-item v-if="form.investment_target" label="Investment Target (obsoleted)">
        {{ form.investment_target }}
      </el-form-item>
      <el-form-item label="Investment Target">
        <div v-for="item in form.investment_target_companies" :key="item.id">
          <router-link-company :data="item" />
          <span v-if="item.exchange"> - {{ item.exchange }}<span v-if="item.stock_code"> | {{ item.stock_code }}</span></span>
        </div>
      </el-form-item>
      <br>
      <el-form-item
        v-if="form.client"
        label="Client"
      >
        <router-link-client :data="form.client" />
      </el-form-item>
      <el-form-item v-if="form.contract_id" label="Client Contract">
        {{ form.contract.name }}
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Relationship Manager">
        <span v-for="(item, index) in form.client.account_managers" :key="item.id" type="info"><slot
                                                                                                 v-if="index">, </slot>
          <slot v-if="item.user">{{ item.user.name }}</slot>
        </span>
      </el-form-item>
      <br>
      <el-form-item label="Start Date">
        {{ form.start_date | momentFormat() }}
      </el-form-item>
      <el-form-item label="End Date">
        {{ form.end_date | momentFormat() }}
      </el-form-item>
      <el-form-item label="Project Manager">
        {{ form.members | getMembers('PROJECT_MANAGER') }}
      </el-form-item>
      <el-form-item label="Support Members">
        {{ form.members | getMembers('SUPPORT_MEMBER') }}
      </el-form-item>
    </el-form>
    <edit-page v-if="is_edit" :data="form" @update="handleSave" @cancel="is_edit = false" />
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import editPage from './edit'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkCompany from '@/components/RouterLink/Company'

export default {
  name: 'CommonProjectInfo',
  components: { RouterLinkClient, editPage, RouterLinkCompany },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      is_edit: false,
      form: {},
    }
  },
  computed: {
    ...mapGetters(['project_options']),
    sub_type_options() {
      let result = []
      const list = this.project_options.common_type.filter(item => item.value === this.form.type)
      if (list.length) {
        result = list[0].children
      }
      return result
    },
  },
  watch: {
    'data': {
      handler(newVal) {
        if (newVal) {
          this.form = this.data
        }
      },
      immediate: true,
    },
  },

  methods: {
    handleSave(params) {
      this.$emit('update', params)
      this.is_edit = false
    },

  },

}
</script>

<style scoped lang="scss">

.el-form-item--mini.el-form-item {
  margin-bottom: 10px;
}

.contract-select {
  width: 50%;
}

.select-options {
  display: flex;
  justify-content: space-between;

  .pay-way {
    color: #3f9eff;
  }
}
</style>
