<template>
  <el-form
    ref="form"
    :model="form"
    :rules="rules"
    label-width="140px"
    :disabled="loading">
    <el-form-item label="Project Name" prop="name">
      <el-input ref="name" v-model="form.name" disabled />
    </el-form-item>
    <el-form-item label="Type" prop="type">
      <el-select v-model="form.type" disabled>
        <el-option
          v-for="item in project_options.common_type"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <el-select v-model="form.sub_type" disabled class="mt-8">
        <el-option
          v-for="item in sub_type_options"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="Industry" prop="industry">
      <el-select v-model="form.industry">
        <el-option
          v-for="item in project_options.industry"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="form.investment_target"
      label="Investment Target (obsoleted)"
    >
      <el-input v-model="form.investment_target" disabled />
    </el-form-item>
    <el-form-item label="Investment Target" prop="investment_target_company_ids">
      <company-search v-model="form.investment_target_company_ids"
                      multiple
                      stock-label
                      :origin-list="form.investment_target_companies"
                      class="multiple-select"
                      placeholder="Investment Target"
                      clearable />
    </el-form-item>
    <br>
    <el-form-item
      label="Client"
    >
      <el-select
        v-model="form.client_id"
        filterable
        remote
        :disabled="checkClientDisabled"
        reserve-keyword
        placeholder="Search"
        class="remote-select"
        :loading="selectLoading"
        :remote-method="getClient"
        no-data-text="No matching data">
        <el-option v-for="item in clientList.data" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="checkType"
      label="Client Contract"
      prop="contract_id">
      <el-select
        v-model="form.contract_id"
        class="contract-select"
        disabled
        no-data-text="No matching data">
        <el-option v-for="item in client_contract_list"
                   :key="item.id"
                   :value="item.id"
                   class="select-options"
                   :label="item.name" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="form.client_id" label="Relationship Manager">
      <span v-for="(item, index) in form.client.account_managers" :key="item.id" type="info"><slot
        v-if="index">, </slot>{{ item.user.name }}</span>
    </el-form-item>
    <br>
    <el-form-item label="Start Date" prop="start_date">
      <el-date-picker
        v-model="form.start_date"
        type="date"
        :clearable="false"
        format="MM/dd/yyyy"
        value-format="yyyy-MM-dd"
        placeholder="Start Date"
      />
    </el-form-item>
    <el-form-item label="End Date" prop="end_date">
      <el-date-picker
        v-model="form.end_date"
        type="date"
        :clearable="false"
        format="MM/dd/yyyy"
        value-format="yyyy-MM-dd"
        placeholder="End Date"
      />
    </el-form-item>
    <el-form-item label="Project Manager" prop="project_manager">
      <user-search
        v-model="form.project_manager"
        placeholder="Search" />
    </el-form-item>
    <el-form-item label="Support Members">
      <user-search
        v-model="form.supporter"
        multiple
        class="multiple-select"
        placeholder="Search" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-check" @click="onSave(form)">Save
      </el-button>
      <el-button type="text" @click="$emit('cancel')">Cancel</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { mapState } from 'vuex'
import ClientAPI from '@/api/client'
import ContractAPI from '@/api/contract'
import ProjectAPI from '@/api/project'
import Mixin from '@/components/FormPage/mixin'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'

export default {
  name: 'CommonBasicEdit',
  components: { UserSearch, CompanySearch },
  mixins: [Mixin],
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      selectLoading: false,
      checkClientDisabled: false,
      form: {},
      clientList: [],
      client_contract_list: [],
      rules: {
        industry: [{ required: true, message: 'industry is required', trigger: 'change' }],
        project_manager: [{ required: true, message: 'project manager is required', trigger: 'change' }],
      },
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    ...mapGetters(['project_options']),
    checkType() {
      return ['GRM Customized Reports', 'GRM Customized Data Services', 'MF RM customized reports'].includes(
        this.form.sub_type) || this.form.type === 'GES_CONVEY'
    },
    sub_type_options() {
      let result = []
      const list = this.project_options.common_type.filter(item => item.value === this.form.type)
      if (list.length) {
        result = list[0].children
      }
      return result
    },
  },
  watch: {
    'form.client_id': {
      handler(newVal) {
        if (newVal && this.clientList.data) {
          this.form.client = this.clientList.data.filter(item => item.id === this.form.client_id)[0]
        }
      },
    },
  },
  mounted() {
    this.form = this.data
    this.gotoUpdate()
  },
  methods: {
    resetData() {},
    gotoUpdate() {
      this.checkClientDisabled = !!(this.checkType && this.form.client_id)
      this.formatMembers()
      this.getClient()
      this.getContract()
    },

    handleSave(val) {
      const members = [{ uid: val.project_manager, role: 'PROJECT_MANAGER' }]
      val.supporter.forEach(item => {
        members.push({ 'uid': item, 'role': 'SUPPORT_MEMBER' })
      })
      const { id, client_id, industry, end_date, start_date, contract_id, investment_target_company_ids } = val

      const params = {
        id,
        end_date,
        start_date,
        // end_date: moment.tz(end_date, this.timeZone)
        //   .toISOString(),
        // start_date: moment.tz(start_date, this.timeZone)
        //   .toISOString(),
        client_id,
        contract_id,
        industry,
        investment_target_company_ids,
        members,
      }
      if (val.contract_id) {
        return ProjectAPI.commonProjectSetContract(id, contract_id)
      }
      this.$emit('update', params)
    },

    getSubType() {
      this.form.sub_type = this.sub_type_options[0].value
    },

    getClient(val) {
      const params = {
        name_like: val || undefined,
        extra: 'client_contacts,account_managers,account_managers.user',
        status: 'EXECUTE',
        compliance_status: 'APPROVED',
        page: 1,
        size: 100,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList.data = data.list
          this.clientList.count = data.count
        })
    },

    getContract() {
      const params = {
        'client_id': this.form.client_id,
        'effective_status': 'EFFECTIVE',
      }
      return ContractAPI.getContractList(params)
        .then(data => {
          this.client_contract_list = data.list.filter(value => value.payway === 'PROJECT_BASE')
            .map(item => {
              return { name: item.name, id: item.id, payWay: item.payway }
            })
        })
    },

    formatMembers() {
      const supporter = []
      let project_manager
      this.form.members.forEach(item => {
        if (item.role === 'RESEARCH_ASSISTANT') {
          supporter.push(item.uid)
        } else {
          project_manager = item.uid
        }
      })
      this.$set(this.form, 'supporter', supporter)
      this.$set(this.form, 'project_manager', project_manager)
    },

  },
}
</script>

<style scoped lang="scss">
.el-form-item--mini.el-form-item {
  margin-bottom: 10px;
}
</style>
