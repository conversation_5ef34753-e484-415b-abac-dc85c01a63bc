<template>
  <el-card class="box-card project-setting-card" shadow="never">
    <el-row
      slot="header"
      type="flex"
      justify="space-between"
    >
      <div class="f-14">
        Request
      </div>
      <div>
        <el-link
          v-if="!isEditing"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          :underline="false"
          @click="goToEdit"
        />
      </div>
    </el-row>
    <div style="min-height:500px">
      <el-form
        v-if="!isEditing"
        :model="data"
      >
        <div class="pre-wrap" style="font-size:12px; line-height: 1.4;">{{ data.request_content }}</div>
      </el-form>
      <el-form
        v-if="isEditing"
        ref="form"
        :model="data"
      >
        <el-form-item>
          <el-input v-model="request_content" type="textarea" :autosize="{ minRows: 7, maxRows:10 }" />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-check"
            :disabled="!request_content"
            @click="actionSave">Save
          </el-button>
          <el-button type="text" @click="isEditing=false">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script>

export default {
  name: 'RequestIndex',
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {
      isEditing: false,
      request_content: '',
    }
  },

  methods: {
    goToEdit() {
      this.request_content = this.data.request_content
      this.isEditing = true
    },
    actionSave() {
      const params = {
        id: this.$route.params.project_id,
        request_content: this.request_content,
      }
      this.$emit('update', params)
      this.isEditing = false
    },
  },
}
</script>

<style scoped>

</style>
