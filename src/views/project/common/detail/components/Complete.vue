<template>
  <div>
    <el-page-header content="Task Complete" @back="actionGoBack" />
    <el-form ref="ruleForm" v-loading="loading" :model="form" :rules="rules" label-width="160px">
      <el-card class="box-card" shadow="none">
        <div slot="header">
          <span>Basic Information</span>
        </div>
        <el-form-item label="Manager" prop="task.lead_uid">
          <el-select v-model="form.task.lead_uid" class="w-200px">
            <el-option
              v-for="(item, index) in members_list"
              :key="index"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Supporter members" prop="task.support_uid">
          <el-select v-model="form.task.support_uid" class="w-200px">
            <el-option
              v-for="(item, index) in members_list"
              :key="index"
              :label="item.name"
              :value="item.id" />
          </el-select>

        </el-form-item>
        <el-form-item v-if="complete_type === 'advisor'" label="Type of Interview" prop="task.interview_type">
          <el-select v-model="form.task.interview_type" class="w-200px">
            <el-option
              v-for="option in interview_type"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="complete_type === 'advisor'" label="Start Time" required style="margin-bottom: 0;">
          <el-form-item prop="time_info.start_date" class="inline-block">
            <el-date-picker
              v-model="form.time_info.start_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              value-format="yyyy-MM-dd" />
          </el-form-item>
          -
          <el-form-item prop="time_info.start_time" class="inline-block">
            <el-time-picker
              v-model="form.time_info.start_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm" />
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="complete_type === 'advisor'" label="End Time" required style="margin-bottom: 0;">
          <el-form-item prop="time_info.end_date" class="inline-block" style="margin-bottom: 0;">
            <el-date-picker
              v-model="form.time_info.end_date"
              class="time-picker"
              type="date"
              placeholder="Date"
              value-format="yyyy-MM-dd" />
          </el-form-item>
          -
          <el-form-item prop="time_info.end_time" class="inline-block" style="margin-bottom: 0;">
            <el-time-picker
              v-model="form.time_info.end_time"
              class="time-picker"
              placeholder="Time"
              value-format="HH:mm"
              format="HH:mm"
              @change="calculateHour" />
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="complete_type !== 'advisor'" label="Date" required>
          <el-date-picker
            v-model="form.time_info.start_date"
            type="date"
            placeholder="Date"
            :disabled="has_billed_jit"
            @change="handleStartTimeChange" />
        </el-form-item>
      </el-card>

      <el-card v-if="complete_type !== 'advisor'" class="box-card" shadow="none">
        <div slot="header">
          Client Charge Information
          <span v-show="is_no_contract" class="warning ml-8"><i class="el-icon-warning" /> There is currently no valid contract!</span>
        </div>
        <el-form-item v-if="!is_custom_service" label="Client Hour" prop="task.client_hours">
          <div class="flex align-items-center">
            <el-input
              v-model="form.task.client_hours"
              :min="0"
              :step="0.25"
              :disabled="has_billed_jit"
              type="number"
              class="client-hour-input">
              <el-button
                slot="prepend"
                icon="el-icon-minus"
                @click="form.task.client_hours >= 1 ? form.task.client_hours -= 1 : form.task.client_hours = 0" />
              <el-button slot="append" icon="el-icon-plus" @click="form.task.client_hours += 1" />
            </el-input>
          </div>
        </el-form-item>
        <div v-if="is_custom_service">
          <el-form-item label="Amount" prop="task.charge_cash">
            <el-input
              v-model="form.task.charge_cash"
              :min="0"
              :max="remain_size"
              :disabled="has_billed_jit"
              type="number"
              class="client-hour-input">
              <template slot="append">USD</template>
            </el-input>
          </el-form-item>
          <el-form-item label="Remaining Amount">
            {{ remain_size }}
          </el-form-item>
        </div>

      </el-card>

      <el-card v-if="complete_type === 'advisor'" class="box-card" shadow="none">
        <div slot="header">
          <span>Payment Information</span>
        </div>
        <el-form-item label="Minutes" prop="payment.minutes">
          <el-input-number v-model="form.payment.minutes" :min="advisor_min_minutes" />
        </el-form-item>
        <el-form-item label="Amount" prop="payment.amount">
          <el-input-number v-model="form.payment.amount" :min="advisor_min_amount" class="mr-8" @input="checkW9Status" />
          <slot v-if="task">{{ task.advisor.rate_currency }}</slot>
          <el-tooltip v-if="task" effect="dark" placement="top">
            <div slot="content">
              <span>Rate: {{ task.advisor.rate }} {{ task.advisor.rate_currency }}/H</span>
            </div>
            <el-link :underline="false" icon="el-icon-question" class="tooltip-icon" />
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="w9_status!==0" label="Form W-9">
          <div v-if="[1,3,4].includes(w9_status)">
            <div v-if="w9_status===3">Under signing...</div>
            <div v-if="w9_status===4">Signed</div>
            <div class="info">Experts have received ${{ calculate_advisor_paid_this_year }} this year.</div>
          </div>
          <div v-if="w9_status===2">
            <el-checkbox v-model="form.should_send_w9_form">Send "Form W-9"
            </el-checkbox>
            <div class="info">Expert's payment has over ${{ calculate_advisor_paid_this_year }} this
              year,need to send w9 forms to experts to fill in tax
              information.
            </div>
          </div>
        </el-form-item>

        <!--        <el-divider v-if="hasPaymentInfo" />-->
        <!--        <payment-info v-if="hasPaymentInfo" :data="form.payment" :task="task" />-->
      </el-card>
      <consultant-mail
        v-if="complete_type === 'advisor'"
        ref="consultant-mail"
        is-common-project
        :form="form"
        :type="1"
        :portal="form.advisor_portal1"
        :task="task" />
    </el-form>
    <el-button type="success" @click="submitComplete">Submit</el-button>
    <el-button type="text" @click="actionGoBack">Cancel</el-button>
  </div>
</template>

<script>
import _ from 'lodash'
import { mapGetters, mapState } from 'vuex'
import { RouteTools } from '@/utils/tool'
import moment from 'moment-timezone'
import ProjectAPI from '@/api/project'
// import PaymentInfo from '@/views/project/consultation/detail/task-complete/components/PaymentInfo'
import ConsultantMail from '@/views/project/consultation/detail/task-complete/components/ConsultantMail'

export default {
  name: 'Complete',
  components: { ConsultantMail },
  data() {
    const validateDate = (rule, value, callback) => {
      const obj = this.form.time_info
      for (const key in obj) {
        if (!obj[key]) {
          return
        }
      }
      const t1 = moment(`${obj.end_date} ${obj.end_time}`)
      const t2 = moment(`${obj.start_date} ${obj.start_time}`)
      if (t1.diff(t2) < 0) {
        callback(new Error('End Time cannot be earlier than Start Time.'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      advisor_min_minutes: 0,
      advisor_min_amount: 0,
      remain_size: null,
      contract_size: null,
      used_size: null,
      has_billed_jit: false,
      task: null,
      task_id: this.$route.params.task_id,
      project_id: this.$route.params.project_id,
      complete_type: this.$route.params.type,
      members_list: [],
      bound_contract: '',
      interview_type: [
        { value: 'IN_PERSON', label: 'In Person' },
        { value: 'PHONE_CONFERENCE', label: 'Phone Conference' },
      ],
      calculate_advisor_paid_this_year: undefined,
      w9_status: 0,
      form: {
        should_send_w9_form: false,
        contract_id: '',
        task: {
          lead_uid: '',
          support_uid: '',
          interview_type: null,
          client_hours: 0,
          charge_cash: '',
        },
        time_info: {
          start_date: '',
          start_time: '',
          end_date: '',
          end_time: '',
        },
        payment: {
          minutes: 0,
          amount: 0,
          // currency: '',
          rate: 0,
          receiver_type: 'ONESELF',
          account_name: undefined,
          bank_name: undefined,
          bank_area: undefined,
          /* --- dynamic --- */
          account_number: undefined,
          account_type: 'CHECKING',
          routing_number: undefined,
          transit_number: undefined,
          swift_code: undefined,
          sort_code: undefined,
          cpf_code: undefined,
          ifsc_code: undefined,
        },
        advisor_portal1: {
          template: null,
          is_send: false,
          email: {
            subject: '',
            content: '',
          },
        },
      },
      rules: {
        'task.interview_type': [{ required: true, trigger: 'change', message: 'interview type is required!' }],
        'task.lead_uid': [{ required: true, trigger: 'change', message: 'manager is required!' }],
        'task.support_uid': [{ required: true, trigger: 'change', message: 'support members is required!' }],
        'task.client_hours': [{ required: true, trigger: 'change', message: 'client hour is required!' }],
        'task.charge_cash': [
          {
            required: true,
            trigger: 'change',
            message: 'amount is required!',
          },
          { validator: this.validateCash, trigger: 'change' },
        ],
        'time_info.start_date': [{ required: true, trigger: 'change', message: 'start date is required!' }],
        'time_info.start_time': [{ required: true, trigger: 'change', message: 'start time is required!' }],
        'time_info.end_date': [
          {
            required: true,
            trigger: 'change',
            message: 'end date is required!',
          },
          { validator: validateDate, trigger: 'change' },
        ],
        'time_info.end_time': [
          {
            required: true,
            trigger: 'change',
            message: 'end time is required!',
          },
          { validator: validateDate, trigger: 'change' },
        ],
        'payment.minutes': [{ required: true, trigger: 'change' }],
        'payment.amount': [{ required: true, trigger: 'change' }],
        'payment.account_name': [{ required: true, trigger: 'change' }],
        'payment.bank_name': [{ required: true, trigger: 'change' }],
        'payment.bank_area': [{ required: true, trigger: 'change' }],
        /* --- dynamic --- */
        'payment.account_number': [{ required: true, trigger: 'change' }, { max: 34, trigger: 'blur' }],
        'payment.routing_number': [{ required: true, trigger: 'change' }, { len: 9, trigger: 'blur' }],
        'payment.transit_number': [{ required: true, trigger: 'change' }, { len: 5, trigger: 'blur' }],
        'payment.swift_code': [{ required: true, trigger: 'change' }],
        'payment.sort_code': [{ required: true, trigger: 'change' }, { len: 6, trigger: 'blur' }],
        'payment.cpf_code': [{ required: true, trigger: 'change' }, { len: 11, trigger: 'blur' }],
        'payment.ifsc_code': [{ required: true, trigger: 'change' }, { len: 11, trigger: 'blur' }],
      },
      task_detail_extra: [
        'schedule',
        'advisor.location_id_path',
        'advisor.paid_usd_in_this_year_jit',
        // 'advisor.w9_form',
        'revenues',
        'has_billed_jit',
        'advisor.bank_account',
        'advisor_payments',
        'project.members.user',
        'advisor_normal_payment.task_amount_hours',
        'client_contact.client',
        'revenues',
      ].join(),
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    ...mapGetters(['project_options', 'contract_options']),

    is_custom_service() {
      return this.$route.query.is_custom
    },

    hasPaymentInfo() {
      return this.task && this.task.advisor.bank_account.account
    },

    is_no_contract() {
      if (this.is_custom_service) return false
      return this.bound_contract === null && this.if_use_time
    },

    if_use_time() {
      if (this.form.time_info.start_date) {
        return moment.tz(this.form.time_info.start_date, this.timeZone)
          .toISOString()
      } else {
        return undefined
      }
    },

    isUSAdvisor() {
      return this.task && this.task.advisor.location_id_path &&
        this.task.advisor.location_id_path.split(' ').includes('174')
    },
  },

  created() {
    this.init()
  },

  methods: {
    init() {
      this.loading = true
      const allPromise = [this.getTaskDetail(), this.getProjectInfo()]
      return Promise.all(allPromise)
        .then(() => {
          // remain_size=contract 剩余额度(即包含当前task的charge cash)
          if (this.form.task.charge_cash) {
            this.remain_size = this.contract_size - this.used_size + this.form.task.charge_cash
          } else {
            this.remain_size = this.contract_size - this.used_size
            this.form.task.charge_cash = this.remain_size
          }
          this.loading = false
        })
    },

    getProjectInfo() {
      return ProjectAPI.getProject(this.project_id)
        .then(data => {
          this.members_list = _.uniqBy(data['members'].map(item => {
            return { id: item.user.id, name: item.user.name }
          }), 'id')
          if (this.is_custom_service) {
            this.contract_size = data['contract'].contract_size
            this.used_size = data['contract'].used_size
          }
        })
    },

    getTaskDetail() {
      const data = {
        project_id: this.project_id,
        id: this.task_id,
        params: { extra: this.task_detail_extra },
      }
      return ProjectAPI.getTaskDetail(data)
        .then(data => {
          this.task = data

          if (this.complete_type !== 'client') {
            if (data.advisor.bank_account.account) {
              for (const key in this.form.payment) {
                if (data.advisor.bank_account.account[key]) this.form.payment[key] = data.advisor.bank_account.account[key]
              }

              // Complete时 如果是美国专家 校验 W9
              // 1:未达标 => 当年累计达到 $600
              // 2:达标 未发送W9
              // 3:达标 已发送 专家未签
              // 4:达标 专家已签 无需再发送（一旦签过，后续无需再签）
              if (data.advisor.location_id_path && data.advisor.location_id_path.split(' ').includes('174')) {
                this.calculate_advisor_paid_this_year = data.advisor.paid_usd_in_this_year_jit
                if (data.advisor.w9_form) {
                  switch (data.advisor.w9_form.status) {
                    case 'INIT':
                      this.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
                      break
                    case 'REQUESTED':
                      this.w9_status = 3
                      break
                    case 'SIGNED':
                      this.w9_status = 4
                  }
                } else {
                  this.w9_status = data.advisor.paid_usd_in_this_year_jit < 600 ? 1 : 2
                }
              }
            }
            if (['ARRANGED', 'COMPLETED'].includes(this.task.general_status)) {
              this.form.time_info.start_date = moment.tz(data.start_time, this.timeZone)
                .format('YYYY-MM-DD')
              this.form.time_info.start_time = moment.tz(data.start_time, this.timeZone)
                .format('HH:mm')
              this.form.time_info.end_date = moment.tz(data.start_time, this.timeZone)
                .format('YYYY-MM-DD')

              if (this.task.general_status === 'COMPLETED') {
                this.form.time_info.end_date = moment.tz(data.end_time, this.timeZone)
                  .format('YYYY-MM-DD')
                this.form.time_info.end_time = moment.tz(data.end_time, this.timeZone)
                  .format('HH:mm')
                this.form.task.lead_uid = data.lead_uid
                this.form.task.support_uid = data.support_uid
                this.form.task.interview_type = data.interview_type
                this.form.payment.rate = data.advisor.rate
                this.form.payment.currency = data.advisor.rate_currency
                this.form.payment.minutes = Math.round(data.advisor_normal_payment.task_amount_hours.hours * 60)
                this.form.payment.amount = data.advisor_normal_payment.task_amount_hours.amount
                data.advisor_payments.forEach(item => {
                  if (item.at_pay_stage) {
                    this.advisor_min_amount += item.amount
                    this.advisor_min_minutes += Math.round(item.hours * 60)
                  }
                })
              }
            }
          } else {
            if (this.task.general_status === 'COMPLETED') {
              this.has_billed_jit = data.has_billed_jit
              this.form.time_info.start_date = moment.tz(data.start_time, this.timeZone)
                .format('YYYY-MM-DD')
              this.form.task.client_hours = data.client_hours
              this.form.task.charge_cash = data.charge_cash
              this.form.task.lead_uid = data.lead_uid
              this.form.task.support_uid = data.support_uid
              this.getBoundContract(moment.tz(data.start_time, this.timeZone))
            }
          }
        })
    },

    handleStartTimeChange(val) {
      this.getBoundContract(val)
    },

    getBoundContract(val = true) {
      if (!val || this.is_custom_service) return
      this.loading = true
      const params = { if_use_time: this.if_use_time }
      return ProjectAPI.getTaskBoundContract(this.task.id, params)
        .then(data => {
          this.bound_contract = data
        })
        .finally(() => {
          this.loading = false
        })
    },

    calculateHour() {
      const start_time = this.form.time_info.start_time
      const end_time = this.form.time_info.end_time
      if (start_time && end_time) {
        const minutes = moment(end_time, 'HH:mm')
          .diff(moment(start_time, 'HH:mm'), 'minutes')
        if (minutes < 60) {
          this.form.client_hours = 1
          this.form.payment.minutes = 60
          this.calculatePaymentAmount()
        } else {
          const hour = parseInt(minutes / 30) * 0.5 + (minutes % 30 ? 0.5 : 0)
          this.form.client_hours = hour
          this.form.payment.minutes = hour * 60
          this.calculatePaymentAmount()
        }
      }
    },

    calculatePaymentAmount() {
      this.form.payment.amount = this.form.payment.minutes / 60 * this.task.advisor.rate
      this.checkW9Status()
    },

    checkW9Status() {
      // 计算本次付款后是否达到 $600
      if (this.isUSAdvisor) {
        const sum = this.form.payment.amount + this.task.advisor.paid_usd_in_this_year_jit
        if (sum > 600) {
          this.calculate_advisor_paid_this_year = sum
          this.w9_status = 2
        } else {
          this.calculate_advisor_paid_this_year = this.task.advisor.paid_usd_in_this_year_jit
          this.w9_status = 1
        }
      }
    },

    submitComplete() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let params = ''
          if (this.complete_type === 'advisor') {
            const { minutes, amount } = this.form.payment
            const { lead_uid, support_uid, interview_type } = this.form.task

            params = {
              task: {
                start_time: moment.tz(this.form.time_info.start_date + ' ' + this.form.time_info.start_time,
                  this.timeZone)
                  .toISOString(),
                end_time: moment.tz(this.form.time_info.end_date + ' ' + this.form.time_info.end_time, this.timeZone)
                  .toISOString(),
                lead_uid,
                support_uid,
                interview_type,
              },
              payment: {
                hours: (minutes / 60).toFixed(4),
                rate: this.task.advisor.rate,
                currency: this.task.advisor.rate_currency,
                amount,
              },
              advisor_portal1: this.form.advisor_portal1.is_send ? {
                portal: {
                  type: 'ADVISOR_POST_CALL',
                },
                email: this.form.advisor_portal1.email,
                email_template_tags: this.form.advisor_portal1.template.tags,
              } : undefined,
            }
            if (this.hasPaymentInfo) {
              Object.assign(params.payment, this.form.payment)
            }
          } else {
            const { lead_uid, support_uid, client_hours, charge_cash } = this.form.task
            params = {
              task: {
                start_time: moment.tz(this.form.time_info.start_date, this.timeZone)
                  .toISOString(),
                lead_uid,
                support_uid,
              },
            }
            if (this.is_custom_service) {
              params.task.charge_cash = +charge_cash
            } else {
              params.task.client_hours = client_hours
            }
          }

          return ProjectAPI.completeTask(this.task_id, params)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: 'Complete Task successfully!',
                type: 'success',
              })
              this.actionGoBack()
            })
        }
      })
    },

    actionGoBack() {
      return RouteTools.removeCurrentRoute(this.$route)
        .then(() => {
          this.$router.go(-1)
        })
    },

    validateCash(rule, value, callback) {
      setTimeout(() => {
        if (value > this.remain_size) {
          callback(new Error('Cannot exceed remaining amount'))
        } else {
          callback()
        }
      }, 300)
    },
  },
}
</script>

<style scoped lang="scss">
.box-card {
  margin: 8px 0;
}

.time-picker {
  width: 165px;
}

::v-deep .client-hour-input {
  width: 150px;

  input {
    text-align: center;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  .el-input-group__prepend,
  .el-input-group__append {
    padding: 0 14px;
  }
}

::v-deep .el-date-editor.el-input {
  width: 200px;
}

.contract-select {
  width: 50%;
}

.select-options {
  display: flex;
  justify-content: space-between;

  .pay-way {
    color: #3f9eff;
  }
}

::v-deep .el-collapse-item__header .el-checkbox__label {
  font-size: 15px;
}
</style>
