<template>
  <div class="inline-block">
    <el-button type="success" icon="el-icon-date" @click="initData()"> Arrange</el-button>

    <el-dialog
      title="Consultant Task Arrange"
      :visible.sync="arrangeDialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      width="50%"
    >
      <div>
        <div v-for="(item, index) in arrange_list" :key="index" class="arrange-item">
          <span class="name">{{ item.name }}</span>
          <el-select v-model="item.interview_type" class="w-200px">
            <el-option
              v-for="option in interview_type"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
          <el-date-picker
            v-model="item.start_time"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            placeholder="choose time" />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitArrange">Submit</el-button>
        <el-button type="text" @click="arrangeDialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'
import { mapState } from 'vuex'
import moment from 'moment-timezone'
import ProjectAPI from '@/api/project'

export default {
  name: 'Arrange',
  props: {
    selectedList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      arrangeDialogVisible: false,
      arrange_list: [],
      interview_type: [
        { value: 'IN_PERSON', label: 'In Person' },
        { value: 'PHONE_CONFERENCE', label: 'Phone Conference' },
      ],
    }
  },

  computed: mapState({
    timeZone: state => state.app.timeZone,
  }),

  methods: {
    initData() {
      const list = _.cloneDeep(this.selectedList)
      this.arrange_list = list.map(item => {
        item.start_time = moment.tz()
        item.end_time = ''
        item.interview_type = null
        return item
      })

      this.arrangeDialogVisible = true
    },

    submitArrange() {
      const allPromise = []
      const params = this.arrange_list.map(item => {
        return {
          task_id: item.task_id,
          data: {
            interview_type: item.interview_type,
            schedule: {
              advisor_id: item.advisor_id,
              start_time: moment.tz(item.start_time, this.timeZone)
                .toISOString(),
              // end_time: null,
            },
          },
        }
      })
      params.forEach(item => {
        allPromise.push(ProjectAPI.arrangeTask(item))
      })

      Promise.all(allPromise)
        .then(() => {
          this.$notify({
            title: 'Success',
            message: 'Arrange Tasks successfully!',
            type: 'success',
          })
        })
        .finally(() => {
          this.$emit('update')
          this.arrangeDialogVisible = false
        })
    },

  },
}
</script>

<style scoped lang="scss">
  .arrange-item {
    width: 600px;
    margin: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: baseline;

    .name {
      width: 100px;
    }
  }
</style>
