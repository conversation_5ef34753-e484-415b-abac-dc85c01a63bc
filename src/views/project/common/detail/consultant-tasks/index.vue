<template>
  <div class="task">
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getAdvisorTask" />
      <add-consultant
        class="position-absolute filter-item"
        :project="project"
        :datatemp="tableData"
        type="ADVISOR"
        :is-common="true"
      />
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :span-method="arraySpanMethod"
      :data="tableData">
      <el-table-column
        label="ID"
        prop="sequence"
        width="55" />
      <el-table-column
        label="Name">
        <template slot-scope="scope">
          <router-link-advisor :data="scope.row.advisor" />
          <span v-if="scope.row.advisor.gdpr_delete_by_id" class="warning pl-5">
            Expert Deleted by {{scope.row.advisor.gdpr_delete_by.name}} at {{ scope.row.advisor.gdpr_delete_at | momentFormat('MM/DD/YYYY hh:mm A') }}.
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Company"
        prop="advisor_profile.company.name"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.advisor_profile && scope.row.advisor_profile.company"
            class="text-truncate"
            :title="scope.row.advisor_profile.company.name">
            {{ scope.row.advisor_profile.company.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Title"
        prop="advisor_profile.position"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.advisor_profile"
            class="text-truncate"
            :title="scope.row.advisor_profile.position">{{ scope.row.advisor_profile.position }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Contact" min-width="80">
        <template slot-scope="scope">
          <contact-tags :data="scope.row.advisor" />
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        prop="status">
        <template slot-scope="scope">
          <span class="capitalize">{{ scope.row.general_status.toLowerCase() }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Rate"
      >
        <template slot-scope="scope">
          {{ scope.row.advisor.rate }} / {{ scope.row.advisor.rate_currency }}
        </template>
      </el-table-column>

      <el-table-column
        label="Cash"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.advisor_payments">{{ getCount(scope.row.advisor_payments, 'amount') }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Manager"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.lead">{{ scope.row.lead.name }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Supporter Members"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.support">{{ scope.row.support.name }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Action"
      >
        <template slot-scope="scope">
          <el-tooltip effect="dark" content="Complete" placement="top">
            <el-link
              type="primary"
              :underline="false"
              icon="el-icon-finished"
              @click="gotoComplete(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getAdvisorTask" />

  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import AddConsultant from '@/views/project/components/AddConsultant'
import ContactTags from '@/views/project/consultation/detail/components/ContactTags'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'ConsultantTaskIndex',
  components: { AddConsultant, Pagination, RouterLinkAdvisor, RefreshButton, ContactTags },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      project: {
        id: this.$route.params.project_id,
      },
      searchQuery: {
        page: 1,
        size: 20,
        project_id: this.$route.params.project_id,
        extra: 'advisor,advisor_profile,advisor_profile.company,advisor.gdpr_delete_by,advisor.jobs.company,lead,support,advisor_payments,advisor.contact_infos',
        type: 'ADVISOR_TASK',
      },
    }
  },

  mounted() {
    this.getAdvisorTask()
  },

  methods: {
    getAdvisorTask() {
      this.loading = true
      const params = Object.assign({}, this.searchQuery)
      return ProjectAPI.getProjectAdvisorList(params)
        .then(data => {
          this.tableData = data.list.filter(item => item.advisor.type !== 'LEAD')
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    gotoComplete(item) {
      return this.$router.push({
        name: 'CompleteTask',
        params: {
          task_id: item.id,
          type: 'advisor',
        },
      })
    },

    getCount(list, value) {
      let result = ''
      list.forEach(item => {
        result += item[value]
      })
      return result
    },

    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.advisor?.gdpr_delete_by_id) {
        if (columnIndex === 1) {
          return [1, 4]
        } else if ([1, 2, 3, 4].includes(columnIndex)) {
          return [0, 0]
        }
      }
    },
  },
}
</script>

<style scoped>
</style>
