<template>
  <div>
    <div class="filter-container">
      <refresh-button class="filter-item" @action="getContactTask" />
      <add-contact
        class="position-absolute filter-item"
        :project-id="searchQuery.project_id"
        :client-id="checkCustomClient"
      />
    </div>
    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData">
      <el-table-column
        label="ID"
        prop="sequence"
        width="55" />
      <el-table-column
        label="Status"
        prop="general_status"
      >
        <template slot-scope="scope">
          <span class="capitalize">{{ scope.row.general_status.toLowerCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="Contact Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :to="{ name: 'ClientContactDetail', params: { contact_id: scope.row.client_contact_id } }">
            {{ scope.row.client_contact && scope.row.client_contact.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Client"
      >
        <template v-if="scope.row.client_contact.client_id" slot-scope="scope">
          <router-link-client
            :data="{id:scope.row.client_contact.client_id,name:scope.row.client_contact.client.name}" />
        </template>
      </el-table-column>

      <el-table-column
        label="Date"
      >
        <template slot-scope="scope">
          <span v-if=" scope.row.create_at">{{ scope.row.create_at | momentFormat() }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Hours"
      >
        <template slot-scope="scope">
          <span v-if=" scope.row.client_hours">{{ scope.row.client_hours }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column
        label="Cash"
      >
        <template slot-scope="scope">
          <span v-if=" scope.row.charge_cash">{{ scope.row.charge_cash }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="Action"
      >
        <template slot-scope="scope">
          <el-tooltip effect="dark" content="Complete" placement="top">
            <el-link
              type="primary"
              :underline="false"
              icon="el-icon-finished"
              @click="gotoComplete(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="searchQuery.page"
      :limit.sync="searchQuery.size"
      @pagination="getContactTask" />

  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import AddContact from '@/views/project/components/AddContact'
import RouterLinkClient from '@/components/RouterLink/Client'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'ContactTaskIndex',
  components: { Pagination, AddContact, RouterLinkClient, RefreshButton },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      project_type: '',
      project_sub_type: '',
      custom_client_id: '',
      contract_pay_way: '',
      searchQuery: {
        page: 1,
        size: 20,
        project_id: this.$route.params.project_id,
        extra: 'client_contact,client_contact.client,project,project.contract',
        type: 'CLIENT_TASK',
      },

    }
  },

  computed: {
    checkCustomClient() {
      return this.$route.query.client_id || undefined
    },
  },

  mounted() {
    this.getContactTask()
  },

  methods: {
    getContactTask() {
      this.loading = true
      const params = Object.assign({}, this.searchQuery)
      return ProjectAPI.getProjectAdvisorList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    gotoComplete(item) {
      // project base contract 扣除 金额
      let is_project_base
      if (item.project.contract) {
        is_project_base = item.project.contract.payway === 'PROJECT_BASE'
      }
      const params = {
        task_id: item.id,
        type: 'client',
      }
      const query = {
        is_custom: this.checkCustomClient && is_project_base,
      }

      this.$router.push({
        name: 'CompleteTask',
        query,
        params,
      })
    },
  },
}
</script>

<style scoped>

</style>
