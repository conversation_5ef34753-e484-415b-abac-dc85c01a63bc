<template>
  <div class="app-container">
    <el-form ref="form" class="form-data" :model="form" :rules="rules" label-width="160px" :disabled="submitting">
      <el-form-item label="Name" prop="name">
        <el-input ref="name" v-model="form.name" placeholder="Name" />
      </el-form-item>
      <el-form-item label="Type" prop="type">
        <el-select v-model="form.type" @change="getSubType()">
          <el-option
            v-for="item in project_options.common_type"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select v-model="form.sub_type">
          <el-option
            v-for="item in sub_type_options"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Industry" prop="industry">
        <el-select v-model="form.industry" filterable placeholder="Search">
          <el-option
            v-for="item in project_options.industry"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="Investment Target" prop="investment_target">
        <company-search v-model="form.investment_target_company_ids"
                        multiple
                        stocklabel
                        class="multiple-select"
                        placeholder="Investment Target"
                        clearable />
      </el-form-item>
      <el-form-item
        label="Client"
        prop="client_id"
        :rules="{ required: checkType, message: 'client  is required', trigger: 'blur' }">
        <el-select
          v-model="form.client_id"
          filterable
          remote
          reserve-keyword
          placeholder="Search"
          class="remote-select"
          :loading="selectLoading"
          :remote-method="getClient"
          no-data-text="No matching data">
          <el-option v-for="item in clientList.data" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <!--        <el-tooltip placement="right" class="ml-8">-->
        <!--          <div slot="content">Only clients in execute state can create projects.</div>-->
        <!--          <span class="el-icon-question tooltip-icon" />-->
        <!--        </el-tooltip>-->
      </el-form-item>
      <el-form-item v-if="form.client_id" label="Relationship Manager">
        <el-tag v-for="item in client_am_list" :key="item.id" type="info">{{ item.user.name }}</el-tag>
      </el-form-item>
      <el-form-item
        v-if="checkType"
        label="Client Contract"
        prop="contract_id">
        <el-select
          v-model="form.contract_id"
          class="contract-select"
          no-data-text="No matching data">
          <el-option v-for="item in client_contract_list"
                     :key="item.id"
                     :value="item.id"
                     class="select-options"
                     :label="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="Start Date" prop="start_date">
        <el-date-picker
          v-model="form.start_date"
          type="date"
          :clearable="false"
          format="MM/dd/yyyy"
          value-format="yyyy-MM-dd"
          placeholder="Start Date"
        />
      </el-form-item>
      <el-form-item label="End Date" prop="end_date">
        <el-date-picker
          v-model="form.end_date"
          type="date"
          :clearable="false"
          format="MM/dd/yyyy"
          value-format="yyyy-MM-dd"
          placeholder="End Date"
        />
      </el-form-item>
      <el-form-item label="Project Manager" prop="project_manager">
        <user-search
          v-model="form.project_manager"
          placeholder="Search" />
      </el-form-item>
      <el-form-item label="Support Members">
        <user-search
          v-model="form.supporter"
          clearable
          multiple
          class="multiple-select"
          placeholder="Search" />
      </el-form-item>
      <el-form-item label="Request Content" prop="request_content">
        <el-input v-model="form.request_content" placeholder="Request Content" type="textarea" :rows="6" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSave()">Create</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import ContractAPI from '@/api/contract'
import moment from 'moment'
import { mapState, mapGetters } from 'vuex'
import Mixin from '@/components/FormPage/mixin'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import CompanySearch from '@/components/SelectRemoteSearch/CompanySearch'

export default {
  name: 'CommonProjectCreate',
  components: { UserSearch, CompanySearch },
  mixins: [Mixin],
  data() {
    return {
      selectLoading: false,
      clientList: {
        list: [],
        count: 0,
      },
      client_am_list: [],
      client_contract_list: [],
      form: this.initForm(),
      rules: {
        type: [{ required: true, message: 'type is required', trigger: 'change' }],
        name: [{ required: true, validator: this.uniqueValidator, trigger: 'change' }],
        industry: [{ required: true, message: 'industry is required', trigger: 'change' }],
        project_manager: [{ required: true, message: 'project manager is required', trigger: 'change' }],
      },
      sub_type_options: [],
    }
  },

  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),
    ...mapGetters(['project_options', 'contract_options']),
    checkType() {
      return ['GRM Customized Reports', 'GRM Customized Data Services', 'MF RM customized reports'].includes(
        this.form.sub_type) || this.form.type === 'GES_CONVEY'
    },

  },

  watch: {
    'form.client_id': {
      handler(newVal) {
        if (newVal) {
          const _list = this.clientList.data.filter(item => item.id === +this.form.client_id)
          this.client_am_list = _list.length ? _list[0].account_managers : []
          if (this.checkType) this.getContract()
        }
      },
    },

    'checkType': {
      handler(newVal) {
        if (newVal && this.form.client_id) {
          this.getContract()
        }
      },
    },

  },

  mounted() {
    this.getClient()
    this.initFormType()
  },

  methods: {
    initForm() {
      return {
        name: '',
        type: '',
        sub_type: '',
        client_id: '',
        client_contract: '',
        industry: '',
        investment_target_company_ids: [],
        start_date: moment
          .tz(this.timeZone)
          .format('YYYY-MM-DD'),
        end_date: null,
        project_manager: '',
        supporter: [],
        request_content: '',
        status: 'IN_PROCESS',
      }
    },

    initFormType() {
      this.form.type = this.$route.params.project_type || ''
      this.getSubType()
    },

    handleSave(val) {
      const members = [{ uid: val.project_manager, role: 'PROJECT_MANAGER' }]
      val.supporter.forEach(item => {
        members.push({ 'uid': item, 'role': 'SUPPORT_MEMBER' })
      })
      const {
        name,
        type,
        sub_type,
        client_id,
        industry,
        request_content,
        end_date,
        start_date,
        contract_id,
        investment_target_company_ids,
      } = val
      const params = {
        name,
        type,
        sub_type,
        end_date,
        start_date,
        // end_date: moment.tz(end_date, this.timeZone)
        //   .toISOString(),
        // start_date: moment.tz(start_date, this.timeZone)
        //   .toISOString(),
        client_id,
        contract_id,
        industry,
        investment_target_company_ids,
        request_content,
        members,
      }

      return ProjectAPI.commonProjectCreate(params)
        .then((data) => {
          this.$router.push({ name: 'CommonProjectDetail', params: { project_id: data.id } })
          this.$notify({
            title: 'Success',
            message: 'Created successfully!',
            type: 'success',
          })
        })
    },

    getSubType() {
      this.sub_type_options = this.project_options.common_type.filter(item => item.value === this.form.type)[0].children
      this.form.sub_type = this.sub_type_options[0].value
    },

    getClient(val) {
      const params = {
        name_like: val || undefined,
        extra: 'client_contacts,account_managers,account_managers.user,contracts',
        // status: 'EXECUTE',
        // 'contracts.approval_status_in': 'PENDING_APPROVAL,APPROVED',
        // compliance_status: 'APPROVED',
        page: 1,
        size: 100,
      }
      this.selectLoading = true
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList.data = data.list
          this.clientList.count = data.count
        })
    },

    getContract() {
      const params = {
        'client_id': this.form.client_id,
        'effective_status': 'EFFECTIVE',
      }
      return ContractAPI.getContractList(params)
        .then(data => {
          this.client_contract_list = data.list.filter(value => value.payway === 'PROJECT_BASE')
            .map(item => {
              return { name: item.name, id: item.id, payWay: item.payway }
            })
        })
    },

    uniqueValidator(rule, value, callback) {
      if (value === '') {
        callback(new Error('name is required'))
      } else {
        callback()
        // 美国要求不验证名字重复
        //   const data = { module: 'project', params: { name: value } }
        //   debounce(() => {
        //     ToolAPI.checkName(data)
        //       .then((data) => {
        //         if (data) {
        //           callback(new Error('The name change already exists, please fill it in again'))
        //         } else {
        //           callback()
        //         }
        //       })
        //   }, 500)
      }
    },
  },
}
</script>

<style scoped>
.tooltip-icon {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
  vertical-align: middle;
}
</style>
