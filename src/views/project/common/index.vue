<template>
  <div class="app-container">
    <div class="filter-container flex justify-content-center align-items-center flex-wrap">
      <div>
        <refresh-button class="filter-item" @action="getList" />
        <user-search
          v-model="searchQuery.member_ids"
          multiple
          clearable
          :extra-member="[{user:{name:name,id:uid},uid:uid}]"
          placeholder="Project Manager"
          class="w-200px filter-item"
          @init-completed="userSearchInitCompleted"
          @change="handleSearch" />
        <el-input
          v-model="searchQuery.name_like"
          placeholder="Project Name"
          class="w-200px filter-item"
          @input="handleSearch" />
        <el-button type="success" class="filter-item" icon="el-icon-plus" @click="actionGotoCreate()">New
        </el-button>
      </div>
      <div class="tabs-stackOverflow filter-item">
        <template v-for="(item, index) in statusOptionsTags">
          <el-tag
            :key="index"
            :class="{active: tags_status === item.value }"
            @click.native="filterStatusSearch(item)">
            {{ item.label }}
            <span class="status-count">{{ getAggregationsCount(item.value) }}</span>
          </el-tag>
        </template>
      </div>
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData">
      <el-table-column
        label="Name">
        <template slot-scope="scope">
          <router-link
            class="text-truncate link"
            :title="scope.row.name"
            :to="{ name: 'CommonProjectDetail', params: { project_id: scope.row.id } }">
            {{ scope.row.name }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Type"
      >
        <template slot-scope="scope">
          {{ scope.row.sub_type }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
      >
        <template slot-scope="scope">
          <el-dropdown
            placement="bottom-start"
            trigger="hover"
            @command="changeStatus"
          >
            <el-link
              :type="scope.row.status | getProjectStausClass(project_options.status)"
              :underline="false"
            >{{ scope.row.status | getLabel(project_options.status) }}
              <i class="el-icon-arrow-down el-icon--right" />
            </el-link>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in project_options.status"
                :key="item.value"
                :command="{id:scope.row.id,status:item.value}">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column
        label="Start Date"
      >
        <template slot-scope="scope">
          {{ scope.row.start_date | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column
        label="Manager"
      >
        <template slot-scope="scope">
          {{ scope.row.members | getMembers('PROJECT_MANAGER') }}
        </template>
      </el-table-column>
      <el-table-column
        label="Support Members"
      >
        <template slot-scope="scope">
          {{ scope.row.members | getMembers('RESEARCH_ASSISTANT') }}
        </template>
      </el-table-column>

    </el-table>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/tool'
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import RouterTools from '@/utils/tool-router'
import RefreshButton from '@/components/RefreshButton/index'

export default {
  name: 'CommonProjectIndex',
  components: { RefreshButton, Pagination, UserSearch },
  data() {
    return {
      firstVisit: true,
      selectLoading: false,
      loading: false,
      aggregations_status_list: [],
      initAggregationsFlag: false,
      tableData: [],
      total: 0,
      tags_status: 'TOTAL',
      searchQuery: {
        name_like: undefined,
        ids: undefined,
        status: undefined,
        member_ids: [],
        page: 1,
        size: 10,
        extra: 'members,members.user',
        type_in: '',
        aggregation: 'status',
        sort: 'id desc',
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid', 'name', 'project_options',
    ]),

    statusOptionsTags() {
      return this.project_options.status.concat([
        { value: 'TOTAL', label: 'Total' },
      ])
    },
  },
  mounted() {
    const params = RouterTools.resolveRouteQuery(this.$route, this.searchQuery, ['member_ids'])

    this.firstVisit = Object.keys(params).length === 0

    if (!this.firstVisit) {
      this.tags_status = this.searchQuery.status || 'TOTAL'
      this.getList()
    }

    this.searchQuery.type_in = this.getCurrentProjectType()
  },
  methods: {
    getList(val) {
      this.loading = true

      RouterTools.saveQueryToRouter(this.searchQuery)

      const params = this.formatParams(this.searchQuery)
      return ProjectAPI.getProjectList(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.count
          if (data['aggregations'] && !val) {
            this.aggregations_status_list = data['aggregations'].status
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      debounce(() => {
        this.searchQuery.status = undefined
        this.searchQuery.page = 1
        this.tags_status = 'TOTAL'
        this.getList()
      }, 500)
    },

    filterStatusSearch(item) {
      this.tags_status = item.value
      this.searchQuery.status = item.value === 'TOTAL' ? undefined : item.value
      this.getList(true)
    },

    getAggregationsCount(value) {
      let result = 0
      let total_count = 0
      if (value === null) return ''

      this.aggregations_status_list.forEach(item => {
        if (item.value === value) {
          result = item.count
        }
        total_count += item.count
      })
      if (value === 'TOTAL') {
        result = total_count
      }
      return result
    },

    actionGotoCreate() {
      this.$router.push({ name: 'CreateCommonProject', params: { project_type: this.getCurrentProjectType() } })
    },

    userSearchInitCompleted() {
      if (this.firstVisit) {
        this.searchQuery.member_ids = (this.uid ? [this.uid] : [])
        this.getList()
        this.firstVisit = false
      }
    },

    changeStatus(value) {
      return ProjectAPI.updateCommonProject(value)
        .then(() => {
          this.getList()
        })
    },

    formatParams(params) {
      const data = Object.assign({}, params)

      data.member_ids = data.member_ids.length
        ? data.member_ids
          .filter(item => item)
          .join()
        : undefined

      Object.keys(data)
        .forEach(key => {
          if (!data[key]) {
            delete data[key]
          }
        })

      return data
    },

    getCurrentProjectType() {
      return this.$route.path
        .split('/')
        .pop()
        .toUpperCase()
    },
  },
}
</script>

<style scoped>
::v-deep .el-dropdown {
  font-size: 12px;
}
</style>
