<template>
  <div>
    <el-form-item label="Copy/Paste Contacts">
      <div class="flex">
        <el-input
          v-model="str_to_be_parsed"
          type="text"
          placeholder="Please enter the contacts emails to parse"
          @keyup.enter.native="handleParseEmails"
        />
        <el-button
          type="primary"
          class="ml-3"
          :loading="loading"
          @click="handleParseEmails"
        >
          Parse Emails
        </el-button>
      </div>
      <div v-if="parsed_contacts.length">
        <span class="info">Matched:</span>
        <el-tag
          v-for="item in parsed_contacts"
          :key="item.email"
          type="success"
          class="mr-8 cursor-pointer"
          @click="createOrEditClientContact(item)"
        >
          {{ item.email }} -
          <router-link
            class="text-truncate link"
            :to="{ name: 'ClientContactDetail', params: { contact_id: item.contact.id } }"
            target="_blank"
          >
            {{ item.contact.name }}
          </router-link>
          <i class="el-icon-edit-outline" />
        </el-tag>
        <el-button type="primary" class="el-button--xs" @click="handleQuickAdd">Quick Add To Roles</el-button>
      </div>
      <div v-if="pending_contacts.length">
        <span class="info">Unmatched:</span>
        <el-tag
          v-for="(item, index) in pending_contacts"
          :key="item.email"
          type="primary"
          class="mr-8 cursor-pointer"
          @click="createOrEditClientContact(item)"
        >
          {{ item.email }}
          <el-link
            type="danger"
            :underline="false"
            class="vertical-top"
            @click.stop="removeClientContact(item, index)"
          >
            <i class="el-icon-error danger" />
          </el-link>
        </el-tag>
      </div>
    </el-form-item>

    <create-contact-dialog
      ref="createContactDialog"
      bcg
      :client-id="clientId"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import CreateContactDialog from '@/views/client/detail/components/CreateContactDialog.vue'

export default {
  name: 'CopyPasteContacts',
  components: { CreateContactDialog },
  props: {
    clientId: Number || String,
    clientContacts: Array,
  },
  data() {
    return {
      loading: false,
      str_to_be_parsed: '',
      parsed_contacts: [],
      pending_contacts: [],
    }
  },
  methods: {
    handleParseEmails() {
      this.parsed_contacts = []
      this.pending_contacts = []
      const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z0-9]{2,}/g
      const result = this.str_to_be_parsed.match(emailPattern)
      result?.forEach(email => {
        if (!email.trim()) return
        const contact = this.clientContacts.find(
          user => user.contact_infos_without_mosaic.some(contact_info => contact_info.value === email))
        const data = { email, contact }
        if (contact) {
          this.parsed_contacts.push(data)
        } else {
          this.pending_contacts.push(data)
        }
      })
    },
    createOrEditClientContact(val) {
      this.$refs['createContactDialog'].quickCreateEditBcgContact(val, this.clientId)
    },
    removeClientContact(val, index) {
      this.str_to_be_parsed = this.str_to_be_parsed.replace(val.email, '')
      this.pending_contacts.splice(index, 1)
    },
    handleQuickAdd() {
      this.$emit('quick-add', this.parsed_contacts)
    },
  },
}
</script>

<style scoped>

</style>
