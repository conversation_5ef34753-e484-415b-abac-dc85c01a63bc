<template>
  <div class="inline-block">
    <el-button
      type="success"
      @click.stop="createDialogVisible=true"
    >
      <svg-icon icon-class="plus" />
      Add Lead
    </el-button>
    <el-dialog
      title="Create Lead"
      :visible.sync="createDialogVisible"
      :close-on-click-modal="false"
      width="60%"
      append-to-body
      center
      @close="closeDialog"
    >

      <div v-if="investment_target_conflict || duplicate_advisor.id" class="duplicate-box">
        <el-alert v-if="investment_target_conflict" type="error" :closable="false" show-icon>
          The current employers of the expert conflict with the investment target of the project.
          <span v-if="result_lead.id" class="ml-3">[<router-link-advisor :data="result_lead" />]</span>
        </el-alert>
      <div v-if="duplicate_advisor.id">
        <el-alert type="error" :closable="false" show-icon>Contact info
          <span v-if="duplicate_contact_info.id">({{duplicate_contact_info.value}})</span>
          already existed in DB.
          <span v-if="isBlacklistAdvisor">This expert is on the blacklist.</span>
        </el-alert>
        <div v-if="!isBlacklistAdvisor && !investment_target_conflict" class="text-center mt-3">
          <el-button
            type="primary"
            plain
            class="mr-normal"
            :loading="add_lead_loading"
            @click="addLeadToList(duplicate_advisor)">
            Add Existing Expert to Leads
          </el-button>
          <add-to-client-task
            :advisor="duplicate_advisor"
            :loading="add_task_loading"
            btn-text="Add Existing Expert to Client Tasks"
            @add="addLeadToClientTask" />
        </div>
      </div>
      </div>

      <create-lead v-if="createDialogVisible" is-dialog @save="createLead" />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ProjectAPI from '@/api/project'
import CreateLead from '@/views/consultant/update'
import AddToClientTask from '@/views/project/consultation/detail/components/AddToClientTask'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'

export default {
  name: 'AddLead',
  components: { RouterLinkAdvisor, CreateLead, AddToClientTask },
  props: {
    projectId: [Number, String],
    isCommonProject: {
      type: Boolean,
      default: false,
    },
    project: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      createDialogVisible: false,
      add_lead_loading: false,
      add_task_loading: false,
      investment_target_conflict: false,
      result_lead: {},
      duplicate_advisor: {},
      duplicate_contact_info: {},
    }
  },

  computed: {
    ...mapGetters([
      'uid',
    ]),

    isBlacklistAdvisor() {
      return this.duplicate_advisor?.status === 'BLACKLIST'
    },
  },

  methods: {
    createLead(val) {
      this.result_lead = val.entity.id ? val.entity : (val.duplicate || {})
      checkConflictWithInvestmentTarget(val.entity.id || val.duplicate?.id, this.project.id)
        .then(result => {
          this.investment_target_conflict = result
          if (val.entity.id && !result) {
            this.addLeadToList(val.entity)
          } else {
            this.duplicate_advisor = val.duplicate || {}
            this.duplicate_contact_info = val.duplicate_contact_info || {}
          }
        })

      function checkConflictWithInvestmentTarget(advisor_id, project_id) {
        if (!advisor_id) return Promise.resolve(false)
        const params = {
          project_id: project_id,
          advisor_ids: [advisor_id],
        }
        return ProjectAPI.checkAdvisorConflictWithInvestmentProjectTarget(params)
          .then(data => {
            return !!data.length
          })
      }
    },

    addLeadToList(val) {
      this.add_lead_loading = true
      const params = {
        pm_id: this.uid,
        advisor_id: val.id,
        creation_source: 'LEADS_TAB_CREATE',
      }
      if (this.isCommonProject) {
        params.type = 'ADVISOR_TASK'
      } else {
        params.client_contact_id = null
        params.willingness = 'UNKNOWN'
      }

      return ProjectAPI.addAdvisor(this.projectId, [{ task: params }])
        .then(() => {
          this.$emit('update')
          this.$message({
            showClose: true,
            message: 'Create lead successfully!',
            type: 'success',
          })
          this.closeDialog()
        }, error => {
          this.$message({
            showClose: true,
            message: error,
            type: 'error',
          })
        })
        .finally(() => {
          this.add_lead_loading = false
        })
    },

    addLeadToClientTask(angleID) {
      this.add_task_loading = true
      const params = [{
        task: {
          angle_id: angleID,
          advisor_id: this.duplicate_advisor.id,
          client_contact_id: null,
          willingness: 'UNKNOWN',
        },
      }]
      return ProjectAPI.addAdvisor(this.projectId, params)
        .then(() => {
          this.$message({
            showClose: true,
            message: 'Added successfully!',
            type: 'success',
          })
          this.closeDialog()
        })
        .finally(() => {
          this.add_task_loading = false
        })
    },

    closeDialog() {
      this.investment_target_conflict = false
      this.result_lead = {}
      this.duplicate_advisor = {}
      this.duplicate_contact_info = {}
      this.createDialogVisible = false
    },
  },
}
</script>

<style scoped>
.duplicate-box{
  margin-bottom: 25px;
  border:1px solid #DCDFE6;
  border-radius: 4px;
  padding: 15px
}
</style>
