<template>
  <div class="inline-block">
    <el-button
      type="success"
      @click.stop="gotoAddContact"
    >
      <svg-icon icon-class="plus" />
      Add Contact
    </el-button>

  </div>
</template>

<script>
export default {
  name: 'AddContact',
  props: {
    projectId: {
      type: [Number, String],
      default: undefined,
    },
    clientId: {
      type: [Number, String],
      default: undefined,
    },
  },
  data() {
    return {}
  },
  methods: {
    gotoAddContact() {
      this.$router.push({
        name: 'AddContact',
        params: {
          project_id: this.projectId,
        },
        query: {
          client_id: this.clientId,

        },
      })
    },
  },
}
</script>

<style scoped>

</style>
