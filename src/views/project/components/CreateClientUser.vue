<template>
  <div class="inline-block">
    <el-button
      :disabled="!permissionEdit"
      type="success"
      class="filter-item"
      icon="el-icon-plus"
      @click="createContact()"
    >New
    </el-button>
    <el-dialog
      :visible.sync="contactDialogVisible"
      width="620px"
      append-to-body
      :destroy-on-close="true"
      title="Create Client User"
    >
      <el-form ref="contactValidateForm" :model="form" label-width="100px">
        <el-form-item
          prop="firstname"
          label="First Name"
          :rules="[{ required: true, message: 'Please enter first name.', trigger: 'change' },]">
          <el-input
            v-model="form.firstname"
            placeholder="First Name"
            class="w-200px"
          />
        </el-form-item>
        <el-form-item
          prop="lastname"
          label="Last Name"
          :rules="[{ required: true, message: 'Please enter last name.', trigger: 'change' },]">
          <el-input
            v-model="form.lastname"
            placeholder="Last Name"
            class="w-200px"
          />
        </el-form-item>
        <el-form-item
          prop="location_id"
          label="Location"
          :rules="[{ required: true, message: 'Location is required.', trigger: 'change' },]">
          <location v-model="form.location_id" class="w-200px filter-item" />
        </el-form-item>
        <el-form-item
          prop="office_id"
          label="Office">
          <el-select
            v-model="form.office_id"
            :loading="loading"
            class="w-200px filter-item"
            @change="checkLocation()"
          >
            <el-option
              v-for="item in office_option"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="types"
          label="Type"
          :rules="[{ required: true, message: 'Type is required.', trigger: 'change' },]">
          <el-checkbox-group
                             v-model="form.types"
                             class="checkbox-group-vertical">
            <el-checkbox
                          v-for="item in client_options.contact_person_type"
                         :key="item.value"
                         :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="bcg"
          prop="roles"
          label="Role"
          :rules="[{ required: true, message: 'Role is required.', trigger: 'change' }]"
        >
          <el-checkbox-group
            v-model="form.roles"
            class="checkbox-group-vertical flex flex-wrap"
          >
            <el-checkbox
              v-for="item in client_options.contact_bcg_roles"
              :key="item.value"
              :label="item.value"
              style="margin-right: 1rem;"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Status" required prop="status">
          <el-select
            v-model="form.status"
            class="w-200px filter-item"
            placeholder="Status"
          >
            <el-option
              v-for="item in client_options.contact_status"
              :key="item.value"
              :disabled="!allowChangeStatus && item.value !== 'LEFT_COMPANY'"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-for="(item, index) in form.contact_infos"
          :key="index"
          :label="!index ? 'Contact' : ''"
          required
          :prop="'contact_infos.' + index + '.value'"
          :rules="[{ validator: getValidType(item.type), trigger: ['change'] }]">
          <el-input
            v-model="item.value"
            class="input-with-select contact-value-input"
          >
            <el-select slot="prepend" v-model="item.type" style="width: 100px;" @change="changeType(item,index)">
              <el-option
                v-for="option in contact_info_options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-input>
          <el-link
            v-if="!index"
            type="primary"
            icon="el-icon-plus"
            class="icon-font"
            :underline="false"
            @click="addContactItem" />
          <el-link
            v-if="index"
            type="danger"
            icon="el-icon-delete"
            class="icon-font"
            :underline="false"
            @click="removeContactItem(index)" />
          <el-tooltip class="item" effect="dark" content="Main Contact" placement="right">
            <el-link
              v-if="item.is_main"
              type="success"
              class="icon-font"
              :underline="false"
              icon="el-icon-star-on"
            />
            <el-link
              v-else
              class="icon-font"
              :underline="false"
              icon="el-icon-star-off"
              @click="setMainContact(item, index)" />
          </el-tooltip>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-check"
          :disabled="!form.firstname || !form.lastname"
          :loading="submitting"
          @click="submitForm"
        >
          Save
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import { mapGetters } from 'vuex'
import Location from '@/components/Location'
import _ from 'lodash'

export default {
  name: 'CreateClientUser',
  components: { Location },
  props: {
    clientId: [Number, String],
    allowChangeStatus: {
      type: Boolean,
      default: false,
    },
    contactType: {
      type: String,
      default: null,
    },
    bcg: Boolean,
    bcgRole: String,
  },

  data() {
    const validatePhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('please input the value'))
      } else {
        const reg = /^\+?\d+(?:-?\d+){5,}$/
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error('please enter a valid phone number'))
        }
      }
    }
    const validateEmail = (rule, value, callback) => {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      if (value === '') {
        callback(new Error('please input the value'))
      } else {
        setTimeout(() => {
          const more_than_one = /,|;|，|；/g
          if (value.match(more_than_one) && value.match(more_than_one).length) {
            callback(new Error('Only one entry can be made into an input box.'))
          } else if (mailReg.test(value)) {
            callback()
          } else {
            callback(new Error('Please enter the correct email address'))
          }
        }, 100)
      }
    }
    return {
      checkPhone: validatePhone,
      checkEmail: validateEmail,
      contactDialogVisible: false,
      loading: false,
      submitting: false,
      form: this.initForm(),
      office_option: [],
      contact_info_options: [
        {
          value: 'EMAIL',
          label: 'Email',
        },
        {
          value: 'PHONE',
          label: 'Phone',
        },
        {
          value: 'LINKEDIN_URL',
          label: 'Linkedin',
        },
      ],
    }
  },

  computed: {
    ...mapGetters([
      'client_options',
      'roles',
    ]),
    permissionEdit() {
      /* KM/Sales/SKM/Compliance/Admin/AdminExcludeCompliance edit client ，Contact*/
      return this.roles.some(item => {
        return ['Admin', 'AdminExcludeCompliance', 'Research Associate', 'Compliance', 'AM / Sales', 'SRM, RM, SRA, CA'].includes(item.role.name)
      })
    },
  },

  methods: {
    initForm() {
      return {
        firstname: '',
        lastname: '',
        types: this.contactType ? [this.contactType] : ['COMMON'],
        coverages: [],
        product_of_interests: [],
        roles: this.bcgRole ? [this.bcgRole] : [],
        status: 'PROSPECT',
        office_id: undefined,
        location_id: undefined,
        contact_infos: [
          {
            owner_type: 'CLIENT_CONTACT',
            type: 'EMAIL',
            value: '',
            is_main: true,
          }],
      }
    },

    createContact() {
      this.contactDialogVisible = true
      this.loading = true
      this.getOffices().finally(() => {
        this.loading = false
      })
    },

    getOffices() {
      return ClientAPI.getClientOffices(this.clientId).then(data => {
        this.office_option = data['list']
      })
    },

    checkLocation() {
      if (!this.form.location_id) {
        this.form.location_id = this.office_option.find(
          item => item.id === this.form.office_id)['location_id']
      }
    },

    addContactItem() {
      this.form.contact_infos.push({
        owner_type: 'CLIENT_CONTACT',
        type: 'EMAIL',
        value: '',
        is_main: false,
      })
      this.checkMainContact()
    },

    removeContactItem(index) {
      this.form.contact_infos.splice(index, 1)
      this.checkMainContact()
    },

    // 每种类型的联系方式 只允许有一个 main联系方式

    setMainContact(item, index) {
      if (item.is_main && this.form.contact_infos.length > 1) {
        this.$set(item, 'is_main', false)
      } else {
        this.$set(item, 'is_main', true)
        this.form.contact_infos.forEach((info, ind) => {
          if (info.type === item.type && ind !== index) {
            this.$set(info, 'is_main', false)
          }
          return info
        })
      }
    },

    changeType(select, index) {
      const contact_list = this.form.contact_infos
      const list = contact_list.filter(item => item.type === select.type)
      const has_main = list.filter(item => item.is_main).length
      if (list.length < 2) {
        this.form.contact_infos[index].is_main = true
      } else if (has_main && list.length > 1) {
        this.form.contact_infos[index].is_main = false
      } else {
        this.form.contact_infos.find(item => item.type === select.type).is_main = true
      }
      this.checkMainContact()
    },

    checkMainContact() {
      const data = _.cloneDeep(this.form.contact_infos)
      const email_list = data.filter(item => item.type === 'EMAIL')
      const phone_list = data.filter(item => item.type === 'PHONE')

      function checkMain(o_list, list) {
        if (!list.length) return []
        const has_main = list.filter(item => item.is_main).length
        if (!has_main) {
          o_list.find(item => item.type === list[0].type).is_main = true
        }
      }

      checkMain(this.form.contact_infos, phone_list)
      checkMain(this.form.contact_infos, email_list)
    },

    getValidType(type) {
      if (['EMAIL', 'PHONE'].includes(type)) return type === 'EMAIL' ? this.checkEmail : this.checkPhone
      return ''
    },

    submitForm() {
      this.$refs.contactValidateForm.validate((valid) => {
        if (valid) {
          const params = []
          const contact_data = Object.assign({}, this.form)
          params.push(contact_data)

          this.submitting = true
          return ClientAPI.addClientContact(this.clientId, params)
            .then((data) => {
              this.$message({
                type: 'success',
                message: 'Created successfully!',
              })
              this.form = this.initForm()
              this.$emit('update', data[0])
              this.contactDialogVisible = false
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">

.contact-value-input {
  width: 400px;
  margin-right: 10px;
}

.icon-font {
  font-size: 1.1rem;
  margin: 0 5px;

  span {
    font-size: 14px;
  }
}
</style>
