<template>
  <div class="inline-block">
    <el-button
      type="success"
      @click.stop="gotoAddAdvisor"
    >
      <svg-icon icon-class="plus" />
      {{btnText[type]}}
    </el-button>
  </div>
</template>

<script>

export default {
  name: 'AddConsultant',
  props: {
    project: {
      type: Object,
      default: undefined,
    },
    datatemp: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: 'LEAD',
    },
    origin: {
      type: String,
      default: '',
    },
    originId: {
      type: Number,
      default: 0,
    },
    isCommon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      btnText: {
        LEAD: 'Add Lead',
        ADVISOR: 'Add Advisor',
      },
    }
  },

  methods: {
    gotoAddAdvisor() {
      const query = {
        project_id: this.project.id,
        client_id: this.project.client_id,
        type: this.type,
        origin: this.origin,
        origin_id: this.originId,
      }
      this.isCommon ? query.is_common = true : ''
      this.$router.push({
        name: 'AddConsultant',
        query,
      })
    },
  },
}
</script>

<style lang="scss" scoped>

  .dialog-title {
    font-size: 18px;
    line-height: 24px;

    .project-name {
      color: red;
    }
  }

  .svg-container {
    padding: 10px;
    cursor: pointer;
  }

  .consultant-tag {
    margin: 5px;
    cursor: pointer;
    user-select: none;
  }

  .is-selected {
    background-color: #43b370;
    border-color: #43b370;
    color: #fff;
  }

</style>

