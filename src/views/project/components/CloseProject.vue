<template>
  <div>
    <el-dialog
      :title="title"
      width="850px"
      :visible.sync="dialogVisible"
    >
      <div>
        <div v-if="advisorList.length > 0">
          <el-divider>Experts who did not send project closure notification emails</el-divider>
          <el-checkbox v-model="checkAll.first" @change="handleCheckAllChange('first')">All</el-checkbox>
        </div>
        <el-checkbox-group v-model="checkFirstList" @change="handleCheckedChange('first')">
          <el-checkbox v-for="item in advisorList" :key="item.advisor_id" :label="item.advisor_id">
            <router-link-advisor :data="{ id: item.advisor_id, full_name: item.name }" />
          </el-checkbox>
        </el-checkbox-group>
        <div v-show="sent_close_email_list.length > 0">
          <el-divider>Experts who have sent project closure notification emails</el-divider>
          <div>
            <el-checkbox v-model="checkAll.repeat" @change="handleCheckAllChange('repeat')">All
            </el-checkbox>
          </div>
          <el-checkbox-group v-model="checkRepeatList" @change="handleCheckedChange('repeat')">
            <el-checkbox v-for="item in sent_close_email_list" :key="item.advisor_id" :label="item.advisor_id">
              <router-link-advisor :data="{ id: item.advisor_id, full_name: item.name }" />
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="text" @click="actionCancel">Cancel</el-button>
        <el-button type="primary" :loading="loading" @click="actionSubmit">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ProjectAPI from '@/api/project'
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'

export default {
  name: 'CloseProject',
  components: { RouterLinkAdvisor },
  props: {},

  data() {
    return {
      loading: false,
      dialogVisible: false,
      title: undefined,
      advisorList: [],
      sent_close_email_list: [],
      checkFirstList: [],
      checkRepeatList: [],
      projectId: undefined,
      checkAll: {
        first: false,
        repeat: false,
      },
    }
  },
  methods: {
    openDialog(data) {
      this.advisorList = []
      this.sent_close_email_list = []
      this.projectId = data.id
      this.title = 'Close Project -- ' + data.name
      this.getTaskList(data).then(() => {
        this.dialogVisible = true
      })
    },

    getTaskList(params_data) {
      const params = {
        project_id: params_data.id,
        'events.type_in': ['CA_RESPOND', 'SQ_RESPOND', 'ADVISOR_SCHEDULE_RESPOND'].join(),
        extra: 'advisor',
      }

      return ProjectAPI.getProjectAdvisorList(params).then((data) => {
        data.list.forEach(item => {
          const target_item = { advisor_id: item.advisor_id, name: item.advisor.full_name }
          if (params_data.sent_close_email_advisor_ids.includes(item.advisor_id)) {
            checkExist(this.sent_close_email_list, item, target_item)
          } else {
            checkExist(this.advisorList, item, target_item)
          }
        })
      })

      function checkExist(list, item, target_item) {
        if (list.filter(i => i.advisor_id === item.advisor_id).length < 1) {
          list.push(target_item)
        }
      }
    },

    handleCheckAllChange(type) {
      if (type === 'first') {
        this.checkFirstList = this.checkAll[type] ? this.advisorList.map(item => item.advisor_id) : []
      } else {
        this.checkRepeatList = this.checkAll[type] ? this.sent_close_email_list.map(item => item.advisor_id) : []
      }
    },

    handleCheckedChange(type) {
      if (type === 'first') {
        this.checkAll[type] = this.checkFirstList.length === this.advisorList.length
      } else {
        this.checkAll[type] = this.checkRepeatList.length === this.sent_close_email_list.length
      }
    },

    actionCancel() {
      this.checkFirstList = []
      this.checkRepeatList = []
      this.checkAll = {
        first: false,
        repeat: false,
      }
      this.dialogVisible = false
    },

    actionSubmit() {
      this.loading = true
      const params = {
        send_notification_to_advisor_ids: this.checkFirstList.concat(this.checkRepeatList).join(),
      }
      return ProjectAPI.closeProject(this.projectId, params).then(() => {
        this.$emit('update')
      }).finally(() => {
        this.loading = false
        this.actionCancel()
      })
    },
  },
}
</script>

<style scoped>

</style>
