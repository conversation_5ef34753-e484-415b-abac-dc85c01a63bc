<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :model="searchQuery" @submit.native.prevent="handleSearch">
        <el-input
          v-model="searchQuery.keywords"
          :placeholder="keywordsPlaceholder"
          class="filter-item w-408px keyword-custom-input"
          clearable
          @clear="handleSearch"
        >
          <el-checkbox
            slot="append"
            v-model="searchQuery.is_contains_mode">
            <el-icon name="bottom" />
          </el-checkbox>
        </el-input>
        <el-select
          v-model="searchQuery.limit_project_angles"
          class="filter-item w-200px"
          placeholder="Limit Results To Project Angles"
          clearable
          multiple
        >
          <el-option
            v-for="(item,index) in limit_project_angles_options"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <br>
        <el-input
          v-model="searchQuery.name"
          placeholder="Project Name"
          class="filter-item w-200px"
        />
        <el-select
          v-model="searchQuery.status"
          class="filter-item w-200px"
          placeholder="Status"
          clearable
          multiple
        >
          <el-option
            v-for="item in project_options.status"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <ProjectCodeSearch v-model="searchQuery.code" class="filter-item w-200px" />

        <br>
        <el-select
          v-model="searchQuery.industry"
          class="filter-item w-200px"
          placeholder="Industry"
          clearable
          multiple
        >
          <el-option v-for="item in project_options.industry" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input
          v-model="searchQuery.investment_target"
          placeholder="Investment Target"
          class="filter-item w-200px"
        />
        <el-input
          v-model="searchQuery.angle_search_words"
          placeholder="Angle"
          class="filter-item w-200px"
        />
        <br>
        <el-input
          v-model="searchQuery.angle_discussion_topic"
          placeholder="Discussion Topic"
          class="filter-item w-200px"
        />
        <el-input
          v-model="searchQuery.screening_search_words"
          placeholder="Screening Questions"
          class="filter-item w-200px"
        />
        <el-date-picker
          v-model="searchQuery.start_date"
          type="date"
          placeholder="Start"
          value-format="yyyy-MM-dd"
          class="filter-item date-picker"
        />
        <br>
        <el-date-picker
          v-model="searchQuery.end_date"
          type="date"
          placeholder="End"
          value-format="yyyy-MM-dd"
          class="filter-item date-picker"
        />
        <el-select
          v-model="searchQuery.client_type"
          class="filter-item w-200px"
          placeholder="Client Type"
          clearable
        >
          <el-option v-for="item in client_options.type" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <client-search
          v-model="searchQuery.client_id"
          class="filter-item remote-select w-200px"
        />
        <br>
        <el-select
          v-model="searchQuery.client_contact_ids"
          class="filter-item w-200px remote-select"
          :class="{'is-reverse':is_visible}"
          filterable
          remote
          reserve-keyword
          placeholder="Client Contact"
          multiple
          clearable
          :loading="selectLoading"
          :remote-method="getClientContacts"
          no-data-text="No matching data"
        >
          <el-option v-for="item in contactsList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input
          v-if="isSGDB"
          v-model="searchQuery.outsource_client_name_contains"
          placeholder="CN Client"
          class="filter-item w-200px"
        />
        <copy-experts
          class="filter-item inline-block"
          :selected-project-ids.sync="selected_project_ids"
          :selected-sq-instance-ids.sync="selected_sq_instance_ids"
          :angle-keywords="searchQuery.limit_project_angles"
        />
        <el-button
          type="primary"
          :loading="loading"
          native-type="submit"
        >
          Search
        </el-button>
      </el-form>

      <div class="text-right">
        <span class="info">Sort :</span>
        <el-select
          v-model="custom_sort"
          clearable
          class="filter-item mb-8"
          collapse-tags
          placeholder="Sort"
          @change="handleSort(custom_sort)">
          <el-option
            v-for="item in sort_options"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </div>
    </div>

    <el-table
      ref="searchProjectTable"
      v-loading="loading"
      border
      stripe
      row-key="id"
      :data="tableData"
      :row-class-name="$options.filters.rowSetIndex"
      highlight-current-row
      @sort-change="sortData"
      @select="(selection,row) => $options.filters.tableShiftMultiple(selection, row, tableData.data, $refs['searchProjectTable'])"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" />
      <el-table-column
        label="Name"
        min-width="180"
      >
        <template slot-scope="scope">
          <router-link-project wrap :data="scope.row.source" />
          <angle-expert-count :angles="scope.row.source.limited_angles" />
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.source.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
        width="70"
      >
        <template slot-scope="scope">
          <span :class="scope.row.source.status | getProjectStausClass(project_options.status)">
            {{ scope.row.source.status | getLabel(project_options.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
        min-width="100"
        prop="source.manager_name"
      />
      <el-table-column
        label="Client"
        min-width="180"
      >
        <template v-if="scope.row.source.client_id" slot-scope="scope">
          <router-link-client wrap :data="{id:scope.row.source.client_id,name:scope.row.source.client_name}" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="isSGDB"
        label="CN Client"
        prop="outsource_client_name"
        min-width="140"
      >
        <template v-slot="{row}">
          <span v-if="row.source && row.source.exported_to === 'CN'">
            {{ row.source.outsource_client_name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Screened"
        sortable="custom"
        width="100"
        prop="screened_count"
      >
        <template slot-scope="{row}">
          <router-link
            class="text-truncate link"
            :to="{ name:'ProjectScreenedExperts', params: { type: getScreenedTaskType(row.source), project_id:row.source.id }}">
            {{ row.source.screened_count }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Scheduled"
        sortable="custom"
        width="106"
        prop="scheduled_count"
      >
        <template slot-scope="{row}">
          <router-link
            class="text-truncate link"
            :to="{ name:'ProjectScheduledCalls', params: { project_id:row.source.id }}">
            {{ row.source.scheduled_count }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        label="Investment Target"
        width="130"
        prop="source.investment_target"
      />
      <el-table-column
        label="Client Contact"
        width="130"
        prop="source.client_contact_names"
      />
      <el-table-column
        label="Angles"
        width="60"
      >
        <template slot-scope="{row}">
          <angles :row="row.source" />
        </template>
      </el-table-column>
      <el-table-column
        label="Client Type"
        width="120"
      >
        <template v-if="scope.row.source" slot-scope="scope">
          {{ scope.row.source.client_type | getLabel(client_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Code"
        prop="source.code"
      />
      <el-table-column
        label="Start / Create Date"
        width="130"
      >
        <template slot-scope="scope">
          {{ scope.row.source.start_date | momentFormat() }}
        </template>
      </el-table-column>
      <el-table-column label="Relevant Screening Question Responses" min-width="600">
        <template slot-scope="scope">
          <SQSlither :sq-list="scope.row.source.match_sq_qas" @select="handleSelectSqInstance" />
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { highlightHtml } from '@/utils/tool'
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkProject from '@/components/RouterLink/Project'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import RouteTools from '@/utils/tool-router'
import ProjectCodeSearch from '@/components/SelectRemoteSearch/ProjectCodeSearch'
import SQSlither from '@/views/consultant/components/SQSlither'
import CopyExperts from '@/views/project/search/components/CopyExperts'
import AngleExpertCount from '@/views/project/search/components/AngleExpertCount'
import ClientSearch from '@/components/SelectRemoteSearch/ClientSearch'
import Angles from '@/views/project/search/components/Angles'
import _ from 'lodash'

export default {
  name: 'ProjectSearch',
  components: {
    Pagination,
    RouterLinkClient,
    RouterLinkProject,
    ProjectCodeSearch,
    SQSlither,
    CopyExperts,
    AngleExpertCount,
    ClientSearch,
    Angles,
  },
  mixins: [SelectMixin],
  data() {
    return {
      loading: false,
      selectLoading: false,
      clientList: [],
      contactsList: [],
      total: 0,
      tableData: [],
      searchQuery: {
        page: 1,
        size: 50,
        keywords: '',
        is_contains_mode: false,
        angle_search_words: '',
        angle_discussion_topic: '',
        screening_search_words: '',
        limit_project_angles: [],
        name: '',
        status: [],
        industry: [],
        start_date: '',
        end_date: '',
        client_type: '',
        client_id: '',
        client_contact_ids: [],
        investment_target: '',
        outsource_client_name_contains: undefined,
      },
      sq_highlight_words: [],
      selected_project_ids: [],
      selected_sq_instance_ids: [],
      custom_sort: undefined,
      sort_options: [
        { label: 'Relevance', value: undefined },
        { label: 'Most Recently Active', value: 'latest_send_to_client_time desc' },
        { label: 'Most Recently Created', value: 'create_time desc' },
      ],
      limit_project_angles_options: [],
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'client_options',
    ]),

    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
      McKinseyId: state => state.app.McKinseyId,
    }),

    keywordsPlaceholder() {
      return this.searchQuery.is_contains_mode ? 'Keyword Contains' : 'Keywords'
    },

  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery, ['limit_project_angles', 'status', 'industry'])
    this.getList()
    this.getLimitProjectAnglesOptions()
    this.getClientContacts()
  },

  methods: {
    handleSelectionChange(val) {
      this.selected_project_ids = val.map(item => item.source.id)
    },

    handleSort(val) {
      this.$refs.searchProjectTable.clearSort()
      this.searchQuery.sort = val
      this.getList()
    },

    sortData(val) {
      if (val) {
        this.custom_sort = undefined
        const order = val.order === 'ascending' ? ' asc' : ' desc'
        this.searchQuery.sort = val.order ? val.prop + order : undefined
        this.getList()
      }
    },
    getList() {
      RouteTools.saveQueryToRouter(this.searchQuery)
      this.loading = true
      const params = this.fromParams(this.searchQuery)
      return ProjectAPI.searchProjectNew(params)
        .then(data => {
          this.tableData = data.list.map(item => {
            const result = _.clone(item['source'])
            const sq_qas = this.sq_highlight_words.length > 0 ? this.filterSQ(result.sq_qas) : result.sq_qas
            sq_qas.forEach(sq => { sq.checked = false })
            result.match_sq_qas = sq_qas
            return { source: result }
          })
          this.total = data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterSQ(sq_list) {
      let result
      const highlight_reg = new RegExp(`${this.sq_highlight_words.join('|')}`, 'i')
      result = sq_list.filter(item => highlight_reg.test(item.answer_text) || highlight_reg.test(item.question_title))

      const sq_search_words = this.searchQuery.screening_search_words
        ? this.searchQuery.screening_search_words.split(' ').map(item => item.trim())
        : []
      const reg_all = /[OR|AND|NOT]/g
      const reg_and = /AND/g
      const sq_reg_all = sq_search_words.filter(item => reg_all.test(item)).length
      const sq_reg_and = sq_search_words.filter(item => reg_and.test(item)).length

      // 单独处理 逻辑运算只包含AND时的情况 ：datadog AND rapid7 =》必须包含两者
      //                              ：data dog AND rapid7 =》必须包含(data|dog）和 rapid7
      //                              ：'data dog' AND rapid7 =》必须包含(data dog) 和 rapid7
      // 其余情况 统一视为 所有单词 或
      if (sq_reg_all === sq_reg_and && sq_reg_and > 0) {
        const temp = this.searchQuery.screening_search_words.split('AND')
        const match = temp.map(item => {
          const keyword = item.toLowerCase().trim()
          if (/^".*"$/.test(keyword)) {
            return `(?=.*${keyword.replace(/"/g, '')})`
          } else {
            return `(?=.*${keyword.replace(/\s+/g, '|')})`
          }
        }).join('')

        const highlight_and_reg = new RegExp(`${match}.*`, 'i')
        result = result.filter(item => {
          return highlight_and_reg.test(item.question_title + item.answer_text)
        })
      }

      return result.map(item => {
        item.answer_text = highlightHtml(item.answer_text, this.sq_highlight_words)
        item.question_title = highlightHtml(item.question_title, this.sq_highlight_words)
        return item
      })
    },

    getLimitProjectAnglesOptions() {
      return ProjectAPI.getProjectAngleAlias()
        .then(data => {
          this.limit_project_angles_options = data.map(item => {
            return { label: `${item[0].toUpperCase()}${item.slice(1)}`, value: item }
          })
        })
    },

    getClientContacts(val) {
      this.selectLoading = true
      const params = {
        name_like: val || undefined,
        page: 1,
        size: 15,
      }

      return ClientAPI.searchClient(params)
        .then(data => {
          this.selectLoading = false
          this.contactsList = data.list
        })
    },

    fromParams(input) {
      this.sq_highlight_words = []
      const result = {}
      for (const key in input) {
        const value = input[key]
        result[key] = Array.isArray(value) ? value.join(' ') : value
        if (['keywords', 'screening_search_words', 'angle_search_words'].includes(key) && value) {
          this.sq_highlight_words.push(...this.formatHighlightWords(value))
        }
      }

      result.page = input.page
      result.size = input.size

      return result
    },

    formatHighlightWords(value) {
      if (!value) return
      const reg = /(OR|AND|NOT)/
      const trim_value = value.trim().replace(/\s+/g, ' ').replace(/\*/g, '')

      // "ab c" OR qw e =》['ab c','qw','e']  双引号内的内容精确搜索
      // ab c OR qw e =》['ab','c','qw','e']
      if (/".*"/.test(trim_value)) {
        const precise_words_list = trim_value.match(/\"(.*?)\"/g).map(item => item.replace(/"/g, ''))
        const other_words = trim_value.replace(/\"(.*?)\"/g, '').split(' ')
        const other_words_list = other_words.map(item => item.trim()).filter(item => !!item && !reg.test(item))

        return precise_words_list.concat(other_words_list)
      } else {
        return trim_value.split(' ').map(item => item.trim()).filter(item => !reg.test(item))
      }
    },

    handleSearch() {
      this.$refs.searchProjectTable.clearSelection()
      this.searchQuery.page = 1
      this.getList()
    },

    handleSelectSqInstance(item) {
      const index = this.selected_sq_instance_ids.indexOf(item.instance_id)
      if (index > -1) {
        this.selected_sq_instance_ids.splice(index, 1)
        item.checked = false
      } else {
        this.selected_sq_instance_ids.push(item.instance_id)
        item.checked = true
      }
    },

    getScreenedTaskType(item) {
      if (item.client_id === this.McKinseyId) return 'MCK'
      if (item.sub_type === 'Investor Call') return 'SST'
      return 'normal'
    },
  },
}
</script>

<style lang="scss" scoped>
.icon-size {
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
}

.date-picker {
  width: 200px !important;
}

.keyword-custom-input{
  ::v-deep .el-input-group__append{
    padding:0 10px;
  }
  ::v-deep .el-checkbox__input{
    display: none;
  }
  ::v-deep .el-checkbox__label {
    padding-left: 0;  // 移除label的左边距
  }
}

</style>
