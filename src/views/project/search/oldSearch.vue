<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="searchQuery.keyword"
        placeholder="Keywords"
        class="filter-item w-408px"
        @input="handleSearch"
      />
      <ProjectCodeSearch v-model="searchQuery.code" class="filter-item w-200px" @change="handleSearch" />
      <br>
      <el-select
        v-model="searchQuery.status"
        class="filter-item w-200px"
        placeholder="Status"
        clearable
        multiple
        @change="handleSearch"
      >
        <el-option v-for="item in project_options.status" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="searchQuery.industry"
        class="filter-item w-200px"
        placeholder="Industry"
        clearable
        multiple
        @change="handleSearch"
      >
        <el-option v-for="item in project_options.industry" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="searchQuery.investment_target"
        placeholder="Investment Target"
        class="filter-item w-200px"
        @input="handleSearch" />
      <br>
      <el-input
        v-model="searchQuery.name"
        placeholder="Project Name"
        class="filter-item w-200px"
        @input="handleSearch" />
      <el-date-picker
        v-model="searchQuery.start_date"
        type="date"
        placeholder="Start"
        value-format="yyyy-MM-dd"
        class="filter-item date-picker"
        @change="handleSearch"
      />
      <el-date-picker
        v-model="searchQuery.end_date"
        type="date"
        placeholder="End"
        value-format="yyyy-MM-dd"
        class="filter-item date-picker"
        @change="handleSearch"
      />
      <br>
      <el-select
        v-model="searchQuery.client_type"
        class="filter-item w-200px"
        placeholder="Client Type"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in client_options.type" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="searchQuery.client_id"
        class="filter-item remote-select w-200px"
        :class="{'is-reverse':is_visible}"
        filterable
        remote
        clearable
        multiple
        reserve-keyword
        placeholder="Client"
        :loading="selectLoading"
        :remote-method="getClient"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option v-for="item in clientList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select
        v-model="searchQuery.client_contact_ids"
        class="filter-item w-200px remote-select"
        :class="{'is-reverse':is_visible}"
        filterable
        remote
        reserve-keyword
        placeholder="Client Contact"
        multiple
        clearable
        :loading="selectLoading"
        :remote-method="getClientContacts"
        no-data-text="No matching data"
        @change="handleSearch"
      >
        <el-option v-for="item in contactsList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-input
        v-if="isSGDB"
        v-model="searchQuery.outsource_client_name_contains"
        placeholder="CN Client"
        class="filter-item w-200px"
        @input="handleSearch" />
    </div>

    <el-table
      v-loading="loading"
      border
      stripe
      :data="tableData"
      @sort-change="handleSort"
    >
      <el-table-column
        label="Name">
        <template slot-scope="scope">
          <router-link-project :data="scope.row.source" />
        </template>
      </el-table-column>
      <el-table-column
        label="Industry"
      >
        <template slot-scope="scope">
          {{ scope.row.source.industry | getLabel(project_options.industry) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status"
      >
        <template slot-scope="scope">
          <span :class="scope.row.source.status | getProjectStausClass(project_options.status)">
            {{ scope.row.source.status | getLabel(project_options.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Project Manager"
      >
        <template slot-scope="scope">
          {{ scope.row.source.manager_name }}
        </template>
      </el-table-column>
      <el-table-column
        label="Client">
        <template v-if="scope.row.source.client_id" slot-scope="scope">
          <router-link-client :data="{id:scope.row.source.client_id,name:scope.row.source.client_name}" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="isSGDB"
        label="CN Client"
        prop="outsource_client_name"
      >
        <template v-slot="{row}">
          <span v-if="row.source && row.source.exported_to === 'CN'">
            {{ row.source.outsource_client_name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="Investment Target"
      >
        <template v-if="scope.row.source" slot-scope="scope">
          {{ scope.row.source.investment_target }}
        </template>
      </el-table-column>
      <el-table-column
        label="Client Contact"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.source.client_contact_names }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="Client Type"
      >
        <template v-if="scope.row.source" slot-scope="scope">
          {{ scope.row.source.client_type | getLabel(client_options.type) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Code"
      >
        <template slot-scope="scope">
          {{ scope.row.source.code }}
        </template>
      </el-table-column>
      <el-table-column
        prop="start_date"
        label="Start / Create Date"
        sortable="custom"
      >
        <template slot-scope="scope">
          {{ scope.row.source.start_date | momentFormat() }}
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" :page.sync="searchQuery.page" :limit.sync="searchQuery.size" @pagination="getList" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { debounce, isEmpty } from '@/utils/tool'
import moment from 'moment'
import ProjectAPI from '@/api/project'
import ClientAPI from '@/api/client'
import Pagination from '@/components/Pagination'
import RouterLinkClient from '@/components/RouterLink/Client'
import RouterLinkProject from '@/components/RouterLink/Project'
import SelectMixin from '@/components/SelectRemoteSearch/mixins'
import RouteTools from '@/utils/tool-router'
import ProjectCodeSearch from '@/components/SelectRemoteSearch/ProjectCodeSearch'

export default {
  name: 'OldSearch',
  components: { Pagination, RouterLinkClient, RouterLinkProject, ProjectCodeSearch },
  mixins: [SelectMixin],
  data() {
    return {
      loading: false,
      selectLoading: false,
      clientList: [],
      contactsList: [],
      total: 0,
      tableData: [],
      searchQuery: {
        page: 1,
        size: 10,
        keyword: '',
        name: '',
        status: '',
        industry: '',
        start_date: '',
        end_date: '',
        client_type: '',
        client_id: '',
        client_contact_ids: '',
        investment_target: '',
        outsource_client_name_contains: '',
      },
    }
  },
  computed: {
    ...mapGetters([
      'project_options',
      'client_options',
    ]),
    ...mapState({
      isSGDB: state => state.app.isSGDB,
      timeZone: state => state.app.timeZone,
    }),

  },
  mounted() {
    RouteTools.resolveRouteQuery(this.$route, this.searchQuery)
    this.getList()
    this.getClient()
    this.getClientContacts()
  },

  methods: {
    handleSort(val) {
      const order = val.order === 'ascending' ? ' asc' : ' desc'
      this.searchQuery.sort = val.order ? val.prop + order : undefined
      this.getList()
    },

    getList() {
      RouteTools.saveQueryToRouter(this.searchQuery)
      this.loading = true
      const params = this.fromParams(this.searchQuery)
      return ProjectAPI.searchProject(params)
        .then(data => {
          this.tableData = data.list
          this.total = data.total
        })
        .finally(() => {
          this.loading = false
        })
    },

    getClient(val) {
      this.selectLoading = true
      const name = val || undefined
      const params = {
        name_like: name,
        extra: 'client_contacts',
        page: 1,
        size: 15,
      }
      return ClientAPI.getClientList(params)
        .then(data => {
          this.selectLoading = false
          this.clientList = data.list
        })
    },

    getClientContacts(val) {
      this.selectLoading = true
      const params = {
        name_like: val || undefined,
        page: 1,
        size: 15,
      }

      return ClientAPI.searchClient(params)
        .then(data => {
          this.selectLoading = false
          this.contactsList = data.list
        })
    },

    fromParams(input) {
      const result = {}
      const query_list = []

      const pass_keys = ['page', 'size', 'sort']

      for (const key in input) {
        const value = input[key]

        if (pass_keys.includes(key) || isEmpty(value)) {
          continue
        }

        if (key === 'start_date') {
          const timeZone = moment.tz(value, this.timeZone).valueOf()
          const result = `start_date:(>=${timeZone})`
          query_list.push(result)
        } else if (key === 'end_date') {
          query_list.push(`start_date:(<=${moment.tz(value, this.timeZone).valueOf()})`)
        } else if (typeof value === 'object') {
          const ids = value.join(' ')
          query_list.push(key + ':(' + ids + ')')
        } else if (key === 'keyword') {
          query_list.push(`"${value}"~0`)
        } else if (key === 'name') {
          query_list.push(`${key}:("${value}"~0)`)
        } else if (key === 'code') {
          query_list.push(`${key}:(\"${value}\"~0)`)
        } else if (key === 'outsource_client_name_contains') {
          query_list.push(`${key}:("${value}"~0)`)
        } else {
          query_list.push(key + ':(' + value + ')')
        }
      }

      result.search_words = query_list.map(item => ['(', item, ')'].join(''))
        .join(' AND ') || '*'

      result.page = input.page
      result.size = input.size
      result.sort = input.sort

      return result
    },

    handleSearch() {
      debounce(() => {
        this.searchQuery.page = 1
        this.getList()
      }, 500)
    },
  },
}
</script>

<style lang="scss" scoped>
.icon-size {
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
}

.date-picker {
  width: 200px !important;
}

</style>
