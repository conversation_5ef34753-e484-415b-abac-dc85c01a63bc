<template>
  <el-popover
    placement="right"
    :open-delay="300"
    width="500"
    trigger="hover">
    <div v-for="item in row.angles" :key="item.id">
      <router-link :to="goToAngleList(row,item)" class="link" target="_blank">
        {{ item.name }}
        <span v-if="item.discussion_topic" class="info">(Discussion Topic : {{ item.discussion_topic }})</span>
      </router-link>
    </div>
    <el-button v-show="row.angles.length" slot="reference" type="text" icon="el-icon-tickets" />
  </el-popover>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'Angles',
  props: {
    row: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  computed: {
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
    }),
  },

  methods: {
    goToAngleList(row, item) {
      let router_name = 'ProjectTasks'
      if (row.sub_type === 'Investor Call') router_name = 'ProjectClientTasksSST'
      if (row.client_id === this.McKinseyId) router_name = 'ProjectClientTasksMcK'
      return {
        name: router_name,
        params: { project_id: row.id },
        query: { angle_ids: item.id.toString() },
      }
    },
  },
}
</script>

<style scoped>

</style>
