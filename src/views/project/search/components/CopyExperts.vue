<template>
  <div class="add-btn">
    <el-button
      type="primary"
      size="mini"
      :disabled="!selectedProjectIds.length && !selectedSqInstanceIds.length"
      @click="actionAddToProject()">
      Copy Experts ({{selectedSqInstanceIds.length}})
    </el-button>

    <el-dialog
      title="Add to Project"
      append-to-body
      :show-close="false"
      :visible.sync="projectDialogVisible"
      :close-on-click-modal="false"
      width="40%"
    >
      <div v-if="!disabled_advisors.length">
        <el-select
          v-model="target_project_id"
          filterable
          clearable
          remote
          placeholder="Project Name"
          class="remote-select"
          :loading="searchLoading"
          :remote-method="searchProject"
          no-data-text="No matching data"
          @change="matchAngle"
        >
          <el-option
            v-for="item in project_option"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
        </el-select>
        <div v-if="target_project_id" class="inline-block">
          <el-select
            v-model="target_angle_id"
            filterable
            clearable
            placeholder="Angle"
          >
            <el-option
              v-for="item in angle_option"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </div>
        <el-checkbox v-model="tc_signed" class="mt-3">Only Experts with TC Signed</el-checkbox>
        <el-checkbox v-model="screened" class="mt-3">Only Screened Experts</el-checkbox>
        <el-checkbox v-model="include_angles" :disabled="!angleKeywords.length" class="mt-3">Include Angles<span class="info"> (Limit Results To Project Angles)</span></el-checkbox>
      </div>
      <div v-else class="white-pre-wrap">
        These experts were not added to the project because they already exist or have no work experience:
        <span v-for="item in disabled_advisors" :key="item.advisor_id">{{item.advisor_id}} </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="addToProject()">Submit</el-button>
        <el-button type="text" @click="actionCancel()">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/project'
import { mapGetters, mapState } from 'vuex'
// import _ from 'lodash'
import ProjectAPI from '@/api/project'

export default {
  name: 'CopyExperts',
  props: {
    selectedProjectIds: {
      type: Array,
      default() {
        return []
      },
    },
    selectedSqInstanceIds: {
      type: Array,
      default() {
        return []
      },
    },
    angleKeywords: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      submitLoading: false,
      searchLoading: false,
      projectDialogVisible: false,
      tc_signed: false,
      screened: false,
      include_angles: false,
      project_option: [],
      angle_option: [],
      disabled_advisors: [],
      target_angle_id: null,
      target_project_id: null,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      McKinseyId: state => state.app.McKinseyId,
    }),
  },
  methods: {
    addToProject() {
      const params = {
        selected_project_ids: this.selectedProjectIds,
        selected_sq_instance_ids: this.selectedSqInstanceIds,
        tc_signed: this.tc_signed,
        screened: this.screened,
        angles_keywords: this.include_angles ? this.angleKeywords : [],
        target_angle_id: this.target_angle_id,
      }
      // const disabled_list = []
      return ProjectAPI.attachExpertsToProject(this.target_project_id, params)
        .then(data => {
          // const disabled_type_list = ['duplicate_in_angle', 'duplicate_in_project', 'empty_employment_history_advisor']
          // disabled_type_list.forEach(key => {
          //   if (data[key].length) {
          //     disabled_list = disabled_list.concat(data[key])
          //   }
          // })
          this.$message.success('success')
          this.$emit('update:selectedSqInstanceIds', [])
          setTimeout(() => {
            this.goToTargetProject()
          }, 500)
        }).finally(() => {
          // this.disabled_advisors = _.uniqBy(disabled_list, 'advisor_id')
          this.projectDialogVisible = false
        })
    },
    actionCancel() {
      this.projectDialogVisible = false
      this.target_angle_id = undefined
      this.target_project_id = undefined
      this.tc_signed = false
      this.screened = false
      this.disabled_advisors = []
    },

    actionAddToProject() {
      this.projectDialogVisible = true
      this.searchProject()
    },

    goToTargetProject() {
      const target_project = this.project_option.find(item => item.id === this.target_project_id)
      console.log(target_project)
      let router_name = 'ProjectClientTasks'
      if (target_project.sub_type === 'Investor Call') router_name = 'ProjectClientTasksSST'
      if (target_project.client_id === this.McKinseyId) router_name = 'ProjectClientTasksMcK'
      const router = this.$router.resolve({
        name: router_name,
        params: { project_id: this.target_project_id },
        query: { angle_ids: this.target_angle_id.toString() },
      })

      window.open(router.href, '_blank')
    },

    matchAngle() {
      this.target_angle_id = undefined
      this.angle_option = this.project_option.find(item => +item.id === +this.target_project_id).angles
    },

    searchProject(val) {
      const params = {
        page: 1,
        size: 20,
        member_ids: this.uid,
        name_like: val || undefined,
        extra: 'angles,members.user',
      }
      this.searchLoading = true
      return API.getProjectList(params)
        .then(data => {
          this.project_option = data.list.map(item => {
            return { name: item.name, id: item.id, angles: item.angles, members: item.members, client_id: item.client_id, sub_type: item.sub_type }
          })
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
