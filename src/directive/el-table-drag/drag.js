export default {
  bind(el, binding, vnode) {
    var target_element = el // 获取当前表格元素

    // 修改样式小手标志
    el.querySelector('.el-table .el-table__body-wrapper').style.cursor =
      'pointer'

    var mouseDownAndUpTimer = null
    var mouseOffset = 0
    var mouseFlag = false
    var click_sortable_element = false // 点击到目标表格的拖拽排序元素

    const bindRef = binding.value[0] // 绑定的表格的ref属性
    const blurElement = binding.value[1] || undefined // 需要先失焦的元素

    target_element.addEventListener('mousedown', function(e) {
      // 点击到表格 sortable 箭头区域 不阻止 dragstart 事件
      click_sortable_element = e.target.closest('.cursor-move')

      const ePath = composedPath(e)
      // 拖拽表头不滑动
      if (ePath && ePath?.some(res => {
        const string_type = res.className === 'string'
        return (res.className && string_type && !!(res.className.indexOf('el-table__header') > -1))
      })
      ) { return }

      if (e.which !== 1) return

      mouseOffset = e.clientX
      mouseDownAndUpTimer = setTimeout(function() {
        mouseFlag = true
      }, 80)
    })

    target_element.addEventListener('mouseup', function(e) {
      setTimeout(() => {
        // 解决拖动列宽行不对齐问题--渲染表格
        if (vnode.context.$refs[bindRef]) vnode.context.$refs[bindRef].doLayout()
      }, 200)
      if (mouseFlag) {
        mouseFlag = false
      } else {
        clearTimeout(mouseDownAndUpTimer) // 清除延迟时间
      }
    })

    target_element.addEventListener('mouseenter', function(e) {
      if (blurElement) document.getElementById(blurElement).blur()
    })

    target_element.addEventListener('mouseleave', function(e) {
      setTimeout(() => {
        // 解决拖动列宽行不对齐问题--渲染表格
        if (vnode.context.$refs[bindRef]) vnode.context.$refs[bindRef].doLayout()
      }, 200)
      mouseFlag = false
    })

    target_element.addEventListener('mousemove', function(e) {
      if (e.which !== 1) return

      const divData = target_element.querySelector('.el-table .el-table__body-wrapper')
      if (mouseFlag && divData) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -= -mouseOffset + (mouseOffset = e.clientX)
      }
    })

    // 解决有些时候,在鼠标松开的时候,元素仍然可以拖动;
    target_element.addEventListener('dragstart', function(e) {
      if (click_sortable_element) return
      e.preventDefault()
    })

    target_element.addEventListener('dragend', function(e) {
      if (click_sortable_element) return
      e.preventDefault()
    })

    // 是否拖拽可选中文字
    target_element.addEventListener('selectstart', function(e) {
      return false
    })

    // 浏览器Event.path属性不存在
    function composedPath(e) {
      // 存在则直接return
      if (e.path) {
        return e.path
      }
      // 不存在则遍历target节点
      let target = e.target
      e.path = []
      while (target.parentNode !== null) {
        e.path.push(target)
        target = target.parentNode
      }
      // 最后补上document和window
      e.path.push(document, window)
      return e.path
    }
  },
}
