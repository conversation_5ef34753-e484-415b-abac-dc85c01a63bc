// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}


// el-uploader 定制
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner {
  width: 230px;
}

// flex justify-content: space-evenly
.el-row--flex.is-justify-space-evenly {
  justify-content: space-evenly;
}

// 时间范围选择器 水平排布不在一条线上
.el-date-editor.el-range-editor {
  vertical-align: middle;
}

/* el-card */
// 调整 Card 的 padding
.el-card__header {
  padding: $gap-md/2 $gap-sm;
}

.el-card__body {
  padding: $gap-sm;
}

.el-card.no-border {
  border: none;
}

// el-link 不加粗
.el-link {
  font-weight: normal;
  font-size: inherit;
}

/* dialog 样式去定制化 */
.el-dialog__body {
  padding: 15px 20px;
  color: initial;
  font-size: inherit;
  word-break: initial;
}

// 表单控件 设置字号为 12px
.el-checkbox__label,
.el-radio__label,
.el-select__input,
.el-select-dropdown__item,
.el-select-dropdown__empty,
.el-form-item__label,
.el-form-item__content {
  font-size: 12px;
}

/* el-radio-group */
.el-radio-group.radio-group-vertical {
  & > .el-radio {
    display: block;
    margin-right: initial;
    line-height: 2em;
  }
}

/* el-checkbox-group */
.el-checkbox-group.checkbox-group-vertical {
  & > .el-checkbox {
    display: block;
    margin-right: initial;
    line-height: 2em;
  }
}

/* el-checkbox-group */
.el-checkbox-group.checkbox-group-vertical {
  & > .el-checkbox {
    display: block;
    margin-right: initial;
    line-height: 2em;
  }
}

// drawer
.el-drawer__body {
  padding: 20px;
  padding-top: 0;
  overflow: auto;
}

.el-drawer__header {
  margin-bottom: 0
}

.el-popover {
  ::v-deep .popover-content {
    font-size: 12px;
  }

  &.subject-popper {
    max-width: 800px;

    .popover-content {
      max-height: calc(100vh - 38px);
      overflow-y: auto;
    }

    a {
      cursor: pointer;
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// exact small button
.el-button--xs {
  padding: 4px 8px;
  font-size: 12px;
}

// 打分器高度 默认为20但项目字号默认为14
.el-rate {
  height: 14px;
}

/** Table **/
/* filters tooltip */
.el-table-filter__list-item {
  line-height: 26px;
  font-size: 12px;
}

/* 单元格视觉改善 (<el-table> 添加 .origin 以使用原版) */
.el-table:not(.origin) .cell {
  line-height: 1.4;
  word-break: initial;
  text-overflow: initial;
}

/* 阻止 option 长度超出选择框 */
.el-select__tags-text {
  display: inline-block;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-select .el-tag__close.el-icon-close {
  top: -7px;
  right: -4px;
}


//表格 fixed 某些列，修复expend 展开行对应部分被覆盖
.el-table__body-wrapper {
  .el-table__expanded-cell {
    z-index: 100;
    padding: 0;
  }
}
.el-table__fixed,
.el-table__fixed-right {
  .el-table__expanded-cell {
    visibility: hidden;
    padding: 0;
  }
}
.table-expand {
  width: calc(100vw - 100px);
  padding: 30px;
  background: #fff;//盖住fixed产生的阴影
}
