@import './variables.module.scss';

// display
.display-none {
  display: none;
}

.inline-block {
  display: inline-block;
}

.overflow-visible {
  overflow: initial;

  > .cell {
    overflow: initial;
  }
}

.outermost-p-td-0 {

   p {
    margin-top: 0;
    margin-bottom: 0;
  }
}

// position
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.vertical-align-middle {
  vertical-align: middle !important;
}

.vertical-top {
  vertical-align: top;

  &.el-table td {
    vertical-align: top;
  }
}


// flex
.flex {
  display: flex;

  &.justify-content-between,
  &.justify-content-center {
    justify-content: space-between;
  }

  &.justify-content-around {
    justify-content: space-around;
  }

  &.align-items-center {
    align-items: center;
  }

  &.align-items-baseline {
    align-items: baseline;
  }

  &.align-items-end {
    align-items: flex-end;
  }

  &.flex-wrap {
    flex-wrap: wrap;
  }
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}


// width
.width-auto {
  width: auto;
}

.w-408px {
  width: 408px;
}

.w-300px{
  width:300px
}

.w-200px {
  width: 200px;
}

.w-172px {
  width: 172px;
}

.w-150px {
  width: 150px;
}

.w-120px {
  width: 120px;
}

.w-100 {
  width: 100%;
}

// height
.h-100 {
  height: 100%;
}

/* Space */
.mt-8 {
  margin-top: $gap-sm/2;
}

.mr-8 {
  margin-right: $gap-sm/2;
}

.mb-8 {
  margin-bottom: $gap-sm/2;
}

.ml-8 {
  margin-left: $gap-sm/2;
}

.mb-5px{
  margin-bottom: 5px;
}

.ml-3 {
  margin-left: 1em;
}

.mb-3 {
  margin-bottom: 1em;
}

.mt-3 {
  margin-top: 1em;
}

.ma-3px {
  margin: 3px;
}

.pl-5 {
  padding-left: 5px;
}

.mt-normal {
  margin-top: $gap-sm;
}

.mr-normal {
  margin-right: $gap-sm;
}

.mb-normal {
  margin-bottom: $gap-sm;
}

.ml-normal {
  margin-left: $gap-sm;
}


// text
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-nowrap-hidden {
  white-space: nowrap;
  overflow: hidden;
}

.text-nowrap-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-wrap {
  white-space: nowrap;
}

.pre-wrap {
  white-space: pre-wrap;
}

.break-word {
  word-break: break-word !important;
}

.pre-line {
  white-space: pre-line;
}

.text-info {
  color: #5e6d82;
}

.text-sm,
.text-small {
  font-size: 0.75em;
}

.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-14 {
  font-size: 14px;
}

.fs-24 {
  font-size: 24px;
}

.c-333{
  color: #333333;
}


// tools
.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.pull-right {
  float: right;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-move {
  cursor: move;
}

.no-float {
  float: none;
}


.hover {
  .hover-visible {
    display: none;
  }

  &:hover {
    .hover-visible {
      display: block;

      &.hover-visible-inline {
        display: inline;
      }
    }
  }
}
.expand-table{
  padding: 0 !important;
  >.cell{
    padding: 0 !important;
  }
}

.sq-option-block{
  display: block;
  margin: 6px 0;
}

// user advisor tags
.light-gray-color {
  background: rgba(227, 226, 224, 0.5);
  color: rgb(50, 48, 44);
}

.gray-color {
  background: rgb(227, 226, 224);
  color: rgb(50, 48, 44);
}

.brown-color {
  background: rgb(238, 224, 218);
  color: rgb(68, 42, 30);
}

.orange-color {
  background: rgb(250, 222, 201);
  color: rgb(73, 41, 14);
}

.yellow-color {
  background: rgb(253, 236, 200);
  color: rgb(64, 44, 27);
}

.green-color {
  background: rgb(219, 237, 219);
  color: rgb(28, 56, 41);
}

.blue-color {
  background: rgb(211, 229, 239);
  color: rgb(24, 51, 71);
}

.purple-color {
  background: rgb(232, 222, 238);
  color: rgb(65, 36, 84);
}

.pink-color {
  background: rgb(245, 224, 233);
  color: rgb(76, 35, 55);
}

.red-color {
  background: rgb(255, 226, 221);
  color: rgb(93, 23, 21);
}


.highlight-color {
  background-color: yellow;
}
.sq-option-flex{
  display: flex;
  align-items: baseline;
  padding-top:2px;
}

.text-decoration-underline {
  text-decoration: underline;
}

.overflow-three{
  overflow: hidden;
  white-space: pre-line;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.2rem;
  max-height: 3.6rem;
}
