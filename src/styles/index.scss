@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './quick.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a {
  text-decoration: none;
}

div:focus {
  outline: none;
}

// main-container global css
.app-container {
  padding: 16px;

  .form-data {
    width: 910px;
    @media screen and (max-width: 800px) {
      width: 100%;
    }
  }
}

.remote-select {
  .el-icon-:before {
    content: "\E6E1"
  }

  &.is-reverse .el-icon- {
    -webkit-transform: rotateZ(0);
    transform: rotateZ(0);
  }

}

.multiple-select {
  width: 100%;
}

.components-container {
  margin: 16px;
  position: relative;
}

.filter-container {
  margin-left: -4px;
  margin-top: -4px;
  padding-bottom: 8px;
  word-spacing: -6px;
  //display: table;

  .filter-item {
    word-spacing: 0;
    vertical-align: middle;
    margin: 4px;
  }
}

.update-form {
  max-width: 1200px;
}

.link {
  cursor: pointer;
  color: #409eff;
}

.link:hover {
  color: #66b1ff;
}

.prompt-words {
  color: #67C23A;
}

.task-notify {
  z-index: 9999 !important;
}

.primary {
  color: #409EFF;
}

.success {
  color: #67C23A;
}

.danger {
  color: #F56C6C;
}

.info {
  color: #909399;
}

.warning {
  color: #E6A23C;
}

.grey-bgc{
  background-color: #f4f4f4 !important;
}

// email
.email-subject {
  margin: 0 0 $gap-sm;
  text-align: center;
  font-size: 14px;
}

.email-content {
  font-size: 12px;

  //p {
  //  margin: 0;
  //}
}

.email-dialog {
  margin-top: 10vh !important;
  max-width: 1300px;
}

.status-tags {
  word-spacing: 0;
  margin-left: auto;
}

// tabs-stackOverflow
.tabs-stackOverflow {
  border-bottom: 1px solid #E4E7ED;
  user-select: none;

  .el-tag {
    $height: 28px;

    margin-left: 4px;
    margin-right: 4px;
    padding: 0 0.5em;
    font-weight: normal;
    font-size: 12px;
    line-height: $height;
    height: $height;
    border-radius: 0;
    margin-bottom: -1px;
    background-color: #FFF;
    color: #333;
    border: 1px solid #E4E7ED;

    &:not(.active) {
      color: #61676d;
      border-color: transparent;
      border-bottom-color: #E4E7ED;
      cursor: pointer;

      &:focus,
      &:hover {
        border-color: #E4E7ED;
        background-color: #f9fafa;
      }
    }

    &.active {
      position: relative;
      border-bottom-color: transparent;
      cursor: default;

      &:before {
        content: '';
        height: 2px;
        position: absolute;
        left: -1px;
        right: -1px;
        top: -1px;
        background-color: #66c039;
      }
    }
  }
}

// tool-switch
.fade-enter-active, .fade-leave-active {
  transition: all .28s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.tool-switch-page1.fade-enter,
.tool-switch-page1.fade-leave-to {
  position: absolute;
  transform: scaleY(0.8);
}

.tool-switch-page2.fade-enter,
.tool-switch-page2.fade-leave-to {
  position: absolute;
  transform: translateY(1.2rem);
}

.tool-switch-page2 {
  &.fixed {
    position: fixed;
    box-shadow: 0 1px 4px #8a8a8a75;
    border-radius: 0 0 6px 6px;
    padding-bottom: 0;
  }
}

//scrollbar
.scrollbar-narrow {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: inherit;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #c2c2c2
  }
}

//question
.questionnaire-container {
  margin: 0 auto;

  .question-subject {
    margin-bottom: 20px;

    .title {
      margin-bottom: 1rem;
      text-align: center;
      font-size: 1.2rem;
      font-weight: 600;
      white-space: pre-wrap;
    }

    .description {
      border-bottom: 1px solid #E0E0E0;
      padding-bottom: 6px;
      margin: 0.5rem 0;
      text-indent: 2rem;
      font-size: 1rem;
      line-height: 1.5rem;
      font-weight: 400;
    }
  }

  .question-subject-edit {
    width: 100%;
    text-align: center;

    .title {
      width: 700px;
      margin: 10px 0;
    }

    .description {
      margin: 20px auto;

      ::v-deep .el-textarea__inner {
        background-color: #f9f9f9;
      }
    }
  }

  .question-box {
    min-height: 84px;
    margin-bottom: 10px;
    padding: 20px;
    border: 1px solid #B5C4DE;
    border-radius: 4px;
    //user-select: none;
    //min-height: 110px;

    .first-level-title {
      display: flex;
      align-items: baseline;

      p {
        margin-bottom: 0;
        margin-top: 0;
      }

      .order {
        min-width: 1rem;
      }

      .request {
        color: red;
        width: 8px;
      }

      .content-html {
        width: 90%;
        padding-left: 3px;
        overflow-wrap: break-word;
        word-break: break-all;
      }
    }

    .sub-item-box {
      padding: 10px 20px;
    }

    .short-box {
      display: none;
    }

    &:hover {
      position: relative;

      .short-box {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 50px;
        background-color: #f0f0f0;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;

        .el-button--mini {
          padding: 2px 10px;
        }
      }
    }
  }

  .default-check {
    display: block;
  }

  .shake {
    border-color: red;
    animation: shake 800ms ease-in-out;
  }

  @keyframes shake {
    10%, 90% {
      transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
      transform: translate3d(+2px, 0, 0);
    }
    30%, 70% {
      transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
      transform: translate3d(+4px, 0, 0);
    }
    50% {
      transform: translate3d(-4px, 0, 0);
    }
  }
}

.line-height-normal {
  line-height: 1.5;

  .cell {
    line-height: 1.5;
  }
}

.el-form-item__content .line-height-normal {
  line-height: 1.5;
  margin-top: 5px;
}

//client compliance rules warning

.compliance-rule-item {
  padding: 10px;
  margin: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 11pt;

  .title {
    font-size: 16px;
    font-weight: bold;
  }
}

.f-14 {
  font-size: 14px;
}

.capitalize {
  text-transform: capitalize;
}


.overflow-break {
  width: 100%;
  overflow-wrap: break-word;
}

// P&L project
.pl-total-box{
  margin-left: 480px;
  margin-top: 20px;
  font-weight: bold;

  .pl-label{
    width: 150px;
    display: inline-block
  }
  .pl-amount{
    width: 170px;
    padding: 6px;
    display: inline-block
  }
}
// P&L client
.client-pl-total-box{
  margin-left: 705px;
  margin-top: 20px;
  font-weight: bold;

  .pl-label{
    width: 130px;
    display: inline-block
  }
  .pl-amount{
    width: 130px;
    padding: 6px;
    display: inline-block
  }
}


// fix browser default
fieldset {
  border: none;
  padding: 0;
}

/* 空白页加载动画高度支撑 */
.app-main > .el-loading-parent--relative {
  min-height: 50px;
}

/* 定制table某列表头隐藏升序排列图标 */
.custom-hidden-sort-asc .sort-caret.ascending {
  display: none !important;
}
