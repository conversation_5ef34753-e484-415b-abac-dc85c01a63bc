import ToolAPI from '@/api/tool'
import _ from 'lodash'

const state = {
  advisor: {
    type: [
      {
        value: 'LEAD',
        label: 'Lead',
      },
      {
        value: 'ADVISOR',
        label: 'Advisor',
      },
    ],
    status: [
      {
        value: 'NOT_CONTACTED',
        label: 'Not Contacted',
      },
      {
        value: 'LINKEDIN_MESSAGE',
        label: 'Linkedin Message',
      },
      {
        value: 'EMAIL',
        label: 'E-mail',
      },
      {
        value: 'LEFT_VM',
        label: 'Left VM',
      },
      {
        value: 'ADVISOR',
        label: 'Advisor',
      },
      {
        value: 'LEAD',
        label: 'Lead',
      },
      {
        value: 'PRE_ADVISOR',
        label: 'Pre Advisor',
      },
      {
        value: 'NOT_INTERESTED',
        label: 'Not Interested',
      },
      {
        value: 'DO_NOT_CONTACT',
        label: 'Do Not Contact',
      },
    ],
    currency: ['USD', 'RMB', 'SGD', 'MYR', 'EUR', 'GBP', 'JPY'],
    name_prefix: ['Dr.'],
    time_minimum_charge: [
      {
        value: 'ONE_HOUR',
        label: '1 Hour',
        minutes: 60,
        hour: 1,
      },
      {
        value: 'THIRTY_MINUTES',
        label: '30 Minutes',
        minutes: 30,
        hour: 0.5,
      },
      {
        value: 'NO_MINIMUM',
        label: 'N/A',
        minutes: 0,
        hour: 0,
      },
    ],
    spoken_language: [
      'English',
      'Chinese (Mandarin)',
      'Japanese',
      'Korean',
      'German',
      'French',
      'Italian',
      'Portuguese (Brazilian)',
      'Portuguese (Portugal)',
      'Spanish',
      'Hindi',
      'Arabic',
      'Czech',
      'Danish',
      'Norwegian',
      'Swedish',
      'Russian',
      'Farsi',
      'Turkish',
      'Thai',
      'Vietnamese',
      'Bahasa Indonesia',
      'Bahasa Melayu',
    ].map(lang => ({ value: lang, label: lang })),
  },
  client: {
    type: [
      {
        value: 'HEDGE_FUND',
        label: 'Hedge Fund',
      },
      {
        value: 'PE_VC',
        label: 'PE / VC',
      },
      {
        value: 'INVESTMENT_BANK',
        label: 'Investment Bank',
      },
      {
        value: 'CONSULTING_FIRM',
        label: 'Consulting Firm',
      },
      {
        value: 'CORPORATE',
        label: 'Corporate',
      },
      {
        value: 'MARKET_RESEARCH',
        label: 'Market Research',
      },
      {
        value: 'ADVERTISING_AGENCY',
        label: 'Advertising Agency',
      },
      {
        value: 'PUBLIC_RELATIONS',
        label: 'Public Relations',
      },
      {
        value: 'OTHER',
        label: 'Other',
      },
    ],
    status: [
      {
        value: 'PROSPECT',
        label: 'Prospect',
      },
      {
        value: 'ENGAGE',
        label: 'Opportunity',
      },
      {
        value: 'OPPORTUNITY_LOST',
        label: 'Opportunity Lost',
      },
      // {
      //   value: 'ACTIVE',
      //   label: 'Active',
      // },
      {
        value: 'EXECUTE',
        label: 'Execute',
      },
      {
        value: 'DORMANT',
        label: 'Dormant',
      },
      {
        value: 'EXPIRED',
        label: 'Expired',
      },
      {
        value: 'CLOSED',
        label: 'Closed',
      },
    ],
    potential: [
      {
        value: 'HIGH',
        label: 'High Potential',
      },
      {
        value: 'MEDIUM',
        label: 'Medium Potential',
      },
      {
        value: 'LOW',
        label: 'Low Potential',
      },
    ],
    product: [
      {
        value: 'CONSULTATION',
        label: 'Consultation',
      },
      {
        value: 'CONFERENCE',
        label: 'Conference',
      },
      {
        value: 'SURVEY',
        label: 'Survey',
      },
      {
        value: 'DUE_DILIGENCE_REPORT',
        label: 'Due Diligence Report',
      },
    ],
    coverage: [
      {
        label: 'Generalist',
        value: 'GENERALIST',
      },
      {
        label: 'Agriculture',
        value: 'AGRICULTURE',
      },
      {
        label: 'Appliance',
        value: 'APPLIANCE',
      },
      {
        label: 'Automotive',
        value: 'AUTOMOTIVE',
      },
      {
        label: 'Chemicals',
        value: 'CHEMICALS',
      },
      {
        label: 'Education',
        value: 'EDUCATION',
      },
      {
        label: 'Energy/Environment',
        value: 'ENERGY_ENVIRONMENT',
      },
      {
        label: 'Engineering',
        value: 'ENGINEERING',
      },
      {
        label: 'Entertainment',
        value: 'ENTERTAINMENT',
      },
      {
        label: 'Finance',
        value: 'FINANCE',
      },
      {
        label: 'Food and Beverage',
        value: 'FOOD_AND_BEVERAGE',
      },
      {
        label: 'Health Care',
        value: 'HEALTH_CARE',
      },
      {
        label: 'Logistics',
        value: 'LOGISTICS',
      },
      {
        label: 'Manufacturing',
        value: 'MANUFACTURING',
      },
      {
        label: 'Materials and Metals',
        value: 'MATERIALS_AND_METALS',
      },
      {
        label: 'Professional Services',
        value: 'PROFESSIONAL_SERVICES',
      },
      {
        label: 'Retail',
        value: 'RETAIL',
      },
      {
        label: 'TMT',
        value: 'TMT',
      },
    ],
    contact_person_type: [
      {
        value: 'COMMON',
        label: 'Common',
      },
      {
        value: 'ASSISTANT',
        label: 'Assistant',
      },
      {
        value: 'ADMINISTRATIVE',
        label: 'Administrative',
      },
      {
        value: 'RENEWAL_CONTRACT',
        label: 'Renewal Contract',
      },
      {
        value: 'LEGAL',
        label: 'Legal',
      },
      {
        value: 'COMPLIANCE',
        label: 'Compliance',
      },
      {
        value: 'BILLING',
        label: 'Finance',
      },
    ],
    contact_status: [
      {
        value: 'ACTIVE',
        label: 'Active',
      },
      {
        value: 'PROSPECT',
        label: 'Prospect',
      },
      {
        value: 'TRANSFERRED',
        label: 'Transferred',
      },
      {
        value: 'INACTIVE',
        label: 'Dormant',
      },
      {
        value: 'LEFT_COMPANY',
        label: 'Left Company',
      },
    ],
    contact_bcg_roles: [
      { value: 'ASSOCIATE_CONSULTANT', label: 'Associate Consultant' },
      { value: 'CONSULTANT', label: 'Consultant' },
      { value: 'PRINCIPAL', label: 'Principal', key: 'principal' },
      { value: 'PARTNER', label: 'Partner', key: 'partner' },
      { value: 'MANAGING_DIRECTOR_AND_PARTNER_MDP', label: 'Managing Director/Partner (MDP)', key: 'mdp' },
      { value: 'PROJECT_LEADER', label: 'Project Leader', key: 'project_leader' },
    ],
    compliance_status: [
      {
        value: 'NOT_YET',
        label: '-',
      },
      {
        value: 'APPLYING',
        label: 'Compliance Rules Pending Approval',
      },
      {
        value: 'APPROVED',
        label: 'Compliance Approved',
      },
      {
        value: 'REJECTED',
        label: 'Rejected',
      },
    ],
    note_types: [
      { value: 'CLIENT', label: 'Client' },
      { value: 'PROJECT', label: 'Project' },
      { value: 'CLIENT_CONTACT', label: 'Client Contact' },
    ],
    to_client_default_profiles_format: [
      {
        value: 'FULL_PROFILES',
        label: 'Full Profiles',
      }, {
        value: 'SHORT_FORM_PROFILES',
        label: 'Short Form Profiles',
      }, {
        value: 'SUMMARY_ONLY',
        label: 'Summary Only',
      }, {
        value: 'SUMMARY_CHARGE',
        label: 'Summary + Nominal Charge',
        need_nominal_charge: true,
      }, {
        value: 'SUMMARY_CHARGE_AND_AVERAGE_CPI',
        label: 'Summary + Nominal Charge + Average CPI',
        need_nominal_charge: true,
      },
    ],
    note_communication_methods: [
      { value: 'VIRTUAL', label: 'Virtual Meeting (Zoom)' },
      { value: 'IN_PERSON', label: 'In-Person Meeting' },
      { value: 'PHONE', label: 'Phone Call' },
      { value: 'EMAIL', label: 'Email' },
      { value: 'OTHER', label: 'Other' },
    ],
  },
  project: {
    type: [
      {
        value: 'CONSULTATION',
        label: 'Consultation',
      },
      {
        value: 'RESEARCH_SERVICE',
        label: 'Research Service',
      },
      {
        value: 'GES_CONVEY',
        label: 'Ges Convey',
      },
      {
        value: 'CONFERENCE',
        label: 'Conference',
      },
    ],
    sub_type: [
      {
        value: 'Consultation',
        label: 'Standard Consultation',
      },
      {
        value: 'Physician Consultation',
        label: 'Physician Consultation',
      },
      {
        value: 'Survey',
        label: 'Survey',
      },
      {
        value: 'Patients',
        label: 'Patients',
      },
      {
        value: 'Investor Call',
        label: 'Investor Call',
      },
      {
        value: 'Longer Term Engagement',
        label: 'Longer Term Engagement',
      },
    ],
    case_type: [
      {
        value: 'DUE_DILIGENCE',
        label: 'Due Diligence',
      },
      {
        value: 'STRATEGY',
        label: 'Strategy',
      },
      {
        value: 'PROPOSAL_OR_LOP',
        label: 'Proposal/LOP',
      },
      {
        value: 'KNOWLEDGE_EFFORT_OR_INTERNAL',
        label: 'Knowledge Effort/Internal',
      },
    ],
    status: [
      // {  value: 0, color: 'primary', label: 'Active', icon: 'play' },
      // { value: 'ADVISORS_NEEDED', color: 'primary', label: 'Advisors Needed' },
      // {
      //   value: 'SUFFICIENT_ADVISORS',
      //   color: 'success',
      //   label: 'Sufficient Advisors',
      // },
      // { value: 'URGENT', color: 'danger', label: 'Urgent' },

      { value: 'ACTIVE', color: 'primary', label: 'Active' },
      { value: 'ON_HOLD', color: 'info', label: 'On Hold' },
      { value: 'CLOSED', color: 'info', label: 'Closed' },
    ],
    industry: [
      { value: 'AGRICULTURE', label: 'Agriculture' },
      { value: 'APPLIANCES', label: 'Appliances' },
      { value: 'AUTOMOTIVE', label: 'Automotive' },
      { value: 'CHEMICALS', label: 'Chemicals' },
      { value: 'EDUCATION', label: 'Education' },
      { value: 'ENERGY_ENVIRONMENT', label: 'Energy / Environment' },
      { value: 'ENGINEERING', label: 'Engineering' },
      { value: 'ENTERTAINMENT', label: 'Entertainment' },
      { value: 'FINANCE', label: 'Finance' },
      { value: 'FOOD_AND_BEVERAGE', label: 'Food and Beverage' },
      { value: 'HEALTH_CARE', label: 'Health Care' },
      { value: 'LOGISTICS', label: 'Logistics' },
      { value: 'MANUFACTURING', label: 'Manufacturing' },
      { value: 'MATERIALS_AND_METALS', label: 'Materials and Metals' },
      { value: 'PROFESSIONAL_SERVICES', label: 'Professional Services' },
      { value: 'RETAIL', label: 'Retail' },
      { value: 'TMT', label: 'TMT' },
    ],
    task: {
      status: [
        { value: -1, label: 'All Status' },
        { value: 1, label: 'Emailed' },
        { value: 2, label: 'Responded' },
        { value: 3, label: 'Sent to compliance' },
        { value: 4, label: 'Approved' },
        { value: 5, label: 'Sent to client' },
        { value: 6, label: 'Scheduled' },
        { value: 7, label: 'Completed' },
        { value: 8, label: 'Rated' },
      ],
      time_range_type: [
        { value: 1, label: 'Emailed' },
        { value: 2, label: 'Responded' },
      ],
      mark: [
        { value: 'UNKNOWN', label: 'Unknown', color: 'info' },
        { value: 'GOOD', label: 'Good', color: 'success' },
        { value: 'GENERAL', label: 'General', color: 'warning' },
        { value: 'BAD', label: 'Bad', color: 'danger' },
      ],
      recent_activities: [
        { value: 0, label: 'Any time' },
        { value: 1, label: 'last 5 mins' },
        { value: 2, label: 'last 1 hour' },
        { value: 3, label: 'last yesterday' },
        { value: 4, label: 'last 3 days' },
        { value: 5, label: 'last 7 days' },
        { value: 6, label: 'last 1 month' },
      ],
      order_by: [
        { value: 'rank asc', label: 'Order By: ID' },
        { value: 'latest_contact_send.create_at desc,create_at asc,id asc', label: 'Order By: Sent to Client' },
      ],
      sub_type: [
        { label: 'Consultation', value: 'CONSULTATION' },
        { label: 'Investor Call', value: 'INVESTOR_CALL' },
        { label: 'Regular Phone Consultation', value: 'REGULAR_PHONE_CONSULTATION' },
        { label: 'Follow Up Phone Consultation', value: 'FOLLOW_UP_PHONE_CONSULTATION' },
        { label: 'Written Follow Up', value: 'WRITTEN_FOLLOW_UP' },
        { label: 'Phone', value: 'PHONE' },
        { label: 'Survey', value: 'SURVEY' },
        { label: 'BD Call', value: 'BD_CALL' },
        { label: 'Consultation-Survey', value: 'CONSULTATION_SURVEY' },
        { label: 'Consultation-Conference', value: 'CONSULTATION_CONFERENCE' }
      ],
    },
    common_type: [
      {
        value: 'RESEARCH_SERVICE', label: 'Research Service', children: [
          { value: 'GRM Customized Reports', label: 'GRM Customized Reports' },
          {
            value: 'GRM Customized Data Services',
            label: 'GRM Customized Data Services',
          },
          { value: 'GRM DD Services', label: 'GRM DD Services' },
          {
            value: 'GRM Non-Customized Research',
            label: 'GRM Non-Customized Research',
          },
          { value: 'GRM FA', label: 'GRM FA' },
          { value: 'GRM Others', label: 'GRM Others' },
          { value: 'MF RM regular reports', label: 'MF RM regular reports' },
          {
            value: 'MF RM customized reports',
            label: 'MF RM customized reports',
          },
          { value: 'MF RM others', label: 'MF RM others' },

        ],
      },
      {
        value: 'GES_CONVEY', label: 'GES Convey', children: [
          { value: '2B Project', label: '2B Project' },
          { value: '2C Project', label: '2C Project' },
          { value: 'Survey', label: 'Survey' },
          { value: 'FA Opportunity', label: 'FA Opportunity' },
          {
            value: 'Investigative Due Diligence',
            label: 'Investigative Due Diligence',
          },
          { value: 'GES Consultation', label: 'GES Consultation' },
        ],
      },
      {
        value: 'CONFERENCE', label: 'Conference', children: [
          { value: 'Tele60', label: 'Tele60' },
          { value: 'Events', label: 'Events' },
          { value: 'MF Tele 60', label: 'MF Tele 60' },
          { value: 'MF Events', label: 'MF Events' },
          { value: 'Newstalk', label: 'Newstalk' },
          { value: 'Capvision Live', label: 'Capvision Live' },
          { value: 'Training', label: 'Training' },
        ],
      },
    ],
  },
  company: {
    industry: [
      { value: 'AGRICULTURE', label: 'Agriculture' },
      { value: 'APPLIANCES', label: 'Appliances' },
      { value: 'AUTOMOTIVE', label: 'Automotive' },
      { value: 'CHEMICALS', label: 'Chemicals' },
      { value: 'EDUCATION', label: 'Education' },
      { value: 'ENERGY_ENVIRONMENT', label: 'Energy / Environment' },
      { value: 'ENGINEERING', label: 'Engineering' },
      { value: 'ENTERTAINMENT', label: 'Entertainment' },
      { value: 'FINANCE', label: 'Finance' },
      { value: 'FOOD_AND_BEVERAGE', label: 'Food and Beverage' },
      { value: 'HEALTH_CARE', label: 'Health Care' },
      { value: 'LOGISTICS', label: 'Logistics' },
      { value: 'MANUFACTURING', label: 'Manufacturing' },
      { value: 'MATERIALS_AND_METALS', label: 'Materials and Metals' },
      { value: 'PROFESSIONAL_SERVICES', label: 'Professional Services' },
      { value: 'RETAIL', label: 'Retail' },
      { value: 'TMT', label: 'TMT' },
    ],
    classification_type: [
      { value: 'SOE', label: 'State Owned Enterprise' },
      { value: 'GOVERNMENT_US_FEDERAL', label: 'Government (US Federal)' },
      { value: 'GOVERNMENT_US_STATE_OR_LOCAL', label: 'Government (US State or Local)' },
      { value: 'GOVERNMENT_NON_US', label: 'Government (Non-US)' },
      { value: null, label: 'N/A' },
    ],
    type: [
      { value: 'PRIVATE', label: 'Private' },
      { value: 'LISTED', label: 'Listed' },
      { value: 'PART_OF_LISTED_GROUP', label: 'Part of listed group' },
    ],
    type_filters: [
      { value: 'PRIVATE', text: 'Private' },
      { value: 'LISTED', text: 'Listed' },
      { value: 'PART_OF_LISTED_GROUP', text: 'Part of listed group' },
    ],
    industry_filters: [
      { value: 'AGRICULTURE', text: 'Agriculture' },
      { value: 'APPLIANCES', text: 'Appliances' },
      { value: 'AUTOMOTIVE', text: 'Automotive' },
      { value: 'CHEMICALS', text: 'Chemicals' },
      { value: 'EDUCATION', text: 'Education' },
      { value: 'ENERGY_ENVIRONMENT', text: 'Energy / Environment' },
      { value: 'ENGINEERING', text: 'Engineering' },
      { value: 'ENTERTAINMENT', text: 'Entertainment' },
      { value: 'FINANCE', text: 'Finance' },
      { value: 'FOOD_AND_BEVERAGE', text: 'Food and Beverage' },
      { value: 'HEALTH_CARE', text: 'Health Care' },
      { value: 'LOGISTICS', text: 'Logistics' },
      { value: 'MANUFACTURING', text: 'Manufacturing' },
      { value: 'MATERIALS_AND_METALS', text: 'Materials and Metals' },
      { value: 'PROFESSIONAL_SERVICES', text: 'Professional Services' },
      { value: 'RETAIL', text: 'Retail' },
      { value: 'TMT', text: 'TMT' },
    ],
    black_status_filters: [
      { value: 'TBA', text: 'TBA' },
      { value: 'Blacklist', text: 'Blacklist' },
    ],
    stock_exchanges: _.sortBy([
      /* Zagreb Stock Exchange、Sydney、Mexican Bolsa、Borsa Espanoles Stock Exchange、
      Nigerian Exchange Group、Nigerian Stock Exchange
       id 仅为 key，无意义*/
      {
        'english_name': 'Brazil Stock Exchange (B3)',
        'id': 9908,
      },
      {
        'english_name': 'Nigerian Stock Exchange',
        'id': 9907,
        'name': 'TSX Venture',
        'nickname': 'TSX Venture',
      },
      {
        'english_name': 'Nigerian Exchange Group',
        'id': 9906,
        'name': 'TSX Venture',
        'nickname': 'TSX Venture',
      },
      {
        'english_name': 'TSX Venture Exchange (TSX)',
        'id': 9905,
        'name': 'TSX Venture',
        'nickname': 'TSX Venture',
      }, {
        'english_name': 'Warsaw Stock Exchange',
        'id': 9903,
        'name': 'Warsaw',
        'nickname': 'Warsaw',
      },
      {
        'english_name': 'Canadian Securities Exchange (CNSX)',
        'id': 9904,
        'name': 'Canadian',
        'nickname': 'Canadian',
      },
      {
        'english_name': 'Zagreb Stock Exchange',
        'id': 9989,
        'name': 'Zagreb',
        'nickname': 'Zagreb',
      },
      {
        'english_name': 'Sydney Stock Exchange',
        'id': 9900,
        'name': 'Sydney ',
        'nickname': 'Sydney',
      },
      {
        'english_name': 'Mexican Bolsa',
        'id': 9901,
        'name': 'Mexican Bolsa',
        'nickname': 'Mexican Bolsa',
      },
      {
        'english_name': 'Borsa Espanoles',
        'id': 9902,
        'name': 'Borsa Espanoles',
        'nickname': 'Borsa Espanoles',
      },
      {
        'company_count': 41736,
        'cre_uid': 0,
        'english_name': 'Tel Aviv Stock Exchange',
        'id': 99,
        'name': 'Israel ',
        'nickname': 'Shenzhen (SZSE)',
        'shortcode': 'SZ',
        'upd_uid': 0,
      },
      {
        'company_count': 41736,
        'cre_uid': 0,
        'english_name': 'Shenzhen Stock Exchange',
        'id': 8,
        'name': '深圳 ',
        'nickname': 'Shenzhen (SZSE)',
        'shortcode': 'SZ',
        'upd_uid': 0,
      },
      {
        'company_count': 22836,
        'cre_uid': 0,
        'english_name': 'Shanghai Stock Exchange',
        'id': 9,
        'name': '上海',
        'nickname': 'Shanghai (SSE)',
        'shortcode': 'SH',
        'upd_uid': 0,
      },
      {
        'company_count': 4882,
        'cre_uid': 0,
        'english_name': 'Hong Kong Stock Exchange',
        'id': 3,
        'name': '香港联交所',
        'nickname': 'HKSE',
        'shortcode': 'HK',
        'upd_uid': 0,
      },
      {
        'company_count': 4122,
        'cre_uid': 0,
        'english_name': 'NASDAQ',
        'id': 11,
        'name': '纳斯达克',
        'nickname': 'NASDAQ',
        'shortcode': 'NASDAQ',
        'upd_uid': 0,
      },
      {
        'company_count': 4045,
        'cre_uid': 0,
        'english_name': 'New York Stock Exchange',
        'id': 10,
        'name': '纽约证券交易所 ',
        'nickname': 'NYSE',
        'shortcode': 'NYSE',
        'upd_uid': 0,
      },
      {
        'company_count': 776,
        'cre_uid': 1138,
        'english_name': 'National Equities Exchange and Quotations',
        'id': 23,
        'name': '新三板',
        'nickname': 'New OCT Market',
        'shortcode': 'NEEQ',
        'upd_uid': 1138,
      },
      {
        'company_count': 547,
        'cre_uid': 0,
        'english_name': 'Tokyo Stock Exchange',
        'id': 17,
        'name': '东京证券交易所 ',
        'nickname': 'Tokyo',
        'shortcode': 'TYO',
        'upd_uid': 0,
      },
      {
        'company_count': 400,
        'cre_uid': 0,
        'english_name': 'American Stock Exchange',
        'id': 12,
        'name': '美国证券交易所',
        'nickname': 'American',
        'shortcode': 'AMEX',
        'upd_uid': 1138,
      },
      {
        'company_count': 227,
        'cre_uid': 0,
        'english_name': 'Korea Exchange',
        'id': 14,
        'name': '韩国证券交易所 ',
        'nickname': 'Korea',
        'shortcode': 'KRX',
        'upd_uid': 1138,
      },
      {
        'company_count': 195,
        'cre_uid': 0,
        'english_name': 'Taiwan Stock Exchange',
        'id': 5,
        'name': '台湾证券交易所',
        'nickname': 'Taiwan',
        'shortcode': 'TWSE',
        'upd_uid': 0,
      },
      {
        'company_count': 192,
        'cre_uid': 0,
        'english_name': 'Frankfurt Stock Exchange ',
        'id': 15,
        'name': '法兰克福证券交易所',
        'nickname': 'Frankfurt',
        'shortcode': 'FWB',
        'upd_uid': 0,
      },
      {
        'company_count': 124,
        'cre_uid': 1138,
        'english_name': 'Euronext N.V',
        'id': 26,
        'name': '泛欧证券交易所',
        'nickname': 'Euronext NV',
        'shortcode': 'Euronext',
        'upd_uid': 1138,
      },
      {
        'company_count': 102,
        'cre_uid': 0,
        'english_name': 'London Stock Exchange',
        'id': 13,
        'name': '伦敦证券交易所 ',
        'nickname': 'London',
        'shortcode': 'LSE',
        'upd_uid': 0,
      },
      {
        'company_count': 96,
        'cre_uid': 0,
        'english_name': 'Paris Stock Exchange',
        'id': 18,
        'name': '巴黎证券交易所 ',
        'nickname': 'Paris',
        'shortcode': 'PA',
        'upd_uid': 0,
      },
      {
        'company_count': 71,
        'cre_uid': 0,
        'english_name': 'SIX Swiss Exchange',
        'id': 22,
        'name': '瑞士证券交易所',
        'nickname': 'Swiss',
        'shortcode': 'SIX',
        'upd_uid': 0,
      },
      {
        'company_count': 56,
        'cre_uid': 0,
        'english_name': 'Singapore Exchange',
        'id': 2,
        'name': '新加坡证券交易所',
        'nickname': 'Singapore',
        'shortcode': 'SGX',
        'upd_uid': 0,
      },
      {
        'company_count': 37,
        'cre_uid': 0,
        'english_name': 'TAISDAQ',
        'id': 6,
        'name': '台湾OTC市场',
        'nickname': 'Taiwan OTC',
        'shortcode': 'TWOTC',
        'upd_uid': 0,
      },
      {
        'company_count': 27,
        'cre_uid': 0,
        'english_name': 'Australian Securities Exchange',
        'id': 19,
        'name': '澳大利亚证券交易所',
        'nickname': 'Australia',
        'shortcode': 'ASX',
        'upd_uid': 0,
      },
      {
        'company_count': 23,
        'cre_uid': 0,
        'english_name': 'Stockholm Stock Exchange',
        'id': 7,
        'name': '斯德哥尔摩证券市场',
        'nickname': 'Stockholm',
        'shortcode': 'ST',
        'upd_uid': 0,
      },
      {
        'company_count': 21,
        'cre_uid': 0,
        'english_name': 'Amsterdam Stock Exchange',
        'id': 21,
        'name': '阿姆斯特丹证券交易所',
        'nickname': 'Amsterdam',
        'shortcode': 'AS',
        'upd_uid': 0,
      },
      {
        'company_count': 20,
        'cre_uid': 1138,
        'english_name': 'National Stock Exchange of India',
        'id': 28,
        'name': '印度国家证券交易所',
        'nickname': 'India',
        'shortcode': 'NSE',
        'upd_uid': 1138,
      },
      {
        'company_count': 18,
        'cre_uid': 0,
        'english_name': 'Toronto Stock Exchange',
        'id': 16,
        'name': '多伦多证券交易所 ',
        'nickname': 'Toronto',
        'shortcode': 'TSX',
        'upd_uid': 0,
      },
      {
        'company_count': 16,
        'cre_uid': 0,
        'english_name': 'Borsa Italiana',
        'id': 1,
        'name': '意大利证券交易所',
        'nickname': 'Milan',
        'shortcode': 'BIT',
        'upd_uid': 0,
      },
      {
        'company_count': 16,
        'cre_uid': 1138,
        'english_name': 'Bursa Malaysia',
        'id': 33,
        'name': '马来西亚股票交易所',
        'nickname': 'Bursa Malaysia Berhad',
        'shortcode': 'MYX',
        'upd_uid': 1138,
      },
      {
        'company_count': 14,
        'cre_uid': 1330,
        'english_name': 'Taiwan Stock Exchange Corporation',
        'id': 39,
        'name': '台北政治經濟交易所',
        'upd_uid': 0,
      },
      {
        'company_count': 10,
        'cre_uid': 1138,
        'english_name': 'Philippine Stock Exchange',
        'id': 36,
        'name': '菲律宾证券交易所',
        'nickname': 'Philippine',
        'shortcode': 'PSE',
        'upd_uid': 1138,
      },
      {
        'company_count': 10,
        'cre_uid': 1138,
        'english_name': 'Bolsas y Mercados Españoles',
        'id': 29,
        'name': '马德里证券交易所',
        'nickname': 'Madrid',
        'shortcode': 'BME',
        'upd_uid': 1138,
      },
      {
        'company_count': 8,
        'cre_uid': 1566,
        'english_name': 'Stock Exchange of Thailand',
        'id': 35,
        'name': '泰国证券交易所',
        'nickname': 'Thailand',
        'shortcode': 'SET',
        'upd_uid': 1138,
      },
      {
        'company_count': 5,
        'cre_uid': 1138,
        'english_name': 'Helsinki Stock Exchange',
        'id': 31,
        'name': '赫尔辛基证券交易所',
        'nickname': 'Helsinki',
        'shortcode': 'Nasdaq Helsinki',
        'upd_uid': 1138,
      },
      {
        'company_count': 5,
        'cre_uid': 1138,
        'english_name': 'New Zealand Exchange',
        'id': 32,
        'name': '新西兰证券交易所',
        'nickname': 'New Zealand',
        'shortcode': 'NZX',
        'upd_uid': 1138,
      },
      {
        'company_count': 4,
        'cre_uid': 1330,
        'english_name': 'BM & FBOVESPA',
        'id': 37,
        'name': '巴西圣保罗证券交易所',
        'nickname': 'BM&F Bovespa',
        'shortcode': 'BM&F Bovespa',
        'upd_uid': 1330,
      },
      {
        'company_count': 4,
        'cre_uid': 0,
        'english_name': 'Oslo Stock Exchange',
        'id': 20,
        'name': '奥斯陆证券交易所',
        'nickname': 'Oslo',
        'shortcode': 'OSE',
        'upd_uid': 0,
      },
      {
        'company_count': 2,
        'cre_uid': 1138,
        'english_name': 'Vietnam Exchange',
        'id': 34,
        'name': '越南证券交易所',
        'nickname': 'Vietnam',
        'upd_uid': 0,
      },
      {
        'company_count': 2,
        'cre_uid': 1138,
        'english_name': 'Athens Stock Exchange',
        'id': 27,
        'name': '雅典证券交易所',
        'nickname': 'Athens',
        'shortcode': 'Athex',
        'upd_uid': 1138,
      },
      {
        'company_count': 1,
        'cre_uid': 1,
        'english_name': 'Indonesia Stock Exchange',
        'id': 40,
        'name': '印度尼西亚证券交易所',
        'nickname': 'Indonesia Stock Exchange',
        'shortcode': 'IDX',
        'upd_uid': 0,
      },
      {
        'company_count': 1,
        'cre_uid': 0,
        'english_name': 'Vienna Stock Exchange',
        'id': 4,
        'name': '维也纳证券交易所',
        'nickname': 'Vienna',
        'shortcode': 'WBAG',
        'upd_uid': 1138,
      },
      {
        'company_count': 1,
        'cre_uid': 1138,
        'english_name': 'Abu Dhabi Securities Exchange',
        'id': 24,
        'name': '阿布扎比证券交易所',
        'nickname': 'Abu Dhabi',
        'shortcode': 'ADX',
        'upd_uid': 1138,
      },
      {
        'company_count': 1,
        'cre_uid': 1138,
        'english_name': 'Moscow Exchange',
        'id': 30,
        'name': '莫斯科证券交易所',
        'nickname': 'Moscow',
        'shortcode': 'MSE',
        'upd_uid': 1138,
      },
      {
        'company_count': 1,
        'cre_uid': 1,
        'english_name': 'National Equities Exchange and Quotations Select Layer',
        'id': 41,
        'name': '新三板精选层',
        'shortcode': 'NEEQ Select',
        'upd_uid': 0,
      },
      {
        'company_count': 1,
        'cre_uid': 1138,
        'english_name': 'JSE Stock Exchange',
        'id': 38,
        'name': '约翰内斯堡证券交易所',
        'shortcode': 'JSE',
        'upd_uid': 0,
      },
      {
        'english_name': 'Bombay Stock Exchange',
        'id': 999,
      },
    ], 'english_name'),
  },
  contactInfo: [
    {
      value: 'EMAIL',
      label: 'Email',
    }, {
      value: 'PHONE',
      label: 'Phone',
    }, {
      value: 'LINKEDIN_URL',
      label: 'LinkedIn Url',
    }],
  email: {
    // type: [{
    //   value: 'CUSTOM',
    //   label: '自定义'
    // }, {
    //   value: 'SIGN_TC',
    //   label: '邀请专家签订 TC'
    // }, {
    //   value: 'SCREENING_CA_AVAILABILITY',
    //   label: '邀请专家回答 SCREENING,CA,AVAILABILITY'
    // }]
  },
  allCountryList: [],
  allContinentList: [],
  contract: {
    payWay: [
      {
        value: 'TRIAL',
        label: 'Trial',
      }, {
        value: 'PREPAY',
        label: 'Prepay',
      }, {
        value: 'PAYGO',
        label: 'Pay Go',
      }, {
        value: 'PROJECT_BASE',
        label: 'Project Base',
      }],
    request_status: [
      { value: 'DRAFT', label: 'Draft', color: 'info' },
      { value: 'IN_REQUEST', label: 'In Request', color: 'primary' },
      { value: 'REQUEST_COMPLETED', label: 'Complete', color: 'success' },
      { value: 'REQUEST_REJECTED', label: 'Rejected', color: 'danger' },
    ],
    apply_status: [
      { value: 'NOT_YET', label: 'Not Yet', color: 'info' },
      { value: 'APPLYING', label: 'Applying', color: 'primary' },
      { value: 'REJECTED', label: 'Rejected', color: 'danger' },
      { value: 'APPROVED', label: 'Approved', color: 'success' },
      { value: null, label: 'Total', color: 'info' },
    ],
    approval_status: [
      // { value: 'DRAFT', label: 'Draft', color: 'info' },
      { value: 'DRAFT_REQUESTED', label: 'Draft Requested', color: 'primary' },
      { value: 'DRAFT_CREATED', label: 'Draft Created', color: 'success' },
      {
        value: 'REQUEST_REJECTED',
        label: 'Requested Rejected',
        color: 'danger',
      },
      {
        value: 'PENDING_APPROVAL',
        label: 'Pending Approval',
        color: 'primary',
      },
      { value: 'REJECTED', label: 'Rejected', color: 'danger' },
      { value: 'APPROVED', label: 'Approved', color: 'success' },
    ],
    effective_status: [
      { value: 'EFFECTIVE', label: 'Effective', color: 'success' },
      { value: 'INEFFECTIVE', label: 'Ineffective', color: 'info' },
      { value: 'EXPIRED', label: 'Expired', color: 'info' },
    ],
  },
  lang_type: [
    { value: 'en-US', label: 'EN-US' },
    { value: 'zh-CN', label: 'ZH-CN' },
    { value: 'en-zh', label: 'EN & ZH' },
  ],
  account_type_options: [
    { id: '-1', value: '' },
    { id: '0', value: 'CHECKING' },
    { id: '1', value: 'SAVING' },
  ],
  receiver_type_options: [
    { id: '0', value: 'ONESELF' },
    { id: '1', value: 'ANOTHER_PERSON' },
    { id: '2', value: 'CORPORATION' },
  ],
  timezone_list: [
    { id: 'Africa/Abidjan', name: 'Africa/Abidjan' },
    { id: 'Africa/Accra', name: 'Africa/Accra' },
    { id: 'Africa/Addis_Ababa', name: 'Africa/Addis Ababa' },
    { id: 'Africa/Algiers', name: 'Africa/Algiers' },
    { id: 'Africa/Asmara', name: 'Africa/Asmara' },
    { id: 'Africa/Bamako', name: 'Africa/Bamako' },
    { id: 'Africa/Bangui', name: 'Africa/Bangui' },
    { id: 'Africa/Banjul', name: 'Africa/Banjul' },
    { id: 'Africa/Bissau', name: 'Africa/Bissau' },
    { id: 'Africa/Blantyre', name: 'Africa/Blantyre' },
    { id: 'Africa/Brazzaville', name: 'Africa/Brazzaville' },
    { id: 'Africa/Bujumbura', name: 'Africa/Bujumbura' },
    { id: 'Africa/Cairo', name: 'Africa/Cairo' },
    { id: 'Africa/Casablanca', name: 'Africa/Casablanca' },
    { id: 'Africa/Ceuta', name: 'Africa/Ceuta' },
    { id: 'Africa/Conakry', name: 'Africa/Conakry' },
    { id: 'Africa/Dakar', name: 'Africa/Dakar' },
    { id: 'Africa/Dar_es_Salaam', name: 'Africa/Dar es Salaam' },
    { id: 'Africa/Djibouti', name: 'Africa/Djibouti' },
    { id: 'Africa/Douala', name: 'Africa/Douala' },
    { id: 'Africa/El_Aaiun', name: 'Africa/El Aaiun' },
    { id: 'Africa/Freetown', name: 'Africa/Freetown' },
    { id: 'Africa/Gaborone', name: 'Africa/Gaborone' },
    { id: 'Africa/Harare', name: 'Africa/Harare' },
    { id: 'Africa/Johannesburg', name: 'Africa/Johannesburg' },
    { id: 'Africa/Juba', name: 'Africa/Juba' },
    { id: 'Africa/Kampala', name: 'Africa/Kampala' },
    { id: 'Africa/Khartoum', name: 'Africa/Khartoum' },
    { id: 'Africa/Kigali', name: 'Africa/Kigali' },
    { id: 'Africa/Kinshasa', name: 'Africa/Kinshasa' },
    { id: 'Africa/Lagos', name: 'Africa/Lagos' },
    { id: 'Africa/Libreville', name: 'Africa/Libreville' },
    { id: 'Africa/Lome', name: 'Africa/Lome' },
    { id: 'Africa/Luanda', name: 'Africa/Luanda' },
    { id: 'Africa/Lubumbashi', name: 'Africa/Lubumbashi' },
    { id: 'Africa/Lusaka', name: 'Africa/Lusaka' },
    { id: 'Africa/Malabo', name: 'Africa/Malabo' },
    { id: 'Africa/Maputo', name: 'Africa/Maputo' },
    { id: 'Africa/Maseru', name: 'Africa/Maseru' },
    { id: 'Africa/Mbabane', name: 'Africa/Mbabane' },
    { id: 'Africa/Mogadishu', name: 'Africa/Mogadishu' },
    { id: 'Africa/Monrovia', name: 'Africa/Monrovia' },
    { id: 'Africa/Nairobi', name: 'Africa/Nairobi' },
    { id: 'Africa/Ndjamena', name: 'Africa/Ndjamena' },
    { id: 'Africa/Niamey', name: 'Africa/Niamey' },
    { id: 'Africa/Nouakchott', name: 'Africa/Nouakchott' },
    { id: 'Africa/Ouagadougou', name: 'Africa/Ouagadougou' },
    { id: 'Africa/Porto-Novo', name: 'Africa/Porto-Novo' },
    { id: 'Africa/Sao_Tome', name: 'Africa/Sao Tome' },
    { id: 'Africa/Tripoli', name: 'Africa/Tripoli' },
    { id: 'Africa/Tunis', name: 'Africa/Tunis' },
    { id: 'Africa/Windhoek', name: 'Africa/Windhoek' },
    { id: 'America/Adak', name: 'America/Adak' },
    { id: 'America/Anchorage', name: 'America/Anchorage' },
    { id: 'America/Anguilla', name: 'America/Anguilla' },
    { id: 'America/Antigua', name: 'America/Antigua' },
    { id: 'America/Araguaina', name: 'America/Araguaina' },
    {
      id: 'America/Argentina/Buenos_Aires',
      name: 'America/Argentina/Buenos Aires',
    },
    { id: 'America/Argentina/Catamarca', name: 'America/Argentina/Catamarca' },
    { id: 'America/Argentina/Cordoba', name: 'America/Argentina/Cordoba' },
    { id: 'America/Argentina/Jujuy', name: 'America/Argentina/Jujuy' },
    { id: 'America/Argentina/La_Rioja', name: 'America/Argentina/La Rioja' },
    { id: 'America/Argentina/Mendoza', name: 'America/Argentina/Mendoza' },
    {
      id: 'America/Argentina/Rio_Gallegos',
      name: 'America/Argentina/Rio Gallegos',
    },
    { id: 'America/Argentina/Salta', name: 'America/Argentina/Salta' },
    { id: 'America/Argentina/San_Juan', name: 'America/Argentina/San Juan' },
    { id: 'America/Argentina/San_Luis', name: 'America/Argentina/San Luis' },
    { id: 'America/Argentina/Tucuman', name: 'America/Argentina/Tucuman' },
    { id: 'America/Argentina/Ushuaia', name: 'America/Argentina/Ushuaia' },
    { id: 'America/Aruba', name: 'America/Aruba' },
    { id: 'America/Asuncion', name: 'America/Asuncion' },
    { id: 'America/Atikokan', name: 'America/Atikokan' },
    { id: 'America/Bahia', name: 'America/Bahia' },
    { id: 'America/Bahia_Banderas', name: 'America/Bahia Banderas' },
    { id: 'America/Barbados', name: 'America/Barbados' },
    { id: 'America/Belem', name: 'America/Belem' },
    { id: 'America/Belize', name: 'America/Belize' },
    { id: 'America/Blanc-Sablon', name: 'America/Blanc-Sablon' },
    { id: 'America/Boa_Vista', name: 'America/Boa Vista' },
    { id: 'America/Bogota', name: 'America/Bogota' },
    { id: 'America/Boise', name: 'America/Boise' },
    { id: 'America/Cambridge_Bay', name: 'America/Cambridge Bay' },
    { id: 'America/Campo_Grande', name: 'America/Campo Grande' },
    { id: 'America/Cancun', name: 'America/Cancun' },
    { id: 'America/Caracas', name: 'America/Caracas' },
    { id: 'America/Cayenne', name: 'America/Cayenne' },
    { id: 'America/Cayman', name: 'America/Cayman' },
    { id: 'America/Chicago', name: 'America/Chicago' },
    { id: 'America/Chihuahua', name: 'America/Chihuahua' },
    { id: 'America/Costa_Rica', name: 'America/Costa Rica' },
    { id: 'America/Creston', name: 'America/Creston' },
    { id: 'America/Cuiaba', name: 'America/Cuiaba' },
    { id: 'America/Curacao', name: 'America/Curacao' },
    { id: 'America/Danmarkshavn', name: 'America/Danmarkshavn' },
    { id: 'America/Dawson', name: 'America/Dawson' },
    { id: 'America/Dawson_Creek', name: 'America/Dawson Creek' },
    { id: 'America/Denver', name: 'America/Denver' },
    { id: 'America/Detroit', name: 'America/Detroit' },
    { id: 'America/Dominica', name: 'America/Dominica' },
    { id: 'America/Edmonton', name: 'America/Edmonton' },
    { id: 'America/Eirunepe', name: 'America/Eirunepe' },
    { id: 'America/El_Salvador', name: 'America/El Salvador' },
    { id: 'America/Fort_Nelson', name: 'America/Fort Nelson' },
    { id: 'America/Fortaleza', name: 'America/Fortaleza' },
    { id: 'America/Glace_Bay', name: 'America/Glace Bay' },
    { id: 'America/Godthab', name: 'America/Godthab' },
    { id: 'America/Goose_Bay', name: 'America/Goose Bay' },
    { id: 'America/Grand_Turk', name: 'America/Grand Turk' },
    { id: 'America/Grenada', name: 'America/Grenada' },
    { id: 'America/Guadeloupe', name: 'America/Guadeloupe' },
    { id: 'America/Guatemala', name: 'America/Guatemala' },
    { id: 'America/Guayaquil', name: 'America/Guayaquil' },
    { id: 'America/Guyana', name: 'America/Guyana' },
    { id: 'America/Halifax', name: 'America/Halifax' },
    { id: 'America/Havana', name: 'America/Havana' },
    { id: 'America/Hermosillo', name: 'America/Hermosillo' },
    {
      id: 'America/Indiana/Indianapolis',
      name: 'America/Indiana/Indianapolis',
    },
    { id: 'America/Indiana/Knox', name: 'America/Indiana/Knox' },
    { id: 'America/Indiana/Marengo', name: 'America/Indiana/Marengo' },
    { id: 'America/Indiana/Petersburg', name: 'America/Indiana/Petersburg' },
    { id: 'America/Indiana/Tell_City', name: 'America/Indiana/Tell City' },
    { id: 'America/Indiana/Vevay', name: 'America/Indiana/Vevay' },
    { id: 'America/Indiana/Vincennes', name: 'America/Indiana/Vincennes' },
    { id: 'America/Indiana/Winamac', name: 'America/Indiana/Winamac' },
    { id: 'America/Inuvik', name: 'America/Inuvik' },
    { id: 'America/Iqaluit', name: 'America/Iqaluit' },
    { id: 'America/Jamaica', name: 'America/Jamaica' },
    { id: 'America/Juneau', name: 'America/Juneau' },
    { id: 'America/Kentucky/Louisville', name: 'America/Kentucky/Louisville' },
    { id: 'America/Kentucky/Monticello', name: 'America/Kentucky/Monticello' },
    { id: 'America/Kralendijk', name: 'America/Kralendijk' },
    { id: 'America/La_Paz', name: 'America/La Paz' },
    { id: 'America/Lima', name: 'America/Lima' },
    { id: 'America/Los_Angeles', name: 'America/Los Angeles' },
    { id: 'America/Lower_Princes', name: 'America/Lower Princes' },
    { id: 'America/Maceio', name: 'America/Maceio' },
    { id: 'America/Managua', name: 'America/Managua' },
    { id: 'America/Manaus', name: 'America/Manaus' },
    { id: 'America/Marigot', name: 'America/Marigot' },
    { id: 'America/Martinique', name: 'America/Martinique' },
    { id: 'America/Matamoros', name: 'America/Matamoros' },
    { id: 'America/Mazatlan', name: 'America/Mazatlan' },
    { id: 'America/Menominee', name: 'America/Menominee' },
    { id: 'America/Merida', name: 'America/Merida' },
    { id: 'America/Metlakatla', name: 'America/Metlakatla' },
    { id: 'America/Mexico_City', name: 'America/Mexico City' },
    { id: 'America/Miquelon', name: 'America/Miquelon' },
    { id: 'America/Moncton', name: 'America/Moncton' },
    { id: 'America/Monterrey', name: 'America/Monterrey' },
    { id: 'America/Montevideo', name: 'America/Montevideo' },
    { id: 'America/Montserrat', name: 'America/Montserrat' },
    { id: 'America/Nassau', name: 'America/Nassau' },
    { id: 'America/New_York', name: 'America/New York' },
    { id: 'America/Nipigon', name: 'America/Nipigon' },
    { id: 'America/Nome', name: 'America/Nome' },
    { id: 'America/Noronha', name: 'America/Noronha' },
    { id: 'America/North_Dakota/Beulah', name: 'America/North Dakota/Beulah' },
    { id: 'America/North_Dakota/Center', name: 'America/North Dakota/Center' },
    {
      id: 'America/North_Dakota/New_Salem',
      name: 'America/North Dakota/New Salem',
    },
    { id: 'America/Ojinaga', name: 'America/Ojinaga' },
    { id: 'America/Panama', name: 'America/Panama' },
    { id: 'America/Pangnirtung', name: 'America/Pangnirtung' },
    { id: 'America/Paramaribo', name: 'America/Paramaribo' },
    { id: 'America/Phoenix', name: 'America/Phoenix' },
    { id: 'America/Port-au-Prince', name: 'America/Port-au-Prince' },
    { id: 'America/Port_of_Spain', name: 'America/Port of Spain' },
    { id: 'America/Porto_Velho', name: 'America/Porto Velho' },
    { id: 'America/Puerto_Rico', name: 'America/Puerto Rico' },
    { id: 'America/Rainy_River', name: 'America/Rainy River' },
    { id: 'America/Rankin_Inlet', name: 'America/Rankin Inlet' },
    { id: 'America/Recife', name: 'America/Recife' },
    { id: 'America/Regina', name: 'America/Regina' },
    { id: 'America/Resolute', name: 'America/Resolute' },
    { id: 'America/Rio_Branco', name: 'America/Rio Branco' },
    { id: 'America/Santarem', name: 'America/Santarem' },
    { id: 'America/Santiago', name: 'America/Santiago' },
    { id: 'America/Santo_Domingo', name: 'America/Santo Domingo' },
    { id: 'America/Sao_Paulo', name: 'America/Sao Paulo' },
    { id: 'America/Scoresbysund', name: 'America/Scoresbysund' },
    { id: 'America/Sitka', name: 'America/Sitka' },
    { id: 'America/St_Barthelemy', name: 'America/St Barthelemy' },
    { id: 'America/St_Johns', name: 'America/St Johns' },
    { id: 'America/St_Kitts', name: 'America/St Kitts' },
    { id: 'America/St_Lucia', name: 'America/St Lucia' },
    { id: 'America/St_Thomas', name: 'America/St Thomas' },
    { id: 'America/St_Vincent', name: 'America/St Vincent' },
    { id: 'America/Swift_Current', name: 'America/Swift Current' },
    { id: 'America/Tegucigalpa', name: 'America/Tegucigalpa' },
    { id: 'America/Thule', name: 'America/Thule' },
    { id: 'America/Thunder_Bay', name: 'America/Thunder Bay' },
    { id: 'America/Tijuana', name: 'America/Tijuana' },
    { id: 'America/Toronto', name: 'America/Toronto' },
    { id: 'America/Tortola', name: 'America/Tortola' },
    { id: 'America/Vancouver', name: 'America/Vancouver' },
    { id: 'America/Whitehorse', name: 'America/Whitehorse' },
    { id: 'America/Winnipeg', name: 'America/Winnipeg' },
    { id: 'America/Yakutat', name: 'America/Yakutat' },
    { id: 'America/Yellowknife', name: 'America/Yellowknife' },
    { id: 'Antarctica/Casey', name: 'Antarctica/Casey' },
    { id: 'Antarctica/Davis', name: 'Antarctica/Davis' },
    { id: 'Antarctica/DumontDUrville', name: 'Antarctica/DumontDUrville' },
    { id: 'Antarctica/Macquarie', name: 'Antarctica/Macquarie' },
    { id: 'Antarctica/Mawson', name: 'Antarctica/Mawson' },
    { id: 'Antarctica/McMurdo', name: 'Antarctica/McMurdo' },
    { id: 'Antarctica/Palmer', name: 'Antarctica/Palmer' },
    { id: 'Antarctica/Rothera', name: 'Antarctica/Rothera' },
    { id: 'Antarctica/Syowa', name: 'Antarctica/Syowa' },
    { id: 'Antarctica/Troll', name: 'Antarctica/Troll' },
    { id: 'Antarctica/Vostok', name: 'Antarctica/Vostok' },
    { id: 'Arctic/Longyearbyen', name: 'Arctic/Longyearbyen' },
    { id: 'Asia/Aden', name: 'Asia/Aden' },
    { id: 'Asia/Almaty', name: 'Asia/Almaty' },
    { id: 'Asia/Amman', name: 'Asia/Amman' },
    { id: 'Asia/Anadyr', name: 'Asia/Anadyr' },
    { id: 'Asia/Aqtau', name: 'Asia/Aqtau' },
    { id: 'Asia/Aqtobe', name: 'Asia/Aqtobe' },
    { id: 'Asia/Ashgabat', name: 'Asia/Ashgabat' },
    { id: 'Asia/Atyrau', name: 'Asia/Atyrau' },
    { id: 'Asia/Baghdad', name: 'Asia/Baghdad' },
    { id: 'Asia/Bahrain', name: 'Asia/Bahrain' },
    { id: 'Asia/Baku', name: 'Asia/Baku' },
    { id: 'Asia/Bangkok', name: 'Asia/Bangkok' },
    { id: 'Asia/Barnaul', name: 'Asia/Barnaul' },
    { id: 'Asia/Beirut', name: 'Asia/Beirut' },
    { id: 'Asia/Bishkek', name: 'Asia/Bishkek' },
    { id: 'Asia/Brunei', name: 'Asia/Brunei' },
    { id: 'Asia/Chita', name: 'Asia/Chita' },
    { id: 'Asia/Choibalsan', name: 'Asia/Choibalsan' },
    { id: 'Asia/Colombo', name: 'Asia/Colombo' },
    { id: 'Asia/Damascus', name: 'Asia/Damascus' },
    { id: 'Asia/Dhaka', name: 'Asia/Dhaka' },
    { id: 'Asia/Dili', name: 'Asia/Dili' },
    { id: 'Asia/Dubai', name: 'Asia/Dubai' },
    { id: 'Asia/Dushanbe', name: 'Asia/Dushanbe' },
    { id: 'Asia/Famagusta', name: 'Asia/Famagusta' },
    { id: 'Asia/Gaza', name: 'Asia/Gaza' },
    { id: 'Asia/Hebron', name: 'Asia/Hebron' },
    { id: 'Asia/Ho_Chi_Minh', name: 'Asia/Ho Chi Minh' },
    { id: 'Asia/Hong_Kong', name: 'Asia/Hong Kong' },
    { id: 'Asia/Hovd', name: 'Asia/Hovd' },
    { id: 'Asia/Irkutsk', name: 'Asia/Irkutsk' },
    { id: 'Asia/Jakarta', name: 'Asia/Jakarta' },
    { id: 'Asia/Jayapura', name: 'Asia/Jayapura' },
    { id: 'Asia/Jerusalem', name: 'Asia/Jerusalem' },
    { id: 'Asia/Kabul', name: 'Asia/Kabul' },
    { id: 'Asia/Kamchatka', name: 'Asia/Kamchatka' },
    { id: 'Asia/Karachi', name: 'Asia/Karachi' },
    { id: 'Asia/Kathmandu', name: 'Asia/Kathmandu' },
    { id: 'Asia/Khandyga', name: 'Asia/Khandyga' },
    { id: 'Asia/Kolkata', name: 'Asia/Kolkata' },
    { id: 'Asia/Krasnoyarsk', name: 'Asia/Krasnoyarsk' },
    { id: 'Asia/Kuala_Lumpur', name: 'Asia/Kuala Lumpur' },
    { id: 'Asia/Kuching', name: 'Asia/Kuching' },
    { id: 'Asia/Kuwait', name: 'Asia/Kuwait' },
    { id: 'Asia/Macau', name: 'Asia/Macau' },
    { id: 'Asia/Magadan', name: 'Asia/Magadan' },
    { id: 'Asia/Makassar', name: 'Asia/Makassar' },
    { id: 'Asia/Manila', name: 'Asia/Manila' },
    { id: 'Asia/Muscat', name: 'Asia/Muscat' },
    { id: 'Asia/Nicosia', name: 'Asia/Nicosia' },
    { id: 'Asia/Novokuznetsk', name: 'Asia/Novokuznetsk' },
    { id: 'Asia/Novosibirsk', name: 'Asia/Novosibirsk' },
    { id: 'Asia/Omsk', name: 'Asia/Omsk' },
    { id: 'Asia/Oral', name: 'Asia/Oral' },
    { id: 'Asia/Phnom_Penh', name: 'Asia/Phnom Penh' },
    { id: 'Asia/Pontianak', name: 'Asia/Pontianak' },
    { id: 'Asia/Pyongyang', name: 'Asia/Pyongyang' },
    { id: 'Asia/Qatar', name: 'Asia/Qatar' },
    { id: 'Asia/Qyzylorda', name: 'Asia/Qyzylorda' },
    { id: 'Asia/Riyadh', name: 'Asia/Riyadh' },
    { id: 'Asia/Sakhalin', name: 'Asia/Sakhalin' },
    { id: 'Asia/Samarkand', name: 'Asia/Samarkand' },
    { id: 'Asia/Seoul', name: 'Asia/Seoul' },
    { id: 'Asia/Shanghai', name: 'Asia/Shanghai' },
    { id: 'Asia/Singapore', name: 'Asia/Singapore' },
    { id: 'Asia/Srednekolymsk', name: 'Asia/Srednekolymsk' },
    { id: 'Asia/Taipei', name: 'Asia/Taipei' },
    { id: 'Asia/Tashkent', name: 'Asia/Tashkent' },
    { id: 'Asia/Tbilisi', name: 'Asia/Tbilisi' },
    { id: 'Asia/Tehran', name: 'Asia/Tehran' },
    { id: 'Asia/Thimphu', name: 'Asia/Thimphu' },
    { id: 'Asia/Tokyo', name: 'Asia/Tokyo' },
    { id: 'Asia/Tomsk', name: 'Asia/Tomsk' },
    { id: 'Asia/Ulaanbaatar', name: 'Asia/Ulaanbaatar' },
    { id: 'Asia/Urumqi', name: 'Asia/Urumqi' },
    { id: 'Asia/Ust-Nera', name: 'Asia/Ust-Nera' },
    { id: 'Asia/Vientiane', name: 'Asia/Vientiane' },
    { id: 'Asia/Vladivostok', name: 'Asia/Vladivostok' },
    { id: 'Asia/Yakutsk', name: 'Asia/Yakutsk' },
    { id: 'Asia/Yangon', name: 'Asia/Yangon' },
    { id: 'Asia/Yekaterinburg', name: 'Asia/Yekaterinburg' },
    { id: 'Asia/Yerevan', name: 'Asia/Yerevan' },
    { id: 'Atlantic/Azores', name: 'Atlantic/Azores' },
    { id: 'Atlantic/Bermuda', name: 'Atlantic/Bermuda' },
    { id: 'Atlantic/Canary', name: 'Atlantic/Canary' },
    { id: 'Atlantic/Cape_Verde', name: 'Atlantic/Cape Verde' },
    { id: 'Atlantic/Faroe', name: 'Atlantic/Faroe' },
    { id: 'Atlantic/Madeira', name: 'Atlantic/Madeira' },
    { id: 'Atlantic/Reykjavik', name: 'Atlantic/Reykjavik' },
    { id: 'Atlantic/South_Georgia', name: 'Atlantic/South Georgia' },
    { id: 'Atlantic/St_Helena', name: 'Atlantic/St Helena' },
    { id: 'Atlantic/Stanley', name: 'Atlantic/Stanley' },
    { id: 'Australia/Adelaide', name: 'Australia/Adelaide' },
    { id: 'Australia/Brisbane', name: 'Australia/Brisbane' },
    { id: 'Australia/Broken_Hill', name: 'Australia/Broken Hill' },
    { id: 'Australia/Currie', name: 'Australia/Currie' },
    { id: 'Australia/Darwin', name: 'Australia/Darwin' },
    { id: 'Australia/Eucla', name: 'Australia/Eucla' },
    { id: 'Australia/Hobart', name: 'Australia/Hobart' },
    { id: 'Australia/Lindeman', name: 'Australia/Lindeman' },
    { id: 'Australia/Lord_Howe', name: 'Australia/Lord Howe' },
    { id: 'Australia/Melbourne', name: 'Australia/Melbourne' },
    { id: 'Australia/Perth', name: 'Australia/Perth' },
    { id: 'Australia/Sydney', name: 'Australia/Sydney' },
    { id: 'Europe/Amsterdam', name: 'Europe/Amsterdam' },
    { id: 'Europe/Andorra', name: 'Europe/Andorra' },
    { id: 'Europe/Astrakhan', name: 'Europe/Astrakhan' },
    { id: 'Europe/Athens', name: 'Europe/Athens' },
    { id: 'Europe/Belgrade', name: 'Europe/Belgrade' },
    { id: 'Europe/Berlin', name: 'Europe/Berlin' },
    { id: 'Europe/Bratislava', name: 'Europe/Bratislava' },
    { id: 'Europe/Brussels', name: 'Europe/Brussels' },
    { id: 'Europe/Bucharest', name: 'Europe/Bucharest' },
    { id: 'Europe/Budapest', name: 'Europe/Budapest' },
    { id: 'Europe/Busingen', name: 'Europe/Busingen' },
    { id: 'Europe/Chisinau', name: 'Europe/Chisinau' },
    { id: 'Europe/Copenhagen', name: 'Europe/Copenhagen' },
    { id: 'Europe/Dublin', name: 'Europe/Dublin' },
    { id: 'Europe/Gibraltar', name: 'Europe/Gibraltar' },
    { id: 'Europe/Guernsey', name: 'Europe/Guernsey' },
    { id: 'Europe/Helsinki', name: 'Europe/Helsinki' },
    { id: 'Europe/Isle_of_Man', name: 'Europe/Isle of Man' },
    { id: 'Europe/Istanbul', name: 'Europe/Istanbul' },
    { id: 'Europe/Jersey', name: 'Europe/Jersey' },
    { id: 'Europe/Kaliningrad', name: 'Europe/Kaliningrad' },
    { id: 'Europe/Kiev', name: 'Europe/Kiev' },
    { id: 'Europe/Kirov', name: 'Europe/Kirov' },
    { id: 'Europe/Lisbon', name: 'Europe/Lisbon' },
    { id: 'Europe/Ljubljana', name: 'Europe/Ljubljana' },
    { id: 'Europe/London', name: 'Europe/London' },
    { id: 'Europe/Luxembourg', name: 'Europe/Luxembourg' },
    { id: 'Europe/Madrid', name: 'Europe/Madrid' },
    { id: 'Europe/Malta', name: 'Europe/Malta' },
    { id: 'Europe/Mariehamn', name: 'Europe/Mariehamn' },
    { id: 'Europe/Minsk', name: 'Europe/Minsk' },
    { id: 'Europe/Monaco', name: 'Europe/Monaco' },
    { id: 'Europe/Moscow', name: 'Europe/Moscow' },
    { id: 'Europe/Oslo', name: 'Europe/Oslo' },
    { id: 'Europe/Paris', name: 'Europe/Paris' },
    { id: 'Europe/Podgorica', name: 'Europe/Podgorica' },
    { id: 'Europe/Prague', name: 'Europe/Prague' },
    { id: 'Europe/Riga', name: 'Europe/Riga' },
    { id: 'Europe/Rome', name: 'Europe/Rome' },
    { id: 'Europe/Samara', name: 'Europe/Samara' },
    { id: 'Europe/San_Marino', name: 'Europe/San Marino' },
    { id: 'Europe/Sarajevo', name: 'Europe/Sarajevo' },
    { id: 'Europe/Saratov', name: 'Europe/Saratov' },
    { id: 'Europe/Simferopol', name: 'Europe/Simferopol' },
    { id: 'Europe/Skopje', name: 'Europe/Skopje' },
    { id: 'Europe/Sofia', name: 'Europe/Sofia' },
    { id: 'Europe/Stockholm', name: 'Europe/Stockholm' },
    { id: 'Europe/Tallinn', name: 'Europe/Tallinn' },
    { id: 'Europe/Tirane', name: 'Europe/Tirane' },
    { id: 'Europe/Ulyanovsk', name: 'Europe/Ulyanovsk' },
    { id: 'Europe/Uzhgorod', name: 'Europe/Uzhgorod' },
    { id: 'Europe/Vaduz', name: 'Europe/Vaduz' },
    { id: 'Europe/Vatican', name: 'Europe/Vatican' },
    { id: 'Europe/Vienna', name: 'Europe/Vienna' },
    { id: 'Europe/Vilnius', name: 'Europe/Vilnius' },
    { id: 'Europe/Volgograd', name: 'Europe/Volgograd' },
    { id: 'Europe/Warsaw', name: 'Europe/Warsaw' },
    { id: 'Europe/Zagreb', name: 'Europe/Zagreb' },
    { id: 'Europe/Zaporozhye', name: 'Europe/Zaporozhye' },
    { id: 'Europe/Zurich', name: 'Europe/Zurich' },
    { id: 'Indian/Antananarivo', name: 'Indian/Antananarivo' },
    { id: 'Indian/Chagos', name: 'Indian/Chagos' },
    { id: 'Indian/Christmas', name: 'Indian/Christmas' },
    { id: 'Indian/Cocos', name: 'Indian/Cocos' },
    { id: 'Indian/Comoro', name: 'Indian/Comoro' },
    { id: 'Indian/Kerguelen', name: 'Indian/Kerguelen' },
    { id: 'Indian/Mahe', name: 'Indian/Mahe' },
    { id: 'Indian/Maldives', name: 'Indian/Maldives' },
    { id: 'Indian/Mauritius', name: 'Indian/Mauritius' },
    { id: 'Indian/Mayotte', name: 'Indian/Mayotte' },
    { id: 'Indian/Reunion', name: 'Indian/Reunion' },
    { id: 'Pacific/Apia', name: 'Pacific/Apia' },
    { id: 'Pacific/Auckland', name: 'Pacific/Auckland' },
    { id: 'Pacific/Bougainville', name: 'Pacific/Bougainville' },
    { id: 'Pacific/Chatham', name: 'Pacific/Chatham' },
    { id: 'Pacific/Chuuk', name: 'Pacific/Chuuk' },
    { id: 'Pacific/Easter', name: 'Pacific/Easter' },
    { id: 'Pacific/Efate', name: 'Pacific/Efate' },
    { id: 'Pacific/Enderbury', name: 'Pacific/Enderbury' },
    { id: 'Pacific/Fakaofo', name: 'Pacific/Fakaofo' },
    { id: 'Pacific/Fiji', name: 'Pacific/Fiji' },
    { id: 'Pacific/Funafuti', name: 'Pacific/Funafuti' },
    { id: 'Pacific/Galapagos', name: 'Pacific/Galapagos' },
    { id: 'Pacific/Gambier', name: 'Pacific/Gambier' },
    { id: 'Pacific/Guadalcanal', name: 'Pacific/Guadalcanal' },
    { id: 'Pacific/Guam', name: 'Pacific/Guam' },
    { id: 'Pacific/Honolulu', name: 'Pacific/Honolulu' },
    { id: 'Pacific/Johnston', name: 'Pacific/Johnston' },
    { id: 'Pacific/Kiritimati', name: 'Pacific/Kiritimati' },
    { id: 'Pacific/Kosrae', name: 'Pacific/Kosrae' },
    { id: 'Pacific/Kwajalein', name: 'Pacific/Kwajalein' },
    { id: 'Pacific/Majuro', name: 'Pacific/Majuro' },
    { id: 'Pacific/Marquesas', name: 'Pacific/Marquesas' },
    { id: 'Pacific/Midway', name: 'Pacific/Midway' },
    { id: 'Pacific/Nauru', name: 'Pacific/Nauru' },
    { id: 'Pacific/Niue', name: 'Pacific/Niue' },
    { id: 'Pacific/Norfolk', name: 'Pacific/Norfolk' },
    { id: 'Pacific/Noumea', name: 'Pacific/Noumea' },
    { id: 'Pacific/Pago_Pago', name: 'Pacific/Pago Pago' },
    { id: 'Pacific/Palau', name: 'Pacific/Palau' },
    { id: 'Pacific/Pitcairn', name: 'Pacific/Pitcairn' },
    { id: 'Pacific/Pohnpei', name: 'Pacific/Pohnpei' },
    { id: 'Pacific/Port_Moresby', name: 'Pacific/Port Moresby' },
    { id: 'Pacific/Rarotonga', name: 'Pacific/Rarotonga' },
    { id: 'Pacific/Saipan', name: 'Pacific/Saipan' },
    { id: 'Pacific/Tahiti', name: 'Pacific/Tahiti' },
    { id: 'Pacific/Tarawa', name: 'Pacific/Tarawa' },
    { id: 'Pacific/Tongatapu', name: 'Pacific/Tongatapu' },
    { id: 'Pacific/Wake', name: 'Pacific/Wake' },
    { id: 'Pacific/Wallis', name: 'Pacific/Wallis' },
  ],
}
const mutations = {
  SET_ALL_COUNTRY_LIST: (state, data) => {
    state.allCountryList = data
  },
  SET_ALL_CONTINENT_LIST: (state, data) => {
    state.allContinentList = data.filter(item => item.parent_id === 0)
  },
}
const actions = {
  getAllCountryList({ commit }) {
    if (state.allCountryList.length) return state.allCountryList
    else {
      return new Promise((resolve, reject) => {
        ToolAPI.getLocation()
          .then(resp => {
            commit('SET_ALL_COUNTRY_LIST',
              resp.sort((a, b) => a.name > b.name ? 1 : -1))
            commit('SET_ALL_CONTINENT_LIST',
              resp.sort((a, b) => a.name > b.name ? 1 : -1))
            resolve(resp)
          })
          .catch(err => {
            return reject(err)
          })
      })
    }
  },
}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
