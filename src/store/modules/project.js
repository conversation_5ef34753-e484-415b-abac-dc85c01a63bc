/**
 * project
 * 2020/1/14
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */

import project from '@/api/project'

const state = {
  id: undefined,
  data: {},
  contract: undefined,
  task_cols_width_list: undefined,
  unread_subscription_events: {
    mask_read_leads: false,
    mask_read_client_task: false,
    total: 0,
    client_task_ids: [],
    leads_task_ids: [],
  },
}

const mutations = {
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_DATA: (state, data) => {
    state.data = data
  },
  SET_TASK_COLS_WIDTH: (state, data) => {
    state.task_cols_width_list = data
  },
  SET_UNREAD_EVENTS: (state, data) => {
    state.unread_subscription_events = data
  },
}

const actions = {
  setID({ commit, state }, id) {
    commit('SET_ID', id)
  },
  getID({ commit, state }) {
    return state.id
  },
  getInfo({ commit, state }, id) {
    commit('SET_ID', id)

    return new Promise((resolve, reject) => {
      return project.getProject(state.id)
        .then((resp) => {
          commit('SET_DATA', resp)
          resolve(resp)
        })
        .catch(err => {
          return reject(err)
        })
    })
  },
  updateInfo({ commit, state }, params) {
    commit('SET_DATA', params)
  },

  setTaskColsWidth({ commit, state }, params) {
    commit('SET_TASK_COLS_WIDTH', params || [])
  },

  setUnreadEvents({ commit, state }, params) {
    commit('SET_UNREAD_EVENTS', params || [])
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
