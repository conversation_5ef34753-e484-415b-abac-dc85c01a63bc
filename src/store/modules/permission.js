import { asyncRoutes, constantRoutes, resetRouter } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param menus
 * @param route
 */
function hasPermission(menus, route) {
  if (!route.meta) return false

  /* 特定路由不做过滤 */
  if (route.meta.alwaysAccess) return true

  const name = route.name || route.meta.tag
  if (name) {
    const currMenu = getMenu(name, menus)
    if (currMenu != null) {
      return true
    } else {
      return false
    }
  } else {
    return false
  }
}

// 根据路由名称获取菜单
function getMenu(route_name, menus) {
  for (let i = 0; i < menus.length; i++) {
    const menu = menus[i]

    if (route_name === menu.value) {
      return menu
    }
  }
  return null
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, permissions) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(permissions, tmp)) {
      res.push(tmp)
    } else {
      if (tmp.children) {
        const childrenHasPermission = filterAsyncRoutes(tmp.children,
          permissions)

        const children_length = childrenHasPermission.length
        const hidden_children = childrenHasPermission.filter(item => item.hidden)
        // 如果有权限的子菜单都不包含在左侧栏的路由 那么父菜单不展示
        if (children_length) {
          tmp.children = childrenHasPermission

          if (hidden_children.length === children_length) {
            tmp.hidden = true
          }
          res.push(tmp)
        }
      }
    }
  })
  return res
}

const state = {
  routes: [],
  addRoutes: [],
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  CLEANUP_ROUTES: (state) => {
    state.addRoutes = []
    state.routes = []
    resetRouter()
  },
}

const actions = {
  generateRoutes({ commit }, info) {
    return new Promise(resolve => {
      const permissions = info.permissions
      const isAdmin = info.roles.find(item => item.role.name === 'Admin')

      let accessedRoutes
      if (isAdmin) {
        accessedRoutes = asyncRoutes || []
      } else {
        accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions)
      }
      commit('SET_ROUTES', accessedRoutes)

      resolve(accessedRoutes)
    })
  },
  cleanup({ commit }) {
    commit('CLEANUP_ROUTES')
  },
}

const getters = {
  isRoutesLoaded: state => state.routes.length !== 0,
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
