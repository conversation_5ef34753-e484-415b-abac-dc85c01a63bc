const state = {
  current: {},
  list: [],
  selectionMap: {},
  selectionList: [],
}

const mutations = {
  SET_CURRENT: (state, data) => {
    state.current = data
  },
  SET_LIST: (state, data) => {
    state.list = data
  },
  SET_SELECTION_LIST: (state, data) => {
    state.selectionMap[data.angle_id] = data.val
    var selectionList = []
    for (const key in state.selectionMap) {
      if (Object.hasOwnProperty.call(state.selectionMap, key)) {
        const val = state.selectionMap[key]
        selectionList = [...selectionList, ...val]
      }
    }
    state.selectionList = selectionList
  },
  REMOVE_SELECTION_ANGLE: (state, angle_id) => {
    state.selectionList = state.selectionList.filter((task) => +task.angle_id !== +angle_id)
    delete state.selectionMap[angle_id]
  },
  CLEAR_SELECTION_LIST: (state, data) => {
    state.selectionMap = {}
    state.selectionList = []
  },
}

const actions = {
  SET_CURRENT({ commit, state }, data) {
    commit('SET_CURRENT', data)
  },
  SET_LIST({ commit, state }, data) {
    commit('SET_LIST', data)
  },
  SET_SELECTION_LIST({ commit, state }, data) {
    commit('SET_SELECTION_LIST', data)
  },
  REMOVE_SELECTION_ANGLE({ commit, state }, data) {
    commit('REMOVE_SELECTION_ANGLE', data)
  },
  CLEAR_SELECTION_LIST({ commit, state }, data) {
    commit('CLEAR_SELECTION_LIST', data)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
