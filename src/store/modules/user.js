import User<PERSON><PERSON> from '@/api/user'
import AuthAP<PERSON> from '@/api/auth'
import {
  getToken,
  removeToken,
  removeUserInfo,
  setToken,
  setUserInfo,
} from '@/utils/auth'
import { AuthService } from '@/utils/request'
import { resetRouter } from '@/router'
import store from '@/store'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    uid: 0,
    roles: [],
    position_id: 0,
    info: null,
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_UID: (state, uid) => {
    state.uid = uid
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_POSITION: (state, position_id) => {
    state.position_id = position_id
  },
  SET_INFO: (state, info) => {
    state.info = info
  },
  SET_ZONE_ID_STRING: (state, zone_id_string) => {
    state.zone_id_string = zone_id_string
  },
}

const actions = {

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      UserAPI.getUserProfile(state.token).then(data => {
        if (!data) {
          reject('Verification failed, please Login again.')
        }

        const { name, picture, id, roles, position_id } = data
        commit('SET_NAME', name)
        commit('SET_AVATAR', picture)
        commit('SET_UID', id)
        commit('SET_ROLES', roles)
        commit('SET_POSITION', position_id)
        commit('SET_INFO', data)

        setAppTimeZone(data).then()
        setUserInfo(data)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user login
  login({ commit, state }, loginForm) {
    return AuthAPI.login(loginForm).then(res => {
      const { idToken, name, picture, id, position_id } = res
      commit('SET_TOKEN', idToken)
      commit('SET_NAME', name)
      commit('SET_AVATAR', picture)
      commit('SET_UID', id)
      commit('SET_POSITION', position_id)
      commit('SET_INFO', res)
      setToken(idToken)
      setUserInfo(res)
    })
  },

  // user login
  loginBy2FA({ commit, state }, loginForm) {
    return AuthAPI.loginBy2FA(loginForm).then(res => {
      if (res.login_status !== 'SUCCESS') return res
      const { idToken, name, picture, id, position_id } = res.user
      commit('SET_TOKEN', idToken)
      commit('SET_NAME', name)
      commit('SET_AVATAR', picture)
      commit('SET_UID', id)
      commit('SET_POSITION', position_id)
      commit('SET_INFO', res)
      setToken(idToken)
      setUserInfo(res.user)
    })
  },

  // user logout
  logout({ commit, state }) {
    return AuthAPI.logout().then(() => {
      // logout(state.token).then(() => {
      removeToken() // must remove  token  first
      removeUserInfo()
      resetRouter()
      commit('RESET_STATE')
      // }).catch(error => {
      //   reject(error)
      // })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  },

  // third party login
  authenticate({ commit, state }, provider) {
    return AuthService.authenticate(provider).then(res => {
      const { idToken, name, picture, id, position_id } = res
      commit('SET_TOKEN', idToken)
      commit('SET_NAME', name)
      commit('SET_AVATAR', picture)
      commit('SET_UID', id)
      commit('SET_POSITION', position_id)
      commit('SET_INFO', res)
      setToken(idToken)
      setUserInfo(res)
    })
  },

  setTimeZone({ commit, state }, zone_id_string) {
    commit('SET_ZONE_ID_STRING', zone_id_string)
    return UserAPI.updateUserProfile(state.uid, { zone_id_string })
  },
}

function setAppTimeZone(data) {
  const zone_id_string = data.zone_id_string
  if (zone_id_string) {
    return store.dispatch('app/setTimeZone', zone_id_string)
  }

  return Promise.resolve()
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}

