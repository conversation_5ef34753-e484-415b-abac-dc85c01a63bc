import Cookies from 'js-cookie'
import Tool from '@/utils/tool'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : false,
    withoutAnimation: false,
  },
  device: 'desktop',
  timeZone: Tool.timeZone,
  McKinseyId: 115,
  JefferiesInternationalLimited: 74,
  GenerationIMId: 108,
  GenerationGrowthEquityId: 172,
  CapvisionGKMTeamId: 5,
  VikingGlobalInvestorsLPId: 79,
  TengYueId: 119,
  activated_events: { 'ProjectList': [] },
  keepAliveComponents: [],
  isSGDB: import.meta.env.VITE_APP_DB_NAME && import.meta.env.VITE_APP_DB_NAME === 'SG',
  shoppingCartCount: 0,
  shoppingCartItemIds: [],
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_TIME_ZONE: (state, timeZone) => {
    state.timeZone = timeZone
  },
  SET_ACTIVATED_EVENTS: (state, data) => {
    state.activated_events[data.name] = data.events
  },
  PUSH_ACTIVATED_EVENTS: (state, data) => {
    data.forEach(item => {
      if (!state.activated_events[item.name].includes(item.event)) {
        state.activated_events[item.name].push(item.event)
      }
    })
  },
  PUSH_KEEP_ALIVE(state, component) {
    !state.keepAliveComponents.includes(component) &&
      state.keepAliveComponents.push(component)
  },
  SPLICE_KEEP_ALIVE(state, component) {
    const index = state.keepAliveComponents.indexOf(component)
    index !== -1 &&
      state.keepAliveComponents.splice(index, 1)
  },
  SET_SHOPPING_CART_COUNT: (state, count) => {
    state.shoppingCartCount = count
  },
  SET_SHOPPING_CART_ITEM_IDS: (state, ids) => {
    state.shoppingCartItemIds = ids
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setTimeZone({ commit }, timeZone) {
    commit('SET_TIME_ZONE', timeZone)
  },
  setActivatedEvents({ commit, state }, data) {
    commit('SET_ACTIVATED_EVENTS', data)
  },
  pushActivatedEvents({ commit, state }, data) {
    commit('PUSH_ACTIVATED_EVENTS', data)
  },
  setShoppingCartCount({ commit, state }, data) {
    commit('SET_SHOPPING_CART_COUNT', data)
  },
  setShoppingCartItemIds({ commit, state }, data) {
    commit('SET_SHOPPING_CART_ITEM_IDS', data)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
