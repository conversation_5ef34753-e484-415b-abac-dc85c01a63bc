<template>
  <router-link
    v-if="data"
    :class="{'text-truncate': !wrap}"
    class="link"
    :title="data['name']"
    :to="to">
    {{data['name']}}
  </router-link>
</template>

<script>
import RouterLinkMixins from './mixins'

/**
 * Project 链接支持的 Tab 标签页如下
 *
 * tasks
 * leads
 * information
 * screening-question
 * outreach
 */
export default {
  name: 'RouterLinkProject',
  mixins: [RouterLinkMixins],
  props: {
    tab: String,
    wrap: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    to() {
      return {
        name: this.targetRouteName(),
        params: {
          project_id: this.data.id,
        },
      }
    },
  },
  methods: {
    targetRouteName() {
      let route_name = 'ProjectDetail'
      const type = this.data.type
      const tab = this.tab

      if (type === 'CONSULTATION') {
        route_name = 'ProjectDetail'
      } else {
        route_name = 'CommonProjectDetail'
      }

      if (tab) {
        if (type === 'CONSULTATION') {
          switch (tab) {
            case 'client tasks':
              route_name = 'ProjectClientTasks'
              break
            case 'leads':
              route_name = 'ProjectLeads'
              break
            case 'information':
              route_name = 'ProjectInformation'
              break
            case 'screening-question':
              route_name = 'ProjectScreeningQuestion'
              break
            case 'outreach':
              route_name = 'ProjectOutreach'
              break
          }
        }
      }

      return route_name
    },
  },
}
</script>
