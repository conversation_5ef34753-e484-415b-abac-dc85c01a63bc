<template>
  <router-link
    v-if="data"
    class="link"
    :class="{'text-truncate': !wrap}"
    :title="data['name']"
    :to="to">
    {{data['name']}}
  </router-link>
</template>

<script>
import RouterLinkMixins from './mixins'

export default {
  name: 'RouterLinkClient',
  mixins: [RouterLinkMixins],
  props: {
    wrap: Boolean,
  },
  computed: {
    to() {
      return {
        name: 'ClientDetail',
        params: {
          client_id: this.data.id,
        },
      }
    },
  },
}
</script>
