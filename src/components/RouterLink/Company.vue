<template>
  <router-link
    v-if="data"
    class="text-truncate link"
    :title="data['name']"
    :to="to">
    {{data['name']}}
  </router-link>
</template>

<script>
import RouterLinkMixins from '@/components/RouterLink/mixins'

export default {
  name: 'RouterLinkCompany',
  mixins: [RouterLinkMixins],
  computed: {
    to() {
      return {
        name: 'CompanyDetail',
        params: {
          company_id: this.data.id,
        },
      }
    },
  },
}
</script>

<style scoped>

</style>
