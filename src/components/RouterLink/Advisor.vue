<template>
  <router-link
    v-if="data"
    class="text-truncate link"
    :title="data['full_name'] || data['name']"
    :to="to">
    {{data.name_prefix || '' }} {{data['full_name'] || data['name']}}
  </router-link>
</template>

<script>
import RouterLinkMixins from './mixins'

export default {
  name: 'RouterLinkAdvisor',
  mixins: [RouterLinkMixins],
  computed: {
    to() {
      return {
        name: 'ConsultantDetail',
        params: {
          advisor_id: this.data.id,
        },
      }
    },
  },
}
</script>
