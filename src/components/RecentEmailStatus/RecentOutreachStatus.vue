<template>
  <el-popover
    placement="top-start"
    width="300"
    :loading="loading"
    :open-delay="300"
    trigger="hover"
    @show="getRecentOutreachRecord">
    <div v-if="apiType === 'ListWise'" class="el-popover__title flex justify-content-between">
      {{apiType}}
      <el-link
        v-if="item"
        type="primary"
        :underline="false"
        :disabled="loading"
        @click="manuallyOverrideListWiseStatus"
      >
        Email is Deliverable
      </el-link>
    </div>
    <div>
      <slot v-if="contentIsArray">
        <div v-for="(i,index) in content" :key="index" class="flex justify-content-between">
          <div class="capitalize">{{ i.result_status }}
            <span v-if="apiType !== 'ListWise'" class="info">({{i.provider | getLabel(email_method_options)}})</span>
          </div>
          <div class="text-right">{{ i.update_at | momentFormat('MM/DD/YYYY hh:mm A') }}</div>
        </div>
      </slot>
      <slot v-else>{{ content }}</slot>
    </div>
    <el-icon slot="reference" name="s-opportunity" :class="emailStatus" />
  </el-popover>
</template>

<script>
import ProjectAPI from '@/api/project'
import ConsultantAPI from '@/api/consultant'
import _ from 'lodash'

export default {
  name: 'RecentOutreachStatus',
  props: {
    emailStatus: String,
    emailRecords: Array,
    apiType: String,
    item: Object,
  },
  data() {
    return {
      content: null,
      loading: false,
      email_method_options: [
        { value: 'SEND_GRID', label: 'SendGrid' },
        { value: 'POSTMARK', label: 'Postmark' },
        { value: 'RESEND', label: 'Resend' },
        { value: 'MAILGUN', label: 'Mailgun' },
        { value: 'MAILJET', label: 'Mailjet' },
      ],
    }
  },
  computed: {
    contentIsArray() {
      return Array.isArray(this.content)
    },
  },
  watch: {
    'emailRecords': {
      handler() {
        this.getRecentOutreachRecord()
      },
    },
  },
  methods: {
    getRecentOutreachRecord() {
      if (this.emailStatus === 'info') {
        this.content = 'Initial'
        return
      }
      if (this.apiType === 'ListWise') {
        const list = _.orderBy(this.emailRecords, 'create_at')
        this.content = list.slice(0, 5)
      } else {
        this.loading = true
        const params = {
          project_id: this.item.project_id,
          params: {
            ids: this.item.id,
            extra: 'advisor.email_address_score.email_record_trackings',
          },
        }
        return ProjectAPI.getTasksDetail(params).then(data => {
          const list = _.orderBy(data.list[0].advisor.email_address_score.email_record_trackings, 'create_at', 'desc')
          this.content = list.slice(0, 5)
          this.content.forEach(item => {
            item.result_status = item.status.toLowerCase()
          })
        }).finally(() => {
          this.loading = false
        })
      }
    },

    manuallyOverrideListWiseStatus() {
      this.loading = true
      return ConsultantAPI.manuallyOverrideListWiseStatus({
        email_info_id: this.item.id,
      }).then(data => {
        this.$emit('manual-override', data)
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
