<template>
  <el-select v-model="timeZone"
             size="mini"
             filterable
             placeholder="-- Search TimeZone --"
             style="width: 160px;"
             @change="setTimeZone">
    <el-option
      v-for="item in options.timezone_list"
      :key="item.id"
      :label="item.name"
      :value="item.id" />
  </el-select>
</template>

<script>
export default {
  name: 'TimeZoneSelect',
  data() {
    return {
      options: {
        timezone_list: this.$store.getters.timezone_list,
      },
    }
  },
  computed: {
    timeZone: {
      get() {
        return this.$store.state.app.timeZone
      },
      set(val) {
        this.$store.dispatch('app/setTimeZone', val)
      },
    },
  },
  created() {
  },
  methods: {
    setTimeZone() {
      this.$store.dispatch('user/setTimeZone', this.timeZone)
    },
  },
}
</script>
