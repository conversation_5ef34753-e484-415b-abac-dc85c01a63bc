<template>
  <el-select
    size="mini"
    filterable
    :filter-method="timezoneFilter"
    placeholder="-- Search TimeZone --"
    v-bind="$attrs"
    v-on="$listeners">
    <el-option
      label="-- Search TimeZone --"
      disabled
      value="" />
    <el-option
      v-for="item in options.timeZoneList"
      :key="item"
      :label="item"
      :value="item" />
  </el-select>
</template>

<script>
import moment from 'moment-timezone'

export default {
  name: 'Custom',
  props: {
    defaultOptions: {
      type: Array,
      default() {
        return ['Asia/Shanghai', 'America/New_York']
      },
    },
  },
  data() {
    return {
      options: {
        timeZoneList: this.defaultOptions,
      },
    }
  },
  watch: {
    '$attrs.value': {
      handler(val) {
        if (val && !this.options.timeZoneList.includes(val)) {
          this.options.timeZoneList.unshift(val)
        }
      },
    },
  },
  methods: {
    timezoneFilter(search) {
      if (search) {
        this.options.timeZoneList = moment.tz.names().filter(option => {
          return option.replace('_', '').toLowerCase().indexOf(search.replace(' ', '').toLowerCase()) > -1
        })
      } else {
        this.options.timeZoneList = this.defaultOptions
      }
    },
  },
}
</script>

<style scoped>

</style>
