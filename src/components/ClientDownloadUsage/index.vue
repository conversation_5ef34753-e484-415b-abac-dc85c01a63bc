<template>
  <div>
    <el-checkbox-group v-model="data_list"
                       size="mini"
                       class="filter-item"
                       @change="formatResult()">
      <div v-for="(item,index) in option_list"
           :key="index"
           :draggable="true"
           class="inline-block"
           :class="{'is-date':item.name==='REVENUE_TIME'}"
           @dragstart="dragStart(index)"
           @dragenter="dragenter(index)"
           @dragend="dragEnd(index)">
        <el-checkbox-button
          class="checkbox-item"
          :label="item.name "
          :disabled="checkDisabled(item)"
        >
          <el-tooltip effect="dark"
                      placement="top"
                      :disabled="tipsDisabled(item)"
                      :content="disabledWarning">
            <span>{{ item.displayName }}</span>
          </el-tooltip>
        </el-checkbox-button>
      </div>
      <el-checkbox class="question-icon" disabled>
        <el-tooltip effect="dark"
                    placement="top"
                    content="Select the columns to be downloaded for usage and drag to sort them.">
          <el-link :underline="false" icon="el-icon-question" />
        </el-tooltip>
      </el-checkbox>
    </el-checkbox-group>

  </div>
</template>

<script>

import { mapGetters } from 'vuex'

export default {
  name: 'ClientDownLoadUsage',
  props: {
    'optionList': Array,
    'dataList': Array,
  },
  data() {
    return {
      disabledWarning: 'This field is very sensitive information and will expose Capvision Pro\'s business volume. If the client Usage needs this field, please confirm with the compliance team first, and then contact the IT department to add this field.',
      data_list: this.dataList,
      oldNum: null,
      newNum: null,
      clone_list: this.initOptions(),
      option_list: this.initOptions(),
    }
  },

  computed: {
    ...mapGetters([
      'roles',
    ]),
    HasEditPermission() {
      return this.roles.some(role => {
        return role.role?.name === 'RD' || [1, 4].includes(role.role_id)
      })
    },
  },

  methods: {
    initOptions() {
      const default_list = []
      const other_list = []
      if (this.dataList.length) {
        this.dataList.forEach(item => {
          const validItems = this.optionList.filter(option => option.name === item)
          if (validItems.length > 0) {
            default_list.push(validItems[0])
          }
        })
      }

      this.optionList.forEach(item => {
        if (!this.dataList.includes(item.name)) {
          other_list.push(item)
        }
      })

      return default_list.concat(other_list)
    },

    /* REVENUE_TIME->用于下载时保持第一列固定，维持正常数据显示格式 (该条逻辑已移除)
    'TASK_ID', 'ADVISOR_ID'->权限控制 admin compliance RD
      * */
    checkDisabled(item) {
      return ['TASK_ID', 'ADVISOR_ID'].includes(item.name) && !this.HasEditPermission
    },

    tipsDisabled(item) {
      return this.HasEditPermission || !['TASK_ID', 'ADVISOR_ID'].includes(item.name)
    },

    dragStart(value) {
      this.oldNum = value
    },

    dragEnd(value) {
      if (this.option_list[this.newNum].name === 'REVENUE_TIME') return
      if (this.oldNum !== this.newNum) {
        const item = this.option_list[this.oldNum]
        this.clone_list.splice(this.oldNum, 1)
        this.clone_list.splice(this.newNum, 0, item)

        this.$set(this, 'option_list', this.clone_list)
        this.formatResult()
      }
    },
    dragenter(value) {
      this.newNum = value
    },

    formatResult() {
      const result = []
      this.option_list.map(item => {
        if (this.data_list.includes(item.name)) {
          result.push(item.name)
        }
      })
      this.$emit('update:dataList', result)
    },
  },

}
</script>

<style scoped lang="scss">
.checkbox-item {
  word-spacing: 0;
  white-space: pre-line;
  margin-top: 4px;
}

.is-date ::v-deep .el-checkbox-button.is-disabled .el-checkbox-button__inner {
  background-color: #409EFF;
  border-color: #409EFF;
}

.question-icon {
  ::v-deep .el-checkbox__inner {
    display: none;
  }

  ::v-deep .el-checkbox__label {
    font-size: 16px;
  }

  vertical-align: middle;
}

::v-deep.el-checkbox-button:last-child .el-checkbox-button__inner {
  border-radius: unset;
}

::v-deep .el-checkbox-button.is-checked:first-child .el-checkbox-button__inner {
  border-left-color: #DCDFE6;
}

</style>
