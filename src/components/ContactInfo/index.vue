<template>
  <div>
    <div>
      <div class="mb-8">
        <b>Contact Information</b>
        <slot v-if="!isCNAdvisor">
          <el-button v-if="!isView" type="text" @click="handleIsView"> View</el-button>
          <el-button v-if="isView && !isEdit && !dataUser.is_outsource_statistic_advisor" class="el-icon-edit" @click="handleIsEdit"> Edit</el-button>
          <div v-if="isView && isEdit" class="inline-block">
            <el-button class="el-icon-check" type="primary" :loading="submitting" @click="onSave()"> Save</el-button>
            <el-button @click="cancelEdit"> Cancel</el-button>
          </div>
        </slot>
      </div>
      <el-form
        v-if="!isEdit"
        class="show-form"
        label-position="left">
        <el-form-item>
          <div class="line-height-normal">
            <div v-for="(item,index) in display.contact_infos" :key="index">
              <slot v-if="item.type==='LINKEDIN_URL'">
                <div class="w-120px inline-block">
                  Linkedin:
                </div>
                <span
                  class="link"
                  @click="openToLink(item.type, item.value)">{{ item.value }}</span>

                <el-tooltip
                  v-if="dataUser.is_linkedin_premium"
                  effect="dark"
                  content="The expert has a premium account."
                  placement="top">
                  <svg-icon icon-class="linkedin_premium" />
                </el-tooltip>
              </slot>
              <slot v-else>
                <div class="capitalize w-120px inline-block">
                  {{ item.type.toLowerCase() }}:
                </div>
                <span v-if="!isView">{{ item.value }}</span>
                <span
                  v-if="isView"
                  :class="{'link': ['LINKEDIN_URL','EMAIL'].includes(item.type)}"
                  @click="openToLink(item.type, item.value)">
                  <span v-if="item.type === 'PHONE'">
                    <a :href="'tel:'+item.value">{{ item.value }}</a>
                    <el-button type="text" icon="el-icon-copy-document" @click="copyMobile(item.value)" />
                  </span>

                  <span v-else>{{ item.value }}</span>
                </span>
              </slot>

              <el-link
                v-if="item.is_main"
                :underline="false"
                type="success"
                class="el-icon-star-on vertical-inherit" />
              <el-tooltip
effect="dark"
                          placement="top"
                          content="Experts have confirmed this on external platforms.">
                <el-link
v-if="item.maintain_by_owner && userType === 'ADVISOR'"
                         :underline="false"
                         type="success"
                         icon="el-icon-s-check" />
              </el-tooltip>
              <recent-outreach-status
                v-if="userType === 'ADVISOR' && item.type === 'EMAIL' && !isSGDB"
                api-type="ListWise"
                :item="item"
                :email-records="item.email_list_wise_records"
                :email-status="item.list_wise_email_status"
                @manual-override="handleManualOverrideListWise" />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <el-form
        v-if="isEdit"
        ref="form"
        :model="form"
        :rules="rules"
        label-position="left"
        class="update-form"
        label-width="110px"
      >
        <div class="contact-warning-label warning">
          Must have one of the following
        </div>
        <el-form-item
          v-for="(con, index) in form.contact_infos"
          :key="`contact-${index}`"
          :show-message="false"
          :label="!index ? 'Contact' : ''"
          prop="contact_infos"
          class="show-form">
          <el-form-item
            :prop="`contact_infos[${index}].value`"
            :rules="contactRules[con.type]"
          >
            <el-input v-model="con.value" class="w-90">
              <template slot="prepend">
                <div class="text-center" style="min-width: 70px;">
                  {{ con.type | getLabel(contact_info_options) }}
                </div>
              </template>
            </el-input>
            <el-link
              v-if="con.origin"
              type="primary"
              icon="el-icon-plus"
              class="icon-font"
              :underline="false"
              @click="addContact(con.type, index)" />
            <el-link
              v-if="isDisplayRemove(con.type)"
              type="danger"
              icon="el-icon-delete"
              class="icon-font"
              :underline="false"
              @click="removeContact(con, index)" />
            <el-tooltip
              v-if="isDisplaySetMain(con)"
              class="item"
              effect="dark"
              content="Main Contact"
              placement="top">
              <el-link
                v-if="con.is_main"
                type="success"
                class="icon-font"
                :underline="false"
                icon="el-icon-star-on"
              />
              <el-link
                v-else
                class="icon-font"
                :underline="false"
                icon="el-icon-star-off"
                @click="setMainContact(con, index)" />
            </el-tooltip>
          </el-form-item>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import FormMixin from '@/components/FormPage/mixin'
import _ from 'lodash'
import Mixin from '@/views/consultant/mixins/form'
import ConsultantAPI from '@/api/consultant'
import ToolAPI from '@/api/tool'
import ClientAPI from '@/api/client'
import { copyUtil } from '@/utils/tool'
import RecentOutreachStatus from '@/components/RecentEmailStatus/RecentOutreachStatus'
import { mapState } from 'vuex'

export default {
  name: 'ContactInfo',
  components: { RecentOutreachStatus },
  mixins: [FormMixin, Mixin],
  props: {
    dataUser: {
      type: Object,
      default() {
        return {}
      },
    },
    userType: String,
  },
  data() {
    const contactValidator = (rule, value, callback) => {
      if (!value.filter(item => item.value).length) {
        callback(new Error('Must have one of the following'))
      } else {
        callback()
      }
    }
    const phoneValidator = (rule, value, callback) => {
      const reg = /^\+?\d+(?:-?\d+){5,}$/
      if (!reg.test(value) && value !== '') {
        callback(new Error('Please enter a valid phone number'))
      } else {
        return callback()
      }
    }
    const emailValidator = (rule, value, callback) => {
      // /^[\w.+-]+@[\w-]+(\.[\w-]+)+$/
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      setTimeout(() => {
        const more_than_one = /,|;|，|；/g
        if (value.match(more_than_one) && value.match(more_than_one).length) {
          callback(new Error('Only one entry can be made into an input box.'))
        } else if (!mailReg.test(value) && value !== '') {
          callback(new Error('Please enter the correct email address'))
        } else {
          callback()
        }
      }, 100)
    }
    return {
      isView: false,
      isEdit: false,
      form: {
        contact_infos: this.initForm(),
        id: this.dataUser.id,
      },
      display: {
        contact_infos: this.initForm(),
      },
      rules: {
        contact_infos: [{ required: true, validator: contactValidator, trigger: 'change' }],
      },
      contactRules: {
        EMAIL: [{ validator: emailValidator, trigger: 'blur' }],
        PHONE: [{ validator: phoneValidator, trigger: 'blur' }],
        LINKEDIN_URL: [{ required: false, message: 'Please input the value', trigger: 'blur' }],
      },
    }
  },
  computed: {
    isCNAdvisor() {
      return this.dataUser.is_cn
    },
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },

  mounted() {
    if (this.dataUser.contact_infos && !this.dataUser.contact_infos.length) this.isView = true
  },

  methods: {
    initForm(data) {
      const result = _.cloneDeep(data || this.dataUser.contact_infos)
      if (this.userType === 'ADVISOR' && result.length) {
        const list_wise_invalid_status = [
          'invalid',
          'no-reply',
          'spam-trap',
          'bad-mx',
          'bounced',
          'suspicious',
          'unknown',
          'disposable',
          'rejected',
          'deferred']
        result.forEach(item => {
          if (item.email_validation_records?.length) {
            item.email_list_wise_records = item.email_validation_records.filter(
              i => i.provider === 'LIST_WISE')
            const latest_status = _.orderBy(item.email_list_wise_records, 'create_at', 'desc')[0]
            item.list_wise_email_status = list_wise_invalid_status.includes(
              latest_status.result_status) ? 'danger' : 'success'
          } else {
            item.list_wise_email_status = 'info'
            item.email_list_wise_records = []
          }
        })
      }
      return result
    },

    handleIsView() {
      let params
      if (this.userType === 'ADVISOR') {
        params = {
          'owner_type': 'ADVISOR',
          'owner_id': this.dataUser.id,
        }
      } else {
        params = {
          ids: this.dataUser.contact_infos.map(item => (item.id))
            .join(),
        }
      }
      return ToolAPI.getContactInfo(params)
        .then(data => {
          this.form.contact_infos.forEach(item => {
            const contact = data['list'].find(list => list.id === item.id)
            if (contact) item.value = contact.value
          })
        })
        .finally(() => {
          this.display.contact_infos = _.cloneDeep(this.form.contact_infos)
          this.isView = true
        })
    },

    handleIsEdit() {
      this.isEdit = true
      this.formatContactInfo()
    },

    formatContactInfo() {
      if (!this.form.contact_infos.length) {
        this.form.contact_infos = [
          {
            type: 'EMAIL',
            value: '',
            is_main: true,
            origin: true,
          }, {
            type: 'PHONE',
            value: '',
            is_main: true,
            origin: true,
          }, {
            type: 'LINKEDIN_URL',
            value: '',
            is_main: true,
            origin: true,
          }]
      } else {
        const list = this.form.contact_infos
        const type_list = ['EMAIL', 'PHONE', 'LINKEDIN_URL']

        type_list.forEach(type => {
          if (list.find(item => item.type === type)) {
            this.form.contact_infos.find(item => item.type === type).origin = true
          } else {
            this.form.contact_infos.push({
              type: type,
              value: '',
              is_main: true,
              origin: true,
            })
          }
        })
      }
    },

    cancelEdit() {
      this.form.contact_infos = _.cloneDeep(this.display.contact_infos)
      this.isEdit = false
    },

    handleSave(val) {
      val.contact_infos = val.contact_infos.filter(item => item.value)
      const request = this.userType === 'ADVISOR' ? ConsultantAPI.updateConsultant(val) : ClientAPI.updateClientContact(
        this.dataUser.client_id, this.dataUser.id, val)

      return request
        .then((data) => {
          this.isEdit = false
          if (this.userType === 'CONTACT') {
            this.display.contact_infos = data.contact_infos
          }
          this.$emit('update')
        })
    },

    openToLink(key, value) {
      if (key === 'LINKEDIN_URL') {
        window.open(value, '_blank')
      }
      if (key === 'EMAIL') {
        const aTag = document.createElement('a')
        aTag.href = `mailto:${value}`
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    },
    addContact(val, index) {
      this.form.contact_infos.splice(index + 1, 0, {
        type: val,
        value: '',
        is_main: false,
      })
    },

    removeContact(val, index) {
      this.form.contact_infos.splice(index, 1)
      if (val.is_main) {
        this.form.contact_infos.find(item => item.type === val.type).is_main = true
      }

      if (val.origin) {
        this.form.contact_infos.find(item => item.type === val.type).origin = true
      }
    },

    setMainContact(item, index) {
      if (item.is_main && this.form.contact_infos.length > 1) {
        this.$set(item, 'is_main', false)
      } else {
        this.$set(item, 'is_main', true)
        this.form.contact_infos.forEach((info, ind) => {
          if (info.type === item.type && ind !== index) {
            this.$set(info, 'is_main', false)
          }
          return info
        })
      }
    },

    changeType(select, index) {
      const data = _.cloneDeep(this.form.contact_infos)
      data.splice(index, 1)
      const list = data.filter(item => item.type === select.type)
      const has_main = list.filter(item => item.is_main).length
      if (!list.length || !has_main) {
        this.form.contact_infos[index].is_main = true
      }
      if (has_main) {
        this.form.contact_infos[index].is_main = false
      }
    },
    isDisplayRemove(type) {
      const type_list = this.form.contact_infos.filter(item => item.type === type)
      return type_list.length > 1
    },

    /**
     * 是否显示 "设置主要联系方式" 按钮
     * 同一种联系方式出现多次时，提供 "set main" 选项
     * @param data
     * @returns {boolean}
     */
    isDisplaySetMain(data) {
      return this.form.contact_infos.filter(item => {
        return item.type === data.type
      }).length > 1
    },

    copyMobile(val) {
      copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },

    handleManualOverrideListWise(data) {
      const contact_infos = [...this.dataUser.contact_infos]
      const email_contact_info = contact_infos.find(item => item.type === 'EMAIL')
      if (email_contact_info) {
        const manually_email_validation_record = email_contact_info.email_validation_records.find(item => item.result_status === 'manually')
        if (!manually_email_validation_record) {
          email_contact_info.email_validation_records.push(data)
        } else {
          Object.assign(manually_email_validation_record, data)
        }
        this.form.contact_infos = this.initForm(contact_infos)
        this.display.contact_infos = this.initForm(contact_infos)
      }
    },
  },

}
</script>

<style scoped>
.show-form ::v-deep .el-form-item {
  margin-bottom: 3px;
}

.w-90 {
  width: 90%;
}

.contact-warning-label {
  margin-left: 110px;
  display: block;
  vertical-align: middle;
  font-size: 12px;
  /*color: #606266;*/
  line-height: 14px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
</style>
