<template>
  <div class="inline-block">
    <el-button class="inline-block info" type="text" @click="editCustomFields()">
      <span v-if="type_value">{{ type_value }}</span>
      <i v-else class="el-icon-edit" />
    </el-button>
    <el-dialog
      title="Client Custom Fields"
      append-to-body
      :visible.sync="dialogVisible"
      width="30%">
      <el-form ref="form" :model="form" label-width="160px">
        <custom-fields :custom-fields.sync="client_custom_fields"
                       :form-data.sync="form.client_custom_fields" />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="cancelEdit">Cancel</el-button>
        <el-button type="primary" @click="saveEdit">Save</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'
import ProjectAPI from '@/api/project'
import CustomFields from '../index'

export default {
  name: 'TaskExpertCode',
  components: { CustomFields },
  props: {
    taskData: Object,
  },
  data() {
    return {
      dialogVisible: false,
      client_custom_fields: [],
      default_form: {},
      form: {},
      type: ['task_expert_code'],
    }
  },
  computed: {

    type_value() {
      return !this.taskData.client_custom_fields ? null : this.taskData.client_custom_fields[`${this.type}`]
    },
  },

  methods: {
    getTaskFields() {
      const params = {
        type: 'TASK',
        client_id: this.taskData.client_id,
      }
      return ClientAPI.getCustomFields(params).then(data => {
        data['list'].forEach(item => {
          if (this.type.includes(item.field_name)) {
            this.default_form[item.field_name] = this.taskData.client_custom_fields
              ? this.taskData.client_custom_fields[item.field_name] || null : null
            this.client_custom_fields.push(item)
          }
        })
      })
    },
    editCustomFields() {
      this.getTaskFields()
        .then(data => {
          this.form.client_custom_fields = this.default_form
        })
        .finally(() => {
          this.dialogVisible = true
        })
    },
    saveEdit() {
      const params = Object.assign({
        project_id: this.taskData.project_id,
        id: this.taskData.id,
      }, this.form)
      return ProjectAPI.updateTask(params).then(data => {
        this.$emit('update')
      }).finally(() => {
        this.cancelEdit()
      })
    },
    cancelEdit() {
      this.default_form = {}
      this.form.client_custom_fields = {}
      this.client_custom_fields = []
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped>

</style>
