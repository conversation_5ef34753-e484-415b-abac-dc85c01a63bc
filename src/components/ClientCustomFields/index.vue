<template>
  <!--client custom-->
  <div v-if="customFields.length>0">
    <div v-for="(item,index) in customFields" :key="index">
      <el-form-item :label="item.field_display_name"
                    :prop="`client_custom_fields.${item.field_name}`"
                    :rules="[{ required: item.is_required, trigger: 'change' },]">

        <div v-if="item.input_type === 'TEXT'">
          <el-input
            v-if="item.field_value_type === 'STRING'"
            v-model="form[item.field_name]"
            size="mini"
            @input="emitData()" />
          <div v-if="item.field_value_type === 'LIST_STRING'">
            <div v-for="(input_item, _index) in item.input_list" :key="`input-${_index}`" class="input-item">
              <el-input
                v-model="input_item.text"
                class="inline-block"
                size="mini"
                @input="updateInputData(item,index)" />

              <el-link
                v-show="item.input_list.length > 1"
                type="danger"
                icon="el-icon-delete"
                class="icon-font ml-3"
                :underline="false"
                @click="deleteTextInput(_index, item,index)" />
            </div>
            <el-link
              type="primary"
              icon="el-icon-plus"
              class="icon-font"
              :underline="false"
              @click="addTextInput(item,index)" />
          </div>
        </div>
        <el-input
          v-if="item.input_type === 'NUMBER'"
          v-model="form[item.field_name]"
          type="number"
          size="mini"
          @input="emitData()" />
        <el-input
          v-if="item.input_type === 'TEXTAREA'"
          v-model="form[item.field_name]"
          type="textarea"
          size="mini"
          @input="emitData()" />
        <el-date-picker
          v-if="item.input_type === 'DATE'"
          v-model="form[item.field_name]"
          type="date"
          @chenge="emitData()" />
      </el-form-item>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'ClientCustomFields',
  props: {
    customFields: Array,
    formData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      form: this.formatData(this.formData),
    }
  },

  methods: {
    addTextInput(item, index) {
      item.input_list.push({ text: null })
      this.updateInputData(item, index)
    },
    deleteTextInput(_index, item, index) {
      item.input_list.splice(_index, 1)
      this.updateInputData(item, index)
    },
    updateInputData(item, index) {
      this.$set(this.customFields, index, item)
      const text_list = []
      item.input_list.forEach(item => {
        if (item.text && item.text.trim() && item.text.trim() !== '') {
          text_list.push(item.text)
        }
      })
      this.form[item.field_name] = text_list
      this.emitData()
    },
    emitData() {
      this.$emit('update:formData', this.form)
    },

    formatData(val) {
      this.customFields.forEach(item => {
        if (item.input_type === 'TEXT' && item.field_value_type === 'LIST_STRING') {
          if (Array.isArray(val[item.field_name]) && val[item.field_name].length > 0) {
            item.input_list = val[item.field_name].map(i => {
              return { text: i }
            })
          } else {
            item.input_list = [{ text: null }]
          }
        }
        return item
      })
      return _.cloneDeep(val)
    },
  },
}
</script>

<style scoped>
.input-item {
  margin-bottom: 5px;
  display: flex;
}
</style>
