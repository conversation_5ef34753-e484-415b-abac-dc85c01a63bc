<template>
  <el-checkbox-group v-model="data"
                     :class="{ 'checkbox-group-vertical': vertical }"
                     @change="actionDataChange">
    <el-checkbox v-for="item in bitwise_options"
                 :key="item.id"
                 :label="item.id">{{ item.name }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script>
/*
 * 按位存储 checkbox-group 勾选项数据
 *
 * 使用范例:
 *  <checkbox-bitwise v-model="form.product"
 *                    :options="options.product_list"
 *                    label="label"
 *                    value="value"
 *                    vertical />
 *
 */

export default {
  name: 'CheckboxBitwise',
  model: {
    prop: 'model',
    event: 'change',
  },
  props: {
    model: {
      type: Number,
      default: undefined,
    },
    /* 可选项 */
    options: {
      type: Array,
      required: true,
      default() {
        return []
      },
    },
    value: {
      type: String,
      default: 'id',
    },
    label: {
      type: String,
      default: 'name',
    },
    /* 选项垂直排列 */
    vertical: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      data: [],
      bitwise_options: [],
    }
  },
  watch: {
    'model': {
      handler: 'analyzeModel',
      immediate: true,
    },
    'options': {
      handler: 'analyzeOptions',
      immediate: true,
    },
  },
  methods: {

    /**
     * 解析 model 为数组
     * @param model
     */
    analyzeModel(model) {
      this.data = this.bitwise_options
        .filter(item => (item.id | model) === model)
        .map(item => item.id)
    },

    /**
     * 根据 options 转换成勾选项
     * 支持 [...String] / [...Object]
     * @param options
     */
    analyzeOptions(options) {
      let bitwise_options = []
      if (Array.isArray(options) && options.length > 0) {
        const test_option = options[0]

        if (typeof test_option === 'string') {
          bitwise_options = options.map((str, index) => {
            return {
              id: 1 << index,
              name: str,
            }
          })
        } else {
          bitwise_options = options.map(item => {
            return {
              id: item[this.value],
              name: item[this.label],
            }
          })
        }
      } else {
        bitwise_options = []
      }

      this.bitwise_options = bitwise_options
    },

    /**
     * 勾选变更时同步更新 model 值
     * @param val
     */
    actionDataChange(val) {
      this.$emit('change', this.totalization(val))
    },

    /**
     * 加总
     * @param data
     * @returns {number}
     */
    totalization(data) {
      let total = 0
      data.forEach(item => { total += item })
      return total
    },

  },
}
</script>
