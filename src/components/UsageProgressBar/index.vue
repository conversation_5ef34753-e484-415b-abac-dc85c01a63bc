<template>
  <div class="progress-box">
    <el-progress :percentage="percentage" :color="customColorMethod" class="percent-progress" />
    <div class="percent-format">
      <span>{{percentage +'%'}}</span>
      <span class="total-usage">{{total}}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Index',
  props: {
    total: {
      type: [Number, String],
      default: 0,
    },
    used: {
      type: [Number, String],
      default: 0,
    },
  },

  data() {
    return {
      percentage: 0,
    }
  },

  watch: {
    'used': {
      handler() {
        setTimeout(() => {
          this.percentage = this.used ? (Math.round(+this.used / +this.total * 10000) / 100.00) : 0
        }, 200)
      },
      immediate: true,
    },
  },

  methods: {
    customColorMethod(percentage) {
      if (percentage < 60) {
        return '#409eff'
      } else if (percentage < 80) {
        return '#e6a23c'
      } else {
        return '#f56c6c'
      }
    },
  },
}
</script>

<style scoped>
  .progress-box {
    display: flex;
    align-items: center;
  }

  .percent-progress {
    flex: 1;
  }

  .percent-format {
    margin-left: 0.3rem;
    vertical-align: middle;
  }

  .total-usage {
    margin-left: 0.1rem;
    color: #66b1ff;
  }

  ::v-deep .el-progress-bar {
    padding-right: 0;
  }

  ::v-deep .el-progress__text {
    display: none;
  }
</style>
