<template>
  <div>
    <table border="0" style="margin-bottom: 5px;">
      <tbody>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0pt;margin-left:0;">
              <b class="advisor-display-name" style="font-size: 14pt;">{{ data.display_id }} {{ advisor.name_prefix || '' }} {{ advisor.full_name }}</b>
              <i v-if="advisor['location']"> – {{ advisor['location']['name'] }}</i>
              <span v-if="shortProfile && !noAvailability" style="color:#e30f1f;word-break: break-word"> –  Availability ({{
                timezone
              }}):
                <slot
                  v-if="hasAdvisorSchedules"
                  style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:3px;margin-left:0;">
                  {{ advisorSchedulesFormat | formatTime(timezone) }}
                </slot>
                <slot
                  v-if="!hasAdvisorSchedules"
                  style="font-size: 11pt; font-family: Calibri;margin-top:1pt;margin-right:0;margin-bottom:3px;margin-left:0;">
                  Pending
                </slot></span>
            </p>
          </td>
        </tr>
        <tr v-if="!isCapvisionGKMTeam && showNominalCharge">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>{{ computedHourRate }}</b>
            </p>
          </td>
        </tr>
        <tr v-if="!isCapvisionGKMTeam && (showAdditionalMultiplier || additional_charge_note)">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri; margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>
                Additional Charges:
                <span v-if="showAdditionalMultiplier">{{ senior_rate_note }}</span>
                <span v-if="additional_charge_note">{{ additional_charge_note }}</span>
              </b>
            </p>
          </td>
        </tr>
        <tr v-if="hasTimeMiniMumCharge">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri; margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>
                {{ data.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
              </b>
            </p>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Employment History</b>
            </p>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <ul
              style="font-size: 11pt; font-family: Calibri; margin-top: 0px;margin-bottom:10px;margin-block-end:0;margin-inline-start: 0px;margin-inline-end: 0px;">
              <li
                v-for="job in advisor.jobs"
                :key="job.id"
                style="margin-top: 0; margin-bottom: 0;">
                <span v-if="data.advisor_profile && job.id === data.advisor_profile.job_id">
                  <span v-if="shortProfile" style="background-color: #ffff00">
                    <b> {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                      handleCurrent(job)
                    }}
                      {{ getStockCode(job.company) }}
                    </b>
                  </span>
                  <b v-else> {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                    {{ getStockCode(job.company) }}
                  </b>
                </span>
                <span v-else>
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                  {{ getStockCode(job.company) }}
                </span>
              </li>
            </ul>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
            &nbsp;</p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Total Consultation Usage:</b>
            </p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 3 Months: {{advisor.calls_last_three_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 6 Months: {{advisor.calls_last_six_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 12 Months: {{advisor.calls_last_twelve_months}}
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
            &nbsp;</p>
          </td>
        </tr>
        <tr v-if="!shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Background</b>
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && data.advisor_profile && data.advisor_profile.background">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
            <span v-if="data.advisor_profile" v-html="$sanitizeHtml(data.advisor_profile.background.replace(/\n/gi, '<br/>'))" />
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && !(data.advisor_profile && data.advisor_profile.background)">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <slot v-for="job in formatted_jobs">
                <slot v-if="job.length === 1">
                  <slot v-if="job[0].is_current">
                    Since {{ displayTime(job[0].start_date) }}, this advisor is
                  </slot>
                  <slot v-else>
                    From {{ displayTime(job[0].start_date) }} to {{ handleCurrent(job[0]) }}, this advisor was
                  </slot>
                  the {{ job[0].position }} at {{ job[0].company.name }}.
                </slot>
                <slot v-else>
                  <slot v-if="job[0].is_current">
                    Since {{ displayTime(job[job.length - 1].start_date) }},
                  </slot>
                  <slot v-else>
                    From {{ displayTime(job[job.length - 1].start_date) }} to {{ handleCurrent(job[0]) }},
                  </slot>
                  the advisor held numerous positions at {{ job[0].company.name }} --
                  <slot v-for="(item,index) in job">{{ item.position }} ({{ displayTime(item.start_date) }} -
                    {{ handleCurrent(item) }})
                    <slot v-if="index !== job.length - 1">;</slot>
                  </slot>
                  .
                </slot>
              </slot>
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && !noQuestions && data.latest_submitted_sq && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
            &nbsp;</p>
          </td>
        </tr>
        <tr v-if="!shortProfile && !noQuestions && data.latest_submitted_sq && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:2pt;margin-left:0;">
              <b>Screening Questions/Responses</b>
            </p>
          </td>
        </tr>
        <tr v-if="data.latest_submitted_sq && !noQuestions && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0cm">
            <sq-copy
              id="sq-copy-client"
              show-answer
              :short-profile="shortProfile"
              :data="data.latest_submitted_sq" />
          </td>
        </tr>
        <tr v-if="!shortProfile && hasAdvisorSchedules && !noAvailability">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:2pt;margin-left:0;font-weight: bold">
              <b>Availability<span v-if="hasAdvisorSchedules">({{ timezone }})</span></b>
            </p>
          </td>
        </tr>
        <tr v-if="!noAvailability && !shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm">
            <slot
              v-if="hasAdvisorSchedules"
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:10px;margin-left:0;">
              <p
                v-for="(value, key) in advisorSchedulesFormat"
                :key="key"
                style="margin-top: 0; margin-bottom: 0;font-size: 11pt;font-family: Calibri;">
                {{ key }}
                <slot v-for="(item, index) in value">
                  <slot v-if="index > 0">,</slot>
                  {{ item.start_time | momentFormatZone('h:mma',timezone) }} -
                  {{ item.end_time | momentFormatZone('h:mma',timezone) }}
                </slot>
              </p>
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import SqCopy from '@/components/Question/SqQuestionCopy'
import FormatProfileMixin from '@/components/FormatProfile/mixin'
import Filters from '@/utils/filters'

export default {
  name: 'ProfileP',
  components: { SqCopy },
  filters: {
    formatTime(value, timezone) {
      return Object.entries(value)
        .map(([date, times]) => {
          return `${date} ${times.map(({ start_time, end_time }) => ([
            `${Filters.momentFormatZone(start_time, 'h:mma', timezone)} - ${Filters.momentFormatZone(end_time, 'h:mma', timezone)}`])).join(', ')}`
        }).join(', ')
    },
  },
  mixins: [FormatProfileMixin],
  mounted() {
    this.handleFormatProfile()
    this.checkClientRule()
    this.formatNote()
  },
}
</script>

<style scoped>

</style>
