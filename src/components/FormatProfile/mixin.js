import moment from 'moment-timezone'
import _ from 'lodash'
import store from '@/store'
import { mapGetters, mapState } from 'vuex'

/**
 * mixin.js
 * 2021/4/2
 * <AUTHOR>
 * copyright 2014-2021, All rights reserved.
 */
export default {
  props: {
    data: Object,
    additionalCharges: {
      type: Array,
      default() {
        return []
      },
    },
    noQuestions: {
      type: Boolean,
      default: false,
    },
    noAvailability: {
      type: Boolean,
      default: false,
    },
    senior: Number,
    customTimeZone: {
      type: String,
      default: null,
    },
    clientProfileFormat: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      advisor: this.data.advisor,
      client: this.data.client,
      formatted_jobs: [],
      fullProfile: false,
      shortProfile: false,
      summaryOnlyProfile: false,
      summaryChargeProfile: false,
      summaryChargeAverageCPIProfile: false,
      showLabelExpert: false,
      additional_charge_note: undefined,
      charge_note: undefined,
      senior_rate_note: undefined,
      hour_rate: 0,
      advisor_senior: 1,
      brief_job: undefined,
    }
  },
  computed: {
    ...mapState({
      CapvisionGKMTeamId: state => state.app.CapvisionGKMTeamId,
    }),
    ...mapGetters([
      'advisor_options',
    ]),

    isCapvisionGKMTeam() {
      return this.client.id === this.CapvisionGKMTeamId
    },

    advisorInChina() {
      return this.advisor.location &&
        this.advisor.location.id_path.includes('108')
    },

    advisorCurrency() {
      return this.advisor.rate_currency
    },

    showNominalCharge() {
      return this.client.preference?.display_nominal_charge_rate_on_portal
    },

    showExpertUsage() {
      return this.client.preference?.display_usage_in_to_client
    },

    showAdditionalMultiplier() {
      return this.client.preference?.display_additional_multiplier_on_portal && this.senior_rate_note
    },

    clientContract() {
      return this.client && this.client.contracts.filter(
        item => item.effective_status === 'EFFECTIVE' &&
          ['PREPAY', 'PAYGO', 'TRIAL'].includes(item.payway))
    },

    computedHourRate() {
      if (!this.clientContract.length) return
      let result
      if (this.showNominalCharge) {
        // let nominal_charge_rate = 0
        // nominal_charge_rate = this.advisorInChina
        //   ? this.clientContract[0].unit_price_china
        //   : this.clientContract[0].unit_price_us
        // result = `Nominal Charge Rate: $${(this.hour_rate +
        //   this.advisor_senior) *
        // nominal_charge_rate} per hour.`
        // default_client_rate_usd：由后端根据合同和对应币种的专家费率计算出每小时单价
        // client_rate_usd：用户修改后的每小时单价
        const client_rate = this.data.client_rate_usd ||
          this.data.specified_client_rate_currency_after_multiple ||
          this.data.default_client_rate_usd ||
          this.data.default_client_rate_usd_considering_unsigned_tc
        result = `Nominal Charge Rate: $${client_rate} per hour.`
      }
      return result
    },

    additionalChargesArray() {
      const result = []
      if (this.clientContract.length) {
        if (this.clientContract[0]['transcription_term']) {
          const price = this.clientContract[0]['transcription_term']['extra']
          result.push({
            value: 'has_transcription',
            text: `extra ${price}hr/hr accounted for transcription fee`,
            price: +price,
          })
        }

        if (this.clientContract[0]['translation_term']) {
          const price = this.clientContract[0]['translation_term']['extra']
          result.push({
            value: 'has_translation',
            text: `extra ${price}hr/hr accounted for interpretation fee`,
            price: +price,
          })
        }

        if (this.clientContract[0]['in_person_term']) {
          result.push({
            value: 'has_in_person',
            text: `extra ${this.clientContract[0]['in_person_term']['value']}hr accounted for in-person fee`,
            price: 0,
          })
        }
      }
      return result
    },

    validAdvisorSchedules() {
      return this.data.advisor_schedules.filter(
        item => moment(item.end_time).valueOf() >= moment().valueOf())
    },

    hasAdvisorSchedules() {
      return this.validAdvisorSchedules.length > 0
    },

    timezone() {
      // store.state.app.timeZone
      // const appTimeZone = Filters.momentFormat(this.data.advisor_schedules[0].start_time, 'z')
      return this.customTimeZone
        ? this.customTimeZone
        : store.state.app.timeZone
    },

    advisorSchedulesFormat() {
      if (this.hasAdvisorSchedules && this.timezone) {
        const sort_list = _.orderBy(this.validAdvisorSchedules, 'start_time',
          'asc')
        const list = sort_list.map(item => {
          item.date_format = moment.tz(item.start_time, this.timezone)
            .format('dddd M/D:')
          return item
        })
        const format_list = _.groupBy(list, 'date_format')
        return _.mapValues(format_list, (item) => {
          item = _.orderBy(item, 'start_time', 'asc')
          return item
        })
      } else {
        return []
      }
    },

    hasTimeMiniMumCharge() {
      return this.data && this.data.advisor?.time_minimum !== 'NO_MINIMUM'
    },
  },

  watch: {
    'additionalCharges': {
      handler() {
        if (this.additionalCharges.length) {
          this.formatAdditionalCharges()
        } else {
          this.additional_charge_note = undefined
          this.hour_rate = 0
        }
      },
    },
    'clientProfileFormat': {
      handler() {
        this.checkClientRule()
      },
    },
  },
  mounted() {
    this.handleFormatProfile()
    this.checkClientRule()
    this.formatNote()
  },
  methods: {
    checkClientRule() {
      const has_rules = this.client && this.client.compliance_preference &&
        this.client.compliance_preference.rule.compliance_rules
      // this.shortProfile = this.client?.preference &&
      //   this.client.preference.short_form_profile
      const to_client_default_profiles_format = this.clientProfileFormat || this.client?.preference?.to_client_default_profiles_format
      this.fullProfile = to_client_default_profiles_format === 'FULL_PROFILES'
      this.shortProfile = to_client_default_profiles_format === 'SHORT_FORM_PROFILES'
      this.summaryOnlyProfile = to_client_default_profiles_format === 'SUMMARY_ONLY'
      this.summaryChargeProfile = to_client_default_profiles_format === 'SUMMARY_CHARGE'
      this.summaryChargeAverageCPIProfile = to_client_default_profiles_format === 'SUMMARY_CHARGE_AND_AVERAGE_CPI'
      this.showLabelExpert = !!has_rules &&
        !!has_rules.recommend_email.is_label_experts
    },

    formatNote() {
      if (!this.clientContract || !this.clientContract.length) return

      this.formatSeniorRate(this.clientContract[0])
    },

    formatAdditionalCharges() {
      if (!this.clientContract.length) return
      const select_charges = this.additionalChargesArray.filter(
        item => this.additionalCharges.includes(item.value))
      const note = select_charges.map(item => (item.text))

      this.hour_rate = 0
      select_charges.forEach(item => {
        this.hour_rate += item.price
      })
      this.additional_charge_note = note.join('; ')
    },

    formatSeniorRate(contract) {
      // 由后端根据客户合同及专家费率计算得出
      // const result = this.data.given_senior_rate_multiplier || this.data.senior_rate_multiplier ||
      //   this.data.senior_rate_multiplier_considering_unsigned_tc
      const result = this.data.standard_rate_multiplier_display_to_client
      if (result <= 1) {
        return ''
      } else {
        this.senior_rate_note = `This advisor is ${result}x Standard Rate.`
      }
      // const china_senior_term = contract['china_senior_term']
      // const other_senior_term = contract['non_china_senior_term']
      // let multiple = ''
      //
      // if (contract.currency === 'USD') {
      //   switch (this.advisorCurrency) {
      //     case 'SGD':
      //       // 1.37 SGD = 1 USD.
      //       multiple = 1 / 1.37
      //       break
      //     case 'EUR':
      //       // 0.85 EUR = 1 USD.
      //       multiple = 1 / 0.85
      //       break
      //     case 'GBP':
      //       // 0.73 GBP = 1 USD
      //       multiple = 1 / 0.73
      //       break
      //     case 'JPY':
      //       // 1 JPY = 0.0095 USD
      //       multiple = 0.0095
      //       break
      //     default:
      //       multiple = 1
      //   }
      // } else if (contract.currency === 'SGD') {
      //   // SGD 新加坡币种
      //   switch (this.advisorCurrency) {
      //     case 'USD':
      //       // 0.73 USD = 1 SGD
      //       multiple = 1 / 0.73
      //       break
      //     case 'MYR':
      //       // 1 MYR = 0.3 SGD
      //       multiple = 0.3
      //       break
      //     case 'EUR':
      //       // 0.7 EUR = 1 SGD
      //       multiple = 1 / 0.7
      //       break
      //     case 'GBP':
      //       // 0.61 GBP = 1 SGD
      //       multiple = 1 / 0.61
      //       break
      //     case 'JPY':
      //       // 1 JPY = 0.0098 SGD
      //       multiple = 0.0098
      //       break
      //     default:
      //       multiple = 1
      //   }
      // } else {
      //   // MYR 马来西亚林吉特币种
      //   switch (this.advisorCurrency) {
      //     case 'SGD':
      //       // 0.3 SGD = 1 MYR
      //       multiple = 1 / 0.3
      //       break
      //     case 'USD':
      //       // 0.22 USD = 1 MYR
      //       multiple = 1 / 0.22
      //       break
      //     case 'EUR':
      //       // 0.21 EUR = 1 MYR
      //       multiple = 1 / 0.21
      //       break
      //     case 'GBP':
      //       // 0.19 GBP = 1 MYR
      //       multiple = 1 / 0.19
      //       break
      //     case 'JPY':
      //       // 1 JPY = 0.032 MYR
      //       multiple = 0.032
      //       break
      //     default:
      //       multiple = 1
      //   }
      // }
      // if (this.advisorCurrency === 'RMB') {
      //   this.senior_rate_note = this.computedSenior(+this.advisor.rate,
      //     china_senior_term)
      // } else {
      //   this.senior_rate_note = this.computedSenior(
      //     this.advisor.rate * multiple, other_senior_term)
      // }
    },
    computedSenior(rate, senior_term) {
      // 兼容从 CN DB 导入的客户
      if (!senior_term) return
      let max_set_senior = 0
      const intervals = senior_term['intervals']
      const min_set_senior = []

      intervals.forEach((item) => {
        if (rate <= item.first) {
          min_set_senior.push(item.second)
        }
      })
      let result
      if (min_set_senior.length) {
        result = _.min(min_set_senior)
      } else {
        const max_interval = _.orderBy(intervals, 'second', 'desc')[0]
        max_set_senior = max_interval.first
        result = max_interval.second

        result += (Math.ceil((rate - max_set_senior) / senior_term.step)) *
          senior_term.inc
        result = Math.round(result * 1e1) / 1e1
      }

      if (result === 1) {
        return undefined
      } else {
        this.$emit('update:senior', result)
        this.advisor_senior = result
        return `This advisor is ${result}x Standard Rate.`
      }
    },

    handleFormatProfile() {
      if (this.advisor.jobs.length) {
        const companies = []
        const formatted_jobs = []
        this.advisor.jobs = this.sortJobs(this.advisor.jobs)
        this.advisor.jobs.forEach(item => {
          const index = companies.indexOf(item.company.name)
          if (index === -1) {
            companies.push(item.company.name)
            formatted_jobs.push([item])
          } else {
            formatted_jobs[index].push(item)
          }
        })
        this.formatted_jobs = formatted_jobs
      }
    },

    sortJobs(list) {
      let current_list = []
      let other_list = []
      list.forEach(item => {
        if (item.is_current) {
          current_list.push(item)
        } else {
          other_list.push(item)
        }
      })
      other_list = _.orderBy(
        other_list,
        'start_date', 'desc',
      )
      current_list = _.orderBy(
        current_list,
        'start_date', 'desc',
      )
      return current_list.concat(other_list)
    },

    initRelevantJobData() {
      const jobs = this.advisor.jobs.length ? this.advisor.jobs[0] : undefined
      this.brief_job = this.data.advisor_profile.company_id
        ? this.data.advisor_profile
        : jobs
    },

    getStockCode(company) {
      if (!this.showLabelExpert) return
      if (company.exchange) {
        return ' | ' + company.exchange + ' - ' + company['stock_code']
      }
      if (company.type === 'PRIVATE') {
        return ' | ' + 'Private'
      }
    },

    handleCurrent(item) {
      return item.is_current ? 'Current' : this.displayTime(item.end_date)
    },

    /**
     * 日期格式化为 "Aug 2019"
     * @param input
     * @returns {string|*}
     */
    displayTime(input) {
      if (!input) {
        return
      }

      if (['current'].includes(String(input)
        .toLowerCase())) {
        return input
      }

      const list = input.split('-')
      if (!list[1] || ['undefined', 'null', ' '].includes(list[1])) {
        return moment(list[0])
          .format('YYYY')
      } else {
        return moment(input)
          .format('MMM YYYY')
      }
    },

  },
}
