<template>
  <div>
    <!--   email 2021-0901
     To Client for Advisor Profile Submission: Profile submission would be specifically for McKinsey client-->
    <!--    a. All font would be <PERSON><PERSON>ri, font size 11-->
    <!--    b. Availability: If no availability is inputted, text would be Availability: Pending-->
    <table border="0" style="margin-bottom: 3px;">
      <tbody>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: <PERSON>ibri;margin-top:0px;margin-right:0;margin-bottom:0pt;margin-left:0;">
              <b class="advisor-display-name" style="font-size: 11pt;">{{ data.display_id }} {{ advisor.name_prefix || '' }} {{ advisor.full_name }}</b>
              <i v-if="advisor['location']"> – {{ advisor['location']['name'] }}</i>
              <span style="color:#e30f1f"> –  Availability
                <slot
                  v-if="hasAdvisorSchedules"
                  style="font-size: 11pt; font-family: <PERSON><PERSON>ri;margin-top:0px;margin-right:0;margin-bottom:3px;margin-left:0;">
                  ({{ timezone }}) :
                  <span
                    v-for="(value, key) in advisorSchedulesFormat"
                    :key="key"
                    style="margin-top: 0; margin-bottom: 0;">
                    {{ key }}
                    <slot v-for="(item, index) in value">
                      <slot v-if="index > 0">, </slot>{{item.start_time | momentFormatZone('h:mma',timezone)}} - {{item.end_time | momentFormatZone('h:mma',timezone)}}</slot><span v-if="Object.keys(advisorSchedulesFormat).slice(-1)[0] === key">,</span>
                  </span>
                </slot>
                <slot
                  v-if="!hasAdvisorSchedules"
                  style="font-size: 11pt; font-family: Calibri;margin-top:1pt;margin-right:0;margin-bottom:3px;margin-left:0;">
                  : Pending
                </slot></span>
            </p>
          </td>
        </tr>
        <tr v-if="showNominalCharge">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>{{ computedHourRate }}</b>
            </p>
          </td>
        </tr>
        <tr v-if="showAdditionalMultiplier || additional_charge_note">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri; margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>
                Additional Charges:
                <span v-if="showAdditionalMultiplier">{{ senior_rate_note }}</span>
                <span v-if="additional_charge_note">{{ additional_charge_note }}</span>
              </b>
            </p>
          </td>
        </tr>
        <tr v-if="hasTimeMiniMumCharge">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri; margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>
                {{ data.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
              </b>
            </p>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Employment History</b>
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <ul
              style="font-size: 11pt; font-family: Calibri; margin-top: 0px;margin-bottom:3px;margin-block-end:0;margin-inline-start: 0px;margin-inline-end: 0px;">
              <li v-for="job in advisor.jobs" :key="job.id" style="margin-top: 2px;margin-bottom: 0px">
                <b v-if="data.advisor_profile && job.id === data.advisor_profile.job_id">
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                  {{ getStockCode(job.company) }}
                </b>
                <span v-else>
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                  {{ getStockCode(job.company) }}
                </span>
              </li>
            </ul>
          </td>
        </tr>
        <tr v-if="shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <span
                v-for="(job, index) in advisor.jobs"
                :key="job.id"
                style="margin-top: 0; margin-bottom: 0;"
              >
                <br v-if="index > 0">
                <b v-if="data.advisor_profile && job.id === data.advisor_profile.job_id" style="background-color: #ffff00">
                  {{ job.company.name }} | {{ job.position }} | {{
                    displayTime(job.start_date)
                  }} – {{
                    handleCurrent(job)
                  }}
                  {{ getStockCode(job.company) }}</b>
                <span v-else>{{ job.company.name }} | {{ job.position }} | {{
                  displayTime(job.start_date)
                }} – {{ handleCurrent(job) }}
                  {{ getStockCode(job.company) }}</span>
              </span>
            </p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Total Consultation Usage:</b>
            </p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 3 Months: {{advisor.calls_last_three_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 6 Months: {{advisor.calls_last_six_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 12 Months: {{advisor.calls_last_twelve_months}}
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
              &nbsp;</p>
          </td>
        </tr>
        <tr v-if="data.latest_submitted_sq && !noQuestions && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0px">
            <sq-copy
              id="sq-copy-client"
              show-answer
              short-profile
              :data="data.latest_submitted_sq" />
          </td>
        </tr>
        <!--        <tr v-if="!data.latest_submitted_sq && !noQuestions">-->
        <!--          <td style="padding:0cm 0cm 0cm 0cm">-->
        <!--            <p-->
        <!--              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">-->
        <!--              No Screening Questions.</p>-->
        <!--          </td>-->
        <!--        </tr>-->
      </tbody>
    </table>
  </div>
</template>

<script>
import SqCopy from '@/components/Question/SqQuestionCopy'
import FormatProfileMixin from '@/components/FormatProfile/mixin'

export default {
  name: 'ProfileMcKinsey',
  components: { SqCopy },
  mixins: [FormatProfileMixin],
}
</script>

<style scoped>

</style>
