<template>
  <div>
    <table border="0" style="margin-bottom: 3px;">
      <tbody>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: <PERSON><PERSON>ri;margin-top:0px;margin-right:0;margin-bottom:0pt;margin-left:0;">
              <b style="font-size: 11pt;">Expert Name: </b>
              <span class="advisor-display-name"> {{ advisor.name_prefix || ''}} {{ advisor.full_name }} </span>
            </p>
            <p
              v-if="brief_job"
              style="font-size: 11pt; font-family: Calibri;margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;">
              <b style="font-size: 11pt;">Relevant Role: </b>
              {{ brief_job.company.name }} | {{ brief_job.position }} | {{
                displayTime(brief_job.start_date)
              }} – {{
                handleCurrent(brief_job)
              }}
            </p>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Full Bio:</b>
              {{ data.advisor_profile && data.advisor_profile.background }}
            </p>
          </td>
        </tr>
        <tr v-if="hasTimeMiniMumCharge">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri; margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>
                {{ data.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge
              </b>
            </p>
          </td>
        </tr>
        <tr>
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:3px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Job History:</b>
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <ul
              style="font-size: 11pt; font-family: Calibri; margin-top: 0px;margin-bottom:3px;margin-block-end:0;margin-inline-start: 0px;margin-inline-end: 0px;">
              <li v-for="job in advisor.jobs" :key="job.id" style="margin-top: 2px;margin-bottom: 0px">
                <b v-if="data.advisor_profile && job.id === data.advisor_profile.job_id">
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                </b>
                <span v-else>
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                </span>
              </li>
            </ul>
          </td>
        </tr>
        <tr v-if="shortProfile">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <span
                v-for="(job, index) in advisor.jobs"
                :key="job.id"
                style="margin-top: 0; margin-bottom: 0;">
                <br v-if="index > 0">
                <b v-if="data.advisor_profile && job.id === data.advisor_profile.job_id" style="background-color: #ffff00">
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                </b>
                <span v-else>
                  {{ job.company.name }} | {{ job.position }} | {{ displayTime(job.start_date) }} – {{
                    handleCurrent(job)
                  }}
                </span>
              </span>
            </p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              <b>Total Consultation Usage:</b>
            </p>
          </td>
        </tr>
        <tr v-if="showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 3 Months: {{advisor.calls_last_three_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 6 Months: {{advisor.calls_last_six_months}}
            </p>
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
              Last 12 Months: {{advisor.calls_last_twelve_months}}
            </p>
          </td>
        </tr>
        <tr v-if="!shortProfile && showExpertUsage">
          <td style="padding:0cm 0cm 0cm 0cm;">
            <p style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
              &nbsp;</p>
          </td>
        </tr>
        <tr v-if="!shortProfile && !noQuestions && data.latest_submitted_sq && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0cm">
            <p
              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:2pt;margin-left:0;">
              <b>Screening Questions/Responses</b>
            </p>
          </td>
        </tr>
        <tr v-if="data.latest_submitted_sq && !noQuestions && data.sq_status === 'RESPONDED'">
          <td style="padding:0cm 0cm 0cm 0cm">
            <sq-copy
              id="sq-copy-client"
              show-answer
              :data="data.latest_submitted_sq" />
          </td>
        </tr>
        <!--        <tr v-if="!data.latest_submitted_sq && !noQuestions">-->
        <!--          <td style="padding:0cm 0cm 0cm 0cm">-->
        <!--            <p-->
        <!--              style="font-size: 11pt; font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">-->
        <!--              No Screening Questions.</p>-->
        <!--          </td>-->
        <!--        </tr>-->
      </tbody>
    </table>
  </div>
</template>

<script>
import FormatProfileMixin from '@/components/FormatProfile/mixin'
import SqCopy from '@/components/Question/SqQuestionCopy'

export default {
  name: 'ProfileForGenerationIM',
  components: { SqCopy },
  mixins: [FormatProfileMixin],

  mounted() {
    this.initRelevantJobData()
  },
}
</script>

<style scoped>

</style>
