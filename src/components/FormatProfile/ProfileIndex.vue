<template>
  <div>
    <general-profile v-if="NoCustom"
                     :data="data"
                     :no-questions="noQuestions"
                     :no-availability="noAvailability"
                     :senior.sync="compute_senior"
                     :custom-time-zone="customTimeZone"
                     :additional-charges="additionalCharges"
                     v-bind="$attrs" />
    <GenerationIMProfile v-if="isGenerationClient"
                         :data="data"
                         :senior.sync="compute_senior"
                         :no-questions="noQuestions"
                         :no-availability="noAvailability"
                         :custom-time-zone="customTimeZone"
                         :additional-charges="additionalCharges"
                         v-bind="$attrs" />
    <mc-kinsey-profile v-if="isMcKinsey"
                       :data="data"
                       :no-questions="noQuestions"
                       :senior.sync="compute_senior"
                       :custom-time-zone="customTimeZone"
                       :additional-charges="additionalCharges"
                       v-bind="$attrs" />
  </div>
</template>

<script>
import GeneralProfile from './profileForCopy'
import GenerationIMProfile from './customGenerationIM/Profile'
import McKinseyProfile from './customMcKinsey/Profile'
import { mapState } from 'vuex'

export default {
  name: 'ProfileIndex',
  components: { GeneralProfile, GenerationIMProfile, McKinseyProfile },
  props: {
    data: Object,
    additionalCharges: {
      type: Array,
      default() {
        return []
      },
    },
    noQuestions: {
      type: Boolean,
      default: false,
    },
    noAvailability: {
      type: Boolean,
      default: false,
    },
    senior: Number,
    customTimeZone: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      compute_senior: this.senior,
    }
  },

  computed: {
    ...mapState({
      tasks: state => state.tasks.list,
      timeZone: state => state.app.timeZone,
      McKinseyId: state => state.app.McKinseyId,
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
    }),
    NoCustom() {
      return ![this.GenerationIMId, this.McKinseyId, this.GenerationGrowthEquityId].includes(this.data.client_id)
    },
    isGenerationClient() {
      return [this.GenerationIMId, this.GenerationGrowthEquityId].includes(this.data.client_id)
    },
    isMcKinsey() {
      return this.data.client_id === this.McKinseyId
    },
  },
  watch: {
    'compute_senior': {
      handler(newVal) {
        if (newVal) {
          this.$emit('update:senior', newVal)
        }
      },
    },
  },
}
</script>

<style scoped>

</style>
