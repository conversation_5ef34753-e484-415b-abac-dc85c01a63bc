<template>
  <div>
    <!--    {{data.firstname}} {{data.lastname}} has ___ years of experience in ___________.-->
    <slot v-for="job in formatted_jobs">
      <!--    // company id 97 :独立专家-->
      <slot v-if="job.length === 1">
        <slot v-if="job[0].is_current">
          Since {{ displayTime(job[0].start_date) }}, this advisor is
        </slot>
        <slot v-else>
          From {{ displayTime(job[0].start_date) }} to {{ handleCurrent(job[0]) }}, this advisor was
        </slot> {{ job[0].position }}
        <slot v-if="job[0].company && job[0].company.id!==97 "> at {{ job[0].company.name }}</slot>.
      </slot>
      <slot v-else>
        <slot v-if="job[0].is_current">
          Since {{ displayTime(job[job.length - 1].start_date) }},
        </slot>
        <slot v-else>
          From {{ displayTime(job[job.length - 1].start_date) }} to {{ handleCurrent(job[0]) }},
        </slot>
        <slot v-if="job[0].company && job[0].company.id!==97"> the advisor held numerous positions at
          {{ job[0].company.name }} --
        </slot>
        <slot v-if="job[0].company && job[0].company.id===97"> the advisor was an</slot>
        <slot v-for="(item,index) in job">{{ item.position }} ({{ displayTime(item.start_date) }} -
          {{ handleCurrent(item) }})<slot v-if="index !== job.length - 1">; </slot>
        </slot>.
      </slot>
    </slot>
  </div>
</template>

<script>
import FormatProfileMixin from '@/components/FormatProfile/mixin'

export default {
  name: 'BackgroundFormat',
  mixins: [FormatProfileMixin],
  props: {
    data: Object,
  },
  data() {
    return {
      formatted_jobs: [],
    }
  },
  mounted() {
    this.handleFormatProfile()
  },
  methods: {
    handleFormatProfile() {
      if (this.data.jobs && this.data.jobs.length) {
        const companies = []
        const formatted_jobs = []
        this.data.jobs.forEach(item => {
          if (!item.company) return

          const index = companies.indexOf(item.company.name)
          if (index === -1) {
            companies.push(item.company.name)
            formatted_jobs.push([item])
          } else {
            formatted_jobs[index].push(item)
          }
        })
        this.formatted_jobs = formatted_jobs
      }
    },

  },
}
</script>
