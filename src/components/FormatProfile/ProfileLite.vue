<!-- 参考风格为: [task_number] [name] – [position] at [company] | [start_date] – [end_date] -->

<template>
  <div>
    <ul
      style="margin-top: 0;margin-inline-start: 0px;margin-inline-end: 10px;"
      :style="[summaryOnlyProfile ? {'margin-bottom': '7px' } : '',shortProfile ? {'margin-bottom': '0px' } : '']"
    >
      <li style="margin-top: 2px;margin-bottom: 2px;">
        <p v-if="NoCustom" style="margin-top: 0; margin-bottom: 0; font-size: 11pt; font-family: Calibri;">
          <span v-if="CapvisionGKMTeam">{{ advisor.rate_currency | getLabel(currency_option) }}{{ advisor.rate }} / hour - </span>
          <span class="advisor-display-name-lite">{{ data.display_id }} {{ advisor.name_prefix || '' }}
            {{ advisor.firstname }} {{ advisor.lastname }}</span>
          <span v-if="displayJob"> – {{ job.position }} at
            <span v-if="shortProfile" style="background-color: #ffff00"><b>{{ job.company.name }}</b></span>
            <span v-else>{{ job.company.name }}</span>
            | {{displayTime(job.start_date) }} – {{ handleCurrent(job) }}{{ getStockCode(job.company) }}
          </span>
          <span v-if="displaySummary && data.standard_rate_multiplier_display_to_client">
            | {{ data.standard_rate_multiplier_display_to_client }}x Rate
          </span>
          <b v-if="data.screening_summary && !shortProfile"><i> - {{data.screening_summary}}</i></b>
        </p>
        <p v-if="GenerationClient" style="margin-top: 0; margin-bottom: 0; font-size: 11pt; font-family: Calibri, serif;">
          <span class="advisor-display-name-lite">{{ advisor.name_prefix || '' }} {{ advisor.firstname }} {{ advisor.lastname }}</span>
          <span v-if="displayJob">–
            <span v-if="shortProfile" style="background-color: #ffff00">{{ job.company.name }}</span>
            <span v-else>{{ job.company.name }}</span>
            | {{ job.position }} | {{displayTime(job.start_date) }} – {{ handleCurrent(job) }}
          </span>
          <span v-if="displaySummary && data.standard_rate_multiplier_display_to_client">
            | {{ data.standard_rate_multiplier_display_to_client }}x Rate
          </span>
          <b v-if="data.screening_summary && !shortProfile"><i> - {{data.screening_summary}}</i></b>
        </p>
        <ul v-if="data.screening_summary && shortProfile" type="circle" style="margin-top: 0px;margin-bottom: 0px;">
          <li style="margin-top: 0px;margin-bottom: 0px;"><b><i> {{data.screening_summary}}</i></b></li>
        </ul>
        <slot v-if="summaryChargeProfile || summaryChargeAverageCPIProfile">
          <p style="margin-top: 0; margin-bottom: 0; font-size: 11pt; font-family: Calibri, serif;">
            <b>{{ computedHourRate }}</b>
          </p>
          <p v-if="hasTimeMiniMumCharge" style="margin-top: 0; margin-bottom: 0; font-size: 11pt; font-family: Calibri, serif;">
            <b>{{ data.advisor.time_minimum | getLabel(advisor_options.time_minimum_charge) }} Minimum Charge</b>
          </p>
        </slot>
      </li>
    </ul>
  </div>
</template>

<script>
import FormatProfileMixin from '@/components/FormatProfile/mixin'
import { mapState } from 'vuex'

export default {
  name: 'ProfileFormatLite',
  mixins: [FormatProfileMixin],
  props: {
    data: Object,
  },
  data() {
    return {
      advisor: this.data.advisor,
      task: this.data.task,
      job: undefined,
      currency_option: [
        { label: '$', value: 'USD' },
        { label: 'S$', value: 'SGD' },
        { label: 'RM', value: 'MYR' },
        { label: '¥', value: 'RMB' },
        { label: '€', value: 'EUR' },
        { label: '£', value: 'GBP' },
        { label: 'J.¥', value: 'JPY' },
      ],
    }
  },
  computed: {
    ...mapState({
      GenerationIMId: state => state.app.GenerationIMId,
      GenerationGrowthEquityId: state => state.app.GenerationGrowthEquityId,
    }),
    displayJob() {
      return this.job
    },
    NoCustom() {
      return ![this.GenerationIMId, this.GenerationGrowthEquityId].includes(this.data.client_id)
    },
    GenerationClient() {
      return [this.GenerationIMId, this.GenerationGrowthEquityId].includes(this.data.client_id)
    },
    CapvisionGKMTeam() {
      return this.data.client.name.startsWith('Capvision GKM')
    },
    displaySummary() {
      return (this.summaryOnlyProfile || this.summaryChargeProfile || this.summaryChargeAverageCPIProfile) &&
        this.client?.preference?.display_additional_multiplier_on_portal
    },
  },
  mounted() {
    this.job = this.initJobData()
  },
  methods: {
    initJobData() {
      const jobs = this.advisor.jobs.length ? this.advisor.jobs[0] : undefined
      return this.data.advisor_profile.company_id ? this.data.advisor_profile : jobs
    },

  },
}
</script>
