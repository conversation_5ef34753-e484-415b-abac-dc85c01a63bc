<template>
  <div class="inline-block">
    <el-button
      type="primary"
      size="mini"
      class="ml-3"
      icon="el-icon-check"
      @click="initForm"
    >Check Email</el-button>
    <el-dialog
      title="Check Email Status"
      append-to-body
      :visible.sync="dialogVisible"
      width="30%">
      <el-form ref="form" v-loading="loading" :model="form" label-width="100px">
        <el-form-item
          label="Email"
          prop="email"
          :rules="[{ required: true, type:'email', trigger: 'blur' }]"
        >
          <el-input v-model="form.email" type="email" />
          <div v-if="no_matching_experts" class="danger">No matching experts.</div>
          <div>
            <span>
              <el-tooltip
                effect="dark"
                placement="top">
                <div slot="content">
                  If Bounced or rejected, return “No”, if deliverable, return “Yes”
                </div>
                <el-link type="info" :underline="false" icon="el-icon-info" />
              </el-tooltip>
              Receiving Status:</span>
            <span v-if="result"
                  :class="result.email_send_valid ? 'success' : 'danger'">{{ result.email_send_valid | yesOrNo }}</span>
          </div>
          <div v-if="result && !result.email_send_valid" v-loading="viewLoading">
            <span>Other Emails:</span>
            <span v-for="item in result.advisor_emails" :key="item.id">{{ item.value }}</span>
            <!--            <el-link v-if="result.advisor_emails.length > 0"-->
            <!--                     size="mini"-->
            <!--                     type="primary"-->
            <!--                     class="ml-3"-->
            <!--                     :underline="false"-->
            <!--                     @click="handleIsView"> View-->
            <!--            </el-link>-->
          </div>
          <div>
            <span>
              <el-tooltip
                effect="dark"
                placement="top">
                <div slot="content">
                  If tied to an expert who is unsubscribed or deleted from the DB, return “No”, and if not unsubscribed or deleted, return “Yes”
                </div>
                <el-link type="info" :underline="false" icon="el-icon-info" />
              </el-tooltip>
              Status:</span>
            <span v-if="result && !no_matching_experts" :class="result.advisor_email_status ? 'success' : 'danger'">
              {{ result.advisor_email_status | yesOrNo }}
            </span>
          </div>
        </el-form-item>
        <el-form-item
          label="Project IDs"
          prop="project_ids"
        >
          <el-input v-model="form.project_ids" type="text" placeholder="separated by a comma" />
          <div>
            <span>
              <el-tooltip
                effect="dark"
                placement="top">
                <div slot="content">
                  If for any of those project IDs the email address is tied to an expert who is in Decline status, return “No”, if not in Decline status, return “Yes”
                </div>
                <el-link type="info" :underline="false" icon="el-icon-info" />
              </el-tooltip>
              Status:</span>
            <span v-if="result && result.email_valid_in_project !== null"
                  :class="result.email_valid_in_project ? 'success' : 'danger'">
              {{ result.email_valid_in_project | yesOrNo }}
            </span>
          </div>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="loading" @click="checkEmail">Check</el-button>
        <el-button type="info" @click="dialogVisible = false">Cancel</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EmailAPI from '@/api/email'
import ToolAPI from '@/api/tool'

export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      viewLoading: false,
      dialogVisible: false,
      form: {
        email: null,
        project_ids: null,
      },
      result: null,
      no_matching_experts: false,
      match_advisor: null,
    }
  },
  methods: {
    initForm() {
      this.form = {
        email: null,
        project_ids: null,
      }
      this.dialogVisible = true
    },

    checkEmail() {
      this.result = null
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            email: this.form.email,
            project_ids: this.form.project_ids?.split(','),
          }
          return EmailAPI.EmailStatusValidation(params)
            .then((data) => {
              this.result = {
                email_send_valid: !data.tracking_status.bounced_or_dropped,
                email_valid_in_project: params.project_ids ? !data.decline_status.declined : null,
                advisor_emails: [],
              }

              if (data.advisor) {
                this.no_matching_experts = false
                this.match_advisor = data.advisor
                this.result.advisor_email_status = data.advisor.status !== 'BLACKLIST' &&
                  !data.advisor.gdpr_delete_by_id
                this.result.advisor_emails = data.advisor.contact_infos_without_mosaic.filter(
                  item => item.type === 'EMAIL' && item.value !== params.email) || []
              } else {
                this.no_matching_experts = true
              }
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    handleIsView() {
      this.viewLoading = true
      const params = {
        'owner_type': 'ADVISOR',
        'owner_id': this.match_advisor.id,
      }
      return ToolAPI.getContactInfo(params)
        .then(data => {
          this.result.advisor_emails.forEach(item => {
            const contact = data['list'].find(list => list.id === item.id)
            if (contact) item.value = contact.value
          })
        })
        .finally(() => {
          this.viewLoading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
