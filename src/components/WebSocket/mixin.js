import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState({
      unreadEvents: state => state.project.unread_subscription_events,
    }),
  },

  methods: {
    setFavicon() {
      const number = this.unreadEvents.total
      let iconUrl = '/favicon.ico'

      if (number > 0 && number <= 9) iconUrl = `/number/number${number}.svg`
      if (number > 9) iconUrl = '/number/number9-plus-circle.svg'
      // if (!number || number === 0) {
      //   iconUrl = '/favicon.ico'
      // } else {
      //   iconUrl = number > 9
      //     ? '/number/number9-plus-circle.svg'
      //     : `/number/number${number}.svg`
      // }

      const favicon = document.querySelector('#cap-tab-favicon')
      if (favicon) {
        favicon.href = iconUrl
      }
    },
  },
}
