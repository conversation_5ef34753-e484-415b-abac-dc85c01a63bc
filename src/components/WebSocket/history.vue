<template>
  <div>
    <el-button circle size="medium" class="websocket-history" @click="showHistory">
      <svg-icon icon-class="history" />
    </el-button>
    <el-drawer
      title="History"
      size="40%"
      :visible.sync="drawer"
      direction="rtl">
      <el-table
        v-loading="loading"
        border
        stripe
        class="mt-3"
        :data="history_list">
        <el-table-column
          label="Type">
          <template slot-scope="scope">
              <span>
                {{ scope.row.type | transformKey }}
              </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Time"
          width="100px">
          <template slot-scope="scope">
              <span>
                {{ scope.row.create_at_fromnow }}
              </span>
          </template>
        </el-table-column>
        <el-table-column
          label="Event">
          <template slot-scope="scope">
            <span v-html="formatMessage(scope.row)" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="searchQuery.page"
        :limit.sync="searchQuery.size"
        @pagination="getHistoryList" />
    </el-drawer>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'
import ProjectAPI from '@/api/project'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { mapGetters, mapState } from 'vuex'
import mixin from '@/components/WebSocket/mixin'

export default {
  name: 'history',
  components: { Pagination },
  mixins: [mixin],
  data() {
    return {
      loading: false,
      drawer: false,
      socketUrl: `${import.meta.env.VITE_APP_US_DB_API_V2}/ws`,
      stompClient: null,
      history_list: [],
      total: 0,
      searchQuery: {
        page: 1,
        size: 20,
        project_id: this.$route.params.project_id,
        sort: 'create_at desc',
        extra: 'task,advisor',
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      userInfo: state => state.user.info,
      projectInfo: state => state.project.data,
    }),
  },

  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 定义 SockJS 和 STOMP 客户端
      const sock = new SockJS(this.socketUrl, null, { transports: ['websocket'] })
      this.stompClient = Stomp.over(sock)

      // 连接服务器
      this.stompClient.connect({
        'Authorization': `Bearer ${getToken()}`,
      }, () => {
        // 订阅主题
        this.getUnreadEvents()

        this.stompClient.subscribe(`/topic/project_real_time/${this.projectInfo.id}`, (message) => {
          console.log('Received message: ', JSON.parse(message.body))
          this.accumulatedUnreadInfo(JSON.parse(message.body))
        })
      }, (error) => {
        console.error('Error connecting to WebSocket: ', error)
      })
    },

    accumulatedUnreadInfo(data) {
      if (data.task.angle_id) this.unreadEvents['client_task_ids'].push(`${data.task.angle_id}-${data.task_id}`)
      else this.unreadEvents['leads_task_ids'].push(data.task_id)
      this.unreadEvents.total += 1

      this.$store.dispatch('project/setUnreadEvents', { ...this.unreadEvents })
      this.setFavicon()
    },

    getUnreadEvents() {
      const params = {
        only_unread: true,
        project_id: this.searchQuery.project_id,
        create_at_gte: this.userInfo.preference?.enable_in_app_notification_time || undefined,
        page: 1,
        size: 10,
      }
      return ProjectAPI.getProjectWebSocketUnread(params)
        .then((data) => {
          const { paged_records, task_info } = data
          const leads_task_ids = task_info?.filter(item => !item.angle_id).map(item => item.task_id)

          // 格式化angle_id-task_id,为了在client task 页面通过angle_id 和 task_id 筛选数据
          const client_task_ids = task_info?.filter(item => item.angle_id)
            .map(item => `${item.angle_id}-${item.task_id}`)

          const result = {
            mask_read_leads: false,
            mask_read_client_task: false,
            total: paged_records?.count,
            client_task_ids: client_task_ids,
            leads_task_ids: leads_task_ids,
          }
          this.$store.dispatch('project/setUnreadEvents', result)
        })
        .then(() => {
          this.setFavicon()
        })
    },

    async showHistory() {
      await this.getHistoryList()
      this.drawer = true
    },

    getHistoryList() {
      this.loading = true
      return ProjectAPI.getProjectWebSocketHistory(this.searchQuery)
        .then(data => {
          data.list.forEach(item => {
            item.create_at_fromnow = `${moment(item.create_at).fromNow()} at ${moment(item.create_at)
              .format('hh:mm A')}`
          })
          this.history_list = data.list
          this.total = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },

    formatMessage(row) {
      const advisor_name = row.advisor.full_name

      let target_page = 'leads'
      let route_query = `?task_id=${row.task_id}&advisor_id=${row.advisor_id}`

      if (row.task?.angle_id) {
        target_page = 'client-tasks'
        if (row.task.project_sub_type === 'Investor Call') target_page = 'client-tasks-sst'
        if (row.task.client_id === this.McKinseyId) target_page = 'client-tasks-mck'
        route_query += `&angle_id=${row.task.angle_id}`
      }
      const target_url = `#/project/consultation/${row.project_id}/${target_page}${route_query}`
      const result = row.message.replace(advisor_name,
        `<a class="link"
            href="${target_url}"
            target="_blank">
            ${advisor_name}
          </a>`)
      return result
    },
  },
}
</script>

<style scoped>
.websocket-history {
  position: fixed;
  top: 35px;
  right: 5px;
  z-index: 100;
  font-size: 22px;
  padding: 8px;
}
</style>
