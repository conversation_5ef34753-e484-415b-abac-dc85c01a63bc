<template>
  <div
    v-if="isTargetPage && unreadEvents.total && unreadEvents[tabKey].length"
    class="custom-alert"
    >
    <el-alert
      type="success"
      class="cursor-pointer"
      center
      @close="markAsRead(true)"
    >
      <template #title>
        <div @click="markAsRead()">{{alertText}}</div>
      </template>
    </el-alert>
  </div>
</template>

<script>
import mixin from '@/components/WebSocket/mixin'
import ProjectAPI from '@/api/project'

export default {
  name: 'index',
  mixins: [mixin],
  props: {
    // leads_task_ids || client_task_ids
    tabKey: String,
  },
  data() {
    return {
      targetPage: ['ProjectLeads', 'ProjectClientTasks', 'ProjectClientTasksMcK', 'ProjectClientTasksSST'],
    }
  },

  computed: {
    alertText() {
      const current_type = this.tabKey === 'leads_task_ids' ? 'leads' : 'client task'
      return `Click here to view ${this.unreadEvents[this.tabKey].length} new ${current_type} updates`
    },
    isTargetPage() {
      return this.targetPage.includes(this.$route.name)
    },
  },

  methods: {
    markAsRead(clear = false) {
      this.maskReadToData()
        .then(() => {
          this.unreadEvents.total -= this.unreadEvents[this.tabKey].length

          if (clear) {
            this.unreadEvents[this.tabKey] = []
          } else {
            this.tabKey === 'leads_task_ids'
              ? this.unreadEvents.mask_read_leads = true
              : this.unreadEvents.mask_read_client_task = true
          }

          this.$store.dispatch('project/setUnreadEvents', { ...this.unreadEvents })
          this.setFavicon()
        })
    },

    maskReadToData() {
      const params = {
        event: 'READ',
        project_id: this.$route.params.project_id,
        'task.angle_id_is_null': this.tabKey === 'leads_task_ids',
      }
      return ProjectAPI.triggerProjectWebSocketEvent(params)
    },
  },
}
</script>

<style scoped lang="scss">
.custom-alert {
  margin-bottom: 10px;

  .el-alert--success.is-light {
    background-color: #dae8f6;
    color: #1989fa;
  }
}
</style>
