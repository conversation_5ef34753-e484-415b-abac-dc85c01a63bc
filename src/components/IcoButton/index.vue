<template>
  <el-tooltip
    class="item"
    :disabled="!tooltip"
    :hide-after="1500"
    effect="dark"
    :content="tooltip"
    placement="bottom">
    <el-button class="ico-btn" :class="{'no-border': noBorder}" @click="action">
      <div :class="{[`rotate-${deg || ''}`]: isRotating}">
        <slot name="ico" />
      </div>
    </el-button>
  </el-tooltip>
</template>

<script>
export default {
  name: 'IcoButton',
  props: {
    isRotate: {
      type: Boolean,
      default: false,
    },
    deg: {
      type: Number,
      default: 0,
    },
    noBorder: {
      type: Boolean,
      default: false,
    },
    tooltip: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      isRotating: false,
    }
  },

  methods: {
    action() {
      this.$emit('action')
      this.isRotating = !this.isRotating
    },
  },

}
</script>

<style scoped lang="scss">
  .ico-btn {
    padding: 0;
    width: 28px;
    height: 28px;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    div {
      transition: all .2s;
    }
  }

  .rotate-90 {
    transform: rotate(90deg);
  }

  .rotate {
    transform: rotate(-360deg);
  }

  .no-border {
    border: none;
    margin: 0;
    &:hover {
      border: 1px solid #DCDFE6;
    }
  }
</style>
