<template>
  <el-drawer
    :with-header="false"
    :close-on-press-escape="true"
    size="50%"
    v-bind="$attrs"
    @open="actionOpenDrawer"
    @close="actionCloseDrawer"
    v-on="$listeners">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane v-for="item in tabs" :key="item.value" :label="item.label" :name="item.value" />
    </el-tabs>

    <top-advisors
      ref="shoppingCart"
      :is-all="isAll"
    />
  </el-drawer>
</template>

<script>
import TopAdvisors from '@/components/ShoppingCart/components/TopAdvisors/index'

export default {
  name: 'ShoppingCartDrawer',
  components: { TopAdvisors },
  data() {
    return {
      tabs: [
        {
          label: 'My Top Advisors',
          value: 'TopAdvisors',
        },
        {
          label: 'All Top Advisors',
          value: 'AllTopAdvisors',
        },
      ],
      activeTab: 'TopAdvisors',
    }
  },
  computed: {
    isAll() {
      return this.activeTab === 'AllTopAdvisors'
    },
  },
  methods: {
    actionOpenDrawer() {
      this.$refs['shoppingCart']?.initCartItems()
    },
    actionCloseDrawer() {
      this.$refs['shoppingCart']?.data.forEach(item => {
        item.tags_editing = false
      })
    },
    handleTabClick() {
      this.$nextTick(() => {
        this.actionOpenDrawer()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
//::v-deep .el-drawer__header {
//  padding: 10px 10px 0 15px;
//}

::v-deep .el-drawer__body {
  padding: 0 10px 10px;
  overflow: inherit;
}
</style>
