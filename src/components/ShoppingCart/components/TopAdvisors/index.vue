<template>
  <div class="h-100">
    <el-form inline>
      <el-form-item class="top-advisor-filter-item">
        <advisor-search
          v-model="form.advisor_ids"
          input-phone-email
          placeholder="Name / Phone / Email"
          class="w-200px"
          @change="initCartItems"
        />
      </el-form-item>
      <el-form-item class="top-advisor-filter-item">
        <top-advisor-tags-selector
          v-model="form.tags"
          class="w-200px"
          @change="initCartItems"
        />
      </el-form-item>
      <el-form-item class="top-advisor-filter-item">
        <add-to-project
          :advisors.sync="selectedAdvisors"
          :init-project="projectInfo"
          @update="clearSelection()" />
      </el-form-item>
      <el-tooltip placement="top">
        <div slot="content">
          You can press <b>Ctrl / Command</b> and <b>Click</b> to select advisors
        </div>
        <el-link
          type="default"
          :underline="false"
          icon="el-icon-question"
          class="mr-8"
          style="margin-top: 5px;" />
      </el-tooltip>
    </el-form>

    <div class="h-100">
      <el-alert
        v-if="!loading && !data.length"
        title="You don’t have any Top Advisors yet!"
        type="warning"
        show-icon />

      <el-row
        v-infinite-scroll="getShoppingCartItems"
        :infinite-scroll-disabled="scrollDisabled"
        :infinite-scroll-distance="50"
        :gutter="10"
        class="top-advisor-list">
        <transition-group name="el-fade-in-linear" class="top-advisor-list-transition">
          <el-col v-for="(item, index) in data" :key="item.ref_id" :md="8" class="top-advisor-col">
            <top-advisor-item-card
              :data="item"
              :tags-editing.sync="item.tags_editing"
              :is-all="isAll"
              @after-edit-tags="afterEditTags($event, item)"
              @click.native="toggleSelectTopAdvisorItem($event, item)"
              @remove="removeTopAdvisorItem(item, index)"
            />
          </el-col>
        </transition-group>
        <p v-if="loading" class="text-center info w-100">
          <i class="el-icon-loading mr-8" />Loading
        </p>
        <p v-if="noMore" class="text-center info w-100">No More</p>
      </el-row>
    </div>
  </div>
</template>

<script>
import CartAPI from '@/api/cart'
import TopAdvisorItemCard from '@/components/ShoppingCart/components/TopAdvisors/components/TopAdvisorItemCard'
import AddToProject from '@/views/consultant/components/addToProject'
import AdvisorSearch from '@/components/SelectRemoteSearch/AdvisorSearch'
import TopAdvisorTagsSelector from './components/TopAdvisorTagsSelector'
import { mapGetters, mapState } from 'vuex'
import Sortable from 'sortablejs'

export default {
  name: 'TopAdvisor',
  components: { TopAdvisorItemCard, AddToProject, AdvisorSearch, TopAdvisorTagsSelector },
  props: {
    isAll: Boolean,
  },
  data() {
    return {
      loading: false,
      form: {
        advisor_ids: [],
        tags: [],
      },
      searchQuery: {
        page: 1,
        size: 15,
      },
      count: 0,
      data: [],
      topAdvisorSortable: null,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    ...mapState({
      projectInfo: state => state.project.data,
      shoppingCartItemIds: state => state.app.shoppingCartItemIds,
    }),
    noMore() {
      return !this.loading && this.data.length >= this.count
    },
    scrollDisabled() {
      return this.loading || this.noMore
    },
    selectedAdvisors() {
      return this.data.filter(item => item.is_selected).map(item => item.ref)
    },
  },
  created() {
    this.initSearchQuerySize()
  },
  mounted() {
    this.initCartItems()
  },
  beforeDestroy() {
    if (this.topAdvisorSortable) this.topAdvisorSortable.destroy()
  },
  methods: {
    getShoppingCartItems() {
      this.loading = true
      const query = {
        ...this.searchQuery,
        ref_type: 'ADVISOR',
        extra: [
          'ref.current_job.company',
          'ref.contact_infos',
          'ref.user_advisor_tag_maps.tag'].join(),
      }
      const params = {
        user_id: this.uid,
        advisor: {
          ids: this.form.advisor_ids.length ? this.form.advisor_ids : undefined,
          user_advisor_tag_maps: this.form.tags.length ? {
            tag_ids: this.form.tags.map(tag => tag.id).filter(id => id),
          } : undefined,
        },
      }
      const requestName = this.isAll ? 'getShoppingCartAllTopItems' : 'getShoppingCartItems'

      return CartAPI[requestName](query, params)
        .then(data => {
          this.count = data.count
          const result = data.list.map(item => {
            item.ref.phone = item.ref.contact_infos.find(item => item.type === 'PHONE')
            item.ref.email = item.ref.contact_infos.find(item => item.type === 'EMAIL')
            item.ref.user_advisor_tag_maps = item.ref.user_advisor_tag_maps.filter(map => map.tag)
            item.in_cart = this.shoppingCartItemIds.includes(item.ref_id)
            item.is_selected = false
            item.tags_editing = false
            return item
          })
          this.data = [...this.data, ...result]
          this.$store.dispatch('app/setShoppingCartItemIds', this.data.map(item => item.ref_id))
          this.$nextTick(() => {
            this.loadSortable()
          })
          this.searchQuery.page += 1
        })
        .finally(() => {
          this.loading = false
        })
    },
    initSearchQuerySize() {
      const height = window.innerHeight
      const size = Math.ceil(height / 160) * 3
      this.searchQuery.size = size > 15 ? size : 15
    },
    initCartItems() {
      this.searchQuery.page = 1
      this.count = 0
      this.data = []
      this.getShoppingCartItems()
    },
    async removeTopAdvisorItem(item, index) {
      await CartAPI.removeShoppingCartItemsBatch({ ids: item.id })
      this.data.splice(index, 1)
      await this.$store.dispatch('app/setShoppingCartCount', this.data.length)
    },
    loadSortable() {
      const list = this.$el.querySelector('.top-advisor-list-transition')
      if (!list) return

      const options = {
        handle: '.cursor-move',
        animation: 200,
        onChoose: ({ oldIndex, item }) => {
          item.id = this.data[oldIndex].id
        },
        onStart: evtData => {},
        onUpdate: evtData => {
          onEnd.call(this, evtData)
        },
        onRemove: ({ oldIndex, item, from }) => {
          // insertNodeAt(from, item, oldIndex)
          // const index = this.data.findIndex(i => i.id === item.id)
          // if (index > -1) this.data.splice(index, 1)
        },
        onAdd: evtData => {
          onAdd.call(this, evtData)
        },
      }

      this.topAdvisorSortable = new Sortable(list, options)

      function onEnd({ newIndex, oldIndex, item, from }) {
        if (newIndex === oldIndex) return

        this.saveSort(newIndex, oldIndex, item, from)
      }

      function onAdd({ newIndex, oldIndex, item, from }) {
        console.log('add')
      }
    },
    saveSort(newIndex, oldIndex, item, from) {
      const params = {
        ref_type: 'ADVISOR',
        id_index_map: {
          [item.id]: newIndex,
        },
      }
      return CartAPI.updateShoppingCartItemsSort(params)
        .then(() => {
          const target = this.data.find(i => i.id === +item.id)
          const clone_data = { ...target }
          this.data.splice(oldIndex, 1)
          this.data.splice(newIndex, 0, clone_data)
        })
    },
    toggleSelectTopAdvisorItem(event, item) {
      const isMetaKey = event.metaKey
      const isCtrlKey = event.ctrlKey
      if (isMetaKey || isCtrlKey) {
        item.is_selected = !item.is_selected
      }
    },
    clearSelection() {
      this.data.forEach(item => {
        item.is_selected = false
      })
    },
    afterEditTags(tags, advisor) {
      advisor.tags_editing = false

      const params = []
      tags.forEach(tag => {
        const origin_tag = advisor.ref.user_advisor_tag_maps.find(item => item.tag_id === tag.id)
        if (origin_tag) {
          params.push({ id: origin_tag.id })
        } else {
          if (tag.id >= 1) {
            params.push({ tag_id: tag.id })
          } else {
            params.push({
              tag: {
                name: tag.name,
                color: tag.color,
              },
            })
          }
        }
      })
      return CartAPI.updateAdvisorTags(advisor.ref_id, params)
        .then(data => {
          advisor.ref.user_advisor_tag_maps = data.user_advisor_tag_maps
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.top-advisor-filter-item {
  margin-bottom: 5px !important;
}

.top-advisor-list {
  width: 100%;
  height: calc(100% - 87px);
  overflow: auto
}

.top-advisor-list-transition {
  display: flex;
  flex-flow: wrap;
}

.top-advisor-col {
  margin: 5px 0;
}
</style>
