<template>
  <el-select
    ref="TopAdvisorTagsSelector"
    multiple
    filterable
    allow-create
    value-key="id"
    default-first-option
    placeholder="Tags"
    remote
    :remote-method="remoteSearchAdvisorTags"
    :loading="loading"
    v-bind="$attrs"
    @change="handleChangeTags"
    @visible-change="handleVisibleChange"
    v-on="$listeners">
    <el-option-group label="Select an option or create one">
      <div v-if="disabled" class="tags-mask" @click="hideTagPopover" />
      <el-option
        v-for="(item, index) in options.tags"
        :key="item.id"
        :label="item.name"
        :value="item"
      >
        <div class="flex align-items-center justify-content-between h-100">
          <div class="option-tag" :class="`${item.color}-color`">{{ item.name }}</div>
          <el-popover
            v-model="item.popover_visible"
            placement="right"
            width="200"
            trigger="click"
            :visible-arrow="false"
            :append-to-body="appendToBody"
            @show="openTagsPopover(item)"
            @after-enter="afterEnterTagsPopover(item, index)"
          >
            <el-link slot="reference" :underline="false" class="more-btn" @click.stop>
              <i class="el-icon-more" />
            </el-link>
            <div class="mb-8 pl-5">
              <!--              <el-input-->
              <!--                ref="tagNameInput"-->
              <!--                v-model="tagName"-->
              <!--                @keyup.enter.native="saveTag(item)"-->
              <!--                @focus="$event.currentTarget.select()"-->
              <!--              />-->
              {{ tagName }}
            </div>
            <!--            <div class="tag-select-item" @click="saveTag(item)">-->
            <!--              <i class="el-icon-check tag-select-item-icon primary" />-->
            <!--              Save-->
            <!--            </div>-->
            <div class="tag-select-item" @click="deleteTag(item, index)">
              <i class="el-icon-delete tag-select-item-icon danger" />
              Delete
            </div>
            <div class="tag-select-item" @click="item.popover_visible = false">
              <i class="el-icon-close tag-select-item-icon" />
              Close
            </div>
            <div class="colors">
              <div class="colors-title">COLORS</div>
              <div
                v-for="color in options.colors"
                :key="color.value"
                class="tag-select-item flex justify-content-between"
                @click="handleChangeTagColor(item, color)"
              >
                <div class="flex align-items-center">
                  <div class="color-example" :class="`${color.value}-color`" />
                  {{ color.label }}
                </div>
                <i v-if="item.color === color.value" class="el-icon-check primary" />
              </div>
            </div>
          </el-popover>
        </div>
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
import CartAPI from '@/api/cart'

export default {
  name: 'TopAdvisorTagsSelector',
  props: {
    appendToBody: Boolean,
  },
  data() {
    return {
      loading: false,
      tagName: '',
      isDeleting: false,
      options: {
        tags: [],
        created_tags: [],
        colors: [
          {
            value: 'light-gray',
            label: 'Light gray',
          }, {
            value: 'gray',
            label: 'Gray',
          }, {
            value: 'brown',
            label: 'Brown',
          }, {
            value: 'orange',
            label: 'Orange',
          }, {
            value: 'yellow',
            label: 'Yellow',
          }, {
            value: 'green',
            label: 'Green',
          }, {
            value: 'blue',
            label: 'Blue',
          }, {
            value: 'purple',
            label: 'Purple',
          }, {
            value: 'pink',
            label: 'Pink',
          }, {
            value: 'red',
            label: 'Red',
          }],
      },
    }
  },
  computed: {
    disabled() {
      return this.options.tags.some(item => item.popover_visible)
    },
    originTags() {
      return this.$attrs.value
    },
  },
  mounted() {
    this.initAdvisorTags()
  },
  methods: {
    initAdvisorTags() {
      this.options.created_tags = []
      if (this.originTags?.length) {
        this.options.tags = [...this.originTags]
      }
      this.searchAdvisorTags().then(tags => {
        tags.forEach(tag => {
          const is_existed = this.options.tags.some(item => item.id === tag.id)
          if (!is_existed) this.options.tags.push(tag)
        })
      })
    },
    remoteSearchAdvisorTags(val) {
      this.loading = true
      return this.searchAdvisorTags(val).then(tags => {
        this.options.tags = [...this.options.created_tags, ...tags]
      }).finally(() => {
        this.loading = false
      })
    },
    searchAdvisorTags(val) {
      return CartAPI.getAdvisorTags({
        name_contains: val?.trim(),
      }).then(data => {
        return data.list.map(item => {
          item.popover_visible = false
          item.origin_color = item.color
          item.origin_name = item.name
          return item
        })
      })
    },
    handleVisibleChange(value) {
      if (!value && !this.isDeleting) {
        this.$emit('after-edit-tags', this.originTags)
      }
    },
    openTagsPopover(tag) {
      this.tagName = tag.name
    },
    afterEnterTagsPopover(tag, index) {
      // this.$nextTick(() => {
      //   this.$refs['tagNameInput'][index].select()
      // })
    },
    hideTagPopover() {
      this.options.tags.forEach(item => {
        item.popover_visible = false
      })
    },
    deleteTag(item, index) {
      this.isDeleting = true
      this.$confirm(`Are you sure you want to remove "${item.name}" this option?`, 'Notice', {
        type: 'warning',
      }).then(async() => {
        if (item.id >= 1) await CartAPI.deleteTag(item.id)
        // 若删除的tag已经在专家卡片中，也要删除对应tag
        const tag_index = this.originTags.findIndex(tag => tag.id === item.id)
        if (tag_index > -1) {
          const arr = [...this.originTags]
          arr.splice(tag_index, 1)
          // this.$emit('input', arr)
          this.$emit('after-edit-tags', arr)
        }

        this.options.tags.splice(index, 1)
      }, () => {
      }).then(() => {
        this.$nextTick(() => {
          this.$refs['TopAdvisorTagsSelector']?.focus()
          item.popover_visible = true
          this.isDeleting = false
        })
      })
    },
    saveTag(item) {
      item.name = this.tagName
      item.popover_visible = false
      return CartAPI.updateTag(item.id, {
        // name: item.name,
        color: item.color,
      })
    },
    handleChangeTagColor(item, color) {
      if (item.color !== color.value) {
        item.color = color.value
        if (item.id >= 1) {
          CartAPI.updateTag(item.id, {
            // name: item.name,
            color: item.color,
          })
        }
      }
      this.$nextTick(() => {
        this.$refs['TopAdvisorTagsSelector']?.focus()
      })
    },
    handleChangeTags(val) {
      const index = val.findIndex(tag => typeof tag === 'string')
      if (index > -1) {
        const tag = {
          name: val[index].trim(),
          color: 'light-gray',
          id: Math.random(),
        }
        this.$nextTick(() => {
          const arr = [...this.originTags]
          arr.splice(index, 1)
          arr.push(tag)
          this.$emit('input', arr)
          if (!this.options.created_tags.some(item => item.name === tag.name)) {
            this.options.created_tags.push(tag)
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-select-group__title {
  padding-right: 20px;
}

.more-btn {
  display: none;
  padding: 0 10px;
  margin-right: -10px;
}

.el-select-dropdown__item.hover {
  .more-btn {
    display: block;
  }
}

.tags-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.option-tag {
  border-radius: 3px;
  padding: 0 6px;
  height: 24px;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-select-item {
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
  transition: background 20ms ease-in 0s;
  padding-right: 4px;
  padding-left: 4px;
  border-radius: 3px;
  font-weight: normal;

  &:hover {
    background-color: #F5F7FA;
  }
}

.tag-select-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 10px;
  margin-top: 3px;
  margin-bottom: 3px;
  width: 20px;
  height: 20px;
  border-radius: 3px;
}

.colors {
  padding-top: 6px;
  box-shadow: rgba(55, 53, 47, 0.09) 0px -1px 0px;
  margin-top: 6px;

  .colors-title {
    display: flex;
    padding-left: 4px;
    padding-right: 4px;
    margin-top: 6px;
    margin-bottom: 8px;
    color: rgba(55, 53, 47, 0.65);
    fill: rgba(55, 53, 47, 0.45);
    font-size: 11px;
    font-weight: 500;
    line-height: 120%;
    user-select: none;
    text-transform: uppercase;
  }

  .color-example {
    margin-right: 10px;
    margin-top: 4px;
    margin-bottom: 4px;
    width: 18px;
    height: 18px;
    border-radius: 3px;
    box-shadow: rgba(15, 15, 15, 0.1) 0 0 0 1px inset;
  }
}
</style>
