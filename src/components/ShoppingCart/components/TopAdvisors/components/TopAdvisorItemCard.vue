<template>
  <div class="top-advisor-card" :class="{'is-selected': data.is_selected}">
    <transition name="fade">
      <div v-if="data.is_selected" class="selected-icon">
        <i class="el-icon-check" />
      </div>
    </transition>

    <div v-show="!undo" class="flex flex-column">
      <div class="top-advisor-card-text flex align-items-center justify-content-between">
        <div class="flex-1 text-truncate flex align-items-center">
          <router-link-advisor :data="data.ref" class="text-truncate" />
          <new-advisor-tag :advisor="data.ref" class="new-tag ml-8" />
        </div>
        <div class="flex align-items-center">
          <el-link
            v-if="isAll && data.in_cart"
            icon="el-icon-star-on"
            type="success"
            :underline="false"
            title="Click to remove from My Top Advisors"
            @click="removeFromShoppingCart(data)" />
          <el-link
            v-if="isAll && !data.in_cart"
            icon="el-icon-star-off"
            type="success"
            :underline="false"
            title="Click to add to My Top Advisors"
            @click="addToShoppingCart(data)" />
          <el-link
            v-if="!isAll"
            icon="el-icon-rank"
            :underline="false"
            class="cursor-move mr-8" />
          <el-link
            v-if="!isAll"
            icon="el-icon-error"
            :underline="false"
            @click="undoRemoveConfirm" />
        </div>
      </div>
      <div class="top-advisor-card-text text-truncate">
        <span v-if="data.ref.rate" class="warning">
          {{ data.ref.rate }} {{ data.ref.rate_currency }}
        </span>
      </div>
      <div class="top-advisor-card-text text-truncate">
        <span v-if="data.ref.current_job" :title="data.ref.current_job.company.name">
          {{ data.ref.current_job.company.name }}
        </span>
      </div>
      <div class="top-advisor-card-text text-truncate">
        <span v-if="data.ref.current_job" :title="data.ref.current_job.position">
          {{ data.ref.current_job.position }}
        </span>
      </div>
      <div class="top-advisor-card-text text-truncate">
        <span v-if="data.ref.email" class="link">{{ data.ref.email.value }}</span>
      </div>
      <div class="top-advisor-card-text text-truncate">
        <span v-if="data.ref.phone" class="top-advisor-card-phone">{{ data.ref.phone.value }}</span>
      </div>
      <top-advisor-tags
        :advisor="data.ref"
        :tags-editing="tagsEditing"
        is-card
        v-bind="$attrs"
        v-on="$listeners"
      />
    </div>

    <div
      v-show="undo"
      class="undo-mask"
      @click="undoRemoveTopAdvisorItem"
    >
      <div class="mb-8">Expert Removed</div>
      <b class="link">Click To Restore ({{ undoCountdown }}s)</b>
    </div>
  </div>
</template>

<script>
import RouterLinkAdvisor from '@/components/RouterLink/Advisor'
import NewAdvisorTag from '@/components/NewAdvisorTag'
import TopAdvisorTags from './TopAdvisorTags'
import CartAPI from '@/api/cart'

export default {
  name: 'TopAdvisorItemCard',
  components: { RouterLinkAdvisor, NewAdvisorTag, TopAdvisorTags },
  props: {
    data: Object,
    tagsEditing: Boolean,
    isAll: Boolean,
  },
  data() {
    return {
      undo: false,
      undoCountdown: 0,
      undoInterval: null,
    }
  },
  methods: {
    undoRemoveConfirm() {
      this.undo = true
      this.undoCountdown = 5
      this.undoInterval = setInterval(() => {
        this.undoCountdown -= 1
        if (!this.undoCountdown) {
          setTimeout(() => {
            clearInterval(this.undoInterval)
            this.$emit('remove')
          }, 200)
        }
      }, 1000)
    },
    undoRemoveTopAdvisorItem() {
      this.undo = false
      this.undoCountdown = 0
      clearInterval(this.undoInterval)
    },
    addToShoppingCart(item) {
      return CartAPI.addToShoppingCartBatch({
        ref_type: 'ADVISOR',
        items: [
          {
            ref_id: item.ref_id,
          },
        ],
      }).then(val => {
        item.in_cart = true
        this.$store.dispatch('app/setShoppingCartCount', val)
      })
    },
    removeFromShoppingCart(item) {
      const query = {
        ref_type: 'ADVISOR',
        ref_ids: item.ref_id,
      }
      return CartAPI.removeShoppingCartItemsBatch(query).then(val => {
        item.in_cart = false
        this.$store.dispatch('app/setShoppingCartCount', val)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.top-advisor-card {
  position: relative;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 6px;
  transition: .3s;
  font-size: 13px;
  min-height: 157px;
  height: 100%;

  &.is-selected {
    border: 1px solid #409eff;
    background-color: #ecf5ff;
  }

  &:hover {
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .1);
  }

  .selected-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 1.2em;
    line-height: 1;
    padding: 0.2em;
    border-top-left-radius: 2px;
    color: #fff;
    background-color: #3f9eff;
    pointer-events: none;
  }

  .top-advisor-card-text {
    padding: 4px 6px;
    min-height: 23px;
  }

  .top-advisor-card-phone {
    color: #0000ff;
  }

  .new-tag {
    height: 15px;
    line-height: 13px;
  }

  .undo-mask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #F2F6FC;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
