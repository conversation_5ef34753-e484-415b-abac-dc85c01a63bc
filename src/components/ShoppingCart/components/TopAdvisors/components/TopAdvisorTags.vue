<template>
  <div>
    <div
      v-if="!tagsEditing"
      class="advisor-tags"
      @click="actionStartEditTags"
    >
      <div class="advisor-tags-inner">
        <div
          v-for="map in advisor.user_advisor_tag_maps"
          :key="map.id"
          class="advisor-tag"
          :class="`${map.tag.color}-color`">
          {{ map.tag.name }}
        </div>
        <div
          v-if="!advisor.user_advisor_tag_maps.length"
          class="advisor-tag-placeholder">
          Click To Select Tags
        </div>
      </div>
    </div>
    <div
      v-if="tagsEditing"
      :class="{'advisor-tags-selector': isCard}"
    >
      <top-advisor-tags-selector
        ref="TopAdvisorTagsSelector"
        v-model="tags"
        class="w-100"
        v-bind="$attrs"
        v-on="$listeners"
      />
    </div>
  </div>
</template>

<script>
import TopAdvisorTagsSelector from './TopAdvisorTagsSelector'

export default {
  name: 'TopAdvisorTags',
  components: { TopAdvisorTagsSelector },
  props: {
    advisor: Object,
    tagsEditing: Boolean,
    isCard: Boolean,
  },
  data() {
    return {
      tags: [],
    }
  },
  methods: {
    actionStartEditTags() {
      this.tags = this.advisor.user_advisor_tag_maps.map(item => item.tag)
      this.$emit('update:tagsEditing', true)
      this.$nextTick(() => {
        this.$refs['TopAdvisorTagsSelector'].$el.click()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.advisor-tags {
  padding: 4px 6px 1px;
  user-select: none;
  transition: background 20ms ease-in 0s;
  cursor: pointer;
  //display: inline-block;
  align-items: center;
  border-radius: 3px;
  width: 100%;
  min-height: 28px;
  font-size: 13px;
  overflow: hidden;

  &:hover {
    background: rgba(55, 53, 47, 0.08);
  }

  .advisor-tags-inner {
    display: flex;
    flex-wrap: wrap;

    .advisor-tag-placeholder {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
    }

    .advisor-tag {
      display: flex;
      align-items: center;
      flex-shrink: 1;
      min-width: 0;
      max-width: 100%;
      height: 20px;
      border-radius: 3px;
      padding-left: 6px;
      padding-right: 6px;
      line-height: 120%;
      margin: 0 6px 3px 0;
    }
  }
}

.advisor-tags-selector {
  padding: 0 6px;
}
</style>
