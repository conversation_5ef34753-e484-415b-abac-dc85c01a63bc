<template>
  <div>
    <el-button class="shopping-cart-button" @click="drawerVisible = true">
      <i class="el-icon-shopping-cart-2" /> {{ cartCount }}
    </el-button>

    <shopping-cart-drawer
      :visible.sync="drawerVisible"
    />
  </div>
</template>

<script>
import CartAPI from '@/api/cart'
import ShoppingCartDrawer from '@/components/ShoppingCart/ShoppingCartDrawer'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'ShoppingCartButton',
  components: { ShoppingCartDrawer },
  data() {
    return {
      drawerVisible: false,
    }
  },
  computed: {
    ...mapState({
      cartCount: state => state.app.shoppingCartCount,
    }),
    ...mapGetters([
      'uid',
    ]),
  },
  mounted() {
    this.getShoppingCartCount()
  },
  methods: {
    getShoppingCartCount() {
      return CartAPI.getShoppingCartItems({}, { user_id: this.uid })
        .then(data => {
          this.$store.dispatch('app/setShoppingCartItemIds', data.list.map(item => item.ref_id))
          this.$store.dispatch('app/setShoppingCartCount', data.count)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.shopping-cart-button {
  margin-right: 8px;
  padding: 3px 10px;

  i {
    font-size: 20px;
  }
}
</style>
