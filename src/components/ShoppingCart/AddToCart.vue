<template>
  <el-button
    :type="isInCart ? 'warning' : 'primary'"
    icon="el-icon-shopping-cart-2"
    :disabled="disabled"
    @click="toggleToCart">
    {{ btnText }}
  </el-button>
</template>

<script>
import CartAPI from '@/api/cart'
import { mapState } from 'vuex'

export default {
  name: 'AddToCart',
  props: {
    advisorId: Number,
    advisors: Array,
  },
  data() {
    return {
      inCart: false,
    }
  },
  computed: {
    ...mapState({
      shoppingCartItemIds: state => state.app.shoppingCartItemIds,
    }),
    isInCart() {
      return this.advisorId && this.shoppingCartItemIds.includes(this.advisorId)
    },
    disabled() {
      return !this.advisorId && !this.advisors?.length
    },
    btnText() {
      let btn_text = 'Add to My Top Advisors'
      if (this.isInCart) {
        btn_text = 'Remove From Cart'
      }
      return btn_text
    },
  },
  methods: {
    toggleToCart() {
      if (!this.isInCart) {
        this.addToShoppingCart()
      } else {
        this.removeFromShoppingCart()
      }
    },
    addToShoppingCart() {
      let items
      if (this.advisorId) {
        items = [{ ref_id: this.advisorId }]
      }
      if (this.advisors) {
        items = this.advisors.map(advisor => ({ ref_id: advisor.advisor_id || advisor.id }))
      }
      return CartAPI.addToShoppingCartBatch({
        ref_type: 'ADVISOR',
        items,
      }).then(val => {
        this.$message({
          message: 'Add to cart successfully',
          type: 'success',
        })
        this.$store.dispatch('app/setShoppingCartCount', val)

        if (this.advisorId) {
          const ids = [...this.shoppingCartItemIds, this.advisorId]
          this.$store.dispatch('app/setShoppingCartItemIds', Array.from(new Set(ids)))
        }
      })
    },
    removeFromShoppingCart() {
      const query = {
        ref_type: 'ADVISOR',
        ref_ids: this.advisorId,
      }
      return CartAPI.removeShoppingCartItemsBatch(query).then(val => {
        this.$message({
          message: 'Remove from cart successfully',
          type: 'success',
        })
        this.$store.dispatch('app/setShoppingCartCount', val)

        const ids = [...this.shoppingCartItemIds]
        const index = ids.findIndex(id => id === this.advisorId)
        if (index > -1) {
          ids.splice(index, 1)
          this.$store.dispatch('app/setShoppingCartItemIds', ids)
        }
      })
    },
  },
}
</script>
