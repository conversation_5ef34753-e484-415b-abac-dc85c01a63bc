<template>
  <div v-show="total>pageSize || total>pageSizes[0]" :class="{'hidden':hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to'

export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 20,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 50, 100, 200, 300]
      },
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    background: {
      type: Boolean,
      default: true,
    },
    autoScroll: {
      type: <PERSON>olean,
      default: true,
    },
    hidden: {
      type: <PERSON>olean,
      default: false,
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      },
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      },
    },
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
      if (this.autoScroll) {
        scrollTo(0, 200)
      }
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
      if (this.autoScroll) {
        scrollTo(0, 200)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.pagination-container {
  background: #fff;
  padding: 20px 10px 0;
}
.el-pagination {
  ::v-deep .el-pagination__total,
  ::v-deep .el-select, ::v-deep .el-pager .number ,
  ::v-deep .el-pagination__jump,
  ::v-deep .el-pagination__sizes .el-input .el-input__inner {
    font-size: 12px;
  }
}

.pagination-container.hidden {
  display: none;
}
</style>
