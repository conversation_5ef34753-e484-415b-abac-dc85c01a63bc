<template>
  <div class="location">
    <div class="el-input el-input--mini form-control cursor-pointer"
         :class="{ 'is-disabled': isLoading }"
         @click="actionShowDialog">
      <div class="el-input__inner location-select">

        <!-- Placeholder -->
        <div v-if="!selected_location.length" style="max-width: 155px;">
          <span class="location-placeholder">{{ placeholder }}</span>
        </div>

        <!-- 正在显示的 Location -->
        <div class="location-option el-select">
          <span v-for="(item, index) in selected_location"
                :key="item ? item.id : index"
                class="el-tag el-tag--info el-tag--mini el-tag--light">
            <span v-if="item" class="el-select__tags-text">{{ item.name }}</span>
            <i class="el-tag__close el-icon-close" @click.stop="actionRemove(index)" />
          </span>
        </div>

        <!-- 输入框后侧图标 -->
        <span class="el-input__suffix">
          <span class="el-input__suffix-inner">
            <i
              class="el-input__icon location-icon"
              :class="isArrayMode ? 'el-icon-add-location' : 'el-icon-location-information'"
            />
            <i
              v-if="selected_location.length"
              class="el-input__icon el-icon-error clear-icon"
              @click.stop="actionRemoveAll"
            />
          </span>
        </span>
      </div>
    </div>

    <!-- Location Pick 窗口 -->
    <el-dialog :append-to-body="true"
               :visible.sync="displayDialog"
               title="Location selector"
               width="660px">
      <div v-if="selected_location.length" class="mb-8">
        <span class="info fs-12">Selected:</span>
        <el-tag
          v-for="(item, index) in selected_location"
          :key="item ? item.id : index"
          class="mr-8 mb-8"
          closable
          @close="actionRemove(index)"
        >
          <span v-if="item">{{ item.name }}</span>
        </el-tag>
      </div>
      <el-select ref="quickSearch"
                 v-model="quick_value"
                 :default-first-option="true"
                 :remote-method="quickSearch"
                 filterable
                 placeholder="Quick Search"
                 remote
                 style="width: 100%;"
                 @change="actionAdd(quick_value)">
        <el-option v-for="item in quick_options"
                   :key="item.id"
                   :label="item.name"
                   :value="item">
          {{item.name}}
          <span v-if="item.country" class="info"> ({{item.country.name}})</span>
        </el-option>
      </el-select>
      <div class="breadcrumb-container">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item><a @click="actionExpand('all')">World</a></el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in label_list" :key="item.id">
            <a @click="label(item)">{{ item.name }}</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="countries-container">
        <el-row>
          <el-col v-if="!list.length">
            <div class="text-center" style="margin-top: 25px;">No Data</div>
          </el-col>
          <el-col v-for="item in list"
                  :key="item.id"
                  :span="6">
            <el-button v-if="!city"
                       size="small"
                       type="text"
                       @click="actionExpand(item)">
              <svg-icon icon-class="location-plus" />
            </el-button>
            <el-button type="text"
                       class="text-truncate country"
                       size="small"
                       @click="itemClick(item)">
              {{ item.name }}
            </el-button>
          </el-col>
        </el-row>
      </div>

      <template v-if="region && list.length && !list[0].parent_id">
        <div class="breadcrumb-container">
          <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item>Region</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <el-row>
          <el-col
            v-for="item in region_list"
            :key="item.name"
            :span="6"
          >
            <el-button
              type="text"
              class="text-truncate country"
              size="small"
              @click="selectRegion(item)"
            >
              {{ item.name }}
            </el-button>
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'Location',
  model: {
    prop: 'model',
    event: 'change',
  },
  props: {
    /* 精确度: 仅城市 */
    city: {
      type: Boolean,
      default: false,
    },
    model: {
      type: [Number, Array, undefined],
      default: undefined,
    },
    placeholder: {
      type: String,
      default: 'Location',
    },
    region: Boolean,
  },
  data() {
    return {
      isArrayMode: false, // 是否为多选
      isLoading: false, // 数据加载中
      selected_location: [],
      label_list: [],
      list: [],
      all_country_list: [],
      continent_list: [],
      region_list: [],
      country_list: [],
      quick_value: undefined,
      quick_options: [],
      displayDialog: false,
    }
  },
  computed: {},
  watch: {
    'model': {
      handler() {
        this.initModel()
      },
    },
  },
  mounted() {
    /* 单选/多选 */
    this.setEditorMode()
    /* 加载 Location 数据 */
    this.loadLocationData()
      .then(this.initModel)
  },
  methods: {
    /**
     * 如传入 model 为数组则应提供多选 <location>
     */
    setEditorMode() {
      this.isArrayMode = Array.isArray(this.model)
    },

    loadLocationData() {
      this.isLoading = true
      return this.$store
        .dispatch('options/getAllCountryList')
        .then(data => {
          this.all_country_list = data
          return data
        })
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    initModel() {
      this.selected_location = []

      /* 忽略无初始值 */
      if (!this.model) {
        return
      }

      if (this.isArrayMode) {
        this.selected_location = this.model.map(this.getLocationById)
      } else {
        const location = this.getLocationById(this.model)
        location && this.selected_location.push(location)
      }
    },

    getLocationById(location_id) {
      return this.all_country_list.find(item => item.id === location_id)
    },

    emit() {
      const location_id_list = this.selected_location.map(item => item.id)
      let output = location_id_list

      if (!this.isArrayMode) {
        if (location_id_list.length) {
          output = location_id_list[0]
        } else {
          output = undefined
        }
      }

      this.$emit('change', output)
    },

    actionAdd(location) {
      /* 忽略重复选择 */
      if (!location || this.isRepeat(location)) return this.actionCloseDialog()

      if (this.isArrayMode) {
        this.selected_location.push(location)
      } else {
        this.selected_location[0] = location
      }

      this.emit()
      this.actionCloseDialog()
    },

    actionRemove(index) {
      this.selected_location.splice(index, 1)
      this.emit()
    },

    actionRemoveAll() {
      this.selected_location = []
      this.emit()
    },

    label(location) {
      this.label_list = this.label_list.filter((item, index) => {
        return index < this.label_list.indexOf(location)
      })
      this.actionExpand(location)
    },

    actionShowDialog() {
      this.resetDialogData()
      this.initDialogData()
      this.displayDialog = true
      this.$nextTick(() => {
        this.$refs.quickSearch.focus()
      })
    },

    actionCloseDialog() {
      this.displayDialog = false
    },

    initDialogData() {
      const continent = []
      const country = []
      this.region_list = []

      this.all_country_list.forEach(item => {
        if (item.parent_id === 0) {
          continent.push(item)
        }
        if (item.level === 2) {
          country.push(item)
        }
        if (item.region_tag) {
          const section_options = [
            { label: 'South East Asia', value: 'SOUTH_EAST_ASIA' },
            { label: 'Middle East', value: 'MIDDLE_EAST' },
          ]
          const name = section_options.find(obj => obj.value === item.region_tag)?.label
          const region = this.region_list.find(region => region?.region_tag === item.region_tag)
          if (!region) {
            this.region_list.push({
              name,
              region_tag: item.region_tag,
              country_list: [item],
            })
          } else {
            region.country_list.push(item)
          }
        }
      })
      this.continent_list = Array.from(new Set(continent)).map(item => ({ name: item.name, id: item.id }))
      this.country_list = _.groupBy(country, 'name')

      /* 初始展开至美国 */
      this.list = _.sortBy(
        this.all_country_list.filter(item => item.parent_id === 174),
        'name',
      )

      this.label_list = [
        { name: 'North America', id: 6 },
        { name: 'United States', id: 174 },
      ]
    },

    actionExpand(location) {
      if (location === 'all') {
        this.label_list = []
        this.list = this.continent_list
      } else if (typeof location.id === 'string') {
        this.label_list.push(location)
        this.list = _.sortBy(this.country_list[location.name], 'name')
      } else {
        this.label_list.push(location)
        const result = _.sortBy(
          this.all_country_list.filter(item => item.parent_id === location.id),
          'name',
        )
        if (!result.length) {
          this.actionAdd(location)
        } else {
          this.list = result
        }
      }
    },

    isRepeat(location) {
      let flag = false
      this.selected_location.forEach(item => {
        if (item.id === location.id) {
          flag = true
        }
      })
      return flag
    },

    quickSearch(query) {
      let output = []

      if (!query) {
        output = this.all_country_list.slice(0, 10)
      } else {
        const input_name = query.toLowerCase()
        let count = 0

        for (let i = 0; i < this.all_country_list.length; i++) {
          const isReachLimit = count > 10

          if (isReachLimit) {
            break
          }

          const item = this.all_country_list[i]
          const item_name = item.name.toLowerCase()
          const is_matched = item_name.indexOf(input_name) > -1

          if (is_matched) {
            count++
            output.push(item)
          }
        }
      }

      this.quick_options = output
      return output
    },

    itemClick(item) {
      this.city ? this.actionExpand(item) : this.actionSelect(item)
    },

    actionSelect(item) {
      this.actionAdd(item)
    },

    resetDialogData() {
      this.quick_value = undefined
      this.quick_options = []
      this.label_list = []
    },

    selectRegion(region) {
      /* 忽略重复选择 */
      if (!region || this.isRepeat(region)) return this.actionCloseDialog()

      if (this.isArrayMode) {
        this.selected_location = [region]
      } else {
        this.selected_location[0] = region
      }

      const country_list = region.country_list
      const location_id_list = country_list.map(item => item.id)
      let output = location_id_list

      if (!this.isArrayMode) {
        if (location_id_list.length) {
          output = location_id_list.join()
        } else {
          output = undefined
        }
      }

      this.$emit('change', output)
      this.actionCloseDialog()
    },
  },
}
</script>

<style lang="scss" scoped>
.location {
  .location-select {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    vertical-align: middle;

    .location-icon {
      display: inline-block;
    }

    .clear-icon {
      display: none;
    }

    &:hover {
      .location-icon {
        display: none;
      }

      .clear-icon {
        display: inline-block;
      }
    }
  }

  .location-placeholder {
    display: block;
    width: 100%;
    height: 100%;
    font-family: sans-serif;
    font-size: 100%;
    color: #C3C7CF;
  }

  .location-option {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-top: 1px;
    height: auto;
  }

  .el-input__inner {
    line-height: 26px;
    padding:0 15px;
  }
}

::v-deep .el-button {
  font-size: 12px;
}

::v-deep .el-breadcrumb {
  font-size: 12px;
}

.country {
  max-width: 130px;
  margin-left: 0;
}

.el-tag--mini .el-tag__close.el-icon-close {
  right: -2px;
}

.breadcrumb-container {
  margin: 15px 0 8px;
  background-color: #f0f0f0;
  padding: 9px 10px;
  border-radius: 4px;
}

.countries-container {
  margin: 0 8px;
}
</style>
