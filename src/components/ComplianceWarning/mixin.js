import ClientAPI from '@/api/client'

export default {
  props: {
    typeRule: {
      type: Object,
      default: () => {
        return {}
      },
    },
    showTitle: {
      type: Boolean,
      default: false,
    },
    clientId: [Number, String],
    clientAm: String,
    showEmptyTip: true,
  },
  data() {
    return {
      loading: false,
      current_rules: null,
      current_custom_rules: [],
      common_rules: [],
      clientRule: undefined,
      client_am: '',
    }
  },
  watch: {
    'clientId': {
      handler(newVal) {
        if (newVal) {
          this.getClientRules()
        }
      }, immediate: true,
    },
    'typeRule': {
      handler(newVal) {
        if (newVal) {
          this.selectRules()
        }
      }, immediate: false,
    },
    'current_rules': {
      handler(newVal) {
        if (newVal) {
          this.formatRules()
        }
      }, immediate: true,
    },
  },
  mounted() {

  },
  methods: {
    getClientRules() {
      this.loading = true
      this.clientRule = undefined
      return ClientAPI.getCompliancePreference(this.clientId)
        .then(data => {
          const compliance_preference = data['compliance_preference']
          data.account_managers.length
            ? this.client_am = data.account_managers.map(item => item.user.name)
              .join(', ')
            : ''

          if (compliance_preference && compliance_preference.rule) {
            const compliance_rules = compliance_preference.rule.compliance_rules

            if (compliance_rules) {
              this.clientRule = compliance_rules
            }
          }
        }, error => {
          this.$message({
            showClose: true,
            message: error,
            type: 'error',
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    selectRules() {
      this.current_rules = this.typeRule[this.rule_type]
      this.current_custom_rules = []
      this.common_rules = []
      this.formatCustomRule()
      this.formatCapvisionCopyRule()
    },

    formatCapvisionCopyRule() {
      const internal_email_rule = this.typeRule.internal_email_copy_rules
      const am = this.client_am || this.clientAm
      if (!am) return
      if (internal_email_rule.all_emails) {
        let internal_people
        if (internal_email_rule.am) {
          internal_people = ' AM [ ' + am + ' ] '
          internal_email_rule.compliance_team
            ? internal_people += 'and Capvision Compliance team '
            : ''
        } else {
          internal_email_rule.compliance_team
            ? internal_people = 'Capvision Compliance team '
            : ''
        }

        if (internal_people) {
          this.common_rules.push({
            type: 'internal_all_emails',
            note: 'Copy the' + internal_people + 'on all emails.',
          })

          this.current_custom_rules.push({
            where: 'internal_all_emails',
            body: 'Copy the' + internal_people + 'on all emails.',
          })

          if (this.rule_type === 'internal_email_copy_rules') {
            this.tips = [
              {
                type: 'internal_all_emails',
                note: 'Copy the' + internal_people + 'on all emails.',
              }]
            this.$emit('update:hasRules', true)
          }
        }
      }
    },

    formatCustomRule() {
      if (!this.typeRule.rule) return
      const custom_rules = this.typeRule.rule.list
      if (custom_rules.length) {
        const options = [
          { value: 'General Rule', label: 'General Rule' },
          { value: 'expert_restrictions', label: 'Restrictions' },
          { value: 'recommend_email', label: 'Recommend' },
          { value: 'approval', label: 'Approval' },
          { value: 'call_scheduling_emails', label: 'Scheduling' },
          { value: 'arrange_email', label: 'Arrange' },
          { value: 'post_call_process', label: 'Post-call' },
        ]
        custom_rules.forEach(item => {
          if (!item) return
          const has_item = options.filter(opt => opt.label === item.where)
          if (has_item.length) {
            item.where = has_item[0].value
          }
        })
      }
      custom_rules.forEach(item => {
        if (!item) return
        if (item.where === this.rule_type) this.current_custom_rules.push(item)
      })
    },

  },
}
