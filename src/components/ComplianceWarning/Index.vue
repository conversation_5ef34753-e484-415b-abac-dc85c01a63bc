<template>
  <div>
    <div
      v-show="has_rule && clientRule"
      v-loading="loading"
      class="compliance-warning-alert mb-8"
    >
      <span class="alert-icon"><b>COMPLIANCE RULES</b></span>
      <div class="mt-4">
        <pre-call-ca v-if="type === 'approval'" :type-rule="clientRule" :client-am="client_am" />
        <Approval v-if="type === 'approval'" :type-rule="clientRule" :has-rules.sync="has_rule" :client-am="client_am" />
        <Arrange v-if="type === 'arrange'" :type-rule="clientRule" :has-rules.sync="has_rule" :client-am="client_am" />
        <calendar-invite v-if="type === 'arrange'" :type-rule="clientRule" :has-rules.sync="has_rule" :client-am="client_am" />
        <Recommend v-if="type === 'recommend'" :type-rule="clientRule" :has-rules.sync="has_rule" :client-am="client_am" />
        <project-creation v-if="type === 'projectCreate'" :type-rule="clientRule" :has-rules.sync="has_rule" />
        <call-scheduling v-if="type === 'AG'" :type-rule="clientRule" :has-rules.sync="has_rule" :client-am="client_am" />
        <post-call v-if="type === 'postCall'" :type-rule="clientRule" :has-rules.sync="has_rule" />
      </div>
      <div v-if="type === 'expert'">
        <expert-restriction :type-rule="clientRule" :has-rules.sync="has_rule" />
      </div>
    </div>

  </div>
</template>

<script>
import Approval from './components/Approval'
import Arrange from './components/Arrange'
import Recommend from './components/RecommendEmail'
import ProjectCreation from './components/ProjectCreation'
import PreCallCa from './components/PreCallCA'
import CalendarInvite from './components/CalendarInvite'
import CallScheduling from './components/CallSchedulingEmail'
import ExpertRestriction from './components/ExpertRestriction'
import PostCall from './components/PostCall'
import Mixin from './mixin'

export default {
  name: 'ClientComplianceIndex',
  components: {
    Approval,
    Arrange,
    Recommend,
    ProjectCreation,
    PreCallCa,
    CalendarInvite,
    ExpertRestriction,
    CallScheduling,
    PostCall,
  },
  mixins: [Mixin],
  props: {
    type: String,
  },
  data() {
    return {
      has_rule: false,
    }
  },

  watch: {
    'type': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.getClientRules()
        }
      }, immediate: false,
    },
  },
}
</script>

<style scoped lang="scss">
  .alert-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: auto;
    margin: 0;
    padding: 5px 20px;
    background: #ffeb3b;
    clip-path: polygon(7px 0, 100% 0, 100% calc(100% - 7px),
      calc(100% - 7px) 100%, 0 100%,
      0 7px
    );
    color: #dc6510;
    font-size: 12px;
  }

  .compliance-warning-alert {
    position: relative;
    padding: 2em 1em 0.5em 2em;
    background: #edeef5cf;
    -webkit-clip-path: polygon(10px 0, 100% 0, 100% calc(100% - 10px),
      calc(100% - 10px) 100%, 0 100%,
      0 10px
    );
    clip-path: polygon(10px 0, 100% 0, 100% calc(100% - 10px),
      calc(100% - 10px) 100%, 0 100%,
      0 10px
    );

    ::v-deep ul {
      margin-bottom: 0;
      margin-top: 0;
      padding-left: 1.2em;
      list-style-type: '- ';
      font-size: 12px;
      line-height: 16px;

      li {
        word-break: break-word;
      }

      ul {
        padding-left: 2em;
        list-style-type: circle;
      }
    }
  }

  .mt-4 {
    margin-top: 4px;
  }
</style>
