<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Research Reports/Surveys (Custom)</div>
      <ul v-if="tips">
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'ResearchReportsSurveys',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'research_reports_surveys',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (rules.approval_required_for_the_proposal.master_switch) {
        const proposal_rules = rules.approval_required_for_the_proposal
        if (proposal_rules.has_any_requirements_except_to_attach_the_proposal && proposal_rules.any_requirements_except_to_attach_the_proposal_detail) {
          result_list.push({
            type: 'proposal_requirements_expects',
            note: proposal_rules.any_requirements_except_to_attach_the_proposal_detail,
          })
        }

        this.formatCommon(proposal_rules, result_list, 'proposal')
      }

      if (rules.approval_required_for_the_questionnaire.master_switch) {
        const questionnaire_rules = rules.approval_required_for_the_questionnaire

        if (questionnaire_rules.is_always_required_just_if_capvision_writes_it && questionnaire_rules.always_required_just_if_capvision_writes_it_detail) {
          result_list.push({
            type: 'questionnaire_require',
            note: questionnaire_rules.always_required_just_if_capvision_writes_it_detail,
          })
        }

        this.formatCommon(questionnaire_rules, result_list, 'questionnaire')
      }

      if (rules.approval_required_for_the_deliverable.master_switch) {
        const deliverable_rules = rules.approval_required_for_the_deliverable

        this.formatCommon(deliverable_rules, result_list, 'deliverable')
      }

      if (rules.when_sending_to_the_user_copy_compliance_team_or_others.master_switch) {
        const sub_rule = rules.when_sending_to_the_user_copy_compliance_team_or_others
        if (sub_rule.has_emails_to_copy && sub_rule.emails_to_copy_detail) {
          result_list.push({
            type: 'report_survey_copy',
            note: sub_rule.emails_to_copy_detail,
          })
        }

        if (sub_rule.has_internal_emails_to_copy && sub_rule.internal_emails_to_copy_detail) {
          result_list.push({
            type: 'report_survey_internal_copy',
            note: sub_rule.internal_emails_to_copy_detail,
          })
        }
      }
      this.tips = result_list
    },

    formatCommon(rule, result_list, type) {
      if (rule.is_capvision_compliance_approval_first) {
        result_list.push({
          type: type + '_capvision_first',
          note: 'The ' + type + ' requires Capvision compliance approval first.',
        })
      }

      if (rule.is_who_to_ask_for_approval && rule.who_to_ask_for_approval_detail) {
        result_list.push({
          type: type + '_ask_for_approval',
          note: 'The ' + type + ' requires ' + rule.who_to_ask_for_approval_detail + '  to ask for approval.',
        })
      }

      if (rule.rm_sends_or_capvision_compliance_sends) {
        result_list.push({
          type: type + '_sends',
          note: rule.rm_sends_or_capvision_compliance_sends + ' sends ' + type,
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
