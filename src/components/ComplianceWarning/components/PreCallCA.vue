<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">(Pre-call) CA Usage/Expiration Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'PreCallCA',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'pre_call_ca_usage',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []
      if (!rules.master_switch) return
      if (rules.has_ca_valid_once_answered) {
        let note
        if (rules.ca_valid_once_answered === 'For the project') {
          note = 'Once answered by the expert, the CA is valid until it is used (for example, if a call is delayed).'
        } else {
          note = 'Once answered by the expert, the CA is valid for ' + rules.ca_valid_once_answered_time_period_count + ' ' + rules.ca_valid_once_answered_time_period_type + ' and then it expires.If the call is delayed, and the CA has expired, the expert must re-answer before the call can happen.'
        }
        result_list.push({ type: 'pre_call_ca_valid_once', note })
      }

      if (rules.has_ca_valid_follow_up) {
        let note
        switch (rules.ca_valid_follow_up) {
          case 'One call only':
            note = 'The CA is valid for one call only. Any follow up requires a new CA.'
            break
          case 'For the project':
            note = 'The CA is valid for the project and can be reused for follow up calls on the SAME PROJECT.'
            break
          case 'Time period':
            note = 'The CA is valid for ' + rules.ca_valid_follow_up_time_period_count + ' ' + rules.ca_valid_follow_up_time_period_type + '. Any follow up calls within that period can reuse the CA.'
            break
        }
        result_list.push({ type: 'pre_call_ca_valid_follow_up', note })
      }

      this.tips = result_list
    },
  },
}
</script>

<style scoped>

</style>
