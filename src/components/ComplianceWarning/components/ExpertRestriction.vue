<template>
  <div>
    <div v-if="tips.length || current_custom_rules.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Expert Restrictions</div>
      <ul>
        <template v-if="tips.length">
          <li v-for="(item, index) in tips" :key="index">
            {{ item.note }}
          </li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
    <span
      v-if="!tips.length && !current_custom_rules.length && !showTitle && showEmptyTip">The client has no rules</span>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'ExpertRestriction',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'expert_restrictions',
    }
  },

  methods: {

    formatRules() {
      const rules = this.current_rules
      const result_list = []

      // listed rule
      if (rules.listed_company.master_switch) {
        const listed_rules = rules.listed_company
        result_list.push({
          type: 'listed_allowed',
          note: 'This client ' + listed_rules.listed_allowed + ' calls with Listed Company Employees.',
        })

        if (listed_rules.listed_allowed !== 'ALLOWS') {
          if (listed_rules.has_geographic_restrictions) {
            result_list.push({
              type: 'geographic_restrictions',
              note: listed_rules.geographic_restrictions_detail,
            })
          }

          if (listed_rules.has_former_employee_window) {
            result_list.push({
              type: 'listed_former_employee',
              note: 'Former employees of a listed company from the last ' + listed_rules.former_employee_window_detail +
                ' months are considered listed company employees.',
            })
          }

          if (listed_rules.has_ipos) {
            result_list.push({
              type: 'has_ipos',
              note: 'Any company with an IPO in the next ' + listed_rules.ipos_detail +
                ' months is considered a listed company.',
            })
          }

          if (listed_rules.definition.length) {
            result_list.push({
              type: 'listed_definition',
              note: 'For this client, they also consider a company listed if any of the following are listed: [' +
                listed_rules.definition.join() + ']',
            })
          }

          if (listed_rules.can_consultant_to_a_listed_company) {
            result_list.push({
              type: 'consultant_to_listed',
              note: 'Experts that are consultants to a listed company (or have been recently, just like former employees) are considered employees of the listed company. (This does not mean expert network consulting, but "real" consulting)',
            })
          }
        }
      }

      // government rule

      if (rules.government_affiliated_employees.master_switch) {
        const government_rules = rules.government_affiliated_employees
        if (government_rules.allowed !== 'ALLOWS') {
          result_list.push({
            type: 'government_allowed',
            note: 'This client ' + government_rules.allowed + ' calls with government affiliated experts.',
          })
        } else {
          if (!(government_rules.has_former_employee_window || government_rules.soe.master_switch ||
            government_rules.public_hospital.master_switch || government_rules.public_university.master_switch ||
            government_rules.other.master_switch)) {
            result_list.push({
              type: 'government_allowed',
              note: 'This client ' + government_rules.allowed + ' calls with government affiliated experts.',
            })
          }
        }

        if (government_rules.has_former_employee_window) {
          result_list.push({
            type: 'government_former_employee',
            note: 'Former employees of a government affiliated employer from the last ' +
              government_rules.former_employee_window_detail +
              ' months are considered government affiliated employees.',
          })
        }

        if (government_rules.soe.master_switch) {
          this.formatCommonTypeRules(government_rules.soe, 'soe', result_list, 'SOE employees',
            'an SOE', 'SOE employees', 'SOEs')
        }
        if (government_rules.public_hospital.master_switch) {
          this.formatCommonTypeRules(government_rules.public_hospital, 'public_hospital', result_list,
            'Public Hospital employees', 'a public hospital', 'public hospital', 'public hospitals')
        }
        if (government_rules.public_university.master_switch) {
          this.formatCommonTypeRules(government_rules.public_university, 'public_university', result_list,
            'Public University employees', 'a public university', 'public university', 'public universities')
        }
        if (government_rules.other.master_switch) {
          this.formatCommonTypeRules(government_rules.other, 'other', result_list,
            'other government-affiliated institution employees (such as research institutes or industry associations)',
            'other government affiliated', '', 'any other government affiliated')
        }
      }

      // Franchinsee/Distributor/Supplier
      if (rules.franchisee_distributor_supplier.master_switch) {
        result_list.push({
          type: 'franchisee_distributor_supplier',
          note: 'The client considers any expert employer that is a Franchisee, Distributor, or Supplier with at least' +
            rules.franchisee_distributor_supplier.relevance_threshold_percentage_detail +
            ' percent of a listed company\'s business as part of that listed company for compliance purposes.',
        })
      }

      // Healthcare Experts/Clinical Trials
      if (rules.healthcare_experts_clinical_trials.master_switch) {
        const sub_rules = rules.healthcare_experts_clinical_trials
        let restriction_note
        let clinical_note
        switch (sub_rules.clinical_trials_restriction) {
          case 'Current trials':
            restriction_note = 'current'
            break
          case 'Completed but not published trials':
            restriction_note = 'completed but not yet published'
            break
          case 'Both':
            restriction_note = 'current or completed but not published'
            break
        }
        switch (sub_rules.clinical_trials) {
          case 'Not allowed':
            clinical_note = 'cannot do a consultation.'
            break
          case 'Not allowed if relevant to project topic':
            clinical_note = 'cannot do a consultation if the trial is relevant to the project topic.'
            break
          case 'Requires special approval':
            clinical_note = 'requires special approval from the client.'
            break
        }

        result_list.push({
          type: 'clinical_trials_restriction',
          note: `Any expert participating in a ${restriction_note} clinical trial ${clinical_note}`,
        })

        if (sub_rules.can_doctors || sub_rules.can_other_medical_professionals ||
          sub_rules.can_medical_tech_services_pharma_etc) {
          const user_list = []
          sub_rules.can_doctors ? user_list.push('Doctors') : ''
          sub_rules.can_other_medical_professionals ? user_list.push('Other medical professionals like nurses') : ''
          sub_rules.can_medical_tech_services_pharma_etc
            ? user_list.push('Medical tech or Pharma company employees')
            : ''
          result_list.push({
            type: 'healthcare_experts_doctors',
            note: `The following medical professionals cannot do a call for any reason:  ${user_list.join(' or ')}.`,
          })
        }
      }

      // Expert position 待定
      if (rules.expert_position.master_switch) {
        if (rules.expert_position.no_restriction) {
          result_list.push({
            type: 'expert_position_no_restriction',
            note: 'The client has no restriction on expert positions.',
          })
        } else {
          // todo
        }
      }

      // Repeat Experts

      if (rules.repeat_experts.master_switch) {
        const sub_rules = rules.repeat_experts
        result_list.push({
          type: 'repeat_experts_calls',
          note: 'The client does not allow more than ' + sub_rules.restriction_call_number_and_time_period.call_count +
            ' calls in the last ' + sub_rules.restriction_call_number_and_time_period.time + ' ' +
            sub_rules.restriction_call_number_and_time_period.time_type + ' with the same expert by ' +
            sub_rules.firm_wide_or_per_user,
        })

        if (sub_rules.has_approval_to_go_over) {
          let note
          if (sub_rules.approval_to_go_over_type === 'Never') {
            note = 'The client is never allowed to go over this limit, do not ask for special approval.'
          } else {
            note = sub_rules.approval_to_go_over_detail
          }
          result_list.push({
            type: 'repeat_experts_approval_to_go_over',
            note,
          })
        }
      }

      // Investment Target compliance need to rethink
      if (rules.investment_target.master_switch) {
        if (rules.investment_target.is_user_must_give_target) {
          result_list.push({
            type: 'investment_target_must_give_target',
            note: 'Client users are required to give an investment target. You must ask the client user what the investment target is if they have not provided one.',
          })
        }
        if (rules.investment_target.is_banned && rules.investment_target.is_banned_detail) {
          result_list.push({
            type: 'investment_target_banned',
            note: rules.investment_target.is_banned_detail,
          })
        }
        if (rules.investment_target.has_former_employee_window) {
          result_list.push({
            type: 'investment_target_former_employee_window',
            note: `Current and last ${rules.investment_target.former_employee_window_detail} months former employees from investment target are not allowed to do consultations.`,
          })
        }
      }

      // Letter from employer required
      if (rules.letter_from_employer_required.master_switch) {
        const sub_rules_current = rules.letter_from_employer_required.current_employer
        const sub_rules_former = rules.letter_from_employer_required.former_employer
        if (sub_rules_current.master_switch) {
          result_list.push({
            type: 'letter_from_employer_current',
            note: sub_rules_current.conditions +
              ' must get a letter from their current employer in order to do a consultation.',
          })

          if (sub_rules_current.can_self_consent) {
            result_list.push({
              type: 'letter_from_employer_current_self',
              note: sub_rules_current.above_what_level_detail,
            })
          }
        }

        if (sub_rules_former.master_switch) {
          result_list.push({
            type: 'letter_from_employer_former',
            note: 'Experts that have a former employer from the last ' + sub_rules_former.left_in_last_month +
              ' months must get a letter from that former employer saying they can do the consultation.',
          })

          if (sub_rules_former.can_self_consent) {
            result_list.push({
              type: 'letter_from_employer_former_self',
              note: sub_rules_current.above_what_level_detail,
            })
          }
        }
      }

      // Custom restriction list
      if (rules.custom_restriction_list.master_switch) {
        const sub_rules = rules.custom_restriction_list
        result_list.push({
          type: 'custom_list_where',
          note: 'This client does not allow calls with employees of certain companies. This list is available at: ' +
            sub_rules.provided_and_in_our_system,
        })
        if (sub_rules.current_employees_or_formers_too === 'Current') {
          result_list.push({
            type: 'custom_list_employee',
            note: 'Current employees from the list are not allowed to do consultations.',
          })
        } else {
          result_list.push({
            type: 'custom_list_employee',
            note: 'Current and last ' + sub_rules.period_to_include_formers +
              ' month(s) former employees from the list are not allowed to do consultations.',
          })
        }
      }

      this.tips = result_list
      if (result_list.length || this.current_custom_rules.length) this.$emit('update:hasRules', true)
    },

    formatCommonTypeRules(rule, type, result_list, allowed_value) {
      switch (rule.allowed) {
        case 'ALLOWS':
          result_list.push({
            type: 'government_' + type + '_allowed',
            note: `This client is allowed to speak with ${allowed_value} for any reason.`,
          })
          break
        case 'DOES NOT ALLOW':
          result_list.push({
            type: 'government_' + type + '_allowed',
            note: `This client is NOT allowed to speak with ${rule.expert_level} level ${allowed_value}${levelFormat(
              rule.expert_level)} ${formerEmployeeFormat(rule)}`,
          })
          break
        case 'REQUIRES SPECIAL APPROVAL FOR':
          result_list.push({
            type: 'government_' + type + '_allowed',
            note: `This client is allowed to speak with ${rule.expert_level} level ${allowed_value}, however they require special approval.  ${formerEmployeeFormat(
              rule)}`,
          })
          break
      }

      if (rule.detail_text) {
        result_list.push({
          type: 'government_' + type + '_detail',
          note: rule.detail_text,
        })
      }

      function levelFormat(level) {
        if (level === 'All') return '.'
        if (level === 'High') return ',but low level employees are ok.'
        if (level === 'Low') return ',but high level employees are ok.'
      }

      function formerEmployeeFormat(data) {
        if (data.has_former_employee_window) {
          return `This includes ${data.former_employee_window_detail} month former employees.`
        } else {
          return ''
        }
      }
    },

  },
}
</script>

<style scoped>

</style>
