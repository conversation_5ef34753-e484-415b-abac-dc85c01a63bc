<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Transcript/Notetaking Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
          <email-copy v-if="item.list" :email-list="item.list" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'TranscriptNotetaking',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'transcript_notetaking_rules',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []
      if (!rules.master_switch) return
      if (rules.allowed_to_use_chinese_transcript_service) {
        const note = 'This client is allowed to use the (Chinese) note-taking/transcript service.'
        result_list.push({
          type: 'schedule_with',
          note,
        })
      }
      if (rules.allowed && rules.send_to) {
        const who = rules.send_to === 'User' ? 'the user.' : ':'
        const note = 'The transcript/recording should be sent to ' + who
        result_list.push({
          type: 'send_to',
          note,
          list: rules.send_to === 'User' ? undefined : rules.send_to_detail,
        })
      }

      if (rules.is_copy_client_compliance_when_sending) {
        const note = 'Compliance Notes: ' + rules.copy_client_compliance_when_sending_detail
        result_list.push({
          type: 'copy_client_compliance',
          note,
        })

        if (rules.cc_on_all_transcript_recording_emails_to_analysts.length) {
          const note = 'CC on all transcript/recording emails to analysts: ' + rules.cc_on_all_transcript_recording_emails_to_analysts.join()
          result_list.push({
            type: 'copy_client_compliance',
            note,
          })
        }
      }

      if (rules.is_require_compliance_review_of_recordings_and_transcripts_first) {
        const note = `Please confirm that all transcripts and links to recordings are sent to (${rules.require_compliance_review_email_list.join()}) for review and approval prior to being sent to the client analyst.`

        result_list.push({
          type: 'sent_to_client_compliance_for_review',
          note,
        })
      }

      this.tips = result_list

      if (result_list.length) this.$emit('update:hasRules', true)
    },
  },

}
</script>

<style scoped>

</style>
