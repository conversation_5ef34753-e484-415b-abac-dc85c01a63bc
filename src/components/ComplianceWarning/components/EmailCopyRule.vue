<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Email Copying Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
          <email-copy :email-list="item.list" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'EmailCopyRule',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'email_copying_rules',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (rules.all_emails.master_switch) {
        const note = 'Copy the following on ALL EMAILS :'
        result_list.push({ type: 'client_all_emails', note, list: rules.all_emails.list })
      }

      if (rules.negotiating_call_schedule.master_switch) {
        result_list.push({
          type: 'client_negotiating_call_schedule',
          note: 'Copy these people when discussing call schedule :',
          list: rules.negotiating_call_schedule.list,
        })
        // if (this.rule_type === 'call_scheduling_emails') {
        //   result_list.push({
        //     type: 'client_negotiating_call_schedule',
        //     note: 'Copy these people when discussing call schedule :',
        //     list: rules.negotiating_call_schedule.list,
        //   })
        // }
      }

      if (rules.recommending_experts.master_switch) {
        result_list.push({
          type: 'client_recommending_experts',
          note: 'Copy these people on all Recommend Emails :',
          list: rules.recommending_experts.list,
        })
      }
      this.tips = result_list

      if (result_list.length) this.$emit('update:hasRules', true)
    },
  },

}
</script>

<style scoped>

</style>
