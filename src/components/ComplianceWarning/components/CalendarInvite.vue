<template>
  <div>
    <div v-if="ruleShow" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Calendar Invite</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          <span v-if="item.type==='schedule_with'">{{item.note}}</span>
          <span v-else>
            {{item.note}}
            <email-copy v-if="item.list && item.list.length" :email-list="item.list" />
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'
import EmailCopy from '../EmailCopy'

export default {
  name: 'CalendarInvite',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'calendar_invite',
    }
  },
  computed: {
    ruleShow() {
      if (this.tips.length) {
        this.$emit('update:hasRules', true)
        return true
      } else {
        return false
      }
    },
  },
  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (!rules.master_switch) return
      if (rules.is_do_not_send) {
        const note = 'No need to send a calendar invite.'
        result_list.push({
          type: 'schedule_with',
          note,
        })
      } else {
        if (rules.copy_others_list.length) {
          let note
          switch (rules.when_to_copy_others) {
            case 'All':
              note = 'All calendar invites need to be copied to these people :'
              break
            case 'Only that needed approval':
              note = 'Copy these people only if the calendar invite needs to be reviewed :'
              break
            case 'Listed company expert':
              note = 'When the expert is a public company, calendar invite needs to copy these people :'
              break
            case 'Other rule':
              note = rules.when_to_copy_others_detail + ',all calendar invites copy these people :'
              break
          }
          result_list.push({
            type: 'copy_others',
            note,
            list: rules.copy_others_list,
          })
        }
      }
      if (rules.is_include_angle_name_in_subject && rules.include_angle_name_position) {
        result_list.push({
          type: 'include_angle_name_in_subject',
          note: `Angle name will appear at the ${rules.include_angle_name_position} of the subject `,
        })
      }
      this.tips = result_list

      if (result_list.length) this.$emit('update:hasRules', true)
    },
  },

}
</script>

<style scoped>

</style>
