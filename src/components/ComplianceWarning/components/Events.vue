<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Evens</div>
      <ul v-if="tips">
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'Events',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'events',
    }
  },

  methods: {

    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (rules.master_switch) {
        let advertisement_note
        switch (rules.should_they_get_event_advertisements) {
          case 'Yes':
            advertisement_note = 'The client needs active advertising.'
            break
          case 'No':
            advertisement_note = 'The client does not need active advertising'
            break
          case 'Yes with rules':
            if (rules.can_former_period_count) {
              advertisement_note = 'If the keynote speaker is a listed company who has left the company for more than ' + rules.can_former_period_count + ' ' + rules.can_former_period_type +
                ' months, the client wants an event advertisement.'
            }
            if (rules.should_they_get_event_advertisements_rules) {
              advertisement_note += rules.should_they_get_event_advertisements_rules
            }

            break
        }
        result_list.push({
          type: 'get_event_advertisements',
          note: advertisement_note,
        })

        if (rules.cost_approvals.master_switch) {
          const cost_rule = rules.cost_approvals
          if (cost_rule.who_to_ask) {
            result_list.push({
              type: 'event_cost_ask',
              note: cost_rule.who_to_ask,
            })
          }

          if (cost_rule.is_cost_approval_required && cost_rule.cost_approval_required_detail) {
            result_list.push({
              type: 'event_cost_approval',
              note: cost_rule.cost_approval_required_detail,
            })
          }
        }

        if (rules.live_events.master_switch) {
          const live_event = rules.live_events
          if (live_event.can_the_user_attend_live_events) {
            let note = 'The user can participate in '
            switch (live_event.can_the_user_attend_live_events_type) {
              case 'Phone':
                note += 'phone interviews.'
                break
              case 'In-person':
                note = 'in-person interviews.'
                break
              case 'Both':
                note = 'phone and in-person interviews.'
                break
            }
            result_list.push({
              type: 'live_event_user_type',
              note,
            })
          }

          if (live_event.do_they_need_approval_to_attend_live) {
            if (live_event.has_who_to_ask_for_approval && live_event.who_to_ask_for_approval_detail) {
              result_list.push({
                type: 'live_event_user_ask_approval',
                note: live_event.who_to_ask_for_approval_detail,
              })
            }

            if (live_event.has_what_to_send_in_the_approval_request && live_event.what_to_send_in_the_approval_request_detail) {
              result_list.push({
                type: 'live_event_request_send',
                note: live_event.what_to_send_in_the_approval_request_detail,
              })
            }
          }
        }

        if (rules.event_transcripts.master_switch) {
          const transcription_rule = rules.event_transcripts
          if (transcription_rule.can_the_user_request_get_transcripts) {
            result_list.push({
              type: 'use_ask_transcripts',
              note: 'The user can request or get transcripts',
            })
          }

          if (transcription_rule.has_who_to_ask_for_approval) {
            if (transcription_rule.has_who_to_ask_for_approval && transcription_rule.who_to_ask_for_approval_detail) {
              result_list.push({
                type: 'transcripts_approval',
                note: transcription_rule.who_to_ask_for_approval_detail,
              })
            }

            if (transcription_rule.has_what_to_send_in_the_approval_request && transcription_rule.what_to_send_in_the_approval_request_detail) {
              result_list.push({
                type: 'transcripts_request',
                note: transcription_rule.what_to_send_in_the_approval_request_detail,
              })
            }
          }
        }
      }
      this.tips = result_list
    },
  },
}
</script>

<style scoped>

</style>
