<template>
  <div>
    <div v-if="general_rule" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">General Rules</div>
      <ul>
        <li v-for="(item, index) in general_rule" :key="index">
          <div class="custom" v-html="item.body" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'GeneralRule',
  mixins: [Mixin],
  data() {
    return {
      general_rule: null,
      rule_type: 'rule',
    }
  },
  methods: {
    formatRules() {
      const rules = this.current_rules.list.filter(item => item.where === 'General Rule')
      if (rules.length) {
        this.general_rule = rules
      }
    },
  },

}
</script>

<style scoped lang="scss">
</style>
