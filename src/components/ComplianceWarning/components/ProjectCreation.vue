<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Project Creation Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'ProjectCreation',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'project_creation_rules',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []
      if (rules.who_can_start_a_project.master_switch) {
        let note
        const special_user_list = rules.who_can_start_a_project.special_user_detail.length ? rules.who_can_start_a_project.special_user_detail.map(item => item.email)
          .join('; ') : undefined
        if (rules.who_can_start_a_project.users_type === 'Any User - special CC rules') {
          note = 'Projects for this client can be started by any user, however they must copy ' + special_user_list + 'when they request a project. If they don\'t ask them to submit again and copy those emails.'
        } else if (rules.who_can_start_a_project.users_type === 'Special persons only') {
          note = 'Projects for this client can only be started by ' + special_user_list + '.'
        } else {
          note = 'Projects for this client can be started by ' + rules.who_can_start_a_project.users_type + '.'
        }
        result_list.push({ type: 'start_user', note })
      }

      if (rules.is_any_project_code_or_other_similar
      ) {
        const note = rules.any_project_code_or_other_similar_detail
        result_list.push({ type: 'project_code', note })
      }
      if (rules.will_projects_have_a_budget) {
        const note = rules.projects_have_a_budget_detail
        result_list.push({ type: 'project_budget', note })
      }

      if (rules.do_projects_auto_expire__close_after_a_certain_amount_of_time) {
        const note = rules.projects_auto_expire__close_after_a_certain_amount_of_time_detail
        result_list.push({ type: 'auto_close', note })
      }

      this.tips = result_list
      this.$emit('update:hasRules', result_list.length > 0)
    },
  },
}
</script>

<style scoped>

</style>
