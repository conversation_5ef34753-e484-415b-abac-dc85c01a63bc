<template>
  <div>
    <div v-if="ruleShow" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Post Call Process</div>
      <ul>
        <template v-if="tips.length">
          <li v-for="(item, index) in tips" :key="index">
            {{item.note}}
          </li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'PostCallCa',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'post_call_process',
    }
  },
  computed: {
    ruleShow() {
      if (this.tips.length || this.current_custom_rules.length) {
        this.$emit('update:hasRules', true)
        return true
      } else {
        return false
      }
    },
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []
      if (!rules.master_switch) return

      if (rules.has_post_call_ca && rules.post_call_ca_detail) {
        result_list.push({ type: 'post_call_ca', note: rules.post_call_ca_detail + ' (Post Call CA)' })
      }
      if (rules.has_post_call_work_for_client) {
        if (rules.post_call_work_for_client__what_content) {
          result_list.push({
            type: 'post_call_for_client_content',
            note: rules.post_call_work_for_client__what_content,
          })
        }
        if (rules.post_call_work_for_client__anyone_to_copy) {
          result_list.push({
            type: 'post_call_for_client_copy',
            note: rules.post_call_work_for_client__anyone_to_copy,
          })
        }
        if (rules.post_call_work_for_client__what_to_do_with_the_answer) {
          result_list.push({
            type: 'post_call_for_client_answer',
            note: rules.post_call_work_for_client__what_to_do_with_the_answer,
          })
        }
      }
      this.tips = result_list
    },
  },
}
</script>

<style scoped>

</style>
