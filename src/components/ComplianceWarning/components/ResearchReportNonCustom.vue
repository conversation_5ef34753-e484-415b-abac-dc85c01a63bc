<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Research Reports (Non-Custom)</div>
      <ul v-if="tips">
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'ResearchReportNonCustom',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'research_reports',
    }
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (rules.master_switch) {
        result_list.push({
          type: 'report_non_custom_language',
          note: rules.language_rules + ' (non custom)',
        })

        if (rules.is_approval_required_before_delivery) {
          if (rules.is_capvision_compliance_approval_first) {
            result_list.push({
              type: 'report_non_custom_capvision_approval',
              note: 'Research report (non custom) requires Capvision compliance approval first.',
            })
          }

          if (rules.has_who_to_ask_for_approval && rules.who_to_ask_for_approval_detail) {
            result_list.push({
              type: 'report_non_custom_capvision_approval',
              note: 'Research report (non custom) requires ' + rules.who_to_ask_for_approval_detail + '  to ask for approval.',
            })
          }
        }
      }
      this.tips = result_list
    },
  },

}
</script>

<style scoped>

</style>
