<template>
  <div>
    <div v-if="ruleShow" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Arrange</div>
      <ul>
        <template v-if="tips.length">
          <li v-for="(item, index) in tips" :key="index">

            <div v-if="item.type === 'copy_user' && item.list.length">
              {{item.note}} copy to:
              <email-copy v-if="item.list" :email-list="item.list" />
            </div>
            <span v-else>{{item.note}}</span>
          </li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
  </div>

</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'Arrange',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'arrange_email',
    }
  },

  computed: {
    ruleShow() {
      if (this.tips.length || this.current_custom_rules.length) {
        this.$emit('update:hasRules', true)
        return true
      } else {
        return false
      }
    },
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []
      if (!rules.master_switch) return
      if (rules.has_any_special_language_that_needs_to_be_put_in) {
        const note = 'You must put the following in the Arrange email : ' + rules.any_special_language_that_needs_to_be_put_in_detail
        result_list.push({ type: 'special_language', note })
      }

      if (rules.can_custom_template_or_custom_insert_into_our_template) {
        const note = 'Client custom email template : ' + rules.custom_template_or_custom_insert_into_our_template_detail
        result_list.push({ type: 'email_template', note })
      }
      if (rules.is_include_ca_answers_in_the_arrange_email) {
        const note = 'Put the CA answers in the arrange mail.'
        result_list.push({ type: 'ca_answer', note })
      }

      if (rules.is_allowed_to_use_their_own_conf_system) {
        const note = 'Allow client to use their systems : ' + rules.allowed_to_use_their_own_conf_system_detail
        result_list.push({ type: 'client_system', note })
      }

      if (rules.when_to_copy_client_compliance && rules.copy_client_compliance_list.length) {
        let note = ''
        switch (rules.when_to_copy_client_compliance) {
          case 'Only that needed approval':
            note = 'All arrangements need to '
            break
          case 'Listed company expert':
            note = 'If the expert is a listed company expert,'
            break
          case 'Other rule':
            note = rules.when_to_copy_client_compliance + ','
            break
          case 'All':
            note = 'If arrange,'
            break
          default:
            break
        }
        result_list.push({ type: 'copy_user', note, list: rules.copy_client_compliance_list })
      }

      this.tips = result_list
    },
  },
}
</script>

<style scoped lang="scss">

  ::v-deep .el-alert {
    padding: 5px 16px;
  }

</style>
