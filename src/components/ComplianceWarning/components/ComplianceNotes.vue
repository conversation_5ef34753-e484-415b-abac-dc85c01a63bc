<template>
  <div>
    <el-alert
      v-if="displayWarningMessage"
      title="Client Compliance rules"
      type="info"
      show-icon
      :closable="false">
      <ul>
        <li v-for="(item, index) in data" :key="index">{{item}}</li>
      </ul>
    </el-alert>
  </div>
</template>

<script>
export default {
  name: 'ComplianceNotes',
  props: {
    rules: Object,
    type: String,
  },
  data() {
    return {
      data: undefined,
    }
  },
  computed: {
    displayWarningMessage() {
      return Array.isArray(this.data) && (this.data.length > 0)
    },
  },
  mounted() {
    this.loadData()
    this.expandData()
  },
  methods: {
    loadData() {
      const rules = this.rules[this.type]

      const translator = {
        'has_custom_language_need_to_include_in_the_recommend_email': '邮件中需要包含指定语言: {recommend_email.custom_language_need_to_include_in_the_recommend_email}',
        'custom_language_need_to_include_in_the_recommend_email': 'Korean',
        'is_label_experts': '专家标准',
        'has_special_email_subject_rules': '需要特殊邮件主题，规则为：{recommend_email.special_email_subject_rules}',
        'special_email_subject_rules': 'YYYY-MM-DD 项目名 KM名',
      }

      this.data = translateRules(rules, translator)

      function translateRules(rules, translator) {
        const rule_message_list = []
        const keys = Object.keys(translator)

        keys.forEach(key => {
          if (Object.hasOwnProperty.call(rules, key)) {
            if (hasValue(rules[key])) {
              console.log(`${key} ${rules[key]} - ${translator[key]}`)
              rule_message_list.push(`${rules[key]} - ${translator[key]}`)
            }
          }
        })

        return rule_message_list

        function hasValue(value) {
          if (Array.isArray(value)) {
            return value.length > 0
          } else {
            return ![undefined, false].includes(value)
          }
        }
      }
    },
    expandData() {
      const list = []

      trySolve(this.rules, '', list)

      function trySolve(rule, path, list) {
        Object.keys(rule).forEach(key => {
          if (typeof rule[key] === 'object') {
            trySolve(rule[key], `${path}.${key}`, list)
          } else {
            list.push(path ? `${path}.${key}` : key)
          }
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
