<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Internal Email Copy Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'InternalCopy',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'internal_email_copy_rules',
    }
  },
  methods: {
    formatRules() {
      this.formatCapvisionCopyRule()
    },
  },
}
</script>

<style scoped>

</style>
