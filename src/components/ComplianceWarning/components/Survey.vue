<template>
  <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
    <div v-if="showTitle" class="title">Survey</div>
    <ul v-if="tips">
      <li v-for="(item, index) in tips" :key="index">
        {{ item.note }}
      </li>
    </ul>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'Survey',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'survey_rules',
    }
  },
  methods: {

    formatRules() {
      const rules = this.current_rules

      if (rules.master_switch) {
        this.tips.push(
          {
            type: 'text',
            note: rules.text,
          })
      }
    },
  },
}
</script>

<style scoped>

</style>
