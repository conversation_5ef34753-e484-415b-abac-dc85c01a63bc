<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Communication Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'Communication',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'communication_rules',
    }
  },
  methods: {
    formatRules() {
      const rules = this.current_rules
      let note = null
      if (rules.email_only) {
        note = 'You may only communicated with this client via EMAIL. No phone calls, no other methods (WeChat, etc.).'
      } else {
        if (rules.phone_allowed) {
          note = rules.we_chat_allowed ? 'This client allows all forms of communication (email, phone, WeChat, etc.), however you must still use the system when required (recommending experts, etc.).' : 'The client allows phone calls and email, but no other communications (WeChat, etc.).'
        }
      }
      if (note) {
        this.tips = [{
          type: 'communication_type',
          note,
        }]
      }

      if (this.tips.length) this.$emit('update:hasRules', true)
    },
  },

}
</script>

<style scoped>

</style>
