<template>
  <div>
    <div v-if="ruleShow" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Approval</div>
      <ul>
        <template v-if="tips.length">
          <li v-for="(item, index) in tips" :key="index">

            <div v-if="isSpecialRule(item)">

              <div v-if="item.type === 'write_after_call'">
                <template v-for="i in item.list">{{ i }}</template>
              </div>
              <div v-else>
                {{ item.note }}
                <email-copy v-if="item.list.length" :email-list="item.list" />
              </div>
            </div>

            <span v-else>{{ item.note }}</span>

          </li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'Approval',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'approval',
      is_client_approval: false,
    }
  },
  computed: {
    ruleShow() {
      if (this.tips.length) {
        this.$emit('update:hasRules', true)
        return true
      } else {
        return false
      }
    },
  },

  methods: {
    isSpecialRule(item) {
      return ['send_approval_list', 'write_after_call', 'in_person_meetings', 'can_approve_list'].includes(item.type)
    },

    formatRules() {
      this.is_client_approval = this.current_rules.has_calls_for_client_to_approve !== false
      const rules = this.current_rules
      const result_list = []
      if (!rules.master_switch) return
      if (rules.master_switch && rules.has_capvision_approval) {
        const note = 'Require Capvision approval'
        result_list.push({ type: 'capvision_approval', note })
      }
      if (!this.is_client_approval) {
        this.tips = result_list
        return
      }

      if (rules.approval_method === 'Email') {
        result_list.push({ type: 'approval_method', note: 'This client approves calls using Email.' })

        result_list.push({
          type: 'approval_method_email_send',
          note: 'Approval emails are sent to client compliance by ' + rules.who_send + '.',
        })
      } else {
        result_list.push({ type: 'approval_method', note: 'This client approves calls on the Portal.' })
      }
      if (rules.is_client_have_portal_access) {
        result_list.push({
          type: 'client+portal_access',
          note: 'Although the client approves by email, they also have portal access and can see the data from the DB. You must have the data filled out in the DB correctly as the client can see it in both your approval email and the portal.',
        })
      }

      if (rules.names_and_emails_to_send_approval_to.length) {
        const note = 'Email approval requests are sent to : '
        result_list.push({ type: 'send_approval_list', note, list: rules.names_and_emails_to_send_approval_to })
      }

      if (rules.who_can_approval === 'Only certain people') {
        const note = 'Only these people from client compliance can approve an expert: '
        result_list.push({ type: 'can_approve_list', note, list: rules.names_and_emails_to_approval })
      }
      if (rules.who_can_approval === 'Anyone that is sent approval email') {
        const note = 'Anyone from client compliance that is copied can approve the expert.'
        result_list.push({ type: 'can_approve_list', note, list: [] })
      }

      if (rules.has_calls_for_client_to_approve) {
        let note
        switch (rules.calls_for_client_to_approve_type) {
          case 'Conditional':
            if (rules.client_conditional_approve_type === 'Based on CA') {
              note = 'Client approves calls based on the CA answers.' +
                rules.client_conditional_approve_based_on_ca_detail
            }
            if (rules.client_conditional_approve_type === 'Listed company experts') {
              note = 'Client approves calls with listed company experts (including former employees or others, if relevant).'
            }
            if (rules.client_conditional_approve_type === 'Other') {
              note = rules.client_conditional_approve_others_detail
            }
            break
          case 'All':
            note = 'Client approves ALL CALLS.'
            break
          case 'Internal to Client':
            note = rules.internal_to_client_detail || 'The client themselves contact the compliance review.'
            break
        }
        result_list.push({ type: 'call_client_approve', note })
      }

      if (rules.has_in_person_meetings) {
        let note = ''
        let list = []
        switch (rules.in_person_meetings_type) {
          case 'Banned':
            note = 'Client DOES NOT ALLOW in-person consultations.'
            break
          case 'Follow same call approval rules':
            note = 'For in-person meetings, follow the same approval rules as calls.'
            break
          case 'Special approval rules':
            note = 'For in-person meetings, there are special approval rules: ' + rules.in_person_meetings_type_detail
            break
          case 'Requires approval regardless of call approval rules':
            note = 'Meeting in person must be approved by the list :'
            list = rules.in_person_meetings_approval_list
            break
          default:
            break
        }
        result_list.push({ type: 'in_person_meetings', note, list })
      }

      if (rules.has_include_details_on_previous_calls_by_expert) {
        let note
        if (rules.has_calls_by_expert_with_all_clients) {
          note = 'Your approval request must include the total number of calls by this expert with all clients in the last ' +
            rules.all_clients_relevant_time_period_month + ' months.'
          result_list.push({ type: 'all_clients_calls', note })
        }
        if (rules.has_calls_by_expert_with_this_client_only) {
          note = 'Your approval request must include the total number of calls by this expert with this client (all users) in the last ' +
            rules.client_only_relevant_time_period_month + ' months.'
          result_list.push({ type: 'only_client_calls', note })
        }
      }

      if (rules.has_written_content) {
        const list = []
        if (rules.is_capvision_compliance_review_first) {
          const content = 'Any written content that needs to be sent from the expert to the client, first send it to Capvision compliance for review.'
          list.push(content)
        }
        if (rules.is_client_compliance_needs_to_pre_approve) {
          let content = 'After Capvision compliance approval: '
          rules.client_compliance_needs_to_pre_approve_detail ? content += '[ ' +
            rules.client_compliance_needs_to_pre_approve_detail + ' ]' : ''
          list.push(content)
        }

        if (rules.is_copy_client_compliance_when_sending_to_user) {
          let content = 'Copy the following people when sending the content to the user:'
          rules.copy_client_compliance_when_sending_to_user_detail ? content += '[ ' +
            rules.copy_client_compliance_when_sending_to_user_detail + ' ]' : ''
          list.push(content)
        }
        list.length ? result_list.push({ type: 'write_after_call', list }) : ''
      }

      if (rules.has_ca_answers_in_the_approval_email) {
        const note = 'If the expert answers the CA via email, you should copy their answers into your approval email so they are faster to read (still attach the CA).'
        result_list.push({ type: 'put_ca_answer', note })
      }

      if (rules.has_internal_copy_rules) {
        let internal_people = null
        const am = this.client_am || this.clientAm
        if (rules.has_internal_copy_rules_am) {
          internal_people = ' AM [ ' + am + ' ] '
          rules.has_internal_copy_rules_capvision_compliance ? internal_people += 'and Capvision Compliance team ' : ''
        } else {
          rules.has_internal_copy_rules_capvision_compliance ? internal_people = 'Capvision Compliance team ' : ''
        }

        if (internal_people) {
          result_list.push({
            type: 'internal_copy', note: 'Copy the' + internal_people + 'on all emails (Approval).',
          })
        }
      }

      if (rules.can_send_ca_and_bio_even_if_they_do_not_require_approval) {
        let note
        if (rules.send_ca_and_bio_even_if_they_do_not_require_approval_to_who === 'Same as approval') {
          note = 'Even if you don\'t need approval, send the CA and expert bio to the client (same people as for approval). Make sure your email clearly says you don\'t need approval and are just sending the CA for their records.'
        } else {
          const mail_list = rules.send_ca_and_bio_even_if_they_do_not_require_approval_special_list.map(
            item => item.email)
            .join('; ') || 'no email'
          note = 'Even if you don\'t need approval, send the CA and expert bio to these people at the client: ' +
            mail_list +
            '. Make sure your email clearly says you don\'t need approval and are just sending the CA for their records.'
        }
        result_list.push({ type: 'send_ca_bio', note })
      }

      this.tips = result_list
    },
  },
}
</script>

<style scoped>

::v-deep .el-alert {
  padding: 5px 16px;
}
</style>
