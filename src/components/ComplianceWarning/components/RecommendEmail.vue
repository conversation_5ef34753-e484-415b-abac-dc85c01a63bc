<template>
  <div>
    <div v-if="displayWarningMessage" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Recommend Email</div>
      <ul>
        <template v-if="data.length">
          <li v-for="(item, index) in data" :key="index">{{item}}</li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'RecommendEmail',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      data: undefined,
      rule_type: 'recommend_email',
    }
  },
  computed: {
    displayWarningMessage() {
      return (Array.isArray(this.data) && (this.data.length > 0)) || this.current_custom_rules.length
    },
  },

  methods: {
    formatRules() {
      this.loadData()
      this.expandData()
    },
    loadData() {
      const rules = this.current_rules
      const translator = {
        'has_custom_language_need_to_include_in_the_recommend_email': 'You must include the following in your recommend email: {value}',
        'is_label_experts': 'Always label the expert employer (including last 6 months former employees) as Private, Listed (with stock ticker) or Government Affiliated.',
        'has_special_email_subject_rules': 'Your recommend email must use the following format in the email subject : {value}',
      }

      this.data = translateRules(rules, translator)
      if (this.data.length || this.current_custom_rules.length) this.$emit('update:hasRules', true)

      function translateRules(rules, translator) {
        const message_list = []

        if (rules.has_custom_language_need_to_include_in_the_recommend_email) {
          message_list.push(translator.has_custom_language_need_to_include_in_the_recommend_email.replace('{value}',
            rules.custom_language_need_to_include_in_the_recommend_email,
          ))
        }

        if (rules.is_label_experts) {
          message_list.push(translator.is_label_experts)
        }

        if (rules.has_special_email_subject_rules) {
          message_list.push(translator.has_special_email_subject_rules.replace('{value}',
            rules.special_email_subject_rules,
          ))
        }

        return message_list

        // eslint-disable-next-line no-unused-vars
        function hasValue(value) {
          if (Array.isArray(value)) {
            return value.length > 0
          } else {
            return ![undefined, false].includes(value)
          }
        }
      }
    },
    expandData() {
      const list = []

      trySolve(this.current_rules, '', list)

      function trySolve(rule, path, list) {
        Object.keys(rule).forEach(key => {
          if (typeof rule[key] === 'object') {
            trySolve(rule[key], `${path}.${key}`, list)
          } else {
            list.push(path ? `${path}.${key}` : key)
          }
        })
      }
    },
  },
}
</script>

<style scoped>
  ::v-deep .el-alert {
    padding: 5px 16px;
  }
</style>
