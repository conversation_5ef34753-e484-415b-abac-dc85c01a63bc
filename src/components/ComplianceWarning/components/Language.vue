<template>
  <div>
    <div v-if="tips.length" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Language Rules</div>
      <ul>
        <li v-for="(item, index) in tips" :key="index">
          {{item.note}}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Mixin from '../mixin'

export default {
  name: 'Language',
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'language_rules',
    }
  },
  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (rules.compliance_team_language.master_switch) {
        result_list.push({
          type: 'compliance_email_text_language',
          note: 'When communicating with the client compliance team, you must write your emails using ' + rules.compliance_team_language.email_text + '.',
        })
        result_list.push({
          type: 'compliance_expert_bios_answer_language',
          note: 'When communicating with the client compliance team, the expert bios should be written in ' + rules.compliance_team_language.expert_bios_ca_answers_etc + '.',
        })
      }

      if (rules.communication_with_users_analysts.master_switch) {
        result_list.push({
          type: 'user_email_text_language',
          note: 'When communicating with the client users, you must write your emails using ' + rules.communication_with_users_analysts.email_text + '. (This is a compliance rule, the user may not overrule it)',
        })
        result_list.push({
          type: 'usr_expert_bios_answer_language',
          note: 'When communicating with the client users, the expert bios should be written in ' + rules.communication_with_users_analysts.expert_bios_ca_answers_etc + '. (This is a compliance rule, the user may not overrule it)',
        })
      }
      this.tips = result_list

      if (result_list.length) this.$emit('update:hasRules', true)
    },
  },
}
</script>

<style scoped>

</style>
