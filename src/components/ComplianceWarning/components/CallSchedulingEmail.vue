<template>
  <div>
    <div v-if="ruleShow" :class="{'compliance-rule-item':showTitle}">
      <div v-if="showTitle" class="title">Call Scheduling Emails</div>
      <ul>
        <template v-if="tips.length">
          <li v-for="(item, index) in tips" :key="index">
            {{item.note}}
          </li>
        </template>
        <template v-if="current_custom_rules.length">
          <li v-for="(custom, keyIndex) in current_custom_rules" :key="keyIndex + 'A'">
            <div v-html="custom.body" />
            <email-copy v-if="custom.list" :email-list="custom.list" />
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'CallSchedulingEmail',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      tips: [],
      rule_type: 'call_scheduling_emails',
    }
  },

  computed: {
    ruleShow() {
      if (this.tips.length || this.current_custom_rules.length) {
        this.$emit('update:hasRules', true)
        return true
      } else {
        return false
      }
    },
  },

  methods: {
    formatRules() {
      const rules = this.current_rules
      const result_list = []

      if (!rules.master_switch) return
      if (rules.schedule_with === 'With User') {
        const note = 'When scheduling emails, talk to the user.'
        result_list.push({
          type: 'schedule_with',
          note,
        })
      } else {
        const note = 'When scheduling emails, do not talk to the user, schedule them with: ' + rules.schedule_with_other_detail
        result_list.push({
          type: 'schedule_with',
          note,
        })
      }

      if (rules.is_wait_for_any_discussion_of_scheduling_until_after_approval_is_received) {
        const note = 'DO NOT DISCUSS CALL TIMES with the user until after you have approval. You can talk to the expert to get their availability, but do not tell those to the client or ask the client their availability until you have approval from client compliance.'
        result_list.push({
          type: 'wait_for_any_discussion',
          note,
        })
      }

      if (rules.can_send_placeholder_calendar_invite_before_approval) {
        let note
        if (rules.send_placeholder_calendar_invite_before_approval_type === 'Can send') {
          note = ' You can send placeholder calendar invites to the client user if they would like, however never include dial-in details and make sure the invite clearly says "PENDING" or "PLACEHOLDER" until you have approval. Only after you have approval should you update the invite. with the dial-in details.'
        } else {
          note = 'Never send a client user any sort of placeholder calendar invite before you have approval from client compliance. If they ask for this, tell them you cannot do so for compliance reasons. They can block out their own calendar if you\'ve settled on a time. Once you have approval, you can send the calendar invite with the dial in details.'
        }
        result_list.push({
          type: 'send_placeholder_calendar',
          note,
        })
      }
      this.tips = result_list
    },
  },
}
</script>

<style scoped>

</style>
