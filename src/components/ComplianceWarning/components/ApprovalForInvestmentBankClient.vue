<template>
  <div v-show="tips.length" class="compliance-warning-alert mb-8">
    <span class="alert-icon"><b>COMPLIANCE RULES</b></span>
    <div class="mt-4">
      <div class="compliance-rule-item">
        <div class="title">Approval</div>
        <ul>
          <template>
            <li v-for="(item, index) in tips" :key="index">
              {{ item.note }}
            </li>
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ApprovalForInvestmentBankClient',
  props: {
    projectType: {
      type: String,
      default: 'Consultation',
    },
    clientId: [Number, String],
    customizedRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      tips: [],
    }
  },

  mounted() {
    this.formatRules()
  },

  methods: {
    formatRules() {
      const result_list = []

      if (this.customizedRules?.approve_policy === 'CAPVISION_APPROVAL') {
        const note = 'Require Capvision approval'
        result_list.push({ type: 'capvision_approval', note })

        if (this.customizedRules.pre_ca.required) {
          const note = 'Requesting the expert to complete the Client Agreement.'
          result_list.push({ type: 'capvision_approval', note })
        }
      }

      if (this.customizedRules?.approve_policy === 'CAPVISION_AND_CLIENT_APPROVAL_ALL') {
        let note = ''
        const pre_ca_rules = this.customizedRules.pre_ca
        result_list.push({ type: 'capvision_approval', note: 'Requires Capvision and client legal review.' })
        if (pre_ca_rules.required) {
          result_list.push({ type: 'capvision_approval', note: 'Request this expert to complete the Client Agreement' })
        }

        if (pre_ca_rules.send_to_client_legal_in_email) {
          note = 'Require to send an email to the client\'s legal team.'
          pre_ca_rules.send_to_client_legal_in_email_attachment && (note += ' (The CA is sent as an attachment in PDF format)')
        }

        note && result_list.push({ type: 'capvision_approval', note })
      }
      this.$set(this, 'tips', result_list)
    },
  },
}
</script>

<style scoped lang="scss">
.alert-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: auto;
  margin: 0;
  padding: 5px 20px;
  background: #ffeb3b;
  clip-path: polygon(7px 0, 100% 0, 100% calc(100% - 7px),
  calc(100% - 7px) 100%, 0 100%,
  0 7px
  );
  color: #dc6510;
  font-size: 12px;
}

.compliance-warning-alert {
  position: relative;
  padding: 2em 1em 0.5em 2em;
  background: #edeef5cf;
  -webkit-clip-path: polygon(10px 0, 100% 0, 100% calc(100% - 10px),
  calc(100% - 10px) 100%, 0 100%,
  0 10px
  );
  clip-path: polygon(10px 0, 100% 0, 100% calc(100% - 10px),
  calc(100% - 10px) 100%, 0 100%,
  0 10px
  );

::v-deep ul {
  margin-bottom: 0;
  margin-top: 0;
  padding-left: 1.2em;
  list-style-type: '- ';
  font-size: 12px;
  line-height: 16px;

li {
  word-break: break-word;
}

ul {
  padding-left: 2em;
  list-style-type: circle;
}
}
}
</style>
