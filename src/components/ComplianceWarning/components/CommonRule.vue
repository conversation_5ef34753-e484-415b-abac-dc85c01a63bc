<template>
  <div>
    <ul>
      <li v-for="(item, index) in common_rules" :key="index">
        <div v-if="hasCopyList(item)">
          {{item.note}}
          <email-copy :email-list="item.list" />
        </div>
        <span v-else>{{item.note}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
import EmailCopy from '../EmailCopy'
import Mixin from '../mixin'

export default {
  name: 'CommonRule',
  components: { EmailCopy },
  mixins: [Mixin],
  data() {
    return {
      rule_type: 'common',
    }
  },

  methods: {
    hasCopyList(item) {
      const list = ['client_all_emails', 'internal_all_emails', 'client_negotiating_call_schedule']
      return list.includes(item.type) && item.list
    },
  },
}
</script>

<style scoped>

</style>
