<template>
  <div class="rules-view">
    <div
      v-show="has_rule1"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <pre-call-ca
        :type-rule="clientRule"
        :has-rules.sync="has_rule1"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule2"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <Approval
        :type-rule="clientRule"
        :has-rules.sync="has_rule2"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule3"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <Arrange
        :type-rule="clientRule"
        :has-rules.sync="has_rule3"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule4"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <calendar-invite
        :type-rule="clientRule"
        :has-rules.sync="has_rule4"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule5"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <Recommend
        :type-rule="clientRule"
        :has-rules.sync="has_rule5"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule6"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <project-creation
        :type-rule="clientRule"
        :has-rules.sync="has_rule6"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule7"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <call-scheduling
        :type-rule="clientRule"
        :has-rules.sync="has_rule7"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule8"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <post-call
        :type-rule="clientRule"
        :has-rules.sync="has_rule8"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule9"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <expert-restriction
        :type-rule="clientRule"
        :has-rules.sync="has_rule9"
        :client-am="am"
        show-title
      />
    </div>
    <div
      v-show="has_rule10"
      style="
        padding: 10px;
        margin: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 11pt;
      "
    >
      <survey-rules
        :type-rule="clientRule"
        :has-rules.sync="has_rule10"
        show-title
      />
    </div>
  </div>
</template>

<script>
import Approval from './components/Approval'
import Arrange from './components/Arrange'
import Recommend from './components/RecommendEmail'
import ProjectCreation from './components/ProjectCreation'
import PreCallCa from './components/PreCallCA'
import CalendarInvite from './components/CalendarInvite'
import CallScheduling from './components/CallSchedulingEmail'
import ExpertRestriction from './components/ExpertRestriction'
import PostCall from './components/PostCall'
import SurveyRules from './components/Survey'
// import Survey from '@/components/ComplianceWarning/components/Survey'

export default {
  name: 'ClientComplianceIndex',
  components: {
    // Survey,
    Approval,
    Arrange,
    Recommend,
    ProjectCreation,
    PreCallCa,
    CalendarInvite,
    ExpertRestriction,
    CallScheduling,
    PostCall,
    SurveyRules,
  },
  props: {
    clientRule: {
      type: Object,
      default() {
        return {}
      },
    },
    am: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      has_rule: true,
      has_rule1: false,
      has_rule2: false,
      has_rule3: false,
      has_rule4: false,
      has_rule5: false,
      has_rule6: false,
      has_rule7: false,
      has_rule8: false,
      has_rule9: false,
      has_rule10: false,
    }
  },
}
</script>

<style scoped lang="scss">
.rules-view .compliance-rule-item {
  border: 1px solid #ccc;
  li {
    list-style: none;
  }
}

.alert-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: auto;
  margin: 0;
  padding: 5px 20px;
  background: #ffeb3b;
  clip-path: polygon(
    7px 0,
    100% 0,
    100% calc(100% - 7px),
    calc(100% - 7px) 100%,
    0 100%,
    0 7px
  );
  color: #dc6510;
  font-size: 12px;
}

.compliance-warning-alert {
  position: relative;
  padding: 2em 1em 0.5em 2em;
  background: #edeef5cf;
  -webkit-clip-path: polygon(
    10px 0,
    100% 0,
    100% calc(100% - 10px),
    calc(100% - 10px) 100%,
    0 100%,
    0 10px
  );
  clip-path: polygon(
    10px 0,
    100% 0,
    100% calc(100% - 10px),
    calc(100% - 10px) 100%,
    0 100%,
    0 10px
  );

  ::v-deep ul {
    margin-bottom: 0;
    margin-top: 0;
    padding-left: 1.2em;
    list-style-type: "- ";
    font-size: 12px;
    line-height: 16px;

    li {
      word-break: break-word;
    }

    ul {
      padding-left: 2em;
      list-style-type: circle;
    }
  }
}

.mt-4 {
  margin-top: 4px;
}
</style>
