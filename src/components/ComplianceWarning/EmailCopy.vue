<template>
  <div class="email-list">
    <el-link :underline="false" type="primary" class="copy-icon" @click="getEmailAddress">copy</el-link>
    <ul>
      <li v-for="(item, keyIndex) in emailList" :key="keyIndex">
        {{item.name}} - {{item.email}}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'EmailCopy',
  props: {
    emailList: Array,
  },
  methods: {
    getEmailAddress() {
      const address_list = this.emailList.map(item => item.email)
      const format_address = address_list.join('; ')
      const input = document.createElement('input')
      document.body.appendChild(input)
      input.setAttribute('value', format_address)
      input.select()

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'warning',
        })
      }

      document.body.removeChild(input)
    },
  },
}
</script>

<style scoped lang="scss">
  .email-list {
    position: relative;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    padding-top: 5px;
    color: #5a5e66;

    ul {
      list-style-type: square;

      li {
        margin-bottom: 5px;
      }
    }

    .copy-icon {
      display: none;
      position: absolute;
      top: 0;
      right: 0;
      background-color: #42B983;
      border-radius: 4px;
      color: #fff;
      padding: 0 2px 2px;
    }

    &:hover {
      border: 1px solid #42B983;

      .copy-icon {
        display: inline-block;
      }
    }
  }

</style>
