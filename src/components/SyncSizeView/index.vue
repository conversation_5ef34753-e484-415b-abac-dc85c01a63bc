<template>
  <div :class="`sync-view-${className}`">
    <slot />
  </div>
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  props: {
    className: {
      type: String,
      required: true,
    },
  },
  mounted() {
    erd.listenTo(this.$el, (element) => {
      this.$nextTick(() => {
        const col_fixed_element = document.querySelector(`.el-table__fixed-right div.sync-view-${this.className}`)
        if (col_fixed_element) {
          col_fixed_element.style.height = element.offsetHeight + 'px'
        }
      })
    })
  },
}

</script>
