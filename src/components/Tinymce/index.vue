<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" :style="{width:containerWidth}">
    <textarea v-if="!inline" :id="tinymceId" :style="cssVars" class="tinymce-textarea place-holder" />
    <slot v-else>
      <h3 v-if="inlineHeader" :id="tinymceId" :style="cssVars" class="tinymce-heading place-holder" />
      <div v-else :id="tinymceId" :style="cssVars" class="tinymce-body place-holder" />
    </slot>
    <div v-if="insertLogo" class="editor-custom-btn-container">
      <el-button type="primary" class="el-button--xs" @click="insertCapLogo">logo</el-button>
<!--      <editorImage color="#1890ff" class="editor-upload-btn" @successCBK="imageSuccessCBK" />-->
    </div>
  </div>
</template>

<script>
/**
 * docs:
 * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce
 */
// import editorImage from './components/EditorImage'
import plugins from './plugins'
import toolbar from './toolbar'

// why use this cdn, detail see https://github.com/PanJiaChen/tinymce-all-in-one
// const tinymceCDN = 'https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.3/tinymce.min.js'

export default {
  name: 'Tinymce',
  // components: { editorImage },
  props: {
    id: {
      type: String,
      default() {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      },
    },
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return []
      },
    },

    menubar: {
      type: String,
      // default: 'file edit insert view format table'
      default: '',
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360,
    },
    width: {
      type: [Number, String],
      required: false,
      default: 'auto',
    },
    inline: {
      type: Boolean,
      default: false,
    },
    inlineHeader: {
      type: Boolean,
      default: false,
    },
    isEmail: {
      type: Boolean,
      default: false,
    },
    inlineContent: {
      type: Boolean,
      default: false,
    },

    fontSizeChange: {
      type: Boolean,
      default: false,
    },

    readonly: {
      type: Boolean,
      default: false,
    },
    pOriginMargin: {
      type: Boolean,
      default: false,
    },

    insertLogo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loadStatus: 0,
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      languageTypeList: {
        'en': 'en',
        'zh': 'zh_CN',
        'es': 'es_MX',
        'ja': 'ja',
      },
      fontsize_formats: '8pt 9pt 10pt 11pt 12pt 14pt 18pt 24pt 36pt',
      font_formats: 'Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Calibri=Calibri;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings,zapf dingbats',
    }
  },
  computed: {
    containerWidth() {
      const width = this.width
      if (/^[\d]+(\.[\d]+)?$/.test(width)) { // matches `100`, `'100'`
        return `${width}px`
      }
      return width
    },
    cssVars() {
      return {
        '--content': this.value ? '' : `'${this.placeholder}'`,
      }
    },
  },
  watch: {
    value(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() =>
          window.tinymce.get(this.tinymceId)
            .setContent(val || ''))
      }
      if (this.hasChange && this.hasInit) {
        this.$emit('change', val)
      }
    },
  },
  mounted() {
    this.init()
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce()
    }
  },
  deactivated() {
    this.destroyTinymce()
  },
  destroyed() {
    this.destroyTinymce()
  },
  methods: {
    confirmLoaded() {
      if (this.loadStatus === 1) {
        return Promise.resolve()
      }

      return new Promise((resolve) => {
        const status_watcher = this.$watch('loadStatus', status => {
          if (status === 1) {
            resolve()
            status_watcher()
          }
        })
      })
    },
    init() {
      // dynamic load tinymce from cdn
      // load(tinymceCDN, (err) => {
      //   if (err) {
      //     this.$message.error(err.message)
      //     return
      //   }
      this.initTinymce()
      this.loadStatus = 1
      // })
    },

    formatValue() {
      const defaultFontSize = '11pt'
      const defaultFont = this.fontSizeChange ? '-webkit-body' : 'Calibri'

      if (this.value) {
        // 解析 HTML 内容
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.value, 'text/html')
        // 查找最外层div 添加内联样式
        const firstElement = doc.body.firstElementChild

        if (firstElement && firstElement.tagName?.toLowerCase() === 'div') {
          // 根据选择器或其他方式获取当前字体大小
          firstElement.style.fontSize = firstElement.style.fontSize || defaultFontSize // 将字体大小转换为内联样式
          firstElement.style.fontFamily = firstElement.style.fontFamily || defaultFont // 将字体大小转换为内联样式
          return doc.body.innerHTML
        } else {
          return `<div style="font-size: ${defaultFontSize};font-family: ${defaultFont};">${this.value}</div>`
        }
      } else {
        return `<div style="font-size: ${defaultFontSize};font-family: ${defaultFont};"> </div>`
      }
    },

    initTinymce() {
      const _this = this
      const initOptions = {
        plugins: plugins,
        branding: false,
        valid_elements: '*[*]',
        entity_encoding: 'raw',
        fontsize_formats: this.fontsize_formats,
        font_formats: this.font_formats,
        content_filter: 'mceDefaultFilter',
        valid_attributes: 'href,target,src,alt,title',
        powerpaste_word_import: 'clean',
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        visual_table_class: 'tinymce-custom-table', // remove table border
        contextmenu: 'link', // 右键菜单只展示链接
        content_style: `
          body { font-family: Calibri; font-size: 11pt; }
          .mce-content-body { font-family: Calibri; font-size: 11pt; }
        `,
        font_default: 'Calibri',
        paste_as_text: true,
        forced_root_block: 'div', // 禁用默认的 <p> 标签
        init_instance_callback: editor => {
          editor.setContent(this.formatValue())
          this.$emit('input', editor.getContent())

          _this.hasInit = true
          editor.on('NodeChange Change KeyUp SetContent', () => {
            this.hasChange = true
            this.$emit('input', editor.getContent())
          })
        },
        setup(editor) {
          editor.on('FullscreenStateChanged', (e) => {
            _this.fullscreen = e.state
          })
          editor.on('focus', (e) => {
            _this.$emit('focus', e)
          })

          editor.on('init', () => {
            if (!_this.inlineHeader && _this.isEmail) {
              editor.setContent(
                '<div style="font-size: 11pt; font-family: Calibri;">' + editor.getContent() + '</div>')
            }
            if (_this.fontSizeChange) {
              editor.setContent(
                '<div style="font-size: 11pt;font-family: -webkit-body;">' + editor.getContent() + '</div>')
            }
          })
        },
      }
      if (this.readonly) {
        initOptions.readonly = 1
      }

      if (this.isEmail) {
        initOptions.content_style = '.tinymce-body { font-size: 11pt; font-family: Calibri; } '
        initOptions.remove_trailing_brs = false
      }

      if (!this.inline) {
        window.tinymce.init(Object.assign({
          selector: `#${this.tinymceId}`,
          language: this.languageTypeList['en'],
          height: this.height,
          body_class: 'panel-body ',
          object_resizing: false,
          toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
          menubar: this.menubar,
          content_style: !this.pOriginMargin ? '.tinymce-body p { margin: 0; } #tinymce p { margin: 0; }' : '',
          end_container_on_empty_block: true,
          code_dialog_height: 450,
          code_dialog_width: 1000,
          advlist_bullet_styles: 'square',
          advlist_number_styles: 'default',
          imagetools_cors_hosts: ['www.tinymce.com', 'codepen.io'],
          default_link_target: '_blank',
          link_title: false,
          nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
          // 整合七牛上传
          // images_dataimg_filter(img) {
          //   setTimeout(() => {
          //     const $image = $(img);
          //     $image.removeAttr('width');
          //     $image.removeAttr('height');
          //     if ($image[0].height && $image[0].width) {
          //       $image.attr('data-wscntype', 'image');
          //       $image.attr('data-wscnh', $image[0].height);
          //       $image.attr('data-wscnw', $image[0].width);
          //       $image.addClass('wscnph');
          //     }
          //   }, 0);
          //   return img
          // },
          // images_upload_handler(blobInfo, success, failure, progress) {
          //   progress(0);
          //   const token = _this.$store.getters.token;
          //   getToken(token).then(response => {
          //     const url = response.data.qiniu_url;
          //     const formData = new FormData();
          //     formData.append('token', response.data.qiniu_token);
          //     formData.append('key', response.data.qiniu_key);
          //     formData.append('file', blobInfo.blob(), url);
          //     upload(formData).then(() => {
          //       success(url);
          //       progress(100);
          //     })
          //   }).catch(err => {
          //     failure('出现未知问题，刷新页面，或者联系程序员')
          //     console.log(err);
          //   });
          // },
        }, initOptions))
      } else {
        if (this.inlineHeader) {
          window.tinymce.init(Object.assign({
            selector: `#${this.tinymceId}`,
            menubar: false,
            inline: true,
            toolbar: false,
          }, initOptions))
        } else {
          window.tinymce.init(Object.assign({
            selector: `#${this.tinymceId}`,
            menubar: false,
            inline: true,
            height: this.height,
            toolbar: [
              'undo redo | bold italic underline | fontselect fontsizeselect',
              'forecolor backcolor | alignleft aligncenter alignright alignfull | numlist bullist outdent indent',
            ],
          }, initOptions))
        }
      }
    },
    destroyTinymce() {
      const tinymce = window.tinymce.get(this.tinymceId)
      if (this.fullscreen) {
        tinymce.execCommand('mceFullScreen')
      }

      if (tinymce) {
        tinymce.destroy()
      }
    },
    setContent(value) {
      window.tinymce.get(this.tinymceId)
        .setContent(value)
    },
    getContent() {
      return window.tinymce
        .get(this.tinymceId)
        .getContent()
    },
    getInnerText() {
      return window.tinymce.get(this.tinymceId).targetElm.innerText
    },
    imageSuccessCBK(arr) {
      const _this = this
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId)
          .insertContent(`<img class="wscnph" src="${v.url}" >`)
      })
    },
    insertCapLogo() {
      const logo_public_url = 'https://static.capvision.cn/files/fileUpload/2025/03/27/20250327-4be501df-32fa-44dc-ac8a-6937b5d96852.png'
      const _this = this
      window.tinymce.get(_this.tinymceId)
        .insertContent(`<img class="wscnph" width="220" height="50" style="isplay:block; width:220px; height:50px;  border:0; outline:none; text-decoration:none;" src="${logo_public_url}" >`)
    },
  },
}
</script>

<style scoped lang="scss">
.tinymce-container {
  position: relative;
  line-height: normal;
}

.tinymce-container > > > .mce-fullscreen {
  z-index: 10000;
}

.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}

.editor-custom-btn-container {
  position: absolute;
  right: 4px;
  top: 4px;
  /*z-index: 2005;*/
}

.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}

.editor-upload-btn {
  display: inline-block;
}

// Toolbar wrap automatically
.tinymce-container > > > .mce-container > div {
  white-space: pre-wrap;
}

.tinymce-heading {
  text-align: center;
  margin: 0;
  font-size: 1.34rem;
}

.tinymce-body {
}

.place-holder {
  &::before {
    color: #C6C8D4;
    content: var(--content);
    position: absolute;
    width: 100%;
    pointer-events: none;
  }

  &.tinymce-heading::before {
    left: 0;
  }
}

</style>
