<template>
  <div class="inline-block">
    <el-link v-if="fileList.length === 1" type="primary" :href="fileList[0].path">
      <i class="el-icon-download" />
    </el-link>
    <el-button v-if="fileList.length > 1" type="text" class="el-icon-download" @click="dialogVisible = true" />
    <el-dialog
      title="CA Respond List"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%">
      <div v-for="(item, index) in fileList" :key="index" class="mb-8">
        <el-link type="primary" :href="item.path">
          {{ item.name }}
          <i class="el-icon-download" />
        </el-link>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DownloadListIndex',
  props: {
    fileList: Array,
  },
  data() {
    return {
      dialogVisible: false,
    }
  },
}
</script>

<style scoped>

</style>
