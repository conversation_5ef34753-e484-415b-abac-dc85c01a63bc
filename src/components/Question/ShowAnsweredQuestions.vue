<template>
  <div>
    <div v-if="list.length">
      <div v-if="['advisor_profile','consultation_approval','task_detail_dialog'].includes(where)">
        <div v-for="(item,item_index) in list" :key="`${item_index}-${list[0].id}`" class="flex q-list">
          <div>{{ item.sort_order + 1 }}.</div>
          <div class="flex-1">
            <div class="outermost-p-td-0 title" v-html="item.title" />
            <div v-if="item.answer && item.answer.content" :class="sqAnswerClassName">
              <template v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                <div v-for="(temp,index) in item.answer.content.result" :key="index">
                  <span v-if="temp.option">{{temp.option }}:</span>
                  <span v-if="item.type === 'RANKING_SET'">{{ temp.rank }},</span>
                  {{ temp.text }}</div>
              </template>
              <template v-if="item.type === 'RADIO'">
                <el-radio-group v-model="item.answer.content.result">
                  <el-radio v-for="(temp, index) in item.options"
                            :key="index"
                            disabled
                            :label="index"
                            class="sq-option-flex">
                    {{temp.text}}
                    <div v-if="item.answer.content.result === index && item.answer.content.desc"
                         class="text-decoration-underline">{{ item.answer.content.desc }}</div>
                  </el-radio>
                </el-radio-group>
              </template>
              <template v-if="item.type === 'CHECKBOX'">
                <el-checkbox-group v-model="item.answer.content.result">
                  <el-checkbox v-for="(temp, index) in item.options"
                               :key="index"
                               disabled
                               :label="index"
                               class="sq-option-flex">
                    {{temp.text}}
                    <div v-if="item.answer.content.result.includes(index) && item.answer.content.desc"
                         class="text-decoration-underline">{{ item.answer.content.desc[index] }}</div>
                  </el-checkbox>
                </el-checkbox-group>
              </template>
              <span v-if="!['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                <i v-if="item.answer.content.rank">{{item.answer.content.rank}}</i>
                <i>{{ item.answer.content.result }}</i>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowAnsweredQuestions',
  props: {
    list: Array,
    where: String,
  },
  computed: {
    sqAnswerClassName() {
      return this.where === 'task_detail_dialog' ? 'sq-answer-for-task' : 'sq-answer'
    },
  },
}
</script>

<style scoped>
.title {
  word-break: break-word;
}

.sq-answer {
  color: #3f9eff;
  margin: 2px 0;
}
.q-list {
  padding: 10px;
}

.sq-answer-for-task {
  width:100%;
  background-color: #f3f1f1;
  border-radius: 4px;
  padding: 10px;
}
</style>
