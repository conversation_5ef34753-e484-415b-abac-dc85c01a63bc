<template>
  <div v-if="createQuestion" class="question-level-2-content">
    <div v-if="has_title" class="show-item-box">
      <div>
        <div class="first-title-section" @click="has_title=false">
          <span v-if="form.is_required" class="request">*</span>
          <div class="content-html" v-html="form.title" />
        </div>
        <div v-for="(item, index) in form.sub_questions" :key="index">
          <level-item v-if="item.edit" :edit-question="item" not-sort @update="updateItem" />
          <div v-else @click="editItem(item, index)">
            <div class="item-box">
              <div class="actions-box">
                <el-tooltip content="Move up" placement="left" :open-delay="300">
                  <el-button
                    v-if="index > 0"
                    type="text"
                    class="item"
                    icon="el-icon-top"
                    @click.stop="upItem(index)" />
                </el-tooltip>
                <el-tooltip content="Delete" placement="left" :open-delay="300">
                  <el-button
                    type="text"
                    class="item"
                    icon="el-icon-delete"
                    @click.stop="removeItem(index)" />
                </el-tooltip>
                <el-tooltip content="Move Down" placement="left" :open-delay="300">
                  <el-button
                    v-if="index < form.sub_questions.length-1"
                    type="text"
                    class="item"
                    icon="el-icon-bottom"
                    @click.stop="downItem(index)" />
                </el-tooltip>
              </div>
              <item-show :form="item" :order="formatSort(index)" class="content-box" />
            </div>
          </div>

        </div>
        <level-item v-if="is_add_item" not-sort @save="saveItem" />
      </div>
    </div>

    <div v-else>
      <el-form ref="form" v-loading="loading" :model="form" :rules="rules" label-width="180px" class="edit-box">
        <el-form-item label="First level heading" prop="title" class="w-percent-80">
          <tinymce v-model="form.title" :height="100" :toolbar="toolbar" />
        </el-form-item>
        <el-form-item label="Sort">
          <el-switch v-model="form.sort" active-color="#13ce66" />
        </el-form-item>
        <el-form-item label="Sub Sort">
          <el-switch v-model="form.sub_sort" active-color="#13ce66" />
        </el-form-item>
        <el-form-item label="Required">
          <el-switch v-model="form.is_required" active-color="#13ce66" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="!form.title" @click.stop="saveTitle">Next</el-button>
          <el-button type="text" @click.stop="cancelEdit">Cancel</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="checkDisabled()" class="actions-btn">
      <el-button type="primary" icon="el-icon-plus" @click="actionAddItem()">Add Item</el-button>
      <el-button type="primary" :disabled="!form.sub_questions.length" @click="actionSaveQuestion()">Save</el-button>
      <el-button type="text" @click="cancelEdit()">Cancel</el-button>
    </div>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import ItemShow from './ItemShow'
import levelItem from './index'

export default {
  name: 'Level2',
  components: { Tinymce, levelItem, ItemShow },
  props: {
    editQuestion: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },

  data() {
    return {
      toolbar: ['undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      createQuestion: true,
      has_title: false,
      is_add_item: false,
      edit_index: null,
      form: {
        title: '',
        sort: true,
        sub_sort: true,
        sub_questions: [],
        type: 'TEXT_DESCRIPTION',
        is_required: false,
      },
      rules: {
        title: [
          { required: true, message: 'Question title is required', trigger: 'blur' },
        ],
      },
    }
  },

  created() {
    this.loading = true
    if (this.editQuestion.edit) {
      this.form = Object.assign({}, this.form, this.editQuestion)
      this.form.edit = false
    }
    this.loading = false
  },

  methods: {
    saveTitle() {
      this.has_title = true
      this.is_add_item = this.form.sub_questions.length === 0
    },

    editItem(item, index) {
      item.edit = true
      this.edit_index = index
    },

    saveItem(val) {
      if (val.title) {
        val.edit = false
        this.form.sub_questions.push(val)
      }
      this.is_add_item = false
    },

    updateItem(val) {
      val.edit = false
      this.$set(this.form.sub_questions, this.edit_index, val)
      this.edit_index = null
    },

    actionAddItem() {
      this.is_add_item = true
    },

    actionSaveQuestion() {
      const data = Object.assign({}, this.form)
      this.editQuestion.edit ? this.$emit('update', data) : this.$emit('save', data)
      this.createQuestion = false
    },

    cancelEdit() {
      if (this.editQuestion.edit) {
        const data = this.editQuestion
        data.edit = false
        this.$emit('update', data)
      } else {
        this.$emit('save', {})
      }
    },

    upItem(index) {
      const arr = this.form.sub_questions
      arr[index] = arr.splice(index - 1, 1, arr[index])[0]
    },

    downItem(index) {
      const arr = this.form.sub_questions
      arr[index] = arr.splice(index + 1, 1, arr[index])[0]
    },

    removeItem(index) {
      this.form.sub_questions.splice(index, 1)
    },

    checkDisabled() {
      return this.has_title && !this.is_add_item && this.edit_index === null
    },

    formatSort(index) {
      let order = null
      if (this.form.sub_sort) {
        order = String.fromCharCode(index + 97)
        this.form.sub_questions[index].display_order = index + 1
      } else {
        this.form.sub_questions[index].display_order = null
      }
      return order
    },

  },
}
</script>

<style lang="scss" scoped>
  .w-percent-80 {
    width: 80%;
  }

  .question-level-2-content {
    border: 1px solid #42B983;
    padding: 10px;
  }

  .show-item-box {
    min-height: 150px;
    position: relative;

    .first-title-section {
      display: flex;
      align-items: baseline;
      cursor: pointer;
      padding: 5px;
      border: 1px solid #fff;
      border-radius: 2px;

      .request {
        color: red;
        width: 8px;
      }

      .content-html {
        width: 90%;
        overflow-wrap: break-word;
        word-break: break-all;
      }

      &:hover {
        border: 1px solid #66b1ff;
      }
    }

    .item-box {
      padding: 8px 30px;
      border: 1px solid #fff;
      display: flex;

      .actions-box {
        display: none;
      }

      .content-box {
        flex: 1;
      }

      &:hover {
        cursor: pointer;
        border: 1px solid #66b1ff;
        border-radius: 2px;
        padding: 8px 0;

        .actions-box {
          display: flex;
          justify-content: center;
          flex-direction: column;
          width: 30px;

          .item {
            &:first-child {
              margin-left: 10px;
            }

            margin-right: 10px;
            font-size: 1rem;
          }

          .el-button--mini {
            padding: 3px 2px;
          }
        }
      }
    }

    .request {
      color: red;
    }
  }

  .edit-box {
    position: relative;
    padding: 20px 0;
    border-top: 1px solid #dfe4e6;
    background-color: #fafafa;
    user-select: none;

    ::v-deep .mce-statusbar {
      display: none;
    }
  }

  .actions-btn {
    text-align: center;
    margin-top: 20px;
  }

</style>
