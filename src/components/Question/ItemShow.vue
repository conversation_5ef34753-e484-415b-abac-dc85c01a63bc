<template>
  <div>
    <div
      class="question-title">
      <div>
        <span class="request"><i v-if="form.is_required">*</i></span>
        <span class="order"><span v-if="order">{{ order + '.  ' }}</span></span>
      </div>
      <div
        class="content"
        v-html="form.title" />
    </div>
    <div v-if="form.type === 'YES_NO'">
      <div class="option-item">
        <el-radio
          v-model="form.answer.result"
          :label="0"
          disabled>Yes <span class="info">(Allow)</span>
        </el-radio>
      </div>
      <div class="option-item">
        <el-radio
          v-model="form.answer.result"
          :label="1"
          disabled>
          No <span class="info">(Deny)</span>
        </el-radio>
      </div>
    </div>
    <div v-if="form.type === 'RADIO'">
      <el-radio-group v-model="form.answer.result">
        <div v-for="(option, indexKey) in form.options" :key="indexKey" class="option-item">
          <el-radio disabled :label="indexKey">{{ option.text }} <span
            class="info">{{getOptionType(option.type)}}</span>
            <span v-if="option.description && !showAnswer" class="el-icon-paperclip allow-desc-icon" /></el-radio>
          <div v-if="showAnswer && getAnswerDesc(form.answer,indexKey)" class="description">
            {{ getAnswerDesc(form.answer,indexKey) }}
            <span v-if="getKmComment(form.answer,indexKey)" class="km_comment ml-8">{{getKmComment(form.answer,indexKey)}}</span>
            <el-link
              v-if="isComment && form.answer.desc"
              type="primary"
              :underline="false"
              class="el-icon-chat-dot-square"
              @click="km_add_comment = !km_add_comment" />
            <div v-if="km_add_comment" class="flex align-items-baseline">
              <el-input v-model="km_comment" class="mt-8 inline-block" />
              <el-link
                type="success"
                :underline="false"
                class="el-icon-circle-check ml-8"
                @click="saveComment(indexKey)" />
            </div>

          </div>
        </div>
      </el-radio-group>
    </div>
    <div v-if="form.type === 'CHECKBOX'">
      <el-checkbox-group v-model="form.answer.result">
        <div v-for="(option, indexKey) in form.options" :key="indexKey" class="option-item">
          <el-checkbox
            disabled
            :label="indexKey">
            {{ option.text }} <span class="info">{{getOptionType(option.type)}}</span>
            <span v-if="option.description && !showAnswer" class="el-icon-paperclip allow-desc-icon" />
          </el-checkbox>
          <div v-if="showAnswer && getAnswerDesc(form.answer,indexKey)" class="description">
            {{ getAnswerDesc(form.answer,indexKey) }}
            <span v-if="getKmComment(form.answer,indexKey)" class="km_comment ml-8">{{ getKmComment(form.answer,indexKey)}}</span>
            <el-link
              v-if="isComment && form.answer.desc"
              type="primary"
              :underline="false"
              class="el-icon-chat-dot-square"
              @click="km_add_comment = !km_add_comment" />
            <div v-if="km_add_comment" class="flex align-items-baseline">
              <el-input v-model="km_comment" class="mt-8 inline-block" />
              <el-link
                type="success"
                :underline="false"
                class="el-icon-circle-check ml-8"
                @click="saveComment(indexKey)" />
            </div>
          </div>
        </div>
      </el-checkbox-group>
    </div>
    <div v-if="form.type === 'NUMERIC'" class="option-item">
      <div class="flex">
        <el-input v-model="form.answer.result" type="number" disabled />
        <el-link
          v-if="isComment"
          type="primary"
          :underline="false"
          class="el-icon-chat-dot-square"
          @click="km_add_comment = !km_add_comment" />
      </div>
      <span
        v-if="form.answer.km_comment"
        class="km_comment mt-8 inline-block"
      >{{form.answer.km_comment.content}}
      </span>
      <div v-if="km_add_comment" class="flex align-items-baseline">
        <el-input v-model="km_comment" class="mt-8 inline-block" />
        <el-link
          type="success"
          :underline="false"
          class="el-icon-circle-check ml-8"
          @click="saveComment(null)" />
      </div>

    </div>
    <div v-if="form.type === 'TEXT'" class="option-item">
      <div class="flex">
        <el-input
          v-model="form.answer.result"
          type="textarea"
          :min-rows="3"
          disabled
        />
        <el-link
          v-if="isComment"
          type="primary"
          :underline="false"
          class="el-icon-chat-dot-square ml-8"
          style="align-items: flex-end;"
          @click="km_add_comment = !km_add_comment" />
      </div>
      <span
        v-if="form.answer.km_comment"
        class="km_comment mt-8 inline-block"
      >{{form.answer.km_comment.content}}
      </span>
      <div v-if="km_add_comment" class="flex align-items-baseline">
        <el-input v-model="km_comment" type="textarea" :min-rows="3" class="mt-8 inline-block" />
        <el-link
          type="success"
          :underline="false"
          class="el-icon-circle-check ml-8"
          @click="saveComment(null)" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ItemShow',
  props: {
    form: {
      type: Object,
      default: () => {
        return {}
      },
    },
    order: {
      type: [Number, String],
      default: null,
    },

    showAnswer: {
      type: Boolean,
      default: false,
    },

    isComment: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      km_add_comment: false,
      km_comment: '',
      option_type: [
        { name: 'Pass', value: 'ALLOW' },
        { name: 'Hold', value: 'HOLD' },
        { name: 'Fail', value: 'DENY' },
      ],
    }
  },

  watch: {
    'form': {
      handler() {
        if (!this.showAnswer) {
          this.form.answer = {
            result: this.form.type === 'CHECKBOX' ? [] : '',
          }
        }
      }, immediate: true,
    },
  },

  methods: {
    getOptionType(type) {
      const result = this.option_type.filter(item => item.value === type)[0].name
      return '(' + result + ')'
    },
    getAnswerDesc(answer, option_index) {
      let result = ''
      if (!answer.desc) return result
      if (typeof answer.desc !== 'string') {
        Object.keys(answer.desc)
          .forEach(function(key) {
            +key === option_index ? result = answer.desc[key] : ''
          })
      } else {
        answer.result === option_index ? result = answer.desc : ''
      }
      return result
    },

    getKmComment(answer, option_index) {
      let result = ''
      if (!answer.km_comment) return result
      if (typeof answer.km_comment.content !== 'string') {
        Object.keys(answer.km_comment.content)
          .forEach(function(key) {
            +key === option_index ? result = answer.km_comment.content[key] : ''
          })
      }
      return result
    },

    saveComment(value) {
      let obj = {}
      if (value !== null) {
        obj = {}
        obj[value] = this.km_comment
      }
      const result = {
        id: this.form.answer.id,
        km_comment: {
          'content': value !== null ? obj : this.km_comment,
        },
      }
      this.$set(this.form.answer, 'km_comment', { 'content': value !== null ? obj : this.km_comment })
      this.km_add_comment = false
      this.$emit('save', result)
    },

  },

}
</script>

<style scoped lang="scss">
  .question-title {
    display: flex;
    align-items: baseline;
    margin-bottom: 15px;
    font-size: 11pt;
    font-family: Calibri;

    .request {
      color: red;
      width: 8px;
    }

    .order {
      width: 1rem;
    }

    .content {
      padding-left: 3px;
      width: 90%;
      overflow-wrap: break-word;
      word-break: break-all;
    }
  }

  .option-item {
    display: block;
    max-width: 90%;
    margin: 0 20px 12px;
    word-break: break-all;
    font-family: Calibri;

    &:last-child {
      margin-bottom: 0;
    }

    ::v-deep .el-radio__input.is-disabled + span.el-radio__label {
      font-size: 11pt;
      color: #61646b;
      white-space: normal;
      word-wrap: break-word;
    }

    ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
      color: #61646b;
      white-space: normal;
      font-size: 11pt;
      word-wrap: break-word;
    }

    .allow-desc-icon {
      font-size: 11pt;
      color: #03a9f4;
      padding-left: 2px;
    }

    .description {
      color: #797b80;
      margin-top: 4px;
      margin-left: 24px;
      font-size: 11pt;
    }

  }

  .km_comment {
    background-color: #fbd7a3;
    padding: 2px 4px;
    font-size: 11pt;
    color: #000;
  }

  ::v-deep .el-radio-group {
    width: 100%;
  }

  ::v-deep .el-checkbox-group {
    width: 100%;
  }
</style>
