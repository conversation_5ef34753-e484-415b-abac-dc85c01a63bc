<template>
  <el-dialog
    v-loading="loading"
    :visible.sync="SQDetailVisible"
    :close-on-click-modal="false"
    :title="title"
    append-to-body
    class="questionnaire-container show-dialog"
    width="40%"
    @close="dialogClose"
  >
    <el-tag v-if="hasAnswer" type="success" class="copy-tag" @click="copySQ()">Copy</el-tag>
    <div class="question-subject">
      <div class="title">{{ questionnaire.title }}</div>
      <div v-if="questionnaire.description" class="description">{{ questionnaire.description }}</div>
    </div>
    <div class="custom-dialog-body scrollbar-narrow">
      <div
        v-for="(item, index) in questionnaire.questions"
        :id="`question-`+index"
        :key="index"
        class="question-box"
      >
        <div>
          <div class="first-level-title">
            <span class="order"><b>Q{{ item.sort_order + 1 + '. ' }}</b></span>
            <div class="content-html outermost-p-td-0" v-html="item.title" />
          </div>
          <div v-if="hasAnswer && (item.answer_text || item.answer_text===0)" class="answer-item">
            <span v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
              <div v-for="(temp, temp_index) in item.answer_text" :key="temp_index">
                <span v-if="temp.option">{{ temp.option }}: </span>
                <span v-if="item.type === 'RANKING_SET'">{{ temp.rank }},</span>
                {{ temp.text }}
              </div>
            </span>
            <span v-if="item.type==='RADIO'">
              <el-radio-group v-model="item.answer_text">
                <el-radio v-for="(temp, _index) in item.options"
                          :key="_index"
                          disabled
                          :label="_index"
                          class="sq-option-flex">
                  {{temp.text}}
                  <div v-if="item.answer_text === _index && item.answer_desc"
                       class="text-decoration-underline">{{ item.answer_desc }}</div>
                </el-radio>
              </el-radio-group>
            </span>
            <span v-if="item.type==='CHECKBOX'">
              <el-checkbox-group v-model="item.answer_text">
                <el-checkbox v-for="(temp, _index) in item.options"
                             :key="_index"
                             disabled
                             :label="_index"
                             class="sq-option-flex">
                  {{temp.text}}
                  <div v-if="item.answer_text.includes(_index) && item.answer_desc[_index]"
                       class="text-decoration-underline">{{ item.answer_desc[_index] }}</div>
                </el-checkbox>
              </el-checkbox-group>
            </span>
            <span v-if="!['CHECKBOX','RADIO','RANKING_SET','NO_RANKING_SET'].includes(item.type)">
              <span v-if="item.answer_rank">{{ item.answer_rank }} </span>
              {{ item.answer_text }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <sq-copy
      v-if="hasAnswer && copy_data"
      id="sq-copy"
      style="display: none;"
      show-answer
      :data="copy_data" />
  </el-dialog>
</template>

<script>
import InquiryAPI from '@/api/inquiry'
import SqCopy from './SqQuestionCopy'

export default {
  name: 'QuestionnaireShowV2',
  components: { SqCopy },
  props: {
    inquiryId: {
      type: Number,
      default: null,
    },
    branchId: {
      type: Number,
      default: null,
    },
    hasAnswer: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,
      SQDetailVisible: false,
      questionnaire: '',
      title: '',
      copy_data: null,
    }
  },

  watch: {
    'branchId': {
      handler(newVal) {
        if (newVal) {
          this.init()
        }
      },
    },
  },

  methods: {
    init() {
      this.loading = true
      this.copy_data = null
      const getData = this.hasAnswer ? this.getInstance() : this.getBranch()

      return getData.then(data => {
        data.questions = data.questions.map(item => {
          item.display_order = item.sort_order + 1
          return item
        })
        this.questionnaire = data
        this.copy_data = data
      }).finally(() => {
        this.loading = false
        this.SQDetailVisible = true
      })
    },

    dialogClose() {
      this.$emit('close')
    },

    // 问卷预览
    getBranch() {
      return InquiryAPI.getBranchOne(this.inquiryId, this.branchId)
    },

    // 已发出的问卷预览
    getInstance() {
      return InquiryAPI.getInstanceOne(this.branchId)
        .then(data => {
          data.questions = data.qa_list_snapshot.map(item => {
            item.answer_rank = null
            if (item.answer && item.type === 'RANKING_WITH_EXPLANATION') {
              item.answer_rank = item.answer.content.rank || null
            }
            item.answer_text = item.answer ? item.answer.content.result : (item.type === 'CHECKBOX' ? [] : '')
            item.answer_desc = item.answer ? item.answer.content.desc : (item.type === 'CHECKBOX' ? {} : '')
            return item
          })
          data.title = data.branch_snapshot.title
          return data
        })
    },

    copySQ() {
      // create element for copy SQ html & remove the element after copied
      const temp_el = document.createElement('div')

      const copyText = document.querySelector('#sq-copy').innerHTML
      temp_el.innerHTML = `<div><span style="color:#ffffff;">&nbsp;</span><div id="node"  style="width:600px;padding-top:10px;padding-bottom:10px;padding-left:50px;padding-right:50px;border:1px solid #c3c7bb;border-radius: 5px;">` +
        copyText + `</div></div>`
      document.body.appendChild(temp_el)
      const select = window.getSelection()
      select.removeAllRanges()

      const range = document.createRange()
      range.selectNode(temp_el)
      select.addRange(range) // to select text

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      }

      window.getSelection().removeAllRanges()// clear selection
      document.body.removeChild(temp_el)
    },

  },
}
</script>

<style scoped lang="scss">
.custom-dialog-body {
  max-height: 500px;
  margin-bottom: 30px;
  overflow: auto;
}

::v-deep .el-dialog__body {
  padding-top: 0;
}

.show-dialog {
  min-height: 500px;
}

.answer-item {
  margin-left: 1.5rem;
  margin-top: 10px;
  padding: 7px;
  border-radius: 4px;
  background-color: #f6f4f4;
  color: #171818;
  opacity: 0.8;
}

.copy-tag {
  margin-right: 0.5rem;

  &:hover {
    cursor: pointer;
  }
}

</style>
