<template>
  <el-dialog
    v-loading="loading"
    :visible.sync="SQDetailVisible"
    :close-on-click-modal="false"
    :title="title"
    append-to-body
    class="questionnaire-container show-dialog"
    width="50%"
    @close="dialogClose"
  >
    <el-tag type="success" class="copy-tag" @click="copySQ()">Copy</el-tag>
    <div class="question-subject">
      <div class="title">{{ name }}</div>
      <div v-if="description" class="description">{{ description }}</div>
    </div>
    <div class="custom-dialog-body scrollbar-narrow">
      <div
        v-for="(item, index) in question_list"
        :id="`question-`+index"
        :key="index"
        class="question-box"
      >
        <!--level 2-->
        <div v-if="!!item.sub_questions">
          <div class="first-level-title">
            <span class="request"><i v-if="item.is_required">*</i></span>
            <span class="order"><span v-if="item.display_order">{{ item.display_order + '. ' }}</span></span>
            <div class="content-html" v-html="item.title" />
          </div>
          <div v-for="(sub, indexKey) in item.sub_questions" :key="indexKey" class="sub-item-box">
            <item-show :form="sub" :order="subCheckSort(sub, indexKey)" :show-answer="has_answers" />
          </div>
        </div>
        <!--level 1-->
        <item-show v-else :form="item" :order="item.display_order" :show-answer="has_answers" />
      </div>
    </div>

    <sq-copy
      id="sq-copy"
      style="display: none;"
      show-answer
      :data="copy_data" />

  </el-dialog>
</template>

<script>
import ItemShow from '@/components/Question/ItemShow'
import SqCopy from './SqQuestionCopy'
import InquiryAPI from '@/api/inquiry'

export default {
  name: 'QuestionnaireShow',
  components: { ItemShow, SqCopy },
  props: {
    instanceId: {
      type: Number,
      default: null,
    },
  },

  data() {
    return {
      loading: false,
      SQDetailVisible: !!this.instanceId,
      name: '',
      title: 'SQ Detail',
      description: '',
      question_list: [],
      has_answers: false,
      copy_data: null,
    }
  },

  watch: {
    'instanceId': {
      handler(newVal) {
        if (newVal) {
          this.init()
        }
      },
    },
  },

  methods: {
    init() {
      this.loading = true

      return InquiryAPI.getInstanceOne(this.instanceId)
        .then(data => {
          this.has_answers = data.answers.length > 0
          this.copy_data = data
          this.question_list = data.questions.map(item => {
            if (item.sub_questions) {
              item.sub_questions.forEach(sub => {
                sub.answer = !data.answers.length ? '' : data.answers.filter(ans => ans.question_id === sub.id)[0].content
              })
            } else {
              item.answer = !data.answers.length ? '' : data.answers.filter(ans => ans.question_id === item.id)[0].content
            }
            return item
          })
          this.name = data.branch.title
          this.description = data.branch.description
        }).finally(() => {
          this.loading = false
          this.SQDetailVisible = true
        })
    },

    dialogClose() {
      this.$emit('close')
    },

    subCheckSort(sub, order) {
      return sub.display_order ? String.fromCharCode(order + 97) : null
    },

    copySQ() {
      // create element for copy SQ html & remove the element after copied
      const temp_el = document.createElement('div')

      const copyText = document.querySelector('#sq-copy').innerHTML
      temp_el.innerHTML = `<div><span style="color:#ffffff;">&nbsp;</span><div id="node"  style="width:600px;padding-top:10px;padding-bottom:10px;padding-left:50px;padding-right:50px;border:1px solid #c3c7bb;border-radius: 5px;">` + copyText + `</div></div>`
      document.body.appendChild(temp_el)
      const select = window.getSelection()
      select.removeAllRanges()

      const range = document.createRange()
      range.selectNode(temp_el)
      select.addRange(range) // to select text

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      }

      window.getSelection().removeAllRanges()// clear selection
      document.body.removeChild(temp_el)
    },
  },

}
</script>

<style scoped lang="scss">
  .custom-dialog-body {
    max-height: 500px;
    margin-bottom: 30px;
    overflow: auto;
  }

  ::v-deep .el-dialog__body {
    padding-top: 0;
  }

  .show-dialog {
    min-height: 500px;
  }

  .copy-tag {
    margin-right: 0.5rem;

    &:hover {
      cursor: pointer;
    }
  }

</style>
