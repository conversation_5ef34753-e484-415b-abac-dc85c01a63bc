<template>
  <el-button class="el-button--xs" :loading="loading" @click="generatePdf">Download PDF</el-button>
</template>

<script>
// https://github.com/bpampuch/pdfmake/issues/2654
// import pdfMake from 'pdfmake-0.2.7'
// import pdfFonts from 'pdfmake-0.2.7/build/vfs_fonts'
import top from '@/assets/image/cap-logo.png'
import footer from '@/assets/image/footer.png'
import footerBar from '@/assets/image/footer-bar.png'
import Filters from '@/utils/filters'
import ToolAPI from '@/api/tool'
import { mapGetters, mapState } from 'vuex'

// pdfMake.vfs = pdfFonts.pdfMake.vfs

// pdfmake太大了，所以放到静态资源目录，在index.html中用script标签引入了
const pdfMake = window.pdfMake

// 亚马逊静态资源

pdfMake.fonts = {
  NotoSansSC: {
    normal: 'https://static-db2.capvision.com/external/front/NotoSansSC-Regular.ttf',
    bold: 'https://static-db2.capvision.com/external/front/NotoSansSC-Bold.ttf',
  },
}
export default {
  name: 'CAPDF',
  props: {
    taskData: Object,
    caContent: Object,
    caType: {
      type: String,
      default: 'pre-call',
    },
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
    isPreCall() {
      return this.caType === 'pre-call'
    },
    ...mapState({
      isSGDB: state => state.app.isSGDB,
    }),
  },
  mounted() {
    this.formatCAContent()
  },
  methods: {
    generatePdf() {
      if (this.isPreCall) {
        this.preCallCA()
      } else {
        this.postCallCA()
      }
    },
    generatePreCaPDF() {
      const completed_date = Filters.momentFormat(this.taskData.latest_submitted_pre_ca.create_at, 'MM/DD/YYYY')

      const data = {
        content: [
          { text: completed_date, style: ['body', 'textRight'] },
          { text: this.caContent.title, style: 'header' },
          ...this.formatCAHeading(),
          ...this.formatCAContent(),
          {
            alignment: 'justify',
            margin: [0, 10, 30, 10],
            columns: [
              {
                text: `Completed by: ${this.taskData.advisor.name_prefix ? this.taskData.advisor.name_prefix + ' ' : ''}${this.taskData.advisor.full_name}`,
                style: ['advisor-info', 'textLeft'],
              },
              {
                text: `Date: ${completed_date}`,
                style: ['advisor-info', 'textRight'],
              },
            ],
          },
        ],
        images: {
          top: new URL(top, location.origin).href,
          footer: new URL(footer, location.origin).href,
          footerBar: new URL(footerBar, location.origin).href,
        },
        styles: {
          header: { font: 'NotoSansSC', fontSize: 20, bold: true, alignment: 'center', margin: [30, 10, 30, 10], decoration: 'underline' },
          body: { font: 'NotoSansSC', fontSize: 10 },
          bold: { bold: true },
          // underline: {decoration: 'underline'},
          textLeft: { alignment: 'left' },
          textRight: { alignment: 'right' },
          margin10: { margin: [10, 0, 10, 0] },
          question: { font: 'NotoSansSC', fontSize: 10, margin: [0, 5, 10, 5] },
          'full-name': { font: 'NotoSansSC', bold: true, fontSize: 10 },
          'answer-text': { font: 'NotoSansSC', fontSize: 10, margin: [15, 0, 0, 5] },
          comment: { font: 'NotoSansSC', fontSize: 9, italics: true, margin: [20, 0, 0, 5] },
          'advisor-info': { font: 'NotoSansSC', fontSize: 14, bold: true, decoration: 'underline' },
          footer: { font: 'NotoSansSC', fontSize: 8 },
        },
      }

      const docDefinition = {
        content: data.content,
        styles: data.styles,
        images: data.images,
        pageMargins: [60, 150, 60, 110], // 左、上、右、下、页边距
        header: () => ({
          image: 'top',
          fit: [631, 105],
        }),
        footer: () => ({
          alignment: 'justify',
          columns: [
            { columns: [
              [
                { image: 'footer', fit: [531, 22.5], margin: [60, 10, 60, 10] },
                {
                  margin: [60, 0, 60, 10],
                  columns: [
                    {
                      margin: [3, 0],
                      alignment: 'left',
                      text: `<EMAIL>`,
                      style: ['footer'],
                    },
                    {
                      margin: [10, 0, 0, 0],
                      alignment: 'center',
                      text: `500 5th Avenue \n18th Floor, New York, NY 10110`,
                      style: ['footer'],
                    },
                    {
                      margin: [0, 0, 26, 0],
                      text: 'www.capvision.com',
                      alignment: 'right',
                      link: 'https://www.capvision.com',
                      style: ['footer'],
                    },
                  ],
                },
                { image: 'footerBar', fit: [600, 30], margin: [0, 0, 0, 10] },
              ],
            ] },
          ],
        }),
      }

      return pdfMake.createPdf(docDefinition)
    },
    async preCallCA(generate_file, file_name) {
      const default_file_name = `${this.taskData.project.name} - ${this.taskData.advisor.name_prefix || ''} ${this.taskData.advisor.full_name} Pre-Call.pdf`
      const pdfDocGenerator = this.generatePreCaPDF()
      this.loading = true
      if (generate_file) {
        const formData = new FormData()
        let result
        await new Promise((resolve, reject) => {
          pdfDocGenerator.getBlob((blob) => {
            const pdfFile = new File([blob], file_name || default_file_name, { type: 'application/pdf' })
            formData.append('file', pdfFile)
            formData.append('uid', this.uid)
            resolve(formData)
          })
        }).then((data) => {
          result = ToolAPI.uploadFile(formData).then(data => {
            return { name: data.file_name, url: data.url }
          })
        })
        this.loading = false
        return result
      } else {
        pdfDocGenerator.download(default_file_name)
        this.loading = false
      }
    },
    postCallCA() {
      const ca_completed_date = Filters.momentFormat(this.taskData.latest_submitted_post_ca.create_at, 'MM/DD/YYYY')
      const task_completed_date = Filters.momentFormat(this.taskData.complete_time, 'MM/DD/YYYY')
      const task_completed_time = Filters.momentFormat(this.taskData.complete_time, 'HH:mm')

      const data = {
        content: [
          { text: 'Call conducted by: ', style: 'bold-info', margin: [0, 20, 30, 10] },
          { text: `${this.taskData.advisor.name_prefix || ''} ${this.taskData.advisor.full_name}`, font: 'NotoSansSC', fontSize: 14 },
          { text: this.caContent.title, style: 'header', decoration: 'underline', margin: [0, 100, 0, 40] },
          ...this.formatCAContent(),
          {
            alignment: 'justify',
            margin: [0, 80, 30, 15],
            columns: [
              {
                width: '70%',
                text: 'Date call completed:',
                style: ['bold-info'],
              },
              {
                width: '30%',
                text: 'Time call completed:',
                style: ['bold-info'],
              },
            ],
          },
          {
            alignment: 'justify',
            margin: [0, 10, 30, 10],
            columns: [
              {
                width: '70%',
                decoration: 'underline',
                text: task_completed_date,
                style: ['bold-info'],
              },
              {
                width: '30%',
                decoration: 'underline',
                text: task_completed_time,
                style: ['bold-info'],
              },
            ],
          },
          {
            alignment: 'justify',
            margin: [0, 40, 30, 15],
            columns: [
              {
                width: '70%',
                text: 'Date post-call statement completed:',
                style: ['bold-info'],
              },
              {
                width: '30%',
                text: 'Completed by:',
                style: ['bold-info'],
              },
            ],
          },
          {
            alignment: 'justify',
            margin: [0, 10, 30, 10],
            columns: [
              {
                width: '70%',
                decoration: 'underline',
                text: ca_completed_date,
                style: ['bold-info'],
              },
              {
                width: '30%',
                decoration: 'underline',
                text: `${this.taskData.advisor.name_prefix ? this.taskData.advisor.name_prefix + ' ' : ''}${this.taskData.advisor.full_name}`,
                style: ['bold-info'],
              },
            ],
          },
        ],
        images: {
          top: new URL(top, location.origin).href,
          footer: new URL(footer, location.origin).href,
          footerBar: new URL(footerBar, location.origin).href,
        },
        styles: {
          header: { font: 'NotoSansSC', fontSize: 20, bold: true, alignment: 'center', margin: [30, 10, 30, 10], decoration: 'underline' },
          body: { font: 'NotoSansSC', fontSize: 12 },
          textLeft: { alignment: 'left' },
          textRight: { alignment: 'right' },
          margin10: { margin: [10, 0, 10, 0] },
          question: { font: 'NotoSansSC', fontSize: 12, bold: true, margin: [0, 5, 10, 10] },
          'full-name': { font: 'NotoSansSC', bold: true, fontSize: 12 },
          'answer-text': { font: 'NotoSansSC', fontSize: 12, bold: true, margin: [15, 0, 0, 5] },
          comment: { font: 'NotoSansSC', fontSize: 10, italics: true, margin: [20, 0, 0, 5] },
          'bold-info': { font: 'NotoSansSC', fontSize: 14, bold: true },
          footer: { font: 'NotoSansSC', fontSize: 8 },
        },
      }

      const docDefinition = {
        content: data.content,
        styles: data.styles,
        images: data.images,
        pageMargins: [60, 150, 60, 110], // 左、上、右、下、页边距
        header: () => ({
          image: 'top',
          fit: [631, 105],
        }),
        footer: () => ({
          alignment: 'justify',
          columns: [
            { columns: [
              [
                { image: 'footer', fit: [531, 22.5], margin: [60, 10, 60, 10] },
                {
                  margin: [60, 0, 60, 10],
                  columns: [
                    {
                      margin: [3, 0],
                      alignment: 'left',
                      text: `<EMAIL>`,
                      style: ['footer'],
                    },
                    {
                      margin: [10, 0, 0, 0],
                      alignment: 'center',
                      text: `500 5th Avenue \n18th Floor, New York, NY 10110`,
                      style: ['footer'],
                    },
                    {
                      margin: [0, 0, 26, 0],
                      text: 'www.capvision.com',
                      alignment: 'right',
                      link: 'https://www.capvision.com',
                      style: ['footer'],
                    },
                  ],
                },
                { image: 'footerBar', fit: [600, 30], margin: [0, 0, 0, 10] },
              ],
            ] },
          ],
        }),
      }

      const pdfDocGenerator = pdfMake.createPdf(docDefinition)
      const pdf_name = `${this.taskData.project.name} - ${this.taskData.advisor.name_prefix || ''} ${this.taskData.advisor.full_name} post CA.pdf`
      pdfDocGenerator.download(pdf_name)

      return pdfDocGenerator
    },

    formatCAContent() {
      const list = []
      this.caContent.questions.map(item => {
        const title = `${item.sort_order + 1}. ${item.title_text}`
        list.push({ text: title, style: 'question' })

        if (item.type === 'TEXT') {
          list.push({ text: item.answer?.content?.result, style: 'answer-text' })
        } else {
          if (item.tags.includes('REQUIRE_ADVISOR_INPUT_FULLNAME')) {
            list.push({ text: `Full Name: ${item.answer?.content?.advisor_full_name}`, style: 'full-name' })
          }

          item.options.forEach((temp, index) => {
            const select = item.answer?.content?.result === index ? '(√)' : `(  )`
            const answer_text = ` ${select} ${temp.text}   ${this.getAnswerDesc(item.answer, index)}   ${this.getKmComment(item.answer, index)}`

            list.push({ text: answer_text, style: 'answer-text' })
          })

          if (item.answer?.content?.comment) {
            list.push({ text: item.answer.content.comment, style: 'comment' })
          }
        }
      })

      return list
    },
    formatCAHeading() {
      if (!this.isSGDB && this.taskData.client_id === 1233) {
        return [
          { text: `Name of Expert: ${this.taskData.advisor.full_name}`, style: 'question' },
          { text: `Subject Matter: ${this.taskData.project.name}`, style: 'question' },
        ]
      }
      return []
    },

    getAnswerDesc(answer, option_index) {
      let result = ''
      if (!answer.content || !answer.content.desc) return result
      if (typeof answer.content.desc !== 'string') {
        Object.keys(answer.content.desc)
          .forEach(function(key) {
            +key === option_index ? result = answer.content.desc[key] : ''
          })
      } else {
        answer.content.result === option_index ? result = answer.content.desc : ''
      }
      return result
    },

    getKmComment(answer, option_index) {
      let result = ''
      if (!answer.km_comment) return result
      if (typeof answer.km_comment.content !== 'string') {
        Object.keys(answer.km_comment.content)
          .forEach(function(key) {
            +key === option_index ? result = answer.km_comment.content[key] : ''
          })
      }
      return result
    },
  },
}
</script>

<style scoped>

</style>
