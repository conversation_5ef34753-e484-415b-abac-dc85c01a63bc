<template>
  <div>
    <table style="margin:0;">
      <tbody>
        <tr v-for="(item, index) in format_data" :key="index">
          <td style="padding:0cm 0cm 0cm 0cm">
            <table style="margin-top:0;margin-bottom: 5px;margin-left:0;margin-right: 0;">
              <tbody>
                <tr>
                  <td style="padding:0cm 0cm 0cm 0cm">
                    <p
                      style="font-size: 11pt;font-family: Calibri;margin-top:0px;white-space:pre-wrap;margin-right:0;margin-bottom:0px;margin-left:0;">
                      <span>{{ index + 1 }}.<span style="word-break: break-word;" v-html="item.title" /></span>
                      <span v-if="shortProfile && !['RANKING_SET','NO_RANKING_SET', 'CHECKBOX'].includes(item.type)">
                        <b>
                          <i v-if="item.answer_rank">{{ item.answer_rank }} </i>
                          <i v-html="$sanitizeHtml(item.answer_text)" />
                        </b>
                      </span>
                    </p>
                  </td>
                </tr>
                <tr v-if="shortProfile && ['RANKING_SET','NO_RANKING_SET','CHECKBOX'].includes(item.type)">
                  <td style="padding:0cm 0cm 0cm 0cm">
                    <div v-if="item.answer_text">
                      <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                        <div
                          v-for="(temp, temp_index) in item.answer_text"
                          :key="temp_index"
                          style="padding-left: 15px;font-size: 11pt;font-family: Calibri;margin-top:10px;margin-right:0;margin-bottom:0;margin-left:0;">
                          <div style="font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:3px;margin-left:0;">
                            <i v-if="temp.option">{{ temp.option }}</i></div>
                          <div style="font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
                            <b>
                              <i>
                                <span v-if="item.type === 'RANKING_SET'">{{ temp.rank }} -</span>
                                <span v-html="$sanitizeHtml(temp.text)" />
                              </i>
                            </b>
                          </div>
                        </div>
                      </div>
                      <div
                        v-else
                        style="padding-left: 15px;font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
                        <b>
                          <i v-if="item.answer_rank">{{ item.answer_rank }} </i>
                          <i v-html="$sanitizeHtml(item.answer_text)" />
                        </b>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr v-if="!shortProfile">
                  <td style="padding:0cm 0cm 0cm 0cm">
                    <div v-if="item.answer_text">
                      <div v-if="['RANKING_SET','NO_RANKING_SET'].includes(item.type)">
                        <div
                          v-for="(temp, temp_index) in item.answer_text"
                          :key="temp_index"
                          style="color:#6EA1D0;padding-left: 15px;font-size: 11pt;font-family: Calibri;margin-top:10px;margin-right:0;margin-bottom:0;margin-left:0;">
                          <p style="color: #333333;font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:3px;margin-left:0;">
                            <i v-if="temp.option">{{ temp.option }}</i></p>
                          <p style="font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0;margin-left:0;">
                            <i>
                              <span v-if="item.type === 'RANKING_SET'">{{ temp.rank }} -</span>
                              <span v-html="$sanitizeHtml(temp.text)" />
                            </i>
                          </p>
                        </div>
                      </div>
                      <p
                        v-else
                        style="color:#6EA1D0;padding-left: 15px;font-size: 11pt;font-family: Calibri;margin-top:0px;margin-right:0;margin-bottom:0px;margin-left:0;">
                        <i v-if="item.answer_rank">{{ item.answer_rank }} </i>
                        <i v-html="$sanitizeHtml(item.answer_text)" />
                      </p>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'SqQuestionCopy',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
    showAnswer: {
      type: Boolean,
      default: false,
    },
    shortProfile: {
      type: Boolean,
      default: false,
    },
    // 默认显示给客户看的问题
    showOnClientPortal: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      format_data: null,
    }
  },
  watch: {
    'showAnswer': {
      handler(newVal) {
        if (newVal) {
          this.formatAnswer()
        } else {
          this.format_data = this.data.qa_list_snapshot
        }
      }, immediate: true,
    },
  },

  methods: {
    formatAnswer() {
      const _data = _.cloneDeep(this.data)
      /* client_publish_question_ids
         null=>初始化全部可见
         [] => 全部不可见
         [1,3,4[ => sq id 1,3,4 可见
     * */
      if (this.data.client_publish_question_ids !== null) {
        _data.qa_list_snapshot = _data.qa_list_snapshot.filter(
          item => this.data.client_publish_question_ids.includes(item.id))
      }

      this.format_data = _data.qa_list_snapshot.map(item => {
        this.answerCheck(item)
        if (item.sub_questions) {
          item.sub_questions.forEach(sub => this.answerCheck(sub))
        }
        // 兼容旧数据 移除用户复制到编辑器中多余的部分
        // 2024-03-20起 db 编辑器中 粘贴部分只保存纯文本 => 问卷title含有的html只包含编辑器头部提供的部分
        item.title = item.title.replace('<p', '<span')
          .replace('</p>', '</span>')
          .replace(/<[^\/>][^>]*><\/[^>]*>/g, '') // 移除空标签
          .replace(/<\/div>\n(?!\n)<div>/g, '<\/div><br><div>') // 编辑器中的换行 以<\div>结束,添加\n后,以<div>开始下一行
          .replace(/<div>\n<div>/g, '')
          .replace(/<div/g, '<span')
          .replace(/<\/div>/g, '</span>')
          .replace(/class/g, 'data-class')
        return item
      })
    },

    answerCheck(item) {
      switch (item.type) {
        case 'TEXT_DESCRIPTION':
          item.answer_text = null
          break
        case 'NUMERIC':
          item.answer_text = item.answer ? item.answer.content.result : ''
          break
        case 'TEXT':
          item.answer_text = item.answer && item.answer.content ? item.answer.content.result.replace(/\n/gi, '<br/>') : ''
          break
        case 'RANKING_WITH_EXPLANATION':
          item.answer_text = item.answer && item.answer.content ? item.answer.content.result : ''
          item.answer_rank = item.answer && item.answer.content ? item.answer.content.rank : ''
          break
        case 'RANKING_SET':
        case 'NO_RANKING_SET':
          item.answer_text = item.answer && item.answer.content ? item.answer.content.result : ''
          break
        case 'CHECKBOX':
          item.answer_text = checkboxAnswer(item)
          break
        case 'RADIO':
          item.answer_text = radioAnswer(item)
          break
        case 'YES_NO':
          item.answer_text = radioAnswer(item)
          break
      }

      function checkboxAnswer(item) {
        let result = null
        if (item.answer && item.answer.content && item.answer.content.result && item.answer.content.result.length) {
          result = item.answer.content.result.map((ans) => {
            const desc = item.answer.content?.desc?.[ans] ? ` (${item.answer.content.desc[ans]})` : ''
            return item.options[ans].text + desc.replace(/\n/gi, '<br/>')
          })
            .join('<br/> ')
        }
        return result
      }

      function radioAnswer(item) {
        let result = null
        if (item.answer &&
          (item.answer.content.result || item.answer.content.result === 0)) {
          result = item.options[item.answer.content.result].text
          result += item.answer.content.desc ? ' (' + item.answer.content.desc.replace(/\n/gi, '<br/>') + ')' : ''
        }
        return result
      }

      return item
    },
    subCheckSort(sub, order) {
      return sub.display_order ? String.fromCharCode(order + 97) : null
    },
  },
}
</script>

<style scoped>

</style>
