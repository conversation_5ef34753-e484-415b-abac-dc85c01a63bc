<template>
  <div contenteditable="false">
    <table border="0"
           cellpadding="0"
           cellspacing="0"
           style="width:100%;border-collapse: collapse;border-spacing:0;border:none;">
      <thead>
        <tr>
          <th />
          <th />
          <th>
            <div
              v-if="data.title"
              style="width:100%;margin-bottom:1em;margin-top:1em;text-align: center;font-size: 1.5em;font-weight: bold;">
              {{ data.title }}
            </div>
          </th>
        </tr>
        <tr v-if="data.description">
          <th />
          <th />
          <th>
            <div
              v-if="data.description"
              style="width:100%;border-bottom: 1px solid #E0E0E0;padding-bottom: 5px;font-size: 1rem;margin-bottom:1em;font-weight: 400;text-align: center;">
              {{ data.description }}
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="investmentTarget">
          <td style="width:99%;vertical-align: top;padding-left:2px;" colspan="3">
            <div style="width:100%;font-size: 1rem;margin-bottom:1em;font-weight: 400;text-align: left;" v-html="$sanitizeHtml(investmentTarget)" />
          </td>

        </tr>
        <template v-for="item in question_list">
          <slot v-if="item.sub_questions" />
          <slot v-else>
            <tr :key="'order-' + item.id" style="padding-bottom:10px;word-break: break-all;">
              <td style="white-space:nowrap !important;vertical-align: top;text-align: right;" />
              <td style="white-space:nowrap !important;vertical-align: top;text-align: right;">
                <div
                  style="font-family: Calibri;vertical-align: baseline;">{{ item.sort_order + 1 + '. ' }}
                </div>
              </td>
              <td style="width:97%;vertical-align: top;padding-left:2px;">
                <div style="vertical-align: baseline;white-space:pre-wrap;font-family: Calibri;"
                     v-html="changeTitle(item.title)" />
              </td>
            </tr>
            <tr v-if="item.type !== 'TEXT_DESCRIPTION'" :key="'desc-' + item.id">
              <td />
              <td />
              <td
                v-if="item.type === 'RADIO'"
                style="word-break: break-all;padding-bottom:10px;padding-left:15px;">
                <div v-if="item.tags.includes('REQUIRE_ADVISOR_INPUT_FULLNAME')">
                  <b>Full Name: </b> <span v-if="item.answer && item.answer.content">{{item.answer.content.advisor_full_name}}</span>
                </div>
                <div
                  v-for="(option, indexKey) in item.options"
                  :key="indexKey"
                  style="max-width: 90%;padding-bottom:5px;font-size: 10pt;">
                  <span
                    v-if="item.answer && item.answer.content && item.answer.content.result === indexKey"
                    style="padding-right:10px;background-color:#bbd0e6;padding-left:5px;">( √ )
                    <span
                      style="word-break: break-all;background-color:#bbd0e6;padding-right:5px;">{{ option.text }}</span>
                  </span>
                  <span
                    v-if="!item.answer || !item.answer.content || item.answer.content.result !== indexKey"
                    style="padding-right:10px;padding-left:5px;">( &nbsp;&nbsp; )
                    <span style="word-break: break-all;">{{ option.text }}</span>
                  </span>
                  <span
                    v-if="option.description"
                    style="font-size: 9pt;">{{ getAnswerDesc(item.answer, indexKey) }}</span>
                  <span
                    v-if="getKmComment(item.answer,indexKey)"
                    style="background-color: #fbd7a3;padding-right:4px;padding-left:4px;padding-top:2px;padding-bottom:2px;color: #000;margin-left:5px;">{{
                      getKmComment(item.answer, indexKey)
                    }}</span>
                </div>
                <div v-if="item.answer && item.answer.content && item.answer.content.comment">
                  <span
                    style="background-color: #fbd7a3;font-size: 9pt;padding-right:4px;padding-left:4px;padding-top:2px;padding-bottom:2px;color: #000;margin-left:5px;">
                    {{ item.answer.content.comment }}
                  </span>
                </div>
              </td>
              <td
                v-if="item.type === 'TEXT'"
                style="word-break: break-all;max-width: 90%;padding-bottom:10px;padding-left:15px;">
                <span v-if="item.answer && item.answer.content" style="color:#6aa7e7;">
                  {{ item.answer.content.result }}
                </span>
              </td>
            </tr>
          </slot>
        </template>
      </tbody>
    </table>
  </div>

</template>

<script>
export default {
  name: 'CACopy',
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      },
    },
    investmentTarget: {
      type: String,
      default: null,
    },
  },

  data() {
    return {
      question_list: this.data.qa_list_snapshot || this.data.questions,
    }
  },
  watch: {
    'data': {
      handler(newVal) {
        if (newVal) {
          this.question_list = this.data.qa_list_snapshot || this.data.questions
          console.log(this.question_list)
        }
      },
    },
  },
  methods: {
    changeTitle(title) {
      return title.replace(/\n/g, '<br>')
    },
    getAnswerDesc(answer, option_index) {
      let result = ''
      if (!answer?.content || !answer.content.desc) return result
      if (typeof answer?.content.desc !== 'string') {
        Object.keys(answer.content.desc)
          .forEach(function(key) {
            +key === option_index ? result = answer.content.desc[key] : ''
          })
      } else {
        answer.content.result === option_index ? result = answer.content.desc : ''
      }
      return result
    },
    getKmComment(answer, option_index) {
      let result = ''
      if (!answer?.km_comment) return result
      if (typeof answer?.km_comment?.content !== 'string') {
        Object.keys(answer?.km_comment?.content)
          .forEach(function(key) {
            +key === option_index ? result = answer?.km_comment?.content[key] : ''
          })
      }
      return result
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep a {
  text-decoration: revert;
}
</style>
