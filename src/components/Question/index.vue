<template>
  <div v-if="createQuestion" class="question-content">
    <div class="show-box">
      <item-show :form="form" />
    </div>

    <!--    edit box-->
    <el-form ref="form" v-loading="loading" :model="form" :rules="rules" label-width="80px" class="edit-box">
      <el-form-item label="Question" prop="title" class="w-percent-80">
        <tinymce v-model="form.title" :height="100" :toolbar="toolbar" />
      </el-form-item>
      <el-form-item label="Type">
        <el-radio-group v-model="form.type" fill="#13ce66">
          <el-radio-button
            v-for="item in question_type"
            :key="item.value"
            :label="item.value">
            {{ item.name }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!notSort" label="Sort">
        <el-switch v-model="form.sort" active-color="#13ce66" />
      </el-form-item>
      <div v-if="form.type !== 'TEXT_DESCRIPTION'">
        <el-form-item label="Required" class="inline-block">
          <el-switch v-model="form.is_required" active-color="#13ce66" />
        </el-form-item>
        <el-form-item v-if="formTypeCheck()" prop="options">
          <div v-for="(item, index) in form.options" :key="index" class="option-edit-box">
            <el-input
              v-model="item.text"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 4}"
              placeholder="option"
              class="option-input" />
            <el-select v-model="item.type" style="width:80px;">
              <el-option
                v-for="i in options_type"
                :key="i.value"
                :label="i.name"
                :value="i.value" />
            </el-select>
            <div class="option-actions">
              <el-tooltip class="item" content="Allow comments" placement="top-start" :open-delay="300">
                <span class="el-icon-paperclip ml-8" @click="optionAddDesc(item)" />
              </el-tooltip>
              <span class="el-icon-circle-plus-outline ml-8 item" @click="addOption(index)" />
              <span class="el-icon-remove-outline ml-8 item" @click="removeOption(index)" />
              <span v-if="index !== 0" class="el-icon-top item" @click="upOption(index)" />
              <span v-if="index !== form.options.length - 1" class="el-icon-bottom item" @click="downOption(index)" />
            </div>
            <div v-if="item.description">
              <el-input
                type="textarea"
                disabled
                :autosize="{ minRows: 2, maxRows: 4}"
                placeholder="Allow respondents to add comments"
                class="option-description" />
              <el-link :underline="false" class="el-icon-delete" @click="cancelDesc(item)" />
            </div>
          </div>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click.stop="submitForm('form')">Save</el-button>
        <el-button type="text" @click.stop="cancelEdit">Cancel</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import _ from 'lodash'
import ItemShow from './ItemShow'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'QuestionIndex',
  components: { Tinymce, ItemShow },
  props: {
    editQuestion: {
      type: Object,
      default: () => {
        return {}
      },
    },
    qType: {
      type: String,
      default: 'RADIO',
    },
    notSort: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    const validateOption = (rule, value, callback) => {
      if (['RADIO', 'CHECKBOX'].indexOf(this.form.type) > -1) {
        const list = value.filter(item => item.text)
        if (list.length < 2) {
          callback(new Error('Options must have two or more!'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      toolbar: ['undo redo | bold italic | alignleft  aligncenter alignright | numlist bullist outdent indent'],
      loading: false,
      createQuestion: true,
      form: {
        title: '',
        sort: true,
        type: 'RADIO',
        options: [
          { text: '', type: 'ALLOW', description: false },
          { text: '', type: 'DENY', description: false },
        ],
        is_required: false,
      },
      rules: {
        title: [
          { required: true, message: 'Question content is required', trigger: 'blur' },
        ],
        options: [
          { validator: validateOption, trigger: ['blur'] },
        ],
      },
      question_type: [
        // { name: 'Yes/No', value: 'YES_NO' },
        { name: 'The radio', value: 'RADIO' },
        { name: 'Multiple choice', value: 'CHECKBOX' },
        { name: 'Number response', value: 'NUMERIC' },
        { name: 'Text response', value: 'TEXT' },
        { name: 'Text description', value: 'TEXT_DESCRIPTION' },
      ],
      options_type: [
        { name: 'Allow', value: 'ALLOW' },
        { name: 'Hold', value: 'HOLD' },
        { name: 'Deny', value: 'DENY' },
      ],
    }
  },

  created() {
    this.form.type = this.qType
    if (this.qType === 'TEXT_DESCRIPTION') {
      this.form.sort = false
    }
    this.loading = true
    if (this.editQuestion.edit) {
      this.form = Object.assign({}, this.form, this.editQuestion)
      this.form.edit = false
    }
    this.loading = false
  },

  methods: {
    optionAddDesc(item) {
      this.$set(item, 'description', true)
    },

    cancelDesc(item) {
      this.$set(item, 'description', false)
    },

    upOption(index) {
      const arr = this.form.options
      arr[index] = arr.splice(index - 1, 1, arr[index])[0]
    },

    downOption(index) {
      const arr = this.form.options
      arr[index] = arr.splice(index + 1, 1, arr[index])[0]
    },

    removeOption(index) {
      this.form.options.splice(index, 1)
    },

    addOption(index) {
      this.form.options.splice(index + 1, 0, { text: '', type: 'ALLOW', description: false })
    },

    submitForm(formName) {
      if (!this.optionsCheck()) return
      const data = Object.assign({}, this.form)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.editQuestion.edit ? this.$emit('update', data) : this.$emit('save', data)
          this.createQuestion = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    cancelEdit() {
      if (this.editQuestion.edit) {
        const data = this.editQuestion
        data.edit = false
        this.$emit('update', data)
      } else {
        this.$emit('save', {})
      }
    },

    optionsCheck() {
      if (!this.formTypeCheck(this.form.type)) return true

      const text_list = this.form.options.map(item => item.text)

      if (_.uniq(text_list).length !== text_list.length) {
        this.$notify({
          title: 'Warn', message: 'You may have two of the same options', type: 'warning',
        })
        return false
      } else {
        return true
      }
    },

    formTypeCheck() {
      return (['RADIO', 'CHECKBOX'].includes(this.form.type))
    },
  },

}
</script>

<style lang="scss" scoped>
  .w-percent-80 {
    width: 80%;
  }

  .question-content {
    padding: 10px;
  }

  .show-box {
    min-height: 150px;
    position: relative;
  }

  .edit-box {
    position: relative;
    padding: 20px 0;
    border-top: 1px solid #dfe4e6;
    background-color: #fafafa;
    user-select: none;

    .option-edit-box {
      font-size: 1.2rem;
      margin: 10px 0;

      .option-input {
        max-width: 50%;
        padding-right: 10px;
        display: inline-block;
      }

      .option-description {
        max-width: 50%;
        margin-top: 5px;
        padding-right: 10px;

        ::v-deep .el-textarea__inner {
          background-color: #f9f9f9;
        }
      }

      .option-actions {
        display: inline-block;
        vertical-align: bottom;

        .item:hover {
          cursor: pointer;
          color: #409EFF;
        }
      }
    }

    ::v-deep .el-radio-button__inner {
      border-left: 1px solid #DCDFE6;
      border-radius: 4px;
    }

    ::v-deep .el-radio-button {
      margin-right: 20px;
    }

    ::v-deep .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 4px;
    }

    ::v-deep .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 4px;
    }

    ::v-deep .mce-statusbar {
      display: none;
    }

  }

</style>
