<template>
  <el-dialog
    v-loading="loading"
    :visible.sync="CADetailVisible"
    :close-on-click-modal="false"
    :title="title"
    append-to-body
    class="questionnaire-container"
    width="50%"
    @close="dialogClose"
  >
    <div class="mb-8">
      <el-tag v-if="ca_status === 'REJECTED'" type="danger">Rejected</el-tag>
      <div v-if="ca_status !== 'REJECTED' && has_answers" class="inline-block">
        <el-tag type="success" class="copy-tag" @click="copyCA()">Copy</el-tag>
      </div>
      <el-tag class="copy-tag" @click="downloadCA()">Download Html</el-tag>
      <CaPdf ref="CAPdf" :ca-content="copy_data" :task-data="taskData" :ca-type="caType" />
      <el-button
        v-if="isExportedToCN"
        class="el-button--xs"
        type="primary"
        :loading="send_CA_loading"
        @click="sendCaToSupport()">{{'Send CA To Support Team'}}</el-button>

      <div v-if="ca_status === 'AUTO_HOLD'" class="inline-block">
        <el-tag type="warning">Hold</el-tag>
        <span class="ml-8"><span class="warning"> {{ hold_ques }} </span>need to be reviewed.</span>
      </div>
    </div>
    <div v-if="isWebLinkMode">
      <div class="question-subject">
        <div class="title">{{ name }}</div>
      </div>
      <div class="custom-dialog-body scrollbar-narrow">
        <div
          v-for="(item, index) in question_list"
          :id="`question-`+index"
          :key="index"
          class="question-box"
        >
          <show-question :form="item" :show-answer="has_answers" :class="getAnswerTypeColor(item)" />
        </div>
      </div>
    </div>

    <div v-if="isEmailFileMode">
      <div v-for="item in files" :key="item.id">
        <el-link class="mt-8" type="primary" :href="item.path">
          <i class="el-icon-download" /> {{ item.name }}
        </el-link>
      </div>

    </div>

    <ca-copy
      v-if="copy_data.title"
      id="ca-copy"
      style="display: none;"
      :data="copy_data" />

  </el-dialog>
</template>

<script>
import CaCopy from './CACopy'
import CaPdf from './CAPDF'
import showQuestion from '@/views/compliance/client-agreement/components/showQuestion'
import ProjectAPI from '@/api/project'
import Outsource from '@/api/outsource'
import _ from 'lodash'

export default {
  name: 'CAQuestionShowV2',
  components: { showQuestion, CaCopy, CaPdf },
  props: {
    taskCaId: {
      type: Number,
      default: null,
    },
    taskId: {
      type: Number,
      default: null,
    },
    taskData: {
      type: Object,
      default: () => { return {} },
    },
    caType: {
      type: String,
      default: 'pre-call',
    },
  },

  data() {
    return {
      loading: false,
      send_CA_loading: false,
      generate_link_loading: false,
      instance_id: null,
      CADetailVisible: !!this.taskCaId,
      name: '',
      title: 'CA Detail',
      ca_status: '',
      hold_ques: '',
      description: '',
      question_list: [],
      has_answers: false,
      files: [],
      file_name: '',
      file_url: undefined,

      // km copy
      copy_data: {
        title: '',
        description: '',
        questions: [],
      },

    }
  },
  computed: {
    isExportedAndSelected() {
      return this.taskData && this.taskData.task_outsource_status === 'EXPORTED'
      // && !!this.taskData.task_outsource_info?.task_outsource_general_status === 'SELECTED'
    },
    isExportedToCN() {
      return this.isExportedAndSelected && this.taskData.task_outsource_info.outsource_advisor_to === 'CN'
    },
    isEmailFileMode() {
      return this.files.length > 0
    },
    isWebLinkMode() {
      return this.files.length < 1
    },
  },
  watch: {
    'taskCaId': {
      handler(newVal) {
        if (newVal) {
          this.init()
        }
      },
    },
  },
  methods: {
    init() {
      this.loading = true
      this.files = []
      this.has_answers = false

      return ProjectAPI.getTaskDetailCa(this.taskId, this.taskCaId)
        .then(data => {
          if (data.method !== 'PORTAL') {
            this.files = data.files || []
            return
          }

          const inquiry = _.cloneDeep(data.inquiry_instance)

          this.instance_id = data.inquiry_instance_id
          this.has_answers = inquiry.qa_list_snapshot.filter(item => !!item.answer).length > 0

          this.question_list = inquiry.qa_list_snapshot.map(item => {
            item.answer = !item.answer ? '' : item.answer.content
            return item
          })

          this.checkStatus(inquiry)
          this.name = inquiry.branch_snapshot.title
          this.copy_data = {
            title: this.name,
            questions: data.inquiry_instance.qa_list_snapshot,
          }
        })
        .finally(() => {
          this.loading = false
          this.CADetailVisible = true
        })
    },

    checkStatus(data) {
      if (['AUTO_REJECTED', 'REJECTED'].includes(data.status)) {
        this.ca_status = 'REJECTED'
      }
      if (data.status === 'AUTO_HOLD') {
        this.ca_status = 'AUTO_HOLD'
        this.getHoldQuestionOrder()
      }
    },

    getHoldQuestionOrder() {
      const result = []
      this.question_list.forEach(item => {
        if (item.sub_questions) {
          item.sub_questions.forEach(sub => {
            if (['RADIO', 'CHECKBOX'].includes(sub.type)) {
              sub.options[sub.answer.result].type === 'HOLD' ? result.push(item.display_order) : ''
            }
          })
        } else {
          if (item.type === 'RADIO') {
            item.options[item.answer.result].type === 'HOLD' ? result.push(item.sort_order + 1) : ''
          }
          if (item.type === 'CHECKBOX') {
            const has_hold_option = item.options.some((i, index) => item.answer.result.includes(index) && i.type === 'HOLD')
            has_hold_option ? result.push(item.sort_order + 1) : ''
          }
        }
      })
      this.hold_ques = result.join()
    },

    getAnswerTypeColor(item) {
      if (!this.has_answers) return
      if (!['RADIO', 'CHECKBOX', 'YES_NO'].includes(item.type)) return
      let color
      const answer_type = item.options[+item.answer.result].type
      switch (answer_type) {
        case 'DENY':
          color = 'danger'
          break
        case 'HOLD':
          color = 'warning'
          break
        case 'ALLOW':
          color = 'success'
          break
      }
      return 'bgc-' + color
    },

    dialogClose() {
      this.copy_data = {
        title: '',
        description: '',
        questions: [],
      }
      this.$emit('close')
    },

    copyCA() {
      // create element for copy CA html & remove the element after copied
      const temp_el = document.createElement('div')

      const copyText = document.querySelector('#ca-copy').innerHTML
      temp_el.innerHTML = `<div style="width:800px;padding-top:10px;padding-bottom:10px;padding-left:50px;padding-right:50px;border:1px solid #c3c7bb;border-radius: 5px;">` +
        copyText + `</div>`

      document.body.appendChild(temp_el)
      const range = document.createRange()
      range.selectNode(temp_el)
      window.getSelection()
        .removeAllRanges() // clear current selection
      window.getSelection()
        .addRange(range) // to select text

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message({
          message: 'Copy successful',
          type: 'success',
        })
      } else {
        this.$message({
          message: 'Copy failed',
          type: 'error',
        })
      }

      window.getSelection()
        .removeAllRanges()// clear selection
      document.body.removeChild(temp_el)
    },

    downloadCA() {
      const eleLink = document.createElement('a')
      eleLink.download = this.taskData.project.name + '-' + this.taskData.advisor.name_prefix + this.taskData.advisor.full_name + ' CA.html'
      eleLink.style.display = 'none'

      const downloadText = document.querySelector('#ca-copy').innerHTML
      const content = `<div style="width:800px;padding-top:10px;padding-bottom:10px;padding-left:50px;padding-right:50px;border:1px solid #c3c7bb;border-radius: 5px;">` +
        downloadText + `</div>`
      // 字符内容转变成blob地址
      const blob = new Blob([content])
      eleLink.href = URL.createObjectURL(blob)
      // 触发点击
      document.body.appendChild(eleLink)
      eleLink.click()
      document.body.removeChild(eleLink)
    },

    async  sendCaToSupport() {
      this.send_CA_loading = true
      const params = { task_id: this.taskId }

      await this.$refs.CAPdf.preCallCA(true, '')
        .then(data => {
          params.file_name = data.name
          params.file_url = data.url
        }, () => {
          this.$message.error('Failed to upload CA PDF.')
        })
      await Outsource.sendCaToSupport(params)
        .then(() => {
          this.$message.success('Success')
        })
        .finally(() => {
          this.send_CA_loading = false
        })
    },

  },
}
</script>

<style scoped lang="scss">
.custom-dialog-body {
  max-height: 500px;
  margin-bottom: 20px;
  overflow: auto;
}

::v-deep .el-dialog__body {
  padding-top: 0;
}

.show-dialog {
  min-height: 500px;
}

.bgc-warning {
  background-color: #fdf6ec;
  padding: 10px 0;
}

.bgc-danger {
  background-color: #f17c7c26;
  padding: 10px 0;
}

.bgc-success {
  /*background-color: #dff1d654;*/
  padding: 10px 0;
}

.copy-tag {
  margin-right: 0.5rem;

  &:hover {
    cursor: pointer;
  }
}

::v-deep a {
  text-decoration: revert;
}
</style>
