<template>
  <div>
    <div ref="max_height" class="read-more-box">
      {{ data }}
    </div>
    <div v-if="text_over" class="more-btn">
      <el-link v-if="!text_more" :underline="false" type="primary" @click="showMore">More</el-link>
      <el-link v-if="text_more" :underline="false" type="primary" @click="showCollapse">Collapse</el-link>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReadMore',
  props: {
    height: Number,
    data: String,
  },

  data() {
    return {
      text_over: false,
      text_more: false,
    }
  },

  mounted() {
    const text_content = this.$refs.max_height
    text_content.style.maxHeight = this.height + 'px'
    const scrollHeight = text_content.scrollHeight
    this.$set(this, 'text_over', scrollHeight > this.height + 1)
    // el-table cell  line_height -> 23
  },

  methods: {
    showMore() {
      this.text_more = true
      const text_content = this.$refs.max_height
      text_content.style.maxHeight = text_content.scrollHeight + 'px'
    },

    showCollapse() {
      this.text_more = false
      const text_content = this.$refs.max_height
      text_content.style.maxHeight = this.height + 'px'
    },
  },
}
</script>

<style scoped>
  .read-more-box {
    overflow-y: hidden;
  }

  .more-btn {
    float: right;
    padding-right: 4px;
  }
</style>
