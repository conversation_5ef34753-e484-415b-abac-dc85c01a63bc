<template>
  <div class="position-relative">
    <el-input
      v-if="type === 'text'"
      v-model="input_value"
      :placeholder="itemPlaceholder"
      class="filter-item w-200px"
      @input="change"
    />
    <slot v-if="type === 'textarea'">
      <el-input
        v-model="input_value"
        :placeholder="itemPlaceholder"
        class="filter-item w-200px"
        @input="change"
      >
        <el-link slot="suffix" type="primary" title="Edit NPI #" @click="openTextareaDialog">
          <i class="el-input__icon el-icon-edit-outline" />
        </el-link>
      </el-input>
      <el-dialog
        v-if="type === 'textarea'"
        :visible.sync="textarea_dialog_visible"
        :title="`Edit ${itemPlaceholder}`"
        @closed="syncTextareaValue"
      >
        <el-input
          v-model="textarea_value"
          type="textarea"
          :placeholder="itemPlaceholder"
          :rows="20"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="textarea_dialog_visible = false">Close</el-button>
        </span>
      </el-dialog>
    </slot>
    <el-select
      v-if="type === 'select'"
      v-model="input_value"
      :multiple="isMultiple"
      :options="itemOptions"
      clearable
      class="filter-item w-200px"
      :placeholder="itemPlaceholder"
      filterable
      :allow-create="allowCreate"
      :default-first-option="allowCreate"
      @change="change"
    >
      <el-option
        v-for="item in itemOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-checkbox
      v-if="type === 'checkbox'"
      v-model="input_value"
      class="filter-item"
      :class="{'w-408px':itemKey === 'tc_status'}"
      @change="change">
      {{ itemPlaceholder }}
    </el-checkbox>
    <div v-if="isDate" class="inline-block">
      <el-date-picker
        v-model="range_value[0]"
        type="month"
        format="MM/yyyy"
        :disabled="range_start_disabled"
        class="filter-item w-200px"
        placeholder="start date"
        value-format="yyyy-MM"
        @change="dateChange"
      />
      <el-date-picker
        v-model="range_value[1]"
        type="month"
        format="MM/yyyy"
        :disabled="range_end_disabled"
        class="filter-item w-200px"
        placeholder="end date"
        value-format="yyyy-MM"
        @change="dateChange"
      />
    </div>
    <location
      v-if="type === 'location'"
      v-model="location_id"
      :placeholder="itemPlaceholder"
      region
      class="filter-item w-200px"
      @change="emit"
    />
    <div v-if="type === 'num'" class="filter-item  w-200px">
      <el-input v-model="num_range" :placeholder="itemPlaceholder" @input="change" />
    </div>
    <user-search
      v-if="type === 'userid'"
      v-model="input_value"
      class="filter-item w-200px"
      :placeholder="itemPlaceholder"
      clearable
      @change="change"
    />
    <search-industry
      v-if="type === 'industry'"
      v-model="input_value"
      multiple
      class="filter-item w-200px"
      clearable
      @change="change"
    />
    <top-advisor-tags-selector
      v-if="type === 'user_advisor_tags'"
      v-model="user_advisor_tags"
      :append-to-body="true"
      class="filter-item w-200px"
      clearable
      @change="emit"
    />
  </div>
</template>

<script>
import Location from '@/components/Location'
import UserSearch from '@/components/SelectRemoteSearch/UserSearch'
import SearchIndustry from '@/components/SelectRemoteSearch/IndustrySearch'
import TopAdvisorTagsSelector from '@/components/ShoppingCart/components/TopAdvisors/components/TopAdvisorTagsSelector'

export default {
  name: 'SearchItem',
  components: {
    Location,
    UserSearch,
    SearchIndustry,
    TopAdvisorTagsSelector,
  },
  model: {
    prop: 'model',
    event: 'change',
  },
  props: {
    itemType: {
      type: String,
      default: 'text',
    },
    itemPlaceholder: {
      type: String,
      default: '',
    },
    itemKey: {
      type: String,
      default: '',
    },
    itemOptions: {
      type: Array,
      default: () => [],
    },
    model: {
      type: [String, Number, Array, Object, Boolean],
      default: '',
    },

  },
  data() {
    return {
      textarea_dialog_visible: false,
      input_value: '',
      textarea_value: '',
      range_value: ['', ''],
      range_end_disabled: false,
      range_start_disabled: false,
      location_id: [],
      num_range: '',
      user_advisor_tags: [],
    }
  },
  computed: {
    type() {
      return this.itemType.split('-')[0]
    },
    isMultiple() {
      return this.itemType.split('-')[1] === 'multiple'
    },
    isSlider() {
      return this.itemType.split('-')[1] === 'slider'
    },
    isDate() {
      return this.itemType.split('-')[1] === 'date'
    },
    isPeriod() {
      return this.itemKey.split('_')[1] === 'period'
    },
    allowCreate() {
      return this.itemType.split('-')[2] === 'allowCreate'
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.isPeriod && this.type === 'select') {
        this.input_value = this.model.input_value
        this.range_value = this.model.range_value
        const target_option = this.itemOptions.find(item => item.value === this.input_value)
        this.range_end_disabled = target_option['end_disabled']
        this.range_start_disabled = target_option['start_disabled']
      } else if (this.isDate && typeof this.model === 'object') {
        this.input_value = this.model.input_value
        this.range_value = this.model.range_value
      } else if (this.type === 'num' && typeof this.model === 'object') {
        this.num_range = this.model.join('-')
      } else if (this.type === 'location' && typeof this.model === 'object') {
        this.location_id = this.model
      } else if (this.type === 'user_advisor_tags' && typeof this.model === 'object') {
        this.user_advisor_tags = this.model
      } else {
        this.input_value = this.model
      }
    })
  },
  methods: {
    change() {
      if (this.isPeriod && this.type === 'select') {
        const target_option = this.itemOptions.find(item => item.value === this.input_value)
        this.range_value = target_option ? target_option['range_value'] : ['', '']
        this.range_end_disabled = target_option ? target_option['end_disabled'] : false
        this.range_start_disabled = target_option ? target_option['start_disabled'] : false
        this.emit({
          input_value: this.input_value,
          range_value: this.range_value,
        })
        return
      }

      if (this.isDate) {
        this.emit({
          input_value: this.input_value,
          range_value: this.range_value,
        })
        return
      }

      if (this.type === 'num') {
        const range = this.num_range ? this.num_range.split('-') : null
        this.emit(range)
        return
      }
      this.emit(this.input_value)
    },

    dateChange() {
      if (this.isPeriod) {
        this.emit({
          input_value: this.input_value,
          range_value: this.range_value,
        })
        return
      }

      if (!this.input_value) {
        return
      }
      this.emit({
        input_value: this.input_value,
        range_value: this.range_value,
      })
    },

    emit(val) {
      this.$emit('change', val)
      this.$emit('update')
    },

    openTextareaDialog() {
      this.textarea_value = this.input_value.split(' ').join('\n')
      this.textarea_dialog_visible = true
    },

    syncTextareaValue() {
      this.input_value = this.textarea_value.split('\n').join(' ')
      this.change()
    },
  },
}
</script>

<style scoped lang="scss">
.position-relative {
  /*position: relative;*/
  display: inline-block;
  /*padding-right: 5px;*/
}

.item-slider {
  display: block;
  height: 0;
  position: absolute;
  top: 12px;
  // fix=>filter item margin 4px
  left: 4px;
  width: calc(100% - 5px);

  ::v-deep .el-slider__runway {
    height: 4px;
    margin: 18px 0;
  }

  ::v-deep .el-slider__bar {
    height: 4px;
  }

  ::v-deep .el-slider__button-wrapper {
    top: -16px;
  }

  ::v-deep .el-slider__button {
    width: 14px;
    height: 14px;

    &:hover {
      transform: scale(1.1);
    }
  }
}

::v-deep .el-dialog__title {
  word-spacing: normal;
}
</style>
