<template>
  <el-button class="btn-refresh" :class="{'no-border': noBorder}" @click="rotate">
    <el-icon name="refresh" :class="{'rotate': isRotate}" />
  </el-button>
</template>

<script>
export default {
  name: 'Refresh<PERSON>utton',
  props: {
    noBorder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isRotate: false,
    }
  },

  methods: {
    rotate() {
      this.isRotate = true
      this.$emit('action')
      setTimeout(() => {
        this.isRotate = false
      }, 1000)
    },
  },

}
</script>

<style scoped lang="scss">
  .btn-refresh {
    padding: 0;
    width: 28px;
    height: 28px;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .rotate {
    transform: rotate(-360deg);
    transition: all 1s;
  }

  .no-border {
    border: none;
    &:hover {
      border: 1px solid #DCDFE6;
    }
  }
</style>
