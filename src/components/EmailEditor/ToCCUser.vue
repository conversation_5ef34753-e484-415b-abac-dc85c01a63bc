<template>
  <div class="email-box">
    <el-select
      ref="newTag"
      v-model="input_value"
      class="select-no-border"
      filterable
      multiple
      :loading="search_loading"
      default-first-option
      placeholder=""
      remote
      :remote-method="(query)=>{searchEmailAddress(query)}"
      @change="handleSelect"
    >
      <el-option
        v-for="(item, index) in search_options"
        :key="`${item.email }-${index}`"
        :value="item.email">
        {{ item.label_name }}
      </el-option>
    </el-select>
  </div>
</template>

<script>
import ToolAPI from '@/api/tool'
import _ from 'lodash'

export default {
  name: 'ToCCUser',
  props: {
    originList: Array,
    originAddresses: Array,
    options: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      input_value: [],
      search_options: [],
      origin_list_options: [],
      search_loading: false,
    }
  },

  watch: {
    'originList': {
      handler() {
        this.input_value = _.uniq(this.originList)
        this.search_options = this.formatOption(this.originList.concat(this.options))
        this.origin_list_options = _.uniq(this.origin_list_options.concat(this.originList))
      },
      immediate: true,
    },
  },

  methods: {
    searchEmailAddress(val) {
      if (val === '') return
      this.search_loading = true
      const params = {
        type: 'USER',
        or: [
          { name_contains: encodeURIComponent(val) },
          { email_contains: encodeURIComponent(val) },
        ],
      }

      return ToolAPI.searchMailAddress(params).then(data => {
        this.search_options = this.formatOption(this.options.concat(data).concat(this.origin_list_options))
      }).finally(() => {
        this.search_loading = false
      })
    },

    handleSelect() {
      this.$emit('update:originList', this.input_value)
    },

    formatOption(list) {
      if (!list) return []

      return _.uniqBy(list.map(item => {
        if (item.name) {
          item.label_name = item.name + ' <' + item.email + '>'
          return item
        } else if (item.email) {
          item.label_name = '<' + item.email + '>'
          return item
        } else {
          return { email: item, label_name: '<' + item + '>' }
        }
      }), 'email')
    },

  },
}
</script>

<style scoped lang="scss">
.email-box{
  display: flex;
  border-radius: 5px;
  border:1px solid #c8c8c8;
}

.select-no-border{
  flex: 1;

  ::v-deep .el-input__inner{
    border:none;
    padding:0 8px;
  }
  ::v-deep .el-input__suffix-inner{
    display: none;
  }
}

</style>
