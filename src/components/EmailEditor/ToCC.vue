<template>
  <div class="email-box">
    <div
      v-for="(item, index) in email_tag_list"
      :key="index"
      class="email-item"
    >
      <div v-show="index === editable_index" style="position: relative">
        <span class="width-span">{{item.email}}</span>
        <el-input
          :ref="`tag-input-${index}`"
          v-model="item.email"
          @blur="toggle(null)"
          @change="toggle(null)"
          @keyup.enter.native="toggle(null)"
        />
      </div>

      <el-tag
        v-show="index !== editable_index"
        closable
        :disable-transitions="true"
        :type="getTagType(item.email)"
        @click="toggle(index)"
        @close="handleClose(index)">
        {{item.email}}
      </el-tag>
    </div>

    <!-- 空格、回车、失去焦点 都视为选中当前值-->
    <div class="select-no-border" @click="click">
      <el-autocomplete
        ref="autocomplete"
        v-model="input_value"
        class="w-100"
        :fetch-suggestions="querySearchAsync"
        placeholder=""
        :select-when-unmatched="true"
        @blur="(query)=>handleSelect(query,'blur')"
        @focus="focus"
        @keyup.native.space="(query)=>handleSelect(query,'space')"
        @keyup.native.delete="handleDelete"
        @select="(query)=>handleSelect(query,'select')"
      >
        <template slot-scope="{ item }">
          {{ item.label_name }}
        </template>
      </el-autocomplete>
    </div>
  </div>
</template>

<script>
import ToolAPI from '@/api/tool'

export default {
  name: 'ToCC',
  props: {
    prohibitRemoteSearch: {
      type: Boolean,
      default: false,
    },
    originList: {
      type: Array,
      default: () => {
        return []
      },
    },
    originAddresses: Array,
    options: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      select_input: '',
      input_value: '',
      email_tag_list: [],
      editable_index: null,
      search_loading: false,
      is_focus: false,
    }
  },

  watch: {
    'originList': {
      handler() {
        this.email_tag_list = this.originList.map(item => {
          return { email: item }
        })
      },
      immediate: true,
    },
  },

  methods: {
    querySearchAsync(queryString, cb) {
      if (queryString.trim() === '') {
        cb([])
        return
      }
      this.searchEmailAddress(queryString).then((data) => {
        let format_options = []
        if (data.length) {
          format_options = this.formatOption(this.options.concat(data))
        } else {
          format_options = [{ value: queryString, label_name: queryString }].concat(this.formatOption(this.options))
        }
        cb(format_options)
      })
    },

    handleDelete() {
      if (!this.input_value && !this.$refs.autocomplete.suggestions.length) {
        this.handleClose(-1)
      }
    },

    handleSelect(query, type) {
      // 处理鼠标点击选项时，会先触发blur 导致搜索字段被选中
      if (type === 'select') {
        const select_input_index = this.email_tag_list.findIndex(temp => temp.email === this.select_input.trim())
        if (select_input_index === this.email_tag_list.length - 1) {
          this.email_tag_list.splice(-1, 1)
        }
      }

      const value = this.input_value
      // 手动输入值时，如果当前输入值已存在于list，将不塞进list
      if (value?.trim()) {
        if (value.includes(';')) {
          // 兼容以 ; 分割的多个邮箱，以及从Outlook 复制过来的多个邮箱（eg. "Ping Li" <<EMAIL>>; "Mike Xu" <<EMAIL>>）
          value.split(';').forEach(item => {
            const email = item.match(/<(.*?)>/)
            const result = email ? email[1] : item
            if (!this.email_tag_list.find(temp => temp.email === result)) {
              this.email_tag_list.push({ email: result })
            }
          })
        } else {
          const value_match = value.match(/<(.*?)>/)
          const value_format = value_match ? value_match[1] : value.trim()
          if (!this.email_tag_list.find(temp => temp.email === value_format)) {
            this.email_tag_list.push({ email: value_format })
          }
        }

        this.input_value = ''
        this.emitList()
        // 用于消除 用户在按住space键或点击输入框 选中当前输入值时，触发搜索，导致下拉框出现
        this.$refs.autocomplete.handleInput('')
      }
      this.is_focus = false
    },

    focus() {
      this.is_focus = true
    },
    click() {
      this.is_focus = !this.is_focus
      if (this.is_focus) {
        this.handleSelect()
        this.$refs.autocomplete.$refs.input.blur()
      }
    },

    getTagType(email) {
      const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      return mailReg.test(email.trim()) ? 'info' : 'danger'
    },

    searchEmailAddress(val) {
      this.select_input = val
      if (val === '' || this.prohibitRemoteSearch) return Promise.resolve([])
      this.search_loading = true
      const params = {
        type: 'USER',
        or: [
          { name_contains: encodeURIComponent(val) },
          { email_contains: encodeURIComponent(val) },
        ],
      }

      return ToolAPI.searchMailAddress(params)
        .finally(() => {
          this.search_loading = false
        })
    },

    formatOption(list) {
      if (!list) return []

      return list.map(item => {
        if (item.name) {
          item.label_name = item.name + ' <' + item.email + '>'
        } else {
          item.label_name = '<' + item.email + '>'
        }
        item.value = item.email
        return item
      })
    },

    handleClose(index) {
      this.email_tag_list.splice(index, 1)
      this.emitList()
    },

    toggle(index) {
      this.$set(this, 'editable_index', index)
      if (typeof index === 'number') {
        this.$nextTick(() => {
          this.$refs[`tag-input-${index}`][0].$refs.input.focus()
        })
      }
      this.emitList()
    },

    emitList() {
      this.$emit('update:originList', this.email_tag_list.map(item => item.email))
    },

  },
}
</script>

<style scoped lang="scss">
.email-box{
  display: flex;
  border-radius: 5px;
  border:1px solid #c8c8c8;
  background-color: #fff;
  flex-wrap: wrap;

  .email-item{
    flex-shrink: 0;
    margin:0 4px;

    .width-span {
      visibility: hidden;
      font-size: 12px;
      padding:0 20px;
    }

    .el-input {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      font-size: 12px;
      border-radius: 4px;
      border-style: none;
    }

    ::v-deep .el-input--mini .el-input__inner{
      height: 24px;
      line-height: 24px;
    }
  }

}

.select-no-border{
  flex-grow: 1;
  ::v-deep .el-input__inner{
    border:none;
    padding:0 8px;
  }
  ::v-deep .el-input__suffix-inner{
    display: none;
  }
}

.display-none{
  display: none;
}
</style>
