<template>
  <div>
    <el-divider v-if="!noOptions">
      <el-dropdown trigger="click" placement="bottom-start" @command="changeEmailTemplate">
        <el-link type="primary" :underline="false" class="el-dropdown-link">
          <slot v-if="template">{{ template.display_name }}</slot>
          <i class="el-icon-arrow-down el-icon--right" />
        </el-link>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="option in options"
            :key="option.id"
            :command="option">
            {{ option.display_name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </el-divider>
    <div v-if="email">
      <to-cc-editor v-if="!noRecipients"
                    :allow-create-address="allowCreateAddress"
                    :data="email"
                    :readonly="readonly"
                    v-bind="$attrs"
                    :options="toCcOptions"
                    :client-id="clientId" />

      <div v-if="email.attachments && email.attachments.length" class="flex">
        <h6>Attachments: </h6>
        <div class="flex-1 mt-8 ml-3">
          <div v-for="(item, index) in email.attachments" :key="index" class="mt-8">
            <div v-if="item.file_path">
              <el-link type="primary"
                       :href="item.file_path">{{ item.name }}
              </el-link>
            </div>
          </div>
        </div>
      </div>
      <div class="flex align-items-center">
        <el-input v-model="email.subject"
                  class="email-subject-input mr-8"
                  @change="changeSubject" />
        <el-tooltip :content="subject_copied ? 'Copied' : 'Copy'" placement="top">
          <span style="margin-top: 8px;margin-right: 3px;font-size: 12px;">
            <i class="el-icon-document-copy link cursor-pointer" @click="copy(email.subject)" />
          </span>
        </el-tooltip>
      </div>

      <tinymce
        ref="tinymce_content"
        v-model="email.content"
        :readonly="readonly"
        inline
        is-email
        @change="changeContent"
      />
    </div>
  </div>
</template>

<script>
import { copyUtil } from '@/utils/tool'
import Tinymce from '@/components/Tinymce'
import ToCcEditor from '@/components/EmailEditor/ToCcEditor'

export default {
  name: 'EmailEditor',
  components: { ToCcEditor, Tinymce },
  props: {
    template: Object,
    options: Array,
    noOptions: {
      type: Boolean,
      default: false,
    },
    noRecipients: {
      type: Boolean,
      default: false,
    },
    allowCreateAddress: {
      type: Boolean,
      default: false,
    },
    toCcOptions: {
      type: Array,
      default() {
        return []
      },
    },
    content: {
      type: String,
      default: '',
    },
    clientId: {
      type: [String, Number],
      default: null,
    },
    email: {
      type: Object,
      default() {
        return {
          subject: '',
          content: '',
        }
      },
    },
    readonly: Boolean,
  },
  data() {
    return {
      subject_copied: false,
    }
  },
  methods: {
    changeEmailTemplate(val) {
      this.$emit('change', val)
    },
    setContent() {
      this.$nextTick(() => {
        if (this.$refs['tinymce_content']) {
          this.$refs['tinymce_content'].setContent(this.email.content)
        }
      })
    },
    copy(val) {
      copyUtil(val)
        .then((val) => {
          this.$message({
            message: 'Copy successful',
            type: 'success',
          })
          this.subject_copied = true
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },
    changeContent() {
      this.$emit('update:content', this.$refs['tinymce_content'].getContent())
    },
    changeSubject() {
      this.subject_copied = false
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-popover {
  min-width: 90px;

  .popper__arrow {
    left: 39.5px !important;
  }
}
/* 邮件编辑 主题 */
.email-subject-input {
  margin: 5px;

  ::v-deep .el-input__inner {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
  }
}
</style>
