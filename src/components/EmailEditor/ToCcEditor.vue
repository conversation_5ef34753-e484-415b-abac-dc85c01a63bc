<template>
  <div v-if="loaded" class="email-receiver" :class="{isEditing: isEdit}">
    <table class="w-100">
      <tr>
        <td class="no-wrap">
          <span>To:</span>
        </td>
        <td class="w-100">
          <div v-if="isEdit" class="flex">
            <toCC v-if="allowCreateAddress"
                  class="flex-1"
                  :options="options"
                  :origin-list.sync="data.to_list"
                  :origin-addresses.sync="data.to_addresses" />
            <ToCCUser v-if="!allowCreateAddress"
                      class="flex-1"
                      :options="options"
                      :origin-list.sync="data.to_list"
                      :origin-addresses.sync="data.to_addresses" />
            <!--   发给客户邮件时，以是否传clientId 决定是否可以新增client user，并将user添加到list-->
            <create-client-user
              v-if="clientId"
              allow-change-status
              class="inline-block ml-5px"
              :client-id="clientId"
              @update="val =>pushClientUser(val,'to')" />
          </div>
          <div v-else>
            <div v-if="showToListRequire">
              <i style="color: #f56c6c;">Please fill in</i>
            </div>
            <div v-else>
              {{ showList(data.to_addresses, data.to_list) }}
            </div>
          </div>
        </td>
        <td>
          <el-tooltip :content="to_copied ? 'Copied' : 'Copy'" placement="top">
            <i class="el-icon-document-copy link cursor-pointer" @click="copy('to')" />
          </el-tooltip>
        </td>
      </tr>
      <tr v-if="!onlyTo">
        <td class="no-wrap">
          <span>Cc:</span>
        </td>
        <td class="w-100">
          <div v-if="isEdit" class="flex">
            <toCC v-if="allowCreateAddress"
                  class="flex-1"
                  :options="options"
                  :origin-list.sync="data.cc_list"
                  :origin-addresses.sync="data.cc_addresses" />
            <ToCCUser
              v-if="!allowCreateAddress"
              class="flex-1"
              :options="options"
              :origin-list.sync="data.cc_list"
              :origin-addresses.sync="data.cc_addresses" />
            <create-client-user
              v-if="clientId"
              allow-change-status
              class="inline-block ml-5px"
              :client-id="clientId"
              @update="val =>pushClientUser(val,'cc')" />
          </div>
          <div v-else>
            {{ showList(data.cc_addresses, data.cc_list) }}
          </div>
        </td>
        <td>
          <el-tooltip :content="cc_copied ? 'Copied' : 'Copy'" placement="top">
            <i class="el-icon-document-copy link cursor-pointer" @click="copy('cc')" />
          </el-tooltip>
        </td>
      </tr>
    </table>
    <div v-if="!readonly" class="edit-cover" @click="actionEdit">
      <div class="edit-text">Edit</div>
    </div>
  </div>
</template>

<script>
import { copyUtil } from '@/utils/tool'
// import ToolAPI from '@/api/tool'
import ToCCUser from '@/components/EmailEditor/ToCCUser'
import ToCC from '@/components/EmailEditor/ToCC'
import CreateClientUser from '@/views/project/components/CreateClientUser'

export default {
  name: 'ToCcEditor',
  components: { ToCCUser, CreateClientUser, ToCC },
  props: {
    data: Object,
    onlyTo: Boolean,
    options: {
      type: Array,
      default() {
        return []
      },
    },
    clientId: {
      type: [String, Number],
      default: null,
    },
    allowCreateAddress: {
      type: Boolean,
      default: false,
    },
    readonly: Boolean,
  },
  data() {
    return {
      loaded: false,
      search_loading: false,
      isEdit: false,
      to_copied: false,
      cc_copied: false,
      // options: {
      //   to: [],
      //   cc: [],
      // },
    }
  },
  computed: {
    showToListRequire() {
      const to_list = this.data.to_list
      return !Array.isArray(to_list) || to_list.length === 0
    },
  },
  watch: {
    'data': {
      handler() {
        this.isEdit = false
        if (!Array.isArray(this.data.to_list)) {
          this.data.to_list = []
          this.data.to_addresses = []
        }
        // this.options.to = this.formatOption(this.data.to_addresses)

        if (!Array.isArray(this.data.cc_list)) {
          this.data.cc_list = []
          this.data.cc_addresses = []
        }
        // this.options.cc = this.formatOption(this.data.cc_addresses)

        this.loaded = true
      },
      immediate: true,
    },
  },
  methods: {
    // searchEmailAddress(val, type) {
    //   if (val === '') return
    //   this.search_loading = true
    //   const params = {
    //     pagination: false,
    //     name_contains: encodeURIComponent(val),
    //   }
    //
    //   return ToolAPI.searchMailAddress(params).then(data => {
    //     this.options[type] = this.formatOption(data)
    //   }).finally(() => {
    //     this.search_loading = false
    //   })
    // },

    showList(list, origin_list) {
      if (!list?.length && !origin_list?.length) return ''

      if (!list.length) return origin_list.join('; ')

      return list.map(item => {
        if (item.name) return item.name + ' <' + item.email + '>'
        else return item.email
      }).join('; ')
    },

    // formatOption(list) {
    //   if (!list) return []
    //
    //   return list.map(item => {
    //     if (item.name) {
    //       item.label_name = item.name + ' <' + item.email + '>'
    //     } else {
    //       item.label_name = '<' + item.email + '>'
    //     }
    //     return item
    //   })
    // },
    actionEdit() {
      this.isEdit = true
    },
    copy(type) {
      copyUtil(this.data[`${type}_list`].join('; '))
        .then((val) => {
          this[`${type}_copied`] = true
        })
        .catch(() => {
          this.$message({
            message: 'Copy failed',
            type: 'error',
          })
        })
    },

    pushClientUser(val, type) {
      const email = val.contact_infos?.find(item => item.type === 'EMAIL')?.value
      if (email) {
        this.data[`${type}_list`].push(email)
      }
    },

    change(val, type) {
      // 兼容 粘贴进多个邮箱地址 以';'分割
      let format_list = []
      val.forEach(item => {
        if (item.includes(';')) {
          format_list = format_list.concat(item.split(';').map(item => item.trim()))
        } else {
          format_list.push(item)
        }
      })

      this.data[`${type}_list`] = format_list
      this[`${type}`] = false
    },
  },
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 2px;
}

.email-receiver {
  position: relative;
  font-size: 12px;
  line-height: 28px;

  .edit-cover {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 98%;
    height: 100%;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid #42B983;
    box-sizing: border-box;
    border-radius: 3px;

    .edit-text {
      width: auto;
      background-color: #42B983;
      color: #FFF;
      padding: 4px;
      line-height: 1;
    }
  }

  &:not(.isEditing):hover {
    .edit-cover {
      display: flex;
    }
  }
}

.ml-5px{
  margin-left: 5px;
}
</style>
