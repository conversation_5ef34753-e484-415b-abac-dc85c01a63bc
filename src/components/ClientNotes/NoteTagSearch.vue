<template>
  <el-select
    filterable
    clearable
    remote
    multiple
    reserve-keyword
    :placeholder="placeholder"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :loading="loading"
    :remote-method="searchTags"
    :allow-create="allowCreate"
    default-first-option
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang"
    @change="changeTags"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script>
import ClientAPI from '@/api/client'

export default {
  name: 'NoteTagSearch',
  props: {
    tagList: {
      type: Array,
      default() {
        return []
      },
    },
    allowCreate: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: 'Tag',
    },
  },
  data() {
    return {
      is_visible: false,
      loading: false,
      list: [],
    }
  },
  mounted() {
    this.list = this.tagList
  },
  methods: {
    searchTags(val) {
      this.loading = true
      const params = {
        name: val || undefined,
      }
      return ClientAPI.getClientNoteTags(params).then(data => {
        this.list = data.list
      }).finally(() => {
        this.loading = false
      })
    },

    changeTags(val) {
      if (val.length) {
        if (typeof val[val.length - 1] === 'string') {
          const params = {
            name: val[val.length - 1],
          }
          return ClientAPI.addClientNoteTags(params).then(data => {
            this.list.push(data)
            val[val.length - 1] = data.id
          })
        }
      }
    },

    visibleChang(val) {
      this.is_visible = val
    },
  },
}
</script>

<style scoped>

</style>
