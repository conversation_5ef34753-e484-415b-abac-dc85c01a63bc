<template>
  <div class="flex align-items-center">
    <el-checkbox v-model="checked" border>Send test email</el-checkbox>
    <div v-if="checked" class="pl-5">
      <el-input v-model="to_email_address" type="text" class="w-200px" />
      <el-button type="primary" @click="sendTestMail">Send</el-button>
    </div>
  </div>
</template>

<script>
import API from '@/api/email'
import { mapState } from 'vuex'

export default {
  name: 'SendTestEmail',
  props: {
    templateId: [String, Number],
    projectId: [String, Number],
    taskId: [String, Number],
    advisorId: [String, Number],
  },
  data() {
    return {
      checked: false,
      to_email_address: '',
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.info,
    }),
  },

  mounted() {
    this.to_email_address = this.userInfo.email
  },
  methods: {
    getTemplate() {
      const data = {
        client_id: this.clientId || undefined,
        project_id: this.projectId || undefined,
        task_id: this.taskId || undefined,
        advisor_id: this.advisorId || undefined,
      }
      return API.getEmailByTemplate(this.templateId, data)
    },

    sendTestMail() {
      this.getTemplate().then(data => {
        const params = {
          subject: data['subject'],
          content: data['content'],
          from: this.userInfo.email,
          from_name: data['from_name'],
          'to_list': [
            this.to_email_address,
          ],
        }
        return API.sendEmail(params)
          .then(() => {
            this.$message({
              message: 'Send successful',
              type: 'success',
            })
          }, () => {
            this.$message({
              message: 'Send failed',
              type: 'warning',
            })
          })
      })
    },
  },
}
</script>

<style scoped>
</style>
