import { RouteTools } from '@/utils/tool'

export default {
  data() {
    return {
      submitting: false,
    }
  },
  mounted() {
    this.autoFocus()
  },
  methods: {
    autoFocus() {
      this.$nextTick(() => {
        if (this.$refs['name']) {
          this.$refs['name'].focus()
        }
      })
    },
    resetData() {
      this.$nextTick(() => {
        if (this.$refs['form']) {
          this.form = this.initForm()
          this.$refs['form'].resetFields()
        }
      })
    },
    onSave(name) {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitting = true
          this.handleSave(this.form)
            .then((reset = true) => {
              if (reset) this.resetData()

              if (name && typeof name === 'string') {
                return RouteTools.removeCurrentRoute(this.$route)
                  .then(() => {
                    this.$router.push({ name: name })
                  })
              }
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          return false
        }
      })
    },
    asyncOnSave() {
      let result
      this.$refs['form'].validate((valid) => {
        if (valid) {
          result = this.handleSave(this.form)
        } else {
          result = Promise.reject()
        }
      })

      return result
    },
  },
}
