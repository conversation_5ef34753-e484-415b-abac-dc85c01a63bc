<template>
  <el-select
    value-key="id"
    filterable
    remote
    placeholder="Search"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :loading="loading"
    :remote-method="searchTCContent"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang">
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item" />
  </el-select>
</template>

<script>
import API from '@/api/tc'
import SelectMixin from '../mixins'

export default {
  name: 'TcSearch',
  mixins: [SelectMixin],
  props: {
    initRequest: {
      type: Boolean,
      default: true,
    },
    initList: Array,
  },
  data() {
    return {
      loading: false,
      list: [],
      searchQuery: {
        name_like: undefined,
      },
    }
  },
  watch: {
    'initList': {
      handler(newVal) {
        if (newVal.length) this.list = JSON.parse(JSON.stringify(newVal))
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.initRequest) {
      this.searchTCContent()
    }
  },
  methods: {
    searchTCContent(query = '') {
      this.searchQuery.name_like = query || undefined

      this.loading = true
      return API.getTCList(this.searchQuery).then(data => {
        this.list = data
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
