<template>
  <el-select
    :loading="loading"
    filterable
    remote
    :multiple="multiple"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    default-first-option
    :remote-method="searchIndustry"
    placeholder="Industry"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang"
    @change="list=[]"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.label" :value="item.id" />
  </el-select>
</template>

<script>
import ToolAPI from '@/api/tool.js'
import SelectMixin from '../mixins'

export default {
  name: 'IndustrySearchIndex',
  mixins: [SelectMixin],
  props: {
    originList: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
    }
  },
  computed: {
    format_list() {
      return this.list.concat(this.matchOriginList())
    },
  },

  mounted() {
    this.list = this.matchOriginList()
    this.$emit('init-completed')
  },
  methods: {
    searchIndustry(val) {
      if (!val) {
        this.list = []
        return
      }
      const params = {
        'label_contains': val || undefined,
        page: 1,
        size: 15,
      }
      this.loading = true
      return ToolAPI.searchLinkedinIndustry(params)
        .then(data => {
          this.list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },

    matchOriginList() {
      const _list = []
      this.originList.forEach(item => {
        const existList = this.list.filter(i => !!item && i.id === item.id).length > 0
        if (!existList && item) {
          _list.push(item)
        }
      })
      return _list
    },
  },
}
</script>

<style scoped>

</style>
