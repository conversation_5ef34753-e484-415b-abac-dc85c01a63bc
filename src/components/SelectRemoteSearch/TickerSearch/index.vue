<template>
  <el-select
    filterable
    remote
    reserve-keyword
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    default-first-option
    :remote-method="searchTickers"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script>
import CompanyAPI from '@/api/company'
import SelectMixin from '../mixins'

export default {
  name: 'UserSearch',
  mixins: [SelectMixin],
  props: {
    extra: String,
  },
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
    }
  },
  mounted() {
    this.searchTickers().then(() => {
      this.$emit('init-completed')
    })
  },
  methods: {
    searchTickers(val) {
      const params = {
        name_or_stock_code_contains: val || undefined,
        page: 1,
        size: 15,
      }
      return CompanyAPI.getCompanyList(params).then(data => {
        this.list = data.list
      })
    },
  },
}
</script>

<style scoped>

</style>
