<template>
  <el-select
    filterable
    clearable
    remote
    placeholder="Contract Name"
    class="remote-select"
    :loading="loading"
    :remote-method="searchContract"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item.id" />
  </el-select>
</template>

<script>
import ContractAPI from '@/api/contract'

export default {
  name: 'ContractSearch',
  props: {
    clientId: Number,
  },
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
    }
  },
  mounted() {
    this.searchContract('')
  },
  methods: {
    searchContract(val) {
      const params = {
        page: 1,
        size: 20,
        'approval_status': 'APPROVED',
        'effective_status': 'EFFECTIVE',
        name_contains: val || undefined,
        client_id: this.clientId || undefined,
      }
      this.loading = true
      return ContractAPI.getContractList(params)
        .then(data => {
          this.list = data.list
          this.count = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>
</style>
