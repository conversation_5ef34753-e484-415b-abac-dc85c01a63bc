<template>
  <el-select
    ref="template-select"
    value-key="id"
    filterable
    remote
    placeholder="Search"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :loading="loading"
    :remote-method="searchEmailTemplate"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang">
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item" />
  </el-select>
</template>

<script>
import API from '@/api/email'
import SelectMixin from '../mixins'

export default {
  name: 'EmailTemplateSearch',
  mixins: [SelectMixin],
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
      searchQuery: {
        reference_ids: `0,${+this.$route.params.project_id}`,
        name_like: undefined,
        content_type: undefined,
        is_private: false,
        page: 1,
        size: 30,
      },
    }
  },
  methods: {
    searchEmailTemplate(query = '') {
      this.searchQuery.name_like = query || undefined

      this.loading = true
      return API.getEmailList(this.searchQuery).then(data => {
        this.list = data.list
        this.count = data.count
      }).finally(() => {
        this.loading = false
      })
    },
    autoFocus() {
      this.$nextTick(() => {
        this.$refs['template-select'].focus()
      })
      this.searchEmailTemplate()
    },
  },
}
</script>

<style scoped>

</style>
