<template>
  <el-select
    clearable
    remote
    filterable
    :loading="loading"
    :remote-method="getTeamList"
    placeholder="Team"
    class="remote-select"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
import userAPI from '@/api/user'

export default {
  name: 'TeamSearch',
  data() {
    return {
      loading: false,
      list: [{
        name: ' ',
        id: 4,
      }],
      count: 0,
    }
  },
  async mounted() {
    await this.getTeamList()
  },
  methods: {
    getTeamList(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        page: 1,
        size: 20,
      }
      this.loading = true
      return userAPI.getUserTeamList(params)
        .then(data => {
          this.list = data.list
          this.count = data.count
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
