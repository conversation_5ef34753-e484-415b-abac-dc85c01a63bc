<template>
  <el-select
    :loading="loading"
    filterable
    remote
    :multiple="multiple"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    default-first-option
    :remote-method="searchCompany"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang"
  >
    <el-option v-for="item in format_list" :key="item.id" :label="item.name" :value="item.id">
      <slot v-if="stockLabel">
        <span style="float: left">{{ item.name }}</span>
        <router-link
          v-if="showLink"
          class="link ml-3"
          target="_blank"
          :to="{ name: 'CompanyDetail', params: { company_id: item.id }}"
          @click.stop.native=""
        >
          <i class="el-icon-link" />
        </router-link>
        <span style="float: right;padding-right: 15px">{{ getStockCode(item) }}</span>
      </slot>
    </el-option>
  </el-select>
</template>

<script>
import CompanyAPI from '@/api/company.js'
import SelectMixin from '../mixins'

export default {
  name: 'CompanySearch',
  mixins: [SelectMixin],
  props: {
    originList: {
      type: Array,
      default: () => [],
    },
    stockLabel: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    extra: String,
    showLink: Boolean,
  },
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
    }
  },
  computed: {
    format_list() {
      return this.list.concat(this.matchOriginList())
    },
  },

  mounted() {
    // this.searchCompaniesContains().then(data => {
    //   this.list = data
    //   this.$emit('init-completed')
    // })
    this.$emit('init-completed')
  },
  methods: {
    searchCompany(val) {
      if (!val.trim()) return

      this.loading = true

      const params = {
        keyword: val,
        page: 1,
        size: 20,
      }

      return CompanyAPI.getCompanyListByEs(params)
        .then(data => {
          this.list = data.list.map(item => item.source)
        })
        .finally(() => {
          this.loading = false
        })
    },

    // searchCompaniesPrecise(val) {
    //   const params_precise = {
    //     'name_or_stock_code': val || undefined,
    //     page: 1,
    //     size: 20,
    //   }
    //
    //   return CompanyAPI.getCompanyList(params_precise).then(data => {
    //     return data.list
    //   })
    // },
    //
    // searchCompaniesContains(val) {
    //   const params = {
    //     'name_or_stock_code_contains': val || undefined,
    //     page: 1,
    //     size: 100,
    //   }
    //
    //   return CompanyAPI.getCompanyList(params).then(data => {
    //     return data.list
    //   })
    // },

    matchOriginList() {
      const _list = []
      this.originList.forEach(item => {
        const existList = this.list.filter(company => company.id === item.id).length > 0
        if (!existList) {
          _list.push(item)
        }
      })
      return _list
    },

    getStockCode(company) {
      if (company.type === 'PRIVATE') {
        return 'Private'
      }
      if (company.exchange && company.stock_code) {
        return company.exchange + ' : ' + company['stock_code']
      }
    },
  },
}
</script>

<style scoped>

</style>
