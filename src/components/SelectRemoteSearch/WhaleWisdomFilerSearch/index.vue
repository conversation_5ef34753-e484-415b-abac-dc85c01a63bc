<template>
  <el-select
    v-bind="$attrs"
    :loading="loading"
    :remote-method="search"
    default-first-option
    multiple
    filterable
    remote
    value-key="whale_wisdom_filer_id"
    :placeholder="placeholder"
    no-data-text="No matching data"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    v-on="$listeners"
    @visible-change="visibleChang">
    <el-option
      v-for="item in list"
      :key="item.whale_wisdom_filer_id"
      :label="item.whale_wisdom_filer.filer_name"
      :value="item">
      {{ item.whale_wisdom_filer.filer_name }}
      <span class="info pl-5">{{ item.whale_wisdom_filer.cik }}</span>
    </el-option>
  </el-select>
</template>

<script>
import ToolAPI from '@/api/tool'
import SelectMixin from '../mixins'

export default {
  name: 'WhaleWisdomFilerSearch',
  mixins: [SelectMixin],
  props: {
    filerList: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: 'Search',
    },
  },
  data() {
    return {
      loading: false,
      list: [],
    }
  },

  mounted() {
    this.list = this.filerList
  },
  methods: {
    search(val) {
      if (!val) return

      this.loading = true
      const params = {
        name: val,
        cik: null,
      }
      return ToolAPI.searchWhaleWisdom(params)
        .then(data => {
          this.list = data.filers.map(item => ({
            whale_wisdom_filer_id: item.id,
            whale_wisdom_filer: {
              id: item.id,
              filer_id: item.id,
              cik: item.cik,
              filer_name: item.name,
            },
          }))
        }).finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
