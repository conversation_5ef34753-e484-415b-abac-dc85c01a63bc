<template>
  <el-select
    filterable
    :multiple="multiple"
    clearable
    remote
    placeholder="Project Name"
    class="remote-select"
    :loading="loading"
    :collapse-tags="collapseTags"
    :remote-method="searchProject"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners">
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item.id" />
  </el-select>
</template>

<script>
import API from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: 'ProjectSearch',
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    collapseTags: { type: Boolean, default: false },
    onlyUserProject: { type: Boolean, default: false },
    clientId: [Number, String],
  },
  data() {
    return {
      loading: false,
      list: [],
      searchQuery: {
        page: 1,
        size: 50,
        name_like: undefined,
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  mounted() {
    this.searchProject()
  },
  methods: {
    searchProject(query = undefined) {
      this.searchQuery.ids = undefined
      this.searchQuery.name_like = undefined
      if (query) {
        if (+query) {
          this.searchQuery.ids = +query
        } else {
          this.searchQuery.name_like = query
        }
      }
      if (this.onlyUserProject) this.searchQuery.member_ids = this.uid
      if (this.clientId) this.searchQuery.client_id = this.clientId

      this.loading = true
      return API.getProjectList(this.searchQuery)
        .then(data => {
          this.list = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
