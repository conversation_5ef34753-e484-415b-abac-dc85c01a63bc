<template>
  <el-select
    filterable
    :multiple="multiple"
    clearable
    remote
    :placeholder="placeholder"
    class="remote-select"
    :loading="loading"
    :collapse-tags="collapseTags"
    :remote-method="searchAdvisor"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners">
    <el-option
      v-for="item in list"
      :key="item.source.id"
      :label="item.source.name || item.source.full_name"
      :value="item.source[isTsId ? 'tsid' : 'id']">
      <div class="flex justify-content-between">
        <div>
          <slot v-if="!inputPhoneEmail">{{ item.source.name || item.source.full_name }}</slot>
          <div v-else class="flex justify-content-between">
            <span class="mr-8">{{ item.source.name || item.source.full_name }}</span>
            <div>
              <span v-if="item.source.phone">{{ item.source.phone }}</span>
              <span v-if="item.source.email" class="ml-8">{{ item.source.email }}</span>
            </div>
          </div>
        </div>
        <div class="info ml-8">{{item.source.id}}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import API from '@/api/consultant'

export default {
  name: 'AdvisorSearch',
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    collapseTags: { type: Boolean, default: false },
    advisorType: { type: String, default: null },
    initialId: [Number, String],
    inputPhoneEmail: Boolean,
    placeholder: { type: String, default: 'Advisor Name' },
    useEsSearch: Boolean,
    isTsId: Boolean,
  },
  data() {
    return {
      loading: false,
      list: [],
      searchQuery: {
        page: 1,
        size: 20,
      },
    }
  },
  mounted() {
    this.searchAdvisor('', this.initialId)
  },
  methods: {
    searchAdvisor(query = '', id) {
      const params = { ...this.searchQuery }

      if (this.inputPhoneEmail) {
        params.name_email_phone_contains = query
      } else {
        if (query) {
          if (+query) {
            params['id'] = +query
          } else {
            params['name'] = query
          }
        }

        if (this.advisorType) {
          params['type'] = this.advisorType
        }

        if (id) {
          params['id'] = +id
        }
      }

      this.loading = true
      const apiName = this.useEsSearch ? 'searchConsultantByEs' : 'searchConsultant'
      return API[apiName](params).then(data => {
        if (this.useEsSearch) {
          data.list.forEach(item => {
            item.source = item
          })
        }
        this.list = data.list
      }).finally(() => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped>

</style>
