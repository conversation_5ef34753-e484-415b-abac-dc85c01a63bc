<template>
  <el-select
    filterable
    clearable
    remote
    :multiple="multiple"
    reserve-keyword
    :placeholder="placeholder"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :loading="loading"
    :remote-method="searchClient"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @change="getClientName"
    @visible-change="visibleChang"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script>
import ClientAPI from '@/api/client'
import SelectMixin from '../mixins'

export default {
  name: 'ClientSearch',
  mixins: [SelectMixin],
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    extra: String,
    clientName: String, // update时  防止初始化显示 client id
    placeholder: {
      type: String,
      default: 'Client Name',
    },
  },
  data() {
    return {
      loading: false,
      list: [],
      count: 0,
    }
  },
  watch: {
    'clientName': {
      handler(newVal) {
        if (newVal) {
          this.searchClient(this.clientName)
        }
      }, immediate: true,
    },
  },
  mounted() {
    this.searchClient()
  },
  methods: {
    searchClient(val) {
      const params = {
        id_or_name_contains: val || undefined,
        extra: this.extra,
        page: 1,
        size: 15,
      }
      if (val !== '') {
        this.loading = true
        return ClientAPI.getClientList(params)
          .then(data => {
            this.list = data.list
            this.count = data.count
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    getClientName(val) {
      if (val && !this.multiple) {
        const name = this.list.find(item => item.id === val).name
        this.$emit('update:clientName', name)
      }
    },
  },
}
</script>

<style scoped>

</style>
