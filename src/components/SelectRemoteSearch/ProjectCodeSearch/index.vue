<template>
  <el-select
    filterable
    clearable
    remote
    placeholder="Project Code"
    class="remote-select"
    :loading="loading"
    :collapse-tags="collapseTags"
    :remote-method="searchProject"
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @clear="$emit('input', undefined)">
    <el-option
      v-for="(item,index) in list"
      :key="index"
      :label="item"
      :value="item" />
  </el-select>
</template>

<script>
import API from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: 'ProjectCodeSearch',
  props: {
    collapseTags: { type: Boolean, default: false },
  },
  data() {
    return {
      loading: false,
      list: [],
      searchQuery: {
        page: 1,
        size: 20,
        input: undefined,
      },
    }
  },
  computed: {
    ...mapGetters([
      'uid',
    ]),
  },
  mounted() {
    this.searchProject()
  },
  methods: {
    searchProject(query = undefined) {
      this.searchQuery.input = query || ''

      this.loading = true
      return API.getProjectCodeList(this.searchQuery)
        .then(data => {
          this.list = data
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
