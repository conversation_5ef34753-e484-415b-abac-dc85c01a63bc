<template>
  <el-select
    v-bind="$attrs"
    :loading="loading"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :remote-method="search"
    default-first-option
    filterable
    remote
    clearable
    no-data-text="No matching data"
    v-on="$listeners"
    @change="select"
    @visible-change="visibleChang">
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" />
  </el-select>
</template>

<script>
import UserAPI from '@/api/user'
import SelectMixin from '../mixins'
import _ from 'lodash'
import AuthorityAPI from '@/api/authority'

export default {
  name: 'UserSearch',
  mixins: [SelectMixin],
  props: {
    extra: String,
    role: {
      type: String,
      default: '',
    },
    extraMember: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 是否允许 以Team为单位，把team中的所有成员选中
    allowSelectTeam: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      value: null,
      loading: false,
      team_list: [],
      list: [],
      extra_list: [],
      count: 0,
      role_params: {
        'create-project': {
          'user_role.role_ids': '1,2,3,8',
        },
        am: {
          'user_role.role_ids': '5',
        },
      },
    }
  },

  watch: {
    'extraMember': {
      handler(val) {
        if (val.length) {
          this.list = _.uniqBy([...this.list, ...this.extraMember.map(item => item.user)], 'id')
        }
      },
    },
  },

  async mounted() {
    if (this.role === 'create-project') { await this.getRolesList() }
    await this.searchUsers()
      .then(() => {
        if (this.extraMember.length) {
          this.list = _.uniqBy([...this.list, ...this.extraMember.map(item => item.user)], 'id')
        }
        if (this.allowSelectTeam) this.searchTeam()
        this.$emit('init-completed')
      })
  },
  methods: {
    getRolesList() {
      return AuthorityAPI.getRolesList()
        .then(data => {
          const ph_role_id = data.filter(item => item.name === 'PH')[0]?.id
          if (ph_role_id) {
            const role_ids = [1, 2, 3, 8, ph_role_id].join()
            this.role_params['create-project']['user_role.role_ids'] = role_ids
          }
        })
    },
    select(val) {
      let supporter_ids = []
      if (this.allowSelectTeam) {
        val.forEach(item => {
          if (typeof item === 'string' && item.includes('team')) {
            // 处理当选中team时 将team中的所有成员加入
            const target_options = this.team_list.find(temp => temp.id === item).team_members
            this.list = _.uniqBy([...this.list, ...target_options.map(item => item.user)], 'id')

            const ids = item.split('-').slice(1).map(item => +item)
            supporter_ids = supporter_ids.concat(ids)
          } else {
            supporter_ids.push(item)
          }
        })

        this.$nextTick(() => {
          this.$emit('select', Array.from(new Set(supporter_ids)))
        })
      }
    },

    search(val) {
      this.loading = true
      this.searchUsers(val)
        .then(data => {
          if (this.allowSelectTeam) {
            return this.searchTeam(val)
          } else {
            return data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    searchUsers(val) {
      this.list = []

      let params = {
        name_like: val || undefined,
        is_blocked: false,
      }
      if (this.role) {
        params = Object.assign(params, this.role_params[this.role])
      }
      return UserAPI.getUsersList(params)
        .then(data => {
          this.list = data['list'].filter(item => item.name)
        })
    },

    searchTeam(val) {
      const params = {
        name_contains: val || undefined,
        type: 'EMPLOYEE_TEAM',
        extra: 'team_members.user',
        page: 1,
        size: 15,
      }

      return UserAPI.getUserTeamList(params)
        .then(data => {
          this.team_list = data['list'].filter(item => item.team_members.length > 0).map(item => {
            const team_members_ids = item.team_members.map(temp => temp.member_id).join('-')
            item.id = `team${item.id}-${team_members_ids}`
            return item
          })
          this.list = this.team_list.concat(this.list)
        })
    },
  },
}
</script>

<style scoped>

</style>
