<template>
  <div>
    <el-skeleton v-if="loading" :rows="1" loading animated />
    <template v-else>
      <el-select
        v-if="options.offices.length"
        filterable
        class="w-100"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <el-option
          v-for="item in options.offices"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          {{ item.name }}<span v-if="item.location"> - {{ item.location.name }}</span>
        </el-option>
      </el-select>
      <el-alert
        v-else
        type="warning"
        :closable="false"
        show-icon
      >
        <template v-slot:title>
          Please create Client Office in
          <router-link :to="{name: 'ClientDetail', params: {client_id: clientId}}" target="_blank" class="link">
            client profile
          </router-link>
          first.
        </template>
      </el-alert>
    </template>
  </div>
</template>

<script>
import ClientAPI from '@/api/client'

export default {
  name: 'ClientOfficeSearch',
  props: {
    clientId: [String, Number],
  },
  data() {
    return {
      loading: false,
      options: {
        offices: [],
      },
    }
  },
  watch: {
    'clientId': {
      handler(val) {
        if (!val) return
        this.getOfficeList()
      },
      immediate: true,
    },
  },
  methods: {
    getOfficeList() {
      this.loading = true
      return ClientAPI.getClientOffices(this.clientId, { extra: 'location' })
        .then(data => {
          this.options.offices = data.list
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
