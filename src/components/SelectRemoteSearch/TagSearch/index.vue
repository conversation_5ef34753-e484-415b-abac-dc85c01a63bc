<template>
  <el-select
    filterable
    clearable
    remote
    reserve-keyword
    placeholder="Search"
    class="remote-select"
    :class="{'is-reverse':is_visible}"
    :loading="loading"
    :remote-method="searchTags"
    allow-create
    default-first-option
    no-data-text="No matching data"
    v-bind="$attrs"
    v-on="$listeners"
    @visible-change="visibleChang"
    @change="changeTags"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.content" :value="item.id" />
  </el-select>
</template>

<script>
import ProjectAPI from '@/api/project'
import SelectMixin from '../mixins'
export default {
  name: 'TagSearch',
  mixins: [SelectMixin],
  props: {
    extra: String,
  },
  data() {
    return {
      is_visible: false,
      loading: false,
      list: [],
      temp_tags: [],
      count: 0,
    }
  },
  mounted() {
    this.searchTags().then(() => {
      this.$emit('init-completed')
    })
  },
  methods: {
    searchTags(val) {
      const params = {
        content_like: val || undefined,
      }
      return ProjectAPI.getTags(params).then(data => {
        this.pushTemp(data.list)
        this.count = data.count
        this.list = data.list
      })
    },
    pushTemp(list) {
      let res_data = []
      res_data = list.map(item => item.id)
      this.temp_tags.forEach(item => {
        if (!res_data.includes(item.id)) {
          list.push(item)
        }
      })
    },
    changeTags(val) {
      if (val.length) {
        if (typeof val[val.length - 1] === 'string') {
          const params = {
            content: val[val.length - 1],
            role: 'PROJECT',
            is_deprecated: false,
          }
          return ProjectAPI.saveTags(params).then(data => {
            this.temp_tags.push({
              content: data.content,
              id: data.id,
            })
            val[val.length - 1] = data.id
          })
        }
      }
    },
  },
}
</script>

<style scoped>

</style>
