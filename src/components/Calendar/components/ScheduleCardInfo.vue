<template>
  <div class="notice-container" @click="showInfo = !showInfo">
    <div v-show="showInfo" class="card-info">
      <div v-for="item in card_info" :key="item.label" class="flex align-items-center">
        <div class="color-block" :style="{'background-color': item.color}" />
        {{item.label}}
      </div>
    </div>
    <div v-show="showInfo" class="calendar-notice">
      <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="100" height="100">
        <path
          d="M281.674 253.424c11.553-14.585 24.288-28.14 38.208-40.267l-68.978-69.013c-10.828-10.867-28.404-10.867-39.275 0-10.831 10.831-10.831 28.404 0 39.235l70.046 70.043zM506.013 142.469c0.268 0 0.494 0 0.802 0 9.109 0 18.109 0.571 26.955 1.487l0-93.986c0-15.327-12.432-27.757-27.76-27.757s-27.721 12.429-27.721 27.757l0 93.968c8.846-0.895 17.842-1.469 26.956-1.469 0.27 0 0.534 0 0.762 0zM730.414 253.388l69.971-70.007c10.866-10.831 10.866-28.404 0-39.235-10.794-10.867-28.405-10.867-39.277 0l-69.018 69.013c13.958 12.123 26.695 25.664 38.324 40.227zM213.494 450.225c0-13.403 1.07-26.519 2.633-39.483l-98.682 0c-15.289 0-27.722 12.432-27.722 27.757 0 15.328 12.434 27.757 27.722 27.757l96.393 0c-0.229-5.28-0.345-10.581-0.345-16.033zM894.563 410.745l-98.643 0c1.6 12.964 2.633 26.082 2.633 39.483 0 5.45-0.115 10.753-0.305 16.033l96.315 0c15.332 0 27.722-12.429 27.722-27.757s-12.39-27.757-27.722-27.757zM745.017 638.262c-11.286 16.547-23.218 30.843-34.695 43.77l50.787 50.829c10.868 10.831 28.483 10.831 39.277 0 10.866-10.867 10.866-28.404 0-39.275l-55.37-55.324zM211.627 693.585c-10.831 10.867-10.831 28.404 0 39.275 10.868 10.831 28.448 10.831 39.275 0l50.83-50.863c-11.475-12.889-23.412-27.222-34.699-43.731l-55.403 55.324zM760.039 450.225c0.037-57.556-19.138-110.785-51.438-153.414-28.33-37.442-66.806-66.802-111.379-84.036l0.229-1.222-19.026-5.586c-14.798-4.29-30.051-7.265-45.605-8.867l-2.211-0.246-0.075 0-0.079-0.019c-7.623-0.724-15.557-1.279-23.83-1.279l-1.218 0c-8.276 0-16.207 0.557-23.833 1.279l-0.115 0.019-2.286 0.246c-15.535 1.6-30.791 4.575-45.549 8.867l-18.416 5.374 0.19 1.222c-44.802 17.214-83.505 46.651-111.99 84.244-32.258 42.628-51.438 95.854-51.438 153.414 0 41.274 7.132 75.207 18.227 103.29 16.661 42.114 42.094 70.559 62.533 92.138 10.22 10.754 19.22 19.941 25.317 27.795 6.254 7.892 9.265 13.995 10.141 18.837 4.459 23.565 4.917 53.306 4.917 60.854l0 2.174c0 34.085 27.609 61.617 61.659 61.655l142.51 0c34.092-0.037 61.659-27.609 61.659-61.655l0-2.097c-0.037-7.474 0.458-37.291 4.919-60.893 0.608-3.278 2.058-7.017 4.917-11.591 4.88-7.97 14.11-17.883 25.548-29.819 17.083-17.959 38.815-40.604 56.091-72.805 17.312-32.161 29.703-73.706 29.626-127.882zM701.813 537.676c-13.614 34.336-34.053 57.575-53.763 78.391-9.841 10.411-19.487 20.095-27.911 30.812-8.311 10.562-15.745 22.65-18.606 37.481-5.377 28.94-5.607 59.978-5.638 68.862 0 1.181 0 1.828 0 2.096-0.037 10.295-8.314 18.606-18.608 18.606l-142.51 0c-5.223 0-9.761-2.059-13.194-5.45-3.391-3.434-5.412-7.934-5.412-13.154 0-0.268 0-0.953 0-2.172-0.037-8.957-0.306-39.921-5.682-68.783-1.828-9.797-5.834-18.571-10.674-26.348-8.579-13.612-19.559-24.819-30.889-36.832-17.116-17.842-35.232-37.119-49.341-63.41-14.07-26.309-24.517-59.745-24.557-107.54 0.037-47.947 15.862-91.95 42.706-127.427 26.845-35.442 64.595-62.209 108.175-75.246l5.377-1.638c10.524-2.708 21.315-4.861 32.429-6.026l0.075 0 2.06-0.229c6.519-0.627 12.847-1.03 19.103-1.066l1.105 0.115 1.106-0.076c6.212 0 12.579 0.42 19.063 1.03l-0.079 0 2.137 0.229 0.037 0c11.093 1.163 21.887 3.277 32.372 6.026l5.451 1.638c43.582 13.037 81.329 39.807 108.175 75.246 26.801 35.478 42.666 79.482 42.666 127.427 0.003 36.409-6.094 64.511-15.169 87.446zM532.782 197.107l0.079 0zM479.17 197.107l0 0c0 0 0.037 0 0.075 0l-0.075 0zM576.706 833.825l-141.369 0c-14.604 0-26.499 11.822-26.499 26.539 0 14.564 11.899 26.465 26.499 26.465l141.369 0c14.604 0 26.499-11.899 26.499-26.465 0-14.721-11.899-26.539-26.499-26.539zM576.706 900.704l-141.369 0c-14.604 0-26.499 11.857-26.499 26.464 0 14.678 11.899 26.539 26.499 26.539l141.369 0c14.604 0 26.499-11.857 26.499-26.539 0-14.605-11.899-26.464-26.499-26.464zM518.406 968.116l-72.805 0c0 0.801-0.154 1.561-0.154 2.404 0 14.643 22.498 26.499 43.297 26.499l34.546 0c20.818 0 43.277-11.857 43.277-26.499 0-0.837-0.115-1.6-0.115-2.404l-48.044 0z" />
      </svg>
      Hover over the card to view details.
    </div>
    <div class="toggle-arrow">
      <slot v-if="showInfo">
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
          <path
            d="M818.393225 712.230324c12.824073 14.09502 34.658358 15.126512 48.752354 2.303462 14.09502-12.843516 15.126512-34.678824 2.302439-48.752354l-332.676845-364.835266c-12.844539-14.114462-34.659381-15.127536-48.753377-2.302439-0.815575 0.733711-1.588171 1.486864-2.302439 2.302439l-0.080841 0.078795-0.13917 0.13917L153.018046 665.780409c-12.824073 14.074553-11.791557 35.909861 2.302439 48.752354 14.09502 12.824073 35.930327 11.792581 48.753377-2.303462l307.168891-336.845795 307.149449 336.845795L818.393225 712.230324 818.393225 712.230324z"
          />
        </svg>
      </slot>
      <slot v-else>
        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
          <path
            d="M818.393225 300.939003c12.824073-14.09502 34.658358-15.126512 48.752354-2.303462 14.09502 12.843516 15.126512 34.678824 2.302439 48.752354l-332.676845 364.835266c-12.844539 14.114462-34.659381 15.127536-48.753377 2.302439-0.815575-0.733711-1.588171-1.486864-2.302439-2.302439l-0.080841-0.078795-0.13917-0.13917L153.018046 347.388918c-12.824073-14.074553-11.791557-35.909861 2.302439-48.752354 14.09502-12.824073 35.930327-11.792581 48.753377 2.303462l307.168891 336.845795 307.149449-336.845795L818.393225 300.939003 818.393225 300.939003z"
          />
        </svg>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScheduleCardInfo',
  props: {
    outsource: Boolean,
  },
  data() {
    return {
      showInfo: true,
      card_info: [{
        color: '#67C23A',
        label: 'Advisor',
      }, {
        color: '#E6A23C',
        label: 'Client',
      }, {
        color: '#a7a5a8',
        label: 'Other Arranged',
      }, {
        color: '#891ae4',
        label: 'Project Arranged',
      }, {
        color: '#3788D8',
        label: 'You Arranged',
      }],
    }
  },
  mounted() {
    if (this.outsource) {
      this.card_info.push({
        color: '#fdb4b4',
        label: 'Outsource DB',
      })
    }
  },
}
</script>

<style scoped lang="scss">
.notice-container {
  transition: all .3s;
  padding: .5em;
  cursor: pointer;
  position: relative;
  min-height: 2em;
  margin-bottom: .5em;

  .toggle-arrow {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
  }

  svg {
    width: 1em;
    height: 1em;
  }

  &:hover {
    background-color: #F2F6FC;
  }
}

.card-info {
  display: flex;
  justify-content: space-between;
  max-width: 600px;

  .color-block {
    width: 1rem;
    height: 1em;
    margin-right: .5em;
  }
}

.calendar-notice {
  margin-top: .5em;
  display: flex;
  align-items: center;

  svg {
    margin-right: .5em;
    fill: #E6A23C;
  }
}
</style>
