<template>
  <div>
    <schedule-card-info
      v-if="!scheduler && advisorSchedules"
      v-bind="$attrs"
    />
    <FullCalendar
      ref="fullCalendar"
      :plugins="calendarPlugins"
      height="auto"
      :long-press-delay="100"
      :event-long-press-delay="100"
      :select-long-press-delay="100"
      :time-grid-event-min-height="20"
      :all-day-slot="false"
      :event-limit="true"
      :now-indicator="true"
      :now="now"
      :time-zone="timeZone"
      :views="eventLimitNum"
      default-view="timeGridFiveDays"
      slot-duration="00:15:00"
      :slot-label-format="{
        hour: 'numeric',
        minute: '2-digit',
        omitZeroMinute: false,
        meridiem: 'short',
        hour12: true
      }"
      :header="{
        left:'prev,next today toggleRangeButton toggleArrangedButton',
        center: 'title',
        right: 'timeGridFiveDays,timeGridDay'
      }"
      :button-text="{
        today: 'Go To Today',
        month: 'Month',
        week: 'Week',
        day: 'Day',
        list: 'List'
      }"
      :custom-buttons="customButtons"
      :selectable="selectable"
      :editable="editable"
      :event-time-format="eventTimeFormat"
      event-order="creator_type"
      :events="calendarEvents"
      :min-time="timeRange.minTime"
      :max-time="timeRange.maxTime"
      :event-render="eventRender"
      :slot-event-overlap="true"
      v-bind="$attrs"
      @datesRender="datesRender"
      @select="handleEventSelect"
      @eventClick="handleEventClick"
      @eventResize="eventResponsive"
      @eventDrop="eventResponsive"
      @eventDragStart="isDrag = true"
      @eventDragStop="isDrag = false"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import API from '@/api/project'
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import interactionPlugin from '@fullcalendar/interaction'
import ScheduleCardInfo from '@/components/Calendar/components/ScheduleCardInfo'
import moment from 'moment-timezone'
import tippy from 'tippy.js'

export default {
  name: 'Calendar',
  components: { FullCalendar, ScheduleCardInfo },
  model: {
    prop: 'calendarEvents',
    event: 'changeEvents',
  },
  props: {
    calendarEvents: Array, // 面板中的事件
    singleSelect: Boolean, // 是否只能选择一个时间段
    selectable: {
      type: Boolean,
      default: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
    scheduler: Boolean, // 是否是menu里的那个scheduler页面
    advisorSchedules: { // Edit Advisor Schedules 使用
      type: Boolean,
      default: true,
    },
    selectTimeZone: {
      type: String,
      default: undefined,
    },
    taskId: {
      type: [String, Number],
      default: undefined,
    },
  },
  data() {
    return {
      calendarPlugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
      eventLimitNum: {
        // 事件显示数量限制
        dayGrid: {
          eventLimit: 6,
        },
        timeGrid: {
          eventLimit: 2, // adjust to 6 only for timeGridWeek/timeGridDay
        },
        timeGridFiveDays: {
          type: 'timeGrid',
          duration: { days: 7 },
          dayCount: 7,
          buttonText: 'Week',
        },
      },
      eventTimeFormat: { hour: 'numeric', minute: '2-digit', timeZoneName: 'short' },
      // eventTimeFormat: { hour: 'numeric', minute: '2-digit' },
      timeRange: {
        minTime: '8:00:00',
        maxTime: '20:00:00',
      },
      isDrag: false, // 是否正在拖拽
      isTimeRangeExpanded: false,
      show_other_arrange: true,
      arranged_scheduled: [],
      customButtons: {
        toggleRangeButton: {
          text: 'Toggle Time Range',
          click: () => {
            this.toggleTimeRange()
          },
        },
        toggleArrangedButton: {
          text: 'Hide Other Scheduled Calls',
          click: () => {
            this.toggleOtherArrangedScheduled()
          },
        },
      },
    }
  },
  computed: {
    ...mapState({
      timeZone: state => state.app.timeZone,
    }),

    resultTimeZone() {
      return this.selectTimeZone || this.timeZone
    },
    now() {
      return moment.tz(this.resultTimeZone).format()
    },
    tzFormat() {
      return moment.tz(this.resultTimeZone).format('Z')
    },
  },
  watch: {
    'selectTimeZone': {
      handler(newVal, oldVal) {
        this.$emit('changeEvents', this.calendarEvents.map(item => {
          item.start = moment.tz(item.start, oldVal).tz(newVal).format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat
          item.end = moment.tz(item.end, oldVal).tz(newVal).format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat
          return item
        }))
      },
    },
  },
  methods: {
    /**
     * 该插件没有对数据进行双向绑定，所以自己封装
     */
    eventResponsive(info) {
      this.$emit('changeEvents', this.calendarEvents.map(event => {
        if (event.id === +info.event.id) {
          event.start = moment(info.event.start).utc().format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat
          event.end = moment(info.event.end).utc().format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat
          this.$emit('resize', event)
        }
        return event
      }))
    },
    /**
     * 选择时间段生成事件
     * @param arg
     */
    handleEventSelect(arg) {
      if (this.singleSelect) {
        const flag = this.calendarEvents.find(item => item.creator_type === 'PM')
        if (flag) return
      }
      const event = {
        id: Math.random(),
        title: '',
        creator_type: 'PM',
        task_id: this.taskId,
        start: moment.tz(arg.startStr, this.resultTimeZone).format(),
        end: moment.tz(arg.endStr, this.resultTimeZone).format(),
      }
      this.$emit('changeEvents', this.calendarEvents.concat([event]))
      this.$emit('new', event)
    },
    /**
     * 当呈现一组新的日期时触发。
     */
    datesRender(info) {
    },
    /**
     * event的渲染
     */
    eventRender(info) {
      if (this.isDrag) return

      const time = moment(info.event.start).utc().format('hh:mm A') + ' ~ ' +
        moment(info.event.end).utc().format('hh:mm A')
      let content = `<div><b>Period: </b>${time}</div>`

      if (this.scheduler) {
        const extra_data = info.event.extendedProps
        if (!extra_data.task) return

        content = content.concat(`<div><b>Advisor: </b>${extra_data.advisor.name_prefix || ''} ${extra_data.advisor.full_name}</div>
<div><b>Company: </b>${extra_data.task.advisor_profile.company.name}</div>
<div><b>Position: </b>${extra_data.task.advisor_profile.position}</div>
<div><b>Project: </b>${extra_data.task.project.name}</div>`)
      }
      tippy(info.el, {
        content,
        allowHTML: true,
      })
    },
    /**
     * 展开或收起时间范围
     */
    toggleTimeRange() {
      this.isTimeRangeExpanded = !this.isTimeRangeExpanded
      this.timeRange = this.isTimeRangeExpanded ? {
        minTime: '00:00:00',
        maxTime: '24:00:00',
      } : {
        minTime: '8:00:00',
        maxTime: '20:00:00',
      }
    },
    /**
     * 展示或隐藏 当前专家或客户在其他项目中已经安排的时间
     */
    toggleOtherArrangedScheduled() {
      this.show_other_arrange = !this.show_other_arrange
      if (this.show_other_arrange) {
        this.$emit('changeEvents', this.calendarEvents.concat(this.arranged_scheduled))
        this.customButtons.toggleArrangedButton.text = 'Hide Other Scheduled Calls'
      } else {
        this.arranged_scheduled = this.calendarEvents.filter(item => ['OTHER_ARRANGED', 'PROJECT_ARRANGED'].includes(item.creator_type))
        this.$emit('changeEvents', this.calendarEvents.filter(item => !['OTHER_ARRANGED', 'PROJECT_ARRANGED'].includes(item.creator_type)))
        this.customButtons.toggleArrangedButton.text = 'Display All Scheduled Calls'
      }
    },
    /**
     * 点击 提示是否删除 / 复制时间段
     * @param info
     */
    handleEventClick(info) {
      const types = ['ADVISOR', 'CLIENT_CONTACT']
      if (types.includes(info.event.extendedProps.creator_type)) {
        this.handleEventSelect({
          startStr: moment(info.event.start).utc().format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat,
          endStr: moment(info.event.end).utc().format('YYYY-MM-DDTHH:mm:ss') + this.tzFormat,
          title: 'PM',
        })
      }
      if (info.event.extendedProps.creator_type === 'PROJECT_ARRANGED') {
        const route = this.$router.resolve({
          name: 'TasksArrange',
          params: { project_id: info.event.extendedProps.project_id },
          query: { id: info.event.extendedProps.task_id },
        })
        window.open(route.href, '_blank')
      }

      if (info.event.extendedProps.creator_type === 'PM') {
        const confirmText = this.scheduler ? 'Cancel or Update this schedule?' : 'Confirm to cancel this schedule?'
        let confirmSettings = {}
        if (this.scheduler) {
          confirmSettings = {
            distinguishCancelAndClose: true,
            confirmButtonText: 'Update',
            cancelButtonText: 'Cancel',
            cancelButtonClass: 'el-button--danger',
            beforeClose: (action, instance, done) => {
              if (action === 'confirm') {
                this.$router.push({
                  name: 'TasksArrange',
                  params: { project_id: info.event.extendedProps.project_id },
                  query: { id: info.event.extendedProps.task_id },
                })
                done()
              } else if (action === 'cancel') {
                instance.confirmButtonLoading = true
                return API.cancelArrangedScheduleById(info.event.id).then(() => {
                  this.removeEvent(info)
                  done()
                }).finally(() => {
                  instance.confirmButtonLoading = false
                })
              } else {
                done()
              }
            },
          }
        }
        this.$confirm(confirmText, 'Notice', Object.assign({
          type: 'warning',
        }, confirmSettings)).then(() => {
          this.removeEvent(info)
        }, () => {
        })
      }
    },
    removeEvent(info) {
      const events_temp = JSON.parse(JSON.stringify(this.calendarEvents))
      const index = events_temp.findIndex(event => event.id === +info.event.id)
      console.log(events_temp, index)
      events_temp.splice(index, 1)
      this.$emit('delete')
      this.$emit('changeEvents', events_temp)
    },
  },
}
</script>

<style scoped lang="scss">
//@import '@fullcalendar/core/main.css';
//@import '@fullcalendar/timegrid/main.css';
//@import 'tippy.js/dist/tippy.css';

::v-deep .fc-time span {
  white-space: pre-wrap;
  word-break: break-word;
}
::v-deep .fc-event-container > a {
  width:40%;
}
</style>
