<template>
  <el-tooltip effect="dark"
              :content="singedTCDate"
              placement="top-start">
    <el-tag v-if="isNewAdvisor" type="success" class="advisor-new-tag">New</el-tag>
  </el-tooltip>
</template>

<script>
import moment from 'moment-timezone'

export default {
  name: 'NewAdvisorTag',
  props: {
    advisor: Object,
  },
  computed: {
    // 专家首次签署CT 30天内 标识为新专家
    isNewAdvisor() {
      if (this.advisor?.sourced_at) {
        const now = moment.tz(this.timeZone)
        return parseInt(Math.abs(now - moment.tz(this.advisor.sourced_at, this.timeZone)) / 1000 / 60 / 60 / 24) < 30
      } else {
        return false
      }
    },
    singedTCDate() {
      return this.isNewAdvisor ? 'Signed T&C on ' + moment.tz(this.advisor.sourced_at, this.timeZone).format('MM:DD:YY') : ''
    },

  },
}
</script>

<style scoped>
.advisor-new-tag {
  color: #257614;
  height: 16px;
  padding: 0 4px;
  line-height: 16px;
}
</style>
