<template>
  <div>
    <div
      v-if="displayLoading"
      class="messageBox messageBox-loading"
      v-html="html_loading"
    />
    <div
      v-if="displayNoData"
      class="messageBox messageBox-noData"
      v-html="html_noData"
    />
  </div>
</template>

<script>
/**
 * 加载状态
 * 提供 "加载中...", "无数据" 等占位框
 *
 *
 * 例子
 * <loading-status :status :data>
 *
 * 参数及定义
 * status:  0/true  加载中
 *          1/false 加载完毕
 * data:    undefined/[] 无数据
 *          Object/[...] 有数据
 *
 */
export default {
  name: 'LoadingStatus',
  props: {
    status: {
      type: [Number, Boolean],
      default: 0,
    },
    data: {
      type: [Object, Boolean, Array],
      default: () => [],
    },
  },
  data() {
    return {
      html_loading: 'Loading...',
      html_noData: 'No Data',
    }
  },
  computed: {
    displayLoading() {
      return [0, true].includes(this.status)
    },
    displayNoData() {
      const isLoaded = [1, false].includes(this.status)
      const isNoData = [undefined, null, false].includes(this.data) ||
        (Array.isArray(this.data) && (this.data.length === 0))

      return isLoaded && isNoData
    },
  },
}
</script>

<style scoped>

.messageBox {
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;

  color: #ccc;
  text-shadow: 1px 1px #ffffffb3;
  font-style: italic;
  text-align: center;
  font-size: 14px;
  padding: 2.4em 0;
}
</style>
