import Vue from 'vue'
import SvgIcon from '@/components/SvgIcon' // svg component

// register globally
Vue.component('svg-icon', SvgIcon)

// const req = require.context('./svg', false, /\.svg$/)
// const requireAll = requireContext => requireContext.keys().map(requireContext)
const req = import.meta.glob('./svg/*.svg', { eager: true })
const requireAll = (requireContext) => Object.keys(requireContext).map((key) => requireContext[key].default)
requireAll(req)
