import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  NProgress.start()

  // 设置标题
  document.title = getPageTitle(to.meta.title)

  //
  const hasToken = getToken()

  // Token 确认存在
  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      const userInfo = store.state.user.info
      const isRoutesLoaded = store.getters['permission/isRoutesLoaded']

      if (isRoutesLoaded) {
        store.commit('user/SET_ROLES', userInfo.roles)
        await store.dispatch('permission/generateRoutes', userInfo)
        next()
      } else {
        return store.dispatch('user/getInfo')
          .then(userInfo => {
            return store.dispatch('permission/generateRoutes', userInfo)
          })
          .then(accessRoutes => {
            router.addRoutes(accessRoutes)
            return next({ ...to, replace: true })
          })
          .catch(error => {
            // remove token and go to login page to re-login
            Message.error(error || 'Has Error')
            NProgress.done()
            return store.dispatch('user/resetToken')
              .then(() => {
                return router.push({
                  name: 'Login',
                  search: {
                    redirect: to.path,
                  },
                })
              })
          })
      }
    }
  } else {
    /* 清理路由 */
    await store.dispatch('permission/cleanup')

    // 跳转登陆页
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
