import { AppHttpService } from '@/utils/request'

export default {
  async getCompanyList(params) {
    return AppHttpService({
      url: '/companies',
      method: 'get',
      params,
    })
  },

  async getCompanyListByEs(data) {
    return AppHttpService({
      url: '/companies/search/es',
      method: 'post',
      data,
    })
  },

  async getCompanyListByEsDb(data, extra = '') {
    return AppHttpService({
      url: '/companies/search/es/db' + extra,
      method: 'post',
      data,
    })
  },

  async getCompanyBlackList(params) {
    return AppHttpService({
      url: '/companies/blacklist',
      method: 'get',
      params,
    })
  },

  async getCompanyAliasesList(params) {
    return AppHttpService({
      url: '/companies/aliases',
      method: 'get',
      params,
    })
  },

  async getCompany(id) {
    return AppHttpService({
      url: '/companies/' + id,
      method: 'get',
      params: {
        extra: 'blacklist_record.operator,create_user_id,creator,aliases',
      },
    })
  },
  async createCompany(data) {
    return AppHttpService({
      url: '/companies',
      method: 'post',
      data,
    })
  },
  async updateCompany(data) {
    return AppHttpService({
      url: `/companies/${data.id}?extra=blacklist_record.operator`,
      method: 'patch',
      data,
    })
  },
  async deleteBlacklistOne(data) {
    return AppHttpService({
      url: `/companies/blacklist/${data.id}`,
      method: 'delete',
    })
  },
  async getCompanyValueChain(id, params) {
    return AppHttpService({
      url: `/companies/${id}/graph`,
      method: 'get',
      params,
    })
  },
  async updateCompanyValueChain(id, data) {
    return AppHttpService({
      url: `/companies/${id}/relations`,
      method: 'put',
      data,
    })
  },
}
