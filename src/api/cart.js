import { AppHttpService } from '@/utils/request'

export default {
  async getShoppingCartItems(query, data) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: `/cart_items/search_v2?${url_query}`,
      method: 'post',
      data,
    })
  },
  async getShoppingCartAllTopItems(query, data) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: `/cart_items/top/search_v2?${url_query}`,
      method: 'post',
      data,
    })
  },
  async addToShoppingCartBatch(data) {
    return AppHttpService({
      url: '/cart_items/batch',
      method: 'post',
      data,
    })
  },
  async removeShoppingCartItemsBatch(query) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: `/cart_items/batch?${url_query}`,
      method: 'delete',
    })
  },
  async updateShoppingCartItemsSort(data) {
    return AppHttpService({
      url: `/cart_items/reposition`,
      method: 'patch',
      data,
    })
  },
  async getAdvisorTags(data) {
    return AppHttpService({
      url: `/user_advisor_tags/search`,
      method: 'post',
      data,
    })
  },
  async updateAdvisorTags(id, data) {
    return AppHttpService({
      url: `/advisors/${id}/user_advisor_tags`,
      method: 'put',
      data,
    })
  },
  async updateTag(id, data) {
    return AppHttpService({
      url: `/user_advisor_tags/${id}`,
      method: 'put',
      data,
    })
  },
  async deleteTag(id) {
    return AppHttpService({
      url: `/user_advisor_tags/${id}`,
      method: 'delete',
    })
  },
}
