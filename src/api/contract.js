import { AppHttpService } from '@/utils/request'

function getFileName(response) {
  const cd = response.headers['content-disposition']
  const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
  const match = regex.exec(cd)
  const file_name = match[1].replace(/"/g, '')
  try {
    return decodeURIComponent(file_name)
  } catch (e) {
    return file_name
  }
}

export default {
  async getContractList(params) {
    return AppHttpService({
      url: '/contract',
      method: 'get',
      params,
    })
  },

  async getContractDetail(id, params) {
    return AppHttpService({
      url: '/contract/' + id,
      method: 'get',
      params,
    })
  },

  async createContract(data) {
    return AppHttpService({
      url: 'contract',
      method: 'post',
      data,
    })
  },

  async updateContract(data) {
    return AppHttpService({
      url: 'contract/' + data.id,
      method: 'put',
      data,
    })
  },

  async updateContractItem(id, data) {
    return AppHttpService({
      url: `contract/` + id,
      method: 'put',
      data,
    })
  },

  async getRevenueList(params) {
    return AppHttpService({
      url: '/revenue',
      method: 'get',
      params: params,
      timeout: 30000,
    })
  },

  async downloadRevenueList(params) {
    return AppHttpService({
      url: '/revenue/download/excel_v1',
      method: 'get',
      params: params,
      responseType: 'blob',
    })
      .then(response => {
        this.content = response.data
        this.filename = getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  async createRevenue(data) {
    return AppHttpService({
      url: '/revenue',
      method: 'post',
      data,
    })
  },

  async getRevenueClientUsage(params) {
    return AppHttpService({
      url: '/revenue/client_usage_table',
      method: 'get',
      params: params,
      timeout: 30000,
    })
  },

  async requestContract(id, data) {
    return AppHttpService({
      url: `/contract/${id}/request`,
      method: 'patch',
      data,
    })
  },

  async generationReject(id, data) {
    return AppHttpService({
      url: `/contract/${id}/request_reject`,
      method: 'patch',
      data,
    })
  },

  async generationApprove(id, data) {
    return AppHttpService({
      url: `/contract/${id}/request_complete`,
      method: 'patch',
      data,
    })
  },

  async applyContract(id, data) {
    return AppHttpService({
      url: `/contract/${id}/apply`,
      method: 'patch',
      data,
    })
  },

  async approvalReject(id, data) {
    return AppHttpService({
      url: `/contract/${id}/apply_reject`,
      method: 'patch',
      data,
    })
  },

  async approvalApprove(id, data) {
    return AppHttpService({
      url: `/contract/${id}/apply_approve`,
      method: 'patch',
      data,
    })
  },

  updatePaymentTerm(id, data) {
    return AppHttpService({
      url: `contract/payment_item/${id}`,
      method: 'put',
      data,
    })
  },
  async adjustContractStartTime(data) {
    return AppHttpService({
      url: `contract/${data.contract_id}/adjust_start_time`,
      method: 'patch',
      data,
    })
  },

  async expireContract(id) {
    return AppHttpService({
      url: `contract/${id}/change_to_expired`,
      method: 'patch',
    })
  },

  async deleteContract(id) {
    return AppHttpService({
      url: `/contract/${id}`,
      method: 'delete',
    })
  },

  async createInvoice(data) {
    return AppHttpService({
      url: `/invoice?zone_id=${data.zone_id}`,
      method: 'post',
      data,
    })
  },

  async downloadInvoice(id) {
    return AppHttpService({
      url: `/invoice/${id}/doc`,
      method: 'get',
      responseType: 'blob',
    })
  },

  async getUsageExcelTemplate() {
    return AppHttpService({
      url: `/usage/excel_template/all/available_fields`,
      method: 'get',
    })
  },

  async downloadUsageReport(params) {
    return AppHttpService({
      url: `/revenue/usage_report`,
      method: 'get',
      params,
      responseType: 'blob',
    })
  },

  async downloadInvoiceUsage(id, params) {
    return AppHttpService({
      url: `/invoice/${id}/usage_report`,
      method: 'get',
      responseType: 'blob',
      params,
    })
  },

  async getInvoiceList(params) {
    return AppHttpService({
      url: `/invoice`,
      method: 'get',
      params,
    })
  },

  async deleteInvoice(id) {
    return AppHttpService({
      url: `/invoice/${id}`,
      method: 'delete',
    })
  },

  async downloadContractList(params) {
    return AppHttpService({
      url: '/contract/download/excel_v1',
      method: 'get',
      params: params,
      responseType: 'blob',
    })
      .then(response => {
        this.content = response.data
        this.filename = getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  async downloadInvoiceList(params) {
    return AppHttpService({
      url: '/invoice/excel',
      method: 'get',
      params: params,
      responseType: 'blob',
    })
      .then(response => {
        this.content = response.data
        this.filename = getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },
}
