import { AppHttpService } from '@/utils/request'

export default {
  async getUsersList(data) {
    return AppHttpService({
      url: '/users',
      method: 'get',
      params: data,
    })
  },
  async getUserProfile(data) {
    return AppHttpService({
      url: '/users/me',
      method: 'get',
      params: data,
    })
  },
  async createUser(data) {
    return AppHttpService({
      url: `/users`,
      method: 'post',
      data,
    })
  },
  async updateUserProfile(id, data) {
    return AppHttpService({
      url: `/users/${id}`,
      method: 'put',
      data,
    })
  },

  async getUserPositionList() {
    return AppHttpService({
      url: '/user_position',
      method: 'get',
    })
  },

  async getUserTeamList(data) {
    return AppHttpService({
      url: '/teams',
      method: 'get',
      params: data,
    })
  },

  async addUserTeam(data) {
    return AppHttpService({
      url: '/teams',
      method: 'post',
      data,
    })
  },

  async updateUserTeam(data) {
    return AppHttpService({
      url: `/teams/${data.id}`,
      method: 'put',
      data,
    })
  },

  async deleteUserTeam(id) {
    return AppHttpService({
      url: `/teams/${id}`,
      method: 'delete',
    })
  },

  async getTeamMembers(data) {
    return AppHttpService({
      url: `/teams/team_members`,
      method: 'get',
      params: data,
    })
  },

  async addTeamMember(team_id, data) {
    return AppHttpService({
      url: `/teams/${team_id}/team_members/batch`,
      method: 'post',
      data,
    })
  },

  async deleteTeamMember(team_id, data) {
    return AppHttpService({
      url: `/teams/${team_id}/team_members/batch`,
      method: 'delete',
      data: data,
    })
  },

  async updateUserPreference(id, data) {
    return AppHttpService({
      url: `/users/${id}/preference`,
      method: 'put',
      data,
    })
  },

  // user api token
  async getUserToken(params) {
    return AppHttpService({
      url: `/users/tokens`,
      method: 'get',
      params,
    })
  },

  // async getUserTokenById(token_id) {
  //   return AppHttpService({
  //     url: `/users/tokens/${token_id}`,
  //     method: 'get',
  //   })
  // },

  async createUserToken(data) {
    return AppHttpService({
      url: `/users/tokens`,
      method: 'post',
      data,
    })
  },

  async updateUserToken(token_id, data) {
    return AppHttpService({
      url: `/users/tokens/${token_id}/refresh`,
      method: 'patch',
      data,
    })
  },

  async deleteUserToken(token_id) {
    return AppHttpService({
      url: `/users/tokens/${token_id}`,
      method: 'delete',
    })
  },

  // key  由前端自己定义 前端自己存 自己取

  // 返回值 有ID 代表有这个key
  // project_task_cols_width_{project_id}: 不同项目task表格的列宽

  async getUserRemoteStorage(key) {
    return AppHttpService({
      url: `/users/remote_storage/${key}`,
      method: 'get',
    })
  },

  async updateUserRemoteStorage(key, data) {
    return AppHttpService({
      url: `/users/remote_storage/${key}`,
      method: 'put',
      data: { value: data },
    })
  },

  async deleteUserRemoteStorage(key) {
    return AppHttpService({
      url: `/users/remote_storage/${key}`,
      method: 'delete',
    })
  },

  async clearUserRemoteStorage() {
    return AppHttpService({
      url: `/users/remote_storage/clear`,
      method: 'patch',
    })
  },
}

