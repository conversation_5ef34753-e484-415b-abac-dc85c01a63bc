import { AppHttpService } from '@/utils/request'

export default {
  async getRolesList() {
    return AppHttpService({
      url: '/authority/roles',
      method: 'get',
      params: {
        extra: 'permissions,permissions.permission',
      },
    })
  },
  async createRole(data) {
    return AppHttpService({
      url: '/authority/roles',
      method: 'post',
      data,
    })
  },
  async updateRole(id, data) {
    return AppHttpService({
      url: '/authority/roles/' + id,
      method: 'patch',
      data,
    })
  },
  async deleteRole(id) {
    return AppHttpService({
      url: '/authority/roles/' + id,
      method: 'delete',
    })
  },
  async getPermissionsList() {
    return AppHttpService({
      url: '/authority/permissions',
      method: 'get',
    })
  },
  async createPermission(data) {
    return AppHttpService({
      url: '/authority/permissions',
      method: 'post',
      data,
    })
  },
  async updatePermission(id, data) {
    return AppHttpService({
      url: `/authority/permissions/${id}`,
      method: 'patch',
      data,
    })
  },
  async deletePermission(id) {
    return AppHttpService({
      url: `/authority/permissions/${id}`,
      method: 'delete',
    })
  },
  async updateUserRole(id, data) {
    return AppHttpService({
      url: `authority/users/${id}/roles`,
      method: 'patch',
      data,
    })
  },
}
