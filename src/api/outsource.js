import { AppHttpService } from '@/utils/request'

export default {
  async getAvailableRegions(params) {
    return AppHttpService({
      url: '/outsource/available_regions',
      method: 'get',
      params,
    })
  },
  async requestOutsourceTeamSupport(data) {
    return AppHttpService({
      url: '/outsource/outsource_team_support',
      method: 'post',
      data,
    })
  },
  async exportTasksToOutsource(data) {
    return AppHttpService({
      url: '/outsource/export_tasks',
      method: 'post',
      data,
    })
  },
  async sendCaToSupport(data) {
    return AppHttpService({
      url: '/outsource/task_ca_completed',
      method: 'post',
      data,
    })
  },

  async sendCaLinkToSupport(data) {
    return AppHttpService({
      url: '/outsource/send_task_ca_link',
      method: 'post',
      data,
    })
  },

  async generateCALink(taskId) {
    return AppHttpService({
      url: '/outsource/reset_task_ca',
      method: 'get',
      params: { task_id: parseInt(taskId) },
    })
  },

  async getOutsourceApprovalList(params) {
    return AppHttpService({
      url: '/outsource_approval',
      method: 'get',
      params,
    })
  },

  async actionOutsourceApproval(data) {
    return AppHttpService({
      url: '/outsource_approval/project_external_event',
      method: 'post',
      data,
    })
  },
}
