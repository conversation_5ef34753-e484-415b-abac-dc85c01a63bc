import { AppHttpService } from '@/utils/request'

export default {
  async getCallTrackerList(params) {
    return AppHttpService({
      url: '/metrics/call_tracker',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async saveCallTrackerNote(data) {
    return AppHttpService({
      url: '/metrics/call_track_note',
      method: 'post',
      data,
      timeout: 60000,
    })
  },
  async getCallTrackerDashboard(params) {
    return AppHttpService({
      url: '/metrics/dashboard_v2',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async getCallTrackerClientDashboard(params) {
    return AppHttpService({
      url: '/metrics/client_dashboard',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async getCallsTarget(params) {
    return AppHttpService({
      url: '/metrics/research_team/calls_target',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async updateCallsTarget(data) {
    return AppHttpService({
      url: '/metrics/research_team/calls_target',
      method: 'patch',
      data,
      timeout: 60000,
    })
  },
  async getUserTarget(params) {
    return AppHttpService({
      url: '/metrics/user/target',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async updateUserTarget(data) {
    return AppHttpService({
      url: '/metrics/user/target',
      method: 'patch',
      data,
      timeout: 60000,
    })
  },
  async getClientHourAdjuster(params) {
    return AppHttpService({
      url: '/metrics/client_hour_adjuster',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async updateClientHourAdjuster(data) {
    return AppHttpService({
      url: '/metrics/client_hour_adjuster',
      method: 'patch',
      data,
      timeout: 60000,
    })
  },
  async getAdvisorData(params) {
    return AppHttpService({
      url: '/users/advisor_data',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async downloadAdvisorData(params) {
    return AppHttpService({
      url: '/users/advisor_data/excel',
      method: 'get',
      responseType: 'blob',
      params,
      timeout: 60000,
    }).then(response => {
      this.downloadThroughTagA(response)
    })
  },
  async getProjectData(params) {
    return AppHttpService({
      url: '/projects/list/project_data',
      method: 'get',
      params,
      timeout: 60000,
    })
  },
  async downloadProjectData(params) {
    return AppHttpService({
      url: '/projects/list/project_data/excel',
      method: 'get',
      responseType: 'blob',
      params,
      timeout: 60000,
    }).then(response => {
      this.downloadThroughTagA(response)
    })
  },

  async downloadCallTrackerData(params) {
    return AppHttpService({
      url: '/metrics/call_tracker/excel',
      method: 'get',
      responseType: 'blob',
      params,
      timeout: 60000,
    }).then(response => {
      this.downloadThroughTagA(response)
    })
  },

  getFileName(response) {
    const cd = response.headers['content-disposition']
    const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const match = regex.exec(cd)
    const file_name = match[1].replace(/"/g, '')
    try {
      return decodeURIComponent(file_name)
    } catch (e) {
      return file_name
    }
  },

  downloadThroughTagA(response) {
    const content = response.data
    const filename = this.getFileName(response)
    const blob = new Blob([content], { type: 'application/excel' })
    if (window.navigator.msSaveOrOpenBlob) {
      // 兼容IE10
      navigator.msSaveBlob(blob, filename)
    } else {
      //  chrome/firefox
      const aTag = document.createElement('a')
      aTag.download = filename
      aTag.href = URL.createObjectURL(blob)
      aTag.click()
      URL.revokeObjectURL(aTag.href)
    }
  },
}
