import { AppHttpService } from '@/utils/request'

export default {
  async getLocation() {
    return new Promise((resolve, reject) => {
      const local_storage_location = localStorage.getItem('location') ? JSON.parse(
        localStorage.getItem('location')) : undefined
      if (!local_storage_location) {
        Promise.all([this.getLocationList(), this.getLocationVersion()])
          .then(([list, version]) => {
            const data = {
              version,
              list,
            }
            localStorage.setItem('location', JSON.stringify(data))
            resolve(list)
          })
      } else {
        const version_current = local_storage_location.version
        this.getLocationVersion().then(version_now => {
          if (version_current === version_now) {
            resolve(local_storage_location.list)
          } else {
            this.getLocationList().then(list => {
              const data = {
                version: version_now,
                list,
              }
              localStorage.setItem('location', JSON.stringify(data))
              resolve(list)
            })
          }
        })
      }
    })
  },
  async getLocationList() {
    return AppHttpService({
      url: `/locations`,
      method: 'get',
      params: { extra: 'country' },
    })
  },
  async getLocationVersion() {
    return AppHttpService({
      url: `/locations/current_version`,
      method: 'get',
    })
  },
  async checkName(data) {
    return AppHttpService({
      url: `/common/${data.module}/exist`,
      method: 'get',
      params: data.params,
    })
  },
  async getContactInfo(data) {
    return AppHttpService({
      url: '/contact_info',
      method: 'get',
      params: data,
    })
  },

  async getPrivacyData(data) {
    return AppHttpService({
      url: '/privacy',
      method: 'patch',
      data,
    })
  },

  async searchMailAddress(data) {
    return AppHttpService({
      url: '/email_addresses?pagination=false',
      method: 'post',
      data,
    })
  },

  async searchLinkedinIndustry(data) {
    return AppHttpService({
      url: '/linkedin_industry_code',
      method: 'get',
      params: data,
    })
  },

  // third party
  async searchWhaleWisdom(data) {
    return AppHttpService({
      url: 'thirdparty/whale-wisdom/filer_look_up',
      method: 'post',
      data,
    })
  },

  // async searchWhaleWisdom(data) {
  //   return AppHttpService({
  //     url: 'thirdparty/whale-wisdom/filers',
  //     method: 'get',
  //     params: data,
  //   })
  // },

  async getWhaleWisdomHoldings(data) {
    return AppHttpService({
      url: 'thirdparty/whale_wisdom_holdings/fetch',
      method: 'get',
      params: data,
    })
  },

  async sendTemplateToBcgForApproval(data) {
    return AppHttpService({
      url: 'thirdparty/bcg_hub/send_template_to_bcg_for_approval',
      method: 'post',
      data,
    })
  },

  async getToBcgTemplates(params) {
    return AppHttpService({
      url: 'thirdparty/bcg_hub/to_bcg_templates',
      method: 'get',
      params,
    })
  },

  async uploadFile(data) {
    return AppHttpService({
      url: import.meta.env.VITE_APP_US_DB_UPLOAD + '/rm',
      method: 'post',
      data,
    })
  },

}
