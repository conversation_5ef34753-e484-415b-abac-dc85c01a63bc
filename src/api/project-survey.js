import { AppHttpService } from '@/utils/request'

export default {
  async updateProjectDecipherStatus(project_id) {
    return AppHttpService({
      url: `/projects/decipher/${project_id}/update`,
      method: 'patch',
    })
  },

  async updateTaskDecipherSurveyStatus(task_id, bodyData) {
    return AppHttpService({
      url: `/projects/decipher/manual_update`,
      method: 'patch',
      data: bodyData,
    })
  },
}
