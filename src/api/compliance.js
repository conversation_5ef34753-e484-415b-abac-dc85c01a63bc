import { AppHttpService } from '@/utils/request'

export default {
  async getGlobalSetting(data) {
    return AppHttpService({
      url: '/app_config_entries',
      method: 'get',
      params: data,
    })
  },

  async updateAllowedToSendBeforeTCSigned(data, type) {
    return AppHttpService({
      url: `/app_config_entries/${type}`,
      method: 'put',
      data,
    })
  },

  async getAllowedToSendBeforeTCSigned(type) {
    return AppHttpService({
      url: `/app_config_entries/${type}`,
      method: 'get',
    })
  },

  async getAppConfigEntriesByKey(key) {
    return AppHttpService({
      url: `/app_config_entries/${key}`,
      method: 'get',
    })
  },

}
