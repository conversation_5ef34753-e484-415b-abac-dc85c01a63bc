import { AppHttpService } from '@/utils/request'

export default {
  async getSchedule() {
    return AppHttpService({
      url: '/schedule',
      method: 'get',
      params: {
        param_preset: 'PM',
        extra: 'advisor,task,task.advisor_profile.company,task.project',
      },
    })
  },
  async getTaskSchedule(params) {
    return AppHttpService({
      url: '/schedule',
      method: 'get',
      params,
    })
  },
}
