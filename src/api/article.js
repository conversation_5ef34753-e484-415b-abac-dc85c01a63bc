import { AppHttpService } from '@/utils/request'

export default {
  async getArticlesList(query) {
    return AppHttpService({
      url: '/user_articles',
      method: 'get',
      params: Object.assign({
        extra: 'create_user,update_user',
      }, query),
    })
  },
  async getArticleDetail(id) {
    return AppHttpService({
      url: '/user_articles/' + id,
      method: 'get',
      params: {
        extra: 'create_user,update_user',
      },
    })
  },
  async createArticle(data) {
    return AppHttpService({
      url: '/user_articles',
      method: 'post',
      data,
    })
  },
  async updateArticle(data) {
    return AppHttpService({
      url: '/user_articles/' + data.id,
      method: 'put',
      data,
    })
  },
  async deleteArticle(id) {
    return AppHttpService({
      url: '/user_articles/' + id,
      method: 'delete',
    })
  },

  async addLinkedinScraperVersion(data) {
    return AppHttpService({
      url: '/client_app_version',
      method: 'post',
      data,
    })
  },

  async updateLinkedinScraperVersion(id, data) {
    return AppHttpService({
      url: `/client_app_version/${id}`,
      method: 'put',
      data,
    })
  },

  async getLinkedinScraperVersion(params) {
    return AppHttpService({
      url: '/client_app_version',
      method: 'get',
      params: params,
    })
  },
}
