import { AppHttpService } from '@/utils/request'

export default {
  async searchConsultant(data) {
    return AppHttpService({
      url: '/advisors/search',
      method: 'post',
      data,
    })
  },
  async searchConsultantByEs(data) {
    return AppHttpService({
      url: '/advisors/es/search',
      method: 'post',
      data,
    })
  },
  async getConsultantList(params) {
    return AppHttpService({
      url: '/advisors',
      method: 'get',
      params: params,
    })
  },
  async getConsultant(id, params) {
    return AppHttpService({
      url: '/advisors/' + id,
      method: 'get',
      params: params,
    })
  },
  async createConsultant(data) {
    // 新创建的job的company数据结构与es不匹配 不能传给后端
    if (data.jobs?.length) {
      data.jobs.forEach(item => {
        delete item.company
      })
    }
    return AppHttpService({
      url: '/advisors',
      method: 'post',
      data,
    })
  },

  /*
  返回值：{
  duplicate:{},表示已经有相同联系方式的专家
  entity:{}如果已经有重复专家，会被返回创建时填写的值;否则返回创建成功的专家信息
  }
  * */
  async createConsultantV2(data) {
    // 新创建的job的company数据结构与es不匹配 不能传给后端
    if (data.jobs?.length) {
      data.jobs.forEach(item => {
        delete item.company
      })
    }
    return AppHttpService({
      url: '/advisors/v2',
      method: 'post',
      data,
    })
  },
  async updateConsultant(data) {
    // 新创建的job的company数据结构与es不匹配 不能传给后端
    if (data.jobs?.length) {
      data.jobs.forEach(item => {
        delete item.company
      })
    }
    return AppHttpService({
      url: '/advisors/' + data.id +
        '?extra=location,jobs.company,contact_infos',
      method: 'put',
      data,
    })
  },

  async updateConsultantStatus(advisor_id, data) {
    return AppHttpService({
      url: `advisors/${advisor_id}/update_status?extra=status_change_logs.project`,
      method: 'patch',
      data,
    })
  },

  async sendConsultantPortal(data) {
    return AppHttpService({
      url: '/portal/portal_advisor',
      method: 'post',
      data,
    })
  },

  async sendPaymentConfirmToConsultant(data) {
    return AppHttpService({
      url: '/portal/advisor_payment_form',
      method: 'post',
      data,
    })
  },
  async getBankAccount(id) {
    return AppHttpService({
      url: `/advisors/${id}/bank_account`,
      method: 'get',
    })
  },
  async updateBankAccount(id, data) {
    return AppHttpService({
      url: `/advisors/${id}/bank_account`,
      method: 'put',
      data,
    })
  },
  async createConsultantFromCNDB(data) {
    return AppHttpService({
      url: `/advisors/cn_db`,
      method: 'post',
      data,
    })
  },

  async consultantCommunications(advisor_id, data) {
    return AppHttpService({
      url: `/advisors/${advisor_id}/communication_records`,
      params: data,
      method: 'get',
    })
  },

  async getConsultantPaymentInfo(advisor_id, extra) {
    return AppHttpService({
      url: `/advisors/${advisor_id}/payment_forms`,
      method: 'get',
      params: { pagination: false, extra },
    })
  },

  async updateConsultantPaymentInfo(advisor_id, data) {
    return AppHttpService({
      url: `/advisors/${advisor_id}/payment_forms/${data.project_sub_type}`,
      method: 'put',
      data,
    })
  },

  async privacyView(data) {
    return AppHttpService({
      url: '/privacy',
      method: 'patch',
      data,
    })
  },

  async getAdvisorProjectList(advisor_id, data) {
    return AppHttpService({
      url: `/advisors/${advisor_id}/project_info`,
      method: 'get',
      params: data,
    })
  },

  async deleteAdvisor(advisor_id) {
    return AppHttpService({
      url: `/advisors/${advisor_id}/gdpr`,
      method: 'delete',
    })
  },

  async advisorReferrals(data) {
    return AppHttpService({
      url: `/advisors/referrals`,
      method: 'post',
      data,
    })
  },

  async advisorTagsProjectSQ(params) {
    return AppHttpService({
      url: `/advisors/tags/project_sq`,
      method: 'get',
      params,
    })
  },

  async sortAdvisorTagsProjectSQ(data) {
    return AppHttpService({
      url: `/advisors/tags/project_sq/reposition`,
      method: 'patch',
      data,
    })
  },
  async advisorTagsProjectSQOptions(data) {
    return AppHttpService({
      url: `/advisors/tags/maps/options/batch`,
      method: 'put',
      data,
    })
  },

  async reevaluateAdvisorTagsProjectSQ(tag_id) {
    return AppHttpService({
      url: `/advisors/tags/project_sq/reevaluate/${tag_id}`,
      method: 'patch',
    })
  },

  async advisorTagsJobExperience(params) {
    return AppHttpService({
      url: `/advisors/tags/job_experience`,
      method: 'get',
      params,
    })
  },

  async reevaluateAdvisorTagsJobExperience(tag_id) {
    return AppHttpService({
      url: `/advisors/tags/job_experience/reevaluate/${tag_id}`,
      method: 'patch',
    })
  },

  async getAdvisorTagsByTicker(params) {
    return AppHttpService({
      url: `/advisors/tags`,
      method: 'get',
      params,
    })
  },

  async getAdvisorTagsHoldingSubscriptions(data) {
    return AppHttpService({
      url: `/advisors/tags/client_holding_subs/search?extra=tag_subscriptions`,
      method: 'post',
      data,
    })
  },

  async saveHoldingSubscriptions(data) {
    return AppHttpService({
      url: `/advisors/tags/client_holding_subs`,
      method: 'post',
      data,
    })
  },

  async updateHoldingSubscriptions(id, data) {
    return AppHttpService({
      url: `/advisors/tags/client_holding_subs/${id}`,
      method: 'put',
      data,
    })
  },

  async validateNpiNumber(params) {
    return AppHttpService({
      url: `/advisors/validate_npi_number`,
      method: 'get',
      params,
    })
  },

  async checkAdvisorIsBlocked(id, type) {
    return AppHttpService({
      url: `client_blacklist/blocked_status/${id}?type=${type}`,
      method: 'get',
    })
  },

  async getAdvisorBlackList(data) {
    return AppHttpService({
      url: `client_blacklist`,
      method: 'get',
      params: data,
    })
  },

  async addToClientBlackList(id, type, data) {
    return AppHttpService({
      url: `client_blacklist/${id}?type=${type}`,
      method: 'put',
      data,
    })
  },

  async removeFromClientBlackList(id, type, data) {
    return AppHttpService({
      url: `client_blacklist/${id}?type=${type}`,
      method: 'delete',
      data,
    })
  },

  async blockAdvisorInAllClient(id, type) {
    return AppHttpService({
      url: `client_blacklist/block/${id}?type=${type}`,
      method: 'put',
    })
  },

  async unblockAdvisorInAllClient(id, type) {
    return AppHttpService({
      url: `client_blacklist/unblock/${id}?type=${type}`,
      method: 'put',
    })
  },

  async manuallyOverrideListWiseStatus(data) {
    return AppHttpService({
      url: `/email_validation_records/validate/manually`,
      method: 'patch',
      data,
    })
  },
}
