import { AppHttpService } from '@/utils/request'
import store from '../store'

export default {

  getFileName(response) {
    const cd = response.headers['content-disposition']
    const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const match = regex.exec(cd)
    const file_name = match[1].replace(/"/g, '')
    try {
      return decodeURIComponent(file_name)
    } catch (e) {
      return file_name
    }
  },

  async getProjectList(data) {
    return AppHttpService({
      url: '/projects',
      method: 'get',
      params: data,
    })
  },
  async searchProject(data) {
    return AppHttpService({
      url: '/projects/search',
      method: 'post',
      data,
    })
  },
  async searchProjectNew(data) {
    return AppHttpService({
      url: '/projects/search_new',
      method: 'post',
      data,
    })
  },
  async getProject(id, extra) {
    return AppHttpService({
      url: `/projects/${id}`,
      method: 'get',
      params: {
        extra: extra || [
          'contract.used_size',
          'advisors',
          'setting',
          'members.user',
          'project_client_contacts.client_contact.contact_infos',
          'project_client_contacts.client_contact.contact_infos_without_mosaic',
          'tags.tag',
          'client.contracts',
          'client.am_team',
          'client.preference',
          'client.account_managers.user',
          'client.compliance_preference',
          'client.custom_fields',
          'client.client_contacts',
          'client.client_contacts.contact_infos_without_mosaic',
          'tickers.company',
          'notes.creator',
          'investment_target_companies',
          'legal_rules.need_compliance_approval_latest_change.create_by',
          'outsource_approvals.events',
          'client_office',
          'call_opportunity_sum_jit',
          'bcg_hub_project',
        ].join(),
      },
    })
  },

  async saveProjectNotes(id, data) {
    return AppHttpService({
      url: `/projects/${id}/notes`,
      method: 'patch',
      data: data,
    })
  },
  async updateProjectNotes(id, note_id, data) {
    return AppHttpService({
      url: `/projects/${id}/notes/${note_id}`,
      method: 'patch',
      data: data,
    })
  },
  async deleteProjectNotes(project_id, note_id) {
    return AppHttpService({
      url: `/projects/${project_id}/notes/${note_id}`,
      method: 'delete',
    })
  },
  async createProject(data) {
    return AppHttpService({
      url: '/projects',
      method: 'post',
      data,
    })
      .then(resp => {
        return new Promise((resolve, reject) => {
          store.dispatch('app/pushActivatedEvents',
            [{ name: 'ProjectList', event: 'update' }])
          resolve(resp)
        })
      })
  },

  async commonProjectCreate(data) {
    return AppHttpService({
      url: '/projects',
      method: 'post',
      data,
    })
  },

  async updateCommonProject(data) {
    return AppHttpService({
      url: `/projects/${data.id}`,
      method: 'put',
      data,
    })
  },

  async commonProjectSetContract(project_id, contract_id) {
    return AppHttpService({
      url: `/projects/${project_id}/set_contract/${contract_id}`,
      method: 'patch',
    })
  },

  async closeProject(project_id, params) {
    return AppHttpService({
      url: `/projects/${project_id}/close?send_notification_to_advisor_ids=${params.send_notification_to_advisor_ids}`,
      method: 'patch',
    })
  },

  async closeProjects(project_ids) {
    return AppHttpService({
      url: `/projects/close?project_ids=${project_ids}`,
      method: 'patch',
    })
  },

  async updateProject(data) {
    return AppHttpService({
      url: `/projects/${data.id}`,
      method: 'put',
      data,
    })
      .then(resp => {
        return new Promise((resolve, reject) => {
          store.dispatch('app/pushActivatedEvents',
            [{ name: 'ProjectList', event: 'update' }])
          resolve(resp)
        })
      })
  },

  async updateProjectSlackChannel(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/update_project_slack_channel`,
      method: 'patch',
      data,
    })
  },

  async updateBatchProject(data) {
    return AppHttpService({
      url: `/projects/`,
      method: 'put',
      data,
    })
  },
  async moveToAngle(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/tasks/move_to_angle`,
      method: 'patch',
      data,
    })
  },
  async getTags(data) {
    return AppHttpService({
      url: `/tags`,
      method: 'get',
      params: data,
    })
  },
  async saveTags(data) {
    return AppHttpService({
      url: `/tags`,
      method: 'post',
      data,
    })
  },

  async getProjectCodeList(data) {
    return AppHttpService({
      url: '/projects/typeahead/code',
      method: 'get',
      params: data,
    })
  },

  // async getTickers(data) {
  //   return AppHttpService({
  //     url: `/tickers`,
  //     method: 'get',
  //     params: data,
  //   })
  // },
  // async saveTickers(data) {
  //   return AppHttpService({
  //     url: `/tickers`,
  //     method: 'post',
  //     data,
  //   })
  // },

  /* Project Settings */
  async getProjectSettings(id) {
    return AppHttpService({
      url: `/projects/${id}/setting`,
      method: 'get',
    })
  },
  async updateProjectSettings(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/setting/${data.id}`,
      method: 'put',
      data: data,
    })
  },

  /* Advisor */
  async getProjectAdvisorList(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/tasks`,
      method: 'get',
      params: data,
    })
  },
  // 专家加入项目
  async checkBeforeAdvisorAddToProject(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/tasks/batch/pre_creation_validate`,
      method: 'patch',
      data: data,
      timeout: 30000,
    })
  },
  // 专家当前雇主是否与项目 investment target 冲突
  async checkAdvisorConflictWithInvestmentProjectTarget(data) {
    return AppHttpService({
      url: '/projects/pre_validate_investment_targets',
      method: 'post',
      data: data,
      timeout: 30000,
    })
  },

  async addAdvisor(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/tasks`,
      method: 'patch',
      data: data,
      timeout: 30000,
    })
  },
  async getTaskDetail(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/tasks/${data.id}`,
      method: 'get',
      params: data.params,
    })
  },
  async getTasksDetail(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/tasks`,
      method: 'get',
      params: data.params,
    })
  },
  // 修改task
  async updateTask(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/tasks/${data.id}?extra=support,investor_call`,
      method: 'put',
      data: data,
    })
  },
  async updateTaskAdvisorProfile(task_id, data) {
    return AppHttpService({
      url: `/tasks/${task_id}/advisor_profile/${data.id}?extra=company`,
      method: 'put',
      data: data,
    })
  },
  async updateTaskScheduling(task_id, data) {
    return AppHttpService({
      url: `/tasks/${task_id}/scheduling`,
      method: 'patch',
      data: data,
    })
  },
  async updateTaskDisplayLocked(task_id, val) {
    return AppHttpService({
      url: `/tasks/${task_id}?display_id_locked=${val}`,
      method: 'put',
    })
  },

  async getExpertUsage(data) {
    return AppHttpService({
      url: '/tasks/expert_usage',
      method: 'get',
      params: data,
    })
  },

  // 删除task
  async deleteTask(data) {
    return AppHttpService({
      url: `/projects/tasks`,
      method: 'delete',
      data: data,
    })
  },

  async searchTask(data) {
    return AppHttpService({
      url: `/tasks/search?extra=advisor.contact_infos,angle,advisor.contact_infos_without_mosaic`,
      method: 'post',
      data,
    })
  },

  async checkDownloadTemplate(params) {
    return AppHttpService({
      url: `/clients/file_templates`,
      method: 'get',
      params,
    })
  },

  // download
  async downloadTask(params) {
    return AppHttpService({
      url: `/tasks/excel`,
      method: 'get',
      responseType: 'blob',
      params,
      timeout: 90000,
    })
      .then(response => {
        this.content = response.data
        this.filename = this.getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  // angle
  async getAngleList(params) {
    return AppHttpService({
      url: '/task_angles',
      method: 'get',
      params,
    })
  },

  async saveAngle(data) {
    return AppHttpService({
      url: '/task_angles?extra=members,project.call_opportunity_sum_jit',
      method: 'post',
      data,
    })
  },

  async updateAngle(angle_id, data) {
    return AppHttpService({
      url: `/task_angles/${angle_id}?extra=members,project.call_opportunity_sum_jit`,
      method: 'put',
      data,
    })
  },

  async deleteAngle(angle_id) {
    return AppHttpService({
      url: `/task_angles/${angle_id}`,
      method: 'delete',
    })
  },

  async updateAngleTasks(project_id, data, params) {
    return AppHttpService({
      url: `/projects/${project_id}/tasks`,
      method: 'put',
      params,
      data,
    })
  },

  async updateTaskRank(project_id, data, params) {
    return AppHttpService({
      url: `/projects/${project_id}/tasks/sort`,
      method: 'patch',
      params,
      data,
    })
  },

  async updateTaskAdvisorSchedules(task_id, data) {
    return AppHttpService({
      url: `/tasks/${task_id}/schedule/advisor?extra=advisor_schedules`,
      method: 'patch',
      data,
    })
  },

  // inquiry 问卷
  async saveProjectInquiry(project_id, inquiry_id) {
    return AppHttpService({
      url: `projects/${project_id}/set_sq/${inquiry_id}`,
      method: 'patch',
    })
  },

  async getProjectInquiry(id) {
    return AppHttpService({
      url: `/projects/${id}`,
      method: 'get',
      params: {
        extra: 'sq',
      },
    })
  },

  async getProjectInquiryBranches(id) {
    return AppHttpService({
      url: `/projects/${id}`,
      method: 'get',
      params: {
        extra: 'sq,sq.branches',
      },
    })
  },

  // 发送邮件流程
  async mailOutreach(data) {
    return AppHttpService({
      url: `tasks/outreach`,
      method: 'patch',
      data,
    })
  },

  async mailOutreachAsync(data) {
    return AppHttpService({
      url: `tasks/outreach/async`,
      method: 'patch',
      data,
    })
  },

  async mailOutreachBulkAsync(data) {
    return AppHttpService({
      url: `tasks/outreach/bulk/async`,
      method: 'patch',
      data,
    })
  },

  async outreachAsyncProcess(id) {
    return AppHttpService({
      url: `tasks/outreach/async/process/${id}`,
      method: 'get',
    })
  },

  async mailToSingleAdvisor(data) {
    return AppHttpService({
      url: `tasks/email_to_advisor`,
      method: 'patch',
      data,
    })
  },

  async mailToAdvisor(data) {
    return AppHttpService({
      url: `tasks/email_to_advisor/batch`,
      method: 'patch',
      data,
    })
  },

  async mailToClient(data) {
    return AppHttpService({
      url: `tasks/email_to_contact`,
      method: 'patch',
      data,
    })
  },

  async copyMailToClient(data) {
    return AppHttpService({
      url: `/tasks/recommend/copy`,
      method: 'patch',
      data,
    })
  },

  async portalToAdvisor(data) {
    return AppHttpService({
      url: `portal/portal_advisor/batch`,
      method: 'post',
      data,
    })
  },

  async portalToClient(data) {
    return AppHttpService({
      url: `portal/portal_contact`,
      method: 'post',
      data,
    })
  },

  async getArrangeSchedule(params) {
    return AppHttpService({
      url: `schedule`,
      method: 'get',
      params,
    })
  },

  async cancelArrangedScheduleByTask(task_id) {
    return AppHttpService({
      url: `schedule/cancel`,
      method: 'patch',
      params: { task_id },
    })
  },

  async cancelArrangedScheduleById(id) {
    return AppHttpService({
      url: `schedule/${id}/cancel`,
      method: 'patch',
    })
  },

  async updateAngleTaskSchedule(id, data) {
    return AppHttpService({
      url: `schedule/${id}/contact_schedules/user_edit?extra=entity_update_logs`,
      method: 'patch',
      data,
    })
  },

  async cancelVettingCall(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/vetting_call/cancel`,
      method: 'patch',
    })
  },

  async vettingCallTask(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/arrange_vetting_call`,
      method: 'patch',
      data: data.data,
    })
  },

  async scheduleTask(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/schedule`,
      method: 'patch',
      data: data.data,
    })
  },

  async arrangeTask(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/arrange`,
      method: 'patch',
      data: data.data,
    })
  },

  getAlternativeHostsEmailList() {
    return AppHttpService({
      url: `tasks/zoom/alternative_hosts`,
      method: 'get',
    })
  },

  async completeTask(id, data) {
    return AppHttpService({
      url: `/tasks/${id}/complete`,
      method: 'patch',
      data,
    })
  },

  async getTaskBoundContract(id, params) {
    return AppHttpService({
      url: `/contract/peek_task_bound/${id}`,
      method: 'get',
      params,
    })
  },

  // task detail get pre ca
  async getTaskDetailCa(task_id, ca_id) {
    return AppHttpService({
      url: `/tasks/${task_id}/ca/${ca_id}`,
      method: 'get',
      params: { extra: 'inquiry_instance,inquiry_instance.questions,inquiry_instance.answers,inquiry_instance.branch,files' },
    })
  },

  async batchUpdateTaskStatusNeedApprove(data) {
    return AppHttpService({
      url: `/tasks/need_approve/batch`,
      method: 'patch',
      data,
    })
  },

  async getLoopUpRoomsList() {
    return AppHttpService({
      url: `/common/loopup/rooms`,
      method: 'get',
      params: {
        is_available: true,
      },
    })
  },

  getTaskPaymentDetail(data) {
    return AppHttpService({
      url: `/tasks/${data.task_id}/payments/${data.id}`,
      method: 'get',
    })
  },
  getTaskContactFeedbackDetail(data) {
    return AppHttpService({
      url: `/tasks/${data.task_id}/contact_feedbacks/${data.id}`,
      method: 'get',
    })
  },

  // task angle

  getProjectTaskAngle(data) {
    return AppHttpService({
      url: `/task_angles`,
      method: 'get',
      params: data,
    })
  },

  questionnaireBindingAngle(task_angle_id, data) {
    return AppHttpService({
      url: `/task_angles/${task_angle_id}`,
      method: 'put',
      data,
    })
  },

  customizeSortTaskAngle(data) {
    return AppHttpService({
      url: `/task_angles/customize_sort`,
      method: 'patch',
      data,
    })
  },

  updateAngleMedianRate(id) {
    return AppHttpService({
      url: `/task_angles/${id}/update_median_rates`,
      method: 'get',
    })
  },

  async getConsultationList(data) {
    return AppHttpService({
      url: `/tasks`,
      method: 'get',
      params: data,
    })
  },

  // client portal task publish
  async updateTaskClientPublish(data) {
    return AppHttpService({
      url: `/tasks/${data.id}/client_publish_status?extra=client_publish_log.create_by`,
      method: 'patch',
      data,
    })
  },

  // client portal task status
  async updateClientPortalStatus(data) {
    return AppHttpService({
      url: `/tasks/${data.id}/update_client_portal_status`,
      method: 'patch',
      data,
    })
  },

  // compliance rule 查看
  async readProjectComplianceRules(project_id) {
    return AppHttpService({
      url: `/projects/${project_id}/read_client_legal_rules`,
      method: 'patch',
    })
  },

  // survey compliance  approval

  async updateSurveyApproval(data) {
    return AppHttpService({
      url: `/projects/${data.project_id}/legal_rules`,
      method: 'put',
      data: {
        'need_compliance_approval': data.need_compliance_approval,
      },
    })
  },

  // 生成 survey link
  async createSurveyLink(data) {
    return AppHttpService({
      url: `/portal/advisor_3rd_party_survey`,
      method: 'post',
      data,
    })
  },

  // survey project revenue
  async updateSurveyRevenue(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/update_survey_project_revenue`,
      method: 'patch',
      data,
    })
  },

  // patients charge
  async updatePatientsCharge(project_id, data) {
    return AppHttpService({
      url: `/projects/${project_id}/update_project_charge`,
      method: 'patch',
      data,
    })
  },

  async copyTasksToProject(data) {
    return AppHttpService({
      url: `/projects/tasks/clone`,
      method: 'patch',
      data,
    })
  },

  // clone project
  cloneProject(id, data) {
    return AppHttpService({
      url: `/projects/${id}/clone`,
      method: 'patch',
      data,
    })
  },

  getClientPortalLink(data) {
    return AppHttpService({
      url: `/tasks/recommend/portal`,
      method: 'post',
      data,
    })
  },

  getCompliancePortalLink(params) {
    return AppHttpService({
      url: `/compliance_portal/link`,
      method: 'get',
      params,
    })
  },

  // create project google sheet
  createGoogleSheet(project_id) {
    return AppHttpService({
      url: `projects/${project_id}/google_sheet`,
      method: 'patch',
    })
  },

  clearChasingInGoogleSheet(project_id) {
    return AppHttpService({
      url: `projects/${project_id}/google_sheet/clear_chasing`,
      method: 'patch',
    })
  },

  createClientGoogleSheet(project_id) {
    return AppHttpService({
      url: `projects/${project_id}/client_google_sheet`,
      method: 'patch',
    })
  },

  updateClientGoogleSheetData(project_id) {
    return AppHttpService({
      url: `projects/${project_id}/client_google_sheet/update_data`,
      method: 'patch',
    })
  },

  getProjectAvailableTags(params) {
    return AppHttpService({
      url: `client_selection_tag/project_available`,
      method: 'get',
      params,
    })
  },

  createClientSelectionTag(data) {
    return AppHttpService({
      url: `client_selection_tag`,
      method: 'post',
      data,
    })
  },

  deleteClientSelectionTag(id) {
    return AppHttpService({
      url: `client_selection_tag/refs/${id}`,
      method: 'delete',
    })
  },

  selectTagForTask(id, data) {
    return AppHttpService({
      url: `client_selection_tag/${id}/refs/update_batch?extra=client_selection_tag_refs.tag`,
      method: 'patch',
      data,
    })
  },

  saveUpdateLog(data) {
    return AppHttpService({
      url: `entity_update_log`,
      method: 'post',
      data,
    })
  },

  // excel 导入专家到项目
  readAdvisorXlsData(params) {
    return AppHttpService({
      url: 'advisor_xls_import/import/read_data',
      method: 'get',
      params,
    })
  },

  saveAdvisorXlsData(data) {
    return AppHttpService({
      url: 'advisor_xls_import/import/batch',
      method: 'post',
      data,
      timeout: 60000,
    })
  },

  importAdvisorXlsToProject(data) {
    return AppHttpService({
      url: 'advisor_xls_import/import/execute_steps',
      method: 'patch',
      data,
    })
  },

  importAdvisorXlsDownload(params) {
    return AppHttpService({
      url: 'advisor_xls_import/import/download_result',
      method: 'get',
      params,
      responseType: 'blob',
    }).then(response => {
      this.content = response.data
      this.filename = this.getFileName(response)
      const blob = new Blob([this.content], { type: 'application/excel' })
      if (window.navigator.msSaveOrOpenBlob) {
        // 兼容IE10
        navigator.msSaveBlob(blob, this.filename)
      } else {
        //  chrome/firefox
        const aTag = document.createElement('a')
        aTag.download = this.filename
        aTag.href = URL.createObjectURL(blob)
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    })
  },

  CNAdvisorTaskCAOverride(id) {
    return AppHttpService({
      url: `tasks/${id}/ca/override?extra=create_by`,
      method: 'patch',
    })
  },

  CNAdvisorTaskCARevoke(id) {
    return AppHttpService({
      url: `tasks/${id}/ca/revoke`,
      method: 'patch',
    })
  },

  taskAdvisorDeclined(data) {
    return AppHttpService({
      url: `tasks/advisor_decline`,
      method: 'patch',
      data,
    })
  },

  taskAdvisorDeclinedInvalidate(id) {
    return AppHttpService({
      url: `tasks/advisor_decline/invalidate/${id}`,
      method: 'patch',
    })
  },

  changeClientVerity(id, data) {
    return AppHttpService({
      url: `projects/${id}/change_client/verify`,
      method: 'patch',
      data,
    })
  },

  changeProjectClient(id, data) {
    return AppHttpService({
      url: `projects/${id}/change_client`,
      method: 'patch',
      data,
    })
  },

  changeTaskLegalRequest(id, data) {
    return AppHttpService({
      url: `tasks/legal_requests/${id}`,
      method: 'put',
      data,
    })
  },

  getTaskLegalRequests(query, data) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: `tasks/legal_requests/search?` + url_query,
      method: 'post',
      data,
    })
  },

  updateTaskArrangement(task_id, data) {
    return AppHttpService({
      url: `tasks/${task_id}/arrangement`,
      method: 'patch',
      data,
    })
  },

  callExpertByTwilio(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/arrangement/twilio/outbound_call/expert`,
      method: 'patch',
      data,
    })
  },

  callExpertByZoom(data) {
    return AppHttpService({
      url: `tasks/${data.task_id}/arrangement/zoom/outbound_call/expert`,
      method: 'patch',
      data,
    })
  },

  getTwilioAssetLink(task_id, data) {
    return AppHttpService({
      url: `tasks/${task_id}/twilio/asset/portal?require_compliance_review=${data.require_compliance_review}&user_id=${data.user_id}`,
      method: 'post',
      data,
    })
  },

  // advisor consent to recoding
  getAdvisorRecordingEmail(task_id) {
    return AppHttpService({
      url: `tasks/${task_id}/advisor_record_consent/email`,
      method: 'get',
    })
  },
  sendAdvisorRecordingEmail(task_id, data) {
    return AppHttpService({
      url: `tasks/${task_id}/advisor_record_consent/send_email`,
      method: 'patch',
      data,
    })
  },

  getAdvisorRecordingPortalLink(task_id) {
    return AppHttpService({
      url: `tasks/${task_id}/advisor_record_consent/portal`,
      method: 'get',
    })
  },

  updateAdvisorRecordingStatus(task_id, data) {
    return AppHttpService({
      url: `tasks/${task_id}/advisor_record_consent/update`,
      method: 'put',
      data,
    })
  },

  twilioKickOutParticipant(task_id, participant_id) {
    return AppHttpService({
      url: `tasks/${task_id}/arrangement/twilio/kick_out/${participant_id}`,
      method: 'patch',
    })
  },

  // project search  将筛选后的专家导入到目标项目
  async attachExpertsToProject(project_id, data) {
    return AppHttpService({
      url: `projects/${project_id}/attach_copied_experts`,
      method: 'post',
      data,
    })
  },

  // receivable/payable transactions ---------------------------------------------
  async getReceivableTransactions(data) {
    return AppHttpService({
      url: `receivable_transaction`,
      method: 'get',
      params: data,
    })
  },

  async saveReceivableTransaction(data) {
    return AppHttpService({
      url: `receivable_transaction`,
      method: 'post',
      data,
    })
  },

  async updateReceivableTransaction(id, data) {
    return AppHttpService({
      url: `receivable_transaction/${id}`,
      method: 'put',
      data,
    })
  },

  async deleteReceivableTransaction(id) {
    return AppHttpService({
      url: `receivable_transaction/${id}`,
      method: 'delete',
    })
  },

  async getPayableTransactions(data) {
    return AppHttpService({
      url: `payable_transaction`,
      method: 'get',
      params: data,
    })
  },

  async savePayableTransaction(data) {
    return AppHttpService({
      url: `payable_transaction`,
      method: 'post',
      data,
    })
  },

  async updatePayableTransaction(id, data) {
    return AppHttpService({
      url: `payable_transaction/${id}`,
      method: 'put',
      data,
    })
  },

  async deletePayableTransaction(id) {
    return AppHttpService({
      url: `payable_transaction/${id}`,
      method: 'delete',
    })
  },

  async getAvailableFields() {
    return AppHttpService({
      url: `projects/pl_excel_template/available_fields`,
      method: 'get',
    })
  },

  async downloadRevenue(data) {
    return AppHttpService({
      url: `projects/pl/revenue/report`,
      method: 'get',
      params: data,
      responseType: 'blob',
    })
      .then(response => {
        this.content = response.data
        this.filename = this.getFileName(response)
        const blob = new Blob([this.content], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  async getProjectWebSocketHistory(data) {
    return AppHttpService({
      url: 'project_real_time',
      method: 'get',
      params: data,
    })
  },

  async getProjectWebSocketUnread(data) {
    return AppHttpService({
      url: 'project_real_time/list_with_task_info',
      method: 'get',
      params: data,
    })
  },

  async triggerProjectWebSocketEvent(data) {
    return AppHttpService({
      url: 'project_real_time/trigger_event',
      method: 'get',
      params: data,
    })
  },

  async getProjectAngleAlias() {
    return AppHttpService({
      url: 'project_search_angle_alias',
      method: 'get',
    })
  },

}

