import { AppHttpService } from '@/utils/request'

export default {
  async getClientList(data) {
    return AppHttpService({
      url: '/clients',
      method: 'get',
      params: data,
    })
  },
  async searchClient(data) {
    return AppHttpService({
      url: '/clients/client_contacts',
      method: 'get',
      params: data,
    })
  },
  async getClient(
    id,
    extra = 'account_managers.user,location,offices') {
    return AppHttpService({
      url: '/clients/' + id,
      method: 'get',
      params: {
        extra: extra,
      },
    })
  },
  async createClient(data) {
    return AppHttpService({
      url: '/clients',
      method: 'post',
      data,
    })
  },
  async updateClient(data) {
    return AppHttpService({
      url: `/clients/${data.id}`,
      method: 'put',
      data,
    })
  },

  async getClientOffices(id, params) {
    return AppHttpService({
      url: `/clients/${id}/offices`,
      method: 'get',
      params,
    })
  },

  async getContactList(id, data) {
    return AppHttpService({
      url: '/clients/' + id + '/client_contacts',
      method: 'get',
      params: data,
    })
  },

  async getContactOne(id, data) {
    return AppHttpService({
      url: `/clients/client_contacts/${id}`,
      method: 'get',
      params: data,
    })
  },

  async getFullContactList(data) {
    return AppHttpService({
      url: '/clients/client_contacts',
      method: 'get',
      params: data,
    })
  },

  async addClientContact(id, data) {
    return AppHttpService({
      url: '/clients/' + id + '/client_contacts',
      method: 'patch',
      data: data,
    })
  },

  async updateClientContact(client_id, contact_id, data) {
    return AppHttpService({
      url: '/clients/' + client_id + '/client_contacts/' + contact_id,
      method: 'put',
      data: data,
    })
  },

  async deleteClientContact(client_id, contact_id) {
    return AppHttpService({
      url: '/clients/' + client_id + '/client_contacts/' + contact_id,
      method: 'delete',
    })
  },

  async getAmList() {
    return AppHttpService({
      url: '/clients/account_managers',
      method: 'get',
    })
  },

  async getAmTeamList() {
    return AppHttpService({
      url: '/teams',
      method: 'get',
      params: { extra: 'team_members', type: 'AM' },
    })
  },

  /**
   * 获取法务规则偏好
   * @param client_id
   * @param data
   */
  async getCompliancePreference(client_id, data) {
    return AppHttpService({
      url: `clients/${client_id}`,
      method: 'get',
      params: {
        extra: 'account_managers,compliance_preference,account_managers.user',
      },
    })
  },

  /**
   * 修改法务规则偏好
   * @param client_id
   * @param data
   */
  async updateComplianceRules(client_id, data) {
    const request_data = {
      approve_method: data.approve_method,
      approve_policy: data.approve_policy,
      require_chaperone: data.require_chaperone,
      chaperones: data.chaperones,
      rule: { compliance_rules: data.compliance_rules },
      rule_version: '1.0.0',
    }
    return AppHttpService({
      url: `clients/${client_id}/compliance_preference`,
      method: 'put',
      data: request_data,
    })
  },

  async getComplianceChangeLog(params) {
    return AppHttpService({
      url: `clients/compliance_change_log`,
      method: 'get',
      params,
    })
  },

  async spendEmailToContactAssociatedPerson(client_id, data) {
    return AppHttpService({
      url: `clients/${client_id}/to_am_compliance`,
      method: 'patch',
      data,
    })
  },

  // task dashboard

  async getTaskDashboard(data) {
    return AppHttpService({
      url: `client/notes`,
      method: 'get',
      params: data,
    })
  },

  async addTaskDashboard(data) {
    return AppHttpService({
      url: `client/notes`,
      method: 'post',
      data,
    })
  },

  async updateTaskDashboard(data) {
    return AppHttpService({
      url: `client/notes/${data.id}`,
      method: 'put',
      data,
    })
  },

  async deleteTaskDashboard(id) {
    return AppHttpService({
      url: `client/notes/${id}`,
      method: 'delete',
    })
  },

  // document
  async addDocument(client_id, data) {
    return AppHttpService({
      url: `clients/${client_id}/documents`,
      method: 'post',
      data,
    })
  },

  async getDocument(client_id) {
    return AppHttpService({
      url: `clients/${client_id}/documents`,
      method: 'get',
      params: { extra: 'create_by' },
    })
  },

  async deleteDocument(client_id, document_id) {
    return AppHttpService({
      url: `clients/${client_id}/documents/${document_id}`,
      method: 'delete',
    })
  },

  async getCustomFields(params) {
    return AppHttpService({
      url: `clients/custom_fields`,
      method: 'get',
      params: params,
    })
  },

  async addCustomFields(data) {
    return AppHttpService({
      url: `clients/custom_fields`,
      method: 'post',
      data,
    })
  },

  async updateCustomFields(data) {
    return AppHttpService({
      url: `clients/custom_fields/${data.id}`,
      method: 'put',
      data,
    })
  },

  async deleteCustomFields(id) {
    return AppHttpService({
      url: `clients/custom_fields/${id}`,
      method: 'delete',
    })
  },

  async mailOutreach(data) {
    return AppHttpService({
      url: `clients/client_contacts/outreach/batch`,
      method: 'patch',
      data,
    })
  },

  async ContactCommunications(data) {
    return AppHttpService({
      url: `communication_records`,
      method: 'get',
      params: data,
    })
  },

  async updateClientContactBatch(data) {
    return AppHttpService({
      url: `clients/client_contacts/batch`,
      method: 'put',
      data,
    })
  },

  async updateClientContactSetBlockBatch(params) {
    return AppHttpService({
      url: `clients/client_contacts/set_block_batch`,
      method: 'patch',
      params,
    })
  },

  async getClientProjectList(data) {
    return AppHttpService({
      url: `projects/list/projects_tab`,
      method: 'get',
      params: data,
      timeout: 90000,
    })
  },

  /* client note*/

  async getClientNoteList(data) {
    return AppHttpService({
      url: `client/account_notes`,
      method: 'get',
      params: data,
    })
  },

  async addClientNote(data) {
    return AppHttpService({
      url: `client/account_notes`,
      method: 'post',
      data,
    })
  },

  async updateClientNote(data) {
    return AppHttpService({
      url: `client/account_notes/${data.id}`,
      method: 'put',
      data,
    })
  },

  async deleteClientNote(id) {
    return AppHttpService({
      url: `client/account_notes/${id}`,
      method: 'delete',
    })
  },

  /* client note tag*/

  async getClientNoteTags(data) {
    return AppHttpService({
      url: `client/account_notes/tag`,
      method: 'get',
      params: data,
    })
  },

  async addClientNoteTags(data) {
    return AppHttpService({
      url: `client/account_notes/tag`,
      method: 'post',
      data,
    })
  },

  // client tab->projects download
  async clientProjectsDownload(data) {
    function getFileName(response) {
      const cd = response.headers['content-disposition']
      const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const match = regex.exec(cd)
      const file_name = match[1].replace(/"/g, '')
      try {
        return decodeURIComponent(file_name)
      } catch (e) {
        return file_name
      }
    }

    return AppHttpService({
      url: `projects/list/projects_tab/excel`,
      method: 'get',
      params: data,
      responseType: 'blob',
      timeout: 90000,
    }).then(response => {
      this.content = response.data
      this.filename = getFileName(response)
      const blob = new Blob([this.content], { type: 'application/excel' })
      if (window.navigator.msSaveOrOpenBlob) {
        // 兼容IE10
        navigator.msSaveBlob(blob, this.filename)
      } else {
        //  chrome/firefox
        const aTag = document.createElement('a')
        aTag.download = this.filename
        aTag.href = URL.createObjectURL(blob)
        aTag.click()
        URL.revokeObjectURL(aTag.href)
      }
    })
  },

  async getClientBlackList(data) {
    return AppHttpService({
      url: `client_blacklist`,
      method: 'get',
      params: data,
    })
  },

  async addToClientBlackList(id, type, data) {
    return AppHttpService({
      url: `client_blacklist/client/${id}?type=${type}`,
      method: 'put',
      data,
    })
  },

  async removeFromClientBlackList(id, type, data) {
    return AppHttpService({
      url: `client_blacklist/client/${id}?type=${type}`,
      method: 'delete',
      data,
    })
  },

  async checkClientBlockList(type, data) {
    return AppHttpService({
      url: `client_blacklist/blocked_info/advisor?type=${type}`,
      method: 'post',
      data,
      timeout: 30000,
    })
  },
}

