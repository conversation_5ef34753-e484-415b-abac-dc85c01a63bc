import { AppHttpService } from '@/utils/request'

export default {
  async getInquiryList(data) {
    return AppHttpService({
      url: '/inquiry',
      method: 'get',
      params: data,
    })
  },
  async getInquiryOne(id) {
    return AppHttpService({
      url: '/inquiry/' + id,
      method: 'get',
      params: { extra: 'branches,branches.questions,client' },
    })
  },

  async getBranchOne(inquiry_id, branch_id) {
    return AppHttpService({
      url: `inquiry/${inquiry_id}/branch/${branch_id}`,
      method: 'get',
      params: { extra: 'questions' },
    })
  },

  async getInstanceOne(id) {
    return AppHttpService({
      url: '/inquiry/instances/' + id,
      method: 'get',
      params: {
        extra: 'questions,answers,branch,client_publish_log.create_by',
      },
    })
  },

  async addInquiry(data) {
    return AppHttpService({
      url: '/inquiry',
      method: 'post',
      data,
    })
  },

  async updateInquiry(id, data) {
    return AppHttpService({
      url: '/inquiry/' + id + '/branch',
      method: 'post',
      data: data,
    })
  },

  /* 参数 actions:
    默认不传：覆盖原来的所有
    UPDATE:只更新
    SAVE：新增及修改
    DELETE:删除 */
  async updateQuestion(inquiry_id, branch_id, data, actions) {
    return AppHttpService({
      url: `/inquiry/${inquiry_id}/branch/${branch_id}?actions=${actions}`,
      method: 'put',
      data: data,
    })
  },

  async setDefaultInquiry(inquiry_id, id) {
    return AppHttpService({
      url: `/inquiry/${inquiry_id}/branch/${id}/set_default_branch`,
      method: 'patch',
    })
  },

  async setDefaultCA(inquiry_id, branch_id) {
    return AppHttpService({
      url: `/inquiry/${inquiry_id}/branch/${branch_id}/set_default_branch`,
      method: 'patch',
    })
  },

  async deleteInquiry(inquiry_id, branch_id) {
    return AppHttpService({
      url: `/inquiry/${inquiry_id}/branch/${branch_id}`,
      method: 'delete',
    })
  },

  async clientInquiryList(params) {
    return AppHttpService({
      url: `/inquiry/view`,
      method: 'get',
      params,
    })
  },

  async getInstances(params) {
    return AppHttpService({
      url: `/inquiry/instances`,
      method: 'get',
      params,
    })
  },

  // 未曾发送SQ至专家 生成task对应的 instance id
  async createInstance(data) {
    return AppHttpService({
      url: `/inquiry/instances`,
      method: 'post',
      data: data,
    })
  },

  // 复用专家 之前回答过的SQ
  async copyInstance(data) {
    return AppHttpService({
      url: `/inquiry/instances/copy`,
      method: 'post',
      data: data,
    })
  },

  // 代替专家填写SQ
  // async answerQuestion(instance_id, data) {
  //   return AppHttpService({
  //     url: `/inquiry/instances/${instance_id}/answers`,
  //     method: 'patch',
  //     data: data,
  //   })
  // },

  async answerQuestion(task_id, data) {
    return AppHttpService({
      url: `/tasks/${task_id}/sq`,
      method: 'patch',
      data: data,
    })
  },

  // 为一份instance（一份问卷快照） 添加一次性问题
  async updateSQOneOff(data) {
    return AppHttpService({
      url: `/inquiry/instances/${data.id}/oneoff`,
      method: 'put',
      data: data,
    })
  },

  // 修正专家SQ答案
  async updateAnswer(data) {
    return AppHttpService({
      url: `/inquiry/instances/${data.id}`,
      method: 'put',
      data: data,
    })
  },

  // km comment
  async saveKmComments(instance_id, data) {
    return AppHttpService({
      url: `/inquiry/instances/${instance_id}/answers/km_comment`,
      method: 'patch',
      data: data,
    })
  },

  // client portal task sq publish
  async updateTaskSQClientPublish(data) {
    return AppHttpService({
      url: `/inquiry/instances/${data.id}/client_publish?extra=client_publish_log.create_by`,
      method: 'patch',
      data,
    })
  },

}
