import { AppHttpService } from '@/utils/request'

export default {
  async getTCList(params) {
    return AppHttpService({
      url: '/tc',
      method: 'get',
      params: Object.assign({ extra: 'templates', pagination: false }, params),
    })
  },
  async getTCDetail(id) {
    return AppHttpService({
      url: '/tc/' + id,
      method: 'get',
      params: { extra: 'templates' },
    })
  },
  async createTC(data) {
    return AppHttpService({
      url: 'tc',
      method: 'post',
      data,
    })
  },
  async updateTC(data) {
    return AppHttpService({
      url: '/tc/' + data.id,
      method: 'put',
      data,
    })
  },
  async updateTCCurrent(id) {
    return AppHttpService({
      url: '/tc/' + id + '/current',
      method: 'patch',
    })
  },
  async getTCListContent(data) {
    return AppHttpService({
      url: '/tc/templates',
      method: 'get',
      params: data,
    })
  },
  async updateTCContent(data) {
    return AppHttpService({
      url: '/tc/templates',
      method: 'post',
      data,
    })
  },
  async getTcIdInCNDb(id) {
    return AppHttpService({
      url: `/tc/cn_db_tc_map?us_tc_template_id=${id}`,
      method: 'get',
    })
  },

  async getTcIdInUSDb(id) {
    return AppHttpService({
      url: `/tc/cn_db_tc_map?cn_tc_id=${id}`,
      method: 'get',
    })
  },
}
