import { AppHttpService } from '@/utils/request'

export default {
  async getEmailList(params) {
    return AppHttpService({
      url: '/email_templates',
      method: 'get',
      params: Object.assign({ is_draft: false }, params),
    })
  },
  async getEmailDetail(id) {
    return AppHttpService({
      url: '/email_templates/' + id,
      method: 'get',
    })
  },
  async createEmail(data) {
    return AppHttpService({
      url: 'email_templates',
      method: 'post',
      data,
    })
  },
  async updateEmail(data) {
    return AppHttpService({
      url: 'email_templates/' + data.id,
      method: 'put',
      data,
    })
  },
  async deleteEmail(id) {
    return AppHttpService({
      url: 'email_templates/' + id,
      method: 'delete',
    })
  },
  async getEmailPlaceholders(is_show_all) {
    return AppHttpService({
      url: 'email_templates/placeholders',
      method: 'get',
      params: {
        show_all: is_show_all,
      },
    })
  },

  // 可以定制 template 参数，获得替换 占位符和link 的邮件内容
  // ids:{
  //    client_id: Int? = null,
  //    contact_id: Int? = null,
  //     user_id: Int? = null,
  //     project_id: Int? = null,
  //     task_id: Int? = null,
  //     advisor_id: Int? = null,
  //     contract_id: Int? = null,
  //     timezone: ZoneId? = null,},
  // template:{}
  async getEmailContentWithTemplateParams(data) {
    return AppHttpService({
      url: `email_templates/custom/process`,
      method: 'patch',
      data,
    })
  },

  async getEmailByTemplate(id, data) {
    return AppHttpService({
      url: `email_templates/${id}/process`,
      method: 'patch',
      data,
    })
  },

  // 带查询数据参数的接口，观察使用效果，后期可能逐步替代`email_templates/${id}/process`接口
  async getEmailTemplateWithQuery(id, data) {
    return AppHttpService({
      url: `email/templates/${id}/process_with_query`,
      method: 'patch',
      data,
    })
      .then(data => {
        data.to_list = data.to.map(item => item.email)
        data.to_addresses = data.to
        data.cc_list = data.cc.map(item => item.email)
        data.cc_addresses = data.cc
        data.from = data.from.email
        data.bcc_list = data.bcc.map(item => item.email)
        data.bcc_addresses = data.bcc
        return data
      })
  },
  async getEmailByTemplateForTaskArrange(id, data) {
    return AppHttpService({
      url: `tasks/arrange/email_templates/${id}/process`,
      method: 'patch',
      data,
    })
  },
  async getEmailsByTemplate(id, data) {
    return AppHttpService({
      url: `email_templates/${id}/batch_process`,
      method: 'patch',
      data,
    })
  },
  async getEmailsByTemplateData(data) {
    return AppHttpService({
      url: `email_templates/template/batch_process`,
      method: 'patch',
      data,
    })
  },
  getEmailTags(params) {
    return AppHttpService({
      url: `email_templates/built_in_tags`,
      method: 'get',
      params,
    })
  },
  getEmailContentTypes() {
    return AppHttpService({
      url: `email_templates/content_types`,
      method: 'get',
    })
  },

  // outreach all
  outreachAllCheck(data) {
    return AppHttpService({
      url: `tasks/outreach_all/check`,
      method: 'patch',
      data,
    })
  },
  outreachAllSend(data) {
    return AppHttpService({
      url: `tasks/outreach_all`,
      method: 'patch',
      data,
    })
  },
  // send test email
  sendEmail(data) {
    return AppHttpService({
      url: `email/send`,
      method: 'patch',
      data,
    })
  },

  // check email status
  // Check 1: Pass in an email address to see if it has been sent to from the US DB and return the status (bounced, rejected, delivered, etc.) at any time.  If Bounced or rejected, return “No”, if deliverable, return “Yes”.  Additionally, if “No, return a list of the alternate email addresses on file for that expert in the payload.
  // Check 2: Pass in an email address and a list of project IDs in the US DB, and if for any of those project IDs the email address is tied to an expert who is in Decline status, return “No”, if not in Decline status, return “Yes”.
  // Check 3: Pass in an email address and if it is tied to an expert who is unsubscribed or deleted from the DB, return “No”, and if not unsubscribed or deleted, return “Yes”
  EmailStatusValidation(data) {
    return AppHttpService({
      url: `email_validation/local/validate`,
      method: 'patch',
      data,
    })
  },

  getEmailContentPlaceholder(data) {
    return AppHttpService({
      url: `email_templates/process_placeholders`,
      method: 'patch',
      data,
    })
  },

}
