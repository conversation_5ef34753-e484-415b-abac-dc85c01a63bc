import { AppHttpService } from '@/utils/request'

export default {
  async login(data) {
    return AppHttpService({
      url: '/auth/login',
      method: 'post',
      data,
    })
  },
  async logout() {
    return AppHttpService({
      url: '/auth/logout',
      method: 'post',
    })
  },
  async loginBy2FA(data) {
    return AppHttpService({
      url: '/auth/login_v2',
      method: 'post',
      data,
    })
  },
  async getTwoFactorAuth(data) {
    return AppHttpService({
      url: '/auth/two_factor_auth/send',
      method: 'get',
      params: data,
    })
  },
  async verifyTwoFactorAuth(data) {
    return AppHttpService({
      url: '/auth/two_factor_auth/verify',
      method: 'get',
      params: data,
    })
  },
  async initPassword(data) {
    return AppHttpService({
      url: '/auth/initial_pwd',
      method: 'patch',
      data,
    })
  },
  async changePassword(data) {
    return AppHttpService({
      url: '/auth/reset_pwd',
      method: 'patch',
      data,
    })
  },
}
