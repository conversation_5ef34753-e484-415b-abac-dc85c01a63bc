import { AppHttpService } from '@/utils/request'

export default {
  async getPaymentList(params) {
    return AppHttpService({
      url: '/advisor_payments/local',
      method: 'get',
      params,
    })
  },
  async getPaymentForPL(params) {
    return AppHttpService({
      url: '/payments/pl_unified_payments',
      method: 'get',
      params,
    })
  },
  async getPaymentItemsList(data, query) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: '/payments/items/search?' + url_query,
      method: 'post',
      data,
    })
  },

  async getPaymentListV2(params) {
    return AppHttpService({
      url: '/payments',
      method: 'get',
      params,
    })
  },

  // 美国专家在CN DB产生的payment
  async getUsPaymentList(params) {
    return AppHttpService({
      url: '/advisor_payments/us',
      method: 'get',
      params,
    })
  },

  // 海外（美国/新加坡）专家在CN DB产生的payment 新版
  async getUsPaymentCnDBList(data, query) {
    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: '/bridge/payments/topics/search?' + url_query,
      method: 'post',
      data,
    })
  },

  async usPaymentApply(id, data) {
    return AppHttpService({
      url: `/advisor_payments/us/${id}/apply`,
      method: 'patch',
      data,
    })
  },

  async usPaymentFormApply(id, data) {
    return AppHttpService({
      url: `/bridge/payments/topics/${id}/payment_form`,
      method: 'post',
      data,
    })
  },

  async changeToPaying(data) {
    return AppHttpService({
      url: '/advisor_payments/local/manual/pay/batch',
      method: 'patch',
      data,
    })
  },

  async changeToPaid(data) {
    return AppHttpService({
      url: '/advisor_payments/local/manual/pay_confirmed/batch',
      method: 'patch',
      data,
    })
  },

  async changeToPayFailed(data) {
    return AppHttpService({
      url: '/advisor_payments/local/manual/pay_failed/batch',
      method: 'patch',
      data,
    })
  },

  async changePaymentEvent(data) {
    return AppHttpService({
      url: '/payments/items/manual/payment_event',
      method: 'patch',
      data,
    })
  },

  async uploadPayment(status, data) {
    return AppHttpService({
      url: `/advisor_payments/local/excel/update_status?target_status=${status}`,
      method: 'patch',
      data,
    })
  },

  async uploadPaymentExcel(status, data) {
    return AppHttpService({
      url: `/payments/items/excel/update_status?event_type=${status}`,
      method: 'patch',
      data,
    })
  },

  async downloadPayment(params) {
    function getFileName(response) {
      const cd = response.headers['content-disposition']
      const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const match = regex.exec(cd)
      const file_name = match[1].replace(/"/g, '')
      try {
        return decodeURIComponent(file_name)
      } catch (e) {
        return file_name
      }
    }

    return AppHttpService({
      url: `/advisor_payments/local/excel`,
      method: 'get',
      responseType: 'blob',
      params,
    })
      .then(response => {
        this.content = response.data
        this.filename = getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  async downloadPaymentExcel(data, query) {
    function getFileName(response) {
      const cd = response.headers['content-disposition']
      const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const match = regex.exec(cd)
      const file_name = match[1].replace(/"/g, '')
      try {
        return decodeURIComponent(file_name)
      } catch (e) {
        return file_name
      }
    }

    const url_query = Object.entries(query).map(item => item.join('=')).join('&')
    return AppHttpService({
      url: '/payments/items/excel?' + url_query,
      method: 'post',
      responseType: 'blob',
      data,
    })
      .then(response => {
        this.content = response.data
        this.filename = getFileName(response)
        const blob = new Blob([this.content], { type: 'application/excel' })
        if (window.navigator.msSaveOrOpenBlob) {
          // 兼容IE10
          navigator.msSaveBlob(blob, this.filename)
        } else {
          //  chrome/firefox
          const aTag = document.createElement('a')
          aTag.download = this.filename
          aTag.href = URL.createObjectURL(blob)
          aTag.click()
          URL.revokeObjectURL(aTag.href)
        }
      })
  },

  async getReferralPaymentList(params) {
    return AppHttpService({
      url: '/advisors/referrals',
      method: 'get',
      params,
    })
  },

  async getVirtualIncentivesApprovalPreview(data) {
    return AppHttpService({
      url: '/payments/items/virtualincentives/approval_preview',
      method: 'patch',
      data,
    })
  },

  async updateVirtualIncentivesApproval(data) {
    return AppHttpService({
      url: '/payments/items/virtualincentives/approval',
      method: 'patch',
      data,
    })
  },
}
