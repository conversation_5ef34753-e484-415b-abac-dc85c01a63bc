import Vue from 'vue'
import ElementUI from 'element-ui'
import locale from 'element-ui/lib/locale/lang/en'
import App from './App'
import store from './store'
import router from './router'

import 'normalize.css/normalize.css'
import '@/styles/element-variables.scss'
// import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/index.scss'
import '@/icons'
import '@/permission'
import 'virtual:svg-icons-register'

import '@fullcalendar/core/main.css'
import '@fullcalendar/timegrid/main.css'
import 'tippy.js/dist/tippy.css'

import filters from '@/utils/filters'

import DOMPurify from 'dompurify'

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (import.meta.env.MODE === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

Vue.prototype.$sanitizeHtml = function(html) {
  return DOMPurify.sanitize(html)
}

ElementUI.Dialog.props.closeOnClickModal.default = false

Vue.use(ElementUI, { locale, size: 'mini' })

Vue.config.productionTip = false

Vue.mixin({
  filters: filters,
})

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App),
})
