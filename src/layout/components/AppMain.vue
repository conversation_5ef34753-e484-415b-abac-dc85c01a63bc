<template>
  <section class="app-main">
    <keep-alive :include="keepAliveComponents">
      <router-view :key="key" />
    </keep-alive>
  </section>
</template>

<script>
import { RouteTools } from '@/utils/tool'
import { mapState } from 'vuex'

export default {
  name: 'AppMain',
  computed: {
    ...mapState({
      keepAliveComponents: state => state.app.keepAliveComponents,
    }),
    key() {
      if (this.$route.meta.tab) {
        return RouteTools.getRouteBaseTabPath(this.$route)
      }

      return this.$route.path
    },
    isKeepAlive() {
      return this.$route.meta.keepAlive ? this.$route.name : void 0
    },
  },
}
</script>

<style lang="scss" scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 66 = navbar + tags-view = 36 + 30 */
    min-height: calc(100vh - 66px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
