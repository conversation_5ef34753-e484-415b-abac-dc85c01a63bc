<template>
  <div class="navbar">

    <div class="left-menu">
      <!-- 左侧菜单栏折叠控制 -->
      <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

      <!-- 路由导航 -->
      <breadcrumb class="breadcrumb-container" />
    </div>

    <div class="right-menu">
      <!-- 购物车 -->
      <shopping-cart />

      <!-- 时区选择 -->
      <time-zone-select class="time-zone-container" />

      <!-- 用户头像、菜单 -->
      <el-dropdown class="avatar-container" size="medium" trigger="click">
        <div class="avatar-wrapper">
          <img v-if="avatar"
               class="user-avatar"
               :src="avatar"
               alt="Avatar">
          <div class="user-name">
            <div style="zoom: 0.9;">{{ name }}</div>
          </div>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item @click.native="goToProfile">
            <span style="display:block;"><el-icon name="document" /> Profile</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout">
            <span style="display:block;"><el-icon name="switch-button" /> Log Out</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import TimeZoneSelect from '@/components/TimeZoneSelect'
import ShoppingCart from '@/components/ShoppingCart'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    TimeZoneSelect,
    ShoppingCart,
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'name',
    ]),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      await this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    goToProfile() {
      this.$router.push({ name: 'Profile' })
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
  display: flex;

  .hamburger-container {
    height: 100%;
  }

  .breadcrumb-container {
  }

  .time-zone-container {
    margin-right: 4px;
  }

  .left-menu {
    flex-grow: 1;
    display: flex;
    align-items: center;
  }

  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      border-left: 1px solid #eeeff0;
      cursor: pointer;

      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        font-size: 12px;

        .user-avatar {
          display: block;
          width: 28px;
          height: 28px;
          margin: 4px 0 4px 4px;
          border-radius: 5px;
        }

        .user-name {
          margin: 0 4px;
          min-height: 28px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
