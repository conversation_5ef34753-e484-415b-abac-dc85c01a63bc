import RouteTools from './tool-router'
import moment from 'moment-timezone'
import store from '@/store'

let timer = null
const MONTH = {
  '1': 31,
  '2': new Date().getFullYear() % 4 ? 28 : 29,
  '3': 31,
  '4': 30,
  '5': 31,
  '6': 30,
  '7': 31,
  '8': 31,
  '9': 30,
  '10': 31,
  '11': 30,
  '12': 31,
}

const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

export function debounce(fn, wait) {
  if (timer !== null) clearTimeout(timer)
  timer = setTimeout(fn, wait)
}

export function isEmpty(val) {
  if (val === '') return true
  if (val === 'null') return true
  if (val === 'undefined') return true
  if (!val && val !== 0 && val !== '') return true
  // eslint-disable-next-line no-prototype-builtins
  if (Array.prototype.isPrototypeOf(val) && val.length === 0) return true
  // eslint-disable-next-line no-prototype-builtins
  if (Object.prototype.isPrototypeOf(val) && Object.keys(val).length ===
    0) return true
  return false
}

export function countWorkDay() {
  const result = {
    total: 0,
    complete: 0,
  }

  const now_date = new Date()
  const now_month = now_date.getMonth() + 1
  const now_day = now_date.getDate()
  const start_week_day = new Date(now_date.setDate(1)).getDay()

  for (let day = 1, week_day = start_week_day; day <= MONTH[now_month]; day++) {
    if (week_day !== 0 && week_day !== 6) {
      result.total++
      if (now_day > day) {
        result.complete++
      }
    }
    week_day === 6 ? week_day = 0 : week_day++
  }
  return result
}

export function downloadDocument(response) {
  const blob = new Blob([response.data],
    { type: response.headers['content-type'] })

  const file_container = document.createElement('a')
  const url = window.URL.createObjectURL(blob)

  file_container.style.display = 'none'
  file_container.setAttribute('href', url)
  file_container.setAttribute('download', getFileName(response))
  document.body.appendChild(file_container)
  file_container.click()
  window.URL.revokeObjectURL(url)
  document.body.removeChild(file_container)

  return response

  function getFileName(response) {
    const cd = response.headers['content-disposition']
    const regex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const match = regex.exec(cd)
    const file_name = match[1].replace(/"/g, '')
    try {
      return decodeURIComponent(file_name)
    } catch (e) {
      return file_name
    }
  }
}

export function fileUrlDownload(url, fileName) {
  const x = new XMLHttpRequest()
  x.open('GET', url, true)
  x.responseType = 'blob'
  x.onload = function(e) {
    const url = window.URL.createObjectURL(x.response)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.click()
    window.URL.revokeObjectURL(url)
  }
  x.send()
}

export function contractMomentFormat(val, type) {
  let time
  switch (type) {
    case 'start':
      time = '00:00:00'
      break
    case 'end':
      time = '23:59:59'
      break
  }
  return moment.tz(val + ' ' + time, store.state.app.timeZone).toISOString()
}

/**
 * 复制到剪贴板
 * @param val 带复制内容
 * @param keepStyle 是否保留 HTML 结构
 * @returns {Promise<unknown>}
 */
export function copyUtil(val, keepStyle) {
  return new Promise((resolve, reject) => {
    /* 带格式复制 */
    if (keepStyle) {
      /* 创建待复制框体 */
      /* 置入内容 */
      /* 执行复制 */
      const container = document.createElement('div')
      container.innerHTML = val
      document.body.appendChild(container)
      const range = document.createRange()
      range.selectNode(container)

      /* 需清空 range. 浏览器不支持多段选中, 导致 add 无效 */
      window.getSelection().removeAllRanges()
      window.getSelection().addRange(range)

      try {
        const successful = document.execCommand('copy')
        successful ? resolve(val) : reject()
      } catch (err) {
        reject()
      }
      window.getSelection().removeAllRanges()
      document.body.removeChild(container)
    } else {
      /* 无格式复制（纯文本模式） */
      const textarea = document.createElement('textarea')
      textarea.value = val
      document.body.appendChild(textarea)
      textarea.select()

      try {
        const successful = document.execCommand('copy')
        successful ? resolve(val) : reject()
      } catch (err) {
        reject()
      }
      document.body.removeChild(textarea)
    }
  })
}

export function removeNode(node) {
  if (node.parentElement !== null) {
    node.parentElement.removeChild(node)
  }
}

export function insertNodeAt(fatherNode, node, position) {
  const refNode =
    position === 0
      ? fatherNode.children[0]
      : fatherNode.children[position - 1].nextSibling
  fatherNode.insertBefore(node, refNode)
}

export function getLocationById(id_or_list) {
  return store.dispatch('options/getAllCountryList')
    .then(location_list => {
      if (Array.isArray(id_or_list)) {
        return location_list.filter(item => id_or_list.includes(item.id))
      } else {
        return location_list.find(item => item.id === id_or_list)
      }
    })
}

export function highlightHtml(value, targets, flag = 'gi') {
  if (!value || !targets.length) {
    return value
  } else {
    const keywords = targets.sort((a, b) => b.length - a.length)
    return value.replace(new RegExp(`(${keywords.join('|')})`, flag), `<span style="background-color: yellow">$1</span>`)
  }
}

/**
 * es6中，给一个关键词，匹配替换其中与其匹配的单词，
 * 要求不能是某个词contains这个关键词，比如我给的value text是’it easier hit it-industry',
 * 给的keyword 是 ‘IT’， 则其中的  it  , it-industry中的IT会被替换， 而hit中的it不应该被替换。
 */
export function highlightHtmlV2(value, targets, flag = 'gi') {
  if (!value || !targets.length) {
    return value
  } else {
    // 将关键词按长度排序，长的关键词优先匹配
    const keywords = targets.sort((a, b) => b.length - a.length)
    // 使用正则表达式匹配单词边界，确保只替换独立的单词
    const regex = new RegExp(`\\b(${keywords.join('|')})\\b`, flag)
    return value.replace(regex,
      `<span style="background-color: yellow">$1</span>`)
  }
}

export function disableAddToProjectPromptFormat(validated_types, disable_data) {
  const data_format = [
    {
      key: 'duplicate_in_project',
      prompt: 'These experts will be filtered,because there are uncompleted tasks in the target perspective,or because experts already exist in the target project:',
      list: [],
    }, {
      key: 'duplicate_in_angle',
      prompt: 'These experts will be filtered,because there are uncompleted tasks in the target perspective,or because experts already exist in the target list:',
      list: [],
    }, {
      key: 'empty_employment_history_advisor',
      prompt: 'These experts will be filtered,because these experts don\'t have any work experience:',
      list: [],
    }, {
      key: 'employees_conflict_investment_target',
      prompt: 'These experts will be filtered,because the current employers of these experts conflict with the investment target of the project:',
      list: [],
    }, {
      key: 'with_not_current_region_advisor',
      prompt: 'These experts will be filtered,because experts belong to outsource db:',
      list: [],
    }, {
      key: 'outsource_limit_advisor',
      prompt: 'These experts will be filtered, because these experts are automatically created by the system and are only used in Outsource projects to record payment:',
      list: [],
    },
  ]

  return validated_types.map(key => {
    const target = data_format.find(item => item.key === key)
    target.list = disable_data[key]
    return target
  })
}

export { RouteTools }

export default {
  timeZone,
}
