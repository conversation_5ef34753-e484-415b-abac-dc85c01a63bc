/**
 * tool-router
 * 2020/3/16
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
import router from '@/router/index'
import store from '@/store/index'
import moment from 'moment'

export default {
  getRouteBaseTabPath(route) {
    const tab = route.meta.tab
    const baseRoute = Object.assign({}, this.getBaseRoute(route, tab))
    return router.resolve(baseRoute).href.replace('#', '')
  },
  getBaseRoute(route, tab) {
    return route.matched.find(item => {
      return item.meta.tab === tab
    })
  },
  setRouterMetaTitle(title, route) {
    const costumed_route = Object.assign({}, route)
    costumed_route.title = title
    costumed_route.base_tab_path = this.getRouteBaseTabPath(route)

    return store.dispatch('tagsView/updateBaseView', costumed_route)
  },
  removeCurrentRoute(route) {
    return store.dispatch('tagsView/delView', route)
  },
  /**
   * 从 URL 中解析 query 数据, 同时尝试转换 Number
   * @param $route
   * @param data
   * @param array_keys
   * @param string_keys
   */
  resolveRouteQuery($route, data, array_keys = [], string_keys = []) {
    const params = $route.query
    const resolved_data = {}

    if (params) {
      Object.keys(params).forEach(key => {
        let value = params[key]

        // 解析数组
        const declareIsArray = array_keys.includes(key)
        const valueExist = value
        const valueIsNotArray = !Array.isArray(value)

        if (declareIsArray && valueExist && valueIsNotArray) {
          if (value.includes(',')) {
            value = value.split(',').map(removeQuote)
          } else {
            value = [removeQuote(value)]
          }
        }

        // 数字尝试转换 Int
        if (!string_keys.includes(key)) {
          if (Array.isArray(value)) {
            value = value.map(item => {
              return tryParseNumber(item)
            })
          } else {
            value = tryParseNumber(value)
          }
        }

        resolved_data[key] = value
      })
    }

    if (typeof data === 'object') {
      Object.assign(data, resolved_data)
    }

    return resolved_data

    /**
     * 移除字符串首尾双引号
     * @param input
     * @returns {*}
     */
    function removeQuote(input) {
      if (input.startsWith('"') && input.endsWith('"')) {
        return input.slice(1, -1)
      } else {
        return input
      }
    }

    /**
     * 查询参数向 Number 还原
     * @param input
     * @returns {number}
     */
    function tryParseNumber(input) {
      let output = input
      // 处理 日期
      if (moment(output).isValid() && isNaN(output)) return output

      if (isNaN(output)) return output

      if (input !== '') {
        const number_converted_value = parseFloat(input)

        if (!Number.isNaN(number_converted_value)) {
          output = number_converted_value
        }
      }

      return output
    }
  },
  /**
   * 表单搜索条件保存到 URL
   * @param params
   */
  saveQueryToRouter(params) {
    const query = {}
    for (const paramsKey in params) {
      query[paramsKey] = params[paramsKey] || undefined
    }
    router.replace({
      query,
    })
  },
}
