import moment from 'moment-timezone'

/**
 * download-excel.js
 * 2021/4/19
 * <AUTHOR>
 * copyright 2014-2021, All rights reserved.
 *
 * Code from
 * 'vue-json-excel' https://github.com/jecovier/vue-json-excel
 * 'download-js' https://github.com/rndme/download
 *
 * Merge and custom by NorthWind
 */
export default {
  download(config) {
    this.data = config.data || []
    this.downloadFields = config.fields || {}
    this.meta = config.meta || [
      [
        {
          key: 'charset',
          value: 'utf-8',
        },
      ],
    ]
    this.name = config.name || `data_${moment().format('YYYYMMDDHHmmSS')}.csv`
    this.type = config.type || 'csv'
    this.escapeCsv = this.type === 'csv'

    this.beforeGenerate = config.beforeGenerate
    this.beforeFinish = config.beforeFinish
    this.worksheet = config.worksheet
    this.stringifyLongNum = config.stringifyLongNum
    this.header = config.header

    return this.generate()
  },

  async generate() {
    if (typeof this.beforeGenerate === 'function') {
      await this.beforeGenerate()
    }
    let data = this.data
    if (typeof this.fetch === 'function' || !data) data = await this.fetch()

    if (!data || !data.length) {
      return
    }

    const json = this.getProcessedJson(data, this.downloadFields)
    if (this.type === 'html') {
      // this is mainly for testing
      return this.export(
        this.jsonToXLS(json),
        this.name.replace('.xls', '.html'),
        'text/html',
      )
    } else if (this.type === 'csv') {
      return this.export(
        this.jsonToCSV(json),
        this.name.replace('.xls', '.csv'),
        'application/csv',
      )
    }
    return this.export(
      this.jsonToXLS(json),
      this.name,
      'application/vnd.ms-excel',
    )
  },
  /*
  Use downloadjs to generate the download link
  */
  async export(data, filename, mime) {
    const blob = this.base64ToBlob(data, mime)
    if (typeof this.beforeFinish === 'function') await this.beforeFinish()
    this.downloadFile(blob, filename, mime)
  },
  /*
  jsonToXLS
  ---------------
  Transform json data into an xml document with MS Excel format, sadly
  it shows a prompt when it opens, that is a default behavior for
  Microsoft office and cannot be avoided. It's recommended to use CSV format instead.
  */
  jsonToXLS(data) {
    const xlsTemp =
      '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>${worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><style>br {mso-data-placement: same-cell;}</style></head><body><table>${table}</table></body></html>'
    let xlsData = '<thead>'
    const colspan = Object.keys(data[0]).length
    const _self = this

    // Header
    const header = this.header
    if (header) {
      xlsData += this.parseExtraData(
        header,
        '<tr><th colspan="' + colspan + '">${data}</th></tr>',
      )
    }

    // Fields
    xlsData += '<tr>'
    for (const key in data[0]) {
      xlsData += '<th>' + key + '</th>'
    }
    xlsData += '</tr>'
    xlsData += '</thead>'

    // Data
    xlsData += '<tbody>'
    data.map((item, index) => {
      xlsData += '<tr>'
      for (const key in item) {
        xlsData +=
          '<td>' +
          _self.preprocessLongNum(
            _self.valueReformattedForMultilines(item[key]),
          ) +
          '</td>'
      }
      xlsData += '</tr>'
    })
    xlsData += '</tbody>'

    // Footer
    if (this.footer != null) {
      xlsData += '<tfoot>'
      xlsData += this.parseExtraData(
        this.footer,
        '<tr><td colspan="' + colspan + '">${data}</td></tr>',
      )
      xlsData += '</tfoot>'
    }

    return xlsTemp
      .replace('${table}', xlsData)
      .replace('${worksheet}', this.worksheet)
  },
  /*
  jsonToCSV
  ---------------
  Transform json data into an CSV file.
  */
  jsonToCSV(data) {
    const _self = this
    const csvData = []

    // Header
    const header = this.header
    if (header) {
      csvData.push(this.parseExtraData(header, '${data}\r\n'))
    }

    // Fields
    for (const key in data[0]) {
      csvData.push(key)
      csvData.push(',')
    }
    csvData.pop()
    csvData.push('\r\n')
    // Data
    data.map(item => {
      for (const key in item) {
        let escapedCSV = item[key] + ''
        // Escaped CSV data to string to avoid problems with numbers or other types of values
        // this is controlled by the prop escapeCsv
        if (_self.escapeCsv) {
          if (escapedCSV.match(/[,"\n]/)) {
            escapedCSV = '"' + escapedCSV.replace(/\"/g, '""') + '"'
          }
        }
        csvData.push(escapedCSV)
        csvData.push(',')
      }
      csvData.pop()
      csvData.push('\r\n')
    })
    // Footer
    if (this.footer != null) {
      csvData.push(this.parseExtraData(this.footer, '${data}\r\n'))
    }
    return csvData.join('')
  },
  /*
  getProcessedJson
  ---------------
  Get only the data to export, if no fields are set return all the data
  */
  getProcessedJson(data, header) {
    const keys = this.getKeys(data, header)
    const newData = []
    const _self = this
    data.map((item, index) => {
      const newItem = {}
      for (const label in keys) {
        const property = keys[label]
        newItem[label] = _self.getValue(property, item)
      }
      newData.push(newItem)
    })

    return newData
  },
  getKeys(data, header) {
    if (header) {
      return header
    }

    const keys = {}
    for (const key in data[0]) {
      keys[key] = key
    }
    return keys
  },
  /*
  parseExtraData
  ---------------
  Parse title and footer attribute to the csv format
  */
  parseExtraData(extraData, format) {
    let parseData = ''
    if (Array.isArray(extraData)) {
      for (let i = 0; i < extraData.length; i++) {
        if (extraData[i]) {
          parseData += format.replace('${data}', extraData[i])
        }
      }
    } else {
      parseData += format.replace('${data}', extraData)
    }
    return parseData
  },

  getValue(key, item) {
    const field = typeof key !== 'object' ? key : key.field
    const indexes = typeof field !== 'string' ? [] : field.split('.')
    let value = this.defaultValue

    if (!field) value = item
    else if (indexes.length > 1) {
      value = this.getValueFromNestedItem(item, indexes)
    } else value = this.parseValue(item[field])

    if (Object.prototype.hasOwnProperty.call(key, 'format')) {
      value = this.getValueFromFormat(item, key.format)
    }

    return value
  },

  /*
  convert values with newline \n characters into <br/>
  */
  valueReformattedForMultilines(value) {
    if (typeof value === 'string') return value.replace(/\n/gi, '<br/>')
    else return value
  },
  preprocessLongNum(value) {
    if (this.stringifyLongNum) {
      if (String(value).startsWith('0x')) {
        return value
      }
      if (!isNaN(value) && value !== '') {
        if (value > 99999999999 || value < 0.0000000000001) {
          return '="' + value + '"'
        }
      }
    }
    return value
  },
  getValueFromNestedItem(item, indexes) {
    let nestedItem = item
    for (const index of indexes) {
      if (nestedItem) nestedItem = nestedItem[index]
    }
    return this.parseValue(nestedItem)
  },

  getValueFromFormat(item, formatter) {
    if (typeof formatter !== 'function') return this.defaultValue
    const value = formatter(item)
    return this.parseValue(value)
  },
  parseValue(value) {
    return value || value === 0 || typeof value === 'boolean'
      ? value
      : this.defaultValue
  },
  base64ToBlob(data, mime) {
    const base64 = window.btoa(window.unescape(encodeURIComponent(data)))
    const bstr = atob(base64)
    let n = bstr.length
    const u8arr = new Uint8ClampedArray(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    return new Blob([u8arr], { type: mime })
  },

  downloadFile(data, strFileName, strMimeType) {
    const self = window // this script is only for browsers anyway...
    const defaultMime = 'application/octet-stream' // this default mime also triggers iframe downloads
    let mimeType = strMimeType || defaultMime
    let payload = data
    const url = !strFileName && !strMimeType && payload
    const anchor = document.createElement('a')
    const toString = a => String(a)
    let MyBlob = (self.Blob || self.MozBlob || self.WebKitBlob || toString)
    let fileName = strFileName || 'download'
    let reader
    MyBlob = MyBlob.call ? MyBlob.bind(self) : Blob

    if (String(this) === 'true') { // reverse arguments, allowing download.bind(true, "text/xml", "export.xml") to act as a callback
      payload = [payload, mimeType]
      mimeType = payload[0]
      payload = payload[1]
    }

    if (url && url.length < 2048) { // if no filename and no mime, assume a url was passed as the only argument
      fileName = url.split('/').pop().split('?')[0]
      anchor.href = url // assign href prop to temp anchor
      if (anchor.href.indexOf(url) !== -1) { // if the browser determines that it's a potentially valid url path:
        const ajax = new XMLHttpRequest()
        ajax.open('GET', url, true)
        ajax.responseType = 'blob'
        ajax.onload = function(e) {
          this.downloadFile(e.target.response, fileName, defaultMime)
        }
        setTimeout(() => { ajax.send() }, 0) // allows setting custom ajax headers using the return:
        return ajax
      } // end if valid url?
    } // end if url?

    // go ahead and download dataURLs right away
    if (/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(payload)) {
      if (payload.length > (1024 * 1024 * 1.999) && MyBlob !== toString) {
        payload = dataUrlToBlob(payload)
        mimeType = payload.type || defaultMime
      } else {
        return navigator.msSaveBlob // IE10 can't do a[download], only Blobs:
          ? navigator.msSaveBlob(dataUrlToBlob(payload), fileName)
          : saver(payload) // everyone else can save dataURLs un-processed
      }
    } else { // not data url, is it a string with special needs?
      if (/([\x80-\xff])/.test(payload)) {
        let i = 0
        const tempUiArr = new Uint8Array(payload.length)
        const mx = tempUiArr.length
        for (i; i < mx; ++i) tempUiArr[i] = payload.charCodeAt(i)
        payload = new MyBlob([tempUiArr], { type: mimeType })
      }
    }
    const blob = payload instanceof MyBlob
      ? payload
      : new MyBlob([payload], { type: mimeType })

    function dataUrlToBlob(strUrl) {
      const parts = strUrl.split(/[:;,]/)
      const type = parts[1]
      const decoder = parts[2] === 'base64' ? atob : decodeURIComponent
      const binData = decoder(parts.pop())
      const mx = binData.length
      let i = 0
      const uiArr = new Uint8Array(mx)

      for (i; i < mx; ++i) uiArr[i] = binData.charCodeAt(i)

      return new MyBlob([uiArr], { type: type })
    }

    function saver(url, winMode) {
      if ('download' in anchor) { // html5 A[download]
        anchor.href = url
        anchor.setAttribute('download', fileName)
        anchor.className = 'download-js-link'
        anchor.innerHTML = 'downloading...'
        anchor.style.display = 'none'
        document.body.appendChild(anchor)
        setTimeout(() => {
          anchor.click()
          document.body.removeChild(anchor)
          if (winMode === true) {
            setTimeout(() => { self.URL.revokeObjectURL(anchor.href) },
              250)
          }
        }, 66)
        return true
      }

      // handle non-a[download] safari as best we can:
      if (/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(
        navigator.userAgent)) {
        if (/^data:/.test(url)) {
          url = 'data:' +
            url.replace(/^data:([\w\/\-\+]+)/, defaultMime)
        }
        if (!window.open(url)) { // popup blocked, offer direct download:
          if (confirm(
            'Displaying New Document\n\nUse Save As... to download, then click back to return to this page.')) { location.href = url }
        }
        return true
      }

      // do iframe dataURL download (old ch+FF):
      const f = document.createElement('iframe')
      document.body.appendChild(f)

      if (!winMode && /^data:/.test(url)) { // force a mime that will download:
        url = 'data:' + url.replace(/^data:([\w\/\-\+]+)/, defaultMime)
      }
      f.src = url
      setTimeout(() => { document.body.removeChild(f) }, 333)
    }// end saver

    if (navigator.msSaveBlob) { // IE10+ : (has Blob, but not a[download] or URL)
      return navigator.msSaveBlob(blob, fileName)
    }

    if (self.URL) { // simple fast and modern way using Blob and URL:
      saver(self.URL.createObjectURL(blob), true)
    } else {
      // handle non-Blob()+non-URL browsers:
      if (typeof blob === 'string' || blob.constructor === toString) {
        try {
          return saver('data:' + mimeType + ';base64,' + self.btoa(blob))
        } catch (y) {
          return saver('data:' + mimeType + ',' + encodeURIComponent(blob))
        }
      }

      // Blob but not URL support:
      reader = new FileReader()
      reader.onload = function(e) {
        saver(this.result)
      }
      reader.readAsDataURL(blob)
    }
    return true
  },
}
