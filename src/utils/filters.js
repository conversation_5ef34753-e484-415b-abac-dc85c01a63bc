import moment from 'moment-timezone'
import store from '@/store'

let shiftPress = false
let last_select_index = null

export default {
  rowSetIndex: ({ row, rowIndex }) => {
    row.table_index = rowIndex
  },
  /* Press the shift key to continuously select */
  tableShiftMultiple: (val, row, tableData, tableTarget, Selectable = function() { return true }) => {
    tableTarget.$el.addEventListener('mousedown', function(e) {
      shiftPress = e.shiftKey
    })
    const in_select = val.find(item => item.id === row.id)
    if (in_select) {
      if (shiftPress) {
        if (last_select_index > -1) {
          const start_index = Math.min(last_select_index, row.table_index)
          const end_index = Math.max(last_select_index, row.table_index)
          const select_list = tableData.filter(
            item => (item.table_index < end_index && item.table_index >
              start_index && !val.find(i => i.id === item.id) && Selectable(item)))

          select_list.forEach(item => {
            val.push(item)
            tableTarget.toggleRowSelection(item)
          })
        }
      } else {
        last_select_index = row.table_index
      }
    }
  },
  getLabel: (val, options) => {
    let result = '-'
    options.forEach(item => {
      if (item.value === val) {
        result = item.label
      }
    })
    return result
  },

  getLabels(list, option) {
    if (!list || !list.length) return '-'
    return list.map(item => {
      const result = option.filter(opt => opt.value === item)
      return result.length ? result[0].label : undefined
    }).join(', ')
  },

  getName: (val, options, key = 'id', target_key = 'name') => {
    let result = '-'
    options.forEach(item => {
      if (item[key] === val) {
        result = item[target_key]
      }
    })
    return result
  },

  getNames(list, option) {
    if (!list || !list.length) return '-'
    return list.map(item => {
      const result = option.filter(opt => opt.id === item)
      return result.length ? result[0].name : undefined
    }).join(', ')
  },

  getMembers(members, role) {
    const result = []
    if (!members || !members.length) return '-'
    members.forEach(item => {
      if (item.role === role && Object.keys(item)
        .includes('user') && item.user != null) {
        result.push(item.user.name)
      }
    })
    return !result.length ? '-' : result.join(', ')
  },
  getProjectStausClass(val, options) {
    let result = ''
    options.forEach(item => {
      if (item.value === val) {
        result = item.color
      }
    })
    return result
  },
  getConsultantContact(val, type) {
    let result = ''
    if (!val) return result
    val.forEach(item => {
      if (item.type === type) {
        result = item.value
      }
    })
    return result
  },
  yesOrNo(val, only) {
    let output = val ? 'Yes' : 'No'

    if ([true, 'Yes'].includes(only)) {
      if (!val) {
        output = ''
      }
    } else if (only === 'No') {
      if (val) {
        output = ''
      }
    }

    return output
  },
  momentFormat(val, format) {
    if (format === 'long') {
      format = 'MM/DD/YYYY HH:mm'
    }
    if (!format) format = 'MM/DD/YYYY'
    let result = '-'
    if (!val) return result
    const date = new Date(val)
    if (!Date.parse(date.toString())) return ''

    result = moment(val)
      .tz(store.state.app.timeZone)
      .format(format)
    return result
  },
  momentFormatZone(val, format, timeZone) {
    if (format === 'long') {
      format = 'MM/DD/YYYY HH:mm'
    }
    if (!format) format = 'MM/DD/YYYY'
    let result = '-'
    if (!val) return result
    const date = new Date(val)
    if (!Date.parse(date.toString())) return ''

    result = moment(val)
      .tz(timeZone)
      .format(format)
    return result
  },

  list(val) {
    return Array.isArray(val) ? val.join(', ') : ''
  },

  /**
   * 逗号分割
   * @param list
   * @param key
   * @returns {Error|*}
   */
  commaSeparated(list, key = 'name') {
    if (!Array.isArray(list)) {
      return new Error(
        'Error: Filter "comma_separated" must input an array.')
    }

    return list
      .map(item => {
        return typeof item === 'object' ? item[key] : item
      })
      .join(', ')
  },

  checkValidMail(list) {
    // /^[\w.+-]+@[\w-]+(\.[\w-]+)+$/
    if (!list.length) return true
    const mailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
    const more_than_one = /,|;|，|；/g
    const error_address = list.filter(
      item => item.match(more_than_one) || !mailReg.test(item))
    return error_address.length < 1
  },

  transformKey(input) {
    return input.replace(/_/g, ' ')
      .replace(/(?:^|\s)\S/g, a => a.toUpperCase())
  },

  getListItem(list, type) {
    if (!!list && list.length > 0) {
      return list.map(item => item[type]).join(', ')
    } else {
      return ''
    }
  },
}
