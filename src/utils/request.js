import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import VueAuthenticate from 'vue-authenticate'

const AppHttpService = axios.create({
  baseURL: import.meta.env.VITE_APP_US_DB_API_V2,
  timeout: 30000,
})

AppHttpService.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['X-Rm-Git-Short-Hash'] = import.meta.env.VITE_GIT_COMMIT_HASH
      config.headers['Authorization'] = 'Bearer ' + store.getters.token
      config.headers['uid'] = store.getters.uid
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  },
)

AppHttpService.interceptors.response.use(
  response => {
    if (!response.headers['content-type']?.includes('application/json')) {
      return response
    }

    const res = response.data

    switch (res.code) {
      case '0000': // 成功
        return res.data
      case '0401': // token 过期
        Message({ message: res.message || 'Error', type: 'error', duration: 5 * 1000 })

        store.dispatch('user/resetToken')
          .then(() => {
            location.reload()
          })
        return Promise.reject(res)
      default:
        Message({ message: res.message || 'Error', type: 'error', duration: 5 * 1000 })

        return Promise.reject(res.message || 'Error')
    }
  },
  error => {
    if (!error.response) {
      Message({
        message: 'Server timeout',
        showClose: true,
        type: 'error',
        duration: 5 * 1000,
      })
    } else {
      // 401: Token is invalid;
      if (error.response.data.status === 401) {
        // to re-login
        MessageBox.confirm(
          'You have been logged out, you can cancel to stay on this page, or log in again',
          'Confirm logout', {
            confirmButtonText: 'Re-Login',
            cancelButtonText: 'Cancel',
            type: 'warning',
          })
          .then(() => {
            store.dispatch('user/resetToken')
              .then(() => {
                location.reload()
              })
          })
      } else {
        console.log('err' + error) // for debug
        Message({
          message: error.message,
          showClose: true,
          type: 'error',
          duration: 5 * 1000,
        })
      }
    }
    return Promise.reject(error)
  },
)

// auth
const AuthService = VueAuthenticate.factory(AppHttpService, {
  baseURL: import.meta.env.VITE_APP_US_DB_API_V2, // Your API domain
  tokenPath: 'idToken',

  providers: {
    google: {
      clientId: '68925922578-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com',
      redirectUri: window.location.origin, // Your client app URL
      requiredUrlParams: ['access_type', 'scope'],
      accessType: 'offline',
    },
    linkedin: {
      clientId: '863ecc7ahyz9a5',
      redirectUri: window.location.origin,
      requiredUrlParams: ['state', 'scope'],
      scope: ['r_emailaddress', 'r_liteprofile'],
      state: 'whatever',
    },
    facebook: {
      clientId: '602030593862496',
      redirectUri: window.location.origin + '/',
      requiredUrlParams: ['state', 'scope'],
      scope: ['email', 'public_profile'],
      state: 'whatever',
    },
  },
})

export {
  AppHttpService,
  AuthService,
}
