import moment from 'moment-timezone'

const TokenKey = 'Admin-Token'
const UserInfo = 'Admin-UserInfo'

export function getToken() {
  const expires_time = localStorage.getItem(TokenKey)?.split('-')[1]

  if (expires_time && expires_time > moment().valueOf()) {
    return localStorage.getItem(TokenKey).split('-')[0]
  }
  return ''
}

export function setToken(token) {
  localStorage.removeItem(TokenKey)
  const expires = moment().add(14, 'day').valueOf()
  return localStorage.setItem(TokenKey, `${token}-${expires}`)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

// 简易的UserInfo，client portal 需要用到
export function setUserInfo(info) {
  const data = {
    idToken: getToken(),
    id: info.id,
    email: info.email,
  }
  return localStorage.setItem(UserInfo, JSON.stringify(data))
}

export function removeUserInfo() {
  return localStorage.removeItem(UserInfo)
}
