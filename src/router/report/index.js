import Layout from '@/layout/index'

/**
 * Report 路由拆解
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: '/report',
  name: 'Report',
  alwaysShow: true,
  component: Layout,
  meta: { title: 'Report', icon: 'report' },
  children: [
    {
      path: 'advisorData',
      name: 'AdvisorData',
      component: () => import('@/views/report/advisor-data/index'),
      meta: { title: 'Advisor Data', icon: 'documentation' },
    },
    {
      path: 'projectData',
      name: 'ProjectData',
      component: () => import('@/views/report/project-data/index'),
      meta: { title: 'Project Data', icon: 'documentation' },
    },
    {
      path: 'callTracker',
      name: 'CallTracker',
      redirect: 'callTracker/index',
      component: () => import('@/views/report/call-tracker/index.vue'),
      meta: { title: 'Call Tracker', icon: 'call_tracker' },
      children: [{
        path: 'index',
        name: 'CallTrackerIndex',
        component: () => import('@/views/report/call-tracker/CallTracker.vue'),
        meta: { title: 'Call Tracker', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }, {
        path: 'dashboard',
        name: 'CallTrackerDashboard',
        component: () => import('@/views/report/call-tracker/Dashboard.vue'),
        meta: { title: 'Call Tracker - Dashboard', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }, {
        path: 'client_dashboard',
        name: 'CallTrackerClientDashboard',
        component: () => import('@/views/report/call-tracker/ClientDashboard.vue'),
        meta: { title: 'Call Tracker - Client Dashboard', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }, {
        path: 'targets',
        name: 'CallTrackerSetTargets',
        component: () => import('@/views/report/call-tracker/Targets.vue'),
        meta: { title: 'Call Tracker - Targets', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }, {
        path: 'user_targets',
        name: 'CallTrackerSetUserTargets',
        component: () => import('@/views/report/call-tracker/UserTargets.vue'),
        meta: { title: 'Call Tracker - User Targets', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }, {
        path: 'client_hour_adjusters',
        name: 'CallTrackerClientHourAdjusters',
        component: () => import('@/views/report/call-tracker/ClientHourAdjusters.vue'),
        meta: { title: 'Call Tracker - Client Hour Adjusters', activeMenu: '/report/callTracker', breadcrumb: false },
        hidden: true,
      }],
    },
    {
      path: 'feedback',
      name: 'Feedback',
      component: () => import('@/views/report/feedback/index'),
      meta: { title: 'Feedback', icon: 'thumbs_up' },
    },
  ],
}
