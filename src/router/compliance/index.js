import Layout from '@/layout/index'
import RouterView from '@/layout/RouterView'

/**
 * Compliance 路由拆解
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  component: Layout,
  path: '/compliance',
  meta: { title: 'Compliance', icon: 'form' },
  children: [
    {
      path: 'tnc',
      component: RouterView,
      meta: {
        title: 'T&C',
        breadcrumb: false,
        icon: 'documentation',
        tag: 'TermsConditions',
      },
      children: [
        {
          path: '',
          component: () => import('@/views/compliance/terms-conditions/components/TcList'),
          name: 'TermsConditionsList',
          meta: { title: 'Terms & Conditions' },
        }, {
          path: 'create',
          component: () => import('@/views/compliance/terms-conditions/update'),
          name: 'CreateTermsConditions',
          meta: {
            title: 'Create T&C',
            activeMenu: '/compliance/tnc/list',
            keepAlive: true,
          },
          hidden: true,
        }, {
          path: ':id/detail',
          component: () => import('@/views/compliance/terms-conditions/detail/index'),
          name: 'TermsConditionsDetail',
          meta: { title: 'Detail', activeMenu: '/compliance/tnc/list' },
          hidden: true,
        }],
    },
    {
      path: 'client_approval',
      name: 'ClientApproval',
      component: () => import('@/views/compliance/client-approval/index'),
      meta: { title: 'Client Approval', icon: 'form' },
    },
    {
      path: 'client_agreement',
      component: RouterView,
      meta: { tag: 'ClientAgreement' },
      children: [
        {
          path: '',
          component: () => import('@/views/compliance/client-agreement/components/DefaultList'),
          name: 'ClientAgreementList',
          meta: { title: 'Client Agreement', icon: 'documentation' },
        }, {
          path: 'list',
          component: () => import('@/views/compliance/client-agreement/detail/List'),
          name: 'ClientAgreementDetail',
          meta: {
            title: 'Client CA',
            breadcrumb: false,
            activeMenu: '/compliance/client_agreement',
            tab: 'client',
          },
          hidden: true,
        }, {
          path: 'create',
          component: () => import('@/views/compliance/client-agreement/detail/Questionnaire_v2'),
          name: 'ClientAgreementCreate',
          meta: {
            title: 'Create',
            activeMenu: '/compliance/client_agreement',
            keepAlive: true,
          },
          hidden: true,
        },
      ],
    }, {
      path: 'consultation_approval',
      name: 'ConsultationApproval',
      component: () => import('@/views/compliance/consultation-approval/index'),
      meta: { title: 'Consultation Approval', icon: 'form' },
    }, {
      path: 'contract_approval',
      name: 'ContractApproval',
      component: () => import('@/views/compliance/contract-review/index'),
      meta: { title: 'Contract Approval', icon: 'documentation' },
    },
    {
      path: 'outsource_approval',
      name: 'OutsourceApproval',
      component: () => import('@/views/compliance/outsource-approval/index'),
      meta: { title: 'Outsource Approval', icon: 'form' },
    },
    {
      path: 'setting',
      name: 'Setting',
      component: () => import('@/views/compliance/settings/index'),
      meta: { title: 'Setting', icon: 'setting' },
    },
  ],
}
