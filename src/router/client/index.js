import Layout from '@/layout/index'
import RouterView from '@/layout/RouterView'

/**
 * Client 路由拆分
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */

const meta = { activeMenu: '/client', tab: 'client', breadcrumb: false }

export default {
  path: '/client',
  component: Layout,
  meta: { title: 'Client', icon: 'peoples' },
  children: [
    {
      path: '',
      component: RouterView,
      meta: {
        title: 'Client List',
        icon: 'peoples',
        breadcrumb: false,
        activeMenu: '/client',
        tag: 'Client',
      },
      children: [
        {
          path: '/',
          name: 'ClientList',
          component: () => import('@/views/client/index'),
          meta: { title: 'Client List', activeMenu: '/client' },
          hidden: true,
        }, {
          path: 'create',
          name: 'CreateClient',
          component: () => import('@/views/client/update'),
          meta: {
            title: 'Create Client',
            activeMenu: '/client',
            keepAlive: true,
          },
          hidden: true,
        },
        {
          path: ':client_id',
          name: 'ClientDetail',
          component: () => import('@/views/client/detail/index'),
          meta: { title: 'Client', activeMenu: '/client', tab: 'client' },
          redirect: ':client_id/detail',
          hidden: true,
          children: [
            {
              path: 'detail',
              name: 'ClientProfile',
              component: () => import('@/views/client/detail/Profile'),
              meta: Object.assign({ title: 'Client Profile' }, meta),
              hidden: true,
            },
            {
              path: 'contact',
              name: 'ClientUser',
              component: () => import('@/views/client/detail/Contact'),
              meta: Object.assign({ title: 'Client User' }, meta),
              hidden: true,
            },
            {
              path: 'projects',
              name: 'ClientProjects',
              component: () => import('@/views/client/detail/Projects'),
              meta: Object.assign({ title: 'Client Projects' }, meta),
              hidden: true,
            },
            {
              path: 'contracts',
              name: 'ClientContracts',
              component: () => import('@/views/client/detail/Contract'),
              meta: Object.assign({ title: 'Client Contracts' }, meta),
              hidden: true,
            },
            {
              path: 'am_dashboard',
              name: 'ClientAmDashboard',
              component: () => import('@/views/client/detail/AMDashboard'),
              meta: Object.assign({ title: 'Client AM Dashboard' }, meta),
              hidden: true,
            },
            {
              path: 'client_agreement',
              name: 'ClientAgreement',
              component: () => import('@/views/client/detail/Compliance'),
              meta: Object.assign({ title: 'Client Agreement' }, meta),
              hidden: true,
            },
            {
              path: 'compliance_rules',
              name: 'ClientComplianceRules',
              component: () => import('@/views/client/detail/compliance-preference/index'),
              meta: Object.assign({ title: 'Client Compliance Rules' }, meta),
              hidden: true,
            },
            {
              path: 'outreach',
              name: 'ClientOutreach',
              component: () => import('@/views/client/detail/Outreach'),
              meta: Object.assign({ title: 'Client Outreach' }, meta),
              hidden: true,
            }, {
              path: 'pl',
              name: 'ClientPL',
              component: () => import('@/views/client/detail/PL/index'),
              meta: Object.assign({ title: 'P&L' }, meta),
              hidden: true,
            },
            {
              path: 'document',
              name: 'ClientDocument',
              component: () => import('@/views/client/detail/Document'),
              meta: Object.assign({ title: 'Client Document' }, meta),
              hidden: true,
            },
            {
              path: 'task_dashboard',
              name: 'ClientDetailTaskDashboard',
              component: () => import('@/views/client/task-dashboard'),
              meta: Object.assign({ title: 'Client Task Dashboard' }, meta),
              hidden: true,
            }, {
              path: 'client_usage',
              name: 'ClientUsage',
              component: () => import('@/views/client/detail/ClientUsage'),
              meta: Object.assign({ title: 'Client Usage' }, meta),
              hidden: true,
            },
            {
              path: 'notes',
              name: 'ClientNotes',
              component: () => import('@/views/client/detail/Notes'),
              meta: Object.assign({ title: 'Client Notes' }, meta),
              hidden: true,
            },
            {
              path: 'vetting_calls',
              name: 'ClientVettingCalls',
              component: () => import('@/views/client/detail/VettingCalls'),
              meta: Object.assign({ title: 'Client Vetting Calls' }, meta),
              hidden: true,
            },
            {
              path: 'custom_fields',
              name: 'ClientCustomFields',
              component: () => import('@/views/client/detail/CustomFields'),
              meta: Object.assign({ title: 'Client Custom Fields' }, meta),
              hidden: true,
            },
            {
              path: 'black_list',
              name: 'ClientBlackList',
              component: () => import('@/views/client/detail/BlackList'),
              meta: Object.assign({ title: 'Client Black List' }, meta),
              hidden: true,
            },
            {
              path: 'account_settings',
              name: 'ClientAccountSettings',
              component: () => import('@/views/client/detail/AccountSettings'),
              meta: Object.assign({ title: 'Client Account Settings' }, meta),
              hidden: true,
            },
          ],
        },
      ],
    },
    {
      path: 'contract/create',
      component: () => import('@/views/finance/contract/update/index'),
      name: 'CreateContract',
      meta: { title: 'Create Contract', keepAlive: true },
      hidden: true,
    },
    {
      path: 'contract/:id/detail',
      component: () => import('@/views/finance/contract/detail/index'),
      name: 'ContractDetail',
      meta: { title: 'Detail' },
      hidden: true,
    },
    {
      path: '/client_contact',
      meta: { title: 'Client Users', icon: 'contact', tag: 'ClientContact' },
      component: RouterView,
      children: [
        {
          path: '',
          name: 'ClientContactList',
          component: () => import('@/views/client-contact/index'),
          meta: {
            title: 'Client Users',
            breadcrumb: false,
            activeMenu: '/client_contact',
          },
        },
        {
          path: ':contact_id',
          name: 'ClientContactDetail',
          component: () => import('@/views/client-contact/detail/index'),
          meta: { title: 'Client User Detail', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
          redirect: ':contact_id/detail',
          hidden: true,
          children: [
            {
              path: 'detail',
              name: 'ClientContactProfile',
              component: () => import('@/views/client-contact/detail/ProfileIndex'),
              meta: { title: 'Client User Profile', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
            {
              path: 'projects',
              name: 'ClientContactProjects',
              component: () => import('@/views/client-contact/detail/Projects'),
              meta: { title: 'Client User Projects', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
            {
              path: 'tasks',
              name: 'ClientContactTasks',
              component: () => import('@/views/client-contact/detail/Task'),
              meta: { title: 'Client User Tasks', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
            {
              path: 'communications',
              name: 'ClientContactCommunications',
              component: () => import('@/views/client-contact/detail/Communication'),
              meta: { title: 'Client User Communications', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
            {
              path: 'task_dashboard',
              name: 'ClientContactTaskDashboard',
              component: () => import('@/views/client/task-dashboard/index'),
              meta: { title: 'Client User Task Dashboard', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
            {
              path: 'am_dashboard',
              name: 'ClientContactAmDashboard',
              component: () => import('@/views/client/detail/AMDashboard'),
              meta: { title: 'Client User AM Dashboard', activeMenu: '/client_contact', breadcrumb: false, tab: 'client contact' },
              hidden: true,
            },
          ],
        },
        {
          path: 'add-contact/:project_id',
          name: 'AddContact',
          component: () => import('@/views/client-contact/index'),
          meta: {
            title: 'Client Users',
            breadcrumb: false,
            activeMenu: '/project',
          },
          hidden: true,
        },
      ],
    },
    {
      path: '/task_dashboard',
      meta: {
        title: 'Task Dashboard',
        icon: 'clock',
        tag: 'TaskDashboard',
      },
      component: RouterView,
      children: [
        {
          path: '',
          name: 'ClientTaskDashboard',
          component: () => import('@/views/client/task-dashboard'),
          meta: {
            title: 'Task Dashboard',
            breadcrumb: false,
            activeMenu: '/task_dashboard',
          },
        },
      ],
    },
    {
      path: '/client_revenue_dashboard',
      meta: { title: 'Client Revenue Dashboard', icon: 'dashboard', tag: 'ClientRevenueDashboard' },
      component: RouterView,
      children: [
        {
          path: '',
          name: 'ClientRevenueDashboard',
          component: () => import('@/views/client/client-revenue-dashboard'),
          meta: { title: 'Client Revenue Dashboard', breadcrumb: false, activeMenu: '/client_revenue_dashboard' },
        },
      ],
    },
    {
      path: ':client_id/compliance_rules_history',
      name: 'ClientComplianceRulesHistory',
      component: () => import('@/views/client/detail/compliance-preference/EditHistory'),
      meta: { title: 'Compliance Rules Edit History' },
      hidden: true,
    },
  ],
}
