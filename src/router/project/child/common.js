import RouterView from '@/layout/RouterView'

const activeMenu = '/project/common'
const meta = {
  activeMenu: activeMenu,
  tab: 'project',
  breadcrumb: false,
}

/**
 * common
 * 2020/5/6
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: 'common',
  name: 'CommonProject',
  component: RouterView,
  children: [
    {
      path: 'research_service',
      name: 'ResearchServiceProject',
      component: () => import('@/views/project/common/index'),
      meta: {
        title: 'Research Service',
        icon: 'project-research',
        activeMenu: activeMenu + '/research_service',
      },
    },
    {
      path: 'ges_convey',
      name: 'GESConveyProject',
      component: () => import('@/views/project/common/index'),
      meta: {
        title: 'GES Convey',
        icon: 'project-ges',
        activeMenu: activeMenu + '/ges_convey',
      },
    },
    {
      path: 'conference',
      name: 'ConferenceProject',
      component: () => import('@/views/project/common/index'),
      meta: {
        title: 'Conference',
        icon: 'project-conference',
        activeMenu: activeMenu + '/conference',
      },
    },
    {
      path: 'create/:project_type',
      name: 'CreateCommonProject',
      component: () => import('@/views/project/common/Create'),
      meta: {
        title: 'Create Project',
        activeMenu: activeMenu,
        keepAlive: true,
      },
      hidden: true,
    },
    {
      path: ':project_id',
      name: 'CommonProjectDetail',
      component: () => import('@/views/project/common/detail/index'),
      meta: { title: 'Project', activeMenu: activeMenu, tab: 'project' },
      redirect: ':project_id/info',
      hidden: true,
      children: [
        {
          path: 'consultant',
          name: 'ConsultantTasks',
          component: () => import('@/views/project/common/detail/consultant-tasks/index'),
          meta: Object.assign({ title: 'Consultant Tasks' }, meta),
          hidden: true,
        },
        {
          path: 'leads',
          name: 'CommonProjectLeads',
          component: () => import('@/views/project/common/detail/leads/index'),
          meta: {
            title: 'Project Leads',
            activeMenu: activeMenu,
            tab: 'project',
            breadcrumb: false,
          },
          hidden: true,
        },
        {
          path: 'task/:task_id/complete/:type',
          name: 'CompleteTask',
          component: () => import('@/views/project/common/detail/components/Complete'),
          meta: {
            title: 'Complete',
            activeMenu: activeMenu,
            tab: 'project',
            breadcrumb: false,
          },
          hidden: true,
        },
        {
          path: 'contact',
          name: 'ContactTasks',
          component: () => import('@/views/project/common/detail/contact-tasks/index'),
          meta: {
            title: 'Contact Tasks',
            activeMenu: activeMenu,
            tab: 'project',
            breadcrumb: false,
          },
          hidden: true,
        },
        {
          path: 'info',
          name: 'ProjectInfo',
          component: () => import('@/views/project/common/detail/information/index'),
          meta: {
            title: 'Project Information',
            activeMenu: activeMenu,
            tab: 'project',
            breadcrumb: false,
          },
          hidden: true,
        },
        {
          path: 'outreach',
          name: 'CommonProjectOutreach',
          component: () => import('@/views/project/common/detail/outreach/index'),
          meta: {
            title: 'Project Outreach',
            activeMenu: activeMenu,
            tab: 'project',
            breadcrumb: false,
          },
          hidden: true,
          children: [
            {
              path: 'create',
              component: () => import('@/views/tools/email-template/update'),
              name: 'CommonProjectCreateOutreach',
              meta: Object.assign({ title: 'Project Outreach' }, meta),
              hidden: true,
            }, {
              path: ':id/detail',
              component: () => import('@/views/tools/email-template/detail/index'),
              name: 'CommonProjectOutreachDetail',
              meta: Object.assign({ title: 'Project Outreach' }, meta),
              hidden: true,
            },
          ],
        },
      ],
    },
  ],
  meta: {
    title: 'Others',
    icon: 'list',
    activeMenu: '/project',
    breadcrumb: false,
  },
}
