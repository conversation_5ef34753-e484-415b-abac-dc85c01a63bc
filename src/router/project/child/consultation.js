import RouterView from '@/layout/RouterView'

const meta = { activeMenu: '/project/consultation', tab: 'project', breadcrumb: false }

/**
 * consultation
 * 2020/5/6
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: 'consultation',
  meta: {
    title: 'Consultation',
    icon: 'list',
    activeMenu: '/project/consultation',
    breadcrumb: false,
    keepAlive: true,
    tag: 'ConsultationProject',
  },
  component: RouterView,
  children: [
    {
      path: '',
      name: 'ConsultationProjectList',
      meta: {
        title: 'Consultation',
        icon: 'list',
        activeMenu: '/project/consultation',
        keepAlive: true,
      },
      component: () => import('@/views/project/consultation/index'),
    },
    {
      path: 'create',
      name: 'CreateProject',
      component: () => import('@/views/project/consultation/create'),
      meta: { title: 'Create Project', activeMenu: '/project/consultation', keepAlive: true },
      hidden: true,
    },
    {
      path: ':project_id',
      name: 'ProjectDetail',
      component: () => import('@/views/project/consultation/detail/index'),
      meta: { title: 'Consultation', activeMenu: '/project/consultation', tab: 'project' },
      redirect: ':project_id/information',
      hidden: true,
      children: [
        {
          path: 'tasks',
          name: 'ProjectTasks',
          component: () => import('@/views/project/consultation/detail/client-tasks/ClientTasks'),
          meta: Object.assign({ title: 'Project Client Tasks' }, meta),
          hidden: true,
        },
        {
          path: 'client-tasks',
          name: 'ProjectClientTasks',
          component: () => import('@/views/project/consultation/detail/client-tasks/ClientTasks'),
          meta: Object.assign({ title: 'Project Client Tasks' }, meta),
          hidden: true,
          children: [{
            path: 'arrange',
            name: 'TasksArrange',
            component: () => import('@/views/project/consultation/detail/tasks-arrange/index'),
            meta: { title: 'Arrange', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete',
            name: 'TaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/index'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete_survey',
            name: 'SurveyTaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/SurveyComplete'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete_patients',
            name: 'PatientsTaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/PatientsComplete'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }],
        },
        {
          path: 'client-tasks-mck',
          name: 'ProjectClientTasksMcK',
          component: () => import('@/views/project/consultation/detail/client-tasks/ClientTasksMcK'),
          meta: Object.assign({ title: 'Project Client Tasks McK' }, meta),
          hidden: true,
          children: [{
            path: 'arrange',
            name: 'TasksArrange',
            component: () => import('@/views/project/consultation/detail/tasks-arrange/index'),
            meta: { title: 'Arrange', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete',
            name: 'TaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/index'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete_survey',
            name: 'SurveyTaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/SurveyComplete'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete_patients',
            name: 'PatientsTaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/PatientsComplete'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }],
        }, {
          path: 'client-tasks-sst',
          name: 'ProjectClientTasksSST',
          component: () => import('@/views/project/consultation/detail/client-tasks/ClientTasksSST'),
          meta: Object.assign({ title: 'Project Client Tasks SST' }, meta),
          hidden: true,
          children: [{
            path: 'arrange',
            name: 'TasksArrange',
            component: () => import('@/views/project/consultation/detail/tasks-arrange/index'),
            meta: { title: 'Arrange', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'vetting-call',
            name: 'TasksVettingCall',
            component: () => import('@/views/project/consultation/detail/tasks-arrange/vetting-call/index'),
            meta: { title: 'Vetting Call', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete',
            name: 'TaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/index'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }, {
            path: 'complete_survey',
            name: 'SurveyTaskComplete',
            component: () => import('@/views/project/consultation/detail/task-complete/SurveyComplete'),
            meta: { title: 'Complete', activeMenu: '/project', breadcrumb: false },
            hidden: true,
          }],
        },
        {
          path: 'screened/experts/:type',
          name: 'ProjectScreenedExperts',
          component: () => import('@/views/project/consultation/detail/screened-tasks/ScreenedExperts'),
          meta: Object.assign({ title: 'Project Screened Experts' }, meta),
          hidden: true,
        }, {
          path: 'leads',
          name: 'ProjectLeads',
          component: () => import('@/views/project/consultation/detail/leads/Leads'),
          meta: Object.assign({ title: 'Project Leads' }, meta),
          hidden: true,
        }, {
          path: 'information',
          name: 'ProjectInformation',
          component: () => import('@/views/project/consultation/detail/information/index'),
          meta: Object.assign({ title: 'Project Information' }, meta),
          hidden: true,
        }, {
          path: 'screening-question',
          name: 'ProjectScreeningQuestion',
          component: () => import('@/views/project/consultation/detail/screening-question/index'),
          meta: Object.assign({ title: 'Project Screening Question' }, meta),
          hidden: true,
          children: [
          //   {
          //   path: 'list',
          //   component: () => import('@/views/project/consultation/detail/screening-question/Inquiries'),
          //   name: 'ProjectScreeningQuestionList',
          //   meta: { breadcrumb: false, activeMenu: '/project/consultation', tab: 'project' },
          //   hidden: true,
          // },
            {
              path: 'create/:client_id',
              component: () => import('@/views/project/consultation/detail/screening-question/Questionnaire_V2'),
              name: 'ProjectScreeningQuestionCreate',
              meta: Object.assign({ title: 'Project Screening Question' }, meta),
              hidden: true,
            }, {
              path: 'update/:inquiry_id',
              component: () => import('@/views/project/consultation/detail/screening-question/Questionnaire_V2'),
              name: 'ProjectScreeningQuestionUpdate',
              meta: Object.assign({ title: 'Project Screening Question' }, meta),
              hidden: true,
            }],
        }, {
          path: 'outreach',
          name: 'ProjectOutreach',
          component: () => import('@/views/project/consultation/detail/outreach/index'),
          meta: Object.assign({ title: 'Project Outreach' }, meta),
          hidden: true,
          children: [{
            path: 'create',
            component: () => import('@/views/tools/email-template/update'),
            name: 'ProjectCreateOutreach',
            meta: Object.assign({ title: 'Project Outreach' }, meta),
            hidden: true,
          }, {
            path: ':id/detail',
            component: () => import('@/views/tools/email-template/detail/index'),
            name: 'ProjectOutreachDetail',
            meta: Object.assign({ title: 'Project Outreach' }, meta),
            hidden: true,
          }],
        },
        {
          path: 'compliance',
          name: 'ProjectCompliance',
          component: () => import('@/views/project/consultation/detail/compliance/index'),
          meta: Object.assign({ title: 'Project Compliance' }, meta),
        },
        {
          path: 'recommendations',
          name: 'ProjectRecommendations',
          component: () => import('@/views/project/consultation/detail/recommendations/index'),
          meta: Object.assign({ title: 'Recommendations' }, meta),
        },
        {
          path: 'scheduled-calls',
          name: 'ProjectScheduledCalls',
          component: () => import('@/views/project/consultation/detail/scheduled-calls/List'),
          meta: Object.assign({ title: 'Project Scheduled Calls' }, meta),
        },
        {
          path: 'pl',
          name: 'PL',
          component: () => import('@/views/project/consultation/detail/pl/index.vue'),
          meta: Object.assign({ title: 'P&L' }, meta),
        }, {
          path: 'ca',
          name: 'CAForProject',
          component: () => import('@/views/project/consultation/detail/CA/index.vue'),
          meta: Object.assign({ title: 'Project level CA' }, meta),
        },
      ],
    },
  ],
}
