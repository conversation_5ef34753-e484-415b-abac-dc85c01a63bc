import Layout from '@/layout/index'
import ConsultationProject from './child/consultation'
import CommonProject from './child/common'
import Search from './child/search'

/**
 * Project 路由拆分
 * 2020/5/6
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: '/project',
  component: Layout,
  name: 'Project',
  meta: { title: 'Project', icon: 'project', keepAlive: true },
  redirect: '/project/consultation',
  children: [
    ConsultationProject,
    CommonProject,
    Search,
    {
      path: '/project/search',
      name: 'OldSearch',
      component: () => import('@/views/project/search/oldSearch'),
      meta: { title: 'Project Search', icon: 'search' },
    },
    {
      path: '/scheduler',
      name: 'Scheduler',
      component: () => import('@/views/scheduler/index'),
      meta: { title: 'Scheduler', icon: 'calendar' },
    },
  ],
}
