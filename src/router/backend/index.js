/**
 * index
 * 2020/6/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
import Layout from '@/layout/index'

export default {
  component: Layout,
  path: '/backend',
  alwaysShow: true,
  meta: { title: 'Admin', icon: 'backend' },
  children: [
    {
      path: 'email_template',
      name: 'EmailTemplate',
      component: () => import('@/views/tools/email-template/index'),
      meta: { title: 'Email Templates', icon: 'email' },
      children: [{
        path: 'create',
        component: () => import('@/views/tools/email-template/update'),
        name: 'CreateEmailTemplate',
        meta: { title: 'Create Email Template', activeMenu: '/tools/email_template', keepAlive: true },
        hidden: true,
      }, {
        path: ':id/detail',
        component: () => import('@/views/tools/email-template/detail/index'),
        name: 'EmailTemplateDetail',
        meta: { title: 'Detail', activeMenu: '/tools/email_template' },
        hidden: true,
      }],
    },
    {
      path: 'user',
      name: 'User',
      component: () => import('@/views/backend/user/index'),
      meta: { title: 'User Manage', icon: 'user-manage' },
    },
    {
      path: 'team',
      name: 'Team',
      component: () => import('@/views/backend/team/index'),
      meta: { title: 'Team Manage', breadcrumb: false, icon: 'peoples' },
    },
    {
      path: ':id/detail',
      name: 'TeamDetail',
      component: () => import('@/views/backend/team/TeamDetail'),
      meta: { title: 'Team', activeMenu: '/backend/team', breadcrumb: false },
      hidden: true,
    },
    {
      path: 'permission',
      name: 'Permission',
      redirect: 'permission/roles',
      component: () => import('@/views/backend/permission/index'),
      meta: { title: 'Permission', icon: 'permission' },
      children: [{
        path: 'roles',
        name: 'PermissionRoles',
        component: () => import('@/views/backend/permission/components/roles/index'),
        meta: { title: 'Roles', activeMenu: '/backend/permission', breadcrumb: false },
        hidden: true,
      }, {
        path: 'router',
        name: 'PermissionRouter',
        component: () => import('@/views/backend/permission/components/router/index'),
        meta: { title: 'Router', activeMenu: '/backend/permission', breadcrumb: false },
        hidden: true,
      }],
    },
    {
      path: 'RDConfig',
      name: 'RDConfig',
      component: () => import('@/views/backend/RDConfig/index'),
      meta: { title: 'RD Config', breadcrumb: false, icon: 'setting' },
    },
  ],
}
