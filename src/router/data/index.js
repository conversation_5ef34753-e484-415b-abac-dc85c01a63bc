import Layout from '@/layout/index'
import RouterView from '@/layout/RouterView'

/**
 * Data 路由拆分
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */

export default {
  path: '/data',
  component: Layout,
  meta: { title: 'Data', icon: 'data' },
  children: [
    {
      path: '',
      redirect: '/company',
    },
    {
      path: '/company',
      component: RouterView,
      meta: { tag: 'Company' },
      children: [
        {
          path: '',
          name: 'CompanyList',
          component: () => import('@/views/company/index'),
          meta: { title: 'Company', activeMenu: '/company', icon: 'company' },
        },
        {
          path: 'create/:name?',
          name: 'CreateCompany',
          component: () => import('@/views/company/update'),
          meta: { title: 'Create Company', activeMenu: '/company', keepAlive: true },
          hidden: true,
        },
        {
          path: ':company_id',
          name: 'CompanyDetail',
          component: () => import('@/views/company/detail/index'),
          meta: { title: 'Company', activeMenu: '/company', tab: 'company' },
          redirect: ':company_id/detail',
          hidden: true,
          children: [
            {
              path: 'detail',
              name: 'CompanyProfile',
              component: () => import('@/views/company/detail/Profile'),
              meta: { title: 'Company Profile', activeMenu: '/company', tab: 'company', breadcrumb: false },
              hidden: true,
            },
            {
              path: 'value_chain',
              name: 'CompanyValueChain',
              component: () => import('@/views/company/detail/ValueChain'),
              meta: { title: 'Company Value Chain', activeMenu: '/company', tab: 'company', breadcrumb: false },
              hidden: true,
            },
          ],
        },
      ],
    },
    {
      path: '/advisor_tag',
      component: RouterView,
      meta: { tag: 'Advisor Tag' },
      children: [
        {
          path: '',
          name: 'AdvisorTag',
          component: () => import('@/views/data/advisor-tag/index'),
          meta: { title: 'Advisor Tag', icon: 'tag' },
        },
      ],
    },
    {
      path: '/global_black_list',
      component: RouterView,
      meta: { tag: 'Global Black List' },
      children: [
        {
          path: '',
          name: 'GlobalBlackList',
          component: () => import('@/views/data/black-list/index'),
          meta: { title: 'Global Black List', icon: 'ban' },
        },
      ],
    },
  ],
}
