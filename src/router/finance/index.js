import Layout from '@/layout/index'

/**
 * Finance 路由拆解
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: '/finance',
  component: Layout,
  redirect: '/finance/contract',
  meta: { title: 'Finance', icon: 'money' },
  children: [
    {
      path: 'revenue',
      name: 'Revenue',
      component: () => import('@/views/finance/revenue/index'),
      meta: { title: 'Revenue', icon: 'revenue' },
    },
    {
      path: 'contract',
      name: 'Contract',
      component: () => import('@/views/finance/contract/index'),
      meta: { title: 'Contract', icon: 'contract' },

    },
    {
      path: 'invoice',
      name: 'Invoice',
      component: () => import('@/views/finance/invoice/index'),
      meta: { title: 'Invoice', icon: 'invoice' },
    },
    {
      path: 'payment',
      name: 'Payment',
      component: () => import('@/views/finance/payment/index'),
      meta: { title: 'Payment', icon: 'payment' },
    },
    {
      path: 'payment_v2',
      name: 'Payment v2',
      component: () => import('@/views/finance/payment/index2'),
      meta: { title: 'Payment v2', icon: 'payment' },
    },
  ],
}
