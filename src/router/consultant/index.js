import Layout from '@/layout/index'

/**
 * Consultant 路由拆解
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: '/consultant',
  component: Layout,
  meta: { title: 'Advisor Network', icon: 'consultant', tag: 'Consultant' },
  children: [{
    path: '',
    name: 'ConsultantList',
    component: () => import('@/views/consultant/index'),
    meta: { title: 'Advisor Network', breadcrumb: false, activeMenu: '/consultant', keepAlive: true },
    hidden: true,
  }, {
    path: 'add-consultant/:project_id?/:type?',
    name: 'AddConsultant',
    component: () => import('@/views/consultant/index'),
    meta: { title: 'Add Consultant', breadcrumb: false, activeMenu: '/project' },
    hidden: true,
  }, {
    path: 'create',
    name: 'CreateConsultant',
    component: () => import('@/views/consultant/update'),
    meta: { title: 'Create Lead', activeMenu: '/consultant', keepAlive: true },
    hidden: true,
  }, {
    path: ':advisor_id/detail',
    name: 'ConsultantDetail',
    component: () => import('@/views/consultant/detail/index'),
    meta: { title: 'Detail', activeMenu: '/consultant', tab: 'consultant' },
    hidden: true,
  }],
}
