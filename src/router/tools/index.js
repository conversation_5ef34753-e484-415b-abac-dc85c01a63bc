import Layout from '@/layout/index'

/**
 * Tools 路由拆解
 * 2020/7/15
 * <AUTHOR>
 * copyright 2014-2020, All rights reserved.
 */
export default {
  path: '/tools',
  name: 'Tools',
  component: Layout,
  redirect: '/tools/advisor_import_to_project',
  meta: { title: 'Tools', icon: 'tools' },
  children: [
    {
      path: 'share_center/linkedin_scraper_version',
      name: 'LinkedinScraperVersion',
      component: () => import('@/views/tools/linkedin-version/index'),
      meta: {
        title: 'Linkedin Scraper Version',
        icon: 'share',
        tab: 'tools',
      },
    },
    {
      path: 'share_center/basic',
      name: 'ShareCenterAllUsers',
      component: () => import('@/views/tools/share-center/Basic'),
      meta: { title: 'Share Center', icon: 'share' },
      children: [
        {
          path: 'create',
          component: () => import('@/views/tools/share-center/update'),
          name: 'BasicCreateArticle',
          meta: {
            title: 'Create Article',
            activeMenu: '/tools/share_center/basic',
            keepAlive: true,
          },
          hidden: true,
        },
        {
          path: ':id/detail',
          component: () => import('@/views/tools/share-center/detail/index'),
          name: 'BasicArticleDetail',
          meta: { title: 'Article Detail', activeMenu: '/tools/share_center/basic' },
          hidden: true,
        },
      ],
    },
    {
      path: 'share_center/sales',
      name: 'ShareCenterSalesAM',
      component: () => import('@/views/tools/share-center/SalesAM'),
      meta: { title: 'Sales/AM Share Center', icon: 'share' },
      children: [
        {
          path: 'create',
          component: () => import('@/views/tools/share-center/update'),
          name: 'SalesCreateArticle',
          meta: {
            title: 'Create Article',
            activeMenu: '/tools/share_center/sales',
            keepAlive: true,
          },
          hidden: true,
        },
        {
          path: ':id/detail',
          component: () => import('@/views/tools/share-center/detail/index'),
          name: 'SalesArticleDetail',
          meta: { title: 'Article Detail', activeMenu: '/tools/share_center/sales' },
          hidden: true,
        },
      ],
    },
    {
      path: 'advisor_import_to_project',
      name: 'AdvisorImportToProject',
      component: () => import('@/views/tools/advisor-import-to-project/index'),
      meta: {
        title: 'Advisor Import to Project',
        icon: 'share',
        tab: 'tools',
      },
    },
    {
      path: 'API_tools',
      name: 'APITools',
      component: () => import('@/views/tools/API/index'),
      meta: {
        title: 'API Tools',
        icon: 'share',
        tab: 'tools',
      },
    },
  ],
}
