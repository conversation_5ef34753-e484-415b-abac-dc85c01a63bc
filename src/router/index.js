import Vue from 'vue'
import Router from 'vue-router'
import store from '../store'

Vue.use(Router)

const originalReplace = Router.prototype.replace
const originalPush = Router.prototype.push

Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location)
    .catch(err => err)
}

Router.prototype.push = function push(location) {
  return originalPush.call(this, location)
    .catch(err => err)
}

/* Layout */
import Layout from '@/layout'

import ProjectRouter from '@/router/project/index'
import ConsultantRouter from '@/router/consultant/index'
import ClientRouter from '@/router/client/index'
import ComplianceRouter from '@/router/compliance/index'
import FinanceRouter from '@/router/finance/index'
import ReportRouter from '@/router/report/index'
import ToolsRouter from '@/router/tools/index'
import DataRouter from '@/router/data/index'
import BackendRouter from '@/router/backend/index'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    name: 'Login',
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  {
    path: '/no_permission',
    component: () => import('@/views/NoPermission'),
    hidden: true,
  },

  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        name: 'Redirect',
        component: () => import('@/views/redirect/index'),
      },
    ],
  },

  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/home_v2/index'),
        meta: { title: 'Home', icon: 'home' },
      },
    ],
  },

  {
    path: '/profile',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Profile',
        component: () => import('@/views/profile/index'),
        meta: { title: 'Profile' },
        hidden: true,
      },
    ],
  },

  {
    name: 'AuthorizeForTool',
    path: '/authorize_tool',
    component: () => import('@/views/login/components/LinkedIn'),
    hidden: true,
  },

  {
    path: '/test',
    component: () => import('@/views/test/index'),
    meta: { title: 'Test' },
    hidden: true,
  },
]

export const asyncRoutes = [
  ProjectRouter,
  ConsultantRouter,
  ClientRouter,
  ComplianceRouter,
  FinanceRouter,
  ReportRouter,
  ToolsRouter,
  DataRouter,
  BackendRouter,

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', meta: { alwaysAccess: true }, hidden: true },
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
})

const router = createRouter()

router.beforeEach((to, from, next) => {
  if (to.meta.keepAlive) {
    store.commit('app/PUSH_KEEP_ALIVE', to.name)
  }
  next()
})

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
